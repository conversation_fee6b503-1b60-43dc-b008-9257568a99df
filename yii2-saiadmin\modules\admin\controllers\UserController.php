<?php
/**
 * 示例控制器 (Yii 2.0 风格)
 */
namespace app\modules\admin\controllers;

use Yii;
use app\components\base\BaseController;
use app\modules\admin\models\User;
use app\components\services\UserService;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * 用户控制器
 */
class UserController extends BaseController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            "verbs" => [
                "class" => VerbFilter::class,
                "actions" => [
                    "delete" => ["POST"],
                ],
            ],
        ]);
    }

    /**
     * 用户列表
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new User();
        $dataProvider = new ActiveDataProvider([
            "query" => User::find(),
            "pagination" => [
                "pageSize" => 20,
            ],
        ]);

        if (Yii::$app->request->isAjax) {
            return $this->success([
                "list" => $dataProvider->getModels(),
                "pagination" => [
                    "total" => $dataProvider->getTotalCount(),
                    "page" => $dataProvider->getPagination()->getPage() + 1,
                    "pageSize" => $dataProvider->getPagination()->getPageSize(),
                ]
            ]);
        }

        return $this->render("index", [
            "searchModel" => $searchModel,
            "dataProvider" => $dataProvider,
        ]);
    }

    /**
     * 查看用户详情
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        
        if (Yii::$app->request->isAjax) {
            return $this->success($model->toArray());
        }
        
        return $this->render("view", [
            "model" => $model,
        ]);
    }

    /**
     * 创建用户
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new User();
        $userService = new UserService();

        if ($model->load(Yii::$app->request->post())) {
            if ($userService->createUser($model)) {
                if (Yii::$app->request->isAjax) {
                    return $this->success([], "用户创建成功");
                }
                return $this->redirect(["view", "id" => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail("用户创建失败");
        }

        return $this->render("create", [
            "model" => $model,
        ]);
    }

    /**
     * 更新用户
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $userService = new UserService();

        if ($model->load(Yii::$app->request->post())) {
            if ($userService->updateUser($model)) {
                if (Yii::$app->request->isAjax) {
                    return $this->success([], "用户更新成功");
                }
                return $this->redirect(["view", "id" => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail("用户更新失败");
        }

        return $this->render("update", [
            "model" => $model,
        ]);
    }

    /**
     * 删除用户
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $userService = new UserService();
        
        if ($userService->deleteUser($model)) {
            if (Yii::$app->request->isAjax) {
                return $this->success([], "用户删除成功");
            }
        } else {
            if (Yii::$app->request->isAjax) {
                return $this->fail("用户删除失败");
            }
        }

        return $this->redirect(["index"]);
    }

    /**
     * 查找用户模型
     * @param integer $id
     * @return User
     * @throws NotFoundHttpException
     */
    protected function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException("请求的页面不存在。");
    }
}