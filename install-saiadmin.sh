#!/bin/bash
# SaiAdmin Yii 2.0 自动安装脚本

echo "🚀 开始安装 SaiAdmin Yii 2.0..."

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 安装依赖
echo "📦 安装系统依赖..."
apt-get update
apt-get install -y php php-mysql php-mbstring php-xml php-curl composer nginx mysql-server

# 创建项目目录
echo "📁 创建项目目录..."
mkdir -p /var/www/html/saiadmin
cd /var/www/html/saiadmin

# 复制项目文件
echo "📋 复制项目文件..."
cp -r /path/to/yii2-saiadmin/* .

# 安装Composer依赖
echo "📦 安装Composer依赖..."
composer install --no-dev --optimize-autoloader

# 设置权限
echo "🔐 设置权限..."
chmod 755 yii
chmod -R 777 runtime/
chmod -R 777 web/assets/
chown -R www-data:www-data .

# 配置Nginx
echo "🌐 配置Nginx..."
cp nginx-saiadmin.conf /etc/nginx/sites-available/saiadmin
ln -s /etc/nginx/sites-available/saiadmin /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

# 创建数据库
echo "🗄️ 创建数据库..."
mysql -e "CREATE DATABASE saiadmin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER 'saiadmin_user'@'localhost' IDENTIFIED BY 'secure_password';"
mysql -e "GRANT ALL PRIVILEGES ON saiadmin.* TO 'saiadmin_user'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

echo "✅ 安装完成！"
echo "🌐 访问地址: http://localhost"
echo "🔧 控制台: php yii help"