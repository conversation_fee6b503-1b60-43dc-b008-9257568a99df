import{a as G,t as I,c as N}from"./index-ybrmzYq5.js";import{a as b}from"./dept-B7mu9jEv.js";import{M as P}from"./@arco-design-uttiljWv.js";import{r as n,a as V,c as T,h as i,j as h,k as z,l as m,t as l,a1 as k,O as E}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const We={__name:"edit",emits:["success"],setup(H,{expose:w,emit:g}){const x=g,u=n(),c=n(""),p=n(!1),f=n(!1),y=G(),U=V({...y.user}),_=n([]);let D=T(()=>"部门管理"+(c.value=="add"?"-新增":"-编辑"));const v={id:"",parent_id:"",level:"",name:"",status:1,sort:100,remark:""},t=V({...v}),C={parent_id:[{required:!0,message:"上级部门不能为空"}],name:[{required:!0,message:"部门名称不能为空"}]},B=async()=>{const o=await await N.commonGet("/core/dept/index?tree=true&filter=false");U.id===1?_.value=[{label:"无上级部门",value:0,children:o.data}]:_.value=o.data},O=async(o="add")=>{c.value=o,Object.assign(t,v),u.value.clearValidate(),p.value=!0,await B()},j=async o=>{for(const e in t)o[e]!=null&&o[e]!=null&&(t[e]=o[e])},q=async o=>{var d;if(!await((d=u.value)==null?void 0:d.validate())){f.value=!0;let r={...t},s={};c.value==="add"?(r.id=void 0,s=await b.save(r)):s=await b.update(r.id,r),s.code===200&&(P.success("操作成功"),x("success"),o(!0)),setTimeout(()=>{f.value=!1},500)}o(!1)},F=()=>p.value=!1;return w({open:O,setFormData:j}),(o,e)=>{const d=i("a-tree-select"),r=i("a-form-item"),s=i("a-input"),M=i("a-input-number"),R=i("sa-radio"),S=i("a-textarea"),A=i("a-form");return z(),h(E("a-modal"),{visible:p.value,"onUpdate:visible":e[5]||(e[5]=a=>p.value=a),width:k(I).getDevice()==="mobile"?"100%":"600px",title:k(D),"mask-closable":!1,"ok-loading":f.value,onCancel:F,onBeforeOk:q},{default:m(()=>[l(A,{ref_key:"formRef",ref:u,model:t,rules:C,"auto-label-width":!0},{default:m(()=>[l(r,{field:"parent_id",label:"上级部门"},{default:m(()=>[l(d,{modelValue:t.parent_id,"onUpdate:modelValue":e[0]||(e[0]=a=>t.parent_id=a),data:_.value,"field-names":{key:"value",title:"label"},"allow-clear":"",placeholder:"请选择上级部门"},null,8,["modelValue","data"])]),_:1}),l(r,{label:"部门名称",field:"name"},{default:m(()=>[l(s,{modelValue:t.name,"onUpdate:modelValue":e[1]||(e[1]=a=>t.name=a),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1}),l(r,{label:"排序数字",field:"sort"},{default:m(()=>[l(M,{modelValue:t.sort,"onUpdate:modelValue":e[2]||(e[2]=a=>t.sort=a),placeholder:"请输入排序数字"},null,8,["modelValue"])]),_:1}),l(r,{label:"状态",field:"status"},{default:m(()=>[l(R,{modelValue:t.status,"onUpdate:modelValue":e[3]||(e[3]=a=>t.status=a),dict:"data_status",placeholder:"请选择状态"},null,8,["modelValue"])]),_:1}),l(r,{label:"备注",field:"remark"},{default:m(()=>[l(S,{modelValue:t.remark,"onUpdate:modelValue":e[4]||(e[4]=a=>t.remark=a),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},40,["visible","width","title","ok-loading"])}}};export{We as default};
