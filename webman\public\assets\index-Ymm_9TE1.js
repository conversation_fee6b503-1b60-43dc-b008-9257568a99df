import{t as L}from"./index-ybrmzYq5.js";import{a as m}from"./generate-CvHPqnHt.js";import W from"./loadTable-BFTONwXN.js";import q from"./preview-F38rEx2-.js";import H from"./editInfo-ChAN-DIk.js";import{M as l,j as J}from"./@arco-design-uttiljWv.js";import{r as s,a as F,o as K,h as r,n as Q,k as c,t as o,l as t,j as f,y as a}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./database-C9oinknn.js";import"./index-D4ERJytk.js";import"./menu-CgiEA4rB.js";import"./dict-C6FxRZf9.js";import"./settingComponent-C9EG_z5A.js";const X={class:"ma-content-block"},ct={__name:"index",setup(Y){const _=s(),v=s(),w=s(),x=s(),g=s([]),B=n=>g.value=n,M=async(n,e)=>{if(n==="generateCode"){C(e);return}if(n==="generateFile"){J.info({title:"提示",content:"生成到项目将会覆盖原有文件，确定要生成吗？",simple:!1,onBeforeOk:d=>{E(e),d(!0)}});return}},C=async n=>{l.info("代码生成下载中，请稍后");const e=await m.generateCode({ids:n.toString().split(",")});e?(L.download(e,"saiadmin.zip"),l.success("代码生成成功，开始下载")):l.error("文件下载失败")},h=async n=>{const e=await m.sync(n);e.code===200&&l.success(e.message)},E=async n=>{const e=await m.generateFile({id:n});e.code===200&&l.success(e.message)},O=()=>{if(g.value.length===0){l.error("至少要选择一条数据");return}C(g.value.join(","))},p=s({table_name:"",source:""}),T=F({api:m.getPageList,rowSelection:{showCheckedAll:!0},operationColumnWidth:300,edit:{show:!0,func:async n=>{v.value.open(n.id)}},delete:{show:!0,func:async n=>{var e;await m.destroy(n),l.success("删除成功！"),(e=_.value)==null||e.refresh()}}}),U=F([{title:"表名称",dataIndex:"table_name",width:180,align:"left"},{title:"表描述",dataIndex:"table_comment",width:150,align:"left"},{title:"应用类型",dataIndex:"template",width:120},{title:"应用名称",dataIndex:"namespace",width:120},{title:"模板类型",dataIndex:"stub",width:120},{title:"生成类型",dataIndex:"tpl_category",width:120},{title:"创建时间",dataIndex:"create_time",width:180}]),y=async()=>{var n;(n=_.value)==null||n.refresh()};return K(async()=>{y()}),(n,e)=>{const d=r("a-input"),R=r("a-form-item"),I=r("a-col"),j=r("icon-code"),S=r("a-button"),A=r("icon-export"),D=r("icon-eye"),b=r("a-link"),$=r("icon-sync"),N=r("a-popconfirm"),P=r("icon-double-right"),V=r("a-doption"),z=r("a-dropdown"),u=r("a-tag"),G=r("sa-table");return c(),Q("div",X,[o(G,{ref_key:"crudRef",ref:_,options:T,columns:U,searchForm:p.value,onSelectionChange:B},{tableSearch:t(()=>[o(I,{span:8},{default:t(()=>[o(R,{field:"table_name",label:"表名称"},{default:t(()=>[o(d,{modelValue:p.value.table_name,"onUpdate:modelValue":e[0]||(e[0]=i=>p.value.table_name=i),placeholder:"请输入数据表名称"},null,8,["modelValue"])]),_:1})]),_:1}),o(I,{span:8},{default:t(()=>[o(R,{field:"source",label:"数据源"},{default:t(()=>[o(d,{modelValue:p.value.source,"onUpdate:modelValue":e[1]||(e[1]=i=>p.value.source=i),placeholder:"请输入数据源名称"},null,8,["modelValue"])]),_:1})]),_:1})]),tableAfterButtons:t(()=>[o(S,{type:"outline",onClick:O},{icon:t(()=>[o(j)]),default:t(()=>[e[3]||(e[3]=a("生成代码 "))]),_:1}),o(S,{onClick:e[2]||(e[2]=()=>x.value.open()),type:"outline",status:"success"},{icon:t(()=>[o(A)]),default:t(()=>[e[4]||(e[4]=a("装载数据表 "))]),_:1})]),operationBeforeExtend:t(({record:i})=>[o(b,{onClick:k=>w.value.open(i.id)},{default:t(()=>[o(D),e[5]||(e[5]=a(" 预览"))]),_:2},1032,["onClick"]),o(N,{content:"同步会自动同步数据库字段，确定同步吗?",position:"bottom",onOk:k=>h(i.id)},{default:t(()=>[o(b,null,{default:t(()=>[o($),e[6]||(e[6]=a(" 同步"))]),_:1})]),_:2},1032,["onOk"])]),operationAfterExtend:t(({record:i})=>[o(z,{trigger:"hover",onSelect:k=>M(k,i.id)},{content:t(()=>[o(V,{value:"generateFile"},{default:t(()=>e[8]||(e[8]=[a("生成到项目")])),_:1}),o(V,{value:"generateCode"},{default:t(()=>e[9]||(e[9]=[a("代码下载")])),_:1})]),default:t(()=>[o(b,null,{default:t(()=>[o(P),e[7]||(e[7]=a(" 生成"))]),_:1})]),_:2},1032,["onSelect"])]),tpl_category:t(({record:i})=>[i.tpl_category=="single"?(c(),f(u,{key:0,color:"green"},{default:t(()=>e[10]||(e[10]=[a("单表CRUD")])),_:1})):(c(),f(u,{key:1,color:"red"},{default:t(()=>e[11]||(e[11]=[a("树表CRUD")])),_:1}))]),form_type:t(({record:i})=>[i.form_type=="a-modal"?(c(),f(u,{key:0,color:"blue"},{default:t(()=>e[12]||(e[12]=[a("Modal")])),_:1})):(c(),f(u,{key:1,color:"orange"},{default:t(()=>e[13]||(e[13]=[a("Drawer")])),_:1}))]),_:1},8,["options","columns","searchForm"]),o(W,{ref_key:"loadTableRef",ref:x,onSuccess:y},null,512),o(q,{ref_key:"previewRef",ref:w},null,512),o(H,{ref_key:"editRef",ref:v,onSuccess:y},null,512)])}}};export{ct as default};
