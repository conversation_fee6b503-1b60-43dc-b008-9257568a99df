@echo off
chcp 65001 >nul
echo ========================================
echo    SaiAdmin 管理功能测试脚本
echo ========================================
echo.

echo [1/5] 检查默认管理员账户...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin -e "SELECT id, username, nickname, status FROM sa_system_user WHERE id=1;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 管理员账户查询成功
) else (
    echo ❌ 管理员账户查询失败
)
echo.

echo [2/5] 检查系统菜单...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin -e "SELECT COUNT(*) as menu_count FROM sa_system_menu;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 系统菜单检查完成
) else (
    echo ❌ 系统菜单检查失败
)
echo.

echo [3/5] 检查系统角色...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin -e "SELECT id, name, code FROM sa_system_role;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 系统角色检查完成
) else (
    echo ❌ 系统角色检查失败
)
echo.

echo [4/5] 检查系统配置...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin -e "SELECT COUNT(*) as config_count FROM sa_system_config;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 系统配置检查完成
) else (
    echo ❌ 系统配置检查失败
)
echo.

echo [5/5] 生成访问信息...
echo ========================================
echo 🎯 SaiAdmin 管理系统信息
echo ========================================
echo.
echo 🌐 前端管理界面:
echo    http://localhost:8889/
echo.
echo 🔑 默认登录信息:
echo    用户名: admin
echo    密码: admin123
echo    (首次登录可能需要修改密码)
echo.
echo 📊 系统功能模块:
echo    ├── 系统管理
echo    │   ├── 用户管理
echo    │   ├── 角色管理
echo    │   ├── 菜单管理
echo    │   ├── 部门管理
echo    │   └── 岗位管理
echo    ├── 系统监控
echo    │   ├── 在线用户
echo    │   ├── 登录日志
echo    │   └── 操作日志
echo    └── 系统工具
echo        ├── 代码生成
echo        ├── 定时任务
echo        └── 数据字典
echo.
echo 🔧 API接口地址:
echo    http://localhost:8787/
echo.
echo ========================================
echo 管理系统测试完成！
echo ========================================
pause
