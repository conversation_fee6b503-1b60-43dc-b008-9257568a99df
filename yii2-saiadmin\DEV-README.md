# SaiAdmin Yii 2.0 开发指南

## 🚀 快速开始

### 启动开发服务器
```bash
./start-server.sh
# 或者
./dev-helper.sh server
```

### 访问地址
- 前端首页: http://localhost:8080
- 管理后台: http://localhost:8080/admin
- Gii代码生成: http://localhost:8080/gii
- 调试工具: http://localhost:8080/debug

## 🔧 开发工具

### 控制台命令
```bash
# 查看所有命令
php yii help

# 数据库迁移
php yii migrate

# 清除缓存
php yii cache/flush-all

# 创建控制器
php yii gii/controller

# 创建模型
php yii gii/model
```

### 开发助手
```bash
# 使用开发助手
./dev-helper.sh [command]

# 可用命令:
# server  - 启动开发服务器
# migrate - 运行数据库迁移
# cache   - 清除缓存
# gii     - 打开Gii代码生成器
# debug   - 打开调试工具
# test    - 运行测试
# log     - 查看日志
```

## 📁 项目结构

```
yii2-saiadmin/
├── config/          # 配置文件
├── controllers/     # 控制器
├── models/         # 模型
├── modules/        # 模块
│   ├── admin/     # 管理模块
│   └── tool/      # 工具模块
├── views/          # 视图
├── web/            # Web根目录
├── runtime/        # 运行时文件
└── vendor/         # 依赖包
```

## 🎯 开发规范

### 命名规范
- 控制器: PascalCase + Controller (UserController)
- 模型: PascalCase (User)
- 视图: kebab-case (user-list.php)
- 方法: camelCase (getUserList)

### 代码规范
- 遵循PSR-4自动加载标准
- 使用Yii 2.0编码规范
- 编写PHPDoc注释
- 使用类型提示

## 📖 参考资源

- [Yii 2.0 指南](https://www.yiiframework.com/doc/guide/2.0/zh-cn)
- [Yii 2.0 API文档](https://www.yiiframework.com/doc/api/2.0)
- [SaiAdmin文档](https://saiadmin.com/docs)
