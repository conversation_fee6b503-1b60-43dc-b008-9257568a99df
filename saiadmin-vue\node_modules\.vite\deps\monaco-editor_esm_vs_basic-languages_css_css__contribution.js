import {
  registerLanguage
} from "./chunk-TIAC434R.js";
import "./chunk-OX74CKBW.js";
import "./chunk-WBIRFOMM.js";
import "./chunk-LK32TJAX.js";

// node_modules/monaco-editor/esm/vs/basic-languages/css/css.contribution.js
registerLanguage({
  id: "css",
  extensions: [".css"],
  aliases: ["CSS", "css"],
  mimetypes: ["text/css"],
  loader: () => {
    if (false) {
      return new Promise((resolve, reject) => {
        __require(["vs/basic-languages/css/css"], resolve, reject);
      });
    } else {
      return import("./css-CR6MIYNS.js");
    }
  }
});
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/css/css.contribution.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=monaco-editor_esm_vs_basic-languages_css_css__contribution.js.map
