import{r as p,o as pe,h as n,ba as ce,n as B,k as i,m as c,t as d,l as t,F as R,P as S,j as r,y as k,z as U,M as x,p as m,N as fe}from"./@vue-9ZIPiVZG.js";import{j as _e}from"./index-DkGLNqVb.js";import{c as V}from"./config-CFOHD7nk.js";import me from"./add-group-B6RsDY0n.js";import ye from"./manage-config-B0P_8HfQ.js";import{M as g}from"./@arco-design-uttiljWv.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./edit-BT4iQK1s.js";import"./index-BskNDCu3.js";const ve={class:"ma-content-block lg:flex justify-between"},ke={class:"lg:w-3/12 w-full h-full p-4 pr-2"},ge={class:"flex justify-between items-center",style:{height:"30px"}},xe={class:"flex justify-between items-center"},Ve={class:"flex"},Oe={class:"lg:w-9/12 w-full p-4 pl-2"},Ce={class:"pl-4 pr-4"},be={class:"mb-2"},we={class:"text-red-500 underline font-black"},he={class:"text-red-500"},Eo={__name:"index",setup(Ue){const E=p(),O=p(),T=p({}),f=p([]),C=p([]),G=p(!1),$=p(""),z=p(""),y=p({name:""}),M=p(!1),_=p({}),b=p(""),A=async()=>{q(_.value.id,_.value.name,_.value.code)},I=l=>{const o=l.id;if(o==1||o==2||o==3){g.info("该配置为系统核心配置，无法删除");return}y.value=C.value.find(e=>e.id==o),M.value=!0},D=async()=>{const l=await V.getConfigGroupList();C.value=l.data;const o=C.value[0];await q(o.id,o.name,o.code)},q=async(l,o,e)=>{_.value.id=l,_.value.name=o,_.value.code=e,G.value=!0;const v={group_id:l,orderBy:"sort",orderType:"desc"};$.value=o;const w=await V.getConfigList(v);w.data.map(u=>(u.key.indexOf("local_")!==-1||u.key.indexOf("qiniu_")!==-1||u.key.indexOf("cos_")!==-1||u.key.indexOf("oss_")!==-1||u.key.indexOf("s3_")!==-1?u.display=!1:u.display=!0,u)),f.value=w.data,l===2&&f.value.map(u=>{u.key==="upload_mode"&&F(u.value,u)}),G.value=!1},F=async(l,o)=>{o.key==="upload_mode"&&(l==1&&f.value.map(e=>{e.key.indexOf("local_")!==-1&&(e.display=!0),(e.key.indexOf("qiniu_")!==-1||e.key.indexOf("cos_")!==-1||e.key.indexOf("oss_")!==-1||e.key.indexOf("s3_")!==-1)&&(e.display=!1)}),l==2&&f.value.map(e=>{e.key.indexOf("oss_")!==-1&&(e.display=!0),(e.key.indexOf("qiniu_")!==-1||e.key.indexOf("cos_")!==-1||e.key.indexOf("local_")!==-1||e.key.indexOf("s3_")!==-1)&&(e.display=!1)}),l==3&&f.value.map(e=>{e.key.indexOf("qiniu_")!==-1&&(e.display=!0),(e.key.indexOf("local_")!==-1||e.key.indexOf("cos_")!==-1||e.key.indexOf("oss_")!==-1||e.key.indexOf("s3_")!==-1)&&(e.display=!1)}),l==4&&f.value.map(e=>{e.key.indexOf("cos_")!==-1&&(e.display=!0),(e.key.indexOf("qiniu_")!==-1||e.key.indexOf("local_")!==-1||e.key.indexOf("oss_")!==-1||e.key.indexOf("s3_")!==-1)&&(e.display=!1)}),l==5&&f.value.map(e=>{e.key.indexOf("s3_")!==-1&&(e.display=!0),(e.key.indexOf("qiniu_")!==-1||e.key.indexOf("cos_")!==-1||e.key.indexOf("local_")!==-1||e.key.indexOf("oss_")!==-1)&&(e.display=!1)}))},P=async l=>{if(!_e("/core/config/save")){g.info("没有权限修改配置");return}const o={group_id:_.value.id,config:l},e=await V.batchUpdate(o);e.code===200&&g.success(e.message)},H=async()=>{if(!/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/.test(b.value)){g.info("请输入正确的邮箱地址");return}const o=await V.testEmail({email:b.value});o.code===200&&g.success(o.message)},J=()=>{const l=_.value.id;E.value.open(l)},K=()=>{var l;return(l=O.value)==null?void 0:l.open()},Q=l=>{var o,e;(o=O.value)==null||o.open("edit"),(e=O.value)==null||e.setFormData(l)},W=async l=>{if(z.value!==y.value.name){g.error("输入错误，验证失败"),l(!1);return}(await V.deleteConfigGroup({ids:y.value.id})).code===200&&(g.success("配置删除成功"),y.value={},D(),l(!0))};return pe(()=>{D()}),(l,o)=>{const e=n("icon-plus"),v=n("a-button"),w=n("a-tooltip"),u=n("icon-edit"),L=n("a-link"),X=n("icon-delete"),Y=n("a-list-item"),Z=n("a-list"),ee=n("icon-settings"),oe=n("a-select"),N=n("a-input"),ae=n("a-radio-group"),le=n("a-textarea"),ne=n("sa-upload-image"),te=n("sa-upload-file"),se=n("ma-wangEditor"),j=n("a-form-item"),ie=n("a-form"),de=n("a-card"),re=n("a-modal"),h=ce("auth");return i(),B("div",ve,[c("div",ke,[d(Z,{"max-height":650,scrollbar:!0,size:"small"},{header:t(()=>[c("div",ge,[o[6]||(o[6]=c("span",null,"配置分组",-1)),d(w,{content:"添加组"},{default:t(()=>[x((i(),r(v,{shape:"round",size:"small",onClick:K,type:"primary"},{icon:t(()=>[d(e)]),_:1})),[[h,["/core/config/save"]]])]),_:1})])]),default:t(()=>[(i(!0),B(R,null,S(C.value,(a,ue)=>(i(),r(Y,null,{default:t(()=>[c("div",xe,[d(v,{type:$.value==a.name?"outline":"",onClick:s=>q(a.id,a.name,a.code)},{default:t(()=>[k(U(a.name)+"("+U(a.code)+") ",1)]),_:2},1032,["type","onClick"]),c("div",Ve,[x((i(),r(L,{onClick:s=>Q(a)},{icon:t(()=>[d(u)]),_:2},1032,["onClick"])),[[h,["/core/config/update"]]]),x((i(),r(L,{onClick:s=>I(a)},{icon:t(()=>[d(X)]),_:2},1032,["onClick"])),[[h,["/core/config/destroy"]]])])])]),_:2},1024))),256))]),_:1})]),c("div",Oe,[d(de,{title:$.value,loading:G.value,"header-style":{height:"45px"}},{extra:t(()=>[x((i(),r(v,{type:"primary",shape:"circle",onClick:o[0]||(o[0]=a=>J())},{default:t(()=>[d(ee)]),_:1})),[[h,["/core/config/index"]]])]),default:t(()=>[c("div",Ce,[d(ie,{model:T.value,"auto-label-width":""},{default:t(()=>[(i(!0),B(R,null,S(f.value,(a,ue)=>x((i(),r(j,{label:a.name,field:a.key,extra:a.remark},{default:t(()=>[a.input_type==="select"?(i(),r(oe,{key:0,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s,options:a.config_select_data,onChange:s=>F(s,a),placeholder:"请选择"+a.name},null,8,["modelValue","onUpdate:modelValue","options","onChange","placeholder"])):m("",!0),a.input_type==="input"?(i(),r(N,{key:1,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s,placeholder:"请输入"+a.name},null,8,["modelValue","onUpdate:modelValue","placeholder"])):m("",!0),a.input_type==="radio"?(i(),r(ae,{key:2,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s,options:a.config_select_data},null,8,["modelValue","onUpdate:modelValue","options"])):m("",!0),a.input_type==="textarea"?(i(),r(le,{key:3,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s,placeholder:"请输入"+a.name},null,8,["modelValue","onUpdate:modelValue","placeholder"])):m("",!0),a.input_type==="uploadImage"?(i(),r(ne,{key:4,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s},null,8,["modelValue","onUpdate:modelValue"])):m("",!0),a.input_type==="uploadFile"?(i(),r(te,{key:5,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s},null,8,["modelValue","onUpdate:modelValue"])):m("",!0),a.input_type==="wangEditor"?(i(),r(se,{key:6,modelValue:a.value,"onUpdate:modelValue":s=>a.value=s},null,8,["modelValue","onUpdate:modelValue"])):m("",!0)]),_:2},1032,["label","field","extra"])),[[fe,a.display]])),256)),f.value.length>0?(i(),r(j,{key:0},{default:t(()=>[d(v,{type:"primary",onClick:o[1]||(o[1]=a=>P(f.value))},{default:t(()=>o[7]||(o[7]=[k("保存修改")])),_:1})]),_:1})):m("",!0),_.value.code==="email_config"?(i(),r(j,{key:1,label:"测试邮件"},{default:t(()=>[d(N,{modelValue:b.value,"onUpdate:modelValue":o[2]||(o[2]=a=>b.value=a),placeholder:"请输入正确的邮箱接收地址"},null,8,["modelValue"]),d(v,{type:"primary",class:"ml-2",onClick:o[3]||(o[3]=a=>H())},{default:t(()=>o[8]||(o[8]=[k("发送")])),_:1})]),_:1})):m("",!0)]),_:1},8,["model"])])]),_:1},8,["title","loading"])]),d(re,{visible:M.value,"onUpdate:visible":o[5]||(o[5]=a=>M.value=a),type:"warning","on-before-ok":W},{title:t(()=>o[9]||(o[9]=[k("危险操作")])),default:t(()=>[c("div",be,[o[10]||(o[10]=k(" 确定要删除 ")),c("span",we,U(y.value.name),1),o[11]||(o[11]=k(" 配置组吗？ "))]),c("div",null,[o[12]||(o[12]=k(" 此操作会删除组和所属组配置项，如果执行请在下面输入框输入：")),c("span",he,U(y.value.name),1)]),d(N,{placeholder:`请输入 ${y.value.name}`,class:"mt-2",modelValue:z.value,"onUpdate:modelValue":o[4]||(o[4]=a=>z.value=a)},null,8,["placeholder","modelValue"])]),_:1},8,["visible"]),d(me,{ref_key:"addGroupRef",ref:O,onSuccess:D},null,512),d(ye,{ref_key:"manageConfigRef",ref:E,onClose:A},null,512)])}}};export{Eo as default};
