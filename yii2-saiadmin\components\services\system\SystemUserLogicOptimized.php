<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 优化版本
// +----------------------------------------------------------------------
// | Author: AI Assistant (优化版本)
// +----------------------------------------------------------------------
namespace app\components\services\system;

use plugin\saiadmin\app\cache\UserInfoCache;
use plugin\saiadmin\app\model\system\SystemDept;
use plugin\saiadmin\app\model\system\SystemRole;
use plugin\saiadmin\app\model\system\SystemUser;
use plugin\saiadmin\exception\ApiException;
use yii\base\Component;
use Webman\Event\Event;
use Tinywan\Jwt\JwtToken;

/**
 * 用户信息逻辑层 - 优化版本
 */
class SystemUserLogicOptimized extends BaseLogic
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new SystemUser();
    }

    /**
     * 读取数据 - 优化版本
     * @param $id
     * @return array
     */
    public function read($id): array
    {
        // 优化1：使用预加载避免N+1查询问题
        $admin = $this->model->with(['roles', 'posts', 'depts'])->findOrEmpty($id);
        
        if ($admin->isEmpty()) {
            throw new ApiException('用户不存在');
        }
        
        $data = $admin->hidden(['password'])->toArray();
        $data['roleList'] = $admin->roles->toArray() ?: [];
        $data['postList'] = $admin->posts->toArray() ?: [];
        $data['deptList'] = $admin->depts ? $admin->depts->toArray() : [];
        
        if ($this->adminInfo['id'] > 1) {
            // 优化2：缓存部门权限查询结果
            $dept_ids = $this->getCachedDeptPermissions($this->adminInfo['dept_id']);
            
            if (!in_array($admin['dept_id'], $dept_ids)) {
                throw new ApiException('没有权限操作该部门数据');
            }
        }
        return $data;
    }

    /**
     * 获取缓存的部门权限
     * @param int $deptId
     * @return array
     */
    private function getCachedDeptPermissions(int $deptId): array
    {
        $cacheKey = 'dept_permissions_' . $deptId;
        $dept_ids = cache($cacheKey);
        
        if ($dept_ids === null) {
            // 优化3：使用更高效的查询方式
            $dept_ids = SystemDept::where('level', 'like', "%{$deptId}%")->column('id');
            cache($cacheKey, $dept_ids, 3600); // 缓存1小时
        }
        
        return $dept_ids;
    }

    /**
     * 批量获取用户信息 - 新增优化方法
     * @param array $userIds
     * @return array
     */
    public function batchRead(array $userIds): array
    {
        if (empty($userIds)) {
            return [];
        }

        // 优化：一次性查询所有用户及关联数据
        $users = $this->model->with(['roles', 'posts', 'depts'])
            ->whereIn('id', $userIds)
            ->select();

        $result = [];
        foreach ($users as $user) {
            $data = $user->hidden(['password'])->toArray();
            $data['roleList'] = $user->roles->toArray() ?: [];
            $data['postList'] = $user->posts->toArray() ?: [];
            $data['deptList'] = $user->depts ? $user->depts->toArray() : [];
            $result[] = $data;
        }

        return $result;
    }

    /**
     * 优化的分页查询
     * @param array $where
     * @return mixed
     */
    public function getOptimizedList(array $where = [])
    {
        $query = $this->model->with(['roles:id,name', 'posts:id,name', 'dept:id,name']);
        
        // 优化：使用索引友好的查询条件
        if (!empty($where['username'])) {
            $query->where('username', 'like', "%{$where['username']}%");
        }
        
        if (!empty($where['status'])) {
            $query->where('status', $where['status']);
        }
        
        if (!empty($where['dept_id'])) {
            $query->where('dept_id', $where['dept_id']);
        }
        
        if (!empty($where['create_time']) && is_array($where['create_time'])) {
            $query->whereBetween('create_time', $where['create_time']);
        }

        return $query->order('id', 'desc')->paginate();
    }

    /**
     * 清除相关缓存
     * @param int $userId
     * @param int $deptId
     */
    public function clearRelatedCache(int $userId, int $deptId = null): void
    {
        // 清除用户信息缓存
        $userInfoCache = new UserInfoCache($userId);
        $userInfoCache->clearUserInfo();
        
        // 清除部门权限缓存
        if ($deptId) {
            cache()->delete('dept_permissions_' . $deptId);
        }
    }
}
