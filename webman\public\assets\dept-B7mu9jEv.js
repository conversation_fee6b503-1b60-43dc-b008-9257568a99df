import{h as t}from"./index-ybrmzYq5.js";const o={getPageList(e={}){return t({url:"/core/dept/index",method:"get",params:e})},getLeaderList(e={}){return t({url:"/core/dept/leaders",method:"get",params:e})},addLeader(e={}){return t({url:"/core/dept/addLeader",method:"post",data:e})},delLeader(e={}){return t({url:"/core/dept/delLeader",method:"delete",data:e})},tree(){return t({url:"/core/dept/index?tree=true",method:"get"})},save(e={}){return t({url:"/core/dept/save",method:"post",data:e})},destroy(e){return t({url:"/core/dept/destroy",method:"delete",data:e})},update(e,r={}){return t({url:"/core/dept/update?id="+e,method:"put",data:r})},changeStatus(e={}){return t({url:"/core/dept/changeStatus",method:"post",data:e})}};export{o as a};
