import{g as r}from"./index-DkGLNqVb.js";const u={getPageList(e={}){return r({url:"/core/role/index",method:"get",params:e})},getMenuByRole(e){return r({url:"/core/role/getMenuByRole?id="+e,method:"get"})},getDeptByRole(e){return r({url:"/core/role/getDeptByRole?id="+e,method:"get"})},save(e={}){return r({url:"/core/role/save",method:"post",data:e})},destroy(e){return r({url:"/core/role/destroy",method:"delete",data:e})},update(e,t={}){return r({url:"/core/role/update?id="+e,method:"put",data:t})},updateMenuPermission(e,t){return r({url:"/core/role/menuPermission?id="+e,method:"post",data:t})},updateDataPermission(e,t){return r({url:"/core/role/dataPermission?id="+e,method:"post",data:t})},changeStatus(e={}){return r({url:"/core/role/changeStatus",method:"post",data:e})}};export{u as a};
