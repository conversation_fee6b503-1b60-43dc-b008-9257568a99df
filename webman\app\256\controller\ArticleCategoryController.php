<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: SaiAdmin Team
// +----------------------------------------------------------------------
namespace app\controller;

use plugin\saiadmin\basic\BaseController;
use app\logic\ArticleCategoryLogic;
use app\validate\ArticleCategoryValidate;
use support\Request;
use support\Response;

/**
 * 文章分类表控制器
 */
class ArticleCategoryController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new ArticleCategoryLogic();
        $this->validate = new ArticleCategoryValidate();
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        $where = $request->more([
            ['category_name', ''],
            ['parent_id', ''],
            ['status', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * 获取单条数据
     * @param Request $request
     * @return Response
     */
    public function read(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->fail('参数错误');
        }

        $data = $this->logic->read($id);
        if (empty($data)) {
            return $this->fail('数据不存在');
        }

        return $this->success($data);
    }

    /**
     * 保存数据
     * @param Request $request
     * @return Response
     */
    public function save(Request $request)
    {
        $data = $request->post();

        // 数据验证
        if (!$this->validate->scene('save')->check($data)) {
            return $this->fail($this->validate->getError());
        }

        try {
            $result = $this->logic->save($data);
            if ($result) {
                return $this->success([], '保存成功');
            } else {
                return $this->fail('保存失败');
            }
        } catch (\Exception $e) {
            return $this->fail('保存失败：' . $e->getMessage());
        }
    }

    /**
     * 更新数据
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        $data = $request->post();
        $id = $request->input('id');

        if (empty($id)) {
            return $this->fail('参数错误');
        }

        // 数据验证
        if (!$this->validate->scene('update')->check($data)) {
            return $this->fail($this->validate->getError());
        }

        try {
            $result = $this->logic->update($id, $data);
            if ($result) {
                return $this->success([], '更新成功');
            } else {
                return $this->fail('更新失败');
            }
        } catch (\Exception $e) {
            return $this->fail('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除数据
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $this->fail('参数错误');
        }

        try {
            $result = $this->logic->delete($ids);
            if ($result) {
                return $this->success([], '删除成功');
            } else {
                return $this->fail('删除失败');
            }
        } catch (\Exception $e) {
            return $this->fail('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 修改状态
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');

        if (empty($id) || !isset($status)) {
            return $this->fail('参数错误');
        }

        try {
            $result = $this->logic->changeStatus($id, $status);
            if ($result) {
                return $this->success([], '状态修改成功');
            } else {
                return $this->fail('状态修改失败');
            }
        } catch (\Exception $e) {
            return $this->fail('状态修改失败：' . $e->getMessage());
        }
    }

    /**
     * 获取分类树形结构
     * @param Request $request
     * @return Response
     */
    public function getTree(Request $request)
    {
        try {
            $data = $this->logic->getTree();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail('获取分类树失败：' . $e->getMessage());
        }
    }

    /**
     * 数字运算操作
     * @param Request $request
     * @return Response
     */
    public function numberOperation(Request $request)
    {
        $id = $request->input('id');
        $field = $request->input('field');
        $value = $request->input('value');

        if (empty($id) || empty($field) || !isset($value)) {
            return $this->fail('参数错误');
        }

        try {
            $result = $this->logic->numberOperation($id, $field, $value);
            if ($result) {
                return $this->success([], '操作成功');
            } else {
                return $this->fail('操作失败');
            }
        } catch (\Exception $e) {
            return $this->fail('操作失败：' . $e->getMessage());
        }
    }
}
