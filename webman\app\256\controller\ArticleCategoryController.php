<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\256\controller;

use plugin\saiadmin\basic\BaseController;
use app\256\logic\ArticleCategoryLogic;
use app\256\validate\ArticleCategoryValidate;
use support\Request;
use support\Response;

/**
 * 文章分类表控制器
 */
class ArticleCategoryController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new ArticleCategoryLogic();
        $this->validate = new ArticleCategoryValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['category_name', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

}
