<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\ceshi\model\ceshi;

use plugin\saiadmin\basic\BaseModel;

/**
 * 23模型
 */
class DdwxArticle extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'ddwx_article';

    /**
     * 数据库连接
     * @var string
     */
    protected $connection = 'mysql_read';

    /**
     *  搜索
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%'.$value.'%');
    }

    /**
     *  搜索
     */
    public function searchSubnameAttr($query, $value)
    {
        $query->where('subname', 'like', '%'.$value.'%');
    }

    /**
     *  搜索
     */
    public function searchShownameAttr($query, $value)
    {
        $query->where('showname', 'like', '%'.$value.'%');
    }

    /**
     *  搜索
     */
    public function searchShowsubnameAttr($query, $value)
    {
        $query->where('showsubname', 'like', '%'.$value.'%');
    }

}
