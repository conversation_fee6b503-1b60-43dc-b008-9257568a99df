<template>
  <component
    is="a-modal"
    :width="tool.getDevice() === 'mobile' ? '100%' : '600px'"
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :ok-loading="loading"
    @cancel="close"
    @before-ok="submit">
    <!-- 表单信息 start -->
    <a-form ref="formRef" :model="formData" :rules="rules" :auto-label-width="true">
      <a-form-item label="" field="goods_id">
        <a-input v-model="formData.goods_id" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="mall_id">
        <a-input v-model="formData.mall_id" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="阶梯规则" field="ladder_rules">
        <a-input v-model="formData.ladder_rules" placeholder="请输入阶梯规则" />
      </a-form-item>
      <a-form-item label="" field="deposit">
        <a-input v-model="formData.deposit" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="定金膨胀金" field="swell_deposit">
        <a-input v-model="formData.swell_deposit" placeholder="请输入定金膨胀金" />
      </a-form-item>
      <a-form-item label="预售开始时间" field="start_prepayment_at">
        <a-date-picker v-model="formData.start_prepayment_at" :show-time="false" mode="date" placeholder="请选择预售开始时间" />
      </a-form-item>
      <a-form-item label="预售结束时间" field="end_prepayment_at">
        <a-date-picker v-model="formData.end_prepayment_at" :show-time="false" mode="date" placeholder="请选择预售结束时间" />
      </a-form-item>
      <a-form-item label="尾款支付时间 -1:无限制" field="pay_limit">
        <a-input v-model="formData.pay_limit" placeholder="请输入尾款支付时间 -1:无限制" />
      </a-form-item>
    </a-form>
    <!-- 表单信息 end -->
  </component>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import tool from '@/utils/tool'
import { Message, Modal } from '@arco-design/web-vue'
import api from '../api/goods'

const emit = defineEmits(['success'])
// 引用定义
const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const mode = ref('')

let title = computed(() => {
  return 'wer' + (mode.value == 'add' ? '-新增' : '-编辑')
})

// 表单初始值
const initialFormData = {
  id: null,
  goods_id: null,
  mall_id: null,
  ladder_rules: '',
  deposit: '0.00',
  swell_deposit: '0.00',
  start_prepayment_at: '',
  end_prepayment_at: '',
  pay_limit: null,
}

// 表单信息
const formData = reactive({ ...initialFormData })

// 验证规则
const rules = {
  goods_id: [{ required: true, message: '必需填写' }],
  mall_id: [{ required: true, message: '必需填写' }],
}

// 打开弹框
const open = async (type = 'add') => {
  mode.value = type
  // 重置表单数据
  Object.assign(formData, initialFormData)
  formRef.value.clearValidate()
  visible.value = true
  await initPage()
}

// 初始化页面数据
const initPage = async () => {}

// 设置数据
const setFormData = async (data) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key]
    }
  }
}

// 数据保存
const submit = async (done) => {
  const validate = await formRef.value?.validate()
  if (!validate) {
    loading.value = true
    let data = { ...formData }
    let result = {}
    if (mode.value === 'add') {
      // 添加数据
      data.id = undefined
      result = await api.save(data)
    } else {
      // 修改数据
      result = await api.update(data.id, data)
    }
    if (result.code === 200) {
      Message.success('操作成功')
      emit('success')
      done(true)
    }
    // 防止连续点击提交
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
  done(false)
}

// 关闭弹窗
const close = () => (visible.value = false)

defineExpose({ open, setFormData })
</script>
