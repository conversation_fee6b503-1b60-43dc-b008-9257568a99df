{"version": 3, "sources": ["../../@arco-design/web-vue/es/locale/lang/en-us.js"], "sourcesContent": ["import { DefaultValidateMessage } from \"b-validate\";\nconst calendarLang = {\n  formatYear: \"YYYY\",\n  formatMonth: \"MMM YYYY\",\n  today: \"Today\",\n  view: {\n    month: \"Month\",\n    year: \"Year\",\n    week: \"Week\",\n    day: \"Day\"\n  },\n  month: {\n    long: {\n      January: \"January\",\n      February: \"February\",\n      March: \"March\",\n      April: \"April\",\n      May: \"May\",\n      June: \"June\",\n      July: \"July\",\n      August: \"August\",\n      September: \"September\",\n      October: \"October\",\n      November: \"November\",\n      December: \"December\"\n    },\n    short: {\n      January: \"Jan\",\n      February: \"Feb\",\n      March: \"Mar\",\n      April: \"Apr\",\n      May: \"May\",\n      June: \"Jun\",\n      July: \"Jul\",\n      August: \"Aug\",\n      September: \"Sept\",\n      October: \"Oct\",\n      November: \"Nov\",\n      December: \"Dec\"\n    }\n  },\n  week: {\n    long: {\n      self: \"Week\",\n      monday: \"Monday\",\n      tuesday: \"Tuesday\",\n      wednesday: \"Wednesday\",\n      thursday: \"Thursday\",\n      friday: \"Friday\",\n      saturday: \"Saturday\",\n      sunday: \"Sunday\"\n    },\n    short: {\n      self: \"Week\",\n      monday: \"Mon\",\n      tuesday: \"Tue\",\n      wednesday: \"Wed\",\n      thursday: \"Thu\",\n      friday: \"Fri\",\n      saturday: \"Sat\",\n      sunday: \"Sun\"\n    }\n  }\n};\nconst lang = {\n  locale: \"en-US\",\n  empty: {\n    description: \"No Data\"\n  },\n  drawer: {\n    okText: \"Ok\",\n    cancelText: \"Cancel\"\n  },\n  popconfirm: {\n    okText: \"Ok\",\n    cancelText: \"Cancel\"\n  },\n  modal: {\n    okText: \"Ok\",\n    cancelText: \"Cancel\"\n  },\n  pagination: {\n    goto: \"Goto\",\n    page: \"Page\",\n    countPerPage: \" / Page\",\n    total: \"Total: {0}\"\n  },\n  table: {\n    okText: \"Ok\",\n    resetText: \"Reset\"\n  },\n  upload: {\n    start: \"Start\",\n    cancel: \"Cancel\",\n    delete: \"Delete\",\n    retry: \"Click to retry\",\n    buttonText: \"Upload\",\n    preview: \"Preview\",\n    drag: \"Click or drag file to this area to upload\",\n    dragHover: \"Release to upload\",\n    error: \"Upload Error\"\n  },\n  calendar: calendarLang,\n  datePicker: {\n    view: calendarLang.view,\n    month: calendarLang.month,\n    week: calendarLang.week,\n    placeholder: {\n      date: \"Please select date\",\n      week: \"Please select week\",\n      month: \"Please select month\",\n      year: \"Please select year\",\n      quarter: \"Please select quarter\",\n      time: \"Please select time\"\n    },\n    rangePlaceholder: {\n      date: [\"Start date\", \"End date\"],\n      week: [\"Start week\", \"End week\"],\n      month: [\"Start month\", \"End month\"],\n      year: [\"Start year\", \"End year\"],\n      quarter: [\"Start quarter\", \"End quarter\"],\n      time: [\"Start time\", \"End time\"]\n    },\n    selectTime: \"Select time\",\n    today: \"Today\",\n    now: \"Now\",\n    ok: \"Ok\"\n  },\n  image: {\n    loading: \"loading\"\n  },\n  imagePreview: {\n    fullScreen: \"Full Screen\",\n    rotateRight: \"Rotate Right\",\n    rotateLeft: \"Rotate Left\",\n    zoomIn: \"Zoom In\",\n    zoomOut: \"Zoom Out\",\n    originalSize: \"Original Size\"\n  },\n  typography: {\n    copied: \"Copied\",\n    copy: \"Copy\",\n    expand: \"Expand\",\n    collapse: \"Collapse\",\n    edit: \"Edit\"\n  },\n  form: {\n    validateMessages: DefaultValidateMessage\n  },\n  colorPicker: {\n    history: \"History Colors\",\n    preset: \"Preset Colors\",\n    empty: \"Empty\"\n  }\n};\nexport { lang as default };\n"], "mappings": ";;;;;;AACA,IAAM,eAAe;AAAA,EACnB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAM,OAAO;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,IACV,MAAM,aAAa;AAAA,IACnB,OAAO,aAAa;AAAA,IACpB,MAAM,aAAa;AAAA,IACnB,aAAa;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM,CAAC,cAAc,UAAU;AAAA,MAC/B,MAAM,CAAC,cAAc,UAAU;AAAA,MAC/B,OAAO,CAAC,eAAe,WAAW;AAAA,MAClC,MAAM,CAAC,cAAc,UAAU;AAAA,MAC/B,SAAS,CAAC,iBAAiB,aAAa;AAAA,MACxC,MAAM,CAAC,cAAc,UAAU;AAAA,IACjC;AAAA,IACA,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,KAAK;AAAA,IACL,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,kBAAkB;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACF;", "names": []}