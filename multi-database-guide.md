# SaiAdmin 多数据源配置指南

## 📋 概述

SaiAdmin 支持多数据源配置，包括读写分离、分库分表、不同类型数据库等功能。本指南将详细介绍如何配置和使用多数据源。

## 🔧 配置步骤

### 1. 环境变量配置 (.env)

```bash
# 主数据库配置
DB_TYPE = mysql
DB_HOST = 127.0.0.1
DB_PORT = 3306
DB_NAME = saiadmin
DB_USER = root
DB_PASSWORD = your_password

# 读写分离配置
DB_RW_SEPARATE = true

# 从数据库配置（读库）
DB_READ_HOST = 127.0.0.1
DB_READ_PORT = 3306
DB_READ_NAME = saiadmin
DB_READ_USER = root
DB_READ_PASSWORD = your_password

# 日志数据库配置
DB_LOG_HOST = 127.0.0.1
DB_LOG_PORT = 3306
DB_LOG_NAME = saiadmin_logs
DB_LOG_USER = root
DB_LOG_PASSWORD = your_password

# 缓存数据库配置
DB_CACHE_HOST = 127.0.0.1
DB_CACHE_PORT = 3306
DB_CACHE_NAME = saiadmin_cache
DB_CACHE_USER = root
DB_CACHE_PASSWORD = your_password

# PostgreSQL数据库配置
PGSQL_HOST = 127.0.0.1
PGSQL_PORT = 5432
PGSQL_NAME = saiadmin_pg
PGSQL_USER = postgres
PGSQL_PASSWORD = your_password
```

### 2. 数据库连接配置 (think-orm.php)

配置文件已自动生成，包含以下连接：

- `mysql` - 主数据库
- `mysql_read` - 读库（读写分离）
- `mysql_log` - 日志数据库
- `mysql_cache` - 缓存数据库
- `pgsql` - PostgreSQL数据库
- `sqlite` - SQLite数据库

## 🚀 使用方法

### 1. 基本连接使用

```php
use plugin\saiadmin\app\database\MultiDatabaseManager;

// 获取主数据库连接
$mainDb = MultiDatabaseManager::main();
$users = $mainDb->table('sa_system_user')->select();

// 获取日志数据库连接
$logDb = MultiDatabaseManager::log();
$logs = $logDb->table('log_operation')->select();

// 获取缓存数据库连接
$cacheDb = MultiDatabaseManager::cache();
$cache = $cacheDb->table('cache_data')->select();

// 获取PostgreSQL连接
$pgDb = MultiDatabaseManager::pgsql();
$data = $pgDb->table('your_table')->select();
```

### 2. 读写分离

```php
// 自动读写分离查询
$result = MultiDatabaseManager::query('SELECT * FROM sa_system_user');

// 强制写库操作
$result = MultiDatabaseManager::execute('UPDATE sa_system_user SET status = 1');

// 事务处理（自动使用写库）
MultiDatabaseManager::transaction(function() {
    // 事务操作
    MultiDatabaseManager::main()->table('sa_system_user')->insert($data);
    MultiDatabaseManager::main()->table('sa_system_role')->insert($roleData);
});
```

### 3. 模型使用

#### 操作日志模型

```php
use plugin\saiadmin\app\model\OperationLog;

// 记录操作日志
OperationLog::record([
    'user_id' => 1,
    'username' => 'admin',
    'method' => 'POST',
    'router' => '/api/users',
    'service_name' => 'UserService',
    'ip' => '127.0.0.1',
    'ip_location' => '本地',
    'request_data' => ['name' => 'test'],
    'response_code' => '200',
    'response_data' => ['status' => 'success']
]);

// 获取用户操作日志
$logs = OperationLog::getUserLogs(1, 50);
```

#### 登录日志模型

```php
use plugin\saiadmin\app\model\LoginLog;

// 记录登录日志
LoginLog::record([
    'username' => 'admin',
    'ip' => '127.0.0.1',
    'ip_location' => '本地',
    'os' => 'Windows 10',
    'browser' => 'Chrome',
    'status' => 1,
    'message' => '登录成功'
]);

// 获取登录统计
$stats = LoginLog::getLoginStats('2024-01-01');
```

#### 性能日志模型

```php
use plugin\saiadmin\app\model\PerformanceLog;

// 记录性能日志
PerformanceLog::record([
    'request_id' => uniqid(),
    'uri' => '/api/users',
    'method' => 'GET',
    'response_time' => 125.50,
    'memory_usage' => 2048000,
    'query_count' => 3
]);

// 获取性能统计
$stats = PerformanceLog::getPerformanceStats('2024-01-01');
```

#### 缓存数据模型

```php
use plugin\saiadmin\app\model\CacheData;

// 设置缓存
CacheData::setCache('user_list', $userList, 3600);

// 获取缓存
$userList = CacheData::getCache('user_list');

// 删除缓存
CacheData::deleteCache('user_list');

// 清理过期缓存
$cleanCount = CacheData::cleanExpired();
```

### 4. 自定义模型

```php
use plugin\saiadmin\app\model\MultiDatabaseModel;

class CustomModel extends MultiDatabaseModel
{
    // 指定使用的数据库连接
    protected $connection = 'mysql_log';
    
    // 启用读写分离
    protected $readWriteSeparation = true;
    
    // 从读库查询
    public function getDataFromRead()
    {
        return $this->selectFromRead(['status' => 1]);
    }
    
    // 写入数据到写库
    public function saveDataToWrite($data)
    {
        return $this->insertToWrite($data);
    }
}
```

## 🔍 监控和管理

### 1. 健康检查

```php
// 检查所有连接状态
$status = MultiDatabaseManager::getAllConnectionStatus();

// 数据库健康检查
$health = MultiDatabaseManager::healthCheck();

// 获取数据库统计信息
$stats = MultiDatabaseManager::getStatistics();
```

### 2. 运行测试脚本

```bash
php multi-database-test.php
```

## 📊 最佳实践

### 1. 数据库分离策略

- **主数据库**: 存储核心业务数据
- **日志数据库**: 存储操作日志、登录日志、性能日志
- **缓存数据库**: 存储缓存数据、会话数据
- **读库**: 承担查询压力，减轻主库负担

### 2. 读写分离建议

- 查询操作自动路由到读库
- 写操作强制使用主库
- 事务操作统一使用主库
- 实时性要求高的查询使用主库

### 3. 性能优化

- 合理配置连接池参数
- 定期清理日志和缓存数据
- 监控数据库连接状态
- 使用索引优化查询性能

### 4. 安全建议

- 不同数据库使用不同的用户权限
- 日志数据库只给写权限
- 读库只给查询权限
- 定期备份重要数据

## 🚨 注意事项

1. **数据一致性**: 读写分离可能存在主从延迟
2. **事务处理**: 跨数据库事务需要特别处理
3. **连接管理**: 合理配置连接池避免连接泄露
4. **错误处理**: 做好数据库连接失败的降级处理
5. **监控告警**: 建立数据库监控和告警机制

## 🔧 故障排除

### 常见问题

1. **连接失败**: 检查数据库服务状态和网络连接
2. **权限错误**: 确认数据库用户权限配置
3. **字符集问题**: 统一使用 utf8mb4 字符集
4. **性能问题**: 检查索引和查询优化

### 调试方法

```php
// 启用SQL日志
$db = MultiDatabaseManager::main();
$db->getConnection()->log(true);

// 查看执行的SQL
$sqlLogs = $db->getConnection()->getQueryLog();
```

## 📈 扩展功能

- 支持更多数据库类型（MongoDB、Redis等）
- 实现自动故障转移
- 添加数据库负载均衡
- 支持分库分表策略
- 集成数据库监控面板
