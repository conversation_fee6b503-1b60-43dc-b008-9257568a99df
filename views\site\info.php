<?php
/**
 * 系统信息页面视图
 */
use yii\helpers\Html;

$this->title = '系统信息';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="site-info">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">🖥️ 系统环境</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <?php foreach ($system_info as $key => $value): ?>
                            <tr>
                                <td><strong><?= Html::encode($key) ?></strong></td>
                                <td><?= Html::encode($value) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">🚀 应用信息</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <?php foreach ($application_info as $key => $value): ?>
                            <tr>
                                <td><strong><?= Html::encode($key) ?></strong></td>
                                <td><?= Html::encode($value) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">📊 性能信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>内存使用</h6>
                                <div class="h4 text-primary">
                                    <?= number_format(memory_get_usage(true) / 1024 / 1024, 2) ?> MB
                                </div>
                                <small class="text-muted">
                                    峰值: <?= number_format(memory_get_peak_usage(true) / 1024 / 1024, 2) ?> MB
                                </small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>执行时间</h6>
                                <div class="h4 text-success">
                                    <?= number_format((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) ?> ms
                                </div>
                                <small class="text-muted">毫秒</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>包含文件</h6>
                                <div class="h4 text-info">
                                    <?= count(get_included_files()) ?>
                                </div>
                                <small class="text-muted">个文件</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>服务器时间</h6>
                                <div class="h4 text-warning">
                                    <?= date('H:i:s') ?>
                                </div>
                                <small class="text-muted"><?= date('Y-m-d') ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">🔧 PHP 扩展</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php 
                        $extensions = get_loaded_extensions();
                        sort($extensions);
                        $chunks = array_chunk($extensions, ceil(count($extensions) / 4));
                        ?>
                        <?php foreach ($chunks as $chunk): ?>
                            <div class="col-md-3">
                                <ul class="list-unstyled">
                                    <?php foreach ($chunk as $extension): ?>
                                        <li>
                                            <i class="fas fa-check text-success me-1"></i>
                                            <?= Html::encode($extension) ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="text-center">
                <?= Html::a('返回首页', ['/'], ['class' => 'btn btn-primary']) ?>
                <?= Html::a('关于我们', ['/site/about'], ['class' => 'btn btn-outline-primary']) ?>
                <?= Html::a('联系我们', ['/site/contact'], ['class' => 'btn btn-outline-info']) ?>
            </div>
        </div>
    </div>
</div>
