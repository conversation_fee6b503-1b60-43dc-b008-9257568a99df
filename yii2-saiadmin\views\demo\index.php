<?php
/**
 * 演示首页视图
 */
use yii\helpers\Html;

$this->title = $title;
?>

<div class="demo-index">
    <div class="jumbotron">
        <h1 class="display-4"><?= Html::encode($title) ?></h1>
        <p class="lead"><?= Html::encode($message) ?></p>
        <hr class="my-4">
        <p>这是一个基于 Yii 2.0 框架的 SaiAdmin 演示页面。</p>
        <?= Html::a("开始探索", ["demo/api"], ["class" => "btn btn-primary btn-lg"]) ?>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <h3>🚀 框架特性</h3>
            <ul class="list-group">
                <?php foreach ($features as $feature): ?>
                    <li class="list-group-item"><?= Html::encode($feature) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <div class="col-md-6">
            <h3>🔗 快速链接</h3>
            <div class="list-group">
                <?= Html::a("API演示", ["demo/api"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("表单演示", ["demo/form"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("数据库演示", ["demo/database"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("缓存演示", ["demo/cache"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("Gii代码生成", ["/gii"], ["class" => "list-group-item list-group-item-action", "target" => "_blank"]) ?>
                <?= Html::a("调试工具", ["/debug"], ["class" => "list-group-item list-group-item-action", "target" => "_blank"]) ?>
            </div>
        </div>
    </div>
</div>