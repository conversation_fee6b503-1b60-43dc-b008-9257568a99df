<?php
/**
 * 登录页面视图
 */
use yii\helpers\Html;
use yii\widgets\ActiveForm;

$this->title = '登录';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="site-login">
    <h1><?= Html::encode($this->title) ?></h1>

    <p>请填写以下字段进行登录：</p>

    <div class="row">
        <div class="col-lg-5">
            <?php $form = ActiveForm::begin([
                'id' => 'login-form',
                'options' => ['class' => 'form-horizontal'],
                'fieldConfig' => [
                    'template' => "{label}\n{input}\n{error}",
                    'labelOptions' => ['class' => 'col-lg-1 col-form-label mr-lg-3'],
                    'inputOptions' => ['class' => 'form-control'],
                    'errorOptions' => ['class' => 'col-lg-7 invalid-feedback'],
                ],
            ]); ?>

            <?= $form->field($model, 'username')->textInput(['autofocus' => true, 'placeholder' => '请输入用户名']) ?>

            <?= $form->field($model, 'password')->passwordInput(['placeholder' => '请输入密码']) ?>

            <?= $form->field($model, 'rememberMe')->checkbox([
                'template' => "<div class=\"custom-control custom-checkbox\">{input} {label}</div>\n<div class=\"col-lg-8\">{error}</div>",
            ]) ?>

            <div class="form-group">
                <div>
                    <?= Html::submitButton('登录', ['class' => 'btn btn-primary', 'name' => 'login-button']) ?>
                    <?= Html::a('返回首页', ['/'], ['class' => 'btn btn-secondary']) ?>
                </div>
            </div>

            <?php ActiveForm::end(); ?>

            <div class="alert alert-info mt-3">
                <strong>演示账号：</strong><br>
                用户名：<code>admin</code><br>
                密码：<code>admin</code>
            </div>
        </div>
    </div>
</div>
