#!/bin/bash
# SaiAdmin Yii 2.0 快速测试脚本

echo "🧪 SaiAdmin Yii 2.0 快速测试"
echo "================================"

# 测试控制台
echo "📋 测试控制台命令..."
php yii help > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 控制台命令正常"
else
    echo "❌ 控制台命令异常"
fi

# 测试Web访问
echo "🌐 测试Web访问..."
if command -v curl &> /dev/null; then
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/demo)
    if [ "$response" = "200" ]; then
        echo "✅ Web访问正常"
    else
        echo "⚠️ Web访问异常 (HTTP $response)"
    fi
else
    echo "⚠️ curl命令不可用，跳过Web测试"
fi

# 测试API
echo "🔌 测试API接口..."
if command -v curl &> /dev/null; then
    api_response=$(curl -s http://localhost:8080/demo/api | head -1)
    if [[ "$api_response" == *"code"* ]]; then
        echo "✅ API接口正常"
    else
        echo "⚠️ API接口异常"
    fi
fi

echo ""
echo "🎯 测试完成！"
echo "📖 查看开发指南: cat DEVELOPMENT-GUIDE.md"
echo "🌐 访问演示页面: http://localhost:8080/demo"