<?php
/**
 * 创建用户表迁移
 */
use yii\db\Migration;

class m250803_062857_create_user_table extends Migration
{
    public function safeUp()
    {
        $this->createTable("{{%user}}", [
            "id" => $this->primaryKey(),
            "username" => $this->string()->notNull()->unique(),
            "auth_key" => $this->string(32)->notNull(),
            "password_hash" => $this->string()->notNull(),
            "password_reset_token" => $this->string()->unique(),
            "email" => $this->string()->notNull()->unique(),
            "status" => $this->smallInteger()->notNull()->defaultValue(10),
            "created_at" => $this->integer()->notNull(),
            "updated_at" => $this->integer()->notNull(),
        ]);
    }

    public function safeDown()
    {
        $this->dropTable("{{%user}}");
    }
}