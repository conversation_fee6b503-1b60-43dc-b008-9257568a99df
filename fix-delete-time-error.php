<?php
/**
 * 修复 delete_time 字段错误
 * 解决 SQLSTATE[42S22]: Column not found: 1054 Unknown column 'delete_time' 问题
 */

echo "🔧 修复 delete_time 字段错误\n";
echo "========================================\n\n";

// 1. 分析问题
echo "[1/4] 问题分析:\n";
echo "  ❌ 错误: Unknown column 'ddwx_article.delete_time'\n";
echo "  🔍 原因: BaseModel使用了软删除，但数据库表缺少delete_time字段\n";
echo "  📋 表前缀: 配置为 'sa_' 但错误显示 'ddwx_'\n\n";

// 2. 检查BaseModel配置
echo "[2/4] 检查BaseModel配置:\n";
$baseModelFile = 'webman/plugin/saiadmin/basic/BaseModel.php';
if (file_exists($baseModelFile)) {
    $content = file_get_contents($baseModelFile);
    
    if (strpos($content, 'use SoftDelete') !== false) {
        echo "  ✅ 发现软删除功能已启用\n";
    }
    
    if (strpos($content, "deleteTime = 'delete_time'") !== false) {
        echo "  ✅ 发现delete_time字段配置\n";
    }
    
    if (strpos($content, "createTime = 'create_time'") !== false) {
        echo "  ✅ 发现create_time字段配置\n";
    }
    
    if (strpos($content, "updateTime = 'update_time'") !== false) {
        echo "  ✅ 发现update_time字段配置\n";
    }
} else {
    echo "  ❌ BaseModel文件不存在\n";
}
echo "\n";

// 3. 创建修复方案
echo "[3/4] 创建修复方案:\n";

// 方案1: 创建不使用软删除的BaseModel
$baseNormalModelContent = '<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: SaiAdmin Team (修复版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\basic;

use think\Model;

/**
 * 普通模型基类 (不使用软删除)
 * @package plugin\saiadmin\basic
 */
class BaseNormalModel extends Model
{
    // 添加时间
    protected $createTime = "created_at";
    // 更新时间
    protected $updateTime = "updated_at";
    // 只读字段
    protected $readonly = array("created_by", "created_at");

    /**
     * 时间范围搜索
     */
    public function searchCreatedAtAttr($query, $value)
    {
        $query->whereTime("created_at", "between", $value);
    }

    /**
     * 新增前
     */
    public static function onBeforeInsert($model)
    {
        if (function_exists("getCurrentInfo")) {
            $info = getCurrentInfo();
            $info && $model->setAttr("created_by", $info["id"]);
        }
    }

    /**
     * 写入前
     */
    public static function onBeforeWrite($model)
    {
        if (function_exists("getCurrentInfo")) {
            $info = getCurrentInfo();
            $info && $model->setAttr("updated_by", $info["id"]);
        }
    }
}';

file_put_contents('webman/plugin/saiadmin/basic/BaseNormalModelFixed.php', $baseNormalModelContent);
echo "  ✅ 创建了不使用软删除的BaseNormalModelFixed.php\n";

// 方案2: 创建修复版的BaseModel
$baseModelFixedContent = '<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: SaiAdmin Team (修复版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\basic;

use think\Model;

/**
 * 模型基类 (修复版本)
 * @package plugin\saiadmin\basic
 */
class BaseModelFixed extends Model
{
    // 添加时间
    protected $createTime = "created_at";
    // 更新时间  
    protected $updateTime = "updated_at";
    // 只读字段
    protected $readonly = array("created_by", "created_at");

    /**
     * 时间范围搜索
     */
    public function searchCreatedAtAttr($query, $value)
    {
        $query->whereTime("created_at", "between", $value);
    }

    /**
     * 新增前
     */
    public static function onBeforeInsert($model)
    {
        if (function_exists("getCurrentInfo")) {
            $info = getCurrentInfo();
            $info && $model->setAttr("created_by", $info["id"]);
        }
    }

    /**
     * 写入前
     */
    public static function onBeforeWrite($model)
    {
        if (function_exists("getCurrentInfo")) {
            $info = getCurrentInfo();
            $info && $model->setAttr("updated_by", $info["id"]);
        }
    }
}';

file_put_contents('webman/plugin/saiadmin/basic/BaseModelFixed.php', $baseModelFixedContent);
echo "  ✅ 创建了修复版的BaseModelFixed.php\n";

// 方案3: 创建数据库迁移脚本
$migrationSql = '-- 修复delete_time字段缺失问题
-- 为现有表添加软删除相关字段

-- 如果要使用软删除，需要为表添加delete_time字段
-- ALTER TABLE sa_article_category ADD COLUMN delete_time timestamp NULL DEFAULT NULL COMMENT "删除时间";
-- ALTER TABLE sa_article ADD COLUMN delete_time timestamp NULL DEFAULT NULL COMMENT "删除时间";

-- 或者创建标准的时间字段结构
-- ALTER TABLE sa_article_category 
-- ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
-- ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
-- ADD COLUMN deleted_at timestamp NULL DEFAULT NULL COMMENT "删除时间";

-- 检查现有表结构
SHOW COLUMNS FROM sa_article_category;
-- SHOW COLUMNS FROM sa_article;

-- 如果表不存在，创建标准的文章分类表
CREATE TABLE IF NOT EXISTS sa_article_category (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT "主键ID",
    parent_id int(11) NOT NULL DEFAULT 0 COMMENT "父级ID", 
    category_name varchar(50) NOT NULL DEFAULT "" COMMENT "分类名称",
    category_desc varchar(200) DEFAULT "" COMMENT "分类描述",
    image varchar(255) DEFAULT "" COMMENT "分类图片",
    sort int(11) NOT NULL DEFAULT 0 COMMENT "排序",
    status tinyint(1) NOT NULL DEFAULT 1 COMMENT "状态 0禁用 1启用",
    created_by int(11) DEFAULT 0 COMMENT "创建者",
    updated_by int(11) DEFAULT 0 COMMENT "更新者", 
    created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
    PRIMARY KEY (id),
    KEY idx_parent_id (parent_id),
    KEY idx_status (status),
    KEY idx_sort (sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="文章分类表";';

file_put_contents('fix-delete-time-migration.sql', $migrationSql);
echo "  ✅ 创建了数据库迁移脚本 fix-delete-time-migration.sql\n";

echo "\n";

// 4. 提供修复建议
echo "[4/4] 修复建议:\n\n";

echo "🎯 立即修复方案:\n";
echo "1. 修改模型继承关系:\n";
echo "   - 将ArticleCategory模型改为继承BaseNormalModelFixed\n";
echo "   - 或者继承BaseModelFixed (不使用软删除)\n\n";

echo "2. 修复模型文件示例:\n";
echo "   // 原来的代码\n";
echo "   class ArticleCategory extends BaseModel\n";
echo "   \n";
echo "   // 修复后的代码\n";
echo "   class ArticleCategory extends BaseNormalModelFixed\n\n";

echo "🗄️ 数据库修复方案:\n";
echo "1. 如果要保持软删除功能:\n";
echo "   - 执行SQL: ALTER TABLE sa_article_category ADD COLUMN delete_time timestamp NULL;\n";
echo "   - 为所有相关表添加delete_time字段\n\n";

echo "2. 如果不需要软删除功能:\n";
echo "   - 使用BaseNormalModelFixed基类\n";
echo "   - 确保表有created_at和updated_at字段\n\n";

echo "⚠️ 表前缀问题:\n";
echo "1. 检查配置文件中的表前缀设置\n";
echo "2. 确保模型中的table属性正确\n";
echo "3. 错误显示ddwx_但配置是sa_，可能存在配置不一致\n\n";

echo "🔧 快速修复命令:\n";
echo "-- 检查表结构\n";
echo "DESCRIBE sa_article_category;\n\n";
echo "-- 添加缺失字段(如果需要软删除)\n";
echo "ALTER TABLE sa_article_category ADD COLUMN delete_time timestamp NULL DEFAULT NULL;\n\n";
echo "-- 或者添加标准时间字段\n";
echo "ALTER TABLE sa_article_category \n";
echo "ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP,\n";
echo "ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;\n\n";

echo "========================================\n";
echo "🎉 修复方案已生成！\n";
echo "========================================\n\n";

echo "📁 生成的文件:\n";
echo "1. BaseNormalModelFixed.php - 不使用软删除的基类\n";
echo "2. BaseModelFixed.php - 修复版基类\n";
echo "3. fix-delete-time-migration.sql - 数据库迁移脚本\n\n";

echo "💡 建议优先使用BaseNormalModelFixed，避免软删除相关问题\n";
