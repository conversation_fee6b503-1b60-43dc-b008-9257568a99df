# Apache 虚拟主机配置
<VirtualHost *:80>
    ServerName saiadmin.local
    ServerAlias www.saiadmin.local
    DocumentRoot /var/www/html/saiadmin/web

    <Directory /var/www/html/saiadmin/web>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted

        # URL重写
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . index.php
    </Directory>

    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/saiadmin_error.log
    CustomLog ${APACHE_LOG_DIR}/saiadmin_access.log combined

    # 安全配置
    <Directory /var/www/html/saiadmin>
        <Files "*.php">
            Require all denied
        </Files>
    </Directory>

    <Directory /var/www/html/saiadmin/web>
        <Files "*.php">
            Require all granted
        </Files>
    </Directory>
</VirtualHost>