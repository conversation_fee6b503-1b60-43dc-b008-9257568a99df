<?php

return [
    // 默认数据库连接标识
    'default' => 'mysql',

    // 多数据库连接配置
    'connections' => [
        // 主数据库 - SaiAdmin核心数据
        'mysql' => [
            // 数据库类型
            'type' => env('DB_TYPE', 'mysql'),
            // 服务器地址
            'hostname' => env('DB_HOST', '127.0.0.1'),
            // 数据库名
            'database' => env('DB_NAME', 'saiadmin'),
            // 数据库用户名
            'username' => env('DB_USER', 'root'),
            // 数据库密码
            'password' => env('DB_PASSWORD', '5GeNi1v7P7Xcur5W'),
            // 数据库连接端口
            'hostport' => env('DB_PORT', 3306),
            // 数据库连接参数
            'params' => [
                // 连接超时3秒
                \PDO::ATTR_TIMEOUT => 3,
                // 设置字符集
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                // 启用持久连接
                \PDO::ATTR_PERSISTENT => true,
            ],
            // 数据库编码默认采用utf8mb4
            'charset' => 'utf8mb4',
            // 数据库表前缀
            'prefix' => env('DB_PREFIX', ''),
            // 断线重连
            'break_reconnect' => true,
            // 自定义分页类
            'bootstrap' => '',
            // 连接池配置
            'pool' => [
                'max_connections' => 20, // 最大连接数
                'min_connections' => 3,  // 最小连接数
                'wait_timeout' => 5,     // 从连接池获取连接等待超时时间
                'idle_timeout' => 300,   // 连接最大空闲时间，超过该时间会被回收
                'heartbeat_interval' => 30, // 心跳检测间隔，需要小于60秒
            ],
        ],

        // 从数据库 - 读写分离（读库）
        'mysql_read' => [
            'type' => 'mysql',
            'hostname' => env('DB_READ_HOST', '127.0.0.1'),
            'database' => env('DB_READ_NAME', 'saiadmin'),
            'username' => env('DB_READ_USER', 'root'),
            'password' => env('DB_READ_PASSWORD', '5GeNi1v7P7Xcur5W'),
            'hostport' => env('DB_READ_PORT', 3306),
            'params' => [
                \PDO::ATTR_TIMEOUT => 3,
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                \PDO::ATTR_PERSISTENT => true,
            ],
            'charset' => 'utf8mb4',
            'prefix' => env('DB_PREFIX', ''),
            'break_reconnect' => true,
            'bootstrap' => '',
            'pool' => [
                'max_connections' => 15,
                'min_connections' => 2,
                'wait_timeout' => 5,
                'idle_timeout' => 300,
                'heartbeat_interval' => 30,
            ],
        ],

        // 日志数据库 - 专门存储日志数据
        'mysql_log' => [
            'type' => 'mysql',
            'hostname' => env('DB_LOG_HOST', '127.0.0.1'),
            'database' => env('DB_LOG_NAME', 'saiadmin_logs'),
            'username' => env('DB_LOG_USER', 'root'),
            'password' => env('DB_LOG_PASSWORD', '5GeNi1v7P7Xcur5W'),
            'hostport' => env('DB_LOG_PORT', 3306),
            'params' => [
                \PDO::ATTR_TIMEOUT => 3,
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                \PDO::ATTR_PERSISTENT => false, // 日志库不使用持久连接
            ],
            'charset' => 'utf8mb4',
            'prefix' => 'log_',
            'break_reconnect' => true,
            'bootstrap' => '',
            'pool' => [
                'max_connections' => 10,
                'min_connections' => 1,
                'wait_timeout' => 3,
                'idle_timeout' => 180,
                'heartbeat_interval' => 60,
            ],
        ],

        // 缓存数据库 - 使用MySQL作为缓存（可选）
        'mysql_cache' => [
            'type' => 'mysql',
            'hostname' => env('DB_CACHE_HOST', '127.0.0.1'),
            'database' => env('DB_CACHE_NAME', 'saiadmin_cache'),
            'username' => env('DB_CACHE_USER', 'root'),
            'password' => env('DB_CACHE_PASSWORD', '5GeNi1v7P7Xcur5W'),
            'hostport' => env('DB_CACHE_PORT', 3306),
            'params' => [
                \PDO::ATTR_TIMEOUT => 2,
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                \PDO::ATTR_PERSISTENT => false,
            ],
            'charset' => 'utf8mb4',
            'prefix' => 'cache_',
            'break_reconnect' => true,
            'bootstrap' => '',
            'pool' => [
                'max_connections' => 8,
                'min_connections' => 1,
                'wait_timeout' => 2,
                'idle_timeout' => 120,
                'heartbeat_interval' => 30,
            ],
        ],

        // PostgreSQL数据库示例
        'pgsql' => [
            'type' => 'pgsql',
            'hostname' => env('PGSQL_HOST', '127.0.0.1'),
            'database' => env('PGSQL_NAME', 'saiadmin_pg'),
            'username' => env('PGSQL_USER', 'postgres'),
            'password' => env('PGSQL_PASSWORD', ''),
            'hostport' => env('PGSQL_PORT', 5432),
            'params' => [
                \PDO::ATTR_TIMEOUT => 3,
            ],
            'charset' => 'utf8',
            'prefix' => '',
            'break_reconnect' => true,
            'bootstrap' => '',
            'pool' => [
                'max_connections' => 10,
                'min_connections' => 1,
                'wait_timeout' => 5,
                'idle_timeout' => 300,
                'heartbeat_interval' => 30,
            ],
        ],

        // SQLite数据库示例（用于轻量级数据）
        'sqlite' => [
            'type' => 'sqlite',
            'database' => runtime_path() . '/database/saiadmin.db',
            'prefix' => '',
            'break_reconnect' => false,
            'bootstrap' => '',
            // SQLite不支持连接池
        ],
    ],

    // 读写分离配置
    'read_write_separation' => [
        'enable' => env('DB_RW_SEPARATE', false),
        'write' => 'mysql',      // 写库
        'read' => 'mysql_read',  // 读库
    ],

    // 分库分表配置
    'sharding' => [
        'enable' => false,
        'rules' => [
            // 用户表分表规则示例
            'sa_system_user' => [
                'type' => 'mod',
                'field' => 'id',
                'mod' => 4,
                'tables' => [
                    'sa_system_user_0',
                    'sa_system_user_1',
                    'sa_system_user_2',
                    'sa_system_user_3',
                ]
            ]
        ]
    ],
];