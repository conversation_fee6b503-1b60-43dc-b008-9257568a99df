<?php

return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            // 数据库类型
            'type' => env('DB_TYPE', 'mysql'),
            // 服务器地址
            'hostname' => env('DB_HOST', '127.0.0.1'),
            // 数据库名
            'database' => env('DB_NAME', 'saiadmin'),
            // 数据库用户名
            'username' => env('DB_USER', 'root'),
            // 数据库密码
            'password' => env('DB_PASSWORD', '123456'),
            // 数据库连接端口
            'hostport' => env('DB_PORT', 3306),
            // 数据库连接参数
            'params' => [
                // 连接超时3秒
                \PDO::ATTR_TIMEOUT => 3,
            ],
            // 数据库编码默认采用utf8
            'charset' => 'utf8',
            // 数据库表前缀
            'prefix' => env('DB_PREFIX', ''),
            // 断线重连
            'break_reconnect' => true,
            // 自定义分页类
            'bootstrap' =>  '',
            // 优化的连接池配置
            'pool' => [
                'max_connections' => 20,    // 最大连接数（提升并发能力）
                'min_connections' => 3,     // 最小连接数（保持基础连接）
                'wait_timeout' => 5,        // 从连接池获取连接等待超时时间
                'idle_timeout' => 300,      // 连接最大空闲时间（5分钟）
                'heartbeat_interval' => 30, // 心跳检测间隔（30秒）
                'max_lifetime' => 3600,     // 连接最大生存时间（1小时）
                'retry_times' => 3,         // 连接重试次数
            ],
            // 查询优化配置
            'query' => [
                'cache_time' => 300,        // 查询缓存时间（5分钟）
                'slow_log' => true,         // 启用慢查询日志
                'slow_threshold' => 1000,   // 慢查询阈值（1秒）
                'explain_slow' => true,     // 自动分析慢查询
            ],
            // 事务配置
            'transaction' => [
                'isolation' => 'READ_COMMITTED', // 事务隔离级别
                'timeout' => 30,            // 事务超时时间（30秒）
                'retry_times' => 3,         // 死锁重试次数
            ],
        ],
    ],
];