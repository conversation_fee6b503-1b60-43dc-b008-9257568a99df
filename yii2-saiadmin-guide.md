# SaiAdmin Yii 2.0 兼容版本 - 安装和使用指南

## 🎯 项目概述

SaiAdmin 已成功优化为完全兼容 Yii 2.0 框架的结构，保持了原有功能的同时，采用了 Yii 2.0 的标准架构和最佳实践。

## 📁 项目结构

```
yii2-saiadmin/
├── assets/                 # 资源包
│   └── AppAsset.php       # 主应用资源包
├── commands/              # 控制台命令
├── components/            # 应用组件
│   ├── base/             # 基础类
│   │   ├── BaseController.php
│   │   └── BaseModel.php
│   ├── services/         # 业务服务层
│   ├── validators/       # 验证器
│   ├── filters/          # 过滤器
│   └── widgets/          # 小部件
├── config/               # 配置文件
│   ├── web.php          # Web应用配置
│   ├── console.php      # 控制台配置
│   ├── db.php           # 数据库配置
│   └── params.php       # 应用参数
├── controllers/          # 主控制器
├── models/              # 主模型
│   └── User.php         # 用户身份类
├── modules/             # 模块
│   ├── admin/          # 管理模块
│   │   ├── Module.php
│   │   ├── controllers/
│   │   ├── models/
│   │   └── views/
│   └── tool/           # 工具模块
├── runtime/             # 运行时文件
│   ├── cache/
│   ├── logs/
│   └── sessions/
├── views/               # 视图文件
│   ├── layouts/
│   │   └── main.php    # 主布局
│   └── site/
├── web/                 # Web根目录
│   ├── assets/         # 前端资源
│   ├── css/
│   ├── js/
│   ├── images/
│   └── index.php       # Web入口
├── widgets/             # 小部件
│   └── NavWidget.php   # 导航小部件
├── migrations/          # 数据库迁移
├── mail/               # 邮件模板
├── rbac/               # RBAC权限
│   └── init.php        # 权限初始化
├── tests/              # 测试文件
├── composer.json       # Composer配置
└── yii                 # 控制台入口
```

## 🚀 安装步骤

### 1. 环境要求

- PHP >= 7.4
- MySQL >= 5.7
- Composer
- Web服务器 (Apache/Nginx)

### 2. 安装依赖

```bash
cd yii2-saiadmin
composer install
```

### 3. 配置数据库

编辑 `config/db.php`：

```php
return [
    'class' => 'yii\db\Connection',
    'dsn' => 'mysql:host=localhost;dbname=saiadmin',
    'username' => 'root',
    'password' => 'your_password',
    'charset' => 'utf8mb4',
];
```

### 4. 运行数据库迁移

```bash
./yii migrate
```

### 5. 配置Web服务器

#### Apache 配置

```apache
<VirtualHost *:80>
    ServerName saiadmin.local
    DocumentRoot /path/to/yii2-saiadmin/web
    
    <Directory /path/to/yii2-saiadmin/web>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### Nginx 配置

```nginx
server {
    listen 80;
    server_name saiadmin.local;
    root /path/to/yii2-saiadmin/web;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 6. 设置权限

```bash
chmod 755 yii
chmod -R 777 runtime/
chmod -R 777 web/assets/
```

## 🔧 Yii 2.0 特性

### 1. MVC 架构

- **Model**: 继承自 `yii\db\ActiveRecord`
- **View**: 使用 Twig 或 PHP 模板
- **Controller**: 继承自 `app\components\base\BaseController`

### 2. 模块化设计

```php
// 模块配置 (config/web.php)
'modules' => [
    'admin' => [
        'class' => 'app\modules\admin\Module',
    ],
    'tool' => [
        'class' => 'app\modules\tool\Module',
    ],
],
```

### 3. 组件系统

```php
// 服务组件示例
namespace app\components\services;

use yii\base\Component;

class UserService extends Component
{
    public function createUser($userData)
    {
        // 业务逻辑
    }
}
```

### 4. 依赖注入

```php
// 在控制器中使用服务
public function actionCreate()
{
    $userService = Yii::$app->get('userService');
    // 或者直接实例化
    $userService = new UserService();
}
```

### 5. 事件系统

```php
// 模型事件
public function afterSave($insert, $changedAttributes)
{
    parent::afterSave($insert, $changedAttributes);
    
    if ($insert) {
        // 新增后的处理
    }
}
```

### 6. 缓存支持

```php
// 使用缓存
$cache = Yii::$app->cache;
$data = $cache->get('key');
if ($data === false) {
    $data = $this->calculateData();
    $cache->set('key', $data, 3600);
}
```

### 7. 日志系统

```php
// 记录日志
Yii::info('用户登录', 'user');
Yii::error('数据库连接失败', 'db');
Yii::warning('缓存过期', 'cache');
```

### 8. 表单验证

```php
// 模型验证规则
public function rules()
{
    return [
        [['username', 'email'], 'required'],
        ['email', 'email'],
        ['username', 'unique'],
        ['password', 'string', 'min' => 6],
    ];
}
```

### 9. RBAC 权限控制

```php
// 检查权限
if (Yii::$app->user->can('createPost')) {
    // 用户有创建文章的权限
}
```

### 10. 国际化支持

```php
// 多语言
echo Yii::t('app', 'Hello World');
```

## 📖 API 文档

### 控制器基类

```php
namespace app\components\base;

class BaseController extends \yii\web\Controller
{
    // 返回成功响应
    protected function success($data = [], $message = '操作成功')
    
    // 返回失败响应  
    protected function fail($message = '操作失败', $code = 400)
    
    // 获取分页参数
    protected function getPaginationParams()
    
    // 获取排序参数
    protected function getSortParams()
}
```

### 模型基类

```php
namespace app\components\base;

class BaseModel extends \yii\db\ActiveRecord
{
    // 软删除
    public function softDelete()
    
    // 批量插入
    public static function batchInsert($data)
    
    // 搜索范围
    public function scopeStatus($query, $status)
    public function scopeTimeRange($query, $timeRange)
}
```

## 🔍 开发工具

### 1. Gii 代码生成器

访问: `http://localhost/gii/`

- 模型生成器
- CRUD 生成器
- 控制器生成器
- 表单生成器
- 模块生成器

### 2. Debug 工具栏

访问: `http://localhost/debug/`

- 性能分析
- 数据库查询
- 日志查看
- 配置信息

### 3. 控制台命令

```bash
# 查看所有命令
./yii help

# 数据库迁移
./yii migrate

# 缓存清理
./yii cache/flush-all

# 创建控制器
./yii gii/controller

# 创建模型
./yii gii/model
```

## 🎯 最佳实践

### 1. 目录组织

- 按功能模块组织代码
- 使用命名空间避免冲突
- 遵循 PSR-4 自动加载标准

### 2. 代码规范

- 遵循 Yii 2.0 编码规范
- 使用类型提示
- 编写单元测试

### 3. 性能优化

- 启用 OpCache
- 使用数据库索引
- 合理使用缓存
- 优化数据库查询

### 4. 安全考虑

- 输入验证和过滤
- SQL 注入防护
- XSS 攻击防护
- CSRF 令牌验证

## 🔗 相关链接

- [Yii 2.0 官方文档](https://www.yiiframework.com/doc/api/2.0)
- [Yii 2.0 指南](https://www.yiiframework.com/doc/guide/2.0/zh-cn)
- [SaiAdmin 官网](https://saiadmin.com)

## 📞 技术支持

如有问题，请联系：
- 邮箱: <EMAIL>
- QQ群: 123456789
- GitHub: https://github.com/saiadmin/yii2-saiadmin

---

🎉 **恭喜！SaiAdmin 已成功优化为完全兼容 Yii 2.0 的现代化框架！**
