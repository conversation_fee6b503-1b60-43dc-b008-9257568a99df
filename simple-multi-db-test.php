<?php
/**
 * SaiAdmin 简化多数据源测试
 * 兼容PHP 7.0+版本
 */

echo "🗄️ SaiAdmin 多数据源功能测试\n";
echo "========================================\n\n";

// 1. 测试基本数据库连接
echo "[1/5] 测试基本数据库连接...\n";

try {
    // 测试主数据库连接
    $mainPdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
        'root',
        '5GeNi1v7P7Xcur5W',
        array(PDO::ATTR_TIMEOUT => 3)
    );
    echo "  ✅ 主数据库连接成功\n";

    // 测试查询
    $stmt = $mainPdo->query('SELECT COUNT(*) as count FROM sa_system_user');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  📊 用户总数: " . $result['count'] . "\n";

} catch (Exception $e) {
    echo "  ❌ 主数据库连接失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. 测试多数据源配置
echo "[2/5] 检查多数据源配置...\n";

$configFile = 'multi-db-config-simple.php';
if (file_exists($configFile)) {
    echo "  ✅ 多数据源配置文件存在\n";

    $config = include $configFile;
    $connections = isset($config['connections']) ? $config['connections'] : array();

    echo "  📋 已配置的数据源:\n";
    foreach ($connections as $name => $conn) {
        $type = isset($conn['type']) ? $conn['type'] : 'unknown';
        $host = isset($conn['hostname']) ? $conn['hostname'] : 'unknown';
        $database = isset($conn['database']) ? $conn['database'] : 'unknown';
        echo "    - {$name}: {$type}://{$host}/{$database}\n";
    }
} else {
    echo "  ❌ 多数据源配置文件不存在\n";
}

echo "\n";

// 3. 测试环境变量配置
echo "[3/5] 检查环境变量配置...\n";

$envFile = 'webman/.env';
if (file_exists($envFile)) {
    echo "  ✅ 环境配置文件存在\n";

    $envContent = file_get_contents($envFile);
    $dbConfigs = array(
        'DB_HOST' => '主数据库地址',
        'DB_READ_HOST' => '读库地址',
        'DB_LOG_HOST' => '日志库地址',
        'DB_CACHE_HOST' => '缓存库地址'
    );

    foreach ($dbConfigs as $key => $desc) {
        if (strpos($envContent, $key) !== false) {
            echo "    ✅ {$desc} 已配置\n";
        } else {
            echo "    ⚠️ {$desc} 未配置\n";
        }
    }
} else {
    echo "  ❌ 环境配置文件不存在\n";
}

echo "\n";

// 4. 测试数据库表创建
echo "[4/5] 测试数据库表创建...\n";

try {
    // 创建日志表的SQL
    $logTableSql = "
        CREATE TABLE IF NOT EXISTS test_log_operation (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL DEFAULT 0,
            username VARCHAR(50) NOT NULL DEFAULT '',
            method VARCHAR(10) NOT NULL DEFAULT '',
            router VARCHAR(500) NOT NULL DEFAULT '',
            ip VARCHAR(45) NOT NULL DEFAULT '',
            request_data TEXT,
            response_code VARCHAR(5) NOT NULL DEFAULT '',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_username (username),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试操作日志表'
    ";

    $mainPdo->exec($logTableSql);
    echo "  ✅ 测试日志表创建成功\n";

    // 插入测试数据
    $insertSql = "INSERT INTO test_log_operation (user_id, username, method, router, ip, request_data, response_code) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $mainPdo->prepare($insertSql);
    $result = $stmt->execute(array(
        1,
        'admin',
        'POST',
        '/api/test',
        '127.0.0.1',
        json_encode(array('test' => 'data')),
        '200'
    ));

    if ($result) {
        echo "  ✅ 测试数据插入成功\n";
    } else {
        echo "  ❌ 测试数据插入失败\n";
    }

    // 查询测试数据
    $stmt = $mainPdo->query('SELECT COUNT(*) as count FROM test_log_operation');
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  📊 测试日志记录数: " . $result['count'] . "\n";

} catch (Exception $e) {
    echo "  ❌ 数据库表操作失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. 生成使用示例
echo "[5/5] 生成使用示例...\n";

$examples = array(
    '基本连接使用' => array(
        'use think\facade\Db;',
        '',
        '// 使用主数据库',
        '$users = Db::connect("mysql")->table("sa_system_user")->select();',
        '',
        '// 使用读库（如果配置了读写分离）',
        '$users = Db::connect("mysql_read")->table("sa_system_user")->select();',
        '',
        '// 使用日志数据库',
        '$logs = Db::connect("mysql_log")->table("log_operation")->select();'
    ),
    '读写分离示例' => array(
        '// 查询操作（自动使用读库）',
        '$users = Db::table("sa_system_user")->where("status", 1)->select();',
        '',
        '// 写入操作（自动使用写库）',
        '$result = Db::table("sa_system_user")->insert($userData);',
        '',
        '// 事务操作（强制使用主库）',
        'Db::transaction(function() {',
        '    Db::table("sa_system_user")->insert($userData);',
        '    Db::table("sa_system_role")->insert($roleData);',
        '});'
    ),
    '环境配置示例' => array(
        '# 主数据库配置',
        'DB_HOST = 127.0.0.1',
        'DB_NAME = saiadmin',
        'DB_USER = root',
        'DB_PASSWORD = your_password',
        '',
        '# 读写分离配置',
        'DB_RW_SEPARATE = true',
        'DB_READ_HOST = *************',
        'DB_READ_NAME = saiadmin_read',
        '',
        '# 日志数据库配置',
        'DB_LOG_HOST = *************',
        'DB_LOG_NAME = saiadmin_logs'
    )
);

foreach ($examples as $title => $code) {
    echo "  📝 {$title}:\n";
    foreach ($code as $line) {
        echo "    {$line}\n";
    }
    echo "\n";
}

echo "========================================\n";
echo "🎉 多数据源功能测试完成！\n";
echo "========================================\n\n";

echo "📋 配置步骤总结:\n";
echo "1. 配置环境变量 (.env 文件)\n";
echo "2. 配置数据库连接 (think-orm.php 文件)\n";
echo "3. 创建多数据源管理类\n";
echo "4. 在代码中使用多数据源\n\n";

echo "💡 最佳实践:\n";
echo "- 主库用于写操作和事务\n";
echo "- 读库用于查询操作\n";
echo "- 日志库独立存储日志数据\n";
echo "- 缓存库存储临时数据\n";
echo "- 定期备份重要数据\n\n";

echo "🔧 故障排除:\n";
echo "- 检查数据库服务状态\n";
echo "- 验证连接参数配置\n";
echo "- 确认用户权限设置\n";
echo "- 查看错误日志信息\n\n";

// 清理测试数据
try {
    $mainPdo->exec('DROP TABLE IF EXISTS test_log_operation');
    echo "🧹 测试数据已清理\n";
} catch (Exception $e) {
    echo "⚠️ 测试数据清理失败: " . $e->getMessage() . "\n";
}

echo "\n🌐 访问地址:\n";
echo "  前端: http://localhost:8889/\n";
echo "  后端: http://localhost:8787/\n";
echo "  管理员: admin / admin123\n";
