<?php
/**
 * 数据库演示视图
 */
use yii\helpers\Html;

$this->title = "数据库演示";
?>

<div class="demo-database">
    <h1><?= Html::encode($this->title) ?></h1>
    
    <div class="row">
        <div class="col-md-6">
            <h4>🗄️ 连接信息</h4>
            <table class="table table-bordered">
                <?php foreach ($connection as $key => $value): ?>
                    <tr>
                        <td><strong><?= Html::encode($key) ?></strong></td>
                        <td><?= Html::encode($value) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="col-md-6">
            <h4>📋 数据库表</h4>
            <?php if (!empty($tables)): ?>
                <ul class="list-group">
                    <?php foreach ($tables as $table): ?>
                        <li class="list-group-item"><?= Html::encode(array_values($table)[0]) ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p class="text-muted">暂无数据表或连接失败</p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="mt-3">
        <?= Html::a("返回", ["demo/index"], ["class" => "btn btn-secondary"]) ?>
    </div>
</div>