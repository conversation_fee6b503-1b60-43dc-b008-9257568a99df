<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 高级日志管理系统
// +----------------------------------------------------------------------
// | Author: AI Assistant (深度优化版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\logger;

use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Handler\RedisHandler;
use Monolog\Formatter\JsonFormatter;
use Monolog\Formatter\LineFormatter;
use Monolog\Processor\WebProcessor;
use Monolog\Processor\MemoryUsageProcessor;
use Monolog\Processor\ProcessIdProcessor;
use plugin\saiadmin\app\cache\RedisManager;

/**
 * 高级日志管理系统
 */
class AdvancedLogger
{
    /**
     * 日志实例
     */
    private static $loggers = [];
    
    /**
     * 配置信息
     */
    private static $config = [
        'default' => [
            'level' => Logger::INFO,
            'path' => '',
            'max_files' => 30,
            'max_size' => 100 * 1024 * 1024, // 100MB
            'format' => 'line',
            'processors' => ['web', 'memory', 'process'],
        ],
        'performance' => [
            'level' => Logger::WARNING,
            'path' => '',
            'format' => 'json',
            'redis_key' => 'logs:performance',
        ],
        'security' => [
            'level' => Logger::ERROR,
            'path' => '',
            'format' => 'json',
            'redis_key' => 'logs:security',
        ],
        'api' => [
            'level' => Logger::INFO,
            'path' => '',
            'format' => 'json',
            'redis_key' => 'logs:api',
        ],
        'sql' => [
            'level' => Logger::DEBUG,
            'path' => '',
            'format' => 'json',
            'redis_key' => 'logs:sql',
        ]
    ];
    
    /**
     * 日志统计
     */
    private static $stats = [
        'total_logs' => 0,
        'error_count' => 0,
        'warning_count' => 0,
        'info_count' => 0,
        'debug_count' => 0,
        'last_error' => null,
    ];
    
    /**
     * 初始化日志系统
     */
    public static function init(array $config = []): void
    {
        self::$config = array_merge(self::$config, $config);
        
        // 设置默认路径
        foreach (self::$config as $name => &$logConfig) {
            if (empty($logConfig['path'])) {
                $logConfig['path'] = runtime_path() . "/logs/{$name}.log";
            }
        }
        
        // 创建默认日志器
        self::getLogger('default');
        
        echo "✅ 高级日志系统初始化完成\n";
    }
    
    /**
     * 获取日志器
     */
    public static function getLogger(string $name = 'default'): Logger
    {
        if (!isset(self::$loggers[$name])) {
            self::$loggers[$name] = self::createLogger($name);
        }
        
        return self::$loggers[$name];
    }
    
    /**
     * 创建日志器
     */
    private static function createLogger(string $name): Logger
    {
        $config = self::$config[$name] ?? self::$config['default'];
        $logger = new Logger($name);
        
        // 添加处理器
        self::addHandlers($logger, $name, $config);
        
        // 添加处理器
        self::addProcessors($logger, $config);
        
        return $logger;
    }
    
    /**
     * 添加处理器
     */
    private static function addHandlers(Logger $logger, string $name, array $config): void
    {
        // 文件处理器
        $fileHandler = new RotatingFileHandler(
            $config['path'],
            $config['max_files'] ?? 30,
            $config['level'] ?? Logger::INFO
        );
        
        // 设置格式化器
        if (($config['format'] ?? 'line') === 'json') {
            $fileHandler->setFormatter(new JsonFormatter());
        } else {
            $formatter = new LineFormatter(
                "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
                'Y-m-d H:i:s'
            );
            $fileHandler->setFormatter($formatter);
        }
        
        $logger->pushHandler($fileHandler);
        
        // Redis处理器（用于实时日志）
        if (isset($config['redis_key'])) {
            try {
                $redis = RedisManager::getConnection();
                $redisHandler = new RedisHandler($redis, $config['redis_key'], $config['level'] ?? Logger::INFO);
                $redisHandler->setFormatter(new JsonFormatter());
                $logger->pushHandler($redisHandler);
            } catch (\Exception $e) {
                // Redis不可用时忽略
            }
        }
        
        // 控制台处理器（开发环境）
        if (env('APP_DEBUG', false)) {
            $consoleHandler = new StreamHandler('php://stdout', Logger::DEBUG);
            $consoleHandler->setFormatter(new LineFormatter(
                "\033[32m[%datetime%]\033[0m \033[36m%channel%\033[0m.\033[33m%level_name%\033[0m: %message%\n",
                'H:i:s'
            ));
            $logger->pushHandler($consoleHandler);
        }
    }
    
    /**
     * 添加处理器
     */
    private static function addProcessors(Logger $logger, array $config): void
    {
        $processors = $config['processors'] ?? [];
        
        foreach ($processors as $processor) {
            switch ($processor) {
                case 'web':
                    $logger->pushProcessor(new WebProcessor());
                    break;
                case 'memory':
                    $logger->pushProcessor(new MemoryUsageProcessor());
                    break;
                case 'process':
                    $logger->pushProcessor(new ProcessIdProcessor());
                    break;
            }
        }
        
        // 添加自定义处理器
        $logger->pushProcessor(function ($record) {
            $record['extra']['request_id'] = request()->requestId ?? uniqid();
            $record['extra']['user_id'] = getCurrentInfo()['id'] ?? 0;
            return $record;
        });
    }
    
    /**
     * 记录性能日志
     */
    public static function performance(string $message, array $context = []): void
    {
        $logger = self::getLogger('performance');
        $logger->warning($message, $context);
        self::updateStats('warning');
    }
    
    /**
     * 记录安全日志
     */
    public static function security(string $message, array $context = []): void
    {
        $logger = self::getLogger('security');
        $logger->error($message, $context);
        self::updateStats('error');
        self::$stats['last_error'] = [
            'message' => $message,
            'context' => $context,
            'timestamp' => time()
        ];
    }
    
    /**
     * 记录API日志
     */
    public static function api(string $message, array $context = []): void
    {
        $logger = self::getLogger('api');
        $logger->info($message, $context);
        self::updateStats('info');
    }
    
    /**
     * 记录SQL日志
     */
    public static function sql(string $sql, array $bindings = [], float $time = 0): void
    {
        $logger = self::getLogger('sql');
        
        $context = [
            'sql' => $sql,
            'bindings' => $bindings,
            'time' => $time,
            'slow' => $time > 1000 // 超过1秒为慢查询
        ];
        
        if ($time > 1000) {
            $logger->warning('慢查询检测', $context);
            self::updateStats('warning');
        } else {
            $logger->debug('SQL查询', $context);
            self::updateStats('debug');
        }
    }
    
    /**
     * 记录错误日志
     */
    public static function error(string $message, array $context = [], \Throwable $exception = null): void
    {
        $logger = self::getLogger('default');
        
        if ($exception) {
            $context['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }
        
        $logger->error($message, $context);
        self::updateStats('error');
        self::$stats['last_error'] = [
            'message' => $message,
            'context' => $context,
            'timestamp' => time()
        ];
    }
    
    /**
     * 记录警告日志
     */
    public static function warning(string $message, array $context = []): void
    {
        $logger = self::getLogger('default');
        $logger->warning($message, $context);
        self::updateStats('warning');
    }
    
    /**
     * 记录信息日志
     */
    public static function info(string $message, array $context = []): void
    {
        $logger = self::getLogger('default');
        $logger->info($message, $context);
        self::updateStats('info');
    }
    
    /**
     * 记录调试日志
     */
    public static function debug(string $message, array $context = []): void
    {
        $logger = self::getLogger('default');
        $logger->debug($message, $context);
        self::updateStats('debug');
    }
    
    /**
     * 更新统计信息
     */
    private static function updateStats(string $level): void
    {
        self::$stats['total_logs']++;
        self::$stats[$level . '_count']++;
        
        // 存储到Redis
        try {
            $redis = RedisManager::getConnection();
            $statsKey = 'logs:stats:' . date('Y-m-d');
            $redis->hincrby($statsKey, 'total_logs', 1);
            $redis->hincrby($statsKey, $level . '_count', 1);
            $redis->expire($statsKey, 86400 * 30); // 30天过期
        } catch (\Exception $e) {
            // Redis不可用时忽略
        }
    }
    
    /**
     * 获取日志统计
     */
    public static function getStats(string $date = null): array
    {
        if ($date) {
            try {
                $redis = RedisManager::getConnection();
                $statsKey = 'logs:stats:' . $date;
                return $redis->hgetall($statsKey) ?: [];
            } catch (\Exception $e) {
                return [];
            }
        }
        
        return self::$stats;
    }
    
    /**
     * 获取最近的错误日志
     */
    public static function getRecentErrors(int $limit = 10): array
    {
        try {
            $redis = RedisManager::getConnection();
            $logs = $redis->lrange('logs:security', 0, $limit - 1);
            
            return array_map(function($log) {
                return json_decode($log, true);
            }, $logs);
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 清理过期日志
     */
    public static function cleanup(): void
    {
        try {
            $redis = RedisManager::getConnection();
            
            // 清理过期的Redis日志
            $logKeys = ['logs:performance', 'logs:security', 'logs:api', 'logs:sql'];
            
            foreach ($logKeys as $key) {
                $length = $redis->llen($key);
                if ($length > 10000) { // 保留最新的10000条
                    $redis->ltrim($key, 0, 9999);
                }
            }
            
            // 清理过期的统计数据
            $pattern = 'logs:stats:*';
            $keys = $redis->keys($pattern);
            $expireDate = date('Y-m-d', strtotime('-30 days'));
            
            foreach ($keys as $key) {
                if (strpos($key, $expireDate) !== false) {
                    $redis->del($key);
                }
            }
            
        } catch (\Exception $e) {
            error_log("日志清理失败: " . $e->getMessage());
        }
    }
    
    /**
     * 导出日志
     */
    public static function exportLogs(string $type, string $date, string $format = 'json'): string
    {
        try {
            $redis = RedisManager::getConnection();
            $key = "logs:{$type}";
            $logs = $redis->lrange($key, 0, -1);
            
            $filteredLogs = array_filter($logs, function($log) use ($date) {
                $logData = json_decode($log, true);
                return isset($logData['datetime']) && 
                       strpos($logData['datetime'], $date) === 0;
            });
            
            if ($format === 'csv') {
                return self::convertToCsv($filteredLogs);
            }
            
            return json_encode($filteredLogs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (\Exception $e) {
            return json_encode(['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 转换为CSV格式
     */
    private static function convertToCsv(array $logs): string
    {
        if (empty($logs)) {
            return '';
        }
        
        $csv = "时间,级别,消息,上下文\n";
        
        foreach ($logs as $log) {
            $logData = json_decode($log, true);
            $csv .= sprintf(
                "%s,%s,%s,%s\n",
                $logData['datetime'] ?? '',
                $logData['level_name'] ?? '',
                str_replace(['"', "\n", "\r"], ['""', ' ', ' '], $logData['message'] ?? ''),
                str_replace(['"', "\n", "\r"], ['""', ' ', ' '], json_encode($logData['context'] ?? []))
            );
        }
        
        return $csv;
    }
}
