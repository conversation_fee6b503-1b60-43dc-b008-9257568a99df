import "./chunk-76EIPOUJ.js";
import {
  Fragment,
  computed,
  createBlock,
  createCommentVNode,
  createVNode,
  defineComponent,
  onUnmounted,
  openBlock,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  toDisplayString,
  vModelText,
  withDirectives,
  withModifiers
} from "./chunk-K5PJWWWA.js";
import "./chunk-LK32TJAX.js";

// node_modules/vue-color-kit/dist/vue-color-kit.esm-bundler.js
function setColorValue(color) {
  let rgba = { r: 0, g: 0, b: 0, a: 1 };
  if (/#/.test(color)) {
    rgba = hex2rgb(color);
  } else if (/rgb/.test(color)) {
    rgba = rgb2rgba(color);
  } else if (typeof color === "string") {
    rgba = rgb2rgba(`rgba(${color})`);
  } else if (Object.prototype.toString.call(color) === "[object Object]") {
    rgba = color;
  }
  const { r, g, b, a } = rgba;
  const { h, s, v } = rgb2hsv(rgba);
  return { r, g, b, a: a === void 0 ? 1 : a, h, s, v };
}
function createAlphaSquare(size) {
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  const doubleSize = size * 2;
  canvas.width = doubleSize;
  canvas.height = doubleSize;
  ctx.fillStyle = "#ffffff";
  ctx.fillRect(0, 0, doubleSize, doubleSize);
  ctx.fillStyle = "#ccd5db";
  ctx.fillRect(0, 0, size, size);
  ctx.fillRect(size, size, size, size);
  return canvas;
}
function createLinearGradient(direction, ctx, width, height, color1, color2) {
  const isL = direction === "l";
  const gradient = ctx.createLinearGradient(0, 0, isL ? width : 0, isL ? 0 : height);
  gradient.addColorStop(0.01, color1);
  gradient.addColorStop(0.99, color2);
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);
}
function rgb2hex({ r, g, b }, toUpper) {
  const change = (val) => ("0" + Number(val).toString(16)).slice(-2);
  const color = `#${change(r)}${change(g)}${change(b)}`;
  return toUpper ? color.toUpperCase() : color;
}
function hex2rgb(hex) {
  hex = hex.slice(1);
  const change = (val) => parseInt(val, 16) || 0;
  return {
    r: change(hex.slice(0, 2)),
    g: change(hex.slice(2, 4)),
    b: change(hex.slice(4, 6))
  };
}
function rgb2rgba(rgba) {
  if (typeof rgba === "string") {
    rgba = (/rgba?\((.*?)\)/.exec(rgba) || ["", "0,0,0,1"])[1].split(",");
    return {
      r: Number(rgba[0]) || 0,
      g: Number(rgba[1]) || 0,
      b: Number(rgba[2]) || 0,
      a: Number(rgba[3] ? rgba[3] : 1)
    };
  } else {
    return rgba;
  }
}
function rgb2hsv({ r, g, b }) {
  r = r / 255;
  g = g / 255;
  b = b / 255;
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const delta = max - min;
  let h = 0;
  if (max === min) {
    h = 0;
  } else if (max === r) {
    if (g >= b) {
      h = 60 * (g - b) / delta;
    } else {
      h = 60 * (g - b) / delta + 360;
    }
  } else if (max === g) {
    h = 60 * (b - r) / delta + 120;
  } else if (max === b) {
    h = 60 * (r - g) / delta + 240;
  }
  h = Math.floor(h);
  let s = parseFloat((max === 0 ? 0 : 1 - min / max).toFixed(2));
  let v = parseFloat(max.toFixed(2));
  return { h, s, v };
}
var script = defineComponent({
  props: {
    color: {
      type: String,
      default: "#000000"
    },
    hsv: {
      type: Object,
      default: null
    },
    size: {
      type: Number,
      default: 152
    }
  },
  emits: ["selectSaturation"],
  data() {
    return {
      slideSaturationStyle: {}
    };
  },
  // Can’t monitor, otherwise the color will change when you change yourself
  // watch: {
  //     color() {
  //         this.renderColor()
  //     }
  // },
  mounted() {
    this.renderColor();
    this.renderSlide();
  },
  methods: {
    renderColor() {
      const canvas = this.$refs.canvasSaturation;
      const size = this.size;
      const ctx = canvas.getContext("2d");
      canvas.width = size;
      canvas.height = size;
      ctx.fillStyle = this.color;
      ctx.fillRect(0, 0, size, size);
      createLinearGradient("l", ctx, size, size, "#FFFFFF", "rgba(255,255,255,0)");
      createLinearGradient("p", ctx, size, size, "rgba(0,0,0,0)", "#000000");
    },
    renderSlide() {
      this.slideSaturationStyle = {
        left: this.hsv.s * this.size - 5 + "px",
        top: (1 - this.hsv.v) * this.size - 5 + "px"
      };
    },
    selectSaturation(e) {
      const { top: saturationTop, left: saturationLeft } = this.$el.getBoundingClientRect();
      const ctx = e.target.getContext("2d");
      const mousemove = (e2) => {
        let x = e2.clientX - saturationLeft;
        let y = e2.clientY - saturationTop;
        if (x < 0) {
          x = 0;
        }
        if (y < 0) {
          y = 0;
        }
        if (x > this.size) {
          x = this.size;
        }
        if (y > this.size) {
          y = this.size;
        }
        this.slideSaturationStyle = {
          left: x - 5 + "px",
          top: y - 5 + "px"
        };
        const imgData = ctx.getImageData(Math.min(x, this.size - 1), Math.min(y, this.size - 1), 1, 1);
        const [r, g, b] = imgData.data;
        this.$emit("selectSaturation", { r, g, b });
      };
      mousemove(e);
      const mouseup = () => {
        document.removeEventListener("mousemove", mousemove);
        document.removeEventListener("mouseup", mouseup);
      };
      document.addEventListener("mousemove", mousemove);
      document.addEventListener("mouseup", mouseup);
    }
  }
});
var _hoisted_1 = { ref: "canvasSaturation" };
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock(
    "div",
    {
      class: "saturation",
      onMousedown: _cache[1] || (_cache[1] = withModifiers((...args) => _ctx.selectSaturation && _ctx.selectSaturation(...args), ["prevent", "stop"]))
    },
    [
      createVNode(
        "canvas",
        _hoisted_1,
        null,
        512
        /* NEED_PATCH */
      ),
      createVNode(
        "div",
        {
          style: _ctx.slideSaturationStyle,
          class: "slide"
        },
        null,
        4
        /* STYLE */
      )
    ],
    32
    /* HYDRATE_EVENTS */
  );
}
script.render = render;
script.__file = "src/color/Saturation.vue";
var script$1 = defineComponent({
  props: {
    hsv: {
      type: Object,
      default: null
    },
    width: {
      type: Number,
      default: 15
    },
    height: {
      type: Number,
      default: 152
    }
  },
  emits: ["selectHue"],
  data() {
    return {
      slideHueStyle: {}
    };
  },
  mounted() {
    this.renderColor();
    this.renderSlide();
  },
  methods: {
    renderColor() {
      const canvas = this.$refs.canvasHue;
      const width = this.width;
      const height = this.height;
      const ctx = canvas.getContext("2d");
      canvas.width = width;
      canvas.height = height;
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, "#FF0000");
      gradient.addColorStop(0.17 * 1, "#FF00FF");
      gradient.addColorStop(0.17 * 2, "#0000FF");
      gradient.addColorStop(0.17 * 3, "#00FFFF");
      gradient.addColorStop(0.17 * 4, "#00FF00");
      gradient.addColorStop(0.17 * 5, "#FFFF00");
      gradient.addColorStop(1, "#FF0000");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
    },
    renderSlide() {
      this.slideHueStyle = {
        top: (1 - this.hsv.h / 360) * this.height - 2 + "px"
      };
    },
    selectHue(e) {
      const { top: hueTop } = this.$el.getBoundingClientRect();
      const ctx = e.target.getContext("2d");
      const mousemove = (e2) => {
        let y = e2.clientY - hueTop;
        if (y < 0) {
          y = 0;
        }
        if (y > this.height) {
          y = this.height;
        }
        this.slideHueStyle = {
          top: y - 2 + "px"
        };
        const imgData = ctx.getImageData(0, Math.min(y, this.height - 1), 1, 1);
        const [r, g, b] = imgData.data;
        this.$emit("selectHue", { r, g, b });
      };
      mousemove(e);
      const mouseup = () => {
        document.removeEventListener("mousemove", mousemove);
        document.removeEventListener("mouseup", mouseup);
      };
      document.addEventListener("mousemove", mousemove);
      document.addEventListener("mouseup", mouseup);
    }
  }
});
var _hoisted_1$1 = { ref: "canvasHue" };
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock(
    "div",
    {
      class: "hue",
      onMousedown: _cache[1] || (_cache[1] = withModifiers((...args) => _ctx.selectHue && _ctx.selectHue(...args), ["prevent", "stop"]))
    },
    [
      createVNode(
        "canvas",
        _hoisted_1$1,
        null,
        512
        /* NEED_PATCH */
      ),
      createVNode(
        "div",
        {
          style: _ctx.slideHueStyle,
          class: "slide"
        },
        null,
        4
        /* STYLE */
      )
    ],
    32
    /* HYDRATE_EVENTS */
  );
}
script$1.render = render$1;
script$1.__file = "src/color/Hue.vue";
var script$2 = defineComponent({
  props: {
    color: {
      type: String,
      default: "#000000"
    },
    rgba: {
      type: Object,
      default: null
    },
    width: {
      type: Number,
      default: 15
    },
    height: {
      type: Number,
      default: 152
    }
  },
  emits: ["selectAlpha"],
  data() {
    return {
      slideAlphaStyle: {},
      alphaSize: 5
    };
  },
  watch: {
    color() {
      this.renderColor();
    },
    "rgba.a"() {
      this.renderSlide();
    }
  },
  mounted() {
    this.renderColor();
    this.renderSlide();
  },
  methods: {
    renderColor() {
      const canvas = this.$refs.canvasAlpha;
      const width = this.width;
      const height = this.height;
      const size = this.alphaSize;
      const canvasSquare = createAlphaSquare(size);
      const ctx = canvas.getContext("2d");
      canvas.width = width;
      canvas.height = height;
      ctx.fillStyle = ctx.createPattern(canvasSquare, "repeat");
      ctx.fillRect(0, 0, width, height);
      createLinearGradient("p", ctx, width, height, "rgba(255,255,255,0)", this.color);
    },
    renderSlide() {
      this.slideAlphaStyle = {
        top: this.rgba.a * this.height - 2 + "px"
      };
    },
    selectAlpha(e) {
      const { top: hueTop } = this.$el.getBoundingClientRect();
      const mousemove = (e2) => {
        let y = e2.clientY - hueTop;
        if (y < 0) {
          y = 0;
        }
        if (y > this.height) {
          y = this.height;
        }
        let a = parseFloat((y / this.height).toFixed(2));
        this.$emit("selectAlpha", a);
      };
      mousemove(e);
      const mouseup = () => {
        document.removeEventListener("mousemove", mousemove);
        document.removeEventListener("mouseup", mouseup);
      };
      document.addEventListener("mousemove", mousemove);
      document.addEventListener("mouseup", mouseup);
    }
  }
});
var _hoisted_1$2 = { ref: "canvasAlpha" };
function render$2(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock(
    "div",
    {
      class: "color-alpha",
      onMousedown: _cache[1] || (_cache[1] = withModifiers((...args) => _ctx.selectAlpha && _ctx.selectAlpha(...args), ["prevent", "stop"]))
    },
    [
      createVNode(
        "canvas",
        _hoisted_1$2,
        null,
        512
        /* NEED_PATCH */
      ),
      createVNode(
        "div",
        {
          style: _ctx.slideAlphaStyle,
          class: "slide"
        },
        null,
        4
        /* STYLE */
      )
    ],
    32
    /* HYDRATE_EVENTS */
  );
}
script$2.render = render$2;
script$2.__file = "src/color/Alpha.vue";
var script$3 = defineComponent({
  props: {
    color: {
      type: String,
      default: "#000000"
    },
    width: {
      type: Number,
      default: 100
    },
    height: {
      type: Number,
      default: 30
    }
  },
  data() {
    return {
      alphaSize: 5
    };
  },
  watch: {
    color() {
      this.renderColor();
    }
  },
  mounted() {
    this.renderColor();
  },
  methods: {
    renderColor() {
      const canvas = this.$el;
      const width = this.width;
      const height = this.height;
      const size = this.alphaSize;
      const canvasSquare = createAlphaSquare(size);
      const ctx = canvas.getContext("2d");
      canvas.width = width;
      canvas.height = height;
      ctx.fillStyle = ctx.createPattern(canvasSquare, "repeat");
      ctx.fillRect(0, 0, width, height);
      ctx.fillStyle = this.color;
      ctx.fillRect(0, 0, width, height);
    }
  }
});
function render$3(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock("canvas");
}
script$3.render = render$3;
script$3.__file = "src/color/Preview.vue";
var script$4 = defineComponent({
  props: {
    suckerCanvas: {
      type: Object,
      default: null
    },
    suckerArea: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isOpenSucker: false,
      suckerPreview: null,
      isSucking: false
    };
  },
  watch: {
    suckerCanvas(newVal) {
      this.isSucking = false;
      this.suckColor(newVal);
    }
  },
  methods: {
    openSucker() {
      if (!this.isOpenSucker) {
        this.isOpenSucker = true;
        this.isSucking = true;
        this.$emit("openSucker", true);
        document.addEventListener("keydown", this.keydownHandler);
      } else {
        this.keydownHandler({ keyCode: 27 });
      }
    },
    keydownHandler(e) {
      if (e.keyCode === 27) {
        this.isOpenSucker = false;
        this.isSucking = false;
        this.$emit("openSucker", false);
        document.removeEventListener("keydown", this.keydownHandler);
        document.removeEventListener("mousemove", this.mousemoveHandler);
        document.removeEventListener("mouseup", this.mousemoveHandler);
        if (this.suckerPreview) {
          document.body.removeChild(this.suckerPreview);
          this.suckerPreview = null;
        }
      }
    },
    mousemoveHandler(e) {
      const { clientX, clientY } = e;
      const { top: domTop, left: domLeft, width, height } = this.suckerCanvas.getBoundingClientRect();
      const x = clientX - domLeft;
      const y = clientY - domTop;
      const ctx = this.suckerCanvas.getContext("2d");
      const imgData = ctx.getImageData(Math.min(x, width - 1), Math.min(y, height - 1), 1, 1);
      let [r, g, b, a] = imgData.data;
      a = parseFloat((a / 255).toFixed(2));
      const style = this.suckerPreview.style;
      Object.assign(style, {
        position: "absolute",
        left: clientX + 20 + "px",
        top: clientY - 36 + "px",
        width: "24px",
        height: "24px",
        borderRadius: "50%",
        border: "2px solid #fff",
        boxShadow: "0 0 8px 0 rgba(0, 0, 0, 0.16)",
        background: `rgba(${r}, ${g}, ${b}, ${a})`,
        zIndex: 95
      });
      if (this.suckerArea.length && // @ts-ignore
      clientX >= this.suckerArea[0] && // @ts-ignore
      clientY >= this.suckerArea[1] && // @ts-ignore
      clientX <= this.suckerArea[2] && // @ts-ignore
      clientY <= this.suckerArea[3]) {
        style.display = "";
      } else {
        style.display = "none";
      }
    },
    suckColor(dom) {
      if (dom && dom.tagName !== "CANVAS") {
        return;
      }
      this.suckerPreview = document.createElement("div");
      if (this.suckerPreview)
        document.body.appendChild(this.suckerPreview);
      document.addEventListener("mousemove", this.mousemoveHandler);
      document.addEventListener("mouseup", this.mousemoveHandler);
      dom.addEventListener("click", (e) => {
        const { clientX, clientY } = e;
        const { top, left, width, height } = dom.getBoundingClientRect();
        const x = clientX - left;
        const y = clientY - top;
        const ctx = dom.getContext("2d");
        const imgData = ctx.getImageData(Math.min(x, width - 1), Math.min(y, height - 1), 1, 1);
        let [r, g, b, a] = imgData.data;
        a = parseFloat((a / 255).toFixed(2));
        this.$emit("selectSucker", { r, g, b, a });
      });
    }
  }
});
var _hoisted_1$3 = createVNode(
  "path",
  { d: "M13.1,8.2l5.6,5.6c0.4,0.4,0.5,1.1,0.1,1.5s-1.1,0.5-1.5,0.1c0,0-0.1,0-0.1-0.1l-1.4-1.4l-7.7,7.7C7.9,21.9,7.6,22,7.3,22H3.1C2.5,22,2,21.5,2,20.9l0,0v-4.2c0-0.3,0.1-0.6,0.3-0.8l5.8-5.8C8.5,9.7,9.2,9.6,9.7,10s0.5,1.1,0.1,1.5c0,0,0,0.1-0.1,0.1l-5.5,5.5v2.7h2.7l7.4-7.4L8.7,6.8c-0.5-0.4-0.5-1-0.1-1.5s1.1-0.5,1.5-0.1c0,0,0.1,0,0.1,0.1l1.4,1.4l3.5-3.5c1.6-1.6,4.1-1.6,5.8-0.1c1.6,1.6,1.6,4.1,0.1,5.8L20.9,9l-3.6,3.6c-0.4,0.4-1.1,0.5-1.5,0.1" },
  null,
  -1
  /* HOISTED */
);
var _hoisted_2 = {
  key: 1,
  class: "sucker",
  viewBox: "-16 -16 68 68",
  xmlns: "http://www.w3.org/2000/svg",
  stroke: "#9099a4"
};
var _hoisted_3 = createVNode(
  "g",
  {
    fill: "none",
    "fill-rule": "evenodd"
  },
  [
    createVNode("g", {
      transform: "translate(1 1)",
      "stroke-width": "4"
    }, [
      createVNode("circle", {
        "stroke-opacity": ".5",
        cx: "18",
        cy: "18",
        r: "18"
      }),
      createVNode("path", { d: "M36 18c0-9.94-8.06-18-18-18" }, [
        createVNode("animateTransform", {
          attributeName: "transform",
          type: "rotate",
          from: "0 18 18",
          to: "360 18 18",
          dur: "1s",
          repeatCount: "indefinite"
        })
      ])
    ])
  ],
  -1
  /* HOISTED */
);
function render$4(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock("div", null, [
    !_ctx.isSucking ? (openBlock(), createBlock(
      "svg",
      {
        key: 0,
        class: [{ active: _ctx.isOpenSucker }, "sucker"],
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "-12 -12 48 48",
        onClick: _cache[1] || (_cache[1] = (...args) => _ctx.openSucker && _ctx.openSucker(...args))
      },
      [
        _hoisted_1$3
      ],
      2
      /* CLASS */
    )) : createCommentVNode("v-if", true),
    _ctx.isSucking ? (openBlock(), createBlock("svg", _hoisted_2, [
      _hoisted_3
    ])) : createCommentVNode("v-if", true)
  ]);
}
script$4.render = render$4;
script$4.__file = "src/color/Sucker.vue";
var script$5 = defineComponent({
  props: {
    name: {
      type: String,
      default: ""
    },
    color: {
      type: String,
      default: ""
    }
  },
  emits: ["inputColor", "inputFocus", "inputBlur"],
  setup(props, { emit }) {
    const modelColor = computed({
      get() {
        return props.color || "";
      },
      set(val) {
        emit("inputColor", val);
      }
    });
    const handleFocus = (event) => {
      emit("inputFocus", event);
    };
    const handleBlur = (event) => {
      emit("inputBlur", event);
    };
    return {
      modelColor,
      handleFocus,
      handleBlur
    };
  }
});
var _hoisted_1$4 = { class: "color-type" };
var _hoisted_2$1 = { class: "name" };
function render$5(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock("div", _hoisted_1$4, [
    createVNode(
      "span",
      _hoisted_2$1,
      toDisplayString(_ctx.name),
      1
      /* TEXT */
    ),
    withDirectives(createVNode(
      "input",
      {
        "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.modelColor = $event),
        class: "value",
        onFocus: _cache[2] || (_cache[2] = (...args) => _ctx.handleFocus && _ctx.handleFocus(...args)),
        onBlur: _cache[3] || (_cache[3] = (...args) => _ctx.handleBlur && _ctx.handleBlur(...args))
      },
      null,
      544
      /* HYDRATE_EVENTS, NEED_PATCH */
    ), [
      [vModelText, _ctx.modelColor]
    ])
  ]);
}
script$5.render = render$5;
script$5.__file = "src/color/Box.vue";
var script$6 = defineComponent({
  name: "ColorPicker",
  props: {
    color: {
      type: String,
      default: "#000000"
    },
    colorsDefault: {
      type: Array,
      default: () => []
    },
    colorsHistoryKey: {
      type: String,
      default: ""
    }
  },
  emits: ["selectColor"],
  setup(props, { emit }) {
    const color = ref();
    const colorsHistory = ref([]);
    const imgAlphaBase64 = ref();
    if (props.colorsHistoryKey && localStorage) {
      colorsHistory.value = JSON.parse(localStorage.getItem(props.colorsHistoryKey)) || [];
    }
    imgAlphaBase64.value = createAlphaSquare(4).toDataURL();
    onUnmounted(() => {
      setColorsHistory(color.value);
    });
    function setColorsHistory(color2) {
      if (!color2) {
        return;
      }
      const colors = colorsHistory.value || [];
      const index2 = colors.indexOf(color2);
      if (index2 >= 0) {
        colors.splice(index2, 1);
      }
      if (colors.length >= 8) {
        colors.length = 7;
      }
      colors.unshift(color2);
      colorsHistory.value = colors || [];
      if (localStorage && props.colorsHistoryKey)
        localStorage.setItem(props.colorsHistoryKey, JSON.stringify(colors));
    }
    function selectColor(color2) {
      emit("selectColor", color2);
    }
    return {
      setColorsHistory,
      colorsHistory,
      color,
      imgAlphaBase64,
      selectColor
    };
  }
});
var _hoisted_1$5 = { class: "colors" };
var _hoisted_2$2 = {
  key: 0,
  class: "colors history"
};
function render$6(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock("div", null, [
    createVNode("ul", _hoisted_1$5, [
      (openBlock(true), createBlock(
        Fragment,
        null,
        renderList(_ctx.colorsDefault, (item) => {
          return openBlock(), createBlock("li", {
            key: item,
            class: "item",
            onClick: ($event) => _ctx.selectColor(item)
          }, [
            createVNode(
              "div",
              {
                style: { background: `url(${_ctx.imgAlphaBase64})` },
                class: "alpha"
              },
              null,
              4
              /* STYLE */
            ),
            createVNode(
              "div",
              {
                style: { background: item },
                class: "color"
              },
              null,
              4
              /* STYLE */
            )
          ], 8, ["onClick"]);
        }),
        128
        /* KEYED_FRAGMENT */
      ))
    ]),
    _ctx.colorsHistory.length ? (openBlock(), createBlock("ul", _hoisted_2$2, [
      (openBlock(true), createBlock(
        Fragment,
        null,
        renderList(_ctx.colorsHistory, (item) => {
          return openBlock(), createBlock("li", {
            key: item,
            class: "item",
            onClick: ($event) => _ctx.selectColor(item)
          }, [
            createVNode(
              "div",
              {
                style: { background: `url(${_ctx.imgAlphaBase64})` },
                class: "alpha"
              },
              null,
              4
              /* STYLE */
            ),
            createVNode(
              "div",
              {
                style: { background: item },
                class: "color"
              },
              null,
              4
              /* STYLE */
            )
          ], 8, ["onClick"]);
        }),
        128
        /* KEYED_FRAGMENT */
      ))
    ])) : createCommentVNode("v-if", true)
  ]);
}
script$6.render = render$6;
script$6.__file = "src/color/Colors.vue";
var script$7 = defineComponent({
  components: {
    Saturation: script,
    Hue: script$1,
    Alpha: script$2,
    Preview: script$3,
    Sucker: script$4,
    Box: script$5,
    Colors: script$6
  },
  emits: ["changeColor", "openSucker", "inputFocus", "inputBlur"],
  props: {
    color: {
      type: String,
      default: "#000000"
    },
    theme: {
      type: String,
      default: "dark"
    },
    suckerHide: {
      type: Boolean,
      default: true
    },
    suckerCanvas: {
      type: null,
      default: null
    },
    suckerArea: {
      type: Array,
      default: () => []
    },
    colorsDefault: {
      type: Array,
      default: () => [
        "#000000",
        "#FFFFFF",
        "#FF1900",
        "#F47365",
        "#FFB243",
        "#FFE623",
        "#6EFF2A",
        "#1BC7B1",
        "#00BEFF",
        "#2E81FF",
        "#5D61FF",
        "#FF89CF",
        "#FC3CAD",
        "#BF3DCE",
        "#8E00A7",
        "rgba(0,0,0,0)"
      ]
    },
    colorsHistoryKey: {
      type: String,
      default: "vue-colorpicker-history"
    }
  },
  data() {
    return {
      hueWidth: 15,
      hueHeight: 152,
      previewHeight: 30,
      modelRgba: "",
      modelHex: "",
      r: 0,
      g: 0,
      b: 0,
      a: 1,
      h: 0,
      s: 0,
      v: 0
    };
  },
  computed: {
    isLightTheme() {
      return this.theme === "light";
    },
    totalWidth() {
      return this.hueHeight + (this.hueWidth + 8) * 2;
    },
    previewWidth() {
      return this.totalWidth - (this.suckerHide ? 0 : this.previewHeight);
    },
    rgba() {
      return {
        r: this.r,
        g: this.g,
        b: this.b,
        a: this.a
      };
    },
    hsv() {
      return {
        h: this.h,
        s: this.s,
        v: this.v
      };
    },
    rgbString() {
      return `rgb(${this.r}, ${this.g}, ${this.b})`;
    },
    rgbaStringShort() {
      return `${this.r}, ${this.g}, ${this.b}, ${this.a}`;
    },
    rgbaString() {
      return `rgba(${this.rgbaStringShort})`;
    },
    hexString() {
      return rgb2hex(this.rgba, true);
    }
  },
  created() {
    Object.assign(this, setColorValue(this.color));
    this.setText();
    this.$watch("rgba", () => {
      this.$emit("changeColor", {
        rgba: this.rgba,
        hsv: this.hsv,
        hex: this.modelHex
      });
    });
  },
  methods: {
    selectSaturation(color) {
      const { r, g, b, h, s, v } = setColorValue(color);
      Object.assign(this, { r, g, b, h, s, v });
      this.setText();
    },
    handleFocus(event) {
      this.$emit("inputFocus", event);
    },
    handleBlur(event) {
      this.$emit("inputBlur", event);
    },
    selectHue(color) {
      const { r, g, b, h, s, v } = setColorValue(color);
      Object.assign(this, { r, g, b, h, s, v });
      this.setText();
      this.$nextTick(() => {
        this.$refs.saturation.renderColor();
        this.$refs.saturation.renderSlide();
      });
    },
    selectAlpha(a) {
      this.a = a;
      this.setText();
    },
    inputHex(color) {
      const { r, g, b, a, h, s, v } = setColorValue(color);
      Object.assign(this, { r, g, b, a, h, s, v });
      this.modelHex = color;
      this.modelRgba = this.rgbaStringShort;
      this.$nextTick(() => {
        this.$refs.saturation.renderColor();
        this.$refs.saturation.renderSlide();
        this.$refs.hue.renderSlide();
      });
    },
    inputRgba(color) {
      const { r, g, b, a, h, s, v } = setColorValue(color);
      Object.assign(this, { r, g, b, a, h, s, v });
      this.modelHex = this.hexString;
      this.modelRgba = color;
      this.$nextTick(() => {
        this.$refs.saturation.renderColor();
        this.$refs.saturation.renderSlide();
        this.$refs.hue.renderSlide();
      });
    },
    setText() {
      this.modelHex = this.hexString;
      this.modelRgba = this.rgbaStringShort;
    },
    openSucker(isOpen) {
      this.$emit("openSucker", isOpen);
    },
    selectSucker(color) {
      const { r, g, b, a, h, s, v } = setColorValue(color);
      Object.assign(this, { r, g, b, a, h, s, v });
      this.setText();
      this.$nextTick(() => {
        this.$refs.saturation.renderColor();
        this.$refs.saturation.renderSlide();
        this.$refs.hue.renderSlide();
      });
    },
    selectColor(color) {
      const { r, g, b, a, h, s, v } = setColorValue(color);
      Object.assign(this, { r, g, b, a, h, s, v });
      this.setText();
      this.$nextTick(() => {
        this.$refs.saturation.renderColor();
        this.$refs.saturation.renderSlide();
        this.$refs.hue.renderSlide();
      });
    }
  }
});
var _hoisted_1$6 = { class: "color-set" };
function render$7(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_Saturation = resolveComponent("Saturation");
  const _component_Hue = resolveComponent("Hue");
  const _component_Alpha = resolveComponent("Alpha");
  const _component_Preview = resolveComponent("Preview");
  const _component_Sucker = resolveComponent("Sucker");
  const _component_Box = resolveComponent("Box");
  const _component_Colors = resolveComponent("Colors");
  return openBlock(), createBlock(
    "div",
    {
      class: ["hu-color-picker", { light: _ctx.isLightTheme }],
      style: { width: _ctx.totalWidth + "px" }
    },
    [
      createVNode("div", _hoisted_1$6, [
        createVNode(_component_Saturation, {
          ref: "saturation",
          color: _ctx.rgbString,
          hsv: _ctx.hsv,
          size: _ctx.hueHeight,
          onSelectSaturation: _ctx.selectSaturation
        }, null, 8, ["color", "hsv", "size", "onSelectSaturation"]),
        createVNode(_component_Hue, {
          ref: "hue",
          hsv: _ctx.hsv,
          width: _ctx.hueWidth,
          height: _ctx.hueHeight,
          onSelectHue: _ctx.selectHue
        }, null, 8, ["hsv", "width", "height", "onSelectHue"]),
        createVNode(_component_Alpha, {
          ref: "alpha",
          color: _ctx.rgbString,
          rgba: _ctx.rgba,
          width: _ctx.hueWidth,
          height: _ctx.hueHeight,
          onSelectAlpha: _ctx.selectAlpha
        }, null, 8, ["color", "rgba", "width", "height", "onSelectAlpha"])
      ]),
      createVNode(
        "div",
        {
          style: { height: _ctx.previewHeight + "px" },
          class: "color-show"
        },
        [
          createVNode(_component_Preview, {
            color: _ctx.rgbaString,
            width: _ctx.previewWidth,
            height: _ctx.previewHeight
          }, null, 8, ["color", "width", "height"]),
          !_ctx.suckerHide ? (openBlock(), createBlock(_component_Sucker, {
            key: 0,
            "sucker-canvas": _ctx.suckerCanvas,
            "sucker-area": _ctx.suckerArea,
            onOpenSucker: _ctx.openSucker,
            onSelectSucker: _ctx.selectSucker
          }, null, 8, ["sucker-canvas", "sucker-area", "onOpenSucker", "onSelectSucker"])) : createCommentVNode("v-if", true)
        ],
        4
        /* STYLE */
      ),
      createVNode(_component_Box, {
        name: "HEX",
        color: _ctx.modelHex,
        onInputColor: _ctx.inputHex,
        onInputFocus: _ctx.handleFocus,
        onInputBlur: _ctx.handleBlur
      }, null, 8, ["color", "onInputColor", "onInputFocus", "onInputBlur"]),
      createVNode(_component_Box, {
        name: "RGBA",
        color: _ctx.modelRgba,
        onInputColor: _ctx.inputRgba,
        onInputFocus: _ctx.handleFocus,
        onInputBlur: _ctx.handleBlur
      }, null, 8, ["color", "onInputColor", "onInputFocus", "onInputBlur"]),
      createVNode(_component_Colors, {
        color: _ctx.rgbaString,
        "colors-default": _ctx.colorsDefault,
        "colors-history-key": _ctx.colorsHistoryKey,
        onSelectColor: _ctx.selectColor
      }, null, 8, ["color", "colors-default", "colors-history-key", "onSelectColor"]),
      createCommentVNode(" custom options "),
      renderSlot(_ctx.$slots, "default")
    ],
    6
    /* CLASS, STYLE */
  );
}
script$7.render = render$7;
script$7.__file = "src/color/ColorPicker.vue";
script$7.install = (Vue) => {
  Vue.component(script$7.name, script$7);
};
function install(Vue) {
  Vue.component(script$7.name, script$7);
}
var index = { install };
var vue_color_kit_esm_bundler_default = index;
export {
  script$7 as ColorPicker,
  vue_color_kit_esm_bundler_default as default
};
/*! Bundled license information:

vue-color-kit/dist/vue-color-kit.esm-bundler.js:
  (*!
    * vue-color-kit v1.0.6
    * (c) 2023 
    * @license MIT
    *)
*/
//# sourceMappingURL=vue-color-kit.js.map
