import{f as O}from"./file2md5-B4-SI92N.js";import{g as c}from"./index-DkGLNqVb.js";import{M as g}from"./@arco-design-uttiljWv.js";import{r as x,a as S,h as m,j as C,k as v,l as o,m as a,n as h,t as i,y as n,z as d,u as q,O as D}from"./@vue-9ZIPiVZG.js";const F={getAppList(t={}){return c({url:"/app/saipackage/install/index",method:"get",params:t})},uploadApp(t={}){return c({url:"/app/saipackage/install/upload",method:"post",data:t})},installApp(t={}){return c({url:"/app/saipackage/install/install",method:"post",data:t})},uninstallApp(t={}){return c({url:"/app/saipackage/install/uninstall",method:"post",data:t})},reloadBackend(t={}){return c({url:"/app/saipackage/install/reload",method:"post",data:t})}},M={class:"flex flex-col items-center mb-24"},N={key:0,class:"mt-10 w-[600px]"},V={key:1,class:"mt-10 w-[600px]"},P={style:{"background-color":"var(--color-fill-2)",border:"1px dashed var(--color-fill-4)"},class:"rounded text-center p-7 w-full"},T=8*1024*1024,E={__name:"install-box",emits:["success"],setup(t,{expose:k,emit:y}){const w=y,f=x(!1);x(!1);const b={app:"",title:"",about:"",author:"",version:"",state:0,update:0},e=S({...b}),A=async p=>{if(!p.fileItem)return;let l=!0;const s=p.fileItem.file;if(s.size>T&&(g.warning(s.name+"超出文件大小限制"),l=!1),l){const _=await O(s),r=new FormData;r.append("file",s),r.append("hash",_);const u=await F.uploadApp(r);u.code==200&&(Object.assign(e,u.data),g.success("上传成功"),w("success"))}},B=async()=>{f.value=!0,Object.assign(e,b),await j()},j=async()=>{};return k({open:B}),(p,l)=>{const s=m("a-descriptions-item"),_=m("a-descriptions"),r=m("icon-upload"),u=m("a-upload");return v(),C(D("a-modal"),{visible:f.value,"onUpdate:visible":l[0]||(l[0]=z=>f.value=z),width:800,title:"上传插件包-安装插件","mask-closable":!1,footer:!1},{default:o(()=>[a("div",M,[l[2]||(l[2]=a("div",{class:"w-[400px]"},[a("div",{class:"text-lg text-red-500 font-bold"}," 请您务必确认模块包文件来自官方渠道或经由官方认证的模块作者，否则系统可能被破坏，因为： "),a("div",{class:"text-red-500"},"1. 模块可以修改和新增系统文件"),a("div",{class:"text-red-500"},"2. 模块可以执行sql命令和代码"),a("div",{class:"text-red-500"},"3. 模块可以安装新的前后端依赖")],-1)),e&&e.app?(v(),h("div",N,[i(_,{column:1,bordered:""},{default:o(()=>[i(s,{label:"应用标识"},{default:o(()=>[n(d(e==null?void 0:e.app),1)]),_:1}),i(s,{label:"应用名称"},{default:o(()=>[n(d(e==null?void 0:e.title),1)]),_:1}),i(s,{label:"应用描述"},{default:o(()=>[n(d(e==null?void 0:e.about),1)]),_:1}),i(s,{label:"作者"},{default:o(()=>[n(d(e==null?void 0:e.author),1)]),_:1}),i(s,{label:"版本"},{default:o(()=>[n(d(e==null?void 0:e.version),1)]),_:1})]),_:1})])):(v(),h("div",V,[i(u,{"custom-request":A,"show-file-list":!1,accept:".zip,.rar",draggable:!0},{"upload-button":o(()=>[q(p.$slots,"customer",{},()=>[a("div",P,[a("div",null,[i(r,{class:"text-3xl text-gray-400"}),l[1]||(l[1]=a("div",null,[n("将插件包文件拖到此处，或"),a("span",{style:{color:"#3370ff","margin-left":"10px"}},"点击上传")],-1))])])])]),_:3})]))])]),_:3},40,["visible"])}}},G=Object.freeze(Object.defineProperty({__proto__:null,default:E},Symbol.toStringTag,{value:"Module"}));export{E as _,G as i,F as s};
