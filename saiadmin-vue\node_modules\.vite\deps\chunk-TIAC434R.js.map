{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/_.contribution.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __reExport = (target, module, copyDefault, desc) => {\n  if (module && typeof module === \"object\" || typeof module === \"function\") {\n    for (let key of __getOwnPropNames(module))\n      if (!__hasOwnProp.call(target, key) && (copyDefault || key !== \"default\"))\n        __defProp(target, key, { get: () => module[key], enumerable: !(desc = __getOwnPropDesc(module, key)) || desc.enumerable });\n  }\n  return target;\n};\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../editor/editor.api.js\";\n\n// src/basic-languages/_.contribution.ts\nvar languageDefinitions = {};\nvar lazyLanguageLoaders = {};\nvar LazyLanguageLoader = class {\n  static getOrCreate(languageId) {\n    if (!lazyLanguageLoaders[languageId]) {\n      lazyLanguageLoaders[languageId] = new LazyLanguageLoader(languageId);\n    }\n    return lazyLanguageLoaders[languageId];\n  }\n  _languageId;\n  _loadingTriggered;\n  _lazyLoadPromise;\n  _lazyLoadPromiseResolve;\n  _lazyLoadPromiseReject;\n  constructor(languageId) {\n    this._languageId = languageId;\n    this._loadingTriggered = false;\n    this._lazyLoadPromise = new Promise((resolve, reject) => {\n      this._lazyLoadPromiseResolve = resolve;\n      this._lazyLoadPromiseReject = reject;\n    });\n  }\n  load() {\n    if (!this._loadingTriggered) {\n      this._loadingTriggered = true;\n      languageDefinitions[this._languageId].loader().then((mod) => this._lazyLoadPromiseResolve(mod), (err) => this._lazyLoadPromiseReject(err));\n    }\n    return this._lazyLoadPromise;\n  }\n};\nasync function loadLanguage(languageId) {\n  await LazyLanguageLoader.getOrCreate(languageId).load();\n  const model = monaco_editor_core_exports.editor.createModel(\"\", languageId);\n  model.dispose();\n}\nfunction registerLanguage(def) {\n  const languageId = def.id;\n  languageDefinitions[languageId] = def;\n  monaco_editor_core_exports.languages.register(def);\n  const lazyLanguageLoader = LazyLanguageLoader.getOrCreate(languageId);\n  monaco_editor_core_exports.languages.registerTokensProviderFactory(languageId, {\n    create: async () => {\n      const mod = await lazyLanguageLoader.load();\n      return mod.language;\n    }\n  });\n  monaco_editor_core_exports.languages.onLanguage(languageId, async () => {\n    const mod = await lazyLanguageLoader.load();\n    monaco_editor_core_exports.languages.setLanguageConfiguration(languageId, mod.conf);\n  });\n}\nexport {\n  loadLanguage,\n  registerLanguage\n};\n"], "mappings": ";;;;;;;;AAOA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,aAAa,CAAC,QAAQ,QAAQ,aAAa,SAAS;AACtD,MAAI,UAAU,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY;AACxE,aAAS,OAAO,kBAAkB,MAAM;AACtC,UAAI,CAAC,aAAa,KAAK,QAAQ,GAAG,MAAM,eAAe,QAAQ;AAC7D,kBAAU,QAAQ,KAAK,EAAE,KAAK,MAAM,OAAO,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,QAAQ,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EAC/H;AACA,SAAO;AACT;AAGA,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,sBAAsB,CAAC;AAC3B,IAAI,sBAAsB,CAAC;AAC3B,IAAI,qBAAqB,MAAM;AAAA,EAY7B,YAAY,YAAY;AALxB;AACA;AACA;AACA;AACA;AAEE,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,mBAAmB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvD,WAAK,0BAA0B;AAC/B,WAAK,yBAAyB;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EAlBA,OAAO,YAAY,YAAY;AAC7B,QAAI,CAAC,oBAAoB,UAAU,GAAG;AACpC,0BAAoB,UAAU,IAAI,IAAI,mBAAmB,UAAU;AAAA,IACrE;AACA,WAAO,oBAAoB,UAAU;AAAA,EACvC;AAAA,EAcA,OAAO;AACL,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,oBAAoB;AACzB,0BAAoB,KAAK,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,QAAQ,KAAK,wBAAwB,GAAG,GAAG,CAAC,QAAQ,KAAK,uBAAuB,GAAG,CAAC;AAAA,IAC3I;AACA,WAAO,KAAK;AAAA,EACd;AACF;AAMA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,aAAa,IAAI;AACvB,sBAAoB,UAAU,IAAI;AAClC,6BAA2B,UAAU,SAAS,GAAG;AACjD,QAAM,qBAAqB,mBAAmB,YAAY,UAAU;AACpE,6BAA2B,UAAU,8BAA8B,YAAY;AAAA,IAC7E,QAAQ,YAAY;AAClB,YAAM,MAAM,MAAM,mBAAmB,KAAK;AAC1C,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AACD,6BAA2B,UAAU,WAAW,YAAY,YAAY;AACtE,UAAM,MAAM,MAAM,mBAAmB,KAAK;AAC1C,+BAA2B,UAAU,yBAAyB,YAAY,IAAI,IAAI;AAAA,EACpF,CAAC;AACH;", "names": []}