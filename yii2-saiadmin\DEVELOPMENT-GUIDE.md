# SaiAdmin Yii 2.0 开发指南

## 🎯 快速开始

### 访问演示页面
- 演示首页: http://localhost:8080/demo
- API演示: http://localhost:8080/demo/api
- 表单演示: http://localhost:8080/demo/form
- 数据库演示: http://localhost:8080/demo/database
- 缓存演示: http://localhost:8080/demo/cache

### 开发工具
- Gii代码生成器: http://localhost:8080/gii
- 调试工具栏: http://localhost:8080/debug

## 🔧 常用命令

### 数据库迁移
```bash
# 运行迁移
php yii migrate

# 创建新迁移
php yii migrate/create create_table_name

# 查看迁移历史
php yii migrate/history
```

### 代码生成
```bash
# 生成模型
php yii gii/model --tableName=demo --modelClass=Demo

# 生成CRUD
php yii gii/crud --modelClass="app\models\Demo" --controllerClass="app\controllers\DemoController"

# 生成控制器
php yii gii/controller --controller=demo
```

### 缓存管理
```bash
# 清除所有缓存
php yii cache/flush-all

# 清除指定缓存
php yii cache/flush cache1 cache2
```

## 📁 项目结构

```
controllers/     # 控制器
├── DemoController.php

models/         # 模型
├── Demo.php

views/          # 视图
├── demo/
│   ├── index.php
│   ├── form.php
│   ├── database.php
│   └── cache.php

migrations/     # 数据库迁移
├── m*_create_demo_table.php
```

## 🎨 开发模式

### MVC模式
- **Model**: 数据模型和业务逻辑
- **View**: 用户界面和展示逻辑
- **Controller**: 请求处理和流程控制

### 组件系统
- **Application**: 应用主体
- **Component**: 可配置的功能组件
- **Behavior**: 行为扩展
- **Event**: 事件系统

## 📖 学习资源

- [Yii 2.0 权威指南](https://www.yiiframework.com/doc/guide/2.0/zh-cn)
- [Yii 2.0 API文档](https://www.yiiframework.com/doc/api/2.0)
- [SaiAdmin文档](https://saiadmin.com/docs)

## 🚀 下一步

1. 运行数据库迁移: `php yii migrate`
2. 访问演示页面: http://localhost:8080/demo
3. 使用Gii生成代码: http://localhost:8080/gii
4. 开始您的项目开发！
