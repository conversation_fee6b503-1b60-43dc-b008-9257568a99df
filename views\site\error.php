<?php
/**
 * 错误页面视图
 */
use yii\helpers\Html;

$this->title = $name;
?>

<div class="site-error">
    <div class="text-center">
        <h1 class="display-1 text-danger"><?= Html::encode($name) ?></h1>
        
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">出现错误！</h4>
            <p><?= nl2br(Html::encode($message)) ?></p>
        </div>

        <div class="mt-4">
            <?= Html::a('返回首页', ['/'], ['class' => 'btn btn-primary btn-lg']) ?>
            <?= Html::a('刷新页面', '', ['class' => 'btn btn-secondary btn-lg', 'onclick' => 'location.reload(); return false;']) ?>
        </div>

        <?php if (YII_DEBUG && !empty($exception)): ?>
            <div class="mt-5">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">调试信息</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>异常类型:</strong> <?= get_class($exception) ?></p>
                        <p><strong>文件:</strong> <?= $exception->getFile() ?></p>
                        <p><strong>行号:</strong> <?= $exception->getLine() ?></p>
                        <p><strong>堆栈跟踪:</strong></p>
                        <pre class="bg-light p-3"><?= Html::encode($exception->getTraceAsString()) ?></pre>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
