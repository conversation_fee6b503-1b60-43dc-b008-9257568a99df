<?php return array(
    'root' => array(
        'name' => 'saiadmin/yii2-saiadmin',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '28bb6fbb22099abf9347376d308a5ffc2d22396f',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'bower-asset/inputmask' => array(
            'pretty_version' => '5.0.9',
            'version' => '5.0.9.0',
            'reference' => '310a33557e2944daf86d5946a5e8c82b9118f8f7',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/inputmask',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/jquery' => array(
            'pretty_version' => '3.7.1',
            'version' => '3.7.1.0',
            'reference' => 'fde1f76e2799dd877c176abde0ec836553246991',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/jquery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/punycode' => array(
            'pretty_version' => 'v1.4.1',
            'version' => '1.4.1.0',
            'reference' => '0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/punycode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/yii2-pjax' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'a9298d57da63d14a950f1b94366a864bc62264fb',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../bower-asset/yii2-pjax',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cebe/markdown' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => '9bac5e971dd391e2802dca5400bbeacbaea9eb86',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cebe/markdown',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '3.2.6',
            'version' => '3.2.6.0',
            'reference' => 'e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'npm-asset/bootstrap' => array(
            'pretty_version' => '4.6.2',
            'version' => '4.6.2.0',
            'reference' => null,
            'type' => 'npm-asset',
            'install_path' => __DIR__ . '/../npm-asset/bootstrap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'saiadmin/yii2-saiadmin' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '28bb6fbb22099abf9347376d308a5ffc2d22396f',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'swiftmailer/swiftmailer' => array(
            'pretty_version' => 'v6.3.0',
            'version' => '6.3.0.0',
            'reference' => '8a5d5072dca8f48460fce2f4131fcc495eec654c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../swiftmailer/swiftmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '5f3b930437ae03ae5dff61269024d8ea1b3774aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2' => array(
            'pretty_version' => '2.0.53',
            'version' => '2.0.53.0',
            'reference' => '6c622fb8243181d7912b62ad80821cc0e1c745db',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yiisoft/yii2',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-bootstrap4' => array(
            'pretty_version' => '2.0.12',
            'version' => '2.0.12.0',
            'reference' => '49f9b70de3f5ab55ea1dc1ea57021a6bf91b3102',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-bootstrap4',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-composer' => array(
            'pretty_version' => '2.0.11',
            'version' => '2.0.11.0',
            'reference' => 'b684b01ecb119c8287721def726a0e24fec2fef2',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../yiisoft/yii2-composer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-swiftmailer' => array(
            'pretty_version' => '2.1.3',
            'version' => '2.1.3.0',
            'reference' => '7b7ec871b4a63c0abbcd10e1ee3fb5be22f8b340',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-swiftmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
