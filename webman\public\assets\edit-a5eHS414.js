import{t as F}from"./index-ybrmzYq5.js";import{a as v}from"./post-DszuaI2s.js";import{M}from"./@arco-design-uttiljWv.js";import{r as d,c as R,a as N,h as m,j as P,k as T,l as r,t as a,a1 as V,O as z}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const Je={__name:"edit",emits:["success"],setup(A,{expose:b,emit:k}){const g=k,u=d(),f=d(""),n=d(!1),c=d(!1);let w=R(()=>"岗位管理"+(f.value=="add"?"-新增":"-编辑"));const _={id:"",name:"",code:"",sort:100,status:1,remark:""},t=N({..._}),x={name:[{required:!0,message:"岗位名称不能为空"}],code:[{required:!0,message:"岗位标识不能为空"}]},y=async(i="add")=>{f.value=i,Object.assign(t,_),u.value.clearValidate(),n.value=!0,await U()},U=async()=>{},D=async i=>{for(const e in t)i[e]!=null&&i[e]!=null&&(t[e]=i[e])},C=async i=>{var s;if(!await((s=u.value)==null?void 0:s.validate())){c.value=!0;let l={...t},p={};f.value==="add"?(l.id=void 0,p=await v.save(l)):p=await v.update(l.id,l),p.code===200&&(M.success("操作成功"),g("success"),i(!0)),setTimeout(()=>{c.value=!1},500)}i(!1)},B=()=>n.value=!1;return b({open:y,setFormData:D}),(i,e)=>{const s=m("a-input"),l=m("a-form-item"),p=m("a-input-number"),O=m("sa-radio"),j=m("a-textarea"),q=m("a-form");return T(),P(z("a-modal"),{visible:n.value,"onUpdate:visible":e[5]||(e[5]=o=>n.value=o),width:V(F).getDevice()==="mobile"?"100%":"600px",title:V(w),"mask-closable":!1,"ok-loading":c.value,onCancel:B,onBeforeOk:C},{default:r(()=>[a(q,{ref_key:"formRef",ref:u,model:t,rules:x,"auto-label-width":!0},{default:r(()=>[a(l,{label:"岗位名称",field:"name"},{default:r(()=>[a(s,{modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.name=o),placeholder:"请输入岗位名称"},null,8,["modelValue"])]),_:1}),a(l,{label:"岗位标识",field:"code"},{default:r(()=>[a(s,{modelValue:t.code,"onUpdate:modelValue":e[1]||(e[1]=o=>t.code=o),placeholder:"请输入岗位标识"},null,8,["modelValue"])]),_:1}),a(l,{label:"排序",field:"sort"},{default:r(()=>[a(p,{modelValue:t.sort,"onUpdate:modelValue":e[2]||(e[2]=o=>t.sort=o),placeholder:"请输入排序"},null,8,["modelValue"])]),_:1}),a(l,{label:"状态",field:"status"},{default:r(()=>[a(O,{modelValue:t.status,"onUpdate:modelValue":e[3]||(e[3]=o=>t.status=o),dict:"data_status",placeholder:"请选择状态"},null,8,["modelValue"])]),_:1}),a(l,{label:"备注",field:"remark"},{default:r(()=>[a(j,{modelValue:t.remark,"onUpdate:modelValue":e[4]||(e[4]=o=>t.remark=o),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},40,["visible","width","title","ok-loading"])}}};export{Je as default};
