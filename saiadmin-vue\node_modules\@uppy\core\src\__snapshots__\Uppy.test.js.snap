// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`src/Core plugins should not be able to add a plugin that has no id 1`] = `"Your plugin must have an id"`;

exports[`src/Core plugins should not be able to add a plugin that has no type 1`] = `"Your plugin must have a type"`;

exports[`src/Core plugins should not be able to add an invalid plugin 1`] = `"Expected a plugin class, but got object. Please verify that the plugin was imported and spelled correctly."`;

exports[`src/Core plugins should prevent the same plugin from being added more than once 1`] = `
"Already found a plugin named 'TestSelector1'. Tried to use: 'TestSelector1'.
Uppy plugins must have unique \`id\` options. See https://uppy.io/docs/plugins/#id."
`;

exports[`src/Core uploading a file should only upload files that are not already assigned to another upload id 1`] = `
Object {
  "failed": Array [],
  "successful": Array [
    Object {
      "data": Uint8Array [],
      "extension": "jpg",
      "id": "uppy-foo/jpg-1e-image/jpeg",
      "isRemote": false,
      "meta": Object {
        "name": "foo.jpg",
        "type": "image/jpeg",
      },
      "name": "foo.jpg",
      "preview": undefined,
      "progress": Object {
        "bytesTotal": null,
        "bytesUploaded": 0,
        "percentage": 0,
        "uploadComplete": false,
        "uploadStarted": null,
      },
      "remote": "",
      "size": null,
      "source": "jest",
      "type": "image/jpeg",
    },
    Object {
      "data": Uint8Array [],
      "extension": "jpg",
      "id": "uppy-bar/jpg-1e-image/jpeg",
      "isRemote": false,
      "meta": Object {
        "name": "bar.jpg",
        "type": "image/jpeg",
      },
      "name": "bar.jpg",
      "preview": undefined,
      "progress": Object {
        "bytesTotal": null,
        "bytesUploaded": 0,
        "percentage": 0,
        "uploadComplete": false,
        "uploadStarted": null,
      },
      "remote": "",
      "size": null,
      "source": "jest",
      "type": "image/jpeg",
    },
  ],
  "uploadID": "cjd09qwxb000dlql4tp4doz8h",
}
`;
