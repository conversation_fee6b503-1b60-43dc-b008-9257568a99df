import{a as d}from"./crontab-DHsN78bF.js";import{t as f}from"./index-ybrmzYq5.js";import{M as L,j as I}from"./@arco-design-uttiljWv.js";import{r as p,a as _,h as i,j as B,k as D,l as r,t as a,y as l,z as F,a1 as M}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const $t={__name:"logList",setup(R,{expose:v}){const m=p(),h=p(),n=p(!1),c=p({orderBy:"create_time",orderType:"desc",crontab_id:""}),w=o=>{h.value=o,c.value.crontab_id=o,n.value=!0,m.value.refresh()},u=o=>{const t=o.exception_info;I.info({simple:!1,width:f.getDevice()==="mobile"?"100%":"600px",title:"异常信息",content:t==""?"无异常信息":t})},g=_({autoRequest:!1,api:d.getLogPageList,showSearch:!1,showTools:!1,rowSelection:{showCheckedAll:!0},operationColumnWidth:140,view:{show:!0,func:async o=>{u(o)}},delete:{show:!0,auth:["/tool/crontab/deleteLog"],func:async o=>{var s;(await d.deleteLog(o)).code===200&&(L.success("删除成功！"),(s=m.value)==null||s.refresh())}}}),b=_([{title:"执行时间",dataIndex:"create_time",width:180},{title:"执行目标",dataIndex:"target",width:240},{title:"执行结果",dataIndex:"status",width:100}]);return v({open:w}),(o,t)=>{const s=i("a-tag"),x=i("icon-eye"),k=i("a-link"),y=i("sa-table"),C=i("a-drawer");return D(),B(C,{footer:!1,visible:n.value,"onUpdate:visible":t[0]||(t[0]=e=>n.value=e),width:M(f).getDevice()==="mobile"?"100%":"60%"},{title:r(()=>t[1]||(t[1]=[l("执行日志")])),default:r(()=>[a(y,{ref_key:"crudRef",ref:m,options:g,columns:b,searchForm:c.value},{status:r(({record:e})=>[a(s,{color:`${e.status==1?"green":"red"}`},{default:r(()=>[l(F(e.status==1?"成功":"失败"),1)]),_:2},1032,["color"])]),exception_info:r(({record:e})=>[a(k,{onClick:S=>u(e)},{default:r(()=>[a(x),t[2]||(t[2]=l(" 查看"))]),_:2},1032,["onClick"])]),_:1},8,["options","columns","searchForm"])]),_:1},8,["visible","width"])}}};export{$t as default};
