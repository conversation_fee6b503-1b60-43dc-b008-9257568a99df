import{h as t}from"./index-ybrmzYq5.js";const o={getPageList(e={}){return t({url:"/app/saicode/table/index",method:"get",params:e})},getRecyclePageList(e={}){return t({url:"/app/saicode/table/recycle",method:"get",params:e})},deletes(e){return t({url:"/app/saicode/table/destroy",method:"delete",data:e})},recoverys(e){return t({url:"/app/saicode/table/recovery",method:"post",data:e})},realDestroy(e){return t({url:"/app/saicode/table/realDestroy",method:"delete",data:e})},update(e,a={}){return t({url:"/app/saicode/table/update?id="+e,method:"put",data:a})},readTable(e){return t({url:"/app/saicode/table/read?id="+e,method:"get"})},generateCode(e={}){return t({url:"/app/saicode/table/generate",method:"post",responseType:"blob",timeout:20*1e3,data:e})},generateFile(e={}){return t({url:"/app/saicode/table/generateFile",method:"post",data:e})},loadTable(e={}){return t({url:"/app/saicode/table/loadTable",method:"post",data:e})},sync(e){return t({url:"/app/saicode/table/sync?id="+e,method:"post"})},preview(e){return t({url:"/app/saicode/table/preview?id="+e,method:"get"})},getTableColumns(e={}){return t({url:"/app/saicode/table/getTableColumns",method:"get",params:e})},getDataSource(e={}){return t({url:"/app/saicode/table/source",method:"get",params:e})},getSourceTable(e={}){return t({url:"/app/saicode/table/sourceTable",method:"get",params:e})},saveDesign(e={}){return t({url:"/app/saicode/table/saveDesign",method:"post",data:e})}};export{o as a};
