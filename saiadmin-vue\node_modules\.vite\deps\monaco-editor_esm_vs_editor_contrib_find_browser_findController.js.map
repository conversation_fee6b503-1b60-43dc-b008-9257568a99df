{"version": 3, "sources": ["../../monaco-editor/esm/vs/editor/contrib/find/browser/findDecorations.js", "../../monaco-editor/esm/vs/editor/contrib/find/browser/replaceAllCommand.js", "../../monaco-editor/esm/vs/base/common/search.js", "../../monaco-editor/esm/vs/editor/contrib/find/browser/replacePattern.js", "../../monaco-editor/esm/vs/editor/contrib/find/browser/findModel.js", "../../monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.js", "../../monaco-editor/esm/vs/base/browser/ui/findinput/findInputCheckboxes.js", "../../monaco-editor/esm/vs/editor/contrib/find/browser/findOptionsWidget.js", "../../monaco-editor/esm/vs/editor/contrib/find/browser/findState.js", "../../monaco-editor/esm/vs/editor/contrib/find/browser/findWidget.js", "../../monaco-editor/esm/vs/base/browser/ui/findinput/findInput.js", "../../monaco-editor/esm/vs/base/browser/ui/findinput/replaceInput.js", "../../monaco-editor/esm/vs/platform/history/browser/contextScopedHistoryWidget.js", "../../monaco-editor/esm/vs/platform/history/browser/historyWidgetKeybindingHint.js", "../../monaco-editor/esm/vs/editor/contrib/find/browser/findController.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/core/range.js';\nimport { MinimapPosition, OverviewRulerLane } from '../../../common/model.js';\nimport { ModelDecorationOptions } from '../../../common/model/textModel.js';\nimport { minimapFindMatch, overviewRulerFindMatchForeground } from '../../../../platform/theme/common/colorRegistry.js';\nimport { themeColorFromId } from '../../../../platform/theme/common/themeService.js';\nexport class FindDecorations {\n    constructor(editor) {\n        this._editor = editor;\n        this._decorations = [];\n        this._overviewRulerApproximateDecorations = [];\n        this._findScopeDecorationIds = [];\n        this._rangeHighlightDecorationId = null;\n        this._highlightedDecorationId = null;\n        this._startPosition = this._editor.getPosition();\n    }\n    dispose() {\n        this._editor.deltaDecorations(this._allDecorations(), []);\n        this._decorations = [];\n        this._overviewRulerApproximateDecorations = [];\n        this._findScopeDecorationIds = [];\n        this._rangeHighlightDecorationId = null;\n        this._highlightedDecorationId = null;\n    }\n    reset() {\n        this._decorations = [];\n        this._overviewRulerApproximateDecorations = [];\n        this._findScopeDecorationIds = [];\n        this._rangeHighlightDecorationId = null;\n        this._highlightedDecorationId = null;\n    }\n    getCount() {\n        return this._decorations.length;\n    }\n    /** @deprecated use getFindScopes to support multiple selections */\n    getFindScope() {\n        if (this._findScopeDecorationIds[0]) {\n            return this._editor.getModel().getDecorationRange(this._findScopeDecorationIds[0]);\n        }\n        return null;\n    }\n    getFindScopes() {\n        if (this._findScopeDecorationIds.length) {\n            const scopes = this._findScopeDecorationIds.map(findScopeDecorationId => this._editor.getModel().getDecorationRange(findScopeDecorationId)).filter(element => !!element);\n            if (scopes.length) {\n                return scopes;\n            }\n        }\n        return null;\n    }\n    getStartPosition() {\n        return this._startPosition;\n    }\n    setStartPosition(newStartPosition) {\n        this._startPosition = newStartPosition;\n        this.setCurrentFindMatch(null);\n    }\n    _getDecorationIndex(decorationId) {\n        const index = this._decorations.indexOf(decorationId);\n        if (index >= 0) {\n            return index + 1;\n        }\n        return 1;\n    }\n    getCurrentMatchesPosition(desiredRange) {\n        let candidates = this._editor.getModel().getDecorationsInRange(desiredRange);\n        for (const candidate of candidates) {\n            const candidateOpts = candidate.options;\n            if (candidateOpts === FindDecorations._FIND_MATCH_DECORATION || candidateOpts === FindDecorations._CURRENT_FIND_MATCH_DECORATION) {\n                return this._getDecorationIndex(candidate.id);\n            }\n        }\n        // We don't know the current match position, so returns zero to show '?' in find widget\n        return 0;\n    }\n    setCurrentFindMatch(nextMatch) {\n        let newCurrentDecorationId = null;\n        let matchPosition = 0;\n        if (nextMatch) {\n            for (let i = 0, len = this._decorations.length; i < len; i++) {\n                let range = this._editor.getModel().getDecorationRange(this._decorations[i]);\n                if (nextMatch.equalsRange(range)) {\n                    newCurrentDecorationId = this._decorations[i];\n                    matchPosition = (i + 1);\n                    break;\n                }\n            }\n        }\n        if (this._highlightedDecorationId !== null || newCurrentDecorationId !== null) {\n            this._editor.changeDecorations((changeAccessor) => {\n                if (this._highlightedDecorationId !== null) {\n                    changeAccessor.changeDecorationOptions(this._highlightedDecorationId, FindDecorations._FIND_MATCH_DECORATION);\n                    this._highlightedDecorationId = null;\n                }\n                if (newCurrentDecorationId !== null) {\n                    this._highlightedDecorationId = newCurrentDecorationId;\n                    changeAccessor.changeDecorationOptions(this._highlightedDecorationId, FindDecorations._CURRENT_FIND_MATCH_DECORATION);\n                }\n                if (this._rangeHighlightDecorationId !== null) {\n                    changeAccessor.removeDecoration(this._rangeHighlightDecorationId);\n                    this._rangeHighlightDecorationId = null;\n                }\n                if (newCurrentDecorationId !== null) {\n                    let rng = this._editor.getModel().getDecorationRange(newCurrentDecorationId);\n                    if (rng.startLineNumber !== rng.endLineNumber && rng.endColumn === 1) {\n                        let lineBeforeEnd = rng.endLineNumber - 1;\n                        let lineBeforeEndMaxColumn = this._editor.getModel().getLineMaxColumn(lineBeforeEnd);\n                        rng = new Range(rng.startLineNumber, rng.startColumn, lineBeforeEnd, lineBeforeEndMaxColumn);\n                    }\n                    this._rangeHighlightDecorationId = changeAccessor.addDecoration(rng, FindDecorations._RANGE_HIGHLIGHT_DECORATION);\n                }\n            });\n        }\n        return matchPosition;\n    }\n    set(findMatches, findScopes) {\n        this._editor.changeDecorations((accessor) => {\n            let findMatchesOptions = FindDecorations._FIND_MATCH_DECORATION;\n            let newOverviewRulerApproximateDecorations = [];\n            if (findMatches.length > 1000) {\n                // we go into a mode where the overview ruler gets \"approximate\" decorations\n                // the reason is that the overview ruler paints all the decorations in the file and we don't want to cause freezes\n                findMatchesOptions = FindDecorations._FIND_MATCH_NO_OVERVIEW_DECORATION;\n                // approximate a distance in lines where matches should be merged\n                const lineCount = this._editor.getModel().getLineCount();\n                const height = this._editor.getLayoutInfo().height;\n                const approxPixelsPerLine = height / lineCount;\n                const mergeLinesDelta = Math.max(2, Math.ceil(3 / approxPixelsPerLine));\n                // merge decorations as much as possible\n                let prevStartLineNumber = findMatches[0].range.startLineNumber;\n                let prevEndLineNumber = findMatches[0].range.endLineNumber;\n                for (let i = 1, len = findMatches.length; i < len; i++) {\n                    const range = findMatches[i].range;\n                    if (prevEndLineNumber + mergeLinesDelta >= range.startLineNumber) {\n                        if (range.endLineNumber > prevEndLineNumber) {\n                            prevEndLineNumber = range.endLineNumber;\n                        }\n                    }\n                    else {\n                        newOverviewRulerApproximateDecorations.push({\n                            range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),\n                            options: FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION\n                        });\n                        prevStartLineNumber = range.startLineNumber;\n                        prevEndLineNumber = range.endLineNumber;\n                    }\n                }\n                newOverviewRulerApproximateDecorations.push({\n                    range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),\n                    options: FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION\n                });\n            }\n            // Find matches\n            let newFindMatchesDecorations = new Array(findMatches.length);\n            for (let i = 0, len = findMatches.length; i < len; i++) {\n                newFindMatchesDecorations[i] = {\n                    range: findMatches[i].range,\n                    options: findMatchesOptions\n                };\n            }\n            this._decorations = accessor.deltaDecorations(this._decorations, newFindMatchesDecorations);\n            // Overview ruler approximate decorations\n            this._overviewRulerApproximateDecorations = accessor.deltaDecorations(this._overviewRulerApproximateDecorations, newOverviewRulerApproximateDecorations);\n            // Range highlight\n            if (this._rangeHighlightDecorationId) {\n                accessor.removeDecoration(this._rangeHighlightDecorationId);\n                this._rangeHighlightDecorationId = null;\n            }\n            // Find scope\n            if (this._findScopeDecorationIds.length) {\n                this._findScopeDecorationIds.forEach(findScopeDecorationId => accessor.removeDecoration(findScopeDecorationId));\n                this._findScopeDecorationIds = [];\n            }\n            if (findScopes === null || findScopes === void 0 ? void 0 : findScopes.length) {\n                this._findScopeDecorationIds = findScopes.map(findScope => accessor.addDecoration(findScope, FindDecorations._FIND_SCOPE_DECORATION));\n            }\n        });\n    }\n    matchBeforePosition(position) {\n        if (this._decorations.length === 0) {\n            return null;\n        }\n        for (let i = this._decorations.length - 1; i >= 0; i--) {\n            let decorationId = this._decorations[i];\n            let r = this._editor.getModel().getDecorationRange(decorationId);\n            if (!r || r.endLineNumber > position.lineNumber) {\n                continue;\n            }\n            if (r.endLineNumber < position.lineNumber) {\n                return r;\n            }\n            if (r.endColumn > position.column) {\n                continue;\n            }\n            return r;\n        }\n        return this._editor.getModel().getDecorationRange(this._decorations[this._decorations.length - 1]);\n    }\n    matchAfterPosition(position) {\n        if (this._decorations.length === 0) {\n            return null;\n        }\n        for (let i = 0, len = this._decorations.length; i < len; i++) {\n            let decorationId = this._decorations[i];\n            let r = this._editor.getModel().getDecorationRange(decorationId);\n            if (!r || r.startLineNumber < position.lineNumber) {\n                continue;\n            }\n            if (r.startLineNumber > position.lineNumber) {\n                return r;\n            }\n            if (r.startColumn < position.column) {\n                continue;\n            }\n            return r;\n        }\n        return this._editor.getModel().getDecorationRange(this._decorations[0]);\n    }\n    _allDecorations() {\n        let result = [];\n        result = result.concat(this._decorations);\n        result = result.concat(this._overviewRulerApproximateDecorations);\n        if (this._findScopeDecorationIds.length) {\n            result.push(...this._findScopeDecorationIds);\n        }\n        if (this._rangeHighlightDecorationId) {\n            result.push(this._rangeHighlightDecorationId);\n        }\n        return result;\n    }\n}\nFindDecorations._CURRENT_FIND_MATCH_DECORATION = ModelDecorationOptions.register({\n    description: 'current-find-match',\n    stickiness: 1 /* NeverGrowsWhenTypingAtEdges */,\n    zIndex: 13,\n    className: 'currentFindMatch',\n    showIfCollapsed: true,\n    overviewRuler: {\n        color: themeColorFromId(overviewRulerFindMatchForeground),\n        position: OverviewRulerLane.Center\n    },\n    minimap: {\n        color: themeColorFromId(minimapFindMatch),\n        position: MinimapPosition.Inline\n    }\n});\nFindDecorations._FIND_MATCH_DECORATION = ModelDecorationOptions.register({\n    description: 'find-match',\n    stickiness: 1 /* NeverGrowsWhenTypingAtEdges */,\n    zIndex: 10,\n    className: 'findMatch',\n    showIfCollapsed: true,\n    overviewRuler: {\n        color: themeColorFromId(overviewRulerFindMatchForeground),\n        position: OverviewRulerLane.Center\n    },\n    minimap: {\n        color: themeColorFromId(minimapFindMatch),\n        position: MinimapPosition.Inline\n    }\n});\nFindDecorations._FIND_MATCH_NO_OVERVIEW_DECORATION = ModelDecorationOptions.register({\n    description: 'find-match-no-overview',\n    stickiness: 1 /* NeverGrowsWhenTypingAtEdges */,\n    className: 'findMatch',\n    showIfCollapsed: true\n});\nFindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION = ModelDecorationOptions.register({\n    description: 'find-match-only-overview',\n    stickiness: 1 /* NeverGrowsWhenTypingAtEdges */,\n    overviewRuler: {\n        color: themeColorFromId(overviewRulerFindMatchForeground),\n        position: OverviewRulerLane.Center\n    }\n});\nFindDecorations._RANGE_HIGHLIGHT_DECORATION = ModelDecorationOptions.register({\n    description: 'find-range-highlight',\n    stickiness: 1 /* NeverGrowsWhenTypingAtEdges */,\n    className: 'rangeHighlight',\n    isWholeLine: true\n});\nFindDecorations._FIND_SCOPE_DECORATION = ModelDecorationOptions.register({\n    description: 'find-scope',\n    className: 'findScope',\n    isWholeLine: true\n});\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/core/range.js';\nexport class ReplaceAllCommand {\n    constructor(editorSelection, ranges, replaceStrings) {\n        this._editorSelection = editorSelection;\n        this._ranges = ranges;\n        this._replaceStrings = replaceStrings;\n        this._trackedEditorSelectionId = null;\n    }\n    getEditOperations(model, builder) {\n        if (this._ranges.length > 0) {\n            // Collect all edit operations\n            let ops = [];\n            for (let i = 0; i < this._ranges.length; i++) {\n                ops.push({\n                    range: this._ranges[i],\n                    text: this._replaceStrings[i]\n                });\n            }\n            // Sort them in ascending order by range starts\n            ops.sort((o1, o2) => {\n                return Range.compareRangesUsingStarts(o1.range, o2.range);\n            });\n            // Merge operations that touch each other\n            let resultOps = [];\n            let previousOp = ops[0];\n            for (let i = 1; i < ops.length; i++) {\n                if (previousOp.range.endLineNumber === ops[i].range.startLineNumber && previousOp.range.endColumn === ops[i].range.startColumn) {\n                    // These operations are one after another and can be merged\n                    previousOp.range = previousOp.range.plusRange(ops[i].range);\n                    previousOp.text = previousOp.text + ops[i].text;\n                }\n                else {\n                    resultOps.push(previousOp);\n                    previousOp = ops[i];\n                }\n            }\n            resultOps.push(previousOp);\n            for (const op of resultOps) {\n                builder.addEditOperation(op.range, op.text);\n            }\n        }\n        this._trackedEditorSelectionId = builder.trackSelection(this._editorSelection);\n    }\n    computeCursorState(model, helper) {\n        return helper.getTrackedSelection(this._trackedEditorSelectionId);\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from './strings.js';\nexport function buildReplaceStringWithCasePreserved(matches, pattern) {\n    if (matches && (matches[0] !== '')) {\n        const containsHyphens = validateSpecificSpecialCharacter(matches, pattern, '-');\n        const containsUnderscores = validateSpecificSpecialCharacter(matches, pattern, '_');\n        if (containsHyphens && !containsUnderscores) {\n            return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, '-');\n        }\n        else if (!containsHyphens && containsUnderscores) {\n            return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, '_');\n        }\n        if (matches[0].toUpperCase() === matches[0]) {\n            return pattern.toUpperCase();\n        }\n        else if (matches[0].toLowerCase() === matches[0]) {\n            return pattern.toLowerCase();\n        }\n        else if (strings.containsUppercaseCharacter(matches[0][0]) && pattern.length > 0) {\n            return pattern[0].toUpperCase() + pattern.substr(1);\n        }\n        else if (matches[0][0].toUpperCase() !== matches[0][0] && pattern.length > 0) {\n            return pattern[0].toLowerCase() + pattern.substr(1);\n        }\n        else {\n            // we don't understand its pattern yet.\n            return pattern;\n        }\n    }\n    else {\n        return pattern;\n    }\n}\nfunction validateSpecificSpecialCharacter(matches, pattern, specialCharacter) {\n    const doesContainSpecialCharacter = matches[0].indexOf(specialCharacter) !== -1 && pattern.indexOf(specialCharacter) !== -1;\n    return doesContainSpecialCharacter && matches[0].split(specialCharacter).length === pattern.split(specialCharacter).length;\n}\nfunction buildReplaceStringForSpecificSpecialCharacter(matches, pattern, specialCharacter) {\n    const splitPatternAtSpecialCharacter = pattern.split(specialCharacter);\n    const splitMatchAtSpecialCharacter = matches[0].split(specialCharacter);\n    let replaceString = '';\n    splitPatternAtSpecialCharacter.forEach((splitValue, index) => {\n        replaceString += buildReplaceStringWithCasePreserved([splitMatchAtSpecialCharacter[index]], splitValue) + specialCharacter;\n    });\n    return replaceString.slice(0, -1);\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { buildReplaceStringWithCasePreserved } from '../../../../base/common/search.js';\n/**\n * Assigned when the replace pattern is entirely static.\n */\nclass StaticValueReplacePattern {\n    constructor(staticValue) {\n        this.staticValue = staticValue;\n        this.kind = 0 /* StaticValue */;\n    }\n}\n/**\n * Assigned when the replace pattern has replacement patterns.\n */\nclass DynamicPiecesReplacePattern {\n    constructor(pieces) {\n        this.pieces = pieces;\n        this.kind = 1 /* DynamicPieces */;\n    }\n}\nexport class ReplacePattern {\n    constructor(pieces) {\n        if (!pieces || pieces.length === 0) {\n            this._state = new StaticValueReplacePattern('');\n        }\n        else if (pieces.length === 1 && pieces[0].staticValue !== null) {\n            this._state = new StaticValueReplacePattern(pieces[0].staticValue);\n        }\n        else {\n            this._state = new DynamicPiecesReplacePattern(pieces);\n        }\n    }\n    static fromStaticValue(value) {\n        return new ReplacePattern([ReplacePiece.staticValue(value)]);\n    }\n    get hasReplacementPatterns() {\n        return (this._state.kind === 1 /* DynamicPieces */);\n    }\n    buildReplaceString(matches, preserveCase) {\n        if (this._state.kind === 0 /* StaticValue */) {\n            if (preserveCase) {\n                return buildReplaceStringWithCasePreserved(matches, this._state.staticValue);\n            }\n            else {\n                return this._state.staticValue;\n            }\n        }\n        let result = '';\n        for (let i = 0, len = this._state.pieces.length; i < len; i++) {\n            let piece = this._state.pieces[i];\n            if (piece.staticValue !== null) {\n                // static value ReplacePiece\n                result += piece.staticValue;\n                continue;\n            }\n            // match index ReplacePiece\n            let match = ReplacePattern._substitute(piece.matchIndex, matches);\n            if (piece.caseOps !== null && piece.caseOps.length > 0) {\n                let repl = [];\n                let lenOps = piece.caseOps.length;\n                let opIdx = 0;\n                for (let idx = 0, len = match.length; idx < len; idx++) {\n                    if (opIdx >= lenOps) {\n                        repl.push(match.slice(idx));\n                        break;\n                    }\n                    switch (piece.caseOps[opIdx]) {\n                        case 'U':\n                            repl.push(match[idx].toUpperCase());\n                            break;\n                        case 'u':\n                            repl.push(match[idx].toUpperCase());\n                            opIdx++;\n                            break;\n                        case 'L':\n                            repl.push(match[idx].toLowerCase());\n                            break;\n                        case 'l':\n                            repl.push(match[idx].toLowerCase());\n                            opIdx++;\n                            break;\n                        default:\n                            repl.push(match[idx]);\n                    }\n                }\n                match = repl.join('');\n            }\n            result += match;\n        }\n        return result;\n    }\n    static _substitute(matchIndex, matches) {\n        if (matches === null) {\n            return '';\n        }\n        if (matchIndex === 0) {\n            return matches[0];\n        }\n        let remainder = '';\n        while (matchIndex > 0) {\n            if (matchIndex < matches.length) {\n                // A match can be undefined\n                let match = (matches[matchIndex] || '');\n                return match + remainder;\n            }\n            remainder = String(matchIndex % 10) + remainder;\n            matchIndex = Math.floor(matchIndex / 10);\n        }\n        return '$' + remainder;\n    }\n}\n/**\n * A replace piece can either be a static string or an index to a specific match.\n */\nexport class ReplacePiece {\n    constructor(staticValue, matchIndex, caseOps) {\n        this.staticValue = staticValue;\n        this.matchIndex = matchIndex;\n        if (!caseOps || caseOps.length === 0) {\n            this.caseOps = null;\n        }\n        else {\n            this.caseOps = caseOps.slice(0);\n        }\n    }\n    static staticValue(value) {\n        return new ReplacePiece(value, -1, null);\n    }\n    static caseOps(index, caseOps) {\n        return new ReplacePiece(null, index, caseOps);\n    }\n}\nclass ReplacePieceBuilder {\n    constructor(source) {\n        this._source = source;\n        this._lastCharIndex = 0;\n        this._result = [];\n        this._resultLen = 0;\n        this._currentStaticPiece = '';\n    }\n    emitUnchanged(toCharIndex) {\n        this._emitStatic(this._source.substring(this._lastCharIndex, toCharIndex));\n        this._lastCharIndex = toCharIndex;\n    }\n    emitStatic(value, toCharIndex) {\n        this._emitStatic(value);\n        this._lastCharIndex = toCharIndex;\n    }\n    _emitStatic(value) {\n        if (value.length === 0) {\n            return;\n        }\n        this._currentStaticPiece += value;\n    }\n    emitMatchIndex(index, toCharIndex, caseOps) {\n        if (this._currentStaticPiece.length !== 0) {\n            this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);\n            this._currentStaticPiece = '';\n        }\n        this._result[this._resultLen++] = ReplacePiece.caseOps(index, caseOps);\n        this._lastCharIndex = toCharIndex;\n    }\n    finalize() {\n        this.emitUnchanged(this._source.length);\n        if (this._currentStaticPiece.length !== 0) {\n            this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);\n            this._currentStaticPiece = '';\n        }\n        return new ReplacePattern(this._result);\n    }\n}\n/**\n * \\n\t\t\t=> inserts a LF\n * \\t\t\t\t=> inserts a TAB\n * \\\\\t\t\t=> inserts a \"\\\".\n * \\u\t\t\t=> upper-cases one character in a match.\n * \\U\t\t\t=> upper-cases ALL remaining characters in a match.\n * \\l\t\t\t=> lower-cases one character in a match.\n * \\L\t\t\t=> lower-cases ALL remaining characters in a match.\n * $$\t\t\t=> inserts a \"$\".\n * $& and $0\t=> inserts the matched substring.\n * $n\t\t\t=> Where n is a non-negative integer lesser than 100, inserts the nth parenthesized submatch string\n * everything else stays untouched\n *\n * Also see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace#Specifying_a_string_as_a_parameter\n */\nexport function parseReplaceString(replaceString) {\n    if (!replaceString || replaceString.length === 0) {\n        return new ReplacePattern(null);\n    }\n    let caseOps = [];\n    let result = new ReplacePieceBuilder(replaceString);\n    for (let i = 0, len = replaceString.length; i < len; i++) {\n        let chCode = replaceString.charCodeAt(i);\n        if (chCode === 92 /* Backslash */) {\n            // move to next char\n            i++;\n            if (i >= len) {\n                // string ends with a \\\n                break;\n            }\n            let nextChCode = replaceString.charCodeAt(i);\n            // let replaceWithCharacter: string | null = null;\n            switch (nextChCode) {\n                case 92 /* Backslash */:\n                    // \\\\ => inserts a \"\\\"\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('\\\\', i + 1);\n                    break;\n                case 110 /* n */:\n                    // \\n => inserts a LF\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('\\n', i + 1);\n                    break;\n                case 116 /* t */:\n                    // \\t => inserts a TAB\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('\\t', i + 1);\n                    break;\n                // Case modification of string replacements, patterned after Boost, but only applied\n                // to the replacement text, not subsequent content.\n                case 117 /* u */:\n                // \\u => upper-cases one character.\n                case 85 /* U */:\n                // \\U => upper-cases ALL following characters.\n                case 108 /* l */:\n                // \\l => lower-cases one character.\n                case 76 /* L */:\n                    // \\L => lower-cases ALL following characters.\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('', i + 1);\n                    caseOps.push(String.fromCharCode(nextChCode));\n                    break;\n            }\n            continue;\n        }\n        if (chCode === 36 /* DollarSign */) {\n            // move to next char\n            i++;\n            if (i >= len) {\n                // string ends with a $\n                break;\n            }\n            let nextChCode = replaceString.charCodeAt(i);\n            if (nextChCode === 36 /* DollarSign */) {\n                // $$ => inserts a \"$\"\n                result.emitUnchanged(i - 1);\n                result.emitStatic('$', i + 1);\n                continue;\n            }\n            if (nextChCode === 48 /* Digit0 */ || nextChCode === 38 /* Ampersand */) {\n                // $& and $0 => inserts the matched substring.\n                result.emitUnchanged(i - 1);\n                result.emitMatchIndex(0, i + 1, caseOps);\n                caseOps.length = 0;\n                continue;\n            }\n            if (49 /* Digit1 */ <= nextChCode && nextChCode <= 57 /* Digit9 */) {\n                // $n\n                let matchIndex = nextChCode - 48 /* Digit0 */;\n                // peek next char to probe for $nn\n                if (i + 1 < len) {\n                    let nextNextChCode = replaceString.charCodeAt(i + 1);\n                    if (48 /* Digit0 */ <= nextNextChCode && nextNextChCode <= 57 /* Digit9 */) {\n                        // $nn\n                        // move to next char\n                        i++;\n                        matchIndex = matchIndex * 10 + (nextNextChCode - 48 /* Digit0 */);\n                        result.emitUnchanged(i - 2);\n                        result.emitMatchIndex(matchIndex, i + 1, caseOps);\n                        caseOps.length = 0;\n                        continue;\n                    }\n                }\n                result.emitUnchanged(i - 1);\n                result.emitMatchIndex(matchIndex, i + 1, caseOps);\n                caseOps.length = 0;\n                continue;\n            }\n        }\n    }\n    return result.finalize();\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { findFirstInSorted } from '../../../../base/common/arrays.js';\nimport { RunOnceScheduler, TimeoutTimer } from '../../../../base/common/async.js';\nimport { DisposableStore, dispose } from '../../../../base/common/lifecycle.js';\nimport { ReplaceCommand, ReplaceCommandThatPreservesSelection } from '../../../common/commands/replaceCommand.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { Selection } from '../../../common/core/selection.js';\nimport { SearchParams } from '../../../common/model/textModelSearch.js';\nimport { FindDecorations } from './findDecorations.js';\nimport { ReplaceAllCommand } from './replaceAllCommand.js';\nimport { parseReplaceString, ReplacePattern } from './replacePattern.js';\nimport { RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';\nexport const CONTEXT_FIND_WIDGET_VISIBLE = new RawContextKey('findWidgetVisible', false);\nexport const CONTEXT_FIND_WIDGET_NOT_VISIBLE = CONTEXT_FIND_WIDGET_VISIBLE.toNegated();\n// Keep ContextKey use of 'Focussed' to not break when clauses\nexport const CONTEXT_FIND_INPUT_FOCUSED = new RawContextKey('findInputFocussed', false);\nexport const CONTEXT_REPLACE_INPUT_FOCUSED = new RawContextKey('replaceInputFocussed', false);\nexport const ToggleCaseSensitiveKeybinding = {\n    primary: 512 /* Alt */ | 33 /* KeyC */,\n    mac: { primary: 2048 /* CtrlCmd */ | 512 /* Alt */ | 33 /* KeyC */ }\n};\nexport const ToggleWholeWordKeybinding = {\n    primary: 512 /* Alt */ | 53 /* KeyW */,\n    mac: { primary: 2048 /* CtrlCmd */ | 512 /* Alt */ | 53 /* KeyW */ }\n};\nexport const ToggleRegexKeybinding = {\n    primary: 512 /* Alt */ | 48 /* KeyR */,\n    mac: { primary: 2048 /* CtrlCmd */ | 512 /* Alt */ | 48 /* KeyR */ }\n};\nexport const ToggleSearchScopeKeybinding = {\n    primary: 512 /* Alt */ | 42 /* KeyL */,\n    mac: { primary: 2048 /* CtrlCmd */ | 512 /* Alt */ | 42 /* KeyL */ }\n};\nexport const TogglePreserveCaseKeybinding = {\n    primary: 512 /* Alt */ | 46 /* KeyP */,\n    mac: { primary: 2048 /* CtrlCmd */ | 512 /* Alt */ | 46 /* KeyP */ }\n};\nexport const FIND_IDS = {\n    StartFindAction: 'actions.find',\n    StartFindWithSelection: 'actions.findWithSelection',\n    StartFindWithArgs: 'editor.actions.findWithArgs',\n    NextMatchFindAction: 'editor.action.nextMatchFindAction',\n    PreviousMatchFindAction: 'editor.action.previousMatchFindAction',\n    NextSelectionMatchFindAction: 'editor.action.nextSelectionMatchFindAction',\n    PreviousSelectionMatchFindAction: 'editor.action.previousSelectionMatchFindAction',\n    StartFindReplaceAction: 'editor.action.startFindReplaceAction',\n    CloseFindWidgetCommand: 'closeFindWidget',\n    ToggleCaseSensitiveCommand: 'toggleFindCaseSensitive',\n    ToggleWholeWordCommand: 'toggleFindWholeWord',\n    ToggleRegexCommand: 'toggleFindRegex',\n    ToggleSearchScopeCommand: 'toggleFindInSelection',\n    TogglePreserveCaseCommand: 'togglePreserveCase',\n    ReplaceOneAction: 'editor.action.replaceOne',\n    ReplaceAllAction: 'editor.action.replaceAll',\n    SelectAllMatchesAction: 'editor.action.selectAllMatches'\n};\nexport const MATCHES_LIMIT = 19999;\nconst RESEARCH_DELAY = 240;\nexport class FindModelBoundToEditorModel {\n    constructor(editor, state) {\n        this._toDispose = new DisposableStore();\n        this._editor = editor;\n        this._state = state;\n        this._isDisposed = false;\n        this._startSearchingTimer = new TimeoutTimer();\n        this._decorations = new FindDecorations(editor);\n        this._toDispose.add(this._decorations);\n        this._updateDecorationsScheduler = new RunOnceScheduler(() => this.research(false), 100);\n        this._toDispose.add(this._updateDecorationsScheduler);\n        this._toDispose.add(this._editor.onDidChangeCursorPosition((e) => {\n            if (e.reason === 3 /* Explicit */\n                || e.reason === 5 /* Undo */\n                || e.reason === 6 /* Redo */) {\n                this._decorations.setStartPosition(this._editor.getPosition());\n            }\n        }));\n        this._ignoreModelContentChanged = false;\n        this._toDispose.add(this._editor.onDidChangeModelContent((e) => {\n            if (this._ignoreModelContentChanged) {\n                return;\n            }\n            if (e.isFlush) {\n                // a model.setValue() was called\n                this._decorations.reset();\n            }\n            this._decorations.setStartPosition(this._editor.getPosition());\n            this._updateDecorationsScheduler.schedule();\n        }));\n        this._toDispose.add(this._state.onFindReplaceStateChange((e) => this._onStateChanged(e)));\n        this.research(false, this._state.searchScope);\n    }\n    dispose() {\n        this._isDisposed = true;\n        dispose(this._startSearchingTimer);\n        this._toDispose.dispose();\n    }\n    _onStateChanged(e) {\n        if (this._isDisposed) {\n            // The find model is disposed during a find state changed event\n            return;\n        }\n        if (!this._editor.hasModel()) {\n            // The find model will be disposed momentarily\n            return;\n        }\n        if (e.searchString || e.isReplaceRevealed || e.isRegex || e.wholeWord || e.matchCase || e.searchScope) {\n            let model = this._editor.getModel();\n            if (model.isTooLargeForSyncing()) {\n                this._startSearchingTimer.cancel();\n                this._startSearchingTimer.setIfNotSet(() => {\n                    if (e.searchScope) {\n                        this.research(e.moveCursor, this._state.searchScope);\n                    }\n                    else {\n                        this.research(e.moveCursor);\n                    }\n                }, RESEARCH_DELAY);\n            }\n            else {\n                if (e.searchScope) {\n                    this.research(e.moveCursor, this._state.searchScope);\n                }\n                else {\n                    this.research(e.moveCursor);\n                }\n            }\n        }\n    }\n    static _getSearchRange(model, findScope) {\n        // If we have set now or before a find scope, use it for computing the search range\n        if (findScope) {\n            return findScope;\n        }\n        return model.getFullModelRange();\n    }\n    research(moveCursor, newFindScope) {\n        let findScopes = null;\n        if (typeof newFindScope !== 'undefined') {\n            if (newFindScope !== null) {\n                if (!Array.isArray(newFindScope)) {\n                    findScopes = [newFindScope];\n                }\n                else {\n                    findScopes = newFindScope;\n                }\n            }\n        }\n        else {\n            findScopes = this._decorations.getFindScopes();\n        }\n        if (findScopes !== null) {\n            findScopes = findScopes.map(findScope => {\n                if (findScope.startLineNumber !== findScope.endLineNumber) {\n                    let endLineNumber = findScope.endLineNumber;\n                    if (findScope.endColumn === 1) {\n                        endLineNumber = endLineNumber - 1;\n                    }\n                    return new Range(findScope.startLineNumber, 1, endLineNumber, this._editor.getModel().getLineMaxColumn(endLineNumber));\n                }\n                return findScope;\n            });\n        }\n        let findMatches = this._findMatches(findScopes, false, MATCHES_LIMIT);\n        this._decorations.set(findMatches, findScopes);\n        const editorSelection = this._editor.getSelection();\n        let currentMatchesPosition = this._decorations.getCurrentMatchesPosition(editorSelection);\n        if (currentMatchesPosition === 0 && findMatches.length > 0) {\n            // current selection is not on top of a match\n            // try to find its nearest result from the top of the document\n            const matchAfterSelection = findFirstInSorted(findMatches.map(match => match.range), range => Range.compareRangesUsingStarts(range, editorSelection) >= 0);\n            currentMatchesPosition = matchAfterSelection > 0 ? matchAfterSelection - 1 + 1 /** match position is one based */ : currentMatchesPosition;\n        }\n        this._state.changeMatchInfo(currentMatchesPosition, this._decorations.getCount(), undefined);\n        if (moveCursor && this._editor.getOption(35 /* find */).cursorMoveOnType) {\n            this._moveToNextMatch(this._decorations.getStartPosition());\n        }\n    }\n    _hasMatches() {\n        return (this._state.matchesCount > 0);\n    }\n    _cannotFind() {\n        if (!this._hasMatches()) {\n            let findScope = this._decorations.getFindScope();\n            if (findScope) {\n                // Reveal the selection so user is reminded that 'selection find' is on.\n                this._editor.revealRangeInCenterIfOutsideViewport(findScope, 0 /* Smooth */);\n            }\n            return true;\n        }\n        return false;\n    }\n    _setCurrentFindMatch(match) {\n        let matchesPosition = this._decorations.setCurrentFindMatch(match);\n        this._state.changeMatchInfo(matchesPosition, this._decorations.getCount(), match);\n        this._editor.setSelection(match);\n        this._editor.revealRangeInCenterIfOutsideViewport(match, 0 /* Smooth */);\n    }\n    _prevSearchPosition(before) {\n        let isUsingLineStops = this._state.isRegex && (this._state.searchString.indexOf('^') >= 0\n            || this._state.searchString.indexOf('$') >= 0);\n        let { lineNumber, column } = before;\n        let model = this._editor.getModel();\n        if (isUsingLineStops || column === 1) {\n            if (lineNumber === 1) {\n                lineNumber = model.getLineCount();\n            }\n            else {\n                lineNumber--;\n            }\n            column = model.getLineMaxColumn(lineNumber);\n        }\n        else {\n            column--;\n        }\n        return new Position(lineNumber, column);\n    }\n    _moveToPrevMatch(before, isRecursed = false) {\n        if (!this._state.canNavigateBack()) {\n            // we are beyond the first matched find result\n            // instead of doing nothing, we should refocus the first item\n            const nextMatchRange = this._decorations.matchAfterPosition(before);\n            if (nextMatchRange) {\n                this._setCurrentFindMatch(nextMatchRange);\n            }\n            return;\n        }\n        if (this._decorations.getCount() < MATCHES_LIMIT) {\n            let prevMatchRange = this._decorations.matchBeforePosition(before);\n            if (prevMatchRange && prevMatchRange.isEmpty() && prevMatchRange.getStartPosition().equals(before)) {\n                before = this._prevSearchPosition(before);\n                prevMatchRange = this._decorations.matchBeforePosition(before);\n            }\n            if (prevMatchRange) {\n                this._setCurrentFindMatch(prevMatchRange);\n            }\n            return;\n        }\n        if (this._cannotFind()) {\n            return;\n        }\n        let findScope = this._decorations.getFindScope();\n        let searchRange = FindModelBoundToEditorModel._getSearchRange(this._editor.getModel(), findScope);\n        // ...(----)...|...\n        if (searchRange.getEndPosition().isBefore(before)) {\n            before = searchRange.getEndPosition();\n        }\n        // ...|...(----)...\n        if (before.isBefore(searchRange.getStartPosition())) {\n            before = searchRange.getEndPosition();\n        }\n        let { lineNumber, column } = before;\n        let model = this._editor.getModel();\n        let position = new Position(lineNumber, column);\n        let prevMatch = model.findPreviousMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(117 /* wordSeparators */) : null, false);\n        if (prevMatch && prevMatch.range.isEmpty() && prevMatch.range.getStartPosition().equals(position)) {\n            // Looks like we're stuck at this position, unacceptable!\n            position = this._prevSearchPosition(position);\n            prevMatch = model.findPreviousMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(117 /* wordSeparators */) : null, false);\n        }\n        if (!prevMatch) {\n            // there is precisely one match and selection is on top of it\n            return;\n        }\n        if (!isRecursed && !searchRange.containsRange(prevMatch.range)) {\n            return this._moveToPrevMatch(prevMatch.range.getStartPosition(), true);\n        }\n        this._setCurrentFindMatch(prevMatch.range);\n    }\n    moveToPrevMatch() {\n        this._moveToPrevMatch(this._editor.getSelection().getStartPosition());\n    }\n    _nextSearchPosition(after) {\n        let isUsingLineStops = this._state.isRegex && (this._state.searchString.indexOf('^') >= 0\n            || this._state.searchString.indexOf('$') >= 0);\n        let { lineNumber, column } = after;\n        let model = this._editor.getModel();\n        if (isUsingLineStops || column === model.getLineMaxColumn(lineNumber)) {\n            if (lineNumber === model.getLineCount()) {\n                lineNumber = 1;\n            }\n            else {\n                lineNumber++;\n            }\n            column = 1;\n        }\n        else {\n            column++;\n        }\n        return new Position(lineNumber, column);\n    }\n    _moveToNextMatch(after) {\n        if (!this._state.canNavigateForward()) {\n            // we are beyond the last matched find result\n            // instead of doing nothing, we should refocus the last item\n            const prevMatchRange = this._decorations.matchBeforePosition(after);\n            if (prevMatchRange) {\n                this._setCurrentFindMatch(prevMatchRange);\n            }\n            return;\n        }\n        if (this._decorations.getCount() < MATCHES_LIMIT) {\n            let nextMatchRange = this._decorations.matchAfterPosition(after);\n            if (nextMatchRange && nextMatchRange.isEmpty() && nextMatchRange.getStartPosition().equals(after)) {\n                // Looks like we're stuck at this position, unacceptable!\n                after = this._nextSearchPosition(after);\n                nextMatchRange = this._decorations.matchAfterPosition(after);\n            }\n            if (nextMatchRange) {\n                this._setCurrentFindMatch(nextMatchRange);\n            }\n            return;\n        }\n        let nextMatch = this._getNextMatch(after, false, true);\n        if (nextMatch) {\n            this._setCurrentFindMatch(nextMatch.range);\n        }\n    }\n    _getNextMatch(after, captureMatches, forceMove, isRecursed = false) {\n        if (this._cannotFind()) {\n            return null;\n        }\n        let findScope = this._decorations.getFindScope();\n        let searchRange = FindModelBoundToEditorModel._getSearchRange(this._editor.getModel(), findScope);\n        // ...(----)...|...\n        if (searchRange.getEndPosition().isBefore(after)) {\n            after = searchRange.getStartPosition();\n        }\n        // ...|...(----)...\n        if (after.isBefore(searchRange.getStartPosition())) {\n            after = searchRange.getStartPosition();\n        }\n        let { lineNumber, column } = after;\n        let model = this._editor.getModel();\n        let position = new Position(lineNumber, column);\n        let nextMatch = model.findNextMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(117 /* wordSeparators */) : null, captureMatches);\n        if (forceMove && nextMatch && nextMatch.range.isEmpty() && nextMatch.range.getStartPosition().equals(position)) {\n            // Looks like we're stuck at this position, unacceptable!\n            position = this._nextSearchPosition(position);\n            nextMatch = model.findNextMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(117 /* wordSeparators */) : null, captureMatches);\n        }\n        if (!nextMatch) {\n            // there is precisely one match and selection is on top of it\n            return null;\n        }\n        if (!isRecursed && !searchRange.containsRange(nextMatch.range)) {\n            return this._getNextMatch(nextMatch.range.getEndPosition(), captureMatches, forceMove, true);\n        }\n        return nextMatch;\n    }\n    moveToNextMatch() {\n        this._moveToNextMatch(this._editor.getSelection().getEndPosition());\n    }\n    _getReplacePattern() {\n        if (this._state.isRegex) {\n            return parseReplaceString(this._state.replaceString);\n        }\n        return ReplacePattern.fromStaticValue(this._state.replaceString);\n    }\n    replace() {\n        if (!this._hasMatches()) {\n            return;\n        }\n        let replacePattern = this._getReplacePattern();\n        let selection = this._editor.getSelection();\n        let nextMatch = this._getNextMatch(selection.getStartPosition(), true, false);\n        if (nextMatch) {\n            if (selection.equalsRange(nextMatch.range)) {\n                // selection sits on a find match => replace it!\n                let replaceString = replacePattern.buildReplaceString(nextMatch.matches, this._state.preserveCase);\n                let command = new ReplaceCommand(selection, replaceString);\n                this._executeEditorCommand('replace', command);\n                this._decorations.setStartPosition(new Position(selection.startLineNumber, selection.startColumn + replaceString.length));\n                this.research(true);\n            }\n            else {\n                this._decorations.setStartPosition(this._editor.getPosition());\n                this._setCurrentFindMatch(nextMatch.range);\n            }\n        }\n    }\n    _findMatches(findScopes, captureMatches, limitResultCount) {\n        const searchRanges = (findScopes || [null]).map((scope) => FindModelBoundToEditorModel._getSearchRange(this._editor.getModel(), scope));\n        return this._editor.getModel().findMatches(this._state.searchString, searchRanges, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(117 /* wordSeparators */) : null, captureMatches, limitResultCount);\n    }\n    replaceAll() {\n        if (!this._hasMatches()) {\n            return;\n        }\n        const findScopes = this._decorations.getFindScopes();\n        if (findScopes === null && this._state.matchesCount >= MATCHES_LIMIT) {\n            // Doing a replace on the entire file that is over ${MATCHES_LIMIT} matches\n            this._largeReplaceAll();\n        }\n        else {\n            this._regularReplaceAll(findScopes);\n        }\n        this.research(false);\n    }\n    _largeReplaceAll() {\n        const searchParams = new SearchParams(this._state.searchString, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(117 /* wordSeparators */) : null);\n        const searchData = searchParams.parseSearchRequest();\n        if (!searchData) {\n            return;\n        }\n        let searchRegex = searchData.regex;\n        if (!searchRegex.multiline) {\n            let mod = 'mu';\n            if (searchRegex.ignoreCase) {\n                mod += 'i';\n            }\n            if (searchRegex.global) {\n                mod += 'g';\n            }\n            searchRegex = new RegExp(searchRegex.source, mod);\n        }\n        const model = this._editor.getModel();\n        const modelText = model.getValue(1 /* LF */);\n        const fullModelRange = model.getFullModelRange();\n        const replacePattern = this._getReplacePattern();\n        let resultText;\n        const preserveCase = this._state.preserveCase;\n        if (replacePattern.hasReplacementPatterns || preserveCase) {\n            resultText = modelText.replace(searchRegex, function () {\n                return replacePattern.buildReplaceString(arguments, preserveCase);\n            });\n        }\n        else {\n            resultText = modelText.replace(searchRegex, replacePattern.buildReplaceString(null, preserveCase));\n        }\n        let command = new ReplaceCommandThatPreservesSelection(fullModelRange, resultText, this._editor.getSelection());\n        this._executeEditorCommand('replaceAll', command);\n    }\n    _regularReplaceAll(findScopes) {\n        const replacePattern = this._getReplacePattern();\n        // Get all the ranges (even more than the highlighted ones)\n        let matches = this._findMatches(findScopes, replacePattern.hasReplacementPatterns || this._state.preserveCase, 1073741824 /* MAX_SAFE_SMALL_INTEGER */);\n        let replaceStrings = [];\n        for (let i = 0, len = matches.length; i < len; i++) {\n            replaceStrings[i] = replacePattern.buildReplaceString(matches[i].matches, this._state.preserveCase);\n        }\n        let command = new ReplaceAllCommand(this._editor.getSelection(), matches.map(m => m.range), replaceStrings);\n        this._executeEditorCommand('replaceAll', command);\n    }\n    selectAllMatches() {\n        if (!this._hasMatches()) {\n            return;\n        }\n        let findScopes = this._decorations.getFindScopes();\n        // Get all the ranges (even more than the highlighted ones)\n        let matches = this._findMatches(findScopes, false, 1073741824 /* MAX_SAFE_SMALL_INTEGER */);\n        let selections = matches.map(m => new Selection(m.range.startLineNumber, m.range.startColumn, m.range.endLineNumber, m.range.endColumn));\n        // If one of the ranges is the editor selection, then maintain it as primary\n        let editorSelection = this._editor.getSelection();\n        for (let i = 0, len = selections.length; i < len; i++) {\n            let sel = selections[i];\n            if (sel.equalsRange(editorSelection)) {\n                selections = [editorSelection].concat(selections.slice(0, i)).concat(selections.slice(i + 1));\n                break;\n            }\n        }\n        this._editor.setSelections(selections);\n    }\n    _executeEditorCommand(source, command) {\n        try {\n            this._ignoreModelContentChanged = true;\n            this._editor.pushUndoStop();\n            this._editor.executeCommand(source, command);\n            this._editor.pushUndoStop();\n        }\n        finally {\n            this._ignoreModelContentChanged = false;\n        }\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Widget } from '../widget.js';\nimport { CSSIcon } from '../../../common/codicons.js';\nimport { Color } from '../../../common/color.js';\nimport { Emitter } from '../../../common/event.js';\nimport './checkbox.css';\nconst defaultOpts = {\n    inputActiveOptionBorder: Color.fromHex('#007ACC00'),\n    inputActiveOptionForeground: Color.fromHex('#FFFFFF'),\n    inputActiveOptionBackground: Color.fromHex('#0E639C50')\n};\nexport class Checkbox extends Widget {\n    constructor(opts) {\n        super();\n        this._onChange = this._register(new Emitter());\n        this.onChange = this._onChange.event;\n        this._onKeyDown = this._register(new Emitter());\n        this.onKeyDown = this._onKeyDown.event;\n        this._opts = Object.assign(Object.assign({}, defaultOpts), opts);\n        this._checked = this._opts.isChecked;\n        const classes = ['monaco-custom-checkbox'];\n        if (this._opts.icon) {\n            classes.push(...CSSIcon.asClassNameArray(this._opts.icon));\n        }\n        if (this._opts.actionClassName) {\n            classes.push(...this._opts.actionClassName.split(' '));\n        }\n        if (this._checked) {\n            classes.push('checked');\n        }\n        this.domNode = document.createElement('div');\n        this.domNode.title = this._opts.title;\n        this.domNode.classList.add(...classes);\n        if (!this._opts.notFocusable) {\n            this.domNode.tabIndex = 0;\n        }\n        this.domNode.setAttribute('role', 'checkbox');\n        this.domNode.setAttribute('aria-checked', String(this._checked));\n        this.domNode.setAttribute('aria-label', this._opts.title);\n        this.applyStyles();\n        this.onclick(this.domNode, (ev) => {\n            if (this.enabled) {\n                this.checked = !this._checked;\n                this._onChange.fire(false);\n                ev.preventDefault();\n            }\n        });\n        this.ignoreGesture(this.domNode);\n        this.onkeydown(this.domNode, (keyboardEvent) => {\n            if (keyboardEvent.keyCode === 10 /* Space */ || keyboardEvent.keyCode === 3 /* Enter */) {\n                this.checked = !this._checked;\n                this._onChange.fire(true);\n                keyboardEvent.preventDefault();\n                return;\n            }\n            this._onKeyDown.fire(keyboardEvent);\n        });\n    }\n    get enabled() {\n        return this.domNode.getAttribute('aria-disabled') !== 'true';\n    }\n    focus() {\n        this.domNode.focus();\n    }\n    get checked() {\n        return this._checked;\n    }\n    set checked(newIsChecked) {\n        this._checked = newIsChecked;\n        this.domNode.setAttribute('aria-checked', String(this._checked));\n        this.domNode.classList.toggle('checked', this._checked);\n        this.applyStyles();\n    }\n    width() {\n        return 2 /*margin left*/ + 2 /*border*/ + 2 /*padding*/ + 16 /* icon width */;\n    }\n    style(styles) {\n        if (styles.inputActiveOptionBorder) {\n            this._opts.inputActiveOptionBorder = styles.inputActiveOptionBorder;\n        }\n        if (styles.inputActiveOptionForeground) {\n            this._opts.inputActiveOptionForeground = styles.inputActiveOptionForeground;\n        }\n        if (styles.inputActiveOptionBackground) {\n            this._opts.inputActiveOptionBackground = styles.inputActiveOptionBackground;\n        }\n        this.applyStyles();\n    }\n    applyStyles() {\n        if (this.domNode) {\n            this.domNode.style.borderColor = this._checked && this._opts.inputActiveOptionBorder ? this._opts.inputActiveOptionBorder.toString() : '';\n            this.domNode.style.color = this._checked && this._opts.inputActiveOptionForeground ? this._opts.inputActiveOptionForeground.toString() : 'inherit';\n            this.domNode.style.backgroundColor = this._checked && this._opts.inputActiveOptionBackground ? this._opts.inputActiveOptionBackground.toString() : '';\n        }\n    }\n    enable() {\n        this.domNode.setAttribute('aria-disabled', String(false));\n    }\n    disable() {\n        this.domNode.setAttribute('aria-disabled', String(true));\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Checkbox } from '../checkbox/checkbox.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport * as nls from '../../../../nls.js';\nconst NLS_CASE_SENSITIVE_CHECKBOX_LABEL = nls.localize('caseDescription', \"Match Case\");\nconst NLS_WHOLE_WORD_CHECKBOX_LABEL = nls.localize('wordsDescription', \"Match Whole Word\");\nconst NLS_REGEX_CHECKBOX_LABEL = nls.localize('regexDescription', \"Use Regular Expression\");\nexport class CaseSensitiveCheckbox extends Checkbox {\n    constructor(opts) {\n        super({\n            icon: Codicon.caseSensitive,\n            title: NLS_CASE_SENSITIVE_CHECKBOX_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\nexport class WholeWordsCheckbox extends Checkbox {\n    constructor(opts) {\n        super({\n            icon: Codicon.wholeWord,\n            title: NLS_WHOLE_WORD_CHECKBOX_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\nexport class RegexCheckbox extends Checkbox {\n    constructor(opts) {\n        super({\n            icon: Codicon.regex,\n            title: NLS_REGEX_CHECKBOX_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { CaseSensitiveCheckbox, RegexCheckbox, WholeWordsCheckbox } from '../../../../base/browser/ui/findinput/findInputCheckboxes.js';\nimport { Widget } from '../../../../base/browser/ui/widget.js';\nimport { RunOnceScheduler } from '../../../../base/common/async.js';\nimport { FIND_IDS } from './findModel.js';\nimport { contrastBorder, editorWidgetBackground, editorWidgetForeground, inputActiveOptionBackground, inputActiveOptionBorder, inputActiveOptionForeground, widgetShadow } from '../../../../platform/theme/common/colorRegistry.js';\nimport { registerThemingParticipant } from '../../../../platform/theme/common/themeService.js';\nexport class FindOptionsWidget extends Widget {\n    constructor(editor, state, keybindingService, themeService) {\n        super();\n        this._hideSoon = this._register(new RunOnceScheduler(() => this._hide(), 2000));\n        this._isVisible = false;\n        this._editor = editor;\n        this._state = state;\n        this._keybindingService = keybindingService;\n        this._domNode = document.createElement('div');\n        this._domNode.className = 'findOptionsWidget';\n        this._domNode.style.display = 'none';\n        this._domNode.style.top = '10px';\n        this._domNode.setAttribute('role', 'presentation');\n        this._domNode.setAttribute('aria-hidden', 'true');\n        const inputActiveOptionBorderColor = themeService.getColorTheme().getColor(inputActiveOptionBorder);\n        const inputActiveOptionForegroundColor = themeService.getColorTheme().getColor(inputActiveOptionForeground);\n        const inputActiveOptionBackgroundColor = themeService.getColorTheme().getColor(inputActiveOptionBackground);\n        this.caseSensitive = this._register(new CaseSensitiveCheckbox({\n            appendTitle: this._keybindingLabelFor(FIND_IDS.ToggleCaseSensitiveCommand),\n            isChecked: this._state.matchCase,\n            inputActiveOptionBorder: inputActiveOptionBorderColor,\n            inputActiveOptionForeground: inputActiveOptionForegroundColor,\n            inputActiveOptionBackground: inputActiveOptionBackgroundColor\n        }));\n        this._domNode.appendChild(this.caseSensitive.domNode);\n        this._register(this.caseSensitive.onChange(() => {\n            this._state.change({\n                matchCase: this.caseSensitive.checked\n            }, false);\n        }));\n        this.wholeWords = this._register(new WholeWordsCheckbox({\n            appendTitle: this._keybindingLabelFor(FIND_IDS.ToggleWholeWordCommand),\n            isChecked: this._state.wholeWord,\n            inputActiveOptionBorder: inputActiveOptionBorderColor,\n            inputActiveOptionForeground: inputActiveOptionForegroundColor,\n            inputActiveOptionBackground: inputActiveOptionBackgroundColor\n        }));\n        this._domNode.appendChild(this.wholeWords.domNode);\n        this._register(this.wholeWords.onChange(() => {\n            this._state.change({\n                wholeWord: this.wholeWords.checked\n            }, false);\n        }));\n        this.regex = this._register(new RegexCheckbox({\n            appendTitle: this._keybindingLabelFor(FIND_IDS.ToggleRegexCommand),\n            isChecked: this._state.isRegex,\n            inputActiveOptionBorder: inputActiveOptionBorderColor,\n            inputActiveOptionForeground: inputActiveOptionForegroundColor,\n            inputActiveOptionBackground: inputActiveOptionBackgroundColor\n        }));\n        this._domNode.appendChild(this.regex.domNode);\n        this._register(this.regex.onChange(() => {\n            this._state.change({\n                isRegex: this.regex.checked\n            }, false);\n        }));\n        this._editor.addOverlayWidget(this);\n        this._register(this._state.onFindReplaceStateChange((e) => {\n            let somethingChanged = false;\n            if (e.isRegex) {\n                this.regex.checked = this._state.isRegex;\n                somethingChanged = true;\n            }\n            if (e.wholeWord) {\n                this.wholeWords.checked = this._state.wholeWord;\n                somethingChanged = true;\n            }\n            if (e.matchCase) {\n                this.caseSensitive.checked = this._state.matchCase;\n                somethingChanged = true;\n            }\n            if (!this._state.isRevealed && somethingChanged) {\n                this._revealTemporarily();\n            }\n        }));\n        this._register(dom.addDisposableNonBubblingMouseOutListener(this._domNode, (e) => this._onMouseOut()));\n        this._register(dom.addDisposableListener(this._domNode, 'mouseover', (e) => this._onMouseOver()));\n        this._applyTheme(themeService.getColorTheme());\n        this._register(themeService.onDidColorThemeChange(this._applyTheme.bind(this)));\n    }\n    _keybindingLabelFor(actionId) {\n        let kb = this._keybindingService.lookupKeybinding(actionId);\n        if (!kb) {\n            return '';\n        }\n        return ` (${kb.getLabel()})`;\n    }\n    dispose() {\n        this._editor.removeOverlayWidget(this);\n        super.dispose();\n    }\n    // ----- IOverlayWidget API\n    getId() {\n        return FindOptionsWidget.ID;\n    }\n    getDomNode() {\n        return this._domNode;\n    }\n    getPosition() {\n        return {\n            preference: 0 /* TOP_RIGHT_CORNER */\n        };\n    }\n    highlightFindOptions() {\n        this._revealTemporarily();\n    }\n    _revealTemporarily() {\n        this._show();\n        this._hideSoon.schedule();\n    }\n    _onMouseOut() {\n        this._hideSoon.schedule();\n    }\n    _onMouseOver() {\n        this._hideSoon.cancel();\n    }\n    _show() {\n        if (this._isVisible) {\n            return;\n        }\n        this._isVisible = true;\n        this._domNode.style.display = 'block';\n    }\n    _hide() {\n        if (!this._isVisible) {\n            return;\n        }\n        this._isVisible = false;\n        this._domNode.style.display = 'none';\n    }\n    _applyTheme(theme) {\n        let inputStyles = {\n            inputActiveOptionBorder: theme.getColor(inputActiveOptionBorder),\n            inputActiveOptionForeground: theme.getColor(inputActiveOptionForeground),\n            inputActiveOptionBackground: theme.getColor(inputActiveOptionBackground)\n        };\n        this.caseSensitive.style(inputStyles);\n        this.wholeWords.style(inputStyles);\n        this.regex.style(inputStyles);\n    }\n}\nFindOptionsWidget.ID = 'editor.contrib.findOptionsWidget';\nregisterThemingParticipant((theme, collector) => {\n    const widgetBackground = theme.getColor(editorWidgetBackground);\n    if (widgetBackground) {\n        collector.addRule(`.monaco-editor .findOptionsWidget { background-color: ${widgetBackground}; }`);\n    }\n    const widgetForeground = theme.getColor(editorWidgetForeground);\n    if (widgetForeground) {\n        collector.addRule(`.monaco-editor .findOptionsWidget { color: ${widgetForeground}; }`);\n    }\n    const widgetShadowColor = theme.getColor(widgetShadow);\n    if (widgetShadowColor) {\n        collector.addRule(`.monaco-editor .findOptionsWidget { box-shadow: 0 0 8px 2px ${widgetShadowColor}; }`);\n    }\n    const hcBorder = theme.getColor(contrastBorder);\n    if (hcBorder) {\n        collector.addRule(`.monaco-editor .findOptionsWidget { border: 2px solid ${hcBorder}; }`);\n    }\n});\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { Range } from '../../../common/core/range.js';\nimport { MATCHES_LIMIT } from './findModel.js';\nfunction effectiveOptionValue(override, value) {\n    if (override === 1 /* True */) {\n        return true;\n    }\n    if (override === 2 /* False */) {\n        return false;\n    }\n    return value;\n}\nexport class FindReplaceState extends Disposable {\n    constructor() {\n        super();\n        this._onFindReplaceStateChange = this._register(new Emitter());\n        this.onFindReplaceStateChange = this._onFindReplaceStateChange.event;\n        this._searchString = '';\n        this._replaceString = '';\n        this._isRevealed = false;\n        this._isReplaceRevealed = false;\n        this._isRegex = false;\n        this._isRegexOverride = 0 /* NotSet */;\n        this._wholeWord = false;\n        this._wholeWordOverride = 0 /* NotSet */;\n        this._matchCase = false;\n        this._matchCaseOverride = 0 /* NotSet */;\n        this._preserveCase = false;\n        this._preserveCaseOverride = 0 /* NotSet */;\n        this._searchScope = null;\n        this._matchesPosition = 0;\n        this._matchesCount = 0;\n        this._currentMatch = null;\n        this._loop = true;\n        this._isSearching = false;\n        this._filters = null;\n    }\n    get searchString() { return this._searchString; }\n    get replaceString() { return this._replaceString; }\n    get isRevealed() { return this._isRevealed; }\n    get isReplaceRevealed() { return this._isReplaceRevealed; }\n    get isRegex() { return effectiveOptionValue(this._isRegexOverride, this._isRegex); }\n    get wholeWord() { return effectiveOptionValue(this._wholeWordOverride, this._wholeWord); }\n    get matchCase() { return effectiveOptionValue(this._matchCaseOverride, this._matchCase); }\n    get preserveCase() { return effectiveOptionValue(this._preserveCaseOverride, this._preserveCase); }\n    get actualIsRegex() { return this._isRegex; }\n    get actualWholeWord() { return this._wholeWord; }\n    get actualMatchCase() { return this._matchCase; }\n    get actualPreserveCase() { return this._preserveCase; }\n    get searchScope() { return this._searchScope; }\n    get matchesPosition() { return this._matchesPosition; }\n    get matchesCount() { return this._matchesCount; }\n    get currentMatch() { return this._currentMatch; }\n    changeMatchInfo(matchesPosition, matchesCount, currentMatch) {\n        let changeEvent = {\n            moveCursor: false,\n            updateHistory: false,\n            searchString: false,\n            replaceString: false,\n            isRevealed: false,\n            isReplaceRevealed: false,\n            isRegex: false,\n            wholeWord: false,\n            matchCase: false,\n            preserveCase: false,\n            searchScope: false,\n            matchesPosition: false,\n            matchesCount: false,\n            currentMatch: false,\n            loop: false,\n            isSearching: false,\n            filters: false\n        };\n        let somethingChanged = false;\n        if (matchesCount === 0) {\n            matchesPosition = 0;\n        }\n        if (matchesPosition > matchesCount) {\n            matchesPosition = matchesCount;\n        }\n        if (this._matchesPosition !== matchesPosition) {\n            this._matchesPosition = matchesPosition;\n            changeEvent.matchesPosition = true;\n            somethingChanged = true;\n        }\n        if (this._matchesCount !== matchesCount) {\n            this._matchesCount = matchesCount;\n            changeEvent.matchesCount = true;\n            somethingChanged = true;\n        }\n        if (typeof currentMatch !== 'undefined') {\n            if (!Range.equalsRange(this._currentMatch, currentMatch)) {\n                this._currentMatch = currentMatch;\n                changeEvent.currentMatch = true;\n                somethingChanged = true;\n            }\n        }\n        if (somethingChanged) {\n            this._onFindReplaceStateChange.fire(changeEvent);\n        }\n    }\n    change(newState, moveCursor, updateHistory = true) {\n        var _a;\n        let changeEvent = {\n            moveCursor: moveCursor,\n            updateHistory: updateHistory,\n            searchString: false,\n            replaceString: false,\n            isRevealed: false,\n            isReplaceRevealed: false,\n            isRegex: false,\n            wholeWord: false,\n            matchCase: false,\n            preserveCase: false,\n            searchScope: false,\n            matchesPosition: false,\n            matchesCount: false,\n            currentMatch: false,\n            loop: false,\n            isSearching: false,\n            filters: false\n        };\n        let somethingChanged = false;\n        const oldEffectiveIsRegex = this.isRegex;\n        const oldEffectiveWholeWords = this.wholeWord;\n        const oldEffectiveMatchCase = this.matchCase;\n        const oldEffectivePreserveCase = this.preserveCase;\n        if (typeof newState.searchString !== 'undefined') {\n            if (this._searchString !== newState.searchString) {\n                this._searchString = newState.searchString;\n                changeEvent.searchString = true;\n                somethingChanged = true;\n            }\n        }\n        if (typeof newState.replaceString !== 'undefined') {\n            if (this._replaceString !== newState.replaceString) {\n                this._replaceString = newState.replaceString;\n                changeEvent.replaceString = true;\n                somethingChanged = true;\n            }\n        }\n        if (typeof newState.isRevealed !== 'undefined') {\n            if (this._isRevealed !== newState.isRevealed) {\n                this._isRevealed = newState.isRevealed;\n                changeEvent.isRevealed = true;\n                somethingChanged = true;\n            }\n        }\n        if (typeof newState.isReplaceRevealed !== 'undefined') {\n            if (this._isReplaceRevealed !== newState.isReplaceRevealed) {\n                this._isReplaceRevealed = newState.isReplaceRevealed;\n                changeEvent.isReplaceRevealed = true;\n                somethingChanged = true;\n            }\n        }\n        if (typeof newState.isRegex !== 'undefined') {\n            this._isRegex = newState.isRegex;\n        }\n        if (typeof newState.wholeWord !== 'undefined') {\n            this._wholeWord = newState.wholeWord;\n        }\n        if (typeof newState.matchCase !== 'undefined') {\n            this._matchCase = newState.matchCase;\n        }\n        if (typeof newState.preserveCase !== 'undefined') {\n            this._preserveCase = newState.preserveCase;\n        }\n        if (typeof newState.searchScope !== 'undefined') {\n            if (!((_a = newState.searchScope) === null || _a === void 0 ? void 0 : _a.every((newSearchScope) => {\n                var _a;\n                return (_a = this._searchScope) === null || _a === void 0 ? void 0 : _a.some(existingSearchScope => {\n                    return !Range.equalsRange(existingSearchScope, newSearchScope);\n                });\n            }))) {\n                this._searchScope = newState.searchScope;\n                changeEvent.searchScope = true;\n                somethingChanged = true;\n            }\n        }\n        if (typeof newState.loop !== 'undefined') {\n            if (this._loop !== newState.loop) {\n                this._loop = newState.loop;\n                changeEvent.loop = true;\n                somethingChanged = true;\n            }\n        }\n        if (typeof newState.isSearching !== 'undefined') {\n            if (this._isSearching !== newState.isSearching) {\n                this._isSearching = newState.isSearching;\n                changeEvent.isSearching = true;\n                somethingChanged = true;\n            }\n        }\n        if (typeof newState.filters !== 'undefined') {\n            if (this._filters) {\n                this._filters.update(newState.filters);\n            }\n            else {\n                this._filters = newState.filters;\n            }\n            changeEvent.filters = true;\n            somethingChanged = true;\n        }\n        // Overrides get set when they explicitly come in and get reset anytime something else changes\n        this._isRegexOverride = (typeof newState.isRegexOverride !== 'undefined' ? newState.isRegexOverride : 0 /* NotSet */);\n        this._wholeWordOverride = (typeof newState.wholeWordOverride !== 'undefined' ? newState.wholeWordOverride : 0 /* NotSet */);\n        this._matchCaseOverride = (typeof newState.matchCaseOverride !== 'undefined' ? newState.matchCaseOverride : 0 /* NotSet */);\n        this._preserveCaseOverride = (typeof newState.preserveCaseOverride !== 'undefined' ? newState.preserveCaseOverride : 0 /* NotSet */);\n        if (oldEffectiveIsRegex !== this.isRegex) {\n            somethingChanged = true;\n            changeEvent.isRegex = true;\n        }\n        if (oldEffectiveWholeWords !== this.wholeWord) {\n            somethingChanged = true;\n            changeEvent.wholeWord = true;\n        }\n        if (oldEffectiveMatchCase !== this.matchCase) {\n            somethingChanged = true;\n            changeEvent.matchCase = true;\n        }\n        if (oldEffectivePreserveCase !== this.preserveCase) {\n            somethingChanged = true;\n            changeEvent.preserveCase = true;\n        }\n        if (somethingChanged) {\n            this._onFindReplaceStateChange.fire(changeEvent);\n        }\n    }\n    canNavigateBack() {\n        return this.canNavigateInLoop() || (this.matchesPosition !== 1);\n    }\n    canNavigateForward() {\n        return this.canNavigateInLoop() || (this.matchesPosition < this.matchesCount);\n    }\n    canNavigateInLoop() {\n        return this._loop || (this.matchesCount >= MATCHES_LIMIT);\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport * as dom from '../../../../base/browser/dom.js';\nimport { alert as alertFn } from '../../../../base/browser/ui/aria/aria.js';\nimport { Checkbox } from '../../../../base/browser/ui/checkbox/checkbox.js';\nimport { Sash } from '../../../../base/browser/ui/sash/sash.js';\nimport { Widget } from '../../../../base/browser/ui/widget.js';\nimport { Delayer } from '../../../../base/common/async.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { onUnexpectedError } from '../../../../base/common/errors.js';\nimport { toDisposable } from '../../../../base/common/lifecycle.js';\nimport * as platform from '../../../../base/common/platform.js';\nimport * as strings from '../../../../base/common/strings.js';\nimport './findWidget.css';\nimport { Range } from '../../../common/core/range.js';\nimport { CONTEXT_FIND_INPUT_FOCUSED, CONTEXT_REPLACE_INPUT_FOCUSED, FIND_IDS, MATCHES_LIMIT } from './findModel.js';\nimport * as nls from '../../../../nls.js';\nimport { ContextScopedFindInput, ContextScopedReplaceInput } from '../../../../platform/history/browser/contextScopedHistoryWidget.js';\nimport { showHistoryKeybindingHint } from '../../../../platform/history/browser/historyWidgetKeybindingHint.js';\nimport { contrastBorder, editorFindMatch, editorFindMatchBorder, editorFindMatchHighlight, editorFindMatchHighlightBorder, editorFindRangeHighlight, editorFindRangeHighlightBorder, editorWidgetBackground, editorWidgetBorder, editorWidgetForeground, editorWidgetResizeBorder, errorForeground, focusBorder, inputActiveOptionBackground, inputActiveOptionBorder, inputActiveOptionForeground, inputBackground, inputBorder, inputForeground, inputValidationErrorBackground, inputValidationErrorBorder, inputValidationErrorForeground, inputValidationInfoBackground, inputValidationInfoBorder, inputValidationInfoForeground, inputValidationWarningBackground, inputValidationWarningBorder, inputValidationWarningForeground, toolbarHoverBackground, widgetShadow } from '../../../../platform/theme/common/colorRegistry.js';\nimport { registerIcon, widgetClose } from '../../../../platform/theme/common/iconRegistry.js';\nimport { registerThemingParticipant, ThemeIcon } from '../../../../platform/theme/common/themeService.js';\nconst findSelectionIcon = registerIcon('find-selection', Codicon.selection, nls.localize('findSelectionIcon', 'Icon for \\'Find in Selection\\' in the editor find widget.'));\nconst findCollapsedIcon = registerIcon('find-collapsed', Codicon.chevronRight, nls.localize('findCollapsedIcon', 'Icon to indicate that the editor find widget is collapsed.'));\nconst findExpandedIcon = registerIcon('find-expanded', Codicon.chevronDown, nls.localize('findExpandedIcon', 'Icon to indicate that the editor find widget is expanded.'));\nexport const findReplaceIcon = registerIcon('find-replace', Codicon.replace, nls.localize('findReplaceIcon', 'Icon for \\'Replace\\' in the editor find widget.'));\nexport const findReplaceAllIcon = registerIcon('find-replace-all', Codicon.replaceAll, nls.localize('findReplaceAllIcon', 'Icon for \\'Replace All\\' in the editor find widget.'));\nexport const findPreviousMatchIcon = registerIcon('find-previous-match', Codicon.arrowUp, nls.localize('findPreviousMatchIcon', 'Icon for \\'Find Previous\\' in the editor find widget.'));\nexport const findNextMatchIcon = registerIcon('find-next-match', Codicon.arrowDown, nls.localize('findNextMatchIcon', 'Icon for \\'Find Next\\' in the editor find widget.'));\nconst NLS_FIND_INPUT_LABEL = nls.localize('label.find', \"Find\");\nconst NLS_FIND_INPUT_PLACEHOLDER = nls.localize('placeholder.find', \"Find\");\nconst NLS_PREVIOUS_MATCH_BTN_LABEL = nls.localize('label.previousMatchButton', \"Previous Match\");\nconst NLS_NEXT_MATCH_BTN_LABEL = nls.localize('label.nextMatchButton', \"Next Match\");\nconst NLS_TOGGLE_SELECTION_FIND_TITLE = nls.localize('label.toggleSelectionFind', \"Find in Selection\");\nconst NLS_CLOSE_BTN_LABEL = nls.localize('label.closeButton', \"Close\");\nconst NLS_REPLACE_INPUT_LABEL = nls.localize('label.replace', \"Replace\");\nconst NLS_REPLACE_INPUT_PLACEHOLDER = nls.localize('placeholder.replace', \"Replace\");\nconst NLS_REPLACE_BTN_LABEL = nls.localize('label.replaceButton', \"Replace\");\nconst NLS_REPLACE_ALL_BTN_LABEL = nls.localize('label.replaceAllButton', \"Replace All\");\nconst NLS_TOGGLE_REPLACE_MODE_BTN_LABEL = nls.localize('label.toggleReplaceButton', \"Toggle Replace\");\nconst NLS_MATCHES_COUNT_LIMIT_TITLE = nls.localize('title.matchesCountLimit', \"Only the first {0} results are highlighted, but all find operations work on the entire text.\", MATCHES_LIMIT);\nexport const NLS_MATCHES_LOCATION = nls.localize('label.matchesLocation', \"{0} of {1}\");\nexport const NLS_NO_RESULTS = nls.localize('label.noResults', \"No results\");\nconst FIND_WIDGET_INITIAL_WIDTH = 419;\nconst PART_WIDTH = 275;\nconst FIND_INPUT_AREA_WIDTH = PART_WIDTH - 54;\nlet MAX_MATCHES_COUNT_WIDTH = 69;\n// let FIND_ALL_CONTROLS_WIDTH = 17/** Find Input margin-left */ + (MAX_MATCHES_COUNT_WIDTH + 3 + 1) /** Match Results */ + 23 /** Button */ * 4 + 2/** sash */;\nconst FIND_INPUT_AREA_HEIGHT = 33; // The height of Find Widget when Replace Input is not visible.\nconst ctrlEnterReplaceAllWarningPromptedKey = 'ctrlEnterReplaceAll.windows.donotask';\nconst ctrlKeyMod = (platform.isMacintosh ? 256 /* WinCtrl */ : 2048 /* CtrlCmd */);\nexport class FindWidgetViewZone {\n    constructor(afterLineNumber) {\n        this.afterLineNumber = afterLineNumber;\n        this.heightInPx = FIND_INPUT_AREA_HEIGHT;\n        this.suppressMouseDown = false;\n        this.domNode = document.createElement('div');\n        this.domNode.className = 'dock-find-viewzone';\n    }\n}\nfunction stopPropagationForMultiLineUpwards(event, value, textarea) {\n    const isMultiline = !!value.match(/\\n/);\n    if (textarea && isMultiline && textarea.selectionStart > 0) {\n        event.stopPropagation();\n        return;\n    }\n}\nfunction stopPropagationForMultiLineDownwards(event, value, textarea) {\n    const isMultiline = !!value.match(/\\n/);\n    if (textarea && isMultiline && textarea.selectionEnd < textarea.value.length) {\n        event.stopPropagation();\n        return;\n    }\n}\nexport class FindWidget extends Widget {\n    constructor(codeEditor, controller, state, contextViewProvider, keybindingService, contextKeyService, themeService, storageService, notificationService) {\n        super();\n        this._cachedHeight = null;\n        this._revealTimeouts = [];\n        this._codeEditor = codeEditor;\n        this._controller = controller;\n        this._state = state;\n        this._contextViewProvider = contextViewProvider;\n        this._keybindingService = keybindingService;\n        this._contextKeyService = contextKeyService;\n        this._storageService = storageService;\n        this._notificationService = notificationService;\n        this._ctrlEnterReplaceAllWarningPrompted = !!storageService.getBoolean(ctrlEnterReplaceAllWarningPromptedKey, 0 /* GLOBAL */);\n        this._isVisible = false;\n        this._isReplaceVisible = false;\n        this._ignoreChangeEvent = false;\n        this._updateHistoryDelayer = new Delayer(500);\n        this._register(toDisposable(() => this._updateHistoryDelayer.cancel()));\n        this._register(this._state.onFindReplaceStateChange((e) => this._onStateChanged(e)));\n        this._buildDomNode();\n        this._updateButtons();\n        this._tryUpdateWidgetWidth();\n        this._findInput.inputBox.layout();\n        this._register(this._codeEditor.onDidChangeConfiguration((e) => {\n            if (e.hasChanged(81 /* readOnly */)) {\n                if (this._codeEditor.getOption(81 /* readOnly */)) {\n                    // Hide replace part if editor becomes read only\n                    this._state.change({ isReplaceRevealed: false }, false);\n                }\n                this._updateButtons();\n            }\n            if (e.hasChanged(131 /* layoutInfo */)) {\n                this._tryUpdateWidgetWidth();\n            }\n            if (e.hasChanged(2 /* accessibilitySupport */)) {\n                this.updateAccessibilitySupport();\n            }\n            if (e.hasChanged(35 /* find */)) {\n                const addExtraSpaceOnTop = this._codeEditor.getOption(35 /* find */).addExtraSpaceOnTop;\n                if (addExtraSpaceOnTop && !this._viewZone) {\n                    this._viewZone = new FindWidgetViewZone(0);\n                    this._showViewZone();\n                }\n                if (!addExtraSpaceOnTop && this._viewZone) {\n                    this._removeViewZone();\n                }\n            }\n        }));\n        this.updateAccessibilitySupport();\n        this._register(this._codeEditor.onDidChangeCursorSelection(() => {\n            if (this._isVisible) {\n                this._updateToggleSelectionFindButton();\n            }\n        }));\n        this._register(this._codeEditor.onDidFocusEditorWidget(() => __awaiter(this, void 0, void 0, function* () {\n            if (this._isVisible) {\n                let globalBufferTerm = yield this._controller.getGlobalBufferTerm();\n                if (globalBufferTerm && globalBufferTerm !== this._state.searchString) {\n                    this._state.change({ searchString: globalBufferTerm }, false);\n                    this._findInput.select();\n                }\n            }\n        })));\n        this._findInputFocused = CONTEXT_FIND_INPUT_FOCUSED.bindTo(contextKeyService);\n        this._findFocusTracker = this._register(dom.trackFocus(this._findInput.inputBox.inputElement));\n        this._register(this._findFocusTracker.onDidFocus(() => {\n            this._findInputFocused.set(true);\n            this._updateSearchScope();\n        }));\n        this._register(this._findFocusTracker.onDidBlur(() => {\n            this._findInputFocused.set(false);\n        }));\n        this._replaceInputFocused = CONTEXT_REPLACE_INPUT_FOCUSED.bindTo(contextKeyService);\n        this._replaceFocusTracker = this._register(dom.trackFocus(this._replaceInput.inputBox.inputElement));\n        this._register(this._replaceFocusTracker.onDidFocus(() => {\n            this._replaceInputFocused.set(true);\n            this._updateSearchScope();\n        }));\n        this._register(this._replaceFocusTracker.onDidBlur(() => {\n            this._replaceInputFocused.set(false);\n        }));\n        this._codeEditor.addOverlayWidget(this);\n        if (this._codeEditor.getOption(35 /* find */).addExtraSpaceOnTop) {\n            this._viewZone = new FindWidgetViewZone(0); // Put it before the first line then users can scroll beyond the first line.\n        }\n        this._applyTheme(themeService.getColorTheme());\n        this._register(themeService.onDidColorThemeChange(this._applyTheme.bind(this)));\n        this._register(this._codeEditor.onDidChangeModel(() => {\n            if (!this._isVisible) {\n                return;\n            }\n            this._viewZoneId = undefined;\n        }));\n        this._register(this._codeEditor.onDidScrollChange((e) => {\n            if (e.scrollTopChanged) {\n                this._layoutViewZone();\n                return;\n            }\n            // for other scroll changes, layout the viewzone in next tick to avoid ruining current rendering.\n            setTimeout(() => {\n                this._layoutViewZone();\n            }, 0);\n        }));\n    }\n    // ----- IOverlayWidget API\n    getId() {\n        return FindWidget.ID;\n    }\n    getDomNode() {\n        return this._domNode;\n    }\n    getPosition() {\n        if (this._isVisible) {\n            return {\n                preference: 0 /* TOP_RIGHT_CORNER */\n            };\n        }\n        return null;\n    }\n    // ----- React to state changes\n    _onStateChanged(e) {\n        if (e.searchString) {\n            try {\n                this._ignoreChangeEvent = true;\n                this._findInput.setValue(this._state.searchString);\n            }\n            finally {\n                this._ignoreChangeEvent = false;\n            }\n            this._updateButtons();\n        }\n        if (e.replaceString) {\n            this._replaceInput.inputBox.value = this._state.replaceString;\n        }\n        if (e.isRevealed) {\n            if (this._state.isRevealed) {\n                this._reveal();\n            }\n            else {\n                this._hide(true);\n            }\n        }\n        if (e.isReplaceRevealed) {\n            if (this._state.isReplaceRevealed) {\n                if (!this._codeEditor.getOption(81 /* readOnly */) && !this._isReplaceVisible) {\n                    this._isReplaceVisible = true;\n                    this._replaceInput.width = dom.getTotalWidth(this._findInput.domNode);\n                    this._updateButtons();\n                    this._replaceInput.inputBox.layout();\n                }\n            }\n            else {\n                if (this._isReplaceVisible) {\n                    this._isReplaceVisible = false;\n                    this._updateButtons();\n                }\n            }\n        }\n        if ((e.isRevealed || e.isReplaceRevealed) && (this._state.isRevealed || this._state.isReplaceRevealed)) {\n            if (this._tryUpdateHeight()) {\n                this._showViewZone();\n            }\n        }\n        if (e.isRegex) {\n            this._findInput.setRegex(this._state.isRegex);\n        }\n        if (e.wholeWord) {\n            this._findInput.setWholeWords(this._state.wholeWord);\n        }\n        if (e.matchCase) {\n            this._findInput.setCaseSensitive(this._state.matchCase);\n        }\n        if (e.preserveCase) {\n            this._replaceInput.setPreserveCase(this._state.preserveCase);\n        }\n        if (e.searchScope) {\n            if (this._state.searchScope) {\n                this._toggleSelectionFind.checked = true;\n            }\n            else {\n                this._toggleSelectionFind.checked = false;\n            }\n            this._updateToggleSelectionFindButton();\n        }\n        if (e.searchString || e.matchesCount || e.matchesPosition) {\n            let showRedOutline = (this._state.searchString.length > 0 && this._state.matchesCount === 0);\n            this._domNode.classList.toggle('no-results', showRedOutline);\n            this._updateMatchesCount();\n            this._updateButtons();\n        }\n        if (e.searchString || e.currentMatch) {\n            this._layoutViewZone();\n        }\n        if (e.updateHistory) {\n            this._delayedUpdateHistory();\n        }\n        if (e.loop) {\n            this._updateButtons();\n        }\n    }\n    _delayedUpdateHistory() {\n        this._updateHistoryDelayer.trigger(this._updateHistory.bind(this)).then(undefined, onUnexpectedError);\n    }\n    _updateHistory() {\n        if (this._state.searchString) {\n            this._findInput.inputBox.addToHistory();\n        }\n        if (this._state.replaceString) {\n            this._replaceInput.inputBox.addToHistory();\n        }\n    }\n    _updateMatchesCount() {\n        this._matchesCount.style.minWidth = MAX_MATCHES_COUNT_WIDTH + 'px';\n        if (this._state.matchesCount >= MATCHES_LIMIT) {\n            this._matchesCount.title = NLS_MATCHES_COUNT_LIMIT_TITLE;\n        }\n        else {\n            this._matchesCount.title = '';\n        }\n        // remove previous content\n        if (this._matchesCount.firstChild) {\n            this._matchesCount.removeChild(this._matchesCount.firstChild);\n        }\n        let label;\n        if (this._state.matchesCount > 0) {\n            let matchesCount = String(this._state.matchesCount);\n            if (this._state.matchesCount >= MATCHES_LIMIT) {\n                matchesCount += '+';\n            }\n            let matchesPosition = String(this._state.matchesPosition);\n            if (matchesPosition === '0') {\n                matchesPosition = '?';\n            }\n            label = strings.format(NLS_MATCHES_LOCATION, matchesPosition, matchesCount);\n        }\n        else {\n            label = NLS_NO_RESULTS;\n        }\n        this._matchesCount.appendChild(document.createTextNode(label));\n        alertFn(this._getAriaLabel(label, this._state.currentMatch, this._state.searchString));\n        MAX_MATCHES_COUNT_WIDTH = Math.max(MAX_MATCHES_COUNT_WIDTH, this._matchesCount.clientWidth);\n    }\n    // ----- actions\n    _getAriaLabel(label, currentMatch, searchString) {\n        if (label === NLS_NO_RESULTS) {\n            return searchString === ''\n                ? nls.localize('ariaSearchNoResultEmpty', \"{0} found\", label)\n                : nls.localize('ariaSearchNoResult', \"{0} found for '{1}'\", label, searchString);\n        }\n        if (currentMatch) {\n            const ariaLabel = nls.localize('ariaSearchNoResultWithLineNum', \"{0} found for '{1}', at {2}\", label, searchString, currentMatch.startLineNumber + ':' + currentMatch.startColumn);\n            const model = this._codeEditor.getModel();\n            if (model && (currentMatch.startLineNumber <= model.getLineCount()) && (currentMatch.startLineNumber >= 1)) {\n                const lineContent = model.getLineContent(currentMatch.startLineNumber);\n                return `${lineContent}, ${ariaLabel}`;\n            }\n            return ariaLabel;\n        }\n        return nls.localize('ariaSearchNoResultWithLineNumNoCurrentMatch', \"{0} found for '{1}'\", label, searchString);\n    }\n    /**\n     * If 'selection find' is ON we should not disable the button (its function is to cancel 'selection find').\n     * If 'selection find' is OFF we enable the button only if there is a selection.\n     */\n    _updateToggleSelectionFindButton() {\n        let selection = this._codeEditor.getSelection();\n        let isSelection = selection ? (selection.startLineNumber !== selection.endLineNumber || selection.startColumn !== selection.endColumn) : false;\n        let isChecked = this._toggleSelectionFind.checked;\n        if (this._isVisible && (isChecked || isSelection)) {\n            this._toggleSelectionFind.enable();\n        }\n        else {\n            this._toggleSelectionFind.disable();\n        }\n    }\n    _updateButtons() {\n        this._findInput.setEnabled(this._isVisible);\n        this._replaceInput.setEnabled(this._isVisible && this._isReplaceVisible);\n        this._updateToggleSelectionFindButton();\n        this._closeBtn.setEnabled(this._isVisible);\n        let findInputIsNonEmpty = (this._state.searchString.length > 0);\n        let matchesCount = this._state.matchesCount ? true : false;\n        this._prevBtn.setEnabled(this._isVisible && findInputIsNonEmpty && matchesCount && this._state.canNavigateBack());\n        this._nextBtn.setEnabled(this._isVisible && findInputIsNonEmpty && matchesCount && this._state.canNavigateForward());\n        this._replaceBtn.setEnabled(this._isVisible && this._isReplaceVisible && findInputIsNonEmpty);\n        this._replaceAllBtn.setEnabled(this._isVisible && this._isReplaceVisible && findInputIsNonEmpty);\n        this._domNode.classList.toggle('replaceToggled', this._isReplaceVisible);\n        this._toggleReplaceBtn.setExpanded(this._isReplaceVisible);\n        let canReplace = !this._codeEditor.getOption(81 /* readOnly */);\n        this._toggleReplaceBtn.setEnabled(this._isVisible && canReplace);\n    }\n    _reveal() {\n        this._revealTimeouts.forEach(e => {\n            clearTimeout(e);\n        });\n        this._revealTimeouts = [];\n        if (!this._isVisible) {\n            this._isVisible = true;\n            const selection = this._codeEditor.getSelection();\n            switch (this._codeEditor.getOption(35 /* find */).autoFindInSelection) {\n                case 'always':\n                    this._toggleSelectionFind.checked = true;\n                    break;\n                case 'never':\n                    this._toggleSelectionFind.checked = false;\n                    break;\n                case 'multiline': {\n                    const isSelectionMultipleLine = !!selection && selection.startLineNumber !== selection.endLineNumber;\n                    this._toggleSelectionFind.checked = isSelectionMultipleLine;\n                    break;\n                }\n                default:\n                    break;\n            }\n            this._tryUpdateWidgetWidth();\n            this._updateButtons();\n            this._revealTimeouts.push(setTimeout(() => {\n                this._domNode.classList.add('visible');\n                this._domNode.setAttribute('aria-hidden', 'false');\n            }, 0));\n            // validate query again as it's being dismissed when we hide the find widget.\n            this._revealTimeouts.push(setTimeout(() => {\n                this._findInput.validate();\n            }, 200));\n            this._codeEditor.layoutOverlayWidget(this);\n            let adjustEditorScrollTop = true;\n            if (this._codeEditor.getOption(35 /* find */).seedSearchStringFromSelection && selection) {\n                const domNode = this._codeEditor.getDomNode();\n                if (domNode) {\n                    const editorCoords = dom.getDomNodePagePosition(domNode);\n                    const startCoords = this._codeEditor.getScrolledVisiblePosition(selection.getStartPosition());\n                    const startLeft = editorCoords.left + (startCoords ? startCoords.left : 0);\n                    const startTop = startCoords ? startCoords.top : 0;\n                    if (this._viewZone && startTop < this._viewZone.heightInPx) {\n                        if (selection.endLineNumber > selection.startLineNumber) {\n                            adjustEditorScrollTop = false;\n                        }\n                        const leftOfFindWidget = dom.getTopLeftOffset(this._domNode).left;\n                        if (startLeft > leftOfFindWidget) {\n                            adjustEditorScrollTop = false;\n                        }\n                        const endCoords = this._codeEditor.getScrolledVisiblePosition(selection.getEndPosition());\n                        const endLeft = editorCoords.left + (endCoords ? endCoords.left : 0);\n                        if (endLeft > leftOfFindWidget) {\n                            adjustEditorScrollTop = false;\n                        }\n                    }\n                }\n            }\n            this._showViewZone(adjustEditorScrollTop);\n        }\n    }\n    _hide(focusTheEditor) {\n        this._revealTimeouts.forEach(e => {\n            clearTimeout(e);\n        });\n        this._revealTimeouts = [];\n        if (this._isVisible) {\n            this._isVisible = false;\n            this._updateButtons();\n            this._domNode.classList.remove('visible');\n            this._domNode.setAttribute('aria-hidden', 'true');\n            this._findInput.clearMessage();\n            if (focusTheEditor) {\n                this._codeEditor.focus();\n            }\n            this._codeEditor.layoutOverlayWidget(this);\n            this._removeViewZone();\n        }\n    }\n    _layoutViewZone(targetScrollTop) {\n        const addExtraSpaceOnTop = this._codeEditor.getOption(35 /* find */).addExtraSpaceOnTop;\n        if (!addExtraSpaceOnTop) {\n            this._removeViewZone();\n            return;\n        }\n        if (!this._isVisible) {\n            return;\n        }\n        const viewZone = this._viewZone;\n        if (this._viewZoneId !== undefined || !viewZone) {\n            return;\n        }\n        this._codeEditor.changeViewZones((accessor) => {\n            viewZone.heightInPx = this._getHeight();\n            this._viewZoneId = accessor.addZone(viewZone);\n            // scroll top adjust to make sure the editor doesn't scroll when adding viewzone at the beginning.\n            this._codeEditor.setScrollTop(targetScrollTop || this._codeEditor.getScrollTop() + viewZone.heightInPx);\n        });\n    }\n    _showViewZone(adjustScroll = true) {\n        if (!this._isVisible) {\n            return;\n        }\n        const addExtraSpaceOnTop = this._codeEditor.getOption(35 /* find */).addExtraSpaceOnTop;\n        if (!addExtraSpaceOnTop) {\n            return;\n        }\n        if (this._viewZone === undefined) {\n            this._viewZone = new FindWidgetViewZone(0);\n        }\n        const viewZone = this._viewZone;\n        this._codeEditor.changeViewZones((accessor) => {\n            if (this._viewZoneId !== undefined) {\n                // the view zone already exists, we need to update the height\n                const newHeight = this._getHeight();\n                if (newHeight === viewZone.heightInPx) {\n                    return;\n                }\n                let scrollAdjustment = newHeight - viewZone.heightInPx;\n                viewZone.heightInPx = newHeight;\n                accessor.layoutZone(this._viewZoneId);\n                if (adjustScroll) {\n                    this._codeEditor.setScrollTop(this._codeEditor.getScrollTop() + scrollAdjustment);\n                }\n                return;\n            }\n            else {\n                let scrollAdjustment = this._getHeight();\n                // if the editor has top padding, factor that into the zone height\n                scrollAdjustment -= this._codeEditor.getOption(75 /* padding */).top;\n                if (scrollAdjustment <= 0) {\n                    return;\n                }\n                viewZone.heightInPx = scrollAdjustment;\n                this._viewZoneId = accessor.addZone(viewZone);\n                if (adjustScroll) {\n                    this._codeEditor.setScrollTop(this._codeEditor.getScrollTop() + scrollAdjustment);\n                }\n            }\n        });\n    }\n    _removeViewZone() {\n        this._codeEditor.changeViewZones((accessor) => {\n            if (this._viewZoneId !== undefined) {\n                accessor.removeZone(this._viewZoneId);\n                this._viewZoneId = undefined;\n                if (this._viewZone) {\n                    this._codeEditor.setScrollTop(this._codeEditor.getScrollTop() - this._viewZone.heightInPx);\n                    this._viewZone = undefined;\n                }\n            }\n        });\n    }\n    _applyTheme(theme) {\n        let inputStyles = {\n            inputActiveOptionBorder: theme.getColor(inputActiveOptionBorder),\n            inputActiveOptionBackground: theme.getColor(inputActiveOptionBackground),\n            inputActiveOptionForeground: theme.getColor(inputActiveOptionForeground),\n            inputBackground: theme.getColor(inputBackground),\n            inputForeground: theme.getColor(inputForeground),\n            inputBorder: theme.getColor(inputBorder),\n            inputValidationInfoBackground: theme.getColor(inputValidationInfoBackground),\n            inputValidationInfoForeground: theme.getColor(inputValidationInfoForeground),\n            inputValidationInfoBorder: theme.getColor(inputValidationInfoBorder),\n            inputValidationWarningBackground: theme.getColor(inputValidationWarningBackground),\n            inputValidationWarningForeground: theme.getColor(inputValidationWarningForeground),\n            inputValidationWarningBorder: theme.getColor(inputValidationWarningBorder),\n            inputValidationErrorBackground: theme.getColor(inputValidationErrorBackground),\n            inputValidationErrorForeground: theme.getColor(inputValidationErrorForeground),\n            inputValidationErrorBorder: theme.getColor(inputValidationErrorBorder),\n        };\n        this._findInput.style(inputStyles);\n        this._replaceInput.style(inputStyles);\n        this._toggleSelectionFind.style(inputStyles);\n    }\n    _tryUpdateWidgetWidth() {\n        if (!this._isVisible) {\n            return;\n        }\n        if (!dom.isInDOM(this._domNode)) {\n            // the widget is not in the DOM\n            return;\n        }\n        const layoutInfo = this._codeEditor.getLayoutInfo();\n        const editorContentWidth = layoutInfo.contentWidth;\n        if (editorContentWidth <= 0) {\n            // for example, diff view original editor\n            this._domNode.classList.add('hiddenEditor');\n            return;\n        }\n        else if (this._domNode.classList.contains('hiddenEditor')) {\n            this._domNode.classList.remove('hiddenEditor');\n        }\n        const editorWidth = layoutInfo.width;\n        const minimapWidth = layoutInfo.minimap.minimapWidth;\n        let collapsedFindWidget = false;\n        let reducedFindWidget = false;\n        let narrowFindWidget = false;\n        if (this._resized) {\n            let widgetWidth = dom.getTotalWidth(this._domNode);\n            if (widgetWidth > FIND_WIDGET_INITIAL_WIDTH) {\n                // as the widget is resized by users, we may need to change the max width of the widget as the editor width changes.\n                this._domNode.style.maxWidth = `${editorWidth - 28 - minimapWidth - 15}px`;\n                this._replaceInput.width = dom.getTotalWidth(this._findInput.domNode);\n                return;\n            }\n        }\n        if (FIND_WIDGET_INITIAL_WIDTH + 28 + minimapWidth >= editorWidth) {\n            reducedFindWidget = true;\n        }\n        if (FIND_WIDGET_INITIAL_WIDTH + 28 + minimapWidth - MAX_MATCHES_COUNT_WIDTH >= editorWidth) {\n            narrowFindWidget = true;\n        }\n        if (FIND_WIDGET_INITIAL_WIDTH + 28 + minimapWidth - MAX_MATCHES_COUNT_WIDTH >= editorWidth + 50) {\n            collapsedFindWidget = true;\n        }\n        this._domNode.classList.toggle('collapsed-find-widget', collapsedFindWidget);\n        this._domNode.classList.toggle('narrow-find-widget', narrowFindWidget);\n        this._domNode.classList.toggle('reduced-find-widget', reducedFindWidget);\n        if (!narrowFindWidget && !collapsedFindWidget) {\n            // the minimal left offset of findwidget is 15px.\n            this._domNode.style.maxWidth = `${editorWidth - 28 - minimapWidth - 15}px`;\n        }\n        if (this._resized) {\n            this._findInput.inputBox.layout();\n            let findInputWidth = this._findInput.inputBox.element.clientWidth;\n            if (findInputWidth > 0) {\n                this._replaceInput.width = findInputWidth;\n            }\n        }\n        else if (this._isReplaceVisible) {\n            this._replaceInput.width = dom.getTotalWidth(this._findInput.domNode);\n        }\n    }\n    _getHeight() {\n        let totalheight = 0;\n        // find input margin top\n        totalheight += 4;\n        // find input height\n        totalheight += this._findInput.inputBox.height + 2 /** input box border */;\n        if (this._isReplaceVisible) {\n            // replace input margin\n            totalheight += 4;\n            totalheight += this._replaceInput.inputBox.height + 2 /** input box border */;\n        }\n        // margin bottom\n        totalheight += 4;\n        return totalheight;\n    }\n    _tryUpdateHeight() {\n        const totalHeight = this._getHeight();\n        if (this._cachedHeight !== null && this._cachedHeight === totalHeight) {\n            return false;\n        }\n        this._cachedHeight = totalHeight;\n        this._domNode.style.height = `${totalHeight}px`;\n        return true;\n    }\n    // ----- Public\n    focusFindInput() {\n        this._findInput.select();\n        // Edge browser requires focus() in addition to select()\n        this._findInput.focus();\n    }\n    focusReplaceInput() {\n        this._replaceInput.select();\n        // Edge browser requires focus() in addition to select()\n        this._replaceInput.focus();\n    }\n    highlightFindOptions() {\n        this._findInput.highlightFindOptions();\n    }\n    _updateSearchScope() {\n        if (!this._codeEditor.hasModel()) {\n            return;\n        }\n        if (this._toggleSelectionFind.checked) {\n            let selections = this._codeEditor.getSelections();\n            selections.map(selection => {\n                if (selection.endColumn === 1 && selection.endLineNumber > selection.startLineNumber) {\n                    selection = selection.setEndPosition(selection.endLineNumber - 1, this._codeEditor.getModel().getLineMaxColumn(selection.endLineNumber - 1));\n                }\n                const currentMatch = this._state.currentMatch;\n                if (selection.startLineNumber !== selection.endLineNumber) {\n                    if (!Range.equalsRange(selection, currentMatch)) {\n                        return selection;\n                    }\n                }\n                return null;\n            }).filter(element => !!element);\n            if (selections.length) {\n                this._state.change({ searchScope: selections }, true);\n            }\n        }\n    }\n    _onFindInputMouseDown(e) {\n        // on linux, middle key does pasting.\n        if (e.middleButton) {\n            e.stopPropagation();\n        }\n    }\n    _onFindInputKeyDown(e) {\n        if (e.equals(ctrlKeyMod | 3 /* Enter */)) {\n            if (this._keybindingService.dispatchEvent(e, e.target)) {\n                e.preventDefault();\n                return;\n            }\n            else {\n                this._findInput.inputBox.insertAtCursor('\\n');\n                e.preventDefault();\n                return;\n            }\n        }\n        if (e.equals(2 /* Tab */)) {\n            if (this._isReplaceVisible) {\n                this._replaceInput.focus();\n            }\n            else {\n                this._findInput.focusOnCaseSensitive();\n            }\n            e.preventDefault();\n            return;\n        }\n        if (e.equals(2048 /* CtrlCmd */ | 18 /* DownArrow */)) {\n            this._codeEditor.focus();\n            e.preventDefault();\n            return;\n        }\n        if (e.equals(16 /* UpArrow */)) {\n            return stopPropagationForMultiLineUpwards(e, this._findInput.getValue(), this._findInput.domNode.querySelector('textarea'));\n        }\n        if (e.equals(18 /* DownArrow */)) {\n            return stopPropagationForMultiLineDownwards(e, this._findInput.getValue(), this._findInput.domNode.querySelector('textarea'));\n        }\n    }\n    _onReplaceInputKeyDown(e) {\n        if (e.equals(ctrlKeyMod | 3 /* Enter */)) {\n            if (this._keybindingService.dispatchEvent(e, e.target)) {\n                e.preventDefault();\n                return;\n            }\n            else {\n                if (platform.isWindows && platform.isNative && !this._ctrlEnterReplaceAllWarningPrompted) {\n                    // this is the first time when users press Ctrl + Enter to replace all\n                    this._notificationService.info(nls.localize('ctrlEnter.keybindingChanged', 'Ctrl+Enter now inserts line break instead of replacing all. You can modify the keybinding for editor.action.replaceAll to override this behavior.'));\n                    this._ctrlEnterReplaceAllWarningPrompted = true;\n                    this._storageService.store(ctrlEnterReplaceAllWarningPromptedKey, true, 0 /* GLOBAL */, 0 /* USER */);\n                }\n                this._replaceInput.inputBox.insertAtCursor('\\n');\n                e.preventDefault();\n                return;\n            }\n        }\n        if (e.equals(2 /* Tab */)) {\n            this._findInput.focusOnCaseSensitive();\n            e.preventDefault();\n            return;\n        }\n        if (e.equals(1024 /* Shift */ | 2 /* Tab */)) {\n            this._findInput.focus();\n            e.preventDefault();\n            return;\n        }\n        if (e.equals(2048 /* CtrlCmd */ | 18 /* DownArrow */)) {\n            this._codeEditor.focus();\n            e.preventDefault();\n            return;\n        }\n        if (e.equals(16 /* UpArrow */)) {\n            return stopPropagationForMultiLineUpwards(e, this._replaceInput.inputBox.value, this._replaceInput.inputBox.element.querySelector('textarea'));\n        }\n        if (e.equals(18 /* DownArrow */)) {\n            return stopPropagationForMultiLineDownwards(e, this._replaceInput.inputBox.value, this._replaceInput.inputBox.element.querySelector('textarea'));\n        }\n    }\n    // ----- sash\n    getVerticalSashLeft(_sash) {\n        return 0;\n    }\n    // ----- initialization\n    _keybindingLabelFor(actionId) {\n        let kb = this._keybindingService.lookupKeybinding(actionId);\n        if (!kb) {\n            return '';\n        }\n        return ` (${kb.getLabel()})`;\n    }\n    _buildDomNode() {\n        const flexibleHeight = true;\n        const flexibleWidth = true;\n        // Find input\n        this._findInput = this._register(new ContextScopedFindInput(null, this._contextViewProvider, {\n            width: FIND_INPUT_AREA_WIDTH,\n            label: NLS_FIND_INPUT_LABEL,\n            placeholder: NLS_FIND_INPUT_PLACEHOLDER,\n            appendCaseSensitiveLabel: this._keybindingLabelFor(FIND_IDS.ToggleCaseSensitiveCommand),\n            appendWholeWordsLabel: this._keybindingLabelFor(FIND_IDS.ToggleWholeWordCommand),\n            appendRegexLabel: this._keybindingLabelFor(FIND_IDS.ToggleRegexCommand),\n            validation: (value) => {\n                if (value.length === 0 || !this._findInput.getRegex()) {\n                    return null;\n                }\n                try {\n                    // use `g` and `u` which are also used by the TextModel search\n                    new RegExp(value, 'gu');\n                    return null;\n                }\n                catch (e) {\n                    return { content: e.message };\n                }\n            },\n            flexibleHeight,\n            flexibleWidth,\n            flexibleMaxHeight: 118,\n            showHistoryHint: () => showHistoryKeybindingHint(this._keybindingService)\n        }, this._contextKeyService, true));\n        this._findInput.setRegex(!!this._state.isRegex);\n        this._findInput.setCaseSensitive(!!this._state.matchCase);\n        this._findInput.setWholeWords(!!this._state.wholeWord);\n        this._register(this._findInput.onKeyDown((e) => this._onFindInputKeyDown(e)));\n        this._register(this._findInput.inputBox.onDidChange(() => {\n            if (this._ignoreChangeEvent) {\n                return;\n            }\n            this._state.change({ searchString: this._findInput.getValue() }, true);\n        }));\n        this._register(this._findInput.onDidOptionChange(() => {\n            this._state.change({\n                isRegex: this._findInput.getRegex(),\n                wholeWord: this._findInput.getWholeWords(),\n                matchCase: this._findInput.getCaseSensitive()\n            }, true);\n        }));\n        this._register(this._findInput.onCaseSensitiveKeyDown((e) => {\n            if (e.equals(1024 /* Shift */ | 2 /* Tab */)) {\n                if (this._isReplaceVisible) {\n                    this._replaceInput.focus();\n                    e.preventDefault();\n                }\n            }\n        }));\n        this._register(this._findInput.onRegexKeyDown((e) => {\n            if (e.equals(2 /* Tab */)) {\n                if (this._isReplaceVisible) {\n                    this._replaceInput.focusOnPreserve();\n                    e.preventDefault();\n                }\n            }\n        }));\n        this._register(this._findInput.inputBox.onDidHeightChange((e) => {\n            if (this._tryUpdateHeight()) {\n                this._showViewZone();\n            }\n        }));\n        if (platform.isLinux) {\n            this._register(this._findInput.onMouseDown((e) => this._onFindInputMouseDown(e)));\n        }\n        this._matchesCount = document.createElement('div');\n        this._matchesCount.className = 'matchesCount';\n        this._updateMatchesCount();\n        // Previous button\n        this._prevBtn = this._register(new SimpleButton({\n            label: NLS_PREVIOUS_MATCH_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.PreviousMatchFindAction),\n            icon: findPreviousMatchIcon,\n            onTrigger: () => {\n                this._codeEditor.getAction(FIND_IDS.PreviousMatchFindAction).run().then(undefined, onUnexpectedError);\n            }\n        }));\n        // Next button\n        this._nextBtn = this._register(new SimpleButton({\n            label: NLS_NEXT_MATCH_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.NextMatchFindAction),\n            icon: findNextMatchIcon,\n            onTrigger: () => {\n                this._codeEditor.getAction(FIND_IDS.NextMatchFindAction).run().then(undefined, onUnexpectedError);\n            }\n        }));\n        let findPart = document.createElement('div');\n        findPart.className = 'find-part';\n        findPart.appendChild(this._findInput.domNode);\n        const actionsContainer = document.createElement('div');\n        actionsContainer.className = 'find-actions';\n        findPart.appendChild(actionsContainer);\n        actionsContainer.appendChild(this._matchesCount);\n        actionsContainer.appendChild(this._prevBtn.domNode);\n        actionsContainer.appendChild(this._nextBtn.domNode);\n        // Toggle selection button\n        this._toggleSelectionFind = this._register(new Checkbox({\n            icon: findSelectionIcon,\n            title: NLS_TOGGLE_SELECTION_FIND_TITLE + this._keybindingLabelFor(FIND_IDS.ToggleSearchScopeCommand),\n            isChecked: false\n        }));\n        this._register(this._toggleSelectionFind.onChange(() => {\n            if (this._toggleSelectionFind.checked) {\n                if (this._codeEditor.hasModel()) {\n                    let selections = this._codeEditor.getSelections();\n                    selections.map(selection => {\n                        if (selection.endColumn === 1 && selection.endLineNumber > selection.startLineNumber) {\n                            selection = selection.setEndPosition(selection.endLineNumber - 1, this._codeEditor.getModel().getLineMaxColumn(selection.endLineNumber - 1));\n                        }\n                        if (!selection.isEmpty()) {\n                            return selection;\n                        }\n                        return null;\n                    }).filter(element => !!element);\n                    if (selections.length) {\n                        this._state.change({ searchScope: selections }, true);\n                    }\n                }\n            }\n            else {\n                this._state.change({ searchScope: null }, true);\n            }\n        }));\n        actionsContainer.appendChild(this._toggleSelectionFind.domNode);\n        // Close button\n        this._closeBtn = this._register(new SimpleButton({\n            label: NLS_CLOSE_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.CloseFindWidgetCommand),\n            icon: widgetClose,\n            onTrigger: () => {\n                this._state.change({ isRevealed: false, searchScope: null }, false);\n            },\n            onKeyDown: (e) => {\n                if (e.equals(2 /* Tab */)) {\n                    if (this._isReplaceVisible) {\n                        if (this._replaceBtn.isEnabled()) {\n                            this._replaceBtn.focus();\n                        }\n                        else {\n                            this._codeEditor.focus();\n                        }\n                        e.preventDefault();\n                    }\n                }\n            }\n        }));\n        actionsContainer.appendChild(this._closeBtn.domNode);\n        // Replace input\n        this._replaceInput = this._register(new ContextScopedReplaceInput(null, undefined, {\n            label: NLS_REPLACE_INPUT_LABEL,\n            placeholder: NLS_REPLACE_INPUT_PLACEHOLDER,\n            appendPreserveCaseLabel: this._keybindingLabelFor(FIND_IDS.TogglePreserveCaseCommand),\n            history: [],\n            flexibleHeight,\n            flexibleWidth,\n            flexibleMaxHeight: 118,\n            showHistoryHint: () => showHistoryKeybindingHint(this._keybindingService)\n        }, this._contextKeyService, true));\n        this._replaceInput.setPreserveCase(!!this._state.preserveCase);\n        this._register(this._replaceInput.onKeyDown((e) => this._onReplaceInputKeyDown(e)));\n        this._register(this._replaceInput.inputBox.onDidChange(() => {\n            this._state.change({ replaceString: this._replaceInput.inputBox.value }, false);\n        }));\n        this._register(this._replaceInput.inputBox.onDidHeightChange((e) => {\n            if (this._isReplaceVisible && this._tryUpdateHeight()) {\n                this._showViewZone();\n            }\n        }));\n        this._register(this._replaceInput.onDidOptionChange(() => {\n            this._state.change({\n                preserveCase: this._replaceInput.getPreserveCase()\n            }, true);\n        }));\n        this._register(this._replaceInput.onPreserveCaseKeyDown((e) => {\n            if (e.equals(2 /* Tab */)) {\n                if (this._prevBtn.isEnabled()) {\n                    this._prevBtn.focus();\n                }\n                else if (this._nextBtn.isEnabled()) {\n                    this._nextBtn.focus();\n                }\n                else if (this._toggleSelectionFind.enabled) {\n                    this._toggleSelectionFind.focus();\n                }\n                else if (this._closeBtn.isEnabled()) {\n                    this._closeBtn.focus();\n                }\n                e.preventDefault();\n            }\n        }));\n        // Replace one button\n        this._replaceBtn = this._register(new SimpleButton({\n            label: NLS_REPLACE_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.ReplaceOneAction),\n            icon: findReplaceIcon,\n            onTrigger: () => {\n                this._controller.replace();\n            },\n            onKeyDown: (e) => {\n                if (e.equals(1024 /* Shift */ | 2 /* Tab */)) {\n                    this._closeBtn.focus();\n                    e.preventDefault();\n                }\n            }\n        }));\n        // Replace all button\n        this._replaceAllBtn = this._register(new SimpleButton({\n            label: NLS_REPLACE_ALL_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.ReplaceAllAction),\n            icon: findReplaceAllIcon,\n            onTrigger: () => {\n                this._controller.replaceAll();\n            }\n        }));\n        let replacePart = document.createElement('div');\n        replacePart.className = 'replace-part';\n        replacePart.appendChild(this._replaceInput.domNode);\n        const replaceActionsContainer = document.createElement('div');\n        replaceActionsContainer.className = 'replace-actions';\n        replacePart.appendChild(replaceActionsContainer);\n        replaceActionsContainer.appendChild(this._replaceBtn.domNode);\n        replaceActionsContainer.appendChild(this._replaceAllBtn.domNode);\n        // Toggle replace button\n        this._toggleReplaceBtn = this._register(new SimpleButton({\n            label: NLS_TOGGLE_REPLACE_MODE_BTN_LABEL,\n            className: 'codicon toggle left',\n            onTrigger: () => {\n                this._state.change({ isReplaceRevealed: !this._isReplaceVisible }, false);\n                if (this._isReplaceVisible) {\n                    this._replaceInput.width = dom.getTotalWidth(this._findInput.domNode);\n                    this._replaceInput.inputBox.layout();\n                }\n                this._showViewZone();\n            }\n        }));\n        this._toggleReplaceBtn.setExpanded(this._isReplaceVisible);\n        // Widget\n        this._domNode = document.createElement('div');\n        this._domNode.className = 'editor-widget find-widget';\n        this._domNode.setAttribute('aria-hidden', 'true');\n        // We need to set this explicitly, otherwise on IE11, the width inheritence of flex doesn't work.\n        this._domNode.style.width = `${FIND_WIDGET_INITIAL_WIDTH}px`;\n        this._domNode.appendChild(this._toggleReplaceBtn.domNode);\n        this._domNode.appendChild(findPart);\n        this._domNode.appendChild(replacePart);\n        this._resizeSash = new Sash(this._domNode, this, { orientation: 0 /* VERTICAL */, size: 2 });\n        this._resized = false;\n        let originalWidth = FIND_WIDGET_INITIAL_WIDTH;\n        this._register(this._resizeSash.onDidStart(() => {\n            originalWidth = dom.getTotalWidth(this._domNode);\n        }));\n        this._register(this._resizeSash.onDidChange((evt) => {\n            this._resized = true;\n            let width = originalWidth + evt.startX - evt.currentX;\n            if (width < FIND_WIDGET_INITIAL_WIDTH) {\n                // narrow down the find widget should be handled by CSS.\n                return;\n            }\n            const maxWidth = parseFloat(dom.getComputedStyle(this._domNode).maxWidth) || 0;\n            if (width > maxWidth) {\n                return;\n            }\n            this._domNode.style.width = `${width}px`;\n            if (this._isReplaceVisible) {\n                this._replaceInput.width = dom.getTotalWidth(this._findInput.domNode);\n            }\n            this._findInput.inputBox.layout();\n            this._tryUpdateHeight();\n        }));\n        this._register(this._resizeSash.onDidReset(() => {\n            // users double click on the sash\n            const currentWidth = dom.getTotalWidth(this._domNode);\n            if (currentWidth < FIND_WIDGET_INITIAL_WIDTH) {\n                // The editor is narrow and the width of the find widget is controlled fully by CSS.\n                return;\n            }\n            let width = FIND_WIDGET_INITIAL_WIDTH;\n            if (!this._resized || currentWidth === FIND_WIDGET_INITIAL_WIDTH) {\n                // 1. never resized before, double click should maximizes it\n                // 2. users resized it already but its width is the same as default\n                const layoutInfo = this._codeEditor.getLayoutInfo();\n                width = layoutInfo.width - 28 - layoutInfo.minimap.minimapWidth - 15;\n                this._resized = true;\n            }\n            else {\n                /**\n                 * no op, the find widget should be shrinked to its default size.\n                 */\n            }\n            this._domNode.style.width = `${width}px`;\n            if (this._isReplaceVisible) {\n                this._replaceInput.width = dom.getTotalWidth(this._findInput.domNode);\n            }\n            this._findInput.inputBox.layout();\n        }));\n    }\n    updateAccessibilitySupport() {\n        const value = this._codeEditor.getOption(2 /* accessibilitySupport */);\n        this._findInput.setFocusInputOnOptionClick(value !== 2 /* Enabled */);\n    }\n}\nFindWidget.ID = 'editor.contrib.findWidget';\nexport class SimpleButton extends Widget {\n    constructor(opts) {\n        super();\n        this._opts = opts;\n        let className = 'button';\n        if (this._opts.className) {\n            className = className + ' ' + this._opts.className;\n        }\n        if (this._opts.icon) {\n            className = className + ' ' + ThemeIcon.asClassName(this._opts.icon);\n        }\n        this._domNode = document.createElement('div');\n        this._domNode.title = this._opts.label;\n        this._domNode.tabIndex = 0;\n        this._domNode.className = className;\n        this._domNode.setAttribute('role', 'button');\n        this._domNode.setAttribute('aria-label', this._opts.label);\n        this.onclick(this._domNode, (e) => {\n            this._opts.onTrigger();\n            e.preventDefault();\n        });\n        this.onkeydown(this._domNode, (e) => {\n            if (e.equals(10 /* Space */) || e.equals(3 /* Enter */)) {\n                this._opts.onTrigger();\n                e.preventDefault();\n                return;\n            }\n            if (this._opts.onKeyDown) {\n                this._opts.onKeyDown(e);\n            }\n        });\n    }\n    get domNode() {\n        return this._domNode;\n    }\n    isEnabled() {\n        return (this._domNode.tabIndex >= 0);\n    }\n    focus() {\n        this._domNode.focus();\n    }\n    setEnabled(enabled) {\n        this._domNode.classList.toggle('disabled', !enabled);\n        this._domNode.setAttribute('aria-disabled', String(!enabled));\n        this._domNode.tabIndex = enabled ? 0 : -1;\n    }\n    setExpanded(expanded) {\n        this._domNode.setAttribute('aria-expanded', String(!!expanded));\n        if (expanded) {\n            this._domNode.classList.remove(...ThemeIcon.asClassNameArray(findCollapsedIcon));\n            this._domNode.classList.add(...ThemeIcon.asClassNameArray(findExpandedIcon));\n        }\n        else {\n            this._domNode.classList.remove(...ThemeIcon.asClassNameArray(findExpandedIcon));\n            this._domNode.classList.add(...ThemeIcon.asClassNameArray(findCollapsedIcon));\n        }\n    }\n}\n// theming\nregisterThemingParticipant((theme, collector) => {\n    const addBackgroundColorRule = (selector, color) => {\n        if (color) {\n            collector.addRule(`.monaco-editor ${selector} { background-color: ${color}; }`);\n        }\n    };\n    addBackgroundColorRule('.findMatch', theme.getColor(editorFindMatchHighlight));\n    addBackgroundColorRule('.currentFindMatch', theme.getColor(editorFindMatch));\n    addBackgroundColorRule('.findScope', theme.getColor(editorFindRangeHighlight));\n    const widgetBackground = theme.getColor(editorWidgetBackground);\n    addBackgroundColorRule('.find-widget', widgetBackground);\n    const widgetShadowColor = theme.getColor(widgetShadow);\n    if (widgetShadowColor) {\n        collector.addRule(`.monaco-editor .find-widget { box-shadow: 0 0 8px 2px ${widgetShadowColor}; }`);\n    }\n    const findMatchHighlightBorder = theme.getColor(editorFindMatchHighlightBorder);\n    if (findMatchHighlightBorder) {\n        collector.addRule(`.monaco-editor .findMatch { border: 1px ${theme.type === 'hc' ? 'dotted' : 'solid'} ${findMatchHighlightBorder}; box-sizing: border-box; }`);\n    }\n    const findMatchBorder = theme.getColor(editorFindMatchBorder);\n    if (findMatchBorder) {\n        collector.addRule(`.monaco-editor .currentFindMatch { border: 2px solid ${findMatchBorder}; padding: 1px; box-sizing: border-box; }`);\n    }\n    const findRangeHighlightBorder = theme.getColor(editorFindRangeHighlightBorder);\n    if (findRangeHighlightBorder) {\n        collector.addRule(`.monaco-editor .findScope { border: 1px ${theme.type === 'hc' ? 'dashed' : 'solid'} ${findRangeHighlightBorder}; }`);\n    }\n    const hcBorder = theme.getColor(contrastBorder);\n    if (hcBorder) {\n        collector.addRule(`.monaco-editor .find-widget { border: 1px solid ${hcBorder}; }`);\n    }\n    const foreground = theme.getColor(editorWidgetForeground);\n    if (foreground) {\n        collector.addRule(`.monaco-editor .find-widget { color: ${foreground}; }`);\n    }\n    const error = theme.getColor(errorForeground);\n    if (error) {\n        collector.addRule(`.monaco-editor .find-widget.no-results .matchesCount { color: ${error}; }`);\n    }\n    const resizeBorderBackground = theme.getColor(editorWidgetResizeBorder);\n    if (resizeBorderBackground) {\n        collector.addRule(`.monaco-editor .find-widget .monaco-sash { background-color: ${resizeBorderBackground}; }`);\n    }\n    else {\n        const border = theme.getColor(editorWidgetBorder);\n        if (border) {\n            collector.addRule(`.monaco-editor .find-widget .monaco-sash { background-color: ${border}; }`);\n        }\n    }\n    // Action bars\n    const toolbarHoverBackgroundColor = theme.getColor(toolbarHoverBackground);\n    if (toolbarHoverBackgroundColor) {\n        collector.addRule(`\n\t\t.monaco-editor .find-widget .button:not(.disabled):hover,\n\t\t.monaco-editor .find-widget .codicon-find-selection:hover {\n\t\t\tbackground-color: ${toolbarHoverBackgroundColor} !important;\n\t\t}\n\t`);\n    }\n    // This rule is used to override the outline color for synthetic-focus find input.\n    const focusOutline = theme.getColor(focusBorder);\n    if (focusOutline) {\n        collector.addRule(`.monaco-editor .find-widget .monaco-inputbox.synthetic-focus { outline-color: ${focusOutline}; }`);\n    }\n});\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { CaseSensitiveCheckbox, RegexCheckbox, WholeWordsCheckbox } from './findInputCheckboxes.js';\nimport { HistoryInputBox } from '../inputbox/inputBox.js';\nimport { Widget } from '../widget.js';\nimport { Emitter } from '../../../common/event.js';\nimport './findInput.css';\nimport * as nls from '../../../../nls.js';\nconst NLS_DEFAULT_LABEL = nls.localize('defaultLabel', \"input\");\nexport class FindInput extends Widget {\n    constructor(parent, contextViewProvider, _showOptionButtons, options) {\n        super();\n        this._showOptionButtons = _showOptionButtons;\n        this.fixFocusOnOptionClickEnabled = true;\n        this.imeSessionInProgress = false;\n        this._onDidOptionChange = this._register(new Emitter());\n        this.onDidOptionChange = this._onDidOptionChange.event;\n        this._onKeyDown = this._register(new Emitter());\n        this.onKeyDown = this._onKeyDown.event;\n        this._onMouseDown = this._register(new Emitter());\n        this.onMouseDown = this._onMouseDown.event;\n        this._onInput = this._register(new Emitter());\n        this._onKeyUp = this._register(new Emitter());\n        this._onCaseSensitiveKeyDown = this._register(new Emitter());\n        this.onCaseSensitiveKeyDown = this._onCaseSensitiveKeyDown.event;\n        this._onRegexKeyDown = this._register(new Emitter());\n        this.onRegexKeyDown = this._onRegexKeyDown.event;\n        this._lastHighlightFindOptions = 0;\n        this.contextViewProvider = contextViewProvider;\n        this.placeholder = options.placeholder || '';\n        this.validation = options.validation;\n        this.label = options.label || NLS_DEFAULT_LABEL;\n        this.inputActiveOptionBorder = options.inputActiveOptionBorder;\n        this.inputActiveOptionForeground = options.inputActiveOptionForeground;\n        this.inputActiveOptionBackground = options.inputActiveOptionBackground;\n        this.inputBackground = options.inputBackground;\n        this.inputForeground = options.inputForeground;\n        this.inputBorder = options.inputBorder;\n        this.inputValidationInfoBorder = options.inputValidationInfoBorder;\n        this.inputValidationInfoBackground = options.inputValidationInfoBackground;\n        this.inputValidationInfoForeground = options.inputValidationInfoForeground;\n        this.inputValidationWarningBorder = options.inputValidationWarningBorder;\n        this.inputValidationWarningBackground = options.inputValidationWarningBackground;\n        this.inputValidationWarningForeground = options.inputValidationWarningForeground;\n        this.inputValidationErrorBorder = options.inputValidationErrorBorder;\n        this.inputValidationErrorBackground = options.inputValidationErrorBackground;\n        this.inputValidationErrorForeground = options.inputValidationErrorForeground;\n        const appendCaseSensitiveLabel = options.appendCaseSensitiveLabel || '';\n        const appendWholeWordsLabel = options.appendWholeWordsLabel || '';\n        const appendRegexLabel = options.appendRegexLabel || '';\n        const history = options.history || [];\n        const flexibleHeight = !!options.flexibleHeight;\n        const flexibleWidth = !!options.flexibleWidth;\n        const flexibleMaxHeight = options.flexibleMaxHeight;\n        this.domNode = document.createElement('div');\n        this.domNode.classList.add('monaco-findInput');\n        this.inputBox = this._register(new HistoryInputBox(this.domNode, this.contextViewProvider, {\n            placeholder: this.placeholder || '',\n            ariaLabel: this.label || '',\n            validationOptions: {\n                validation: this.validation\n            },\n            inputBackground: this.inputBackground,\n            inputForeground: this.inputForeground,\n            inputBorder: this.inputBorder,\n            inputValidationInfoBackground: this.inputValidationInfoBackground,\n            inputValidationInfoForeground: this.inputValidationInfoForeground,\n            inputValidationInfoBorder: this.inputValidationInfoBorder,\n            inputValidationWarningBackground: this.inputValidationWarningBackground,\n            inputValidationWarningForeground: this.inputValidationWarningForeground,\n            inputValidationWarningBorder: this.inputValidationWarningBorder,\n            inputValidationErrorBackground: this.inputValidationErrorBackground,\n            inputValidationErrorForeground: this.inputValidationErrorForeground,\n            inputValidationErrorBorder: this.inputValidationErrorBorder,\n            history,\n            showHistoryHint: options.showHistoryHint,\n            flexibleHeight,\n            flexibleWidth,\n            flexibleMaxHeight\n        }));\n        this.regex = this._register(new RegexCheckbox({\n            appendTitle: appendRegexLabel,\n            isChecked: false,\n            inputActiveOptionBorder: this.inputActiveOptionBorder,\n            inputActiveOptionForeground: this.inputActiveOptionForeground,\n            inputActiveOptionBackground: this.inputActiveOptionBackground\n        }));\n        this._register(this.regex.onChange(viaKeyboard => {\n            this._onDidOptionChange.fire(viaKeyboard);\n            if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                this.inputBox.focus();\n            }\n            this.validate();\n        }));\n        this._register(this.regex.onKeyDown(e => {\n            this._onRegexKeyDown.fire(e);\n        }));\n        this.wholeWords = this._register(new WholeWordsCheckbox({\n            appendTitle: appendWholeWordsLabel,\n            isChecked: false,\n            inputActiveOptionBorder: this.inputActiveOptionBorder,\n            inputActiveOptionForeground: this.inputActiveOptionForeground,\n            inputActiveOptionBackground: this.inputActiveOptionBackground\n        }));\n        this._register(this.wholeWords.onChange(viaKeyboard => {\n            this._onDidOptionChange.fire(viaKeyboard);\n            if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                this.inputBox.focus();\n            }\n            this.validate();\n        }));\n        this.caseSensitive = this._register(new CaseSensitiveCheckbox({\n            appendTitle: appendCaseSensitiveLabel,\n            isChecked: false,\n            inputActiveOptionBorder: this.inputActiveOptionBorder,\n            inputActiveOptionForeground: this.inputActiveOptionForeground,\n            inputActiveOptionBackground: this.inputActiveOptionBackground\n        }));\n        this._register(this.caseSensitive.onChange(viaKeyboard => {\n            this._onDidOptionChange.fire(viaKeyboard);\n            if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                this.inputBox.focus();\n            }\n            this.validate();\n        }));\n        this._register(this.caseSensitive.onKeyDown(e => {\n            this._onCaseSensitiveKeyDown.fire(e);\n        }));\n        if (this._showOptionButtons) {\n            this.inputBox.paddingRight = this.caseSensitive.width() + this.wholeWords.width() + this.regex.width();\n        }\n        // Arrow-Key support to navigate between options\n        let indexes = [this.caseSensitive.domNode, this.wholeWords.domNode, this.regex.domNode];\n        this.onkeydown(this.domNode, (event) => {\n            if (event.equals(15 /* LeftArrow */) || event.equals(17 /* RightArrow */) || event.equals(9 /* Escape */)) {\n                let index = indexes.indexOf(document.activeElement);\n                if (index >= 0) {\n                    let newIndex = -1;\n                    if (event.equals(17 /* RightArrow */)) {\n                        newIndex = (index + 1) % indexes.length;\n                    }\n                    else if (event.equals(15 /* LeftArrow */)) {\n                        if (index === 0) {\n                            newIndex = indexes.length - 1;\n                        }\n                        else {\n                            newIndex = index - 1;\n                        }\n                    }\n                    if (event.equals(9 /* Escape */)) {\n                        indexes[index].blur();\n                        this.inputBox.focus();\n                    }\n                    else if (newIndex >= 0) {\n                        indexes[newIndex].focus();\n                    }\n                    dom.EventHelper.stop(event, true);\n                }\n            }\n        });\n        this.controls = document.createElement('div');\n        this.controls.className = 'controls';\n        this.controls.style.display = this._showOptionButtons ? 'block' : 'none';\n        this.controls.appendChild(this.caseSensitive.domNode);\n        this.controls.appendChild(this.wholeWords.domNode);\n        this.controls.appendChild(this.regex.domNode);\n        this.domNode.appendChild(this.controls);\n        if (parent) {\n            parent.appendChild(this.domNode);\n        }\n        this._register(dom.addDisposableListener(this.inputBox.inputElement, 'compositionstart', (e) => {\n            this.imeSessionInProgress = true;\n        }));\n        this._register(dom.addDisposableListener(this.inputBox.inputElement, 'compositionend', (e) => {\n            this.imeSessionInProgress = false;\n            this._onInput.fire();\n        }));\n        this.onkeydown(this.inputBox.inputElement, (e) => this._onKeyDown.fire(e));\n        this.onkeyup(this.inputBox.inputElement, (e) => this._onKeyUp.fire(e));\n        this.oninput(this.inputBox.inputElement, (e) => this._onInput.fire());\n        this.onmousedown(this.inputBox.inputElement, (e) => this._onMouseDown.fire(e));\n    }\n    enable() {\n        this.domNode.classList.remove('disabled');\n        this.inputBox.enable();\n        this.regex.enable();\n        this.wholeWords.enable();\n        this.caseSensitive.enable();\n    }\n    disable() {\n        this.domNode.classList.add('disabled');\n        this.inputBox.disable();\n        this.regex.disable();\n        this.wholeWords.disable();\n        this.caseSensitive.disable();\n    }\n    setFocusInputOnOptionClick(value) {\n        this.fixFocusOnOptionClickEnabled = value;\n    }\n    setEnabled(enabled) {\n        if (enabled) {\n            this.enable();\n        }\n        else {\n            this.disable();\n        }\n    }\n    getValue() {\n        return this.inputBox.value;\n    }\n    setValue(value) {\n        if (this.inputBox.value !== value) {\n            this.inputBox.value = value;\n        }\n    }\n    style(styles) {\n        this.inputActiveOptionBorder = styles.inputActiveOptionBorder;\n        this.inputActiveOptionForeground = styles.inputActiveOptionForeground;\n        this.inputActiveOptionBackground = styles.inputActiveOptionBackground;\n        this.inputBackground = styles.inputBackground;\n        this.inputForeground = styles.inputForeground;\n        this.inputBorder = styles.inputBorder;\n        this.inputValidationInfoBackground = styles.inputValidationInfoBackground;\n        this.inputValidationInfoForeground = styles.inputValidationInfoForeground;\n        this.inputValidationInfoBorder = styles.inputValidationInfoBorder;\n        this.inputValidationWarningBackground = styles.inputValidationWarningBackground;\n        this.inputValidationWarningForeground = styles.inputValidationWarningForeground;\n        this.inputValidationWarningBorder = styles.inputValidationWarningBorder;\n        this.inputValidationErrorBackground = styles.inputValidationErrorBackground;\n        this.inputValidationErrorForeground = styles.inputValidationErrorForeground;\n        this.inputValidationErrorBorder = styles.inputValidationErrorBorder;\n        this.applyStyles();\n    }\n    applyStyles() {\n        if (this.domNode) {\n            const checkBoxStyles = {\n                inputActiveOptionBorder: this.inputActiveOptionBorder,\n                inputActiveOptionForeground: this.inputActiveOptionForeground,\n                inputActiveOptionBackground: this.inputActiveOptionBackground,\n            };\n            this.regex.style(checkBoxStyles);\n            this.wholeWords.style(checkBoxStyles);\n            this.caseSensitive.style(checkBoxStyles);\n            const inputBoxStyles = {\n                inputBackground: this.inputBackground,\n                inputForeground: this.inputForeground,\n                inputBorder: this.inputBorder,\n                inputValidationInfoBackground: this.inputValidationInfoBackground,\n                inputValidationInfoForeground: this.inputValidationInfoForeground,\n                inputValidationInfoBorder: this.inputValidationInfoBorder,\n                inputValidationWarningBackground: this.inputValidationWarningBackground,\n                inputValidationWarningForeground: this.inputValidationWarningForeground,\n                inputValidationWarningBorder: this.inputValidationWarningBorder,\n                inputValidationErrorBackground: this.inputValidationErrorBackground,\n                inputValidationErrorForeground: this.inputValidationErrorForeground,\n                inputValidationErrorBorder: this.inputValidationErrorBorder\n            };\n            this.inputBox.style(inputBoxStyles);\n        }\n    }\n    select() {\n        this.inputBox.select();\n    }\n    focus() {\n        this.inputBox.focus();\n    }\n    getCaseSensitive() {\n        return this.caseSensitive.checked;\n    }\n    setCaseSensitive(value) {\n        this.caseSensitive.checked = value;\n    }\n    getWholeWords() {\n        return this.wholeWords.checked;\n    }\n    setWholeWords(value) {\n        this.wholeWords.checked = value;\n    }\n    getRegex() {\n        return this.regex.checked;\n    }\n    setRegex(value) {\n        this.regex.checked = value;\n        this.validate();\n    }\n    focusOnCaseSensitive() {\n        this.caseSensitive.focus();\n    }\n    highlightFindOptions() {\n        this.domNode.classList.remove('highlight-' + (this._lastHighlightFindOptions));\n        this._lastHighlightFindOptions = 1 - this._lastHighlightFindOptions;\n        this.domNode.classList.add('highlight-' + (this._lastHighlightFindOptions));\n    }\n    validate() {\n        this.inputBox.validate();\n    }\n    clearMessage() {\n        this.inputBox.hideMessage();\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { Checkbox } from '../checkbox/checkbox.js';\nimport { HistoryInputBox } from '../inputbox/inputBox.js';\nimport { Widget } from '../widget.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { Emitter } from '../../../common/event.js';\nimport './findInput.css';\nimport * as nls from '../../../../nls.js';\nconst NLS_DEFAULT_LABEL = nls.localize('defaultLabel', \"input\");\nconst NLS_PRESERVE_CASE_LABEL = nls.localize('label.preserveCaseCheckbox', \"Preserve Case\");\nexport class PreserveCaseCheckbox extends Checkbox {\n    constructor(opts) {\n        super({\n            // TODO: does this need its own icon?\n            icon: Codicon.preserveCase,\n            title: NLS_PRESERVE_CASE_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\nexport class ReplaceInput extends Widget {\n    constructor(parent, contextViewProvider, _showOptionButtons, options) {\n        super();\n        this._showOptionButtons = _showOptionButtons;\n        this.fixFocusOnOptionClickEnabled = true;\n        this.cachedOptionsWidth = 0;\n        this._onDidOptionChange = this._register(new Emitter());\n        this.onDidOptionChange = this._onDidOptionChange.event;\n        this._onKeyDown = this._register(new Emitter());\n        this.onKeyDown = this._onKeyDown.event;\n        this._onMouseDown = this._register(new Emitter());\n        this._onInput = this._register(new Emitter());\n        this._onKeyUp = this._register(new Emitter());\n        this._onPreserveCaseKeyDown = this._register(new Emitter());\n        this.onPreserveCaseKeyDown = this._onPreserveCaseKeyDown.event;\n        this.contextViewProvider = contextViewProvider;\n        this.placeholder = options.placeholder || '';\n        this.validation = options.validation;\n        this.label = options.label || NLS_DEFAULT_LABEL;\n        this.inputActiveOptionBorder = options.inputActiveOptionBorder;\n        this.inputActiveOptionForeground = options.inputActiveOptionForeground;\n        this.inputActiveOptionBackground = options.inputActiveOptionBackground;\n        this.inputBackground = options.inputBackground;\n        this.inputForeground = options.inputForeground;\n        this.inputBorder = options.inputBorder;\n        this.inputValidationInfoBorder = options.inputValidationInfoBorder;\n        this.inputValidationInfoBackground = options.inputValidationInfoBackground;\n        this.inputValidationInfoForeground = options.inputValidationInfoForeground;\n        this.inputValidationWarningBorder = options.inputValidationWarningBorder;\n        this.inputValidationWarningBackground = options.inputValidationWarningBackground;\n        this.inputValidationWarningForeground = options.inputValidationWarningForeground;\n        this.inputValidationErrorBorder = options.inputValidationErrorBorder;\n        this.inputValidationErrorBackground = options.inputValidationErrorBackground;\n        this.inputValidationErrorForeground = options.inputValidationErrorForeground;\n        const appendPreserveCaseLabel = options.appendPreserveCaseLabel || '';\n        const history = options.history || [];\n        const flexibleHeight = !!options.flexibleHeight;\n        const flexibleWidth = !!options.flexibleWidth;\n        const flexibleMaxHeight = options.flexibleMaxHeight;\n        this.domNode = document.createElement('div');\n        this.domNode.classList.add('monaco-findInput');\n        this.inputBox = this._register(new HistoryInputBox(this.domNode, this.contextViewProvider, {\n            ariaLabel: this.label || '',\n            placeholder: this.placeholder || '',\n            validationOptions: {\n                validation: this.validation\n            },\n            inputBackground: this.inputBackground,\n            inputForeground: this.inputForeground,\n            inputBorder: this.inputBorder,\n            inputValidationInfoBackground: this.inputValidationInfoBackground,\n            inputValidationInfoForeground: this.inputValidationInfoForeground,\n            inputValidationInfoBorder: this.inputValidationInfoBorder,\n            inputValidationWarningBackground: this.inputValidationWarningBackground,\n            inputValidationWarningForeground: this.inputValidationWarningForeground,\n            inputValidationWarningBorder: this.inputValidationWarningBorder,\n            inputValidationErrorBackground: this.inputValidationErrorBackground,\n            inputValidationErrorForeground: this.inputValidationErrorForeground,\n            inputValidationErrorBorder: this.inputValidationErrorBorder,\n            history,\n            showHistoryHint: options.showHistoryHint,\n            flexibleHeight,\n            flexibleWidth,\n            flexibleMaxHeight\n        }));\n        this.preserveCase = this._register(new PreserveCaseCheckbox({\n            appendTitle: appendPreserveCaseLabel,\n            isChecked: false,\n            inputActiveOptionBorder: this.inputActiveOptionBorder,\n            inputActiveOptionForeground: this.inputActiveOptionForeground,\n            inputActiveOptionBackground: this.inputActiveOptionBackground,\n        }));\n        this._register(this.preserveCase.onChange(viaKeyboard => {\n            this._onDidOptionChange.fire(viaKeyboard);\n            if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                this.inputBox.focus();\n            }\n            this.validate();\n        }));\n        this._register(this.preserveCase.onKeyDown(e => {\n            this._onPreserveCaseKeyDown.fire(e);\n        }));\n        if (this._showOptionButtons) {\n            this.cachedOptionsWidth = this.preserveCase.width();\n        }\n        else {\n            this.cachedOptionsWidth = 0;\n        }\n        // Arrow-Key support to navigate between options\n        let indexes = [this.preserveCase.domNode];\n        this.onkeydown(this.domNode, (event) => {\n            if (event.equals(15 /* LeftArrow */) || event.equals(17 /* RightArrow */) || event.equals(9 /* Escape */)) {\n                let index = indexes.indexOf(document.activeElement);\n                if (index >= 0) {\n                    let newIndex = -1;\n                    if (event.equals(17 /* RightArrow */)) {\n                        newIndex = (index + 1) % indexes.length;\n                    }\n                    else if (event.equals(15 /* LeftArrow */)) {\n                        if (index === 0) {\n                            newIndex = indexes.length - 1;\n                        }\n                        else {\n                            newIndex = index - 1;\n                        }\n                    }\n                    if (event.equals(9 /* Escape */)) {\n                        indexes[index].blur();\n                        this.inputBox.focus();\n                    }\n                    else if (newIndex >= 0) {\n                        indexes[newIndex].focus();\n                    }\n                    dom.EventHelper.stop(event, true);\n                }\n            }\n        });\n        let controls = document.createElement('div');\n        controls.className = 'controls';\n        controls.style.display = this._showOptionButtons ? 'block' : 'none';\n        controls.appendChild(this.preserveCase.domNode);\n        this.domNode.appendChild(controls);\n        if (parent) {\n            parent.appendChild(this.domNode);\n        }\n        this.onkeydown(this.inputBox.inputElement, (e) => this._onKeyDown.fire(e));\n        this.onkeyup(this.inputBox.inputElement, (e) => this._onKeyUp.fire(e));\n        this.oninput(this.inputBox.inputElement, (e) => this._onInput.fire());\n        this.onmousedown(this.inputBox.inputElement, (e) => this._onMouseDown.fire(e));\n    }\n    enable() {\n        this.domNode.classList.remove('disabled');\n        this.inputBox.enable();\n        this.preserveCase.enable();\n    }\n    disable() {\n        this.domNode.classList.add('disabled');\n        this.inputBox.disable();\n        this.preserveCase.disable();\n    }\n    setEnabled(enabled) {\n        if (enabled) {\n            this.enable();\n        }\n        else {\n            this.disable();\n        }\n    }\n    style(styles) {\n        this.inputActiveOptionBorder = styles.inputActiveOptionBorder;\n        this.inputActiveOptionForeground = styles.inputActiveOptionForeground;\n        this.inputActiveOptionBackground = styles.inputActiveOptionBackground;\n        this.inputBackground = styles.inputBackground;\n        this.inputForeground = styles.inputForeground;\n        this.inputBorder = styles.inputBorder;\n        this.inputValidationInfoBackground = styles.inputValidationInfoBackground;\n        this.inputValidationInfoForeground = styles.inputValidationInfoForeground;\n        this.inputValidationInfoBorder = styles.inputValidationInfoBorder;\n        this.inputValidationWarningBackground = styles.inputValidationWarningBackground;\n        this.inputValidationWarningForeground = styles.inputValidationWarningForeground;\n        this.inputValidationWarningBorder = styles.inputValidationWarningBorder;\n        this.inputValidationErrorBackground = styles.inputValidationErrorBackground;\n        this.inputValidationErrorForeground = styles.inputValidationErrorForeground;\n        this.inputValidationErrorBorder = styles.inputValidationErrorBorder;\n        this.applyStyles();\n    }\n    applyStyles() {\n        if (this.domNode) {\n            const checkBoxStyles = {\n                inputActiveOptionBorder: this.inputActiveOptionBorder,\n                inputActiveOptionForeground: this.inputActiveOptionForeground,\n                inputActiveOptionBackground: this.inputActiveOptionBackground,\n            };\n            this.preserveCase.style(checkBoxStyles);\n            const inputBoxStyles = {\n                inputBackground: this.inputBackground,\n                inputForeground: this.inputForeground,\n                inputBorder: this.inputBorder,\n                inputValidationInfoBackground: this.inputValidationInfoBackground,\n                inputValidationInfoForeground: this.inputValidationInfoForeground,\n                inputValidationInfoBorder: this.inputValidationInfoBorder,\n                inputValidationWarningBackground: this.inputValidationWarningBackground,\n                inputValidationWarningForeground: this.inputValidationWarningForeground,\n                inputValidationWarningBorder: this.inputValidationWarningBorder,\n                inputValidationErrorBackground: this.inputValidationErrorBackground,\n                inputValidationErrorForeground: this.inputValidationErrorForeground,\n                inputValidationErrorBorder: this.inputValidationErrorBorder\n            };\n            this.inputBox.style(inputBoxStyles);\n        }\n    }\n    select() {\n        this.inputBox.select();\n    }\n    focus() {\n        this.inputBox.focus();\n    }\n    getPreserveCase() {\n        return this.preserveCase.checked;\n    }\n    setPreserveCase(value) {\n        this.preserveCase.checked = value;\n    }\n    focusOnPreserve() {\n        this.preserveCase.focus();\n    }\n    validate() {\n        if (this.inputBox) {\n            this.inputBox.validate();\n        }\n    }\n    set width(newWidth) {\n        this.inputBox.paddingRight = this.cachedOptionsWidth;\n        this.inputBox.width = newWidth;\n        this.domNode.style.width = newWidth + 'px';\n    }\n    dispose() {\n        super.dispose();\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { FindInput } from '../../../base/browser/ui/findinput/findInput.js';\nimport { ReplaceInput } from '../../../base/browser/ui/findinput/replaceInput.js';\nimport { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../contextkey/common/contextkey.js';\nimport { KeybindingsRegistry } from '../../keybinding/common/keybindingsRegistry.js';\nimport { localize } from '../../../nls.js';\nexport const historyNavigationVisible = new RawContextKey('suggestWidgetVisible', false, localize('suggestWidgetVisible', \"Whether suggestion are visible\"));\nexport const HistoryNavigationWidgetContext = 'historyNavigationWidget';\nconst HistoryNavigationForwardsEnablementContext = 'historyNavigationForwardsEnabled';\nconst HistoryNavigationBackwardsEnablementContext = 'historyNavigationBackwardsEnabled';\nfunction bindContextScopedWidget(contextKeyService, widget, contextKey) {\n    new RawContextKey(contextKey, widget).bindTo(contextKeyService);\n}\nfunction createWidgetScopedContextKeyService(contextKeyService, widget) {\n    return contextKeyService.createScoped(widget.target);\n}\nfunction getContextScopedWidget(contextKeyService, contextKey) {\n    return contextKeyService.getContext(document.activeElement).getValue(contextKey);\n}\nexport function createAndBindHistoryNavigationWidgetScopedContextKeyService(contextKeyService, widget) {\n    const scopedContextKeyService = createWidgetScopedContextKeyService(contextKeyService, widget);\n    bindContextScopedWidget(scopedContextKeyService, widget, HistoryNavigationWidgetContext);\n    const historyNavigationForwardsEnablement = new RawContextKey(HistoryNavigationForwardsEnablementContext, true).bindTo(scopedContextKeyService);\n    const historyNavigationBackwardsEnablement = new RawContextKey(HistoryNavigationBackwardsEnablementContext, true).bindTo(scopedContextKeyService);\n    return {\n        scopedContextKeyService,\n        historyNavigationForwardsEnablement,\n        historyNavigationBackwardsEnablement,\n    };\n}\nlet ContextScopedFindInput = class ContextScopedFindInput extends FindInput {\n    constructor(container, contextViewProvider, options, contextKeyService, showFindOptions = false) {\n        super(container, contextViewProvider, showFindOptions, options);\n        this._register(createAndBindHistoryNavigationWidgetScopedContextKeyService(contextKeyService, { target: this.inputBox.element, historyNavigator: this.inputBox }).scopedContextKeyService);\n    }\n};\nContextScopedFindInput = __decorate([\n    __param(3, IContextKeyService)\n], ContextScopedFindInput);\nexport { ContextScopedFindInput };\nlet ContextScopedReplaceInput = class ContextScopedReplaceInput extends ReplaceInput {\n    constructor(container, contextViewProvider, options, contextKeyService, showReplaceOptions = false) {\n        super(container, contextViewProvider, showReplaceOptions, options);\n        this._register(createAndBindHistoryNavigationWidgetScopedContextKeyService(contextKeyService, { target: this.inputBox.element, historyNavigator: this.inputBox }).scopedContextKeyService);\n    }\n};\nContextScopedReplaceInput = __decorate([\n    __param(3, IContextKeyService)\n], ContextScopedReplaceInput);\nexport { ContextScopedReplaceInput };\nKeybindingsRegistry.registerCommandAndKeybindingRule({\n    id: 'history.showPrevious',\n    weight: 200 /* WorkbenchContrib */,\n    when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetContext), ContextKeyExpr.equals(HistoryNavigationBackwardsEnablementContext, true), historyNavigationVisible.isEqualTo(false)),\n    primary: 16 /* UpArrow */,\n    secondary: [512 /* Alt */ | 16 /* UpArrow */],\n    handler: (accessor) => {\n        const widget = getContextScopedWidget(accessor.get(IContextKeyService), HistoryNavigationWidgetContext);\n        if (widget) {\n            const historyInputBox = widget.historyNavigator;\n            historyInputBox.showPreviousValue();\n        }\n    }\n});\nKeybindingsRegistry.registerCommandAndKeybindingRule({\n    id: 'history.showNext',\n    weight: 200 /* WorkbenchContrib */,\n    when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetContext), ContextKeyExpr.equals(HistoryNavigationForwardsEnablementContext, true), historyNavigationVisible.isEqualTo(false)),\n    primary: 18 /* DownArrow */,\n    secondary: [512 /* Alt */ | 18 /* DownArrow */],\n    handler: (accessor) => {\n        const widget = getContextScopedWidget(accessor.get(IContextKeyService), HistoryNavigationWidgetContext);\n        if (widget) {\n            const historyInputBox = widget.historyNavigator;\n            historyInputBox.showNextValue();\n        }\n    }\n});\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function showHistoryKeybindingHint(keybindingService) {\n    var _a, _b;\n    return ((_a = keybindingService.lookupKeybinding('history.showPrevious')) === null || _a === void 0 ? void 0 : _a.getElectronAccelerator()) === 'Up' && ((_b = keybindingService.lookupKeybinding('history.showNext')) === null || _b === void 0 ? void 0 : _b.getElectronAccelerator()) === 'Down';\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { Delayer } from '../../../../base/common/async.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport * as strings from '../../../../base/common/strings.js';\nimport { EditorAction, EditorCommand, MultiEditorAction, registerEditorAction, registerEditorCommand, registerEditorContribution, registerMultiEditorAction } from '../../../browser/editorExtensions.js';\nimport { EditorContextKeys } from '../../../common/editorContextKeys.js';\nimport { CONTEXT_FIND_INPUT_FOCUSED, CONTEXT_FIND_WIDGET_VISIBLE, CONTEXT_REPLACE_INPUT_FOCUSED, FindModelBoundToEditorModel, FIND_IDS, ToggleCaseSensitiveKeybinding, TogglePreserveCaseKeybinding, ToggleRegexKeybinding, ToggleSearchScopeKeybinding, ToggleWholeWordKeybinding } from './findModel.js';\nimport { FindOptionsWidget } from './findOptionsWidget.js';\nimport { FindReplaceState } from './findState.js';\nimport { FindWidget } from './findWidget.js';\nimport * as nls from '../../../../nls.js';\nimport { MenuId } from '../../../../platform/actions/common/actions.js';\nimport { IClipboardService } from '../../../../platform/clipboard/common/clipboardService.js';\nimport { ContextKeyExpr, IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IContextViewService } from '../../../../platform/contextview/browser/contextView.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { INotificationService } from '../../../../platform/notification/common/notification.js';\nimport { IStorageService } from '../../../../platform/storage/common/storage.js';\nimport { IThemeService } from '../../../../platform/theme/common/themeService.js';\nconst SEARCH_STRING_MAX_LENGTH = 524288;\nexport function getSelectionSearchString(editor, seedSearchStringFromSelection = 'single', seedSearchStringFromNonEmptySelection = false) {\n    if (!editor.hasModel()) {\n        return null;\n    }\n    const selection = editor.getSelection();\n    // if selection spans multiple lines, default search string to empty\n    if ((seedSearchStringFromSelection === 'single' && selection.startLineNumber === selection.endLineNumber)\n        || seedSearchStringFromSelection === 'multiple') {\n        if (selection.isEmpty()) {\n            const wordAtPosition = editor.getConfiguredWordAtPosition(selection.getStartPosition());\n            if (wordAtPosition && (false === seedSearchStringFromNonEmptySelection)) {\n                return wordAtPosition.word;\n            }\n        }\n        else {\n            if (editor.getModel().getValueLengthInRange(selection) < SEARCH_STRING_MAX_LENGTH) {\n                return editor.getModel().getValueInRange(selection);\n            }\n        }\n    }\n    return null;\n}\nlet CommonFindController = class CommonFindController extends Disposable {\n    constructor(editor, contextKeyService, storageService, clipboardService) {\n        super();\n        this._editor = editor;\n        this._findWidgetVisible = CONTEXT_FIND_WIDGET_VISIBLE.bindTo(contextKeyService);\n        this._contextKeyService = contextKeyService;\n        this._storageService = storageService;\n        this._clipboardService = clipboardService;\n        this._updateHistoryDelayer = new Delayer(500);\n        this._state = this._register(new FindReplaceState());\n        this.loadQueryState();\n        this._register(this._state.onFindReplaceStateChange((e) => this._onStateChanged(e)));\n        this._model = null;\n        this._register(this._editor.onDidChangeModel(() => {\n            let shouldRestartFind = (this._editor.getModel() && this._state.isRevealed);\n            this.disposeModel();\n            this._state.change({\n                searchScope: null,\n                matchCase: this._storageService.getBoolean('editor.matchCase', 1 /* WORKSPACE */, false),\n                wholeWord: this._storageService.getBoolean('editor.wholeWord', 1 /* WORKSPACE */, false),\n                isRegex: this._storageService.getBoolean('editor.isRegex', 1 /* WORKSPACE */, false),\n                preserveCase: this._storageService.getBoolean('editor.preserveCase', 1 /* WORKSPACE */, false)\n            }, false);\n            if (shouldRestartFind) {\n                this._start({\n                    forceRevealReplace: false,\n                    seedSearchStringFromSelection: 'none',\n                    seedSearchStringFromNonEmptySelection: false,\n                    seedSearchStringFromGlobalClipboard: false,\n                    shouldFocus: 0 /* NoFocusChange */,\n                    shouldAnimate: false,\n                    updateSearchScope: false,\n                    loop: this._editor.getOption(35 /* find */).loop\n                });\n            }\n        }));\n    }\n    get editor() {\n        return this._editor;\n    }\n    static get(editor) {\n        return editor.getContribution(CommonFindController.ID);\n    }\n    dispose() {\n        this.disposeModel();\n        super.dispose();\n    }\n    disposeModel() {\n        if (this._model) {\n            this._model.dispose();\n            this._model = null;\n        }\n    }\n    _onStateChanged(e) {\n        this.saveQueryState(e);\n        if (e.isRevealed) {\n            if (this._state.isRevealed) {\n                this._findWidgetVisible.set(true);\n            }\n            else {\n                this._findWidgetVisible.reset();\n                this.disposeModel();\n            }\n        }\n        if (e.searchString) {\n            this.setGlobalBufferTerm(this._state.searchString);\n        }\n    }\n    saveQueryState(e) {\n        if (e.isRegex) {\n            this._storageService.store('editor.isRegex', this._state.actualIsRegex, 1 /* WORKSPACE */, 0 /* USER */);\n        }\n        if (e.wholeWord) {\n            this._storageService.store('editor.wholeWord', this._state.actualWholeWord, 1 /* WORKSPACE */, 0 /* USER */);\n        }\n        if (e.matchCase) {\n            this._storageService.store('editor.matchCase', this._state.actualMatchCase, 1 /* WORKSPACE */, 0 /* USER */);\n        }\n        if (e.preserveCase) {\n            this._storageService.store('editor.preserveCase', this._state.actualPreserveCase, 1 /* WORKSPACE */, 0 /* USER */);\n        }\n    }\n    loadQueryState() {\n        this._state.change({\n            matchCase: this._storageService.getBoolean('editor.matchCase', 1 /* WORKSPACE */, this._state.matchCase),\n            wholeWord: this._storageService.getBoolean('editor.wholeWord', 1 /* WORKSPACE */, this._state.wholeWord),\n            isRegex: this._storageService.getBoolean('editor.isRegex', 1 /* WORKSPACE */, this._state.isRegex),\n            preserveCase: this._storageService.getBoolean('editor.preserveCase', 1 /* WORKSPACE */, this._state.preserveCase)\n        }, false);\n    }\n    isFindInputFocused() {\n        return !!CONTEXT_FIND_INPUT_FOCUSED.getValue(this._contextKeyService);\n    }\n    getState() {\n        return this._state;\n    }\n    closeFindWidget() {\n        this._state.change({\n            isRevealed: false,\n            searchScope: null\n        }, false);\n        this._editor.focus();\n    }\n    toggleCaseSensitive() {\n        this._state.change({ matchCase: !this._state.matchCase }, false);\n        if (!this._state.isRevealed) {\n            this.highlightFindOptions();\n        }\n    }\n    toggleWholeWords() {\n        this._state.change({ wholeWord: !this._state.wholeWord }, false);\n        if (!this._state.isRevealed) {\n            this.highlightFindOptions();\n        }\n    }\n    toggleRegex() {\n        this._state.change({ isRegex: !this._state.isRegex }, false);\n        if (!this._state.isRevealed) {\n            this.highlightFindOptions();\n        }\n    }\n    togglePreserveCase() {\n        this._state.change({ preserveCase: !this._state.preserveCase }, false);\n        if (!this._state.isRevealed) {\n            this.highlightFindOptions();\n        }\n    }\n    toggleSearchScope() {\n        if (this._state.searchScope) {\n            this._state.change({ searchScope: null }, true);\n        }\n        else {\n            if (this._editor.hasModel()) {\n                let selections = this._editor.getSelections();\n                selections.map(selection => {\n                    if (selection.endColumn === 1 && selection.endLineNumber > selection.startLineNumber) {\n                        selection = selection.setEndPosition(selection.endLineNumber - 1, this._editor.getModel().getLineMaxColumn(selection.endLineNumber - 1));\n                    }\n                    if (!selection.isEmpty()) {\n                        return selection;\n                    }\n                    return null;\n                }).filter(element => !!element);\n                if (selections.length) {\n                    this._state.change({ searchScope: selections }, true);\n                }\n            }\n        }\n    }\n    setSearchString(searchString) {\n        if (this._state.isRegex) {\n            searchString = strings.escapeRegExpCharacters(searchString);\n        }\n        this._state.change({ searchString: searchString }, false);\n    }\n    highlightFindOptions(ignoreWhenVisible = false) {\n        // overwritten in subclass\n    }\n    _start(opts, newState) {\n        return __awaiter(this, void 0, void 0, function* () {\n            this.disposeModel();\n            if (!this._editor.hasModel()) {\n                // cannot do anything with an editor that doesn't have a model...\n                return;\n            }\n            let stateChanges = Object.assign(Object.assign({}, newState), { isRevealed: true });\n            if (opts.seedSearchStringFromSelection === 'single') {\n                let selectionSearchString = getSelectionSearchString(this._editor, opts.seedSearchStringFromSelection, opts.seedSearchStringFromNonEmptySelection);\n                if (selectionSearchString) {\n                    if (this._state.isRegex) {\n                        stateChanges.searchString = strings.escapeRegExpCharacters(selectionSearchString);\n                    }\n                    else {\n                        stateChanges.searchString = selectionSearchString;\n                    }\n                }\n            }\n            else if (opts.seedSearchStringFromSelection === 'multiple' && !opts.updateSearchScope) {\n                let selectionSearchString = getSelectionSearchString(this._editor, opts.seedSearchStringFromSelection);\n                if (selectionSearchString) {\n                    stateChanges.searchString = selectionSearchString;\n                }\n            }\n            if (!stateChanges.searchString && opts.seedSearchStringFromGlobalClipboard) {\n                let selectionSearchString = yield this.getGlobalBufferTerm();\n                if (!this._editor.hasModel()) {\n                    // the editor has lost its model in the meantime\n                    return;\n                }\n                if (selectionSearchString) {\n                    stateChanges.searchString = selectionSearchString;\n                }\n            }\n            // Overwrite isReplaceRevealed\n            if (opts.forceRevealReplace || stateChanges.isReplaceRevealed) {\n                stateChanges.isReplaceRevealed = true;\n            }\n            else if (!this._findWidgetVisible.get()) {\n                stateChanges.isReplaceRevealed = false;\n            }\n            if (opts.updateSearchScope) {\n                let currentSelections = this._editor.getSelections();\n                if (currentSelections.some(selection => !selection.isEmpty())) {\n                    stateChanges.searchScope = currentSelections;\n                }\n            }\n            stateChanges.loop = opts.loop;\n            this._state.change(stateChanges, false);\n            if (!this._model) {\n                this._model = new FindModelBoundToEditorModel(this._editor, this._state);\n            }\n        });\n    }\n    start(opts, newState) {\n        return this._start(opts, newState);\n    }\n    moveToNextMatch() {\n        if (this._model) {\n            this._model.moveToNextMatch();\n            return true;\n        }\n        return false;\n    }\n    moveToPrevMatch() {\n        if (this._model) {\n            this._model.moveToPrevMatch();\n            return true;\n        }\n        return false;\n    }\n    replace() {\n        if (this._model) {\n            this._model.replace();\n            return true;\n        }\n        return false;\n    }\n    replaceAll() {\n        if (this._model) {\n            this._model.replaceAll();\n            return true;\n        }\n        return false;\n    }\n    selectAllMatches() {\n        if (this._model) {\n            this._model.selectAllMatches();\n            this._editor.focus();\n            return true;\n        }\n        return false;\n    }\n    getGlobalBufferTerm() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this._editor.getOption(35 /* find */).globalFindClipboard\n                && this._editor.hasModel()\n                && !this._editor.getModel().isTooLargeForSyncing()) {\n                return this._clipboardService.readFindText();\n            }\n            return '';\n        });\n    }\n    setGlobalBufferTerm(text) {\n        if (this._editor.getOption(35 /* find */).globalFindClipboard\n            && this._editor.hasModel()\n            && !this._editor.getModel().isTooLargeForSyncing()) {\n            // intentionally not awaited\n            this._clipboardService.writeFindText(text);\n        }\n    }\n};\nCommonFindController.ID = 'editor.contrib.findController';\nCommonFindController = __decorate([\n    __param(1, IContextKeyService),\n    __param(2, IStorageService),\n    __param(3, IClipboardService)\n], CommonFindController);\nexport { CommonFindController };\nlet FindController = class FindController extends CommonFindController {\n    constructor(editor, _contextViewService, _contextKeyService, _keybindingService, _themeService, _notificationService, _storageService, clipboardService) {\n        super(editor, _contextKeyService, _storageService, clipboardService);\n        this._contextViewService = _contextViewService;\n        this._keybindingService = _keybindingService;\n        this._themeService = _themeService;\n        this._notificationService = _notificationService;\n        this._widget = null;\n        this._findOptionsWidget = null;\n    }\n    _start(opts, newState) {\n        const _super = Object.create(null, {\n            _start: { get: () => super._start }\n        });\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this._widget) {\n                this._createFindWidget();\n            }\n            const selection = this._editor.getSelection();\n            let updateSearchScope = false;\n            switch (this._editor.getOption(35 /* find */).autoFindInSelection) {\n                case 'always':\n                    updateSearchScope = true;\n                    break;\n                case 'never':\n                    updateSearchScope = false;\n                    break;\n                case 'multiline': {\n                    const isSelectionMultipleLine = !!selection && selection.startLineNumber !== selection.endLineNumber;\n                    updateSearchScope = isSelectionMultipleLine;\n                    break;\n                }\n                default:\n                    break;\n            }\n            opts.updateSearchScope = opts.updateSearchScope || updateSearchScope;\n            yield _super._start.call(this, opts, newState);\n            if (this._widget) {\n                if (opts.shouldFocus === 2 /* FocusReplaceInput */) {\n                    this._widget.focusReplaceInput();\n                }\n                else if (opts.shouldFocus === 1 /* FocusFindInput */) {\n                    this._widget.focusFindInput();\n                }\n            }\n        });\n    }\n    highlightFindOptions(ignoreWhenVisible = false) {\n        if (!this._widget) {\n            this._createFindWidget();\n        }\n        if (this._state.isRevealed && !ignoreWhenVisible) {\n            this._widget.highlightFindOptions();\n        }\n        else {\n            this._findOptionsWidget.highlightFindOptions();\n        }\n    }\n    _createFindWidget() {\n        this._widget = this._register(new FindWidget(this._editor, this, this._state, this._contextViewService, this._keybindingService, this._contextKeyService, this._themeService, this._storageService, this._notificationService));\n        this._findOptionsWidget = this._register(new FindOptionsWidget(this._editor, this._state, this._keybindingService, this._themeService));\n    }\n};\nFindController = __decorate([\n    __param(1, IContextViewService),\n    __param(2, IContextKeyService),\n    __param(3, IKeybindingService),\n    __param(4, IThemeService),\n    __param(5, INotificationService),\n    __param(6, IStorageService),\n    __param(7, IClipboardService)\n], FindController);\nexport { FindController };\nexport const StartFindAction = registerMultiEditorAction(new MultiEditorAction({\n    id: FIND_IDS.StartFindAction,\n    label: nls.localize('startFindAction', \"Find\"),\n    alias: 'Find',\n    precondition: ContextKeyExpr.or(EditorContextKeys.focus, ContextKeyExpr.has('editorIsOpen')),\n    kbOpts: {\n        kbExpr: null,\n        primary: 2048 /* CtrlCmd */ | 36 /* KeyF */,\n        weight: 100 /* EditorContrib */\n    },\n    menuOpts: {\n        menuId: MenuId.MenubarEditMenu,\n        group: '3_find',\n        title: nls.localize({ key: 'miFind', comment: ['&& denotes a mnemonic'] }, \"&&Find\"),\n        order: 1\n    }\n}));\nStartFindAction.addImplementation(0, (accessor, editor, args) => {\n    const controller = CommonFindController.get(editor);\n    if (!controller) {\n        return false;\n    }\n    return controller.start({\n        forceRevealReplace: false,\n        seedSearchStringFromSelection: editor.getOption(35 /* find */).seedSearchStringFromSelection !== 'never' ? 'single' : 'none',\n        seedSearchStringFromNonEmptySelection: editor.getOption(35 /* find */).seedSearchStringFromSelection === 'selection',\n        seedSearchStringFromGlobalClipboard: editor.getOption(35 /* find */).globalFindClipboard,\n        shouldFocus: 1 /* FocusFindInput */,\n        shouldAnimate: true,\n        updateSearchScope: false,\n        loop: editor.getOption(35 /* find */).loop\n    });\n});\nconst findArgDescription = {\n    description: 'Open a new In-Editor Find Widget.',\n    args: [{\n            name: 'Open a new In-Editor Find Widget args',\n            schema: {\n                properties: {\n                    searchString: { type: 'string' },\n                    replaceString: { type: 'string' },\n                    regex: { type: 'boolean' },\n                    regexOverride: {\n                        type: 'number',\n                        description: nls.localize('actions.find.isRegexOverride', 'Overrides \"Use Regular Expression\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False')\n                    },\n                    wholeWord: { type: 'boolean' },\n                    wholeWordOverride: {\n                        type: 'number',\n                        description: nls.localize('actions.find.wholeWordOverride', 'Overrides \"Match Whole Word\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False')\n                    },\n                    matchCase: { type: 'boolean' },\n                    matchCaseOverride: {\n                        type: 'number',\n                        description: nls.localize('actions.find.matchCaseOverride', 'Overrides \"Math Case\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False')\n                    },\n                    preserveCase: { type: 'boolean' },\n                    preserveCaseOverride: {\n                        type: 'number',\n                        description: nls.localize('actions.find.preserveCaseOverride', 'Overrides \"Preserve Case\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False')\n                    },\n                    findInSelection: { type: 'boolean' },\n                }\n            }\n        }]\n};\nexport class StartFindWithArgsAction extends EditorAction {\n    constructor() {\n        super({\n            id: FIND_IDS.StartFindWithArgs,\n            label: nls.localize('startFindWithArgsAction', \"Find With Arguments\"),\n            alias: 'Find With Arguments',\n            precondition: undefined,\n            kbOpts: {\n                kbExpr: null,\n                primary: 0,\n                weight: 100 /* EditorContrib */\n            },\n            description: findArgDescription\n        });\n    }\n    run(accessor, editor, args) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const controller = CommonFindController.get(editor);\n            if (controller) {\n                const newState = args ? {\n                    searchString: args.searchString,\n                    replaceString: args.replaceString,\n                    isReplaceRevealed: args.replaceString !== undefined,\n                    isRegex: args.isRegex,\n                    // isRegexOverride: args.regexOverride,\n                    wholeWord: args.matchWholeWord,\n                    // wholeWordOverride: args.wholeWordOverride,\n                    matchCase: args.isCaseSensitive,\n                    // matchCaseOverride: args.matchCaseOverride,\n                    preserveCase: args.preserveCase,\n                    // preserveCaseOverride: args.preserveCaseOverride,\n                } : {};\n                yield controller.start({\n                    forceRevealReplace: false,\n                    seedSearchStringFromSelection: (controller.getState().searchString.length === 0) && editor.getOption(35 /* find */).seedSearchStringFromSelection !== 'never' ? 'single' : 'none',\n                    seedSearchStringFromNonEmptySelection: editor.getOption(35 /* find */).seedSearchStringFromSelection === 'selection',\n                    seedSearchStringFromGlobalClipboard: true,\n                    shouldFocus: 1 /* FocusFindInput */,\n                    shouldAnimate: true,\n                    updateSearchScope: (args === null || args === void 0 ? void 0 : args.findInSelection) || false,\n                    loop: editor.getOption(35 /* find */).loop\n                }, newState);\n                controller.setGlobalBufferTerm(controller.getState().searchString);\n            }\n        });\n    }\n}\nexport class StartFindWithSelectionAction extends EditorAction {\n    constructor() {\n        super({\n            id: FIND_IDS.StartFindWithSelection,\n            label: nls.localize('startFindWithSelectionAction', \"Find With Selection\"),\n            alias: 'Find With Selection',\n            precondition: undefined,\n            kbOpts: {\n                kbExpr: null,\n                primary: 0,\n                mac: {\n                    primary: 2048 /* CtrlCmd */ | 35 /* KeyE */,\n                },\n                weight: 100 /* EditorContrib */\n            }\n        });\n    }\n    run(accessor, editor) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const controller = CommonFindController.get(editor);\n            if (controller) {\n                yield controller.start({\n                    forceRevealReplace: false,\n                    seedSearchStringFromSelection: 'multiple',\n                    seedSearchStringFromNonEmptySelection: false,\n                    seedSearchStringFromGlobalClipboard: false,\n                    shouldFocus: 0 /* NoFocusChange */,\n                    shouldAnimate: true,\n                    updateSearchScope: false,\n                    loop: editor.getOption(35 /* find */).loop\n                });\n                controller.setGlobalBufferTerm(controller.getState().searchString);\n            }\n        });\n    }\n}\nexport class MatchFindAction extends EditorAction {\n    run(accessor, editor) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const controller = CommonFindController.get(editor);\n            if (controller && !this._run(controller)) {\n                yield controller.start({\n                    forceRevealReplace: false,\n                    seedSearchStringFromSelection: (controller.getState().searchString.length === 0) && editor.getOption(35 /* find */).seedSearchStringFromSelection !== 'never' ? 'single' : 'none',\n                    seedSearchStringFromNonEmptySelection: editor.getOption(35 /* find */).seedSearchStringFromSelection === 'selection',\n                    seedSearchStringFromGlobalClipboard: true,\n                    shouldFocus: 0 /* NoFocusChange */,\n                    shouldAnimate: true,\n                    updateSearchScope: false,\n                    loop: editor.getOption(35 /* find */).loop\n                });\n                this._run(controller);\n            }\n        });\n    }\n}\nexport class NextMatchFindAction extends MatchFindAction {\n    constructor() {\n        super({\n            id: FIND_IDS.NextMatchFindAction,\n            label: nls.localize('findNextMatchAction', \"Find Next\"),\n            alias: 'Find Next',\n            precondition: undefined,\n            kbOpts: [{\n                    kbExpr: EditorContextKeys.focus,\n                    primary: 61 /* F3 */,\n                    mac: { primary: 2048 /* CtrlCmd */ | 37 /* KeyG */, secondary: [61 /* F3 */] },\n                    weight: 100 /* EditorContrib */\n                }, {\n                    kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_FIND_INPUT_FOCUSED),\n                    primary: 3 /* Enter */,\n                    weight: 100 /* EditorContrib */\n                }]\n        });\n    }\n    _run(controller) {\n        const result = controller.moveToNextMatch();\n        if (result) {\n            controller.editor.pushUndoStop();\n            return true;\n        }\n        return false;\n    }\n}\nexport class PreviousMatchFindAction extends MatchFindAction {\n    constructor() {\n        super({\n            id: FIND_IDS.PreviousMatchFindAction,\n            label: nls.localize('findPreviousMatchAction', \"Find Previous\"),\n            alias: 'Find Previous',\n            precondition: undefined,\n            kbOpts: [{\n                    kbExpr: EditorContextKeys.focus,\n                    primary: 1024 /* Shift */ | 61 /* F3 */,\n                    mac: { primary: 2048 /* CtrlCmd */ | 1024 /* Shift */ | 37 /* KeyG */, secondary: [1024 /* Shift */ | 61 /* F3 */] },\n                    weight: 100 /* EditorContrib */\n                }, {\n                    kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_FIND_INPUT_FOCUSED),\n                    primary: 1024 /* Shift */ | 3 /* Enter */,\n                    weight: 100 /* EditorContrib */\n                }\n            ]\n        });\n    }\n    _run(controller) {\n        return controller.moveToPrevMatch();\n    }\n}\nexport class SelectionMatchFindAction extends EditorAction {\n    run(accessor, editor) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const controller = CommonFindController.get(editor);\n            if (!controller) {\n                return;\n            }\n            const seedSearchStringFromNonEmptySelection = editor.getOption(35 /* find */).seedSearchStringFromSelection === 'selection';\n            let selectionSearchString = null;\n            if (editor.getOption(35 /* find */).seedSearchStringFromSelection !== 'never') {\n                selectionSearchString = getSelectionSearchString(editor, 'single', seedSearchStringFromNonEmptySelection);\n            }\n            if (selectionSearchString) {\n                controller.setSearchString(selectionSearchString);\n            }\n            if (!this._run(controller)) {\n                yield controller.start({\n                    forceRevealReplace: false,\n                    seedSearchStringFromSelection: editor.getOption(35 /* find */).seedSearchStringFromSelection !== 'never' ? 'single' : 'none',\n                    seedSearchStringFromNonEmptySelection: seedSearchStringFromNonEmptySelection,\n                    seedSearchStringFromGlobalClipboard: false,\n                    shouldFocus: 0 /* NoFocusChange */,\n                    shouldAnimate: true,\n                    updateSearchScope: false,\n                    loop: editor.getOption(35 /* find */).loop\n                });\n                this._run(controller);\n            }\n        });\n    }\n}\nexport class NextSelectionMatchFindAction extends SelectionMatchFindAction {\n    constructor() {\n        super({\n            id: FIND_IDS.NextSelectionMatchFindAction,\n            label: nls.localize('nextSelectionMatchFindAction', \"Find Next Selection\"),\n            alias: 'Find Next Selection',\n            precondition: undefined,\n            kbOpts: {\n                kbExpr: EditorContextKeys.focus,\n                primary: 2048 /* CtrlCmd */ | 61 /* F3 */,\n                weight: 100 /* EditorContrib */\n            }\n        });\n    }\n    _run(controller) {\n        return controller.moveToNextMatch();\n    }\n}\nexport class PreviousSelectionMatchFindAction extends SelectionMatchFindAction {\n    constructor() {\n        super({\n            id: FIND_IDS.PreviousSelectionMatchFindAction,\n            label: nls.localize('previousSelectionMatchFindAction', \"Find Previous Selection\"),\n            alias: 'Find Previous Selection',\n            precondition: undefined,\n            kbOpts: {\n                kbExpr: EditorContextKeys.focus,\n                primary: 2048 /* CtrlCmd */ | 1024 /* Shift */ | 61 /* F3 */,\n                weight: 100 /* EditorContrib */\n            }\n        });\n    }\n    _run(controller) {\n        return controller.moveToPrevMatch();\n    }\n}\nexport const StartFindReplaceAction = registerMultiEditorAction(new MultiEditorAction({\n    id: FIND_IDS.StartFindReplaceAction,\n    label: nls.localize('startReplace', \"Replace\"),\n    alias: 'Replace',\n    precondition: ContextKeyExpr.or(EditorContextKeys.focus, ContextKeyExpr.has('editorIsOpen')),\n    kbOpts: {\n        kbExpr: null,\n        primary: 2048 /* CtrlCmd */ | 38 /* KeyH */,\n        mac: { primary: 2048 /* CtrlCmd */ | 512 /* Alt */ | 36 /* KeyF */ },\n        weight: 100 /* EditorContrib */\n    },\n    menuOpts: {\n        menuId: MenuId.MenubarEditMenu,\n        group: '3_find',\n        title: nls.localize({ key: 'miReplace', comment: ['&& denotes a mnemonic'] }, \"&&Replace\"),\n        order: 2\n    }\n}));\nStartFindReplaceAction.addImplementation(0, (accessor, editor, args) => {\n    if (!editor.hasModel() || editor.getOption(81 /* readOnly */)) {\n        return false;\n    }\n    const controller = CommonFindController.get(editor);\n    if (!controller) {\n        return false;\n    }\n    const currentSelection = editor.getSelection();\n    const findInputFocused = controller.isFindInputFocused();\n    // we only seed search string from selection when the current selection is single line and not empty,\n    // + the find input is not focused\n    const seedSearchStringFromSelection = !currentSelection.isEmpty()\n        && currentSelection.startLineNumber === currentSelection.endLineNumber\n        && (editor.getOption(35 /* find */).seedSearchStringFromSelection !== 'never')\n        && !findInputFocused;\n    /*\n    * if the existing search string in find widget is empty and we don't seed search string from selection, it means the Find Input is still empty, so we should focus the Find Input instead of Replace Input.\n\n    * findInputFocused true -> seedSearchStringFromSelection false, FocusReplaceInput\n    * findInputFocused false, seedSearchStringFromSelection true FocusReplaceInput\n    * findInputFocused false seedSearchStringFromSelection false FocusFindInput\n    */\n    const shouldFocus = (findInputFocused || seedSearchStringFromSelection) ?\n        2 /* FocusReplaceInput */ : 1 /* FocusFindInput */;\n    return controller.start({\n        forceRevealReplace: true,\n        seedSearchStringFromSelection: seedSearchStringFromSelection ? 'single' : 'none',\n        seedSearchStringFromNonEmptySelection: editor.getOption(35 /* find */).seedSearchStringFromSelection === 'selection',\n        seedSearchStringFromGlobalClipboard: editor.getOption(35 /* find */).seedSearchStringFromSelection !== 'never',\n        shouldFocus: shouldFocus,\n        shouldAnimate: true,\n        updateSearchScope: false,\n        loop: editor.getOption(35 /* find */).loop\n    });\n});\nregisterEditorContribution(CommonFindController.ID, FindController);\nregisterEditorAction(StartFindWithArgsAction);\nregisterEditorAction(StartFindWithSelectionAction);\nregisterEditorAction(NextMatchFindAction);\nregisterEditorAction(PreviousMatchFindAction);\nregisterEditorAction(NextSelectionMatchFindAction);\nregisterEditorAction(PreviousSelectionMatchFindAction);\nconst FindCommand = EditorCommand.bindToContribution(CommonFindController.get);\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.CloseFindWidgetCommand,\n    precondition: CONTEXT_FIND_WIDGET_VISIBLE,\n    handler: x => x.closeFindWidget(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, ContextKeyExpr.not('isComposing')),\n        primary: 9 /* Escape */,\n        secondary: [1024 /* Shift */ | 9 /* Escape */]\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ToggleCaseSensitiveCommand,\n    precondition: undefined,\n    handler: x => x.toggleCaseSensitive(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: ToggleCaseSensitiveKeybinding.primary,\n        mac: ToggleCaseSensitiveKeybinding.mac,\n        win: ToggleCaseSensitiveKeybinding.win,\n        linux: ToggleCaseSensitiveKeybinding.linux\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ToggleWholeWordCommand,\n    precondition: undefined,\n    handler: x => x.toggleWholeWords(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: ToggleWholeWordKeybinding.primary,\n        mac: ToggleWholeWordKeybinding.mac,\n        win: ToggleWholeWordKeybinding.win,\n        linux: ToggleWholeWordKeybinding.linux\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ToggleRegexCommand,\n    precondition: undefined,\n    handler: x => x.toggleRegex(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: ToggleRegexKeybinding.primary,\n        mac: ToggleRegexKeybinding.mac,\n        win: ToggleRegexKeybinding.win,\n        linux: ToggleRegexKeybinding.linux\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ToggleSearchScopeCommand,\n    precondition: undefined,\n    handler: x => x.toggleSearchScope(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: ToggleSearchScopeKeybinding.primary,\n        mac: ToggleSearchScopeKeybinding.mac,\n        win: ToggleSearchScopeKeybinding.win,\n        linux: ToggleSearchScopeKeybinding.linux\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.TogglePreserveCaseCommand,\n    precondition: undefined,\n    handler: x => x.togglePreserveCase(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: TogglePreserveCaseKeybinding.primary,\n        mac: TogglePreserveCaseKeybinding.mac,\n        win: TogglePreserveCaseKeybinding.win,\n        linux: TogglePreserveCaseKeybinding.linux\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ReplaceOneAction,\n    precondition: CONTEXT_FIND_WIDGET_VISIBLE,\n    handler: x => x.replace(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: 2048 /* CtrlCmd */ | 1024 /* Shift */ | 22 /* Digit1 */\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ReplaceOneAction,\n    precondition: CONTEXT_FIND_WIDGET_VISIBLE,\n    handler: x => x.replace(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_REPLACE_INPUT_FOCUSED),\n        primary: 3 /* Enter */\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ReplaceAllAction,\n    precondition: CONTEXT_FIND_WIDGET_VISIBLE,\n    handler: x => x.replaceAll(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: 2048 /* CtrlCmd */ | 512 /* Alt */ | 3 /* Enter */\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.ReplaceAllAction,\n    precondition: CONTEXT_FIND_WIDGET_VISIBLE,\n    handler: x => x.replaceAll(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_REPLACE_INPUT_FOCUSED),\n        primary: undefined,\n        mac: {\n            primary: 2048 /* CtrlCmd */ | 3 /* Enter */,\n        }\n    }\n}));\nregisterEditorCommand(new FindCommand({\n    id: FIND_IDS.SelectAllMatchesAction,\n    precondition: CONTEXT_FIND_WIDGET_VISIBLE,\n    handler: x => x.selectAllMatches(),\n    kbOpts: {\n        weight: 100 /* EditorContrib */ + 5,\n        kbExpr: EditorContextKeys.focus,\n        primary: 512 /* Alt */ | 3 /* Enter */\n    }\n}));\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASO,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACzB,YAAY,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,eAAe,CAAC;AACrB,SAAK,uCAAuC,CAAC;AAC7C,SAAK,0BAA0B,CAAC;AAChC,SAAK,8BAA8B;AACnC,SAAK,2BAA2B;AAChC,SAAK,iBAAiB,KAAK,QAAQ,YAAY;AAAA,EACnD;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,iBAAiB,KAAK,gBAAgB,GAAG,CAAC,CAAC;AACxD,SAAK,eAAe,CAAC;AACrB,SAAK,uCAAuC,CAAC;AAC7C,SAAK,0BAA0B,CAAC;AAChC,SAAK,8BAA8B;AACnC,SAAK,2BAA2B;AAAA,EACpC;AAAA,EACA,QAAQ;AACJ,SAAK,eAAe,CAAC;AACrB,SAAK,uCAAuC,CAAC;AAC7C,SAAK,0BAA0B,CAAC;AAChC,SAAK,8BAA8B;AACnC,SAAK,2BAA2B;AAAA,EACpC;AAAA,EACA,WAAW;AACP,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA;AAAA,EAEA,eAAe;AACX,QAAI,KAAK,wBAAwB,CAAC,GAAG;AACjC,aAAO,KAAK,QAAQ,SAAS,EAAE,mBAAmB,KAAK,wBAAwB,CAAC,CAAC;AAAA,IACrF;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB;AACZ,QAAI,KAAK,wBAAwB,QAAQ;AACrC,YAAM,SAAS,KAAK,wBAAwB,IAAI,2BAAyB,KAAK,QAAQ,SAAS,EAAE,mBAAmB,qBAAqB,CAAC,EAAE,OAAO,aAAW,CAAC,CAAC,OAAO;AACvK,UAAI,OAAO,QAAQ;AACf,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB;AACf,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,iBAAiB,kBAAkB;AAC/B,SAAK,iBAAiB;AACtB,SAAK,oBAAoB,IAAI;AAAA,EACjC;AAAA,EACA,oBAAoB,cAAc;AAC9B,UAAM,QAAQ,KAAK,aAAa,QAAQ,YAAY;AACpD,QAAI,SAAS,GAAG;AACZ,aAAO,QAAQ;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AAAA,EACA,0BAA0B,cAAc;AACpC,QAAI,aAAa,KAAK,QAAQ,SAAS,EAAE,sBAAsB,YAAY;AAC3E,eAAW,aAAa,YAAY;AAChC,YAAM,gBAAgB,UAAU;AAChC,UAAI,kBAAkB,iBAAgB,0BAA0B,kBAAkB,iBAAgB,gCAAgC;AAC9H,eAAO,KAAK,oBAAoB,UAAU,EAAE;AAAA,MAChD;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EACA,oBAAoB,WAAW;AAC3B,QAAI,yBAAyB;AAC7B,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACX,eAAS,IAAI,GAAG,MAAM,KAAK,aAAa,QAAQ,IAAI,KAAK,KAAK;AAC1D,YAAI,QAAQ,KAAK,QAAQ,SAAS,EAAE,mBAAmB,KAAK,aAAa,CAAC,CAAC;AAC3E,YAAI,UAAU,YAAY,KAAK,GAAG;AAC9B,mCAAyB,KAAK,aAAa,CAAC;AAC5C,0BAAiB,IAAI;AACrB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,KAAK,6BAA6B,QAAQ,2BAA2B,MAAM;AAC3E,WAAK,QAAQ,kBAAkB,CAAC,mBAAmB;AAC/C,YAAI,KAAK,6BAA6B,MAAM;AACxC,yBAAe,wBAAwB,KAAK,0BAA0B,iBAAgB,sBAAsB;AAC5G,eAAK,2BAA2B;AAAA,QACpC;AACA,YAAI,2BAA2B,MAAM;AACjC,eAAK,2BAA2B;AAChC,yBAAe,wBAAwB,KAAK,0BAA0B,iBAAgB,8BAA8B;AAAA,QACxH;AACA,YAAI,KAAK,gCAAgC,MAAM;AAC3C,yBAAe,iBAAiB,KAAK,2BAA2B;AAChE,eAAK,8BAA8B;AAAA,QACvC;AACA,YAAI,2BAA2B,MAAM;AACjC,cAAI,MAAM,KAAK,QAAQ,SAAS,EAAE,mBAAmB,sBAAsB;AAC3E,cAAI,IAAI,oBAAoB,IAAI,iBAAiB,IAAI,cAAc,GAAG;AAClE,gBAAI,gBAAgB,IAAI,gBAAgB;AACxC,gBAAI,yBAAyB,KAAK,QAAQ,SAAS,EAAE,iBAAiB,aAAa;AACnF,kBAAM,IAAI,MAAM,IAAI,iBAAiB,IAAI,aAAa,eAAe,sBAAsB;AAAA,UAC/F;AACA,eAAK,8BAA8B,eAAe,cAAc,KAAK,iBAAgB,2BAA2B;AAAA,QACpH;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,aAAa,YAAY;AACzB,SAAK,QAAQ,kBAAkB,CAAC,aAAa;AACzC,UAAI,qBAAqB,iBAAgB;AACzC,UAAI,yCAAyC,CAAC;AAC9C,UAAI,YAAY,SAAS,KAAM;AAG3B,6BAAqB,iBAAgB;AAErC,cAAM,YAAY,KAAK,QAAQ,SAAS,EAAE,aAAa;AACvD,cAAM,SAAS,KAAK,QAAQ,cAAc,EAAE;AAC5C,cAAM,sBAAsB,SAAS;AACrC,cAAM,kBAAkB,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,mBAAmB,CAAC;AAEtE,YAAI,sBAAsB,YAAY,CAAC,EAAE,MAAM;AAC/C,YAAI,oBAAoB,YAAY,CAAC,EAAE,MAAM;AAC7C,iBAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACpD,gBAAM,QAAQ,YAAY,CAAC,EAAE;AAC7B,cAAI,oBAAoB,mBAAmB,MAAM,iBAAiB;AAC9D,gBAAI,MAAM,gBAAgB,mBAAmB;AACzC,kCAAoB,MAAM;AAAA,YAC9B;AAAA,UACJ,OACK;AACD,mDAAuC,KAAK;AAAA,cACxC,OAAO,IAAI,MAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAAA,cAC7D,SAAS,iBAAgB;AAAA,YAC7B,CAAC;AACD,kCAAsB,MAAM;AAC5B,gCAAoB,MAAM;AAAA,UAC9B;AAAA,QACJ;AACA,+CAAuC,KAAK;AAAA,UACxC,OAAO,IAAI,MAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAAA,UAC7D,SAAS,iBAAgB;AAAA,QAC7B,CAAC;AAAA,MACL;AAEA,UAAI,4BAA4B,IAAI,MAAM,YAAY,MAAM;AAC5D,eAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACpD,kCAA0B,CAAC,IAAI;AAAA,UAC3B,OAAO,YAAY,CAAC,EAAE;AAAA,UACtB,SAAS;AAAA,QACb;AAAA,MACJ;AACA,WAAK,eAAe,SAAS,iBAAiB,KAAK,cAAc,yBAAyB;AAE1F,WAAK,uCAAuC,SAAS,iBAAiB,KAAK,sCAAsC,sCAAsC;AAEvJ,UAAI,KAAK,6BAA6B;AAClC,iBAAS,iBAAiB,KAAK,2BAA2B;AAC1D,aAAK,8BAA8B;AAAA,MACvC;AAEA,UAAI,KAAK,wBAAwB,QAAQ;AACrC,aAAK,wBAAwB,QAAQ,2BAAyB,SAAS,iBAAiB,qBAAqB,CAAC;AAC9G,aAAK,0BAA0B,CAAC;AAAA,MACpC;AACA,UAAI,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,QAAQ;AAC3E,aAAK,0BAA0B,WAAW,IAAI,eAAa,SAAS,cAAc,WAAW,iBAAgB,sBAAsB,CAAC;AAAA,MACxI;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,UAAU;AAC1B,QAAI,KAAK,aAAa,WAAW,GAAG;AAChC,aAAO;AAAA,IACX;AACA,aAAS,IAAI,KAAK,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AACpD,UAAI,eAAe,KAAK,aAAa,CAAC;AACtC,UAAI,IAAI,KAAK,QAAQ,SAAS,EAAE,mBAAmB,YAAY;AAC/D,UAAI,CAAC,KAAK,EAAE,gBAAgB,SAAS,YAAY;AAC7C;AAAA,MACJ;AACA,UAAI,EAAE,gBAAgB,SAAS,YAAY;AACvC,eAAO;AAAA,MACX;AACA,UAAI,EAAE,YAAY,SAAS,QAAQ;AAC/B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO,KAAK,QAAQ,SAAS,EAAE,mBAAmB,KAAK,aAAa,KAAK,aAAa,SAAS,CAAC,CAAC;AAAA,EACrG;AAAA,EACA,mBAAmB,UAAU;AACzB,QAAI,KAAK,aAAa,WAAW,GAAG;AAChC,aAAO;AAAA,IACX;AACA,aAAS,IAAI,GAAG,MAAM,KAAK,aAAa,QAAQ,IAAI,KAAK,KAAK;AAC1D,UAAI,eAAe,KAAK,aAAa,CAAC;AACtC,UAAI,IAAI,KAAK,QAAQ,SAAS,EAAE,mBAAmB,YAAY;AAC/D,UAAI,CAAC,KAAK,EAAE,kBAAkB,SAAS,YAAY;AAC/C;AAAA,MACJ;AACA,UAAI,EAAE,kBAAkB,SAAS,YAAY;AACzC,eAAO;AAAA,MACX;AACA,UAAI,EAAE,cAAc,SAAS,QAAQ;AACjC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO,KAAK,QAAQ,SAAS,EAAE,mBAAmB,KAAK,aAAa,CAAC,CAAC;AAAA,EAC1E;AAAA,EACA,kBAAkB;AACd,QAAI,SAAS,CAAC;AACd,aAAS,OAAO,OAAO,KAAK,YAAY;AACxC,aAAS,OAAO,OAAO,KAAK,oCAAoC;AAChE,QAAI,KAAK,wBAAwB,QAAQ;AACrC,aAAO,KAAK,GAAG,KAAK,uBAAuB;AAAA,IAC/C;AACA,QAAI,KAAK,6BAA6B;AAClC,aAAO,KAAK,KAAK,2BAA2B;AAAA,IAChD;AACA,WAAO;AAAA,EACX;AACJ;AACA,gBAAgB,iCAAiC,uBAAuB,SAAS;AAAA,EAC7E,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,eAAe;AAAA,IACX,OAAO,iBAAiB,gCAAgC;AAAA,IACxD,UAAU,kBAAkB;AAAA,EAChC;AAAA,EACA,SAAS;AAAA,IACL,OAAO,iBAAiB,gBAAgB;AAAA,IACxC,UAAU,gBAAgB;AAAA,EAC9B;AACJ,CAAC;AACD,gBAAgB,yBAAyB,uBAAuB,SAAS;AAAA,EACrE,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,eAAe;AAAA,IACX,OAAO,iBAAiB,gCAAgC;AAAA,IACxD,UAAU,kBAAkB;AAAA,EAChC;AAAA,EACA,SAAS;AAAA,IACL,OAAO,iBAAiB,gBAAgB;AAAA,IACxC,UAAU,gBAAgB;AAAA,EAC9B;AACJ,CAAC;AACD,gBAAgB,qCAAqC,uBAAuB,SAAS;AAAA,EACjF,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,iBAAiB;AACrB,CAAC;AACD,gBAAgB,uCAAuC,uBAAuB,SAAS;AAAA,EACnF,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,IACX,OAAO,iBAAiB,gCAAgC;AAAA,IACxD,UAAU,kBAAkB;AAAA,EAChC;AACJ,CAAC;AACD,gBAAgB,8BAA8B,uBAAuB,SAAS;AAAA,EAC1E,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AACjB,CAAC;AACD,gBAAgB,yBAAyB,uBAAuB,SAAS;AAAA,EACrE,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AACjB,CAAC;;;AC3RM,IAAM,oBAAN,MAAwB;AAAA,EAC3B,YAAY,iBAAiB,QAAQ,gBAAgB;AACjD,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,4BAA4B;AAAA,EACrC;AAAA,EACA,kBAAkB,OAAO,SAAS;AAC9B,QAAI,KAAK,QAAQ,SAAS,GAAG;AAEzB,UAAI,MAAM,CAAC;AACX,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,YAAI,KAAK;AAAA,UACL,OAAO,KAAK,QAAQ,CAAC;AAAA,UACrB,MAAM,KAAK,gBAAgB,CAAC;AAAA,QAChC,CAAC;AAAA,MACL;AAEA,UAAI,KAAK,CAAC,IAAI,OAAO;AACjB,eAAO,MAAM,yBAAyB,GAAG,OAAO,GAAG,KAAK;AAAA,MAC5D,CAAC;AAED,UAAI,YAAY,CAAC;AACjB,UAAI,aAAa,IAAI,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,YAAI,WAAW,MAAM,kBAAkB,IAAI,CAAC,EAAE,MAAM,mBAAmB,WAAW,MAAM,cAAc,IAAI,CAAC,EAAE,MAAM,aAAa;AAE5H,qBAAW,QAAQ,WAAW,MAAM,UAAU,IAAI,CAAC,EAAE,KAAK;AAC1D,qBAAW,OAAO,WAAW,OAAO,IAAI,CAAC,EAAE;AAAA,QAC/C,OACK;AACD,oBAAU,KAAK,UAAU;AACzB,uBAAa,IAAI,CAAC;AAAA,QACtB;AAAA,MACJ;AACA,gBAAU,KAAK,UAAU;AACzB,iBAAW,MAAM,WAAW;AACxB,gBAAQ,iBAAiB,GAAG,OAAO,GAAG,IAAI;AAAA,MAC9C;AAAA,IACJ;AACA,SAAK,4BAA4B,QAAQ,eAAe,KAAK,gBAAgB;AAAA,EACjF;AAAA,EACA,mBAAmB,OAAO,QAAQ;AAC9B,WAAO,OAAO,oBAAoB,KAAK,yBAAyB;AAAA,EACpE;AACJ;;;AC7CO,SAAS,oCAAoC,SAAS,SAAS;AAClE,MAAI,WAAY,QAAQ,CAAC,MAAM,IAAK;AAChC,UAAM,kBAAkB,iCAAiC,SAAS,SAAS,GAAG;AAC9E,UAAM,sBAAsB,iCAAiC,SAAS,SAAS,GAAG;AAClF,QAAI,mBAAmB,CAAC,qBAAqB;AACzC,aAAO,8CAA8C,SAAS,SAAS,GAAG;AAAA,IAC9E,WACS,CAAC,mBAAmB,qBAAqB;AAC9C,aAAO,8CAA8C,SAAS,SAAS,GAAG;AAAA,IAC9E;AACA,QAAI,QAAQ,CAAC,EAAE,YAAY,MAAM,QAAQ,CAAC,GAAG;AACzC,aAAO,QAAQ,YAAY;AAAA,IAC/B,WACS,QAAQ,CAAC,EAAE,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC9C,aAAO,QAAQ,YAAY;AAAA,IAC/B,WACiB,2BAA2B,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,SAAS,GAAG;AAC9E,aAAO,QAAQ,CAAC,EAAE,YAAY,IAAI,QAAQ,OAAO,CAAC;AAAA,IACtD,WACS,QAAQ,CAAC,EAAE,CAAC,EAAE,YAAY,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,QAAQ,SAAS,GAAG;AAC1E,aAAO,QAAQ,CAAC,EAAE,YAAY,IAAI,QAAQ,OAAO,CAAC;AAAA,IACtD,OACK;AAED,aAAO;AAAA,IACX;AAAA,EACJ,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,iCAAiC,SAAS,SAAS,kBAAkB;AAC1E,QAAM,8BAA8B,QAAQ,CAAC,EAAE,QAAQ,gBAAgB,MAAM,MAAM,QAAQ,QAAQ,gBAAgB,MAAM;AACzH,SAAO,+BAA+B,QAAQ,CAAC,EAAE,MAAM,gBAAgB,EAAE,WAAW,QAAQ,MAAM,gBAAgB,EAAE;AACxH;AACA,SAAS,8CAA8C,SAAS,SAAS,kBAAkB;AACvF,QAAM,iCAAiC,QAAQ,MAAM,gBAAgB;AACrE,QAAM,+BAA+B,QAAQ,CAAC,EAAE,MAAM,gBAAgB;AACtE,MAAI,gBAAgB;AACpB,iCAA+B,QAAQ,CAAC,YAAY,UAAU;AAC1D,qBAAiB,oCAAoC,CAAC,6BAA6B,KAAK,CAAC,GAAG,UAAU,IAAI;AAAA,EAC9G,CAAC;AACD,SAAO,cAAc,MAAM,GAAG,EAAE;AACpC;;;ACxCA,IAAM,4BAAN,MAAgC;AAAA,EAC5B,YAAY,aAAa;AACrB,SAAK,cAAc;AACnB,SAAK,OAAO;AAAA,EAChB;AACJ;AAIA,IAAM,8BAAN,MAAkC;AAAA,EAC9B,YAAY,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AACJ;AACO,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACxB,YAAY,QAAQ;AAChB,QAAI,CAAC,UAAU,OAAO,WAAW,GAAG;AAChC,WAAK,SAAS,IAAI,0BAA0B,EAAE;AAAA,IAClD,WACS,OAAO,WAAW,KAAK,OAAO,CAAC,EAAE,gBAAgB,MAAM;AAC5D,WAAK,SAAS,IAAI,0BAA0B,OAAO,CAAC,EAAE,WAAW;AAAA,IACrE,OACK;AACD,WAAK,SAAS,IAAI,4BAA4B,MAAM;AAAA,IACxD;AAAA,EACJ;AAAA,EACA,OAAO,gBAAgB,OAAO;AAC1B,WAAO,IAAI,gBAAe,CAAC,aAAa,YAAY,KAAK,CAAC,CAAC;AAAA,EAC/D;AAAA,EACA,IAAI,yBAAyB;AACzB,WAAQ,KAAK,OAAO,SAAS;AAAA,EACjC;AAAA,EACA,mBAAmB,SAAS,cAAc;AACtC,QAAI,KAAK,OAAO,SAAS,GAAqB;AAC1C,UAAI,cAAc;AACd,eAAO,oCAAoC,SAAS,KAAK,OAAO,WAAW;AAAA,MAC/E,OACK;AACD,eAAO,KAAK,OAAO;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,MAAM,KAAK,OAAO,OAAO,QAAQ,IAAI,KAAK,KAAK;AAC3D,UAAI,QAAQ,KAAK,OAAO,OAAO,CAAC;AAChC,UAAI,MAAM,gBAAgB,MAAM;AAE5B,kBAAU,MAAM;AAChB;AAAA,MACJ;AAEA,UAAI,QAAQ,gBAAe,YAAY,MAAM,YAAY,OAAO;AAChE,UAAI,MAAM,YAAY,QAAQ,MAAM,QAAQ,SAAS,GAAG;AACpD,YAAI,OAAO,CAAC;AACZ,YAAI,SAAS,MAAM,QAAQ;AAC3B,YAAI,QAAQ;AACZ,iBAAS,MAAM,GAAGA,OAAM,MAAM,QAAQ,MAAMA,MAAK,OAAO;AACpD,cAAI,SAAS,QAAQ;AACjB,iBAAK,KAAK,MAAM,MAAM,GAAG,CAAC;AAC1B;AAAA,UACJ;AACA,kBAAQ,MAAM,QAAQ,KAAK,GAAG;AAAA,YAC1B,KAAK;AACD,mBAAK,KAAK,MAAM,GAAG,EAAE,YAAY,CAAC;AAClC;AAAA,YACJ,KAAK;AACD,mBAAK,KAAK,MAAM,GAAG,EAAE,YAAY,CAAC;AAClC;AACA;AAAA,YACJ,KAAK;AACD,mBAAK,KAAK,MAAM,GAAG,EAAE,YAAY,CAAC;AAClC;AAAA,YACJ,KAAK;AACD,mBAAK,KAAK,MAAM,GAAG,EAAE,YAAY,CAAC;AAClC;AACA;AAAA,YACJ;AACI,mBAAK,KAAK,MAAM,GAAG,CAAC;AAAA,UAC5B;AAAA,QACJ;AACA,gBAAQ,KAAK,KAAK,EAAE;AAAA,MACxB;AACA,gBAAU;AAAA,IACd;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,YAAY,YAAY,SAAS;AACpC,QAAI,YAAY,MAAM;AAClB,aAAO;AAAA,IACX;AACA,QAAI,eAAe,GAAG;AAClB,aAAO,QAAQ,CAAC;AAAA,IACpB;AACA,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG;AACnB,UAAI,aAAa,QAAQ,QAAQ;AAE7B,YAAI,QAAS,QAAQ,UAAU,KAAK;AACpC,eAAO,QAAQ;AAAA,MACnB;AACA,kBAAY,OAAO,aAAa,EAAE,IAAI;AACtC,mBAAa,KAAK,MAAM,aAAa,EAAE;AAAA,IAC3C;AACA,WAAO,MAAM;AAAA,EACjB;AACJ;AAIO,IAAM,eAAN,MAAM,cAAa;AAAA,EACtB,YAAY,aAAa,YAAY,SAAS;AAC1C,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,QAAI,CAAC,WAAW,QAAQ,WAAW,GAAG;AAClC,WAAK,UAAU;AAAA,IACnB,OACK;AACD,WAAK,UAAU,QAAQ,MAAM,CAAC;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,OAAO,YAAY,OAAO;AACtB,WAAO,IAAI,cAAa,OAAO,IAAI,IAAI;AAAA,EAC3C;AAAA,EACA,OAAO,QAAQ,OAAO,SAAS;AAC3B,WAAO,IAAI,cAAa,MAAM,OAAO,OAAO;AAAA,EAChD;AACJ;AACA,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAAA,EAC/B;AAAA,EACA,cAAc,aAAa;AACvB,SAAK,YAAY,KAAK,QAAQ,UAAU,KAAK,gBAAgB,WAAW,CAAC;AACzE,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,WAAW,OAAO,aAAa;AAC3B,SAAK,YAAY,KAAK;AACtB,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,YAAY,OAAO;AACf,QAAI,MAAM,WAAW,GAAG;AACpB;AAAA,IACJ;AACA,SAAK,uBAAuB;AAAA,EAChC;AAAA,EACA,eAAe,OAAO,aAAa,SAAS;AACxC,QAAI,KAAK,oBAAoB,WAAW,GAAG;AACvC,WAAK,QAAQ,KAAK,YAAY,IAAI,aAAa,YAAY,KAAK,mBAAmB;AACnF,WAAK,sBAAsB;AAAA,IAC/B;AACA,SAAK,QAAQ,KAAK,YAAY,IAAI,aAAa,QAAQ,OAAO,OAAO;AACrE,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,WAAW;AACP,SAAK,cAAc,KAAK,QAAQ,MAAM;AACtC,QAAI,KAAK,oBAAoB,WAAW,GAAG;AACvC,WAAK,QAAQ,KAAK,YAAY,IAAI,aAAa,YAAY,KAAK,mBAAmB;AACnF,WAAK,sBAAsB;AAAA,IAC/B;AACA,WAAO,IAAI,eAAe,KAAK,OAAO;AAAA,EAC1C;AACJ;AAgBO,SAAS,mBAAmB,eAAe;AAC9C,MAAI,CAAC,iBAAiB,cAAc,WAAW,GAAG;AAC9C,WAAO,IAAI,eAAe,IAAI;AAAA,EAClC;AACA,MAAI,UAAU,CAAC;AACf,MAAI,SAAS,IAAI,oBAAoB,aAAa;AAClD,WAAS,IAAI,GAAG,MAAM,cAAc,QAAQ,IAAI,KAAK,KAAK;AACtD,QAAI,SAAS,cAAc,WAAW,CAAC;AACvC,QAAI,WAAW,IAAoB;AAE/B;AACA,UAAI,KAAK,KAAK;AAEV;AAAA,MACJ;AACA,UAAI,aAAa,cAAc,WAAW,CAAC;AAE3C,cAAQ,YAAY;AAAA,QAChB,KAAK;AAED,iBAAO,cAAc,IAAI,CAAC;AAC1B,iBAAO,WAAW,MAAM,IAAI,CAAC;AAC7B;AAAA,QACJ,KAAK;AAED,iBAAO,cAAc,IAAI,CAAC;AAC1B,iBAAO,WAAW,MAAM,IAAI,CAAC;AAC7B;AAAA,QACJ,KAAK;AAED,iBAAO,cAAc,IAAI,CAAC;AAC1B,iBAAO,WAAW,KAAM,IAAI,CAAC;AAC7B;AAAA,QAGJ,KAAK;AAAA,QAEL,KAAK;AAAA,QAEL,KAAK;AAAA,QAEL,KAAK;AAED,iBAAO,cAAc,IAAI,CAAC;AAC1B,iBAAO,WAAW,IAAI,IAAI,CAAC;AAC3B,kBAAQ,KAAK,OAAO,aAAa,UAAU,CAAC;AAC5C;AAAA,MACR;AACA;AAAA,IACJ;AACA,QAAI,WAAW,IAAqB;AAEhC;AACA,UAAI,KAAK,KAAK;AAEV;AAAA,MACJ;AACA,UAAI,aAAa,cAAc,WAAW,CAAC;AAC3C,UAAI,eAAe,IAAqB;AAEpC,eAAO,cAAc,IAAI,CAAC;AAC1B,eAAO,WAAW,KAAK,IAAI,CAAC;AAC5B;AAAA,MACJ;AACA,UAAI,eAAe,MAAmB,eAAe,IAAoB;AAErE,eAAO,cAAc,IAAI,CAAC;AAC1B,eAAO,eAAe,GAAG,IAAI,GAAG,OAAO;AACvC,gBAAQ,SAAS;AACjB;AAAA,MACJ;AACA,UAAI,MAAmB,cAAc,cAAc,IAAiB;AAEhE,YAAI,aAAa,aAAa;AAE9B,YAAI,IAAI,IAAI,KAAK;AACb,cAAI,iBAAiB,cAAc,WAAW,IAAI,CAAC;AACnD,cAAI,MAAmB,kBAAkB,kBAAkB,IAAiB;AAGxE;AACA,yBAAa,aAAa,MAAM,iBAAiB;AACjD,mBAAO,cAAc,IAAI,CAAC;AAC1B,mBAAO,eAAe,YAAY,IAAI,GAAG,OAAO;AAChD,oBAAQ,SAAS;AACjB;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,cAAc,IAAI,CAAC;AAC1B,eAAO,eAAe,YAAY,IAAI,GAAG,OAAO;AAChD,gBAAQ,SAAS;AACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,OAAO,SAAS;AAC3B;;;AC7QO,IAAM,8BAA8B,IAAI,cAAc,qBAAqB,KAAK;AAChF,IAAM,kCAAkC,4BAA4B,UAAU;AAE9E,IAAM,6BAA6B,IAAI,cAAc,qBAAqB,KAAK;AAC/E,IAAM,gCAAgC,IAAI,cAAc,wBAAwB,KAAK;AACrF,IAAM,gCAAgC;AAAA,EACzC,SAAS,MAAgB;AAAA,EACzB,KAAK;AAAA,IAAE,SAAS,OAAqB,MAAgB;AAAA;AAAA,EAAc;AACvE;AACO,IAAM,4BAA4B;AAAA,EACrC,SAAS,MAAgB;AAAA,EACzB,KAAK;AAAA,IAAE,SAAS,OAAqB,MAAgB;AAAA;AAAA,EAAc;AACvE;AACO,IAAM,wBAAwB;AAAA,EACjC,SAAS,MAAgB;AAAA,EACzB,KAAK;AAAA,IAAE,SAAS,OAAqB,MAAgB;AAAA;AAAA,EAAc;AACvE;AACO,IAAM,8BAA8B;AAAA,EACvC,SAAS,MAAgB;AAAA,EACzB,KAAK;AAAA,IAAE,SAAS,OAAqB,MAAgB;AAAA;AAAA,EAAc;AACvE;AACO,IAAM,+BAA+B;AAAA,EACxC,SAAS,MAAgB;AAAA,EACzB,KAAK;AAAA,IAAE,SAAS,OAAqB,MAAgB;AAAA;AAAA,EAAc;AACvE;AACO,IAAM,WAAW;AAAA,EACpB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,wBAAwB;AAC5B;AACO,IAAM,gBAAgB;AAC7B,IAAM,iBAAiB;AAChB,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EACrC,YAAY,QAAQ,OAAO;AACvB,SAAK,aAAa,IAAI,gBAAgB;AACtC,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,uBAAuB,IAAI,aAAa;AAC7C,SAAK,eAAe,IAAI,gBAAgB,MAAM;AAC9C,SAAK,WAAW,IAAI,KAAK,YAAY;AACrC,SAAK,8BAA8B,IAAI,iBAAiB,MAAM,KAAK,SAAS,KAAK,GAAG,GAAG;AACvF,SAAK,WAAW,IAAI,KAAK,2BAA2B;AACpD,SAAK,WAAW,IAAI,KAAK,QAAQ,0BAA0B,CAAC,MAAM;AAC9D,UAAI,EAAE,WAAW,KACV,EAAE,WAAW,KACb,EAAE,WAAW,GAAc;AAC9B,aAAK,aAAa,iBAAiB,KAAK,QAAQ,YAAY,CAAC;AAAA,MACjE;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,6BAA6B;AAClC,SAAK,WAAW,IAAI,KAAK,QAAQ,wBAAwB,CAAC,MAAM;AAC5D,UAAI,KAAK,4BAA4B;AACjC;AAAA,MACJ;AACA,UAAI,EAAE,SAAS;AAEX,aAAK,aAAa,MAAM;AAAA,MAC5B;AACA,WAAK,aAAa,iBAAiB,KAAK,QAAQ,YAAY,CAAC;AAC7D,WAAK,4BAA4B,SAAS;AAAA,IAC9C,CAAC,CAAC;AACF,SAAK,WAAW,IAAI,KAAK,OAAO,yBAAyB,CAAC,MAAM,KAAK,gBAAgB,CAAC,CAAC,CAAC;AACxF,SAAK,SAAS,OAAO,KAAK,OAAO,WAAW;AAAA,EAChD;AAAA,EACA,UAAU;AACN,SAAK,cAAc;AACnB,YAAQ,KAAK,oBAAoB;AACjC,SAAK,WAAW,QAAQ;AAAA,EAC5B;AAAA,EACA,gBAAgB,GAAG;AACf,QAAI,KAAK,aAAa;AAElB;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,QAAQ,SAAS,GAAG;AAE1B;AAAA,IACJ;AACA,QAAI,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnG,UAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,UAAI,MAAM,qBAAqB,GAAG;AAC9B,aAAK,qBAAqB,OAAO;AACjC,aAAK,qBAAqB,YAAY,MAAM;AACxC,cAAI,EAAE,aAAa;AACf,iBAAK,SAAS,EAAE,YAAY,KAAK,OAAO,WAAW;AAAA,UACvD,OACK;AACD,iBAAK,SAAS,EAAE,UAAU;AAAA,UAC9B;AAAA,QACJ,GAAG,cAAc;AAAA,MACrB,OACK;AACD,YAAI,EAAE,aAAa;AACf,eAAK,SAAS,EAAE,YAAY,KAAK,OAAO,WAAW;AAAA,QACvD,OACK;AACD,eAAK,SAAS,EAAE,UAAU;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,gBAAgB,OAAO,WAAW;AAErC,QAAI,WAAW;AACX,aAAO;AAAA,IACX;AACA,WAAO,MAAM,kBAAkB;AAAA,EACnC;AAAA,EACA,SAAS,YAAY,cAAc;AAC/B,QAAI,aAAa;AACjB,QAAI,OAAO,iBAAiB,aAAa;AACrC,UAAI,iBAAiB,MAAM;AACvB,YAAI,CAAC,MAAM,QAAQ,YAAY,GAAG;AAC9B,uBAAa,CAAC,YAAY;AAAA,QAC9B,OACK;AACD,uBAAa;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ,OACK;AACD,mBAAa,KAAK,aAAa,cAAc;AAAA,IACjD;AACA,QAAI,eAAe,MAAM;AACrB,mBAAa,WAAW,IAAI,eAAa;AACrC,YAAI,UAAU,oBAAoB,UAAU,eAAe;AACvD,cAAI,gBAAgB,UAAU;AAC9B,cAAI,UAAU,cAAc,GAAG;AAC3B,4BAAgB,gBAAgB;AAAA,UACpC;AACA,iBAAO,IAAI,MAAM,UAAU,iBAAiB,GAAG,eAAe,KAAK,QAAQ,SAAS,EAAE,iBAAiB,aAAa,CAAC;AAAA,QACzH;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,QAAI,cAAc,KAAK,aAAa,YAAY,OAAO,aAAa;AACpE,SAAK,aAAa,IAAI,aAAa,UAAU;AAC7C,UAAM,kBAAkB,KAAK,QAAQ,aAAa;AAClD,QAAI,yBAAyB,KAAK,aAAa,0BAA0B,eAAe;AACxF,QAAI,2BAA2B,KAAK,YAAY,SAAS,GAAG;AAGxD,YAAM,sBAAsB,kBAAkB,YAAY,IAAI,WAAS,MAAM,KAAK,GAAG,WAAS,MAAM,yBAAyB,OAAO,eAAe,KAAK,CAAC;AACzJ,+BAAyB,sBAAsB,IAAI,sBAAsB,IAAI,IAAuC;AAAA,IACxH;AACA,SAAK,OAAO,gBAAgB,wBAAwB,KAAK,aAAa,SAAS,GAAG,MAAS;AAC3F,QAAI,cAAc,KAAK,QAAQ;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE,kBAAkB;AACtE,WAAK,iBAAiB,KAAK,aAAa,iBAAiB,CAAC;AAAA,IAC9D;AAAA,EACJ;AAAA,EACA,cAAc;AACV,WAAQ,KAAK,OAAO,eAAe;AAAA,EACvC;AAAA,EACA,cAAc;AACV,QAAI,CAAC,KAAK,YAAY,GAAG;AACrB,UAAI,YAAY,KAAK,aAAa,aAAa;AAC/C,UAAI,WAAW;AAEX,aAAK,QAAQ;AAAA,UAAqC;AAAA,UAAW;AAAA;AAAA,QAAc;AAAA,MAC/E;AACA,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,OAAO;AACxB,QAAI,kBAAkB,KAAK,aAAa,oBAAoB,KAAK;AACjE,SAAK,OAAO,gBAAgB,iBAAiB,KAAK,aAAa,SAAS,GAAG,KAAK;AAChF,SAAK,QAAQ,aAAa,KAAK;AAC/B,SAAK,QAAQ;AAAA,MAAqC;AAAA,MAAO;AAAA;AAAA,IAAc;AAAA,EAC3E;AAAA,EACA,oBAAoB,QAAQ;AACxB,QAAI,mBAAmB,KAAK,OAAO,YAAY,KAAK,OAAO,aAAa,QAAQ,GAAG,KAAK,KACjF,KAAK,OAAO,aAAa,QAAQ,GAAG,KAAK;AAChD,QAAI,EAAE,YAAY,OAAO,IAAI;AAC7B,QAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,QAAI,oBAAoB,WAAW,GAAG;AAClC,UAAI,eAAe,GAAG;AAClB,qBAAa,MAAM,aAAa;AAAA,MACpC,OACK;AACD;AAAA,MACJ;AACA,eAAS,MAAM,iBAAiB,UAAU;AAAA,IAC9C,OACK;AACD;AAAA,IACJ;AACA,WAAO,IAAI,SAAS,YAAY,MAAM;AAAA,EAC1C;AAAA,EACA,iBAAiB,QAAQ,aAAa,OAAO;AACzC,QAAI,CAAC,KAAK,OAAO,gBAAgB,GAAG;AAGhC,YAAM,iBAAiB,KAAK,aAAa,mBAAmB,MAAM;AAClE,UAAI,gBAAgB;AAChB,aAAK,qBAAqB,cAAc;AAAA,MAC5C;AACA;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,SAAS,IAAI,eAAe;AAC9C,UAAI,iBAAiB,KAAK,aAAa,oBAAoB,MAAM;AACjE,UAAI,kBAAkB,eAAe,QAAQ,KAAK,eAAe,iBAAiB,EAAE,OAAO,MAAM,GAAG;AAChG,iBAAS,KAAK,oBAAoB,MAAM;AACxC,yBAAiB,KAAK,aAAa,oBAAoB,MAAM;AAAA,MACjE;AACA,UAAI,gBAAgB;AAChB,aAAK,qBAAqB,cAAc;AAAA,MAC5C;AACA;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,GAAG;AACpB;AAAA,IACJ;AACA,QAAI,YAAY,KAAK,aAAa,aAAa;AAC/C,QAAI,cAAc,6BAA4B,gBAAgB,KAAK,QAAQ,SAAS,GAAG,SAAS;AAEhG,QAAI,YAAY,eAAe,EAAE,SAAS,MAAM,GAAG;AAC/C,eAAS,YAAY,eAAe;AAAA,IACxC;AAEA,QAAI,OAAO,SAAS,YAAY,iBAAiB,CAAC,GAAG;AACjD,eAAS,YAAY,eAAe;AAAA,IACxC;AACA,QAAI,EAAE,YAAY,OAAO,IAAI;AAC7B,QAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,QAAI,WAAW,IAAI,SAAS,YAAY,MAAM;AAC9C,QAAI,YAAY,MAAM,kBAAkB,KAAK,OAAO,cAAc,UAAU,KAAK,OAAO,SAAS,KAAK,OAAO,WAAW,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,MAAU;AAAA;AAAA,IAAwB,IAAI,MAAM,KAAK;AAC9M,QAAI,aAAa,UAAU,MAAM,QAAQ,KAAK,UAAU,MAAM,iBAAiB,EAAE,OAAO,QAAQ,GAAG;AAE/F,iBAAW,KAAK,oBAAoB,QAAQ;AAC5C,kBAAY,MAAM,kBAAkB,KAAK,OAAO,cAAc,UAAU,KAAK,OAAO,SAAS,KAAK,OAAO,WAAW,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,QAAU;AAAA;AAAA,MAAwB,IAAI,MAAM,KAAK;AAAA,IAC9M;AACA,QAAI,CAAC,WAAW;AAEZ;AAAA,IACJ;AACA,QAAI,CAAC,cAAc,CAAC,YAAY,cAAc,UAAU,KAAK,GAAG;AAC5D,aAAO,KAAK,iBAAiB,UAAU,MAAM,iBAAiB,GAAG,IAAI;AAAA,IACzE;AACA,SAAK,qBAAqB,UAAU,KAAK;AAAA,EAC7C;AAAA,EACA,kBAAkB;AACd,SAAK,iBAAiB,KAAK,QAAQ,aAAa,EAAE,iBAAiB,CAAC;AAAA,EACxE;AAAA,EACA,oBAAoB,OAAO;AACvB,QAAI,mBAAmB,KAAK,OAAO,YAAY,KAAK,OAAO,aAAa,QAAQ,GAAG,KAAK,KACjF,KAAK,OAAO,aAAa,QAAQ,GAAG,KAAK;AAChD,QAAI,EAAE,YAAY,OAAO,IAAI;AAC7B,QAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,QAAI,oBAAoB,WAAW,MAAM,iBAAiB,UAAU,GAAG;AACnE,UAAI,eAAe,MAAM,aAAa,GAAG;AACrC,qBAAa;AAAA,MACjB,OACK;AACD;AAAA,MACJ;AACA,eAAS;AAAA,IACb,OACK;AACD;AAAA,IACJ;AACA,WAAO,IAAI,SAAS,YAAY,MAAM;AAAA,EAC1C;AAAA,EACA,iBAAiB,OAAO;AACpB,QAAI,CAAC,KAAK,OAAO,mBAAmB,GAAG;AAGnC,YAAM,iBAAiB,KAAK,aAAa,oBAAoB,KAAK;AAClE,UAAI,gBAAgB;AAChB,aAAK,qBAAqB,cAAc;AAAA,MAC5C;AACA;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,SAAS,IAAI,eAAe;AAC9C,UAAI,iBAAiB,KAAK,aAAa,mBAAmB,KAAK;AAC/D,UAAI,kBAAkB,eAAe,QAAQ,KAAK,eAAe,iBAAiB,EAAE,OAAO,KAAK,GAAG;AAE/F,gBAAQ,KAAK,oBAAoB,KAAK;AACtC,yBAAiB,KAAK,aAAa,mBAAmB,KAAK;AAAA,MAC/D;AACA,UAAI,gBAAgB;AAChB,aAAK,qBAAqB,cAAc;AAAA,MAC5C;AACA;AAAA,IACJ;AACA,QAAI,YAAY,KAAK,cAAc,OAAO,OAAO,IAAI;AACrD,QAAI,WAAW;AACX,WAAK,qBAAqB,UAAU,KAAK;AAAA,IAC7C;AAAA,EACJ;AAAA,EACA,cAAc,OAAO,gBAAgB,WAAW,aAAa,OAAO;AAChE,QAAI,KAAK,YAAY,GAAG;AACpB,aAAO;AAAA,IACX;AACA,QAAI,YAAY,KAAK,aAAa,aAAa;AAC/C,QAAI,cAAc,6BAA4B,gBAAgB,KAAK,QAAQ,SAAS,GAAG,SAAS;AAEhG,QAAI,YAAY,eAAe,EAAE,SAAS,KAAK,GAAG;AAC9C,cAAQ,YAAY,iBAAiB;AAAA,IACzC;AAEA,QAAI,MAAM,SAAS,YAAY,iBAAiB,CAAC,GAAG;AAChD,cAAQ,YAAY,iBAAiB;AAAA,IACzC;AACA,QAAI,EAAE,YAAY,OAAO,IAAI;AAC7B,QAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,QAAI,WAAW,IAAI,SAAS,YAAY,MAAM;AAC9C,QAAI,YAAY,MAAM,cAAc,KAAK,OAAO,cAAc,UAAU,KAAK,OAAO,SAAS,KAAK,OAAO,WAAW,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,MAAU;AAAA;AAAA,IAAwB,IAAI,MAAM,cAAc;AACnN,QAAI,aAAa,aAAa,UAAU,MAAM,QAAQ,KAAK,UAAU,MAAM,iBAAiB,EAAE,OAAO,QAAQ,GAAG;AAE5G,iBAAW,KAAK,oBAAoB,QAAQ;AAC5C,kBAAY,MAAM,cAAc,KAAK,OAAO,cAAc,UAAU,KAAK,OAAO,SAAS,KAAK,OAAO,WAAW,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,QAAU;AAAA;AAAA,MAAwB,IAAI,MAAM,cAAc;AAAA,IACnN;AACA,QAAI,CAAC,WAAW;AAEZ,aAAO;AAAA,IACX;AACA,QAAI,CAAC,cAAc,CAAC,YAAY,cAAc,UAAU,KAAK,GAAG;AAC5D,aAAO,KAAK,cAAc,UAAU,MAAM,eAAe,GAAG,gBAAgB,WAAW,IAAI;AAAA,IAC/F;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB;AACd,SAAK,iBAAiB,KAAK,QAAQ,aAAa,EAAE,eAAe,CAAC;AAAA,EACtE;AAAA,EACA,qBAAqB;AACjB,QAAI,KAAK,OAAO,SAAS;AACrB,aAAO,mBAAmB,KAAK,OAAO,aAAa;AAAA,IACvD;AACA,WAAO,eAAe,gBAAgB,KAAK,OAAO,aAAa;AAAA,EACnE;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,YAAY,GAAG;AACrB;AAAA,IACJ;AACA,QAAI,iBAAiB,KAAK,mBAAmB;AAC7C,QAAI,YAAY,KAAK,QAAQ,aAAa;AAC1C,QAAI,YAAY,KAAK,cAAc,UAAU,iBAAiB,GAAG,MAAM,KAAK;AAC5E,QAAI,WAAW;AACX,UAAI,UAAU,YAAY,UAAU,KAAK,GAAG;AAExC,YAAI,gBAAgB,eAAe,mBAAmB,UAAU,SAAS,KAAK,OAAO,YAAY;AACjG,YAAI,UAAU,IAAI,eAAe,WAAW,aAAa;AACzD,aAAK,sBAAsB,WAAW,OAAO;AAC7C,aAAK,aAAa,iBAAiB,IAAI,SAAS,UAAU,iBAAiB,UAAU,cAAc,cAAc,MAAM,CAAC;AACxH,aAAK,SAAS,IAAI;AAAA,MACtB,OACK;AACD,aAAK,aAAa,iBAAiB,KAAK,QAAQ,YAAY,CAAC;AAC7D,aAAK,qBAAqB,UAAU,KAAK;AAAA,MAC7C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,aAAa,YAAY,gBAAgB,kBAAkB;AACvD,UAAM,gBAAgB,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,6BAA4B,gBAAgB,KAAK,QAAQ,SAAS,GAAG,KAAK,CAAC;AACtI,WAAO,KAAK,QAAQ,SAAS,EAAE,YAAY,KAAK,OAAO,cAAc,cAAc,KAAK,OAAO,SAAS,KAAK,OAAO,WAAW,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,MAAU;AAAA;AAAA,IAAwB,IAAI,MAAM,gBAAgB,gBAAgB;AAAA,EACpP;AAAA,EACA,aAAa;AACT,QAAI,CAAC,KAAK,YAAY,GAAG;AACrB;AAAA,IACJ;AACA,UAAM,aAAa,KAAK,aAAa,cAAc;AACnD,QAAI,eAAe,QAAQ,KAAK,OAAO,gBAAgB,eAAe;AAElE,WAAK,iBAAiB;AAAA,IAC1B,OACK;AACD,WAAK,mBAAmB,UAAU;AAAA,IACtC;AACA,SAAK,SAAS,KAAK;AAAA,EACvB;AAAA,EACA,mBAAmB;AACf,UAAM,eAAe,IAAI,aAAa,KAAK,OAAO,cAAc,KAAK,OAAO,SAAS,KAAK,OAAO,WAAW,KAAK,OAAO,YAAY,KAAK,QAAQ;AAAA,MAAU;AAAA;AAAA,IAAwB,IAAI,IAAI;AAC3L,UAAM,aAAa,aAAa,mBAAmB;AACnD,QAAI,CAAC,YAAY;AACb;AAAA,IACJ;AACA,QAAI,cAAc,WAAW;AAC7B,QAAI,CAAC,YAAY,WAAW;AACxB,UAAI,MAAM;AACV,UAAI,YAAY,YAAY;AACxB,eAAO;AAAA,MACX;AACA,UAAI,YAAY,QAAQ;AACpB,eAAO;AAAA,MACX;AACA,oBAAc,IAAI,OAAO,YAAY,QAAQ,GAAG;AAAA,IACpD;AACA,UAAM,QAAQ,KAAK,QAAQ,SAAS;AACpC,UAAM,YAAY,MAAM;AAAA,MAAS;AAAA;AAAA,IAAU;AAC3C,UAAM,iBAAiB,MAAM,kBAAkB;AAC/C,UAAM,iBAAiB,KAAK,mBAAmB;AAC/C,QAAI;AACJ,UAAM,eAAe,KAAK,OAAO;AACjC,QAAI,eAAe,0BAA0B,cAAc;AACvD,mBAAa,UAAU,QAAQ,aAAa,WAAY;AACpD,eAAO,eAAe,mBAAmB,WAAW,YAAY;AAAA,MACpE,CAAC;AAAA,IACL,OACK;AACD,mBAAa,UAAU,QAAQ,aAAa,eAAe,mBAAmB,MAAM,YAAY,CAAC;AAAA,IACrG;AACA,QAAI,UAAU,IAAI,qCAAqC,gBAAgB,YAAY,KAAK,QAAQ,aAAa,CAAC;AAC9G,SAAK,sBAAsB,cAAc,OAAO;AAAA,EACpD;AAAA,EACA,mBAAmB,YAAY;AAC3B,UAAM,iBAAiB,KAAK,mBAAmB;AAE/C,QAAI,UAAU,KAAK;AAAA,MAAa;AAAA,MAAY,eAAe,0BAA0B,KAAK,OAAO;AAAA,MAAc;AAAA;AAAA,IAAuC;AACtJ,QAAI,iBAAiB,CAAC;AACtB,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAChD,qBAAe,CAAC,IAAI,eAAe,mBAAmB,QAAQ,CAAC,EAAE,SAAS,KAAK,OAAO,YAAY;AAAA,IACtG;AACA,QAAI,UAAU,IAAI,kBAAkB,KAAK,QAAQ,aAAa,GAAG,QAAQ,IAAI,OAAK,EAAE,KAAK,GAAG,cAAc;AAC1G,SAAK,sBAAsB,cAAc,OAAO;AAAA,EACpD;AAAA,EACA,mBAAmB;AACf,QAAI,CAAC,KAAK,YAAY,GAAG;AACrB;AAAA,IACJ;AACA,QAAI,aAAa,KAAK,aAAa,cAAc;AAEjD,QAAI,UAAU,KAAK;AAAA,MAAa;AAAA,MAAY;AAAA,MAAO;AAAA;AAAA,IAAuC;AAC1F,QAAI,aAAa,QAAQ,IAAI,OAAK,IAAI,UAAU,EAAE,MAAM,iBAAiB,EAAE,MAAM,aAAa,EAAE,MAAM,eAAe,EAAE,MAAM,SAAS,CAAC;AAEvI,QAAI,kBAAkB,KAAK,QAAQ,aAAa;AAChD,aAAS,IAAI,GAAG,MAAM,WAAW,QAAQ,IAAI,KAAK,KAAK;AACnD,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,IAAI,YAAY,eAAe,GAAG;AAClC,qBAAa,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,WAAW,MAAM,IAAI,CAAC,CAAC;AAC5F;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,QAAQ,cAAc,UAAU;AAAA,EACzC;AAAA,EACA,sBAAsB,QAAQ,SAAS;AACnC,QAAI;AACA,WAAK,6BAA6B;AAClC,WAAK,QAAQ,aAAa;AAC1B,WAAK,QAAQ,eAAe,QAAQ,OAAO;AAC3C,WAAK,QAAQ,aAAa;AAAA,IAC9B,UACA;AACI,WAAK,6BAA6B;AAAA,IACtC;AAAA,EACJ;AACJ;;;ACrdA,OAAO;AACP,IAAM,cAAc;AAAA,EAChB,yBAAyB,MAAM,QAAQ,WAAW;AAAA,EAClD,6BAA6B,MAAM,QAAQ,SAAS;AAAA,EACpD,6BAA6B,MAAM,QAAQ,WAAW;AAC1D;AACO,IAAM,WAAN,cAAuB,OAAO;AAAA,EACjC,YAAY,MAAM;AACd,UAAM;AACN,SAAK,YAAY,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC7C,SAAK,WAAW,KAAK,UAAU;AAC/B,SAAK,aAAa,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC9C,SAAK,YAAY,KAAK,WAAW;AACjC,SAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,IAAI;AAC/D,SAAK,WAAW,KAAK,MAAM;AAC3B,UAAM,UAAU,CAAC,wBAAwB;AACzC,QAAI,KAAK,MAAM,MAAM;AACjB,cAAQ,KAAK,GAAG,QAAQ,iBAAiB,KAAK,MAAM,IAAI,CAAC;AAAA,IAC7D;AACA,QAAI,KAAK,MAAM,iBAAiB;AAC5B,cAAQ,KAAK,GAAG,KAAK,MAAM,gBAAgB,MAAM,GAAG,CAAC;AAAA,IACzD;AACA,QAAI,KAAK,UAAU;AACf,cAAQ,KAAK,SAAS;AAAA,IAC1B;AACA,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,QAAQ,QAAQ,KAAK,MAAM;AAChC,SAAK,QAAQ,UAAU,IAAI,GAAG,OAAO;AACrC,QAAI,CAAC,KAAK,MAAM,cAAc;AAC1B,WAAK,QAAQ,WAAW;AAAA,IAC5B;AACA,SAAK,QAAQ,aAAa,QAAQ,UAAU;AAC5C,SAAK,QAAQ,aAAa,gBAAgB,OAAO,KAAK,QAAQ,CAAC;AAC/D,SAAK,QAAQ,aAAa,cAAc,KAAK,MAAM,KAAK;AACxD,SAAK,YAAY;AACjB,SAAK,QAAQ,KAAK,SAAS,CAAC,OAAO;AAC/B,UAAI,KAAK,SAAS;AACd,aAAK,UAAU,CAAC,KAAK;AACrB,aAAK,UAAU,KAAK,KAAK;AACzB,WAAG,eAAe;AAAA,MACtB;AAAA,IACJ,CAAC;AACD,SAAK,cAAc,KAAK,OAAO;AAC/B,SAAK,UAAU,KAAK,SAAS,CAAC,kBAAkB;AAC5C,UAAI,cAAc,YAAY,MAAkB,cAAc,YAAY,GAAe;AACrF,aAAK,UAAU,CAAC,KAAK;AACrB,aAAK,UAAU,KAAK,IAAI;AACxB,sBAAc,eAAe;AAC7B;AAAA,MACJ;AACA,WAAK,WAAW,KAAK,aAAa;AAAA,IACtC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK,QAAQ,aAAa,eAAe,MAAM;AAAA,EAC1D;AAAA,EACA,QAAQ;AACJ,SAAK,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ,cAAc;AACtB,SAAK,WAAW;AAChB,SAAK,QAAQ,aAAa,gBAAgB,OAAO,KAAK,QAAQ,CAAC;AAC/D,SAAK,QAAQ,UAAU,OAAO,WAAW,KAAK,QAAQ;AACtD,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,QAAQ;AACJ,WAAO,IAAoB,IAAe,IAAgB;AAAA,EAC9D;AAAA,EACA,MAAM,QAAQ;AACV,QAAI,OAAO,yBAAyB;AAChC,WAAK,MAAM,0BAA0B,OAAO;AAAA,IAChD;AACA,QAAI,OAAO,6BAA6B;AACpC,WAAK,MAAM,8BAA8B,OAAO;AAAA,IACpD;AACA,QAAI,OAAO,6BAA6B;AACpC,WAAK,MAAM,8BAA8B,OAAO;AAAA,IACpD;AACA,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,cAAc;AACV,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,MAAM,cAAc,KAAK,YAAY,KAAK,MAAM,0BAA0B,KAAK,MAAM,wBAAwB,SAAS,IAAI;AACvI,WAAK,QAAQ,MAAM,QAAQ,KAAK,YAAY,KAAK,MAAM,8BAA8B,KAAK,MAAM,4BAA4B,SAAS,IAAI;AACzI,WAAK,QAAQ,MAAM,kBAAkB,KAAK,YAAY,KAAK,MAAM,8BAA8B,KAAK,MAAM,4BAA4B,SAAS,IAAI;AAAA,IACvJ;AAAA,EACJ;AAAA,EACA,SAAS;AACL,SAAK,QAAQ,aAAa,iBAAiB,OAAO,KAAK,CAAC;AAAA,EAC5D;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,aAAa,iBAAiB,OAAO,IAAI,CAAC;AAAA,EAC3D;AACJ;;;ACjGA,IAAM,oCAAwC,SAAS,mBAAmB,YAAY;AACtF,IAAM,gCAAoC,SAAS,oBAAoB,kBAAkB;AACzF,IAAM,2BAA+B,SAAS,oBAAoB,wBAAwB;AACnF,IAAM,wBAAN,cAAoC,SAAS;AAAA,EAChD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM,QAAQ;AAAA,MACd,OAAO,oCAAoC,KAAK;AAAA,MAChD,WAAW,KAAK;AAAA,MAChB,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC;AAAA,EACL;AACJ;AACO,IAAM,qBAAN,cAAiC,SAAS;AAAA,EAC7C,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM,QAAQ;AAAA,MACd,OAAO,gCAAgC,KAAK;AAAA,MAC5C,WAAW,KAAK;AAAA,MAChB,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC;AAAA,EACL;AACJ;AACO,IAAM,gBAAN,cAA4B,SAAS;AAAA,EACxC,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM,QAAQ;AAAA,MACd,OAAO,2BAA2B,KAAK;AAAA,MACvC,WAAW,KAAK;AAAA,MAChB,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC;AAAA,EACL;AACJ;;;AClCO,IAAM,oBAAN,MAAM,2BAA0B,OAAO;AAAA,EAC1C,YAAY,QAAQ,OAAO,mBAAmB,cAAc;AACxD,UAAM;AACN,SAAK,YAAY,KAAK,UAAU,IAAI,iBAAiB,MAAM,KAAK,MAAM,GAAG,GAAI,CAAC;AAC9E,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,qBAAqB;AAC1B,SAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,MAAM,UAAU;AAC9B,SAAK,SAAS,MAAM,MAAM;AAC1B,SAAK,SAAS,aAAa,QAAQ,cAAc;AACjD,SAAK,SAAS,aAAa,eAAe,MAAM;AAChD,UAAM,+BAA+B,aAAa,cAAc,EAAE,SAAS,uBAAuB;AAClG,UAAM,mCAAmC,aAAa,cAAc,EAAE,SAAS,2BAA2B;AAC1G,UAAM,mCAAmC,aAAa,cAAc,EAAE,SAAS,2BAA2B;AAC1G,SAAK,gBAAgB,KAAK,UAAU,IAAI,sBAAsB;AAAA,MAC1D,aAAa,KAAK,oBAAoB,SAAS,0BAA0B;AAAA,MACzE,WAAW,KAAK,OAAO;AAAA,MACvB,yBAAyB;AAAA,MACzB,6BAA6B;AAAA,MAC7B,6BAA6B;AAAA,IACjC,CAAC,CAAC;AACF,SAAK,SAAS,YAAY,KAAK,cAAc,OAAO;AACpD,SAAK,UAAU,KAAK,cAAc,SAAS,MAAM;AAC7C,WAAK,OAAO,OAAO;AAAA,QACf,WAAW,KAAK,cAAc;AAAA,MAClC,GAAG,KAAK;AAAA,IACZ,CAAC,CAAC;AACF,SAAK,aAAa,KAAK,UAAU,IAAI,mBAAmB;AAAA,MACpD,aAAa,KAAK,oBAAoB,SAAS,sBAAsB;AAAA,MACrE,WAAW,KAAK,OAAO;AAAA,MACvB,yBAAyB;AAAA,MACzB,6BAA6B;AAAA,MAC7B,6BAA6B;AAAA,IACjC,CAAC,CAAC;AACF,SAAK,SAAS,YAAY,KAAK,WAAW,OAAO;AACjD,SAAK,UAAU,KAAK,WAAW,SAAS,MAAM;AAC1C,WAAK,OAAO,OAAO;AAAA,QACf,WAAW,KAAK,WAAW;AAAA,MAC/B,GAAG,KAAK;AAAA,IACZ,CAAC,CAAC;AACF,SAAK,QAAQ,KAAK,UAAU,IAAI,cAAc;AAAA,MAC1C,aAAa,KAAK,oBAAoB,SAAS,kBAAkB;AAAA,MACjE,WAAW,KAAK,OAAO;AAAA,MACvB,yBAAyB;AAAA,MACzB,6BAA6B;AAAA,MAC7B,6BAA6B;AAAA,IACjC,CAAC,CAAC;AACF,SAAK,SAAS,YAAY,KAAK,MAAM,OAAO;AAC5C,SAAK,UAAU,KAAK,MAAM,SAAS,MAAM;AACrC,WAAK,OAAO,OAAO;AAAA,QACf,SAAS,KAAK,MAAM;AAAA,MACxB,GAAG,KAAK;AAAA,IACZ,CAAC,CAAC;AACF,SAAK,QAAQ,iBAAiB,IAAI;AAClC,SAAK,UAAU,KAAK,OAAO,yBAAyB,CAAC,MAAM;AACvD,UAAI,mBAAmB;AACvB,UAAI,EAAE,SAAS;AACX,aAAK,MAAM,UAAU,KAAK,OAAO;AACjC,2BAAmB;AAAA,MACvB;AACA,UAAI,EAAE,WAAW;AACb,aAAK,WAAW,UAAU,KAAK,OAAO;AACtC,2BAAmB;AAAA,MACvB;AACA,UAAI,EAAE,WAAW;AACb,aAAK,cAAc,UAAU,KAAK,OAAO;AACzC,2BAAmB;AAAA,MACvB;AACA,UAAI,CAAC,KAAK,OAAO,cAAc,kBAAkB;AAC7C,aAAK,mBAAmB;AAAA,MAC5B;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,UAAc,yCAAyC,KAAK,UAAU,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC;AACrG,SAAK,UAAc,sBAAsB,KAAK,UAAU,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;AAChG,SAAK,YAAY,aAAa,cAAc,CAAC;AAC7C,SAAK,UAAU,aAAa,sBAAsB,KAAK,YAAY,KAAK,IAAI,CAAC,CAAC;AAAA,EAClF;AAAA,EACA,oBAAoB,UAAU;AAC1B,QAAI,KAAK,KAAK,mBAAmB,iBAAiB,QAAQ;AAC1D,QAAI,CAAC,IAAI;AACL,aAAO;AAAA,IACX;AACA,WAAO,KAAK,GAAG,SAAS,CAAC;AAAA,EAC7B;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,oBAAoB,IAAI;AACrC,UAAM,QAAQ;AAAA,EAClB;AAAA;AAAA,EAEA,QAAQ;AACJ,WAAO,mBAAkB;AAAA,EAC7B;AAAA,EACA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,cAAc;AACV,WAAO;AAAA,MACH,YAAY;AAAA;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,SAAK,mBAAmB;AAAA,EAC5B;AAAA,EACA,qBAAqB;AACjB,SAAK,MAAM;AACX,SAAK,UAAU,SAAS;AAAA,EAC5B;AAAA,EACA,cAAc;AACV,SAAK,UAAU,SAAS;AAAA,EAC5B;AAAA,EACA,eAAe;AACX,SAAK,UAAU,OAAO;AAAA,EAC1B;AAAA,EACA,QAAQ;AACJ,QAAI,KAAK,YAAY;AACjB;AAAA,IACJ;AACA,SAAK,aAAa;AAClB,SAAK,SAAS,MAAM,UAAU;AAAA,EAClC;AAAA,EACA,QAAQ;AACJ,QAAI,CAAC,KAAK,YAAY;AAClB;AAAA,IACJ;AACA,SAAK,aAAa;AAClB,SAAK,SAAS,MAAM,UAAU;AAAA,EAClC;AAAA,EACA,YAAY,OAAO;AACf,QAAI,cAAc;AAAA,MACd,yBAAyB,MAAM,SAAS,uBAAuB;AAAA,MAC/D,6BAA6B,MAAM,SAAS,2BAA2B;AAAA,MACvE,6BAA6B,MAAM,SAAS,2BAA2B;AAAA,IAC3E;AACA,SAAK,cAAc,MAAM,WAAW;AACpC,SAAK,WAAW,MAAM,WAAW;AACjC,SAAK,MAAM,MAAM,WAAW;AAAA,EAChC;AACJ;AACA,kBAAkB,KAAK;AACvB,2BAA2B,CAAC,OAAO,cAAc;AAC7C,QAAM,mBAAmB,MAAM,SAAS,sBAAsB;AAC9D,MAAI,kBAAkB;AAClB,cAAU,QAAQ,yDAAyD,gBAAgB,KAAK;AAAA,EACpG;AACA,QAAM,mBAAmB,MAAM,SAAS,sBAAsB;AAC9D,MAAI,kBAAkB;AAClB,cAAU,QAAQ,8CAA8C,gBAAgB,KAAK;AAAA,EACzF;AACA,QAAM,oBAAoB,MAAM,SAAS,YAAY;AACrD,MAAI,mBAAmB;AACnB,cAAU,QAAQ,+DAA+D,iBAAiB,KAAK;AAAA,EAC3G;AACA,QAAM,WAAW,MAAM,SAAS,cAAc;AAC9C,MAAI,UAAU;AACV,cAAU,QAAQ,yDAAyD,QAAQ,KAAK;AAAA,EAC5F;AACJ,CAAC;;;AClKD,SAAS,qBAAqB,UAAU,OAAO;AAC3C,MAAI,aAAa,GAAc;AAC3B,WAAO;AAAA,EACX;AACA,MAAI,aAAa,GAAe;AAC5B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACO,IAAM,mBAAN,cAA+B,WAAW;AAAA,EAC7C,cAAc;AACV,UAAM;AACN,SAAK,4BAA4B,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC7D,SAAK,2BAA2B,KAAK,0BAA0B;AAC/D,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAC1B,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,IAAI,eAAe;AAAE,WAAO,KAAK;AAAA,EAAe;AAAA,EAChD,IAAI,gBAAgB;AAAE,WAAO,KAAK;AAAA,EAAgB;AAAA,EAClD,IAAI,aAAa;AAAE,WAAO,KAAK;AAAA,EAAa;AAAA,EAC5C,IAAI,oBAAoB;AAAE,WAAO,KAAK;AAAA,EAAoB;AAAA,EAC1D,IAAI,UAAU;AAAE,WAAO,qBAAqB,KAAK,kBAAkB,KAAK,QAAQ;AAAA,EAAG;AAAA,EACnF,IAAI,YAAY;AAAE,WAAO,qBAAqB,KAAK,oBAAoB,KAAK,UAAU;AAAA,EAAG;AAAA,EACzF,IAAI,YAAY;AAAE,WAAO,qBAAqB,KAAK,oBAAoB,KAAK,UAAU;AAAA,EAAG;AAAA,EACzF,IAAI,eAAe;AAAE,WAAO,qBAAqB,KAAK,uBAAuB,KAAK,aAAa;AAAA,EAAG;AAAA,EAClG,IAAI,gBAAgB;AAAE,WAAO,KAAK;AAAA,EAAU;AAAA,EAC5C,IAAI,kBAAkB;AAAE,WAAO,KAAK;AAAA,EAAY;AAAA,EAChD,IAAI,kBAAkB;AAAE,WAAO,KAAK;AAAA,EAAY;AAAA,EAChD,IAAI,qBAAqB;AAAE,WAAO,KAAK;AAAA,EAAe;AAAA,EACtD,IAAI,cAAc;AAAE,WAAO,KAAK;AAAA,EAAc;AAAA,EAC9C,IAAI,kBAAkB;AAAE,WAAO,KAAK;AAAA,EAAkB;AAAA,EACtD,IAAI,eAAe;AAAE,WAAO,KAAK;AAAA,EAAe;AAAA,EAChD,IAAI,eAAe;AAAE,WAAO,KAAK;AAAA,EAAe;AAAA,EAChD,gBAAgB,iBAAiB,cAAc,cAAc;AACzD,QAAI,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,MACd,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,IACb;AACA,QAAI,mBAAmB;AACvB,QAAI,iBAAiB,GAAG;AACpB,wBAAkB;AAAA,IACtB;AACA,QAAI,kBAAkB,cAAc;AAChC,wBAAkB;AAAA,IACtB;AACA,QAAI,KAAK,qBAAqB,iBAAiB;AAC3C,WAAK,mBAAmB;AACxB,kBAAY,kBAAkB;AAC9B,yBAAmB;AAAA,IACvB;AACA,QAAI,KAAK,kBAAkB,cAAc;AACrC,WAAK,gBAAgB;AACrB,kBAAY,eAAe;AAC3B,yBAAmB;AAAA,IACvB;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,UAAI,CAAC,MAAM,YAAY,KAAK,eAAe,YAAY,GAAG;AACtD,aAAK,gBAAgB;AACrB,oBAAY,eAAe;AAC3B,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,kBAAkB;AAClB,WAAK,0BAA0B,KAAK,WAAW;AAAA,IACnD;AAAA,EACJ;AAAA,EACA,OAAO,UAAU,YAAY,gBAAgB,MAAM;AAC/C,QAAI;AACJ,QAAI,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,IACb;AACA,QAAI,mBAAmB;AACvB,UAAM,sBAAsB,KAAK;AACjC,UAAM,yBAAyB,KAAK;AACpC,UAAM,wBAAwB,KAAK;AACnC,UAAM,2BAA2B,KAAK;AACtC,QAAI,OAAO,SAAS,iBAAiB,aAAa;AAC9C,UAAI,KAAK,kBAAkB,SAAS,cAAc;AAC9C,aAAK,gBAAgB,SAAS;AAC9B,oBAAY,eAAe;AAC3B,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,kBAAkB,aAAa;AAC/C,UAAI,KAAK,mBAAmB,SAAS,eAAe;AAChD,aAAK,iBAAiB,SAAS;AAC/B,oBAAY,gBAAgB;AAC5B,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,eAAe,aAAa;AAC5C,UAAI,KAAK,gBAAgB,SAAS,YAAY;AAC1C,aAAK,cAAc,SAAS;AAC5B,oBAAY,aAAa;AACzB,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,sBAAsB,aAAa;AACnD,UAAI,KAAK,uBAAuB,SAAS,mBAAmB;AACxD,aAAK,qBAAqB,SAAS;AACnC,oBAAY,oBAAoB;AAChC,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,YAAY,aAAa;AACzC,WAAK,WAAW,SAAS;AAAA,IAC7B;AACA,QAAI,OAAO,SAAS,cAAc,aAAa;AAC3C,WAAK,aAAa,SAAS;AAAA,IAC/B;AACA,QAAI,OAAO,SAAS,cAAc,aAAa;AAC3C,WAAK,aAAa,SAAS;AAAA,IAC/B;AACA,QAAI,OAAO,SAAS,iBAAiB,aAAa;AAC9C,WAAK,gBAAgB,SAAS;AAAA,IAClC;AACA,QAAI,OAAO,SAAS,gBAAgB,aAAa;AAC7C,UAAI,GAAG,KAAK,SAAS,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,CAAC,mBAAmB;AAChG,YAAIC;AACJ,gBAAQA,MAAK,KAAK,kBAAkB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,yBAAuB;AAChG,iBAAO,CAAC,MAAM,YAAY,qBAAqB,cAAc;AAAA,QACjE,CAAC;AAAA,MACL,CAAC,IAAI;AACD,aAAK,eAAe,SAAS;AAC7B,oBAAY,cAAc;AAC1B,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,SAAS,aAAa;AACtC,UAAI,KAAK,UAAU,SAAS,MAAM;AAC9B,aAAK,QAAQ,SAAS;AACtB,oBAAY,OAAO;AACnB,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,gBAAgB,aAAa;AAC7C,UAAI,KAAK,iBAAiB,SAAS,aAAa;AAC5C,aAAK,eAAe,SAAS;AAC7B,oBAAY,cAAc;AAC1B,2BAAmB;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,OAAO,SAAS,YAAY,aAAa;AACzC,UAAI,KAAK,UAAU;AACf,aAAK,SAAS,OAAO,SAAS,OAAO;AAAA,MACzC,OACK;AACD,aAAK,WAAW,SAAS;AAAA,MAC7B;AACA,kBAAY,UAAU;AACtB,yBAAmB;AAAA,IACvB;AAEA,SAAK,mBAAoB,OAAO,SAAS,oBAAoB,cAAc,SAAS,kBAAkB;AACtG,SAAK,qBAAsB,OAAO,SAAS,sBAAsB,cAAc,SAAS,oBAAoB;AAC5G,SAAK,qBAAsB,OAAO,SAAS,sBAAsB,cAAc,SAAS,oBAAoB;AAC5G,SAAK,wBAAyB,OAAO,SAAS,yBAAyB,cAAc,SAAS,uBAAuB;AACrH,QAAI,wBAAwB,KAAK,SAAS;AACtC,yBAAmB;AACnB,kBAAY,UAAU;AAAA,IAC1B;AACA,QAAI,2BAA2B,KAAK,WAAW;AAC3C,yBAAmB;AACnB,kBAAY,YAAY;AAAA,IAC5B;AACA,QAAI,0BAA0B,KAAK,WAAW;AAC1C,yBAAmB;AACnB,kBAAY,YAAY;AAAA,IAC5B;AACA,QAAI,6BAA6B,KAAK,cAAc;AAChD,yBAAmB;AACnB,kBAAY,eAAe;AAAA,IAC/B;AACA,QAAI,kBAAkB;AAClB,WAAK,0BAA0B,KAAK,WAAW;AAAA,IACnD;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,WAAO,KAAK,kBAAkB,KAAM,KAAK,oBAAoB;AAAA,EACjE;AAAA,EACA,qBAAqB;AACjB,WAAO,KAAK,kBAAkB,KAAM,KAAK,kBAAkB,KAAK;AAAA,EACpE;AAAA,EACA,oBAAoB;AAChB,WAAO,KAAK,SAAU,KAAK,gBAAgB;AAAA,EAC/C;AACJ;;;AC1NA,OAAO;;;ACfP,OAAO;AAEP,IAAM,oBAAwB,SAAS,gBAAgB,OAAO;AACvD,IAAM,YAAN,cAAwB,OAAO;AAAA,EAClC,YAAY,QAAQ,qBAAqB,oBAAoB,SAAS;AAClE,UAAM;AACN,SAAK,qBAAqB;AAC1B,SAAK,+BAA+B;AACpC,SAAK,uBAAuB;AAC5B,SAAK,qBAAqB,KAAK,UAAU,IAAI,QAAQ,CAAC;AACtD,SAAK,oBAAoB,KAAK,mBAAmB;AACjD,SAAK,aAAa,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC9C,SAAK,YAAY,KAAK,WAAW;AACjC,SAAK,eAAe,KAAK,UAAU,IAAI,QAAQ,CAAC;AAChD,SAAK,cAAc,KAAK,aAAa;AACrC,SAAK,WAAW,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC5C,SAAK,WAAW,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC5C,SAAK,0BAA0B,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC3D,SAAK,yBAAyB,KAAK,wBAAwB;AAC3D,SAAK,kBAAkB,KAAK,UAAU,IAAI,QAAQ,CAAC;AACnD,SAAK,iBAAiB,KAAK,gBAAgB;AAC3C,SAAK,4BAA4B;AACjC,SAAK,sBAAsB;AAC3B,SAAK,cAAc,QAAQ,eAAe;AAC1C,SAAK,aAAa,QAAQ;AAC1B,SAAK,QAAQ,QAAQ,SAAS;AAC9B,SAAK,0BAA0B,QAAQ;AACvC,SAAK,8BAA8B,QAAQ;AAC3C,SAAK,8BAA8B,QAAQ;AAC3C,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,cAAc,QAAQ;AAC3B,SAAK,4BAA4B,QAAQ;AACzC,SAAK,gCAAgC,QAAQ;AAC7C,SAAK,gCAAgC,QAAQ;AAC7C,SAAK,+BAA+B,QAAQ;AAC5C,SAAK,mCAAmC,QAAQ;AAChD,SAAK,mCAAmC,QAAQ;AAChD,SAAK,6BAA6B,QAAQ;AAC1C,SAAK,iCAAiC,QAAQ;AAC9C,SAAK,iCAAiC,QAAQ;AAC9C,UAAM,2BAA2B,QAAQ,4BAA4B;AACrE,UAAM,wBAAwB,QAAQ,yBAAyB;AAC/D,UAAM,mBAAmB,QAAQ,oBAAoB;AACrD,UAAM,UAAU,QAAQ,WAAW,CAAC;AACpC,UAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,UAAM,gBAAgB,CAAC,CAAC,QAAQ;AAChC,UAAM,oBAAoB,QAAQ;AAClC,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,QAAQ,UAAU,IAAI,kBAAkB;AAC7C,SAAK,WAAW,KAAK,UAAU,IAAI,gBAAgB,KAAK,SAAS,KAAK,qBAAqB;AAAA,MACvF,aAAa,KAAK,eAAe;AAAA,MACjC,WAAW,KAAK,SAAS;AAAA,MACzB,mBAAmB;AAAA,QACf,YAAY,KAAK;AAAA,MACrB;AAAA,MACA,iBAAiB,KAAK;AAAA,MACtB,iBAAiB,KAAK;AAAA,MACtB,aAAa,KAAK;AAAA,MAClB,+BAA+B,KAAK;AAAA,MACpC,+BAA+B,KAAK;AAAA,MACpC,2BAA2B,KAAK;AAAA,MAChC,kCAAkC,KAAK;AAAA,MACvC,kCAAkC,KAAK;AAAA,MACvC,8BAA8B,KAAK;AAAA,MACnC,gCAAgC,KAAK;AAAA,MACrC,gCAAgC,KAAK;AAAA,MACrC,4BAA4B,KAAK;AAAA,MACjC;AAAA,MACA,iBAAiB,QAAQ;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,QAAQ,KAAK,UAAU,IAAI,cAAc;AAAA,MAC1C,aAAa;AAAA,MACb,WAAW;AAAA,MACX,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,MAAM,SAAS,iBAAe;AAC9C,WAAK,mBAAmB,KAAK,WAAW;AACxC,UAAI,CAAC,eAAe,KAAK,8BAA8B;AACnD,aAAK,SAAS,MAAM;AAAA,MACxB;AACA,WAAK,SAAS;AAAA,IAClB,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,MAAM,UAAU,OAAK;AACrC,WAAK,gBAAgB,KAAK,CAAC;AAAA,IAC/B,CAAC,CAAC;AACF,SAAK,aAAa,KAAK,UAAU,IAAI,mBAAmB;AAAA,MACpD,aAAa;AAAA,MACb,WAAW;AAAA,MACX,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,WAAW,SAAS,iBAAe;AACnD,WAAK,mBAAmB,KAAK,WAAW;AACxC,UAAI,CAAC,eAAe,KAAK,8BAA8B;AACnD,aAAK,SAAS,MAAM;AAAA,MACxB;AACA,WAAK,SAAS;AAAA,IAClB,CAAC,CAAC;AACF,SAAK,gBAAgB,KAAK,UAAU,IAAI,sBAAsB;AAAA,MAC1D,aAAa;AAAA,MACb,WAAW;AAAA,MACX,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,cAAc,SAAS,iBAAe;AACtD,WAAK,mBAAmB,KAAK,WAAW;AACxC,UAAI,CAAC,eAAe,KAAK,8BAA8B;AACnD,aAAK,SAAS,MAAM;AAAA,MACxB;AACA,WAAK,SAAS;AAAA,IAClB,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,cAAc,UAAU,OAAK;AAC7C,WAAK,wBAAwB,KAAK,CAAC;AAAA,IACvC,CAAC,CAAC;AACF,QAAI,KAAK,oBAAoB;AACzB,WAAK,SAAS,eAAe,KAAK,cAAc,MAAM,IAAI,KAAK,WAAW,MAAM,IAAI,KAAK,MAAM,MAAM;AAAA,IACzG;AAEA,QAAI,UAAU,CAAC,KAAK,cAAc,SAAS,KAAK,WAAW,SAAS,KAAK,MAAM,OAAO;AACtF,SAAK,UAAU,KAAK,SAAS,CAAC,UAAU;AACpC,UAAI,MAAM;AAAA,QAAO;AAAA;AAAA,MAAkB,KAAK,MAAM;AAAA,QAAO;AAAA;AAAA,MAAmB,KAAK,MAAM;AAAA,QAAO;AAAA;AAAA,MAAc,GAAG;AACvG,YAAI,QAAQ,QAAQ,QAAQ,SAAS,aAAa;AAClD,YAAI,SAAS,GAAG;AACZ,cAAI,WAAW;AACf,cAAI,MAAM;AAAA,YAAO;AAAA;AAAA,UAAmB,GAAG;AACnC,wBAAY,QAAQ,KAAK,QAAQ;AAAA,UACrC,WACS,MAAM;AAAA,YAAO;AAAA;AAAA,UAAkB,GAAG;AACvC,gBAAI,UAAU,GAAG;AACb,yBAAW,QAAQ,SAAS;AAAA,YAChC,OACK;AACD,yBAAW,QAAQ;AAAA,YACvB;AAAA,UACJ;AACA,cAAI,MAAM;AAAA,YAAO;AAAA;AAAA,UAAc,GAAG;AAC9B,oBAAQ,KAAK,EAAE,KAAK;AACpB,iBAAK,SAAS,MAAM;AAAA,UACxB,WACS,YAAY,GAAG;AACpB,oBAAQ,QAAQ,EAAE,MAAM;AAAA,UAC5B;AACA,UAAI,YAAY,KAAK,OAAO,IAAI;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,SAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,MAAM,UAAU,KAAK,qBAAqB,UAAU;AAClE,SAAK,SAAS,YAAY,KAAK,cAAc,OAAO;AACpD,SAAK,SAAS,YAAY,KAAK,WAAW,OAAO;AACjD,SAAK,SAAS,YAAY,KAAK,MAAM,OAAO;AAC5C,SAAK,QAAQ,YAAY,KAAK,QAAQ;AACtC,QAAI,QAAQ;AACR,aAAO,YAAY,KAAK,OAAO;AAAA,IACnC;AACA,SAAK,UAAc,sBAAsB,KAAK,SAAS,cAAc,oBAAoB,CAAC,MAAM;AAC5F,WAAK,uBAAuB;AAAA,IAChC,CAAC,CAAC;AACF,SAAK,UAAc,sBAAsB,KAAK,SAAS,cAAc,kBAAkB,CAAC,MAAM;AAC1F,WAAK,uBAAuB;AAC5B,WAAK,SAAS,KAAK;AAAA,IACvB,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,WAAW,KAAK,CAAC,CAAC;AACzE,SAAK,QAAQ,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,SAAS,KAAK,CAAC,CAAC;AACrE,SAAK,QAAQ,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,SAAS,KAAK,CAAC;AACpE,SAAK,YAAY,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,aAAa,KAAK,CAAC,CAAC;AAAA,EACjF;AAAA,EACA,SAAS;AACL,SAAK,QAAQ,UAAU,OAAO,UAAU;AACxC,SAAK,SAAS,OAAO;AACrB,SAAK,MAAM,OAAO;AAClB,SAAK,WAAW,OAAO;AACvB,SAAK,cAAc,OAAO;AAAA,EAC9B;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,UAAU,IAAI,UAAU;AACrC,SAAK,SAAS,QAAQ;AACtB,SAAK,MAAM,QAAQ;AACnB,SAAK,WAAW,QAAQ;AACxB,SAAK,cAAc,QAAQ;AAAA,EAC/B;AAAA,EACA,2BAA2B,OAAO;AAC9B,SAAK,+BAA+B;AAAA,EACxC;AAAA,EACA,WAAW,SAAS;AAChB,QAAI,SAAS;AACT,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,WAAW;AACP,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,KAAK,SAAS,UAAU,OAAO;AAC/B,WAAK,SAAS,QAAQ;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,MAAM,QAAQ;AACV,SAAK,0BAA0B,OAAO;AACtC,SAAK,8BAA8B,OAAO;AAC1C,SAAK,8BAA8B,OAAO;AAC1C,SAAK,kBAAkB,OAAO;AAC9B,SAAK,kBAAkB,OAAO;AAC9B,SAAK,cAAc,OAAO;AAC1B,SAAK,gCAAgC,OAAO;AAC5C,SAAK,gCAAgC,OAAO;AAC5C,SAAK,4BAA4B,OAAO;AACxC,SAAK,mCAAmC,OAAO;AAC/C,SAAK,mCAAmC,OAAO;AAC/C,SAAK,+BAA+B,OAAO;AAC3C,SAAK,iCAAiC,OAAO;AAC7C,SAAK,iCAAiC,OAAO;AAC7C,SAAK,6BAA6B,OAAO;AACzC,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,cAAc;AACV,QAAI,KAAK,SAAS;AACd,YAAM,iBAAiB;AAAA,QACnB,yBAAyB,KAAK;AAAA,QAC9B,6BAA6B,KAAK;AAAA,QAClC,6BAA6B,KAAK;AAAA,MACtC;AACA,WAAK,MAAM,MAAM,cAAc;AAC/B,WAAK,WAAW,MAAM,cAAc;AACpC,WAAK,cAAc,MAAM,cAAc;AACvC,YAAM,iBAAiB;AAAA,QACnB,iBAAiB,KAAK;AAAA,QACtB,iBAAiB,KAAK;AAAA,QACtB,aAAa,KAAK;AAAA,QAClB,+BAA+B,KAAK;AAAA,QACpC,+BAA+B,KAAK;AAAA,QACpC,2BAA2B,KAAK;AAAA,QAChC,kCAAkC,KAAK;AAAA,QACvC,kCAAkC,KAAK;AAAA,QACvC,8BAA8B,KAAK;AAAA,QACnC,gCAAgC,KAAK;AAAA,QACrC,gCAAgC,KAAK;AAAA,QACrC,4BAA4B,KAAK;AAAA,MACrC;AACA,WAAK,SAAS,MAAM,cAAc;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,SAAK,SAAS,OAAO;AAAA,EACzB;AAAA,EACA,QAAQ;AACJ,SAAK,SAAS,MAAM;AAAA,EACxB;AAAA,EACA,mBAAmB;AACf,WAAO,KAAK,cAAc;AAAA,EAC9B;AAAA,EACA,iBAAiB,OAAO;AACpB,SAAK,cAAc,UAAU;AAAA,EACjC;AAAA,EACA,gBAAgB;AACZ,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA,EACA,cAAc,OAAO;AACjB,SAAK,WAAW,UAAU;AAAA,EAC9B;AAAA,EACA,WAAW;AACP,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,MAAM,UAAU;AACrB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,uBAAuB;AACnB,SAAK,cAAc,MAAM;AAAA,EAC7B;AAAA,EACA,uBAAuB;AACnB,SAAK,QAAQ,UAAU,OAAO,eAAgB,KAAK,yBAA0B;AAC7E,SAAK,4BAA4B,IAAI,KAAK;AAC1C,SAAK,QAAQ,UAAU,IAAI,eAAgB,KAAK,yBAA0B;AAAA,EAC9E;AAAA,EACA,WAAW;AACP,SAAK,SAAS,SAAS;AAAA,EAC3B;AAAA,EACA,eAAe;AACX,SAAK,SAAS,YAAY;AAAA,EAC9B;AACJ;;;ACpSA,OAAO;AAEP,IAAMC,qBAAwB,SAAS,gBAAgB,OAAO;AAC9D,IAAM,0BAA8B,SAAS,8BAA8B,eAAe;AACnF,IAAM,uBAAN,cAAmC,SAAS;AAAA,EAC/C,YAAY,MAAM;AACd,UAAM;AAAA;AAAA,MAEF,MAAM,QAAQ;AAAA,MACd,OAAO,0BAA0B,KAAK;AAAA,MACtC,WAAW,KAAK;AAAA,MAChB,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC;AAAA,EACL;AACJ;AACO,IAAM,eAAN,cAA2B,OAAO;AAAA,EACrC,YAAY,QAAQ,qBAAqB,oBAAoB,SAAS;AAClE,UAAM;AACN,SAAK,qBAAqB;AAC1B,SAAK,+BAA+B;AACpC,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB,KAAK,UAAU,IAAI,QAAQ,CAAC;AACtD,SAAK,oBAAoB,KAAK,mBAAmB;AACjD,SAAK,aAAa,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC9C,SAAK,YAAY,KAAK,WAAW;AACjC,SAAK,eAAe,KAAK,UAAU,IAAI,QAAQ,CAAC;AAChD,SAAK,WAAW,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC5C,SAAK,WAAW,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC5C,SAAK,yBAAyB,KAAK,UAAU,IAAI,QAAQ,CAAC;AAC1D,SAAK,wBAAwB,KAAK,uBAAuB;AACzD,SAAK,sBAAsB;AAC3B,SAAK,cAAc,QAAQ,eAAe;AAC1C,SAAK,aAAa,QAAQ;AAC1B,SAAK,QAAQ,QAAQ,SAASA;AAC9B,SAAK,0BAA0B,QAAQ;AACvC,SAAK,8BAA8B,QAAQ;AAC3C,SAAK,8BAA8B,QAAQ;AAC3C,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,cAAc,QAAQ;AAC3B,SAAK,4BAA4B,QAAQ;AACzC,SAAK,gCAAgC,QAAQ;AAC7C,SAAK,gCAAgC,QAAQ;AAC7C,SAAK,+BAA+B,QAAQ;AAC5C,SAAK,mCAAmC,QAAQ;AAChD,SAAK,mCAAmC,QAAQ;AAChD,SAAK,6BAA6B,QAAQ;AAC1C,SAAK,iCAAiC,QAAQ;AAC9C,SAAK,iCAAiC,QAAQ;AAC9C,UAAM,0BAA0B,QAAQ,2BAA2B;AACnE,UAAM,UAAU,QAAQ,WAAW,CAAC;AACpC,UAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,UAAM,gBAAgB,CAAC,CAAC,QAAQ;AAChC,UAAM,oBAAoB,QAAQ;AAClC,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,QAAQ,UAAU,IAAI,kBAAkB;AAC7C,SAAK,WAAW,KAAK,UAAU,IAAI,gBAAgB,KAAK,SAAS,KAAK,qBAAqB;AAAA,MACvF,WAAW,KAAK,SAAS;AAAA,MACzB,aAAa,KAAK,eAAe;AAAA,MACjC,mBAAmB;AAAA,QACf,YAAY,KAAK;AAAA,MACrB;AAAA,MACA,iBAAiB,KAAK;AAAA,MACtB,iBAAiB,KAAK;AAAA,MACtB,aAAa,KAAK;AAAA,MAClB,+BAA+B,KAAK;AAAA,MACpC,+BAA+B,KAAK;AAAA,MACpC,2BAA2B,KAAK;AAAA,MAChC,kCAAkC,KAAK;AAAA,MACvC,kCAAkC,KAAK;AAAA,MACvC,8BAA8B,KAAK;AAAA,MACnC,gCAAgC,KAAK;AAAA,MACrC,gCAAgC,KAAK;AAAA,MACrC,4BAA4B,KAAK;AAAA,MACjC;AAAA,MACA,iBAAiB,QAAQ;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,eAAe,KAAK,UAAU,IAAI,qBAAqB;AAAA,MACxD,aAAa;AAAA,MACb,WAAW;AAAA,MACX,yBAAyB,KAAK;AAAA,MAC9B,6BAA6B,KAAK;AAAA,MAClC,6BAA6B,KAAK;AAAA,IACtC,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,aAAa,SAAS,iBAAe;AACrD,WAAK,mBAAmB,KAAK,WAAW;AACxC,UAAI,CAAC,eAAe,KAAK,8BAA8B;AACnD,aAAK,SAAS,MAAM;AAAA,MACxB;AACA,WAAK,SAAS;AAAA,IAClB,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,aAAa,UAAU,OAAK;AAC5C,WAAK,uBAAuB,KAAK,CAAC;AAAA,IACtC,CAAC,CAAC;AACF,QAAI,KAAK,oBAAoB;AACzB,WAAK,qBAAqB,KAAK,aAAa,MAAM;AAAA,IACtD,OACK;AACD,WAAK,qBAAqB;AAAA,IAC9B;AAEA,QAAI,UAAU,CAAC,KAAK,aAAa,OAAO;AACxC,SAAK,UAAU,KAAK,SAAS,CAAC,UAAU;AACpC,UAAI,MAAM;AAAA,QAAO;AAAA;AAAA,MAAkB,KAAK,MAAM;AAAA,QAAO;AAAA;AAAA,MAAmB,KAAK,MAAM;AAAA,QAAO;AAAA;AAAA,MAAc,GAAG;AACvG,YAAI,QAAQ,QAAQ,QAAQ,SAAS,aAAa;AAClD,YAAI,SAAS,GAAG;AACZ,cAAI,WAAW;AACf,cAAI,MAAM;AAAA,YAAO;AAAA;AAAA,UAAmB,GAAG;AACnC,wBAAY,QAAQ,KAAK,QAAQ;AAAA,UACrC,WACS,MAAM;AAAA,YAAO;AAAA;AAAA,UAAkB,GAAG;AACvC,gBAAI,UAAU,GAAG;AACb,yBAAW,QAAQ,SAAS;AAAA,YAChC,OACK;AACD,yBAAW,QAAQ;AAAA,YACvB;AAAA,UACJ;AACA,cAAI,MAAM;AAAA,YAAO;AAAA;AAAA,UAAc,GAAG;AAC9B,oBAAQ,KAAK,EAAE,KAAK;AACpB,iBAAK,SAAS,MAAM;AAAA,UACxB,WACS,YAAY,GAAG;AACpB,oBAAQ,QAAQ,EAAE,MAAM;AAAA,UAC5B;AACA,UAAI,YAAY,KAAK,OAAO,IAAI;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,WAAW,SAAS,cAAc,KAAK;AAC3C,aAAS,YAAY;AACrB,aAAS,MAAM,UAAU,KAAK,qBAAqB,UAAU;AAC7D,aAAS,YAAY,KAAK,aAAa,OAAO;AAC9C,SAAK,QAAQ,YAAY,QAAQ;AACjC,QAAI,QAAQ;AACR,aAAO,YAAY,KAAK,OAAO;AAAA,IACnC;AACA,SAAK,UAAU,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,WAAW,KAAK,CAAC,CAAC;AACzE,SAAK,QAAQ,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,SAAS,KAAK,CAAC,CAAC;AACrE,SAAK,QAAQ,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,SAAS,KAAK,CAAC;AACpE,SAAK,YAAY,KAAK,SAAS,cAAc,CAAC,MAAM,KAAK,aAAa,KAAK,CAAC,CAAC;AAAA,EACjF;AAAA,EACA,SAAS;AACL,SAAK,QAAQ,UAAU,OAAO,UAAU;AACxC,SAAK,SAAS,OAAO;AACrB,SAAK,aAAa,OAAO;AAAA,EAC7B;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,UAAU,IAAI,UAAU;AACrC,SAAK,SAAS,QAAQ;AACtB,SAAK,aAAa,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW,SAAS;AAChB,QAAI,SAAS;AACT,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,MAAM,QAAQ;AACV,SAAK,0BAA0B,OAAO;AACtC,SAAK,8BAA8B,OAAO;AAC1C,SAAK,8BAA8B,OAAO;AAC1C,SAAK,kBAAkB,OAAO;AAC9B,SAAK,kBAAkB,OAAO;AAC9B,SAAK,cAAc,OAAO;AAC1B,SAAK,gCAAgC,OAAO;AAC5C,SAAK,gCAAgC,OAAO;AAC5C,SAAK,4BAA4B,OAAO;AACxC,SAAK,mCAAmC,OAAO;AAC/C,SAAK,mCAAmC,OAAO;AAC/C,SAAK,+BAA+B,OAAO;AAC3C,SAAK,iCAAiC,OAAO;AAC7C,SAAK,iCAAiC,OAAO;AAC7C,SAAK,6BAA6B,OAAO;AACzC,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,cAAc;AACV,QAAI,KAAK,SAAS;AACd,YAAM,iBAAiB;AAAA,QACnB,yBAAyB,KAAK;AAAA,QAC9B,6BAA6B,KAAK;AAAA,QAClC,6BAA6B,KAAK;AAAA,MACtC;AACA,WAAK,aAAa,MAAM,cAAc;AACtC,YAAM,iBAAiB;AAAA,QACnB,iBAAiB,KAAK;AAAA,QACtB,iBAAiB,KAAK;AAAA,QACtB,aAAa,KAAK;AAAA,QAClB,+BAA+B,KAAK;AAAA,QACpC,+BAA+B,KAAK;AAAA,QACpC,2BAA2B,KAAK;AAAA,QAChC,kCAAkC,KAAK;AAAA,QACvC,kCAAkC,KAAK;AAAA,QACvC,8BAA8B,KAAK;AAAA,QACnC,gCAAgC,KAAK;AAAA,QACrC,gCAAgC,KAAK;AAAA,QACrC,4BAA4B,KAAK;AAAA,MACrC;AACA,WAAK,SAAS,MAAM,cAAc;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,SAAK,SAAS,OAAO;AAAA,EACzB;AAAA,EACA,QAAQ;AACJ,SAAK,SAAS,MAAM;AAAA,EACxB;AAAA,EACA,kBAAkB;AACd,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA,EACA,gBAAgB,OAAO;AACnB,SAAK,aAAa,UAAU;AAAA,EAChC;AAAA,EACA,kBAAkB;AACd,SAAK,aAAa,MAAM;AAAA,EAC5B;AAAA,EACA,WAAW;AACP,QAAI,KAAK,UAAU;AACf,WAAK,SAAS,SAAS;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,IAAI,MAAM,UAAU;AAChB,SAAK,SAAS,eAAe,KAAK;AAClC,SAAK,SAAS,QAAQ;AACtB,SAAK,QAAQ,MAAM,QAAQ,WAAW;AAAA,EAC1C;AAAA,EACA,UAAU;AACN,UAAM,QAAQ;AAAA,EAClB;AACJ;;;AClPA,IAAI,aAA0C,SAAU,YAAY,QAAQ,KAAK,MAAM;AACnF,MAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,KAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA,MACxH,UAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,IAAK,KAAI,IAAI,WAAW,CAAC,EAAG,MAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,SAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAChE;AACA,IAAI,UAAoC,SAAU,YAAY,WAAW;AACrE,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACxE;AAMO,IAAM,2BAA2B,IAAI,cAAc,wBAAwB,OAAO,SAAS,wBAAwB,gCAAgC,CAAC;AACpJ,IAAM,iCAAiC;AAC9C,IAAM,6CAA6C;AACnD,IAAM,8CAA8C;AACpD,SAAS,wBAAwB,mBAAmB,QAAQ,YAAY;AACpE,MAAI,cAAc,YAAY,MAAM,EAAE,OAAO,iBAAiB;AAClE;AACA,SAAS,oCAAoC,mBAAmB,QAAQ;AACpE,SAAO,kBAAkB,aAAa,OAAO,MAAM;AACvD;AACA,SAAS,uBAAuB,mBAAmB,YAAY;AAC3D,SAAO,kBAAkB,WAAW,SAAS,aAAa,EAAE,SAAS,UAAU;AACnF;AACO,SAAS,4DAA4D,mBAAmB,QAAQ;AACnG,QAAM,0BAA0B,oCAAoC,mBAAmB,MAAM;AAC7F,0BAAwB,yBAAyB,QAAQ,8BAA8B;AACvF,QAAM,sCAAsC,IAAI,cAAc,4CAA4C,IAAI,EAAE,OAAO,uBAAuB;AAC9I,QAAM,uCAAuC,IAAI,cAAc,6CAA6C,IAAI,EAAE,OAAO,uBAAuB;AAChJ,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB,MAAMC,gCAA+B,UAAU;AAAA,EACxE,YAAY,WAAW,qBAAqB,SAAS,mBAAmB,kBAAkB,OAAO;AAC7F,UAAM,WAAW,qBAAqB,iBAAiB,OAAO;AAC9D,SAAK,UAAU,4DAA4D,mBAAmB,EAAE,QAAQ,KAAK,SAAS,SAAS,kBAAkB,KAAK,SAAS,CAAC,EAAE,uBAAuB;AAAA,EAC7L;AACJ;AACA,yBAAyB,WAAW;AAAA,EAChC,QAAQ,GAAG,kBAAkB;AACjC,GAAG,sBAAsB;AAEzB,IAAI,4BAA4B,MAAMC,mCAAkC,aAAa;AAAA,EACjF,YAAY,WAAW,qBAAqB,SAAS,mBAAmB,qBAAqB,OAAO;AAChG,UAAM,WAAW,qBAAqB,oBAAoB,OAAO;AACjE,SAAK,UAAU,4DAA4D,mBAAmB,EAAE,QAAQ,KAAK,SAAS,SAAS,kBAAkB,KAAK,SAAS,CAAC,EAAE,uBAAuB;AAAA,EAC7L;AACJ;AACA,4BAA4B,WAAW;AAAA,EACnC,QAAQ,GAAG,kBAAkB;AACjC,GAAG,yBAAyB;AAE5B,oBAAoB,iCAAiC;AAAA,EACjD,IAAI;AAAA,EACJ,QAAQ;AAAA,EACR,MAAM,eAAe,IAAI,eAAe,IAAI,8BAA8B,GAAG,eAAe,OAAO,6CAA6C,IAAI,GAAG,yBAAyB,UAAU,KAAK,CAAC;AAAA,EAChM,SAAS;AAAA,EACT,WAAW;AAAA,IAAC,MAAgB;AAAA;AAAA,EAAgB;AAAA,EAC5C,SAAS,CAAC,aAAa;AACnB,UAAM,SAAS,uBAAuB,SAAS,IAAI,kBAAkB,GAAG,8BAA8B;AACtG,QAAI,QAAQ;AACR,YAAM,kBAAkB,OAAO;AAC/B,sBAAgB,kBAAkB;AAAA,IACtC;AAAA,EACJ;AACJ,CAAC;AACD,oBAAoB,iCAAiC;AAAA,EACjD,IAAI;AAAA,EACJ,QAAQ;AAAA,EACR,MAAM,eAAe,IAAI,eAAe,IAAI,8BAA8B,GAAG,eAAe,OAAO,4CAA4C,IAAI,GAAG,yBAAyB,UAAU,KAAK,CAAC;AAAA,EAC/L,SAAS;AAAA,EACT,WAAW;AAAA,IAAC,MAAgB;AAAA;AAAA,EAAkB;AAAA,EAC9C,SAAS,CAAC,aAAa;AACnB,UAAM,SAAS,uBAAuB,SAAS,IAAI,kBAAkB,GAAG,8BAA8B;AACtG,QAAI,QAAQ;AACR,YAAM,kBAAkB,OAAO;AAC/B,sBAAgB,cAAc;AAAA,IAClC;AAAA,EACJ;AACJ,CAAC;;;ACrFM,SAAS,0BAA0B,mBAAmB;AACzD,MAAI,IAAI;AACR,WAAS,KAAK,kBAAkB,iBAAiB,sBAAsB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,uBAAuB,OAAO,UAAU,KAAK,kBAAkB,iBAAiB,kBAAkB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,uBAAuB,OAAO;AACjS;;;AJHA,IAAI,YAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAqBA,IAAM,oBAAoB,aAAa,kBAAkB,QAAQ,WAAe,SAAS,qBAAqB,yDAA2D,CAAC;AAC1K,IAAM,oBAAoB,aAAa,kBAAkB,QAAQ,cAAkB,SAAS,qBAAqB,4DAA4D,CAAC;AAC9K,IAAM,mBAAmB,aAAa,iBAAiB,QAAQ,aAAiB,SAAS,oBAAoB,2DAA2D,CAAC;AAClK,IAAM,kBAAkB,aAAa,gBAAgB,QAAQ,SAAa,SAAS,mBAAmB,+CAAiD,CAAC;AACxJ,IAAM,qBAAqB,aAAa,oBAAoB,QAAQ,YAAgB,SAAS,sBAAsB,mDAAqD,CAAC;AACzK,IAAM,wBAAwB,aAAa,uBAAuB,QAAQ,SAAa,SAAS,yBAAyB,qDAAuD,CAAC;AACjL,IAAM,oBAAoB,aAAa,mBAAmB,QAAQ,WAAe,SAAS,qBAAqB,iDAAmD,CAAC;AAC1K,IAAM,uBAA2B,SAAS,cAAc,MAAM;AAC9D,IAAM,6BAAiC,SAAS,oBAAoB,MAAM;AAC1E,IAAM,+BAAmC,SAAS,6BAA6B,gBAAgB;AAC/F,IAAM,2BAA+B,SAAS,yBAAyB,YAAY;AACnF,IAAM,kCAAsC,SAAS,6BAA6B,mBAAmB;AACrG,IAAM,sBAA0B,SAAS,qBAAqB,OAAO;AACrE,IAAM,0BAA8B,SAAS,iBAAiB,SAAS;AACvE,IAAM,gCAAoC,SAAS,uBAAuB,SAAS;AACnF,IAAM,wBAA4B,SAAS,uBAAuB,SAAS;AAC3E,IAAM,4BAAgC,SAAS,0BAA0B,aAAa;AACtF,IAAM,oCAAwC,SAAS,6BAA6B,gBAAgB;AACpG,IAAM,gCAAoC,SAAS,2BAA2B,gGAAgG,aAAa;AACpL,IAAM,uBAA2B,SAAS,yBAAyB,YAAY;AAC/E,IAAM,iBAAqB,SAAS,mBAAmB,YAAY;AAC1E,IAAM,4BAA4B;AAClC,IAAM,aAAa;AACnB,IAAM,wBAAwB,aAAa;AAC3C,IAAI,0BAA0B;AAE9B,IAAM,yBAAyB;AAC/B,IAAM,wCAAwC;AAC9C,IAAM,aAAuB,cAAc,MAAoB;AACxD,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,iBAAiB;AACzB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,oBAAoB;AACzB,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,QAAQ,YAAY;AAAA,EAC7B;AACJ;AACA,SAAS,mCAAmC,OAAO,OAAO,UAAU;AAChE,QAAM,cAAc,CAAC,CAAC,MAAM,MAAM,IAAI;AACtC,MAAI,YAAY,eAAe,SAAS,iBAAiB,GAAG;AACxD,UAAM,gBAAgB;AACtB;AAAA,EACJ;AACJ;AACA,SAAS,qCAAqC,OAAO,OAAO,UAAU;AAClE,QAAM,cAAc,CAAC,CAAC,MAAM,MAAM,IAAI;AACtC,MAAI,YAAY,eAAe,SAAS,eAAe,SAAS,MAAM,QAAQ;AAC1E,UAAM,gBAAgB;AACtB;AAAA,EACJ;AACJ;AACO,IAAM,aAAN,MAAM,oBAAmB,OAAO;AAAA,EACnC,YAAY,YAAY,YAAY,OAAO,qBAAqB,mBAAmB,mBAAmB,cAAc,gBAAgB,qBAAqB;AACrJ,UAAM;AACN,SAAK,gBAAgB;AACrB,SAAK,kBAAkB,CAAC;AACxB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,uBAAuB;AAC5B,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAC5B,SAAK,sCAAsC,CAAC,CAAC,eAAe;AAAA,MAAW;AAAA,MAAuC;AAAA;AAAA,IAAc;AAC5H,SAAK,aAAa;AAClB,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,wBAAwB,IAAI,QAAQ,GAAG;AAC5C,SAAK,UAAU,aAAa,MAAM,KAAK,sBAAsB,OAAO,CAAC,CAAC;AACtE,SAAK,UAAU,KAAK,OAAO,yBAAyB,CAAC,MAAM,KAAK,gBAAgB,CAAC,CAAC,CAAC;AACnF,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAC3B,SAAK,WAAW,SAAS,OAAO;AAChC,SAAK,UAAU,KAAK,YAAY,yBAAyB,CAAC,MAAM;AAC5D,UAAI,EAAE;AAAA,QAAW;AAAA;AAAA,MAAiB,GAAG;AACjC,YAAI,KAAK,YAAY;AAAA,UAAU;AAAA;AAAA,QAAiB,GAAG;AAE/C,eAAK,OAAO,OAAO,EAAE,mBAAmB,MAAM,GAAG,KAAK;AAAA,QAC1D;AACA,aAAK,eAAe;AAAA,MACxB;AACA,UAAI,EAAE;AAAA,QAAW;AAAA;AAAA,MAAoB,GAAG;AACpC,aAAK,sBAAsB;AAAA,MAC/B;AACA,UAAI,EAAE;AAAA,QAAW;AAAA;AAAA,MAA4B,GAAG;AAC5C,aAAK,2BAA2B;AAAA,MACpC;AACA,UAAI,EAAE;AAAA,QAAW;AAAA;AAAA,MAAa,GAAG;AAC7B,cAAM,qBAAqB,KAAK,YAAY;AAAA,UAAU;AAAA;AAAA,QAAa,EAAE;AACrE,YAAI,sBAAsB,CAAC,KAAK,WAAW;AACvC,eAAK,YAAY,IAAI,mBAAmB,CAAC;AACzC,eAAK,cAAc;AAAA,QACvB;AACA,YAAI,CAAC,sBAAsB,KAAK,WAAW;AACvC,eAAK,gBAAgB;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,2BAA2B;AAChC,SAAK,UAAU,KAAK,YAAY,2BAA2B,MAAM;AAC7D,UAAI,KAAK,YAAY;AACjB,aAAK,iCAAiC;AAAA,MAC1C;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,YAAY,uBAAuB,MAAM,UAAU,MAAM,QAAQ,QAAQ,aAAa;AACtG,UAAI,KAAK,YAAY;AACjB,YAAI,mBAAmB,MAAM,KAAK,YAAY,oBAAoB;AAClE,YAAI,oBAAoB,qBAAqB,KAAK,OAAO,cAAc;AACnE,eAAK,OAAO,OAAO,EAAE,cAAc,iBAAiB,GAAG,KAAK;AAC5D,eAAK,WAAW,OAAO;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC,CAAC;AACH,SAAK,oBAAoB,2BAA2B,OAAO,iBAAiB;AAC5E,SAAK,oBAAoB,KAAK,UAAc,WAAW,KAAK,WAAW,SAAS,YAAY,CAAC;AAC7F,SAAK,UAAU,KAAK,kBAAkB,WAAW,MAAM;AACnD,WAAK,kBAAkB,IAAI,IAAI;AAC/B,WAAK,mBAAmB;AAAA,IAC5B,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,kBAAkB,UAAU,MAAM;AAClD,WAAK,kBAAkB,IAAI,KAAK;AAAA,IACpC,CAAC,CAAC;AACF,SAAK,uBAAuB,8BAA8B,OAAO,iBAAiB;AAClF,SAAK,uBAAuB,KAAK,UAAc,WAAW,KAAK,cAAc,SAAS,YAAY,CAAC;AACnG,SAAK,UAAU,KAAK,qBAAqB,WAAW,MAAM;AACtD,WAAK,qBAAqB,IAAI,IAAI;AAClC,WAAK,mBAAmB;AAAA,IAC5B,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,qBAAqB,UAAU,MAAM;AACrD,WAAK,qBAAqB,IAAI,KAAK;AAAA,IACvC,CAAC,CAAC;AACF,SAAK,YAAY,iBAAiB,IAAI;AACtC,QAAI,KAAK,YAAY;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE,oBAAoB;AAC9D,WAAK,YAAY,IAAI,mBAAmB,CAAC;AAAA,IAC7C;AACA,SAAK,YAAY,aAAa,cAAc,CAAC;AAC7C,SAAK,UAAU,aAAa,sBAAsB,KAAK,YAAY,KAAK,IAAI,CAAC,CAAC;AAC9E,SAAK,UAAU,KAAK,YAAY,iBAAiB,MAAM;AACnD,UAAI,CAAC,KAAK,YAAY;AAClB;AAAA,MACJ;AACA,WAAK,cAAc;AAAA,IACvB,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,YAAY,kBAAkB,CAAC,MAAM;AACrD,UAAI,EAAE,kBAAkB;AACpB,aAAK,gBAAgB;AACrB;AAAA,MACJ;AAEA,iBAAW,MAAM;AACb,aAAK,gBAAgB;AAAA,MACzB,GAAG,CAAC;AAAA,IACR,CAAC,CAAC;AAAA,EACN;AAAA;AAAA,EAEA,QAAQ;AACJ,WAAO,YAAW;AAAA,EACtB;AAAA,EACA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,cAAc;AACV,QAAI,KAAK,YAAY;AACjB,aAAO;AAAA,QACH,YAAY;AAAA;AAAA,MAChB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,gBAAgB,GAAG;AACf,QAAI,EAAE,cAAc;AAChB,UAAI;AACA,aAAK,qBAAqB;AAC1B,aAAK,WAAW,SAAS,KAAK,OAAO,YAAY;AAAA,MACrD,UACA;AACI,aAAK,qBAAqB;AAAA,MAC9B;AACA,WAAK,eAAe;AAAA,IACxB;AACA,QAAI,EAAE,eAAe;AACjB,WAAK,cAAc,SAAS,QAAQ,KAAK,OAAO;AAAA,IACpD;AACA,QAAI,EAAE,YAAY;AACd,UAAI,KAAK,OAAO,YAAY;AACxB,aAAK,QAAQ;AAAA,MACjB,OACK;AACD,aAAK,MAAM,IAAI;AAAA,MACnB;AAAA,IACJ;AACA,QAAI,EAAE,mBAAmB;AACrB,UAAI,KAAK,OAAO,mBAAmB;AAC/B,YAAI,CAAC,KAAK,YAAY;AAAA,UAAU;AAAA;AAAA,QAAiB,KAAK,CAAC,KAAK,mBAAmB;AAC3E,eAAK,oBAAoB;AACzB,eAAK,cAAc,QAAY,cAAc,KAAK,WAAW,OAAO;AACpE,eAAK,eAAe;AACpB,eAAK,cAAc,SAAS,OAAO;AAAA,QACvC;AAAA,MACJ,OACK;AACD,YAAI,KAAK,mBAAmB;AACxB,eAAK,oBAAoB;AACzB,eAAK,eAAe;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,EAAE,cAAc,EAAE,uBAAuB,KAAK,OAAO,cAAc,KAAK,OAAO,oBAAoB;AACpG,UAAI,KAAK,iBAAiB,GAAG;AACzB,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,EAAE,SAAS;AACX,WAAK,WAAW,SAAS,KAAK,OAAO,OAAO;AAAA,IAChD;AACA,QAAI,EAAE,WAAW;AACb,WAAK,WAAW,cAAc,KAAK,OAAO,SAAS;AAAA,IACvD;AACA,QAAI,EAAE,WAAW;AACb,WAAK,WAAW,iBAAiB,KAAK,OAAO,SAAS;AAAA,IAC1D;AACA,QAAI,EAAE,cAAc;AAChB,WAAK,cAAc,gBAAgB,KAAK,OAAO,YAAY;AAAA,IAC/D;AACA,QAAI,EAAE,aAAa;AACf,UAAI,KAAK,OAAO,aAAa;AACzB,aAAK,qBAAqB,UAAU;AAAA,MACxC,OACK;AACD,aAAK,qBAAqB,UAAU;AAAA,MACxC;AACA,WAAK,iCAAiC;AAAA,IAC1C;AACA,QAAI,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB;AACvD,UAAI,iBAAkB,KAAK,OAAO,aAAa,SAAS,KAAK,KAAK,OAAO,iBAAiB;AAC1F,WAAK,SAAS,UAAU,OAAO,cAAc,cAAc;AAC3D,WAAK,oBAAoB;AACzB,WAAK,eAAe;AAAA,IACxB;AACA,QAAI,EAAE,gBAAgB,EAAE,cAAc;AAClC,WAAK,gBAAgB;AAAA,IACzB;AACA,QAAI,EAAE,eAAe;AACjB,WAAK,sBAAsB;AAAA,IAC/B;AACA,QAAI,EAAE,MAAM;AACR,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,wBAAwB;AACpB,SAAK,sBAAsB,QAAQ,KAAK,eAAe,KAAK,IAAI,CAAC,EAAE,KAAK,QAAW,iBAAiB;AAAA,EACxG;AAAA,EACA,iBAAiB;AACb,QAAI,KAAK,OAAO,cAAc;AAC1B,WAAK,WAAW,SAAS,aAAa;AAAA,IAC1C;AACA,QAAI,KAAK,OAAO,eAAe;AAC3B,WAAK,cAAc,SAAS,aAAa;AAAA,IAC7C;AAAA,EACJ;AAAA,EACA,sBAAsB;AAClB,SAAK,cAAc,MAAM,WAAW,0BAA0B;AAC9D,QAAI,KAAK,OAAO,gBAAgB,eAAe;AAC3C,WAAK,cAAc,QAAQ;AAAA,IAC/B,OACK;AACD,WAAK,cAAc,QAAQ;AAAA,IAC/B;AAEA,QAAI,KAAK,cAAc,YAAY;AAC/B,WAAK,cAAc,YAAY,KAAK,cAAc,UAAU;AAAA,IAChE;AACA,QAAI;AACJ,QAAI,KAAK,OAAO,eAAe,GAAG;AAC9B,UAAI,eAAe,OAAO,KAAK,OAAO,YAAY;AAClD,UAAI,KAAK,OAAO,gBAAgB,eAAe;AAC3C,wBAAgB;AAAA,MACpB;AACA,UAAI,kBAAkB,OAAO,KAAK,OAAO,eAAe;AACxD,UAAI,oBAAoB,KAAK;AACzB,0BAAkB;AAAA,MACtB;AACA,cAAgB,OAAO,sBAAsB,iBAAiB,YAAY;AAAA,IAC9E,OACK;AACD,cAAQ;AAAA,IACZ;AACA,SAAK,cAAc,YAAY,SAAS,eAAe,KAAK,CAAC;AAC7D,UAAQ,KAAK,cAAc,OAAO,KAAK,OAAO,cAAc,KAAK,OAAO,YAAY,CAAC;AACrF,8BAA0B,KAAK,IAAI,yBAAyB,KAAK,cAAc,WAAW;AAAA,EAC9F;AAAA;AAAA,EAEA,cAAc,OAAO,cAAc,cAAc;AAC7C,QAAI,UAAU,gBAAgB;AAC1B,aAAO,iBAAiB,KACd,SAAS,2BAA2B,aAAa,KAAK,IACtD,SAAS,sBAAsB,uBAAuB,OAAO,YAAY;AAAA,IACvF;AACA,QAAI,cAAc;AACd,YAAM,YAAgB,SAAS,iCAAiC,+BAA+B,OAAO,cAAc,aAAa,kBAAkB,MAAM,aAAa,WAAW;AACjL,YAAM,QAAQ,KAAK,YAAY,SAAS;AACxC,UAAI,SAAU,aAAa,mBAAmB,MAAM,aAAa,KAAO,aAAa,mBAAmB,GAAI;AACxG,cAAM,cAAc,MAAM,eAAe,aAAa,eAAe;AACrE,eAAO,GAAG,WAAW,KAAK,SAAS;AAAA,MACvC;AACA,aAAO;AAAA,IACX;AACA,WAAW,SAAS,+CAA+C,uBAAuB,OAAO,YAAY;AAAA,EACjH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mCAAmC;AAC/B,QAAI,YAAY,KAAK,YAAY,aAAa;AAC9C,QAAI,cAAc,YAAa,UAAU,oBAAoB,UAAU,iBAAiB,UAAU,gBAAgB,UAAU,YAAa;AACzI,QAAI,YAAY,KAAK,qBAAqB;AAC1C,QAAI,KAAK,eAAe,aAAa,cAAc;AAC/C,WAAK,qBAAqB,OAAO;AAAA,IACrC,OACK;AACD,WAAK,qBAAqB,QAAQ;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,SAAK,WAAW,WAAW,KAAK,UAAU;AAC1C,SAAK,cAAc,WAAW,KAAK,cAAc,KAAK,iBAAiB;AACvE,SAAK,iCAAiC;AACtC,SAAK,UAAU,WAAW,KAAK,UAAU;AACzC,QAAI,sBAAuB,KAAK,OAAO,aAAa,SAAS;AAC7D,QAAI,eAAe,KAAK,OAAO,eAAe,OAAO;AACrD,SAAK,SAAS,WAAW,KAAK,cAAc,uBAAuB,gBAAgB,KAAK,OAAO,gBAAgB,CAAC;AAChH,SAAK,SAAS,WAAW,KAAK,cAAc,uBAAuB,gBAAgB,KAAK,OAAO,mBAAmB,CAAC;AACnH,SAAK,YAAY,WAAW,KAAK,cAAc,KAAK,qBAAqB,mBAAmB;AAC5F,SAAK,eAAe,WAAW,KAAK,cAAc,KAAK,qBAAqB,mBAAmB;AAC/F,SAAK,SAAS,UAAU,OAAO,kBAAkB,KAAK,iBAAiB;AACvE,SAAK,kBAAkB,YAAY,KAAK,iBAAiB;AACzD,QAAI,aAAa,CAAC,KAAK,YAAY;AAAA,MAAU;AAAA;AAAA,IAAiB;AAC9D,SAAK,kBAAkB,WAAW,KAAK,cAAc,UAAU;AAAA,EACnE;AAAA,EACA,UAAU;AACN,SAAK,gBAAgB,QAAQ,OAAK;AAC9B,mBAAa,CAAC;AAAA,IAClB,CAAC;AACD,SAAK,kBAAkB,CAAC;AACxB,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa;AAClB,YAAM,YAAY,KAAK,YAAY,aAAa;AAChD,cAAQ,KAAK,YAAY;AAAA,QAAU;AAAA;AAAA,MAAa,EAAE,qBAAqB;AAAA,QACnE,KAAK;AACD,eAAK,qBAAqB,UAAU;AACpC;AAAA,QACJ,KAAK;AACD,eAAK,qBAAqB,UAAU;AACpC;AAAA,QACJ,KAAK,aAAa;AACd,gBAAM,0BAA0B,CAAC,CAAC,aAAa,UAAU,oBAAoB,UAAU;AACvF,eAAK,qBAAqB,UAAU;AACpC;AAAA,QACJ;AAAA,QACA;AACI;AAAA,MACR;AACA,WAAK,sBAAsB;AAC3B,WAAK,eAAe;AACpB,WAAK,gBAAgB,KAAK,WAAW,MAAM;AACvC,aAAK,SAAS,UAAU,IAAI,SAAS;AACrC,aAAK,SAAS,aAAa,eAAe,OAAO;AAAA,MACrD,GAAG,CAAC,CAAC;AAEL,WAAK,gBAAgB,KAAK,WAAW,MAAM;AACvC,aAAK,WAAW,SAAS;AAAA,MAC7B,GAAG,GAAG,CAAC;AACP,WAAK,YAAY,oBAAoB,IAAI;AACzC,UAAI,wBAAwB;AAC5B,UAAI,KAAK,YAAY;AAAA,QAAU;AAAA;AAAA,MAAa,EAAE,iCAAiC,WAAW;AACtF,cAAM,UAAU,KAAK,YAAY,WAAW;AAC5C,YAAI,SAAS;AACT,gBAAM,eAAmB,uBAAuB,OAAO;AACvD,gBAAM,cAAc,KAAK,YAAY,2BAA2B,UAAU,iBAAiB,CAAC;AAC5F,gBAAM,YAAY,aAAa,QAAQ,cAAc,YAAY,OAAO;AACxE,gBAAM,WAAW,cAAc,YAAY,MAAM;AACjD,cAAI,KAAK,aAAa,WAAW,KAAK,UAAU,YAAY;AACxD,gBAAI,UAAU,gBAAgB,UAAU,iBAAiB;AACrD,sCAAwB;AAAA,YAC5B;AACA,kBAAM,mBAAuB,iBAAiB,KAAK,QAAQ,EAAE;AAC7D,gBAAI,YAAY,kBAAkB;AAC9B,sCAAwB;AAAA,YAC5B;AACA,kBAAM,YAAY,KAAK,YAAY,2BAA2B,UAAU,eAAe,CAAC;AACxF,kBAAM,UAAU,aAAa,QAAQ,YAAY,UAAU,OAAO;AAClE,gBAAI,UAAU,kBAAkB;AAC5B,sCAAwB;AAAA,YAC5B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,cAAc,qBAAqB;AAAA,IAC5C;AAAA,EACJ;AAAA,EACA,MAAM,gBAAgB;AAClB,SAAK,gBAAgB,QAAQ,OAAK;AAC9B,mBAAa,CAAC;AAAA,IAClB,CAAC;AACD,SAAK,kBAAkB,CAAC;AACxB,QAAI,KAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,SAAS,UAAU,OAAO,SAAS;AACxC,WAAK,SAAS,aAAa,eAAe,MAAM;AAChD,WAAK,WAAW,aAAa;AAC7B,UAAI,gBAAgB;AAChB,aAAK,YAAY,MAAM;AAAA,MAC3B;AACA,WAAK,YAAY,oBAAoB,IAAI;AACzC,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,gBAAgB,iBAAiB;AAC7B,UAAM,qBAAqB,KAAK,YAAY;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE;AACrE,QAAI,CAAC,oBAAoB;AACrB,WAAK,gBAAgB;AACrB;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,YAAY;AAClB;AAAA,IACJ;AACA,UAAM,WAAW,KAAK;AACtB,QAAI,KAAK,gBAAgB,UAAa,CAAC,UAAU;AAC7C;AAAA,IACJ;AACA,SAAK,YAAY,gBAAgB,CAAC,aAAa;AAC3C,eAAS,aAAa,KAAK,WAAW;AACtC,WAAK,cAAc,SAAS,QAAQ,QAAQ;AAE5C,WAAK,YAAY,aAAa,mBAAmB,KAAK,YAAY,aAAa,IAAI,SAAS,UAAU;AAAA,IAC1G,CAAC;AAAA,EACL;AAAA,EACA,cAAc,eAAe,MAAM;AAC/B,QAAI,CAAC,KAAK,YAAY;AAClB;AAAA,IACJ;AACA,UAAM,qBAAqB,KAAK,YAAY;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE;AACrE,QAAI,CAAC,oBAAoB;AACrB;AAAA,IACJ;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,IAAI,mBAAmB,CAAC;AAAA,IAC7C;AACA,UAAM,WAAW,KAAK;AACtB,SAAK,YAAY,gBAAgB,CAAC,aAAa;AAC3C,UAAI,KAAK,gBAAgB,QAAW;AAEhC,cAAM,YAAY,KAAK,WAAW;AAClC,YAAI,cAAc,SAAS,YAAY;AACnC;AAAA,QACJ;AACA,YAAI,mBAAmB,YAAY,SAAS;AAC5C,iBAAS,aAAa;AACtB,iBAAS,WAAW,KAAK,WAAW;AACpC,YAAI,cAAc;AACd,eAAK,YAAY,aAAa,KAAK,YAAY,aAAa,IAAI,gBAAgB;AAAA,QACpF;AACA;AAAA,MACJ,OACK;AACD,YAAI,mBAAmB,KAAK,WAAW;AAEvC,4BAAoB,KAAK,YAAY;AAAA,UAAU;AAAA;AAAA,QAAgB,EAAE;AACjE,YAAI,oBAAoB,GAAG;AACvB;AAAA,QACJ;AACA,iBAAS,aAAa;AACtB,aAAK,cAAc,SAAS,QAAQ,QAAQ;AAC5C,YAAI,cAAc;AACd,eAAK,YAAY,aAAa,KAAK,YAAY,aAAa,IAAI,gBAAgB;AAAA,QACpF;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,kBAAkB;AACd,SAAK,YAAY,gBAAgB,CAAC,aAAa;AAC3C,UAAI,KAAK,gBAAgB,QAAW;AAChC,iBAAS,WAAW,KAAK,WAAW;AACpC,aAAK,cAAc;AACnB,YAAI,KAAK,WAAW;AAChB,eAAK,YAAY,aAAa,KAAK,YAAY,aAAa,IAAI,KAAK,UAAU,UAAU;AACzF,eAAK,YAAY;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,YAAY,OAAO;AACf,QAAI,cAAc;AAAA,MACd,yBAAyB,MAAM,SAAS,uBAAuB;AAAA,MAC/D,6BAA6B,MAAM,SAAS,2BAA2B;AAAA,MACvE,6BAA6B,MAAM,SAAS,2BAA2B;AAAA,MACvE,iBAAiB,MAAM,SAAS,eAAe;AAAA,MAC/C,iBAAiB,MAAM,SAAS,eAAe;AAAA,MAC/C,aAAa,MAAM,SAAS,WAAW;AAAA,MACvC,+BAA+B,MAAM,SAAS,6BAA6B;AAAA,MAC3E,+BAA+B,MAAM,SAAS,6BAA6B;AAAA,MAC3E,2BAA2B,MAAM,SAAS,yBAAyB;AAAA,MACnE,kCAAkC,MAAM,SAAS,gCAAgC;AAAA,MACjF,kCAAkC,MAAM,SAAS,gCAAgC;AAAA,MACjF,8BAA8B,MAAM,SAAS,4BAA4B;AAAA,MACzE,gCAAgC,MAAM,SAAS,8BAA8B;AAAA,MAC7E,gCAAgC,MAAM,SAAS,8BAA8B;AAAA,MAC7E,4BAA4B,MAAM,SAAS,0BAA0B;AAAA,IACzE;AACA,SAAK,WAAW,MAAM,WAAW;AACjC,SAAK,cAAc,MAAM,WAAW;AACpC,SAAK,qBAAqB,MAAM,WAAW;AAAA,EAC/C;AAAA,EACA,wBAAwB;AACpB,QAAI,CAAC,KAAK,YAAY;AAClB;AAAA,IACJ;AACA,QAAI,CAAK,QAAQ,KAAK,QAAQ,GAAG;AAE7B;AAAA,IACJ;AACA,UAAM,aAAa,KAAK,YAAY,cAAc;AAClD,UAAM,qBAAqB,WAAW;AACtC,QAAI,sBAAsB,GAAG;AAEzB,WAAK,SAAS,UAAU,IAAI,cAAc;AAC1C;AAAA,IACJ,WACS,KAAK,SAAS,UAAU,SAAS,cAAc,GAAG;AACvD,WAAK,SAAS,UAAU,OAAO,cAAc;AAAA,IACjD;AACA,UAAM,cAAc,WAAW;AAC/B,UAAM,eAAe,WAAW,QAAQ;AACxC,QAAI,sBAAsB;AAC1B,QAAI,oBAAoB;AACxB,QAAI,mBAAmB;AACvB,QAAI,KAAK,UAAU;AACf,UAAI,cAAkB,cAAc,KAAK,QAAQ;AACjD,UAAI,cAAc,2BAA2B;AAEzC,aAAK,SAAS,MAAM,WAAW,GAAG,cAAc,KAAK,eAAe,EAAE;AACtE,aAAK,cAAc,QAAY,cAAc,KAAK,WAAW,OAAO;AACpE;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,4BAA4B,KAAK,gBAAgB,aAAa;AAC9D,0BAAoB;AAAA,IACxB;AACA,QAAI,4BAA4B,KAAK,eAAe,2BAA2B,aAAa;AACxF,yBAAmB;AAAA,IACvB;AACA,QAAI,4BAA4B,KAAK,eAAe,2BAA2B,cAAc,IAAI;AAC7F,4BAAsB;AAAA,IAC1B;AACA,SAAK,SAAS,UAAU,OAAO,yBAAyB,mBAAmB;AAC3E,SAAK,SAAS,UAAU,OAAO,sBAAsB,gBAAgB;AACrE,SAAK,SAAS,UAAU,OAAO,uBAAuB,iBAAiB;AACvE,QAAI,CAAC,oBAAoB,CAAC,qBAAqB;AAE3C,WAAK,SAAS,MAAM,WAAW,GAAG,cAAc,KAAK,eAAe,EAAE;AAAA,IAC1E;AACA,QAAI,KAAK,UAAU;AACf,WAAK,WAAW,SAAS,OAAO;AAChC,UAAI,iBAAiB,KAAK,WAAW,SAAS,QAAQ;AACtD,UAAI,iBAAiB,GAAG;AACpB,aAAK,cAAc,QAAQ;AAAA,MAC/B;AAAA,IACJ,WACS,KAAK,mBAAmB;AAC7B,WAAK,cAAc,QAAY,cAAc,KAAK,WAAW,OAAO;AAAA,IACxE;AAAA,EACJ;AAAA,EACA,aAAa;AACT,QAAI,cAAc;AAElB,mBAAe;AAEf,mBAAe,KAAK,WAAW,SAAS,SAAS;AACjD,QAAI,KAAK,mBAAmB;AAExB,qBAAe;AACf,qBAAe,KAAK,cAAc,SAAS,SAAS;AAAA,IACxD;AAEA,mBAAe;AACf,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB;AACf,UAAM,cAAc,KAAK,WAAW;AACpC,QAAI,KAAK,kBAAkB,QAAQ,KAAK,kBAAkB,aAAa;AACnE,aAAO;AAAA,IACX;AACA,SAAK,gBAAgB;AACrB,SAAK,SAAS,MAAM,SAAS,GAAG,WAAW;AAC3C,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,iBAAiB;AACb,SAAK,WAAW,OAAO;AAEvB,SAAK,WAAW,MAAM;AAAA,EAC1B;AAAA,EACA,oBAAoB;AAChB,SAAK,cAAc,OAAO;AAE1B,SAAK,cAAc,MAAM;AAAA,EAC7B;AAAA,EACA,uBAAuB;AACnB,SAAK,WAAW,qBAAqB;AAAA,EACzC;AAAA,EACA,qBAAqB;AACjB,QAAI,CAAC,KAAK,YAAY,SAAS,GAAG;AAC9B;AAAA,IACJ;AACA,QAAI,KAAK,qBAAqB,SAAS;AACnC,UAAI,aAAa,KAAK,YAAY,cAAc;AAChD,iBAAW,IAAI,eAAa;AACxB,YAAI,UAAU,cAAc,KAAK,UAAU,gBAAgB,UAAU,iBAAiB;AAClF,sBAAY,UAAU,eAAe,UAAU,gBAAgB,GAAG,KAAK,YAAY,SAAS,EAAE,iBAAiB,UAAU,gBAAgB,CAAC,CAAC;AAAA,QAC/I;AACA,cAAM,eAAe,KAAK,OAAO;AACjC,YAAI,UAAU,oBAAoB,UAAU,eAAe;AACvD,cAAI,CAAC,MAAM,YAAY,WAAW,YAAY,GAAG;AAC7C,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX,CAAC,EAAE,OAAO,aAAW,CAAC,CAAC,OAAO;AAC9B,UAAI,WAAW,QAAQ;AACnB,aAAK,OAAO,OAAO,EAAE,aAAa,WAAW,GAAG,IAAI;AAAA,MACxD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,sBAAsB,GAAG;AAErB,QAAI,EAAE,cAAc;AAChB,QAAE,gBAAgB;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,oBAAoB,GAAG;AACnB,QAAI,EAAE;AAAA,MAAO,aAAa;AAAA;AAAA,IAAa,GAAG;AACtC,UAAI,KAAK,mBAAmB,cAAc,GAAG,EAAE,MAAM,GAAG;AACpD,UAAE,eAAe;AACjB;AAAA,MACJ,OACK;AACD,aAAK,WAAW,SAAS,eAAe,IAAI;AAC5C,UAAE,eAAe;AACjB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,EAAE;AAAA,MAAO;AAAA;AAAA,IAAW,GAAG;AACvB,UAAI,KAAK,mBAAmB;AACxB,aAAK,cAAc,MAAM;AAAA,MAC7B,OACK;AACD,aAAK,WAAW,qBAAqB;AAAA,MACzC;AACA,QAAE,eAAe;AACjB;AAAA,IACJ;AACA,QAAI,EAAE;AAAA,MAAO,OAAqB;AAAA;AAAA,IAAkB,GAAG;AACnD,WAAK,YAAY,MAAM;AACvB,QAAE,eAAe;AACjB;AAAA,IACJ;AACA,QAAI,EAAE;AAAA,MAAO;AAAA;AAAA,IAAgB,GAAG;AAC5B,aAAO,mCAAmC,GAAG,KAAK,WAAW,SAAS,GAAG,KAAK,WAAW,QAAQ,cAAc,UAAU,CAAC;AAAA,IAC9H;AACA,QAAI,EAAE;AAAA,MAAO;AAAA;AAAA,IAAkB,GAAG;AAC9B,aAAO,qCAAqC,GAAG,KAAK,WAAW,SAAS,GAAG,KAAK,WAAW,QAAQ,cAAc,UAAU,CAAC;AAAA,IAChI;AAAA,EACJ;AAAA,EACA,uBAAuB,GAAG;AACtB,QAAI,EAAE;AAAA,MAAO,aAAa;AAAA;AAAA,IAAa,GAAG;AACtC,UAAI,KAAK,mBAAmB,cAAc,GAAG,EAAE,MAAM,GAAG;AACpD,UAAE,eAAe;AACjB;AAAA,MACJ,OACK;AACD,YAAa,aAAsB,YAAY,CAAC,KAAK,qCAAqC;AAEtF,eAAK,qBAAqB,KAAS,SAAS,+BAA+B,mJAAmJ,CAAC;AAC/N,eAAK,sCAAsC;AAC3C,eAAK,gBAAgB;AAAA,YAAM;AAAA,YAAuC;AAAA,YAAM;AAAA,YAAgB;AAAA;AAAA,UAAY;AAAA,QACxG;AACA,aAAK,cAAc,SAAS,eAAe,IAAI;AAC/C,UAAE,eAAe;AACjB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,EAAE;AAAA,MAAO;AAAA;AAAA,IAAW,GAAG;AACvB,WAAK,WAAW,qBAAqB;AACrC,QAAE,eAAe;AACjB;AAAA,IACJ;AACA,QAAI,EAAE;AAAA,MAAO,OAAmB;AAAA;AAAA,IAAW,GAAG;AAC1C,WAAK,WAAW,MAAM;AACtB,QAAE,eAAe;AACjB;AAAA,IACJ;AACA,QAAI,EAAE;AAAA,MAAO,OAAqB;AAAA;AAAA,IAAkB,GAAG;AACnD,WAAK,YAAY,MAAM;AACvB,QAAE,eAAe;AACjB;AAAA,IACJ;AACA,QAAI,EAAE;AAAA,MAAO;AAAA;AAAA,IAAgB,GAAG;AAC5B,aAAO,mCAAmC,GAAG,KAAK,cAAc,SAAS,OAAO,KAAK,cAAc,SAAS,QAAQ,cAAc,UAAU,CAAC;AAAA,IACjJ;AACA,QAAI,EAAE;AAAA,MAAO;AAAA;AAAA,IAAkB,GAAG;AAC9B,aAAO,qCAAqC,GAAG,KAAK,cAAc,SAAS,OAAO,KAAK,cAAc,SAAS,QAAQ,cAAc,UAAU,CAAC;AAAA,IACnJ;AAAA,EACJ;AAAA;AAAA,EAEA,oBAAoB,OAAO;AACvB,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,oBAAoB,UAAU;AAC1B,QAAI,KAAK,KAAK,mBAAmB,iBAAiB,QAAQ;AAC1D,QAAI,CAAC,IAAI;AACL,aAAO;AAAA,IACX;AACA,WAAO,KAAK,GAAG,SAAS,CAAC;AAAA,EAC7B;AAAA,EACA,gBAAgB;AACZ,UAAM,iBAAiB;AACvB,UAAM,gBAAgB;AAEtB,SAAK,aAAa,KAAK,UAAU,IAAI,uBAAuB,MAAM,KAAK,sBAAsB;AAAA,MACzF,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,0BAA0B,KAAK,oBAAoB,SAAS,0BAA0B;AAAA,MACtF,uBAAuB,KAAK,oBAAoB,SAAS,sBAAsB;AAAA,MAC/E,kBAAkB,KAAK,oBAAoB,SAAS,kBAAkB;AAAA,MACtE,YAAY,CAAC,UAAU;AACnB,YAAI,MAAM,WAAW,KAAK,CAAC,KAAK,WAAW,SAAS,GAAG;AACnD,iBAAO;AAAA,QACX;AACA,YAAI;AAEA,cAAI,OAAO,OAAO,IAAI;AACtB,iBAAO;AAAA,QACX,SACO,GAAG;AACN,iBAAO,EAAE,SAAS,EAAE,QAAQ;AAAA,QAChC;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB,iBAAiB,MAAM,0BAA0B,KAAK,kBAAkB;AAAA,IAC5E,GAAG,KAAK,oBAAoB,IAAI,CAAC;AACjC,SAAK,WAAW,SAAS,CAAC,CAAC,KAAK,OAAO,OAAO;AAC9C,SAAK,WAAW,iBAAiB,CAAC,CAAC,KAAK,OAAO,SAAS;AACxD,SAAK,WAAW,cAAc,CAAC,CAAC,KAAK,OAAO,SAAS;AACrD,SAAK,UAAU,KAAK,WAAW,UAAU,CAAC,MAAM,KAAK,oBAAoB,CAAC,CAAC,CAAC;AAC5E,SAAK,UAAU,KAAK,WAAW,SAAS,YAAY,MAAM;AACtD,UAAI,KAAK,oBAAoB;AACzB;AAAA,MACJ;AACA,WAAK,OAAO,OAAO,EAAE,cAAc,KAAK,WAAW,SAAS,EAAE,GAAG,IAAI;AAAA,IACzE,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,WAAW,kBAAkB,MAAM;AACnD,WAAK,OAAO,OAAO;AAAA,QACf,SAAS,KAAK,WAAW,SAAS;AAAA,QAClC,WAAW,KAAK,WAAW,cAAc;AAAA,QACzC,WAAW,KAAK,WAAW,iBAAiB;AAAA,MAChD,GAAG,IAAI;AAAA,IACX,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,WAAW,uBAAuB,CAAC,MAAM;AACzD,UAAI,EAAE;AAAA,QAAO,OAAmB;AAAA;AAAA,MAAW,GAAG;AAC1C,YAAI,KAAK,mBAAmB;AACxB,eAAK,cAAc,MAAM;AACzB,YAAE,eAAe;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,WAAW,eAAe,CAAC,MAAM;AACjD,UAAI,EAAE;AAAA,QAAO;AAAA;AAAA,MAAW,GAAG;AACvB,YAAI,KAAK,mBAAmB;AACxB,eAAK,cAAc,gBAAgB;AACnC,YAAE,eAAe;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,WAAW,SAAS,kBAAkB,CAAC,MAAM;AAC7D,UAAI,KAAK,iBAAiB,GAAG;AACzB,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ,CAAC,CAAC;AACF,QAAa,SAAS;AAClB,WAAK,UAAU,KAAK,WAAW,YAAY,CAAC,MAAM,KAAK,sBAAsB,CAAC,CAAC,CAAC;AAAA,IACpF;AACA,SAAK,gBAAgB,SAAS,cAAc,KAAK;AACjD,SAAK,cAAc,YAAY;AAC/B,SAAK,oBAAoB;AAEzB,SAAK,WAAW,KAAK,UAAU,IAAI,aAAa;AAAA,MAC5C,OAAO,+BAA+B,KAAK,oBAAoB,SAAS,uBAAuB;AAAA,MAC/F,MAAM;AAAA,MACN,WAAW,MAAM;AACb,aAAK,YAAY,UAAU,SAAS,uBAAuB,EAAE,IAAI,EAAE,KAAK,QAAW,iBAAiB;AAAA,MACxG;AAAA,IACJ,CAAC,CAAC;AAEF,SAAK,WAAW,KAAK,UAAU,IAAI,aAAa;AAAA,MAC5C,OAAO,2BAA2B,KAAK,oBAAoB,SAAS,mBAAmB;AAAA,MACvF,MAAM;AAAA,MACN,WAAW,MAAM;AACb,aAAK,YAAY,UAAU,SAAS,mBAAmB,EAAE,IAAI,EAAE,KAAK,QAAW,iBAAiB;AAAA,MACpG;AAAA,IACJ,CAAC,CAAC;AACF,QAAI,WAAW,SAAS,cAAc,KAAK;AAC3C,aAAS,YAAY;AACrB,aAAS,YAAY,KAAK,WAAW,OAAO;AAC5C,UAAM,mBAAmB,SAAS,cAAc,KAAK;AACrD,qBAAiB,YAAY;AAC7B,aAAS,YAAY,gBAAgB;AACrC,qBAAiB,YAAY,KAAK,aAAa;AAC/C,qBAAiB,YAAY,KAAK,SAAS,OAAO;AAClD,qBAAiB,YAAY,KAAK,SAAS,OAAO;AAElD,SAAK,uBAAuB,KAAK,UAAU,IAAI,SAAS;AAAA,MACpD,MAAM;AAAA,MACN,OAAO,kCAAkC,KAAK,oBAAoB,SAAS,wBAAwB;AAAA,MACnG,WAAW;AAAA,IACf,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,qBAAqB,SAAS,MAAM;AACpD,UAAI,KAAK,qBAAqB,SAAS;AACnC,YAAI,KAAK,YAAY,SAAS,GAAG;AAC7B,cAAI,aAAa,KAAK,YAAY,cAAc;AAChD,qBAAW,IAAI,eAAa;AACxB,gBAAI,UAAU,cAAc,KAAK,UAAU,gBAAgB,UAAU,iBAAiB;AAClF,0BAAY,UAAU,eAAe,UAAU,gBAAgB,GAAG,KAAK,YAAY,SAAS,EAAE,iBAAiB,UAAU,gBAAgB,CAAC,CAAC;AAAA,YAC/I;AACA,gBAAI,CAAC,UAAU,QAAQ,GAAG;AACtB,qBAAO;AAAA,YACX;AACA,mBAAO;AAAA,UACX,CAAC,EAAE,OAAO,aAAW,CAAC,CAAC,OAAO;AAC9B,cAAI,WAAW,QAAQ;AACnB,iBAAK,OAAO,OAAO,EAAE,aAAa,WAAW,GAAG,IAAI;AAAA,UACxD;AAAA,QACJ;AAAA,MACJ,OACK;AACD,aAAK,OAAO,OAAO,EAAE,aAAa,KAAK,GAAG,IAAI;AAAA,MAClD;AAAA,IACJ,CAAC,CAAC;AACF,qBAAiB,YAAY,KAAK,qBAAqB,OAAO;AAE9D,SAAK,YAAY,KAAK,UAAU,IAAI,aAAa;AAAA,MAC7C,OAAO,sBAAsB,KAAK,oBAAoB,SAAS,sBAAsB;AAAA,MACrF,MAAM;AAAA,MACN,WAAW,MAAM;AACb,aAAK,OAAO,OAAO,EAAE,YAAY,OAAO,aAAa,KAAK,GAAG,KAAK;AAAA,MACtE;AAAA,MACA,WAAW,CAAC,MAAM;AACd,YAAI,EAAE;AAAA,UAAO;AAAA;AAAA,QAAW,GAAG;AACvB,cAAI,KAAK,mBAAmB;AACxB,gBAAI,KAAK,YAAY,UAAU,GAAG;AAC9B,mBAAK,YAAY,MAAM;AAAA,YAC3B,OACK;AACD,mBAAK,YAAY,MAAM;AAAA,YAC3B;AACA,cAAE,eAAe;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AACF,qBAAiB,YAAY,KAAK,UAAU,OAAO;AAEnD,SAAK,gBAAgB,KAAK,UAAU,IAAI,0BAA0B,MAAM,QAAW;AAAA,MAC/E,OAAO;AAAA,MACP,aAAa;AAAA,MACb,yBAAyB,KAAK,oBAAoB,SAAS,yBAAyB;AAAA,MACpF,SAAS,CAAC;AAAA,MACV;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB,iBAAiB,MAAM,0BAA0B,KAAK,kBAAkB;AAAA,IAC5E,GAAG,KAAK,oBAAoB,IAAI,CAAC;AACjC,SAAK,cAAc,gBAAgB,CAAC,CAAC,KAAK,OAAO,YAAY;AAC7D,SAAK,UAAU,KAAK,cAAc,UAAU,CAAC,MAAM,KAAK,uBAAuB,CAAC,CAAC,CAAC;AAClF,SAAK,UAAU,KAAK,cAAc,SAAS,YAAY,MAAM;AACzD,WAAK,OAAO,OAAO,EAAE,eAAe,KAAK,cAAc,SAAS,MAAM,GAAG,KAAK;AAAA,IAClF,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,cAAc,SAAS,kBAAkB,CAAC,MAAM;AAChE,UAAI,KAAK,qBAAqB,KAAK,iBAAiB,GAAG;AACnD,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,cAAc,kBAAkB,MAAM;AACtD,WAAK,OAAO,OAAO;AAAA,QACf,cAAc,KAAK,cAAc,gBAAgB;AAAA,MACrD,GAAG,IAAI;AAAA,IACX,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,cAAc,sBAAsB,CAAC,MAAM;AAC3D,UAAI,EAAE;AAAA,QAAO;AAAA;AAAA,MAAW,GAAG;AACvB,YAAI,KAAK,SAAS,UAAU,GAAG;AAC3B,eAAK,SAAS,MAAM;AAAA,QACxB,WACS,KAAK,SAAS,UAAU,GAAG;AAChC,eAAK,SAAS,MAAM;AAAA,QACxB,WACS,KAAK,qBAAqB,SAAS;AACxC,eAAK,qBAAqB,MAAM;AAAA,QACpC,WACS,KAAK,UAAU,UAAU,GAAG;AACjC,eAAK,UAAU,MAAM;AAAA,QACzB;AACA,UAAE,eAAe;AAAA,MACrB;AAAA,IACJ,CAAC,CAAC;AAEF,SAAK,cAAc,KAAK,UAAU,IAAI,aAAa;AAAA,MAC/C,OAAO,wBAAwB,KAAK,oBAAoB,SAAS,gBAAgB;AAAA,MACjF,MAAM;AAAA,MACN,WAAW,MAAM;AACb,aAAK,YAAY,QAAQ;AAAA,MAC7B;AAAA,MACA,WAAW,CAAC,MAAM;AACd,YAAI,EAAE;AAAA,UAAO,OAAmB;AAAA;AAAA,QAAW,GAAG;AAC1C,eAAK,UAAU,MAAM;AACrB,YAAE,eAAe;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AAEF,SAAK,iBAAiB,KAAK,UAAU,IAAI,aAAa;AAAA,MAClD,OAAO,4BAA4B,KAAK,oBAAoB,SAAS,gBAAgB;AAAA,MACrF,MAAM;AAAA,MACN,WAAW,MAAM;AACb,aAAK,YAAY,WAAW;AAAA,MAChC;AAAA,IACJ,CAAC,CAAC;AACF,QAAI,cAAc,SAAS,cAAc,KAAK;AAC9C,gBAAY,YAAY;AACxB,gBAAY,YAAY,KAAK,cAAc,OAAO;AAClD,UAAM,0BAA0B,SAAS,cAAc,KAAK;AAC5D,4BAAwB,YAAY;AACpC,gBAAY,YAAY,uBAAuB;AAC/C,4BAAwB,YAAY,KAAK,YAAY,OAAO;AAC5D,4BAAwB,YAAY,KAAK,eAAe,OAAO;AAE/D,SAAK,oBAAoB,KAAK,UAAU,IAAI,aAAa;AAAA,MACrD,OAAO;AAAA,MACP,WAAW;AAAA,MACX,WAAW,MAAM;AACb,aAAK,OAAO,OAAO,EAAE,mBAAmB,CAAC,KAAK,kBAAkB,GAAG,KAAK;AACxE,YAAI,KAAK,mBAAmB;AACxB,eAAK,cAAc,QAAY,cAAc,KAAK,WAAW,OAAO;AACpE,eAAK,cAAc,SAAS,OAAO;AAAA,QACvC;AACA,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ,CAAC,CAAC;AACF,SAAK,kBAAkB,YAAY,KAAK,iBAAiB;AAEzD,SAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,aAAa,eAAe,MAAM;AAEhD,SAAK,SAAS,MAAM,QAAQ,GAAG,yBAAyB;AACxD,SAAK,SAAS,YAAY,KAAK,kBAAkB,OAAO;AACxD,SAAK,SAAS,YAAY,QAAQ;AAClC,SAAK,SAAS,YAAY,WAAW;AACrC,SAAK,cAAc,IAAI,KAAK,KAAK,UAAU,MAAM,EAAE,aAAa,GAAkB,MAAM,EAAE,CAAC;AAC3F,SAAK,WAAW;AAChB,QAAI,gBAAgB;AACpB,SAAK,UAAU,KAAK,YAAY,WAAW,MAAM;AAC7C,sBAAoB,cAAc,KAAK,QAAQ;AAAA,IACnD,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,YAAY,YAAY,CAAC,QAAQ;AACjD,WAAK,WAAW;AAChB,UAAI,QAAQ,gBAAgB,IAAI,SAAS,IAAI;AAC7C,UAAI,QAAQ,2BAA2B;AAEnC;AAAA,MACJ;AACA,YAAM,WAAW,WAAe,iBAAiB,KAAK,QAAQ,EAAE,QAAQ,KAAK;AAC7E,UAAI,QAAQ,UAAU;AAClB;AAAA,MACJ;AACA,WAAK,SAAS,MAAM,QAAQ,GAAG,KAAK;AACpC,UAAI,KAAK,mBAAmB;AACxB,aAAK,cAAc,QAAY,cAAc,KAAK,WAAW,OAAO;AAAA,MACxE;AACA,WAAK,WAAW,SAAS,OAAO;AAChC,WAAK,iBAAiB;AAAA,IAC1B,CAAC,CAAC;AACF,SAAK,UAAU,KAAK,YAAY,WAAW,MAAM;AAE7C,YAAM,eAAmB,cAAc,KAAK,QAAQ;AACpD,UAAI,eAAe,2BAA2B;AAE1C;AAAA,MACJ;AACA,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,YAAY,iBAAiB,2BAA2B;AAG9D,cAAM,aAAa,KAAK,YAAY,cAAc;AAClD,gBAAQ,WAAW,QAAQ,KAAK,WAAW,QAAQ,eAAe;AAClE,aAAK,WAAW;AAAA,MACpB,OACK;AAAA,MAIL;AACA,WAAK,SAAS,MAAM,QAAQ,GAAG,KAAK;AACpC,UAAI,KAAK,mBAAmB;AACxB,aAAK,cAAc,QAAY,cAAc,KAAK,WAAW,OAAO;AAAA,MACxE;AACA,WAAK,WAAW,SAAS,OAAO;AAAA,IACpC,CAAC,CAAC;AAAA,EACN;AAAA,EACA,6BAA6B;AACzB,UAAM,QAAQ,KAAK,YAAY;AAAA,MAAU;AAAA;AAAA,IAA4B;AACrE,SAAK,WAAW;AAAA,MAA2B,UAAU;AAAA;AAAA,IAAe;AAAA,EACxE;AACJ;AACA,WAAW,KAAK;AACT,IAAM,eAAN,cAA2B,OAAO;AAAA,EACrC,YAAY,MAAM;AACd,UAAM;AACN,SAAK,QAAQ;AACb,QAAI,YAAY;AAChB,QAAI,KAAK,MAAM,WAAW;AACtB,kBAAY,YAAY,MAAM,KAAK,MAAM;AAAA,IAC7C;AACA,QAAI,KAAK,MAAM,MAAM;AACjB,kBAAY,YAAY,MAAM,UAAU,YAAY,KAAK,MAAM,IAAI;AAAA,IACvE;AACA,SAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,SAAK,SAAS,QAAQ,KAAK,MAAM;AACjC,SAAK,SAAS,WAAW;AACzB,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,aAAa,QAAQ,QAAQ;AAC3C,SAAK,SAAS,aAAa,cAAc,KAAK,MAAM,KAAK;AACzD,SAAK,QAAQ,KAAK,UAAU,CAAC,MAAM;AAC/B,WAAK,MAAM,UAAU;AACrB,QAAE,eAAe;AAAA,IACrB,CAAC;AACD,SAAK,UAAU,KAAK,UAAU,CAAC,MAAM;AACjC,UAAI,EAAE;AAAA,QAAO;AAAA;AAAA,MAAc,KAAK,EAAE;AAAA,QAAO;AAAA;AAAA,MAAa,GAAG;AACrD,aAAK,MAAM,UAAU;AACrB,UAAE,eAAe;AACjB;AAAA,MACJ;AACA,UAAI,KAAK,MAAM,WAAW;AACtB,aAAK,MAAM,UAAU,CAAC;AAAA,MAC1B;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,YAAY;AACR,WAAQ,KAAK,SAAS,YAAY;AAAA,EACtC;AAAA,EACA,QAAQ;AACJ,SAAK,SAAS,MAAM;AAAA,EACxB;AAAA,EACA,WAAW,SAAS;AAChB,SAAK,SAAS,UAAU,OAAO,YAAY,CAAC,OAAO;AACnD,SAAK,SAAS,aAAa,iBAAiB,OAAO,CAAC,OAAO,CAAC;AAC5D,SAAK,SAAS,WAAW,UAAU,IAAI;AAAA,EAC3C;AAAA,EACA,YAAY,UAAU;AAClB,SAAK,SAAS,aAAa,iBAAiB,OAAO,CAAC,CAAC,QAAQ,CAAC;AAC9D,QAAI,UAAU;AACV,WAAK,SAAS,UAAU,OAAO,GAAG,UAAU,iBAAiB,iBAAiB,CAAC;AAC/E,WAAK,SAAS,UAAU,IAAI,GAAG,UAAU,iBAAiB,gBAAgB,CAAC;AAAA,IAC/E,OACK;AACD,WAAK,SAAS,UAAU,OAAO,GAAG,UAAU,iBAAiB,gBAAgB,CAAC;AAC9E,WAAK,SAAS,UAAU,IAAI,GAAG,UAAU,iBAAiB,iBAAiB,CAAC;AAAA,IAChF;AAAA,EACJ;AACJ;AAEA,2BAA2B,CAAC,OAAO,cAAc;AAC7C,QAAM,yBAAyB,CAAC,UAAU,UAAU;AAChD,QAAI,OAAO;AACP,gBAAU,QAAQ,kBAAkB,QAAQ,wBAAwB,KAAK,KAAK;AAAA,IAClF;AAAA,EACJ;AACA,yBAAuB,cAAc,MAAM,SAAS,wBAAwB,CAAC;AAC7E,yBAAuB,qBAAqB,MAAM,SAAS,eAAe,CAAC;AAC3E,yBAAuB,cAAc,MAAM,SAAS,wBAAwB,CAAC;AAC7E,QAAM,mBAAmB,MAAM,SAAS,sBAAsB;AAC9D,yBAAuB,gBAAgB,gBAAgB;AACvD,QAAM,oBAAoB,MAAM,SAAS,YAAY;AACrD,MAAI,mBAAmB;AACnB,cAAU,QAAQ,yDAAyD,iBAAiB,KAAK;AAAA,EACrG;AACA,QAAM,2BAA2B,MAAM,SAAS,8BAA8B;AAC9E,MAAI,0BAA0B;AAC1B,cAAU,QAAQ,2CAA2C,MAAM,SAAS,OAAO,WAAW,OAAO,IAAI,wBAAwB,6BAA6B;AAAA,EAClK;AACA,QAAM,kBAAkB,MAAM,SAAS,qBAAqB;AAC5D,MAAI,iBAAiB;AACjB,cAAU,QAAQ,wDAAwD,eAAe,2CAA2C;AAAA,EACxI;AACA,QAAM,2BAA2B,MAAM,SAAS,8BAA8B;AAC9E,MAAI,0BAA0B;AAC1B,cAAU,QAAQ,2CAA2C,MAAM,SAAS,OAAO,WAAW,OAAO,IAAI,wBAAwB,KAAK;AAAA,EAC1I;AACA,QAAM,WAAW,MAAM,SAAS,cAAc;AAC9C,MAAI,UAAU;AACV,cAAU,QAAQ,mDAAmD,QAAQ,KAAK;AAAA,EACtF;AACA,QAAM,aAAa,MAAM,SAAS,sBAAsB;AACxD,MAAI,YAAY;AACZ,cAAU,QAAQ,wCAAwC,UAAU,KAAK;AAAA,EAC7E;AACA,QAAM,QAAQ,MAAM,SAAS,eAAe;AAC5C,MAAI,OAAO;AACP,cAAU,QAAQ,iEAAiE,KAAK,KAAK;AAAA,EACjG;AACA,QAAM,yBAAyB,MAAM,SAAS,wBAAwB;AACtE,MAAI,wBAAwB;AACxB,cAAU,QAAQ,gEAAgE,sBAAsB,KAAK;AAAA,EACjH,OACK;AACD,UAAM,SAAS,MAAM,SAAS,kBAAkB;AAChD,QAAI,QAAQ;AACR,gBAAU,QAAQ,gEAAgE,MAAM,KAAK;AAAA,IACjG;AAAA,EACJ;AAEA,QAAM,8BAA8B,MAAM,SAAS,sBAAsB;AACzE,MAAI,6BAA6B;AAC7B,cAAU,QAAQ;AAAA;AAAA;AAAA,uBAGH,2BAA2B;AAAA;AAAA,EAEhD;AAAA,EACE;AAEA,QAAM,eAAe,MAAM,SAAS,WAAW;AAC/C,MAAI,cAAc;AACd,cAAU,QAAQ,iFAAiF,YAAY,KAAK;AAAA,EACxH;AACJ,CAAC;;;AKjqCD,IAAIC,cAA0C,SAAU,YAAY,QAAQ,KAAK,MAAM;AACnF,MAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,KAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA,MACxH,UAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,IAAK,KAAI,IAAI,WAAW,CAAC,EAAG,MAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,SAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAChE;AACA,IAAIC,WAAoC,SAAU,YAAY,WAAW;AACrE,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACxE;AACA,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAmBA,IAAM,2BAA2B;AAC1B,SAAS,yBAAyB,QAAQ,gCAAgC,UAAU,wCAAwC,OAAO;AACtI,MAAI,CAAC,OAAO,SAAS,GAAG;AACpB,WAAO;AAAA,EACX;AACA,QAAM,YAAY,OAAO,aAAa;AAEtC,MAAK,kCAAkC,YAAY,UAAU,oBAAoB,UAAU,iBACpF,kCAAkC,YAAY;AACjD,QAAI,UAAU,QAAQ,GAAG;AACrB,YAAM,iBAAiB,OAAO,4BAA4B,UAAU,iBAAiB,CAAC;AACtF,UAAI,kBAAmB,UAAU,uCAAwC;AACrE,eAAO,eAAe;AAAA,MAC1B;AAAA,IACJ,OACK;AACD,UAAI,OAAO,SAAS,EAAE,sBAAsB,SAAS,IAAI,0BAA0B;AAC/E,eAAO,OAAO,SAAS,EAAE,gBAAgB,SAAS;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAI,uBAAuB,MAAMC,8BAA6B,WAAW;AAAA,EACrE,YAAY,QAAQ,mBAAmB,gBAAgB,kBAAkB;AACrE,UAAM;AACN,SAAK,UAAU;AACf,SAAK,qBAAqB,4BAA4B,OAAO,iBAAiB;AAC9E,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,wBAAwB,IAAI,QAAQ,GAAG;AAC5C,SAAK,SAAS,KAAK,UAAU,IAAI,iBAAiB,CAAC;AACnD,SAAK,eAAe;AACpB,SAAK,UAAU,KAAK,OAAO,yBAAyB,CAAC,MAAM,KAAK,gBAAgB,CAAC,CAAC,CAAC;AACnF,SAAK,SAAS;AACd,SAAK,UAAU,KAAK,QAAQ,iBAAiB,MAAM;AAC/C,UAAI,oBAAqB,KAAK,QAAQ,SAAS,KAAK,KAAK,OAAO;AAChE,WAAK,aAAa;AAClB,WAAK,OAAO,OAAO;AAAA,QACf,aAAa;AAAA,QACb,WAAW,KAAK,gBAAgB,WAAW,oBAAoB,GAAmB,KAAK;AAAA,QACvF,WAAW,KAAK,gBAAgB,WAAW,oBAAoB,GAAmB,KAAK;AAAA,QACvF,SAAS,KAAK,gBAAgB,WAAW,kBAAkB,GAAmB,KAAK;AAAA,QACnF,cAAc,KAAK,gBAAgB,WAAW,uBAAuB,GAAmB,KAAK;AAAA,MACjG,GAAG,KAAK;AACR,UAAI,mBAAmB;AACnB,aAAK,OAAO;AAAA,UACR,oBAAoB;AAAA,UACpB,+BAA+B;AAAA,UAC/B,uCAAuC;AAAA,UACvC,qCAAqC;AAAA,UACrC,aAAa;AAAA,UACb,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,MAAM,KAAK,QAAQ;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE;AAAA,QAChD,CAAC;AAAA,MACL;AAAA,IACJ,CAAC,CAAC;AAAA,EACN;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,OAAO,IAAI,QAAQ;AACf,WAAO,OAAO,gBAAgBA,sBAAqB,EAAE;AAAA,EACzD;AAAA,EACA,UAAU;AACN,SAAK,aAAa;AAClB,UAAM,QAAQ;AAAA,EAClB;AAAA,EACA,eAAe;AACX,QAAI,KAAK,QAAQ;AACb,WAAK,OAAO,QAAQ;AACpB,WAAK,SAAS;AAAA,IAClB;AAAA,EACJ;AAAA,EACA,gBAAgB,GAAG;AACf,SAAK,eAAe,CAAC;AACrB,QAAI,EAAE,YAAY;AACd,UAAI,KAAK,OAAO,YAAY;AACxB,aAAK,mBAAmB,IAAI,IAAI;AAAA,MACpC,OACK;AACD,aAAK,mBAAmB,MAAM;AAC9B,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,EAAE,cAAc;AAChB,WAAK,oBAAoB,KAAK,OAAO,YAAY;AAAA,IACrD;AAAA,EACJ;AAAA,EACA,eAAe,GAAG;AACd,QAAI,EAAE,SAAS;AACX,WAAK,gBAAgB;AAAA,QAAM;AAAA,QAAkB,KAAK,OAAO;AAAA,QAAe;AAAA,QAAmB;AAAA;AAAA,MAAY;AAAA,IAC3G;AACA,QAAI,EAAE,WAAW;AACb,WAAK,gBAAgB;AAAA,QAAM;AAAA,QAAoB,KAAK,OAAO;AAAA,QAAiB;AAAA,QAAmB;AAAA;AAAA,MAAY;AAAA,IAC/G;AACA,QAAI,EAAE,WAAW;AACb,WAAK,gBAAgB;AAAA,QAAM;AAAA,QAAoB,KAAK,OAAO;AAAA,QAAiB;AAAA,QAAmB;AAAA;AAAA,MAAY;AAAA,IAC/G;AACA,QAAI,EAAE,cAAc;AAChB,WAAK,gBAAgB;AAAA,QAAM;AAAA,QAAuB,KAAK,OAAO;AAAA,QAAoB;AAAA,QAAmB;AAAA;AAAA,MAAY;AAAA,IACrH;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,SAAK,OAAO,OAAO;AAAA,MACf,WAAW,KAAK,gBAAgB,WAAW,oBAAoB,GAAmB,KAAK,OAAO,SAAS;AAAA,MACvG,WAAW,KAAK,gBAAgB,WAAW,oBAAoB,GAAmB,KAAK,OAAO,SAAS;AAAA,MACvG,SAAS,KAAK,gBAAgB,WAAW,kBAAkB,GAAmB,KAAK,OAAO,OAAO;AAAA,MACjG,cAAc,KAAK,gBAAgB,WAAW,uBAAuB,GAAmB,KAAK,OAAO,YAAY;AAAA,IACpH,GAAG,KAAK;AAAA,EACZ;AAAA,EACA,qBAAqB;AACjB,WAAO,CAAC,CAAC,2BAA2B,SAAS,KAAK,kBAAkB;AAAA,EACxE;AAAA,EACA,WAAW;AACP,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,kBAAkB;AACd,SAAK,OAAO,OAAO;AAAA,MACf,YAAY;AAAA,MACZ,aAAa;AAAA,IACjB,GAAG,KAAK;AACR,SAAK,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,sBAAsB;AAClB,SAAK,OAAO,OAAO,EAAE,WAAW,CAAC,KAAK,OAAO,UAAU,GAAG,KAAK;AAC/D,QAAI,CAAC,KAAK,OAAO,YAAY;AACzB,WAAK,qBAAqB;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,mBAAmB;AACf,SAAK,OAAO,OAAO,EAAE,WAAW,CAAC,KAAK,OAAO,UAAU,GAAG,KAAK;AAC/D,QAAI,CAAC,KAAK,OAAO,YAAY;AACzB,WAAK,qBAAqB;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,cAAc;AACV,SAAK,OAAO,OAAO,EAAE,SAAS,CAAC,KAAK,OAAO,QAAQ,GAAG,KAAK;AAC3D,QAAI,CAAC,KAAK,OAAO,YAAY;AACzB,WAAK,qBAAqB;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,SAAK,OAAO,OAAO,EAAE,cAAc,CAAC,KAAK,OAAO,aAAa,GAAG,KAAK;AACrE,QAAI,CAAC,KAAK,OAAO,YAAY;AACzB,WAAK,qBAAqB;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,QAAI,KAAK,OAAO,aAAa;AACzB,WAAK,OAAO,OAAO,EAAE,aAAa,KAAK,GAAG,IAAI;AAAA,IAClD,OACK;AACD,UAAI,KAAK,QAAQ,SAAS,GAAG;AACzB,YAAI,aAAa,KAAK,QAAQ,cAAc;AAC5C,mBAAW,IAAI,eAAa;AACxB,cAAI,UAAU,cAAc,KAAK,UAAU,gBAAgB,UAAU,iBAAiB;AAClF,wBAAY,UAAU,eAAe,UAAU,gBAAgB,GAAG,KAAK,QAAQ,SAAS,EAAE,iBAAiB,UAAU,gBAAgB,CAAC,CAAC;AAAA,UAC3I;AACA,cAAI,CAAC,UAAU,QAAQ,GAAG;AACtB,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX,CAAC,EAAE,OAAO,aAAW,CAAC,CAAC,OAAO;AAC9B,YAAI,WAAW,QAAQ;AACnB,eAAK,OAAO,OAAO,EAAE,aAAa,WAAW,GAAG,IAAI;AAAA,QACxD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,gBAAgB,cAAc;AAC1B,QAAI,KAAK,OAAO,SAAS;AACrB,qBAAuB,uBAAuB,YAAY;AAAA,IAC9D;AACA,SAAK,OAAO,OAAO,EAAE,aAA2B,GAAG,KAAK;AAAA,EAC5D;AAAA,EACA,qBAAqB,oBAAoB,OAAO;AAAA,EAEhD;AAAA,EACA,OAAO,MAAM,UAAU;AACnB,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,WAAK,aAAa;AAClB,UAAI,CAAC,KAAK,QAAQ,SAAS,GAAG;AAE1B;AAAA,MACJ;AACA,UAAI,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,EAAE,YAAY,KAAK,CAAC;AAClF,UAAI,KAAK,kCAAkC,UAAU;AACjD,YAAI,wBAAwB,yBAAyB,KAAK,SAAS,KAAK,+BAA+B,KAAK,qCAAqC;AACjJ,YAAI,uBAAuB;AACvB,cAAI,KAAK,OAAO,SAAS;AACrB,yBAAa,eAAuB,uBAAuB,qBAAqB;AAAA,UACpF,OACK;AACD,yBAAa,eAAe;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ,WACS,KAAK,kCAAkC,cAAc,CAAC,KAAK,mBAAmB;AACnF,YAAI,wBAAwB,yBAAyB,KAAK,SAAS,KAAK,6BAA6B;AACrG,YAAI,uBAAuB;AACvB,uBAAa,eAAe;AAAA,QAChC;AAAA,MACJ;AACA,UAAI,CAAC,aAAa,gBAAgB,KAAK,qCAAqC;AACxE,YAAI,wBAAwB,MAAM,KAAK,oBAAoB;AAC3D,YAAI,CAAC,KAAK,QAAQ,SAAS,GAAG;AAE1B;AAAA,QACJ;AACA,YAAI,uBAAuB;AACvB,uBAAa,eAAe;AAAA,QAChC;AAAA,MACJ;AAEA,UAAI,KAAK,sBAAsB,aAAa,mBAAmB;AAC3D,qBAAa,oBAAoB;AAAA,MACrC,WACS,CAAC,KAAK,mBAAmB,IAAI,GAAG;AACrC,qBAAa,oBAAoB;AAAA,MACrC;AACA,UAAI,KAAK,mBAAmB;AACxB,YAAI,oBAAoB,KAAK,QAAQ,cAAc;AACnD,YAAI,kBAAkB,KAAK,eAAa,CAAC,UAAU,QAAQ,CAAC,GAAG;AAC3D,uBAAa,cAAc;AAAA,QAC/B;AAAA,MACJ;AACA,mBAAa,OAAO,KAAK;AACzB,WAAK,OAAO,OAAO,cAAc,KAAK;AACtC,UAAI,CAAC,KAAK,QAAQ;AACd,aAAK,SAAS,IAAI,4BAA4B,KAAK,SAAS,KAAK,MAAM;AAAA,MAC3E;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,MAAM,UAAU;AAClB,WAAO,KAAK,OAAO,MAAM,QAAQ;AAAA,EACrC;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,QAAQ;AACb,WAAK,OAAO,gBAAgB;AAC5B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,QAAQ;AACb,WAAK,OAAO,gBAAgB;AAC5B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,QAAI,KAAK,QAAQ;AACb,WAAK,OAAO,QAAQ;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa;AACT,QAAI,KAAK,QAAQ;AACb,WAAK,OAAO,WAAW;AACvB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB;AACf,QAAI,KAAK,QAAQ;AACb,WAAK,OAAO,iBAAiB;AAC7B,WAAK,QAAQ,MAAM;AACnB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,sBAAsB;AAClB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,KAAK,QAAQ;AAAA,QAAU;AAAA;AAAA,MAAa,EAAE,uBACnC,KAAK,QAAQ,SAAS,KACtB,CAAC,KAAK,QAAQ,SAAS,EAAE,qBAAqB,GAAG;AACpD,eAAO,KAAK,kBAAkB,aAAa;AAAA,MAC/C;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB,MAAM;AACtB,QAAI,KAAK,QAAQ;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE,uBACnC,KAAK,QAAQ,SAAS,KACtB,CAAC,KAAK,QAAQ,SAAS,EAAE,qBAAqB,GAAG;AAEpD,WAAK,kBAAkB,cAAc,IAAI;AAAA,IAC7C;AAAA,EACJ;AACJ;AACA,qBAAqB,KAAK;AAC1B,uBAAuBF,YAAW;AAAA,EAC9BC,SAAQ,GAAG,kBAAkB;AAAA,EAC7BA,SAAQ,GAAG,eAAe;AAAA,EAC1BA,SAAQ,GAAG,iBAAiB;AAChC,GAAG,oBAAoB;AAEvB,IAAI,iBAAiB,MAAMG,wBAAuB,qBAAqB;AAAA,EACnE,YAAY,QAAQ,qBAAqB,oBAAoB,oBAAoB,eAAe,sBAAsB,iBAAiB,kBAAkB;AACrJ,UAAM,QAAQ,oBAAoB,iBAAiB,gBAAgB;AACnE,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,uBAAuB;AAC5B,SAAK,UAAU;AACf,SAAK,qBAAqB;AAAA,EAC9B;AAAA,EACA,OAAO,MAAM,UAAU;AACnB,UAAM,SAAS,OAAO,OAAO,MAAM;AAAA,MAC/B,QAAQ,EAAE,KAAK,MAAM,MAAM,OAAO;AAAA,IACtC,CAAC;AACD,WAAOC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,UAAI,CAAC,KAAK,SAAS;AACf,aAAK,kBAAkB;AAAA,MAC3B;AACA,YAAM,YAAY,KAAK,QAAQ,aAAa;AAC5C,UAAI,oBAAoB;AACxB,cAAQ,KAAK,QAAQ;AAAA,QAAU;AAAA;AAAA,MAAa,EAAE,qBAAqB;AAAA,QAC/D,KAAK;AACD,8BAAoB;AACpB;AAAA,QACJ,KAAK;AACD,8BAAoB;AACpB;AAAA,QACJ,KAAK,aAAa;AACd,gBAAM,0BAA0B,CAAC,CAAC,aAAa,UAAU,oBAAoB,UAAU;AACvF,8BAAoB;AACpB;AAAA,QACJ;AAAA,QACA;AACI;AAAA,MACR;AACA,WAAK,oBAAoB,KAAK,qBAAqB;AACnD,YAAM,OAAO,OAAO,KAAK,MAAM,MAAM,QAAQ;AAC7C,UAAI,KAAK,SAAS;AACd,YAAI,KAAK,gBAAgB,GAA2B;AAChD,eAAK,QAAQ,kBAAkB;AAAA,QACnC,WACS,KAAK,gBAAgB,GAAwB;AAClD,eAAK,QAAQ,eAAe;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,qBAAqB,oBAAoB,OAAO;AAC5C,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,kBAAkB;AAAA,IAC3B;AACA,QAAI,KAAK,OAAO,cAAc,CAAC,mBAAmB;AAC9C,WAAK,QAAQ,qBAAqB;AAAA,IACtC,OACK;AACD,WAAK,mBAAmB,qBAAqB;AAAA,IACjD;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,SAAK,UAAU,KAAK,UAAU,IAAI,WAAW,KAAK,SAAS,MAAM,KAAK,QAAQ,KAAK,qBAAqB,KAAK,oBAAoB,KAAK,oBAAoB,KAAK,eAAe,KAAK,iBAAiB,KAAK,oBAAoB,CAAC;AAC9N,SAAK,qBAAqB,KAAK,UAAU,IAAI,kBAAkB,KAAK,SAAS,KAAK,QAAQ,KAAK,oBAAoB,KAAK,aAAa,CAAC;AAAA,EAC1I;AACJ;AACA,iBAAiBC,YAAW;AAAA,EACxBC,SAAQ,GAAG,mBAAmB;AAAA,EAC9BA,SAAQ,GAAG,kBAAkB;AAAA,EAC7BA,SAAQ,GAAG,kBAAkB;AAAA,EAC7BA,SAAQ,GAAG,aAAa;AAAA,EACxBA,SAAQ,GAAG,oBAAoB;AAAA,EAC/BA,SAAQ,GAAG,eAAe;AAAA,EAC1BA,SAAQ,GAAG,iBAAiB;AAChC,GAAG,cAAc;AAEV,IAAM,kBAAkB,0BAA0B,IAAI,kBAAkB;AAAA,EAC3E,IAAI,SAAS;AAAA,EACb,OAAW,SAAS,mBAAmB,MAAM;AAAA,EAC7C,OAAO;AAAA,EACP,cAAc,eAAe,GAAG,kBAAkB,OAAO,eAAe,IAAI,cAAc,CAAC;AAAA,EAC3F,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS,OAAqB;AAAA,IAC9B,QAAQ;AAAA;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,QAAQ,OAAO;AAAA,IACf,OAAO;AAAA,IACP,OAAW,SAAS,EAAE,KAAK,UAAU,SAAS,CAAC,uBAAuB,EAAE,GAAG,QAAQ;AAAA,IACnF,OAAO;AAAA,EACX;AACJ,CAAC,CAAC;AACF,gBAAgB,kBAAkB,GAAG,CAAC,UAAU,QAAQ,SAAS;AAC7D,QAAM,aAAa,qBAAqB,IAAI,MAAM;AAClD,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,SAAO,WAAW,MAAM;AAAA,IACpB,oBAAoB;AAAA,IACpB,+BAA+B,OAAO;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE,kCAAkC,UAAU,WAAW;AAAA,IACtH,uCAAuC,OAAO;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE,kCAAkC;AAAA,IACzG,qCAAqC,OAAO;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE;AAAA,IACrE,aAAa;AAAA,IACb,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,MAAM,OAAO;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE;AAAA,EAC1C,CAAC;AACL,CAAC;AACD,IAAM,qBAAqB;AAAA,EACvB,aAAa;AAAA,EACb,MAAM,CAAC;AAAA,IACC,MAAM;AAAA,IACN,QAAQ;AAAA,MACJ,YAAY;AAAA,QACR,cAAc,EAAE,MAAM,SAAS;AAAA,QAC/B,eAAe,EAAE,MAAM,SAAS;AAAA,QAChC,OAAO,EAAE,MAAM,UAAU;AAAA,QACzB,eAAe;AAAA,UACX,MAAM;AAAA,UACN,aAAiB,SAAS,gCAAgC,wHAAwH;AAAA,QACtL;AAAA,QACA,WAAW,EAAE,MAAM,UAAU;AAAA,QAC7B,mBAAmB;AAAA,UACf,MAAM;AAAA,UACN,aAAiB,SAAS,kCAAkC,kHAAkH;AAAA,QAClL;AAAA,QACA,WAAW,EAAE,MAAM,UAAU;AAAA,QAC7B,mBAAmB;AAAA,UACf,MAAM;AAAA,UACN,aAAiB,SAAS,kCAAkC,2GAA2G;AAAA,QAC3K;AAAA,QACA,cAAc,EAAE,MAAM,UAAU;AAAA,QAChC,sBAAsB;AAAA,UAClB,MAAM;AAAA,UACN,aAAiB,SAAS,qCAAqC,+GAA+G;AAAA,QAClL;AAAA,QACA,iBAAiB,EAAE,MAAM,UAAU;AAAA,MACvC;AAAA,IACJ;AAAA,EACJ,CAAC;AACT;AACO,IAAM,0BAAN,cAAsC,aAAa;AAAA,EACtD,cAAc;AACV,UAAM;AAAA,MACF,IAAI,SAAS;AAAA,MACb,OAAW,SAAS,2BAA2B,qBAAqB;AAAA,MACpE,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA,aAAa;AAAA,IACjB,CAAC;AAAA,EACL;AAAA,EACA,IAAI,UAAU,QAAQ,MAAM;AACxB,WAAOC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAM,aAAa,qBAAqB,IAAI,MAAM;AAClD,UAAI,YAAY;AACZ,cAAM,WAAW,OAAO;AAAA,UACpB,cAAc,KAAK;AAAA,UACnB,eAAe,KAAK;AAAA,UACpB,mBAAmB,KAAK,kBAAkB;AAAA,UAC1C,SAAS,KAAK;AAAA;AAAA,UAEd,WAAW,KAAK;AAAA;AAAA,UAEhB,WAAW,KAAK;AAAA;AAAA,UAEhB,cAAc,KAAK;AAAA;AAAA,QAEvB,IAAI,CAAC;AACL,cAAM,WAAW,MAAM;AAAA,UACnB,oBAAoB;AAAA,UACpB,+BAAgC,WAAW,SAAS,EAAE,aAAa,WAAW,KAAM,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE,kCAAkC,UAAU,WAAW;AAAA,UAC3K,uCAAuC,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE,kCAAkC;AAAA,UACzG,qCAAqC;AAAA,UACrC,aAAa;AAAA,UACb,eAAe;AAAA,UACf,oBAAoB,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,oBAAoB;AAAA,UACzF,MAAM,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE;AAAA,QAC1C,GAAG,QAAQ;AACX,mBAAW,oBAAoB,WAAW,SAAS,EAAE,YAAY;AAAA,MACrE;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACO,IAAM,+BAAN,cAA2C,aAAa;AAAA,EAC3D,cAAc;AACV,UAAM;AAAA,MACF,IAAI,SAAS;AAAA,MACb,OAAW,SAAS,gCAAgC,qBAAqB;AAAA,MACzE,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,KAAK;AAAA,UACD,SAAS,OAAqB;AAAA,QAClC;AAAA,QACA,QAAQ;AAAA;AAAA,MACZ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,IAAI,UAAU,QAAQ;AAClB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAM,aAAa,qBAAqB,IAAI,MAAM;AAClD,UAAI,YAAY;AACZ,cAAM,WAAW,MAAM;AAAA,UACnB,oBAAoB;AAAA,UACpB,+BAA+B;AAAA,UAC/B,uCAAuC;AAAA,UACvC,qCAAqC;AAAA,UACrC,aAAa;AAAA,UACb,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,MAAM,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE;AAAA,QAC1C,CAAC;AACD,mBAAW,oBAAoB,WAAW,SAAS,EAAE,YAAY;AAAA,MACrE;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACO,IAAM,kBAAN,cAA8B,aAAa;AAAA,EAC9C,IAAI,UAAU,QAAQ;AAClB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAM,aAAa,qBAAqB,IAAI,MAAM;AAClD,UAAI,cAAc,CAAC,KAAK,KAAK,UAAU,GAAG;AACtC,cAAM,WAAW,MAAM;AAAA,UACnB,oBAAoB;AAAA,UACpB,+BAAgC,WAAW,SAAS,EAAE,aAAa,WAAW,KAAM,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE,kCAAkC,UAAU,WAAW;AAAA,UAC3K,uCAAuC,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE,kCAAkC;AAAA,UACzG,qCAAqC;AAAA,UACrC,aAAa;AAAA,UACb,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,MAAM,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE;AAAA,QAC1C,CAAC;AACD,aAAK,KAAK,UAAU;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACO,IAAM,sBAAN,cAAkC,gBAAgB;AAAA,EACrD,cAAc;AACV,UAAM;AAAA,MACF,IAAI,SAAS;AAAA,MACb,OAAW,SAAS,uBAAuB,WAAW;AAAA,MACtD,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ,CAAC;AAAA,QACD,QAAQ,kBAAkB;AAAA,QAC1B,SAAS;AAAA,QACT,KAAK,EAAE,SAAS,OAAqB,IAAe,WAAW;AAAA,UAAC;AAAA;AAAA,QAAW,EAAE;AAAA,QAC7E,QAAQ;AAAA;AAAA,MACZ,GAAG;AAAA,QACC,QAAQ,eAAe,IAAI,kBAAkB,OAAO,0BAA0B;AAAA,QAC9E,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,MACZ,CAAC;AAAA,IACT,CAAC;AAAA,EACL;AAAA,EACA,KAAK,YAAY;AACb,UAAM,SAAS,WAAW,gBAAgB;AAC1C,QAAI,QAAQ;AACR,iBAAW,OAAO,aAAa;AAC/B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AACO,IAAM,0BAAN,cAAsC,gBAAgB;AAAA,EACzD,cAAc;AACV,UAAM;AAAA,MACF,IAAI,SAAS;AAAA,MACb,OAAW,SAAS,2BAA2B,eAAe;AAAA,MAC9D,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,QAAC;AAAA,UACD,QAAQ,kBAAkB;AAAA,UAC1B,SAAS,OAAmB;AAAA,UAC5B,KAAK,EAAE,SAAS,OAAqB,OAAmB,IAAe,WAAW;AAAA,YAAC,OAAmB;AAAA;AAAA,UAAW,EAAE;AAAA,UACnH,QAAQ;AAAA;AAAA,QACZ;AAAA,QAAG;AAAA,UACC,QAAQ,eAAe,IAAI,kBAAkB,OAAO,0BAA0B;AAAA,UAC9E,SAAS,OAAmB;AAAA,UAC5B,QAAQ;AAAA;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,KAAK,YAAY;AACb,WAAO,WAAW,gBAAgB;AAAA,EACtC;AACJ;AACO,IAAM,2BAAN,cAAuC,aAAa;AAAA,EACvD,IAAI,UAAU,QAAQ;AAClB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAM,aAAa,qBAAqB,IAAI,MAAM;AAClD,UAAI,CAAC,YAAY;AACb;AAAA,MACJ;AACA,YAAM,wCAAwC,OAAO;AAAA,QAAU;AAAA;AAAA,MAAa,EAAE,kCAAkC;AAChH,UAAI,wBAAwB;AAC5B,UAAI,OAAO;AAAA,QAAU;AAAA;AAAA,MAAa,EAAE,kCAAkC,SAAS;AAC3E,gCAAwB,yBAAyB,QAAQ,UAAU,qCAAqC;AAAA,MAC5G;AACA,UAAI,uBAAuB;AACvB,mBAAW,gBAAgB,qBAAqB;AAAA,MACpD;AACA,UAAI,CAAC,KAAK,KAAK,UAAU,GAAG;AACxB,cAAM,WAAW,MAAM;AAAA,UACnB,oBAAoB;AAAA,UACpB,+BAA+B,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE,kCAAkC,UAAU,WAAW;AAAA,UACtH;AAAA,UACA,qCAAqC;AAAA,UACrC,aAAa;AAAA,UACb,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,MAAM,OAAO;AAAA,YAAU;AAAA;AAAA,UAAa,EAAE;AAAA,QAC1C,CAAC;AACD,aAAK,KAAK,UAAU;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACO,IAAM,+BAAN,cAA2C,yBAAyB;AAAA,EACvE,cAAc;AACV,UAAM;AAAA,MACF,IAAI,SAAS;AAAA,MACb,OAAW,SAAS,gCAAgC,qBAAqB;AAAA,MACzE,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,QACJ,QAAQ,kBAAkB;AAAA,QAC1B,SAAS,OAAqB;AAAA,QAC9B,QAAQ;AAAA;AAAA,MACZ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,KAAK,YAAY;AACb,WAAO,WAAW,gBAAgB;AAAA,EACtC;AACJ;AACO,IAAM,mCAAN,cAA+C,yBAAyB;AAAA,EAC3E,cAAc;AACV,UAAM;AAAA,MACF,IAAI,SAAS;AAAA,MACb,OAAW,SAAS,oCAAoC,yBAAyB;AAAA,MACjF,OAAO;AAAA,MACP,cAAc;AAAA,MACd,QAAQ;AAAA,QACJ,QAAQ,kBAAkB;AAAA,QAC1B,SAAS,OAAqB,OAAmB;AAAA,QACjD,QAAQ;AAAA;AAAA,MACZ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,KAAK,YAAY;AACb,WAAO,WAAW,gBAAgB;AAAA,EACtC;AACJ;AACO,IAAM,yBAAyB,0BAA0B,IAAI,kBAAkB;AAAA,EAClF,IAAI,SAAS;AAAA,EACb,OAAW,SAAS,gBAAgB,SAAS;AAAA,EAC7C,OAAO;AAAA,EACP,cAAc,eAAe,GAAG,kBAAkB,OAAO,eAAe,IAAI,cAAc,CAAC;AAAA,EAC3F,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS,OAAqB;AAAA,IAC9B,KAAK;AAAA,MAAE,SAAS,OAAqB,MAAgB;AAAA;AAAA,IAAc;AAAA,IACnE,QAAQ;AAAA;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,QAAQ,OAAO;AAAA,IACf,OAAO;AAAA,IACP,OAAW,SAAS,EAAE,KAAK,aAAa,SAAS,CAAC,uBAAuB,EAAE,GAAG,WAAW;AAAA,IACzF,OAAO;AAAA,EACX;AACJ,CAAC,CAAC;AACF,uBAAuB,kBAAkB,GAAG,CAAC,UAAU,QAAQ,SAAS;AACpE,MAAI,CAAC,OAAO,SAAS,KAAK,OAAO;AAAA,IAAU;AAAA;AAAA,EAAiB,GAAG;AAC3D,WAAO;AAAA,EACX;AACA,QAAM,aAAa,qBAAqB,IAAI,MAAM;AAClD,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,QAAM,mBAAmB,OAAO,aAAa;AAC7C,QAAM,mBAAmB,WAAW,mBAAmB;AAGvD,QAAM,gCAAgC,CAAC,iBAAiB,QAAQ,KACzD,iBAAiB,oBAAoB,iBAAiB,iBACrD,OAAO;AAAA,IAAU;AAAA;AAAA,EAAa,EAAE,kCAAkC,WACnE,CAAC;AAQR,QAAM,cAAe,oBAAoB,gCACrC,IAA4B;AAChC,SAAO,WAAW,MAAM;AAAA,IACpB,oBAAoB;AAAA,IACpB,+BAA+B,gCAAgC,WAAW;AAAA,IAC1E,uCAAuC,OAAO;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE,kCAAkC;AAAA,IACzG,qCAAqC,OAAO;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE,kCAAkC;AAAA,IACvG;AAAA,IACA,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,MAAM,OAAO;AAAA,MAAU;AAAA;AAAA,IAAa,EAAE;AAAA,EAC1C,CAAC;AACL,CAAC;AACD,2BAA2B,qBAAqB,IAAI,cAAc;AAClE,qBAAqB,uBAAuB;AAC5C,qBAAqB,4BAA4B;AACjD,qBAAqB,mBAAmB;AACxC,qBAAqB,uBAAuB;AAC5C,qBAAqB,4BAA4B;AACjD,qBAAqB,gCAAgC;AACrD,IAAM,cAAc,cAAc,mBAAmB,qBAAqB,GAAG;AAC7E,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,gBAAgB;AAAA,EAChC,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,eAAe,IAAI,kBAAkB,OAAO,eAAe,IAAI,aAAa,CAAC;AAAA,IACrF,SAAS;AAAA,IACT,WAAW;AAAA,MAAC,OAAmB;AAAA;AAAA,IAAc;AAAA,EACjD;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,oBAAoB;AAAA,EACpC,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,8BAA8B;AAAA,IACvC,KAAK,8BAA8B;AAAA,IACnC,KAAK,8BAA8B;AAAA,IACnC,OAAO,8BAA8B;AAAA,EACzC;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,iBAAiB;AAAA,EACjC,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,0BAA0B;AAAA,IACnC,KAAK,0BAA0B;AAAA,IAC/B,KAAK,0BAA0B;AAAA,IAC/B,OAAO,0BAA0B;AAAA,EACrC;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,YAAY;AAAA,EAC5B,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,sBAAsB;AAAA,IAC/B,KAAK,sBAAsB;AAAA,IAC3B,KAAK,sBAAsB;AAAA,IAC3B,OAAO,sBAAsB;AAAA,EACjC;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,kBAAkB;AAAA,EAClC,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,4BAA4B;AAAA,IACrC,KAAK,4BAA4B;AAAA,IACjC,KAAK,4BAA4B;AAAA,IACjC,OAAO,4BAA4B;AAAA,EACvC;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,mBAAmB;AAAA,EACnC,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,6BAA6B;AAAA,IACtC,KAAK,6BAA6B;AAAA,IAClC,KAAK,6BAA6B;AAAA,IAClC,OAAO,6BAA6B;AAAA,EACxC;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,QAAQ;AAAA,EACxB,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,OAAqB,OAAmB;AAAA;AAAA,EACrD;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,QAAQ;AAAA,EACxB,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,eAAe,IAAI,kBAAkB,OAAO,6BAA6B;AAAA,IACjF,SAAS;AAAA;AAAA,EACb;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,WAAW;AAAA,EAC3B,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,OAAqB,MAAgB;AAAA;AAAA,EAClD;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,WAAW;AAAA,EAC3B,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,eAAe,IAAI,kBAAkB,OAAO,6BAA6B;AAAA,IACjF,SAAS;AAAA,IACT,KAAK;AAAA,MACD,SAAS,OAAqB;AAAA,IAClC;AAAA,EACJ;AACJ,CAAC,CAAC;AACF,sBAAsB,IAAI,YAAY;AAAA,EAClC,IAAI,SAAS;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAK,EAAE,iBAAiB;AAAA,EACjC,QAAQ;AAAA,IACJ,QAAQ,MAA0B;AAAA,IAClC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS,MAAgB;AAAA;AAAA,EAC7B;AACJ,CAAC,CAAC;", "names": ["len", "_a", "NLS_DEFAULT_LABEL", "ContextScopedFindInput", "ContextScopedReplaceInput", "__decorate", "__param", "__awaiter", "CommonFindController", "FindController", "__awaiter", "__decorate", "__param", "__awaiter"]}