import{g as t}from"./index-DkGLNqVb.js";const d={getPageList(e={}){return t({url:"/tool/code/index",method:"get",params:e})},destroy(e){return t({url:"/tool/code/destroy",method:"delete",data:e})},update(e,o={}){return t({url:"/tool/code/update?id="+e,method:"put",data:o})},readTable(e){return t({url:"/tool/code/read?id="+e,method:"get"})},generateCode(e={}){return t({url:"/tool/code/generate",method:"post",responseType:"blob",timeout:20*1e3,data:e})},generateFile(e={}){return t({url:"/tool/code/generateFile",method:"post",data:e})},loadTable(e={}){return t({url:"/tool/code/loadTable",method:"post",data:e})},sync(e){return t({url:"/tool/code/sync?id="+e,method:"post"})},preview(e){return t({url:"/tool/code/preview?id="+e,method:"get"})},getTableColumns(e={}){return t({url:"/tool/code/getTableColumns",method:"get",params:e})},getDataSourceList(e={}){return t({url:"/tool/code/getDataSourceList",method:"get",params:e})},getModels(){return t({url:"/tool/code/getModels",method:"get"})}};export{d as a};
