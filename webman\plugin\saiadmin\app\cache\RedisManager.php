<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 高级Redis缓存管理器
// +----------------------------------------------------------------------
// | Author: AI Assistant (深度优化版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\cache;

use Redis;
use RedisCluster;
use Workerman\Timer;

/**
 * 高级Redis缓存管理器
 */
class RedisManager
{
    /**
     * Redis连接实例
     */
    private static $connections = [];
    
    /**
     * 配置信息
     */
    private static $config = [
        'default' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'database' => 0,
            'timeout' => 5,
            'read_timeout' => 5,
            'persistent' => true,
        ],
        'cluster' => [
            'enabled' => false,
            'nodes' => [
                '127.0.0.1:7000',
                '127.0.0.1:7001',
                '127.0.0.1:7002',
            ],
            'timeout' => 5,
            'read_timeout' => 5,
        ],
        'sentinel' => [
            'enabled' => false,
            'master' => 'mymaster',
            'nodes' => [
                '127.0.0.1:26379',
                '127.0.0.1:26380',
                '127.0.0.1:26381',
            ],
        ]
    ];
    
    /**
     * 缓存统计
     */
    private static $stats = [
        'hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0,
        'errors' => 0,
        'total_operations' => 0,
        'avg_response_time' => 0,
    ];
    
    /**
     * 初始化Redis管理器
     */
    public static function init(array $config = []): void
    {
        self::$config = array_merge(self::$config, $config);
        
        // 创建默认连接
        self::getConnection();
        
        // 启动统计定时器
        Timer::add(60, [self::class, 'logStats']);
        
        echo "✅ Redis缓存管理器初始化完成\n";
    }
    
    /**
     * 获取Redis连接
     */
    public static function getConnection(string $name = 'default'): Redis|RedisCluster
    {
        if (!isset(self::$connections[$name])) {
            self::$connections[$name] = self::createConnection($name);
        }
        
        return self::$connections[$name];
    }
    
    /**
     * 创建Redis连接
     */
    private static function createConnection(string $name): Redis|RedisCluster
    {
        try {
            if ($name === 'cluster' && self::$config['cluster']['enabled']) {
                return self::createClusterConnection();
            }
            
            $config = self::$config[$name] ?? self::$config['default'];
            $redis = new Redis();
            
            if ($config['persistent']) {
                $redis->pconnect($config['host'], $config['port'], $config['timeout']);
            } else {
                $redis->connect($config['host'], $config['port'], $config['timeout']);
            }
            
            if (!empty($config['password'])) {
                $redis->auth($config['password']);
            }
            
            $redis->select($config['database']);
            $redis->setOption(Redis::OPT_READ_TIMEOUT, $config['read_timeout']);
            $redis->setOption(Redis::OPT_SERIALIZER, Redis::SERIALIZER_JSON);
            
            return $redis;
            
        } catch (\Exception $e) {
            self::$stats['errors']++;
            throw new \RuntimeException("Redis连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 创建集群连接
     */
    private static function createClusterConnection(): RedisCluster
    {
        $config = self::$config['cluster'];
        return new RedisCluster(
            null,
            $config['nodes'],
            $config['timeout'],
            $config['read_timeout']
        );
    }
    
    /**
     * 智能缓存设置
     */
    public static function smartSet(string $key, $value, int $ttl = 3600, array $options = []): bool
    {
        $startTime = microtime(true);
        
        try {
            $redis = self::getConnection();
            
            // 数据压缩
            if (isset($options['compress']) && $options['compress']) {
                $value = gzcompress(serialize($value), 6);
            }
            
            // 设置缓存
            $result = $redis->setex($key, $ttl, $value);
            
            // 设置标签
            if (isset($options['tags'])) {
                self::setTags($key, $options['tags'], $ttl);
            }
            
            // 设置依赖
            if (isset($options['dependencies'])) {
                self::setDependencies($key, $options['dependencies']);
            }
            
            self::$stats['sets']++;
            self::recordResponseTime($startTime);
            
            return $result;
            
        } catch (\Exception $e) {
            self::$stats['errors']++;
            error_log("Redis设置失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 智能缓存获取
     */
    public static function smartGet(string $key, $default = null, array $options = [])
    {
        $startTime = microtime(true);
        
        try {
            $redis = self::getConnection();
            $value = $redis->get($key);
            
            if ($value === false) {
                self::$stats['misses']++;
                self::recordResponseTime($startTime);
                return $default;
            }
            
            // 数据解压缩
            if (isset($options['compress']) && $options['compress']) {
                $value = unserialize(gzuncompress($value));
            }
            
            self::$stats['hits']++;
            self::recordResponseTime($startTime);
            
            return $value;
            
        } catch (\Exception $e) {
            self::$stats['errors']++;
            error_log("Redis获取失败: " . $e->getMessage());
            return $default;
        }
    }
    
    /**
     * 批量操作
     */
    public static function multiSet(array $data, int $ttl = 3600): bool
    {
        try {
            $redis = self::getConnection();
            $pipe = $redis->multi(Redis::PIPELINE);
            
            foreach ($data as $key => $value) {
                $pipe->setex($key, $ttl, $value);
            }
            
            $results = $pipe->exec();
            self::$stats['sets'] += count($data);
            
            return !in_array(false, $results);
            
        } catch (\Exception $e) {
            self::$stats['errors']++;
            return false;
        }
    }
    
    /**
     * 批量获取
     */
    public static function multiGet(array $keys): array
    {
        try {
            $redis = self::getConnection();
            $values = $redis->mget($keys);
            
            $result = [];
            foreach ($keys as $index => $key) {
                $result[$key] = $values[$index] !== false ? $values[$index] : null;
                if ($values[$index] !== false) {
                    self::$stats['hits']++;
                } else {
                    self::$stats['misses']++;
                }
            }
            
            return $result;
            
        } catch (\Exception $e) {
            self::$stats['errors']++;
            return array_fill_keys($keys, null);
        }
    }
    
    /**
     * 设置缓存标签
     */
    private static function setTags(string $key, array $tags, int $ttl): void
    {
        $redis = self::getConnection();
        
        foreach ($tags as $tag) {
            $tagKey = "tag:{$tag}";
            $redis->sadd($tagKey, $key);
            $redis->expire($tagKey, $ttl + 3600); // 标签比数据多存1小时
        }
    }
    
    /**
     * 根据标签删除缓存
     */
    public static function deleteByTag(string $tag): int
    {
        try {
            $redis = self::getConnection();
            $tagKey = "tag:{$tag}";
            $keys = $redis->smembers($tagKey);
            
            if (empty($keys)) {
                return 0;
            }
            
            $deleted = $redis->del($keys);
            $redis->del($tagKey);
            
            self::$stats['deletes'] += $deleted;
            return $deleted;
            
        } catch (\Exception $e) {
            self::$stats['errors']++;
            return 0;
        }
    }
    
    /**
     * 设置缓存依赖
     */
    private static function setDependencies(string $key, array $dependencies): void
    {
        $redis = self::getConnection();
        
        foreach ($dependencies as $dep) {
            $depKey = "dep:{$dep}";
            $redis->sadd($depKey, $key);
        }
    }
    
    /**
     * 分布式锁
     */
    public static function lock(string $key, int $timeout = 10, int $expire = 30): bool
    {
        $redis = self::getConnection();
        $lockKey = "lock:{$key}";
        $identifier = uniqid();
        
        $end = time() + $timeout;
        while (time() < $end) {
            if ($redis->set($lockKey, $identifier, ['nx', 'ex' => $expire])) {
                return true;
            }
            usleep(1000); // 等待1ms
        }
        
        return false;
    }
    
    /**
     * 释放分布式锁
     */
    public static function unlock(string $key, string $identifier): bool
    {
        $redis = self::getConnection();
        $lockKey = "lock:{$key}";
        
        $script = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";
        
        return $redis->eval($script, [$lockKey, $identifier], 1) === 1;
    }
    
    /**
     * 记录响应时间
     */
    private static function recordResponseTime(float $startTime): void
    {
        $responseTime = (microtime(true) - $startTime) * 1000;
        self::$stats['total_operations']++;
        
        if (self::$stats['total_operations'] > 0) {
            self::$stats['avg_response_time'] = 
                (self::$stats['avg_response_time'] * (self::$stats['total_operations'] - 1) + $responseTime) / 
                self::$stats['total_operations'];
        }
    }
    
    /**
     * 获取缓存统计
     */
    public static function getStats(): array
    {
        $hitRate = self::$stats['hits'] + self::$stats['misses'] > 0 
            ? round(self::$stats['hits'] / (self::$stats['hits'] + self::$stats['misses']) * 100, 2)
            : 0;
            
        return array_merge(self::$stats, [
            'hit_rate' => $hitRate . '%',
            'memory_usage' => self::getMemoryUsage(),
        ]);
    }
    
    /**
     * 获取内存使用情况
     */
    private static function getMemoryUsage(): array
    {
        try {
            $redis = self::getConnection();
            $info = $redis->info('memory');
            
            return [
                'used_memory' => $info['used_memory_human'] ?? 'N/A',
                'used_memory_peak' => $info['used_memory_peak_human'] ?? 'N/A',
                'memory_fragmentation_ratio' => $info['mem_fragmentation_ratio'] ?? 'N/A',
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * 记录统计日志
     */
    public static function logStats(): void
    {
        $stats = self::getStats();
        error_log("Redis统计: " . json_encode($stats, JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * 清理过期数据
     */
    public static function cleanup(): void
    {
        try {
            $redis = self::getConnection();
            
            // 清理过期的标签
            $tagKeys = $redis->keys('tag:*');
            foreach ($tagKeys as $tagKey) {
                $keys = $redis->smembers($tagKey);
                $validKeys = [];
                
                foreach ($keys as $key) {
                    if ($redis->exists($key)) {
                        $validKeys[] = $key;
                    }
                }
                
                if (empty($validKeys)) {
                    $redis->del($tagKey);
                } else {
                    $redis->del($tagKey);
                    $redis->sadd($tagKey, ...$validKeys);
                }
            }
            
        } catch (\Exception $e) {
            error_log("Redis清理失败: " . $e->getMessage());
        }
    }
}
