{"version": 3, "sources": ["../../b-validate/es/locale/en-US.js", "../../b-validate/es/is.js", "../../b-validate/es/util.js", "../../b-validate/es/rules/base.js", "../../b-validate/es/rules/string.js", "../../b-validate/es/rules/number.js", "../../b-validate/es/rules/array.js", "../../b-validate/es/rules/object.js", "../../b-validate/es/rules/boolean.js", "../../b-validate/es/rules/type.js", "../../b-validate/es/rules/custom.js", "../../b-validate/es/index.js"], "sourcesContent": ["var defaultTypeTemplate = '#{field} is not a #{type} type';\nvar defaultValidateLocale = {\n    required: '#{field} is required',\n    type: {\n        ip: defaultTypeTemplate,\n        email: defaultTypeTemplate,\n        url: defaultTypeTemplate,\n        string: defaultTypeTemplate,\n        number: defaultTypeTemplate,\n        array: defaultTypeTemplate,\n        object: defaultTypeTemplate,\n        boolean: defaultTypeTemplate,\n    },\n    number: {\n        min: '`#{value}` is not greater than `#{min}`',\n        max: '`#{value}` is not less than `#{max}`',\n        equal: '`#{value}` is not equal to `#{equal}`',\n        range: '`#{value}` is not in range `#{min} ~ #{max}`',\n        positive: '`#{value}` is not a positive number',\n        negative: '`#{value}` is not a negative number',\n    },\n    string: {\n        maxLength: '#{field} cannot be longer than #{maxLength} characters',\n        minLength: '#{field} must be at least #{minLength} characters',\n        length: '#{field} must be exactly #{length} characters',\n        match: '`#{value}` does not match pattern #{pattern}',\n        uppercase: '`#{value}` must be all uppercase',\n        lowercase: '`#{value}` must be all lowercased',\n    },\n    array: {\n        length: '#{field} must be exactly #{length} in length',\n        minLength: '#{field} cannot be less than #{minLength} in length',\n        maxLength: '#{field} cannot be greater than #{maxLength} in length',\n        includes: '#{field} is not includes #{includes}',\n        deepEqual: '#{field} is not deep equal with #{deepEqual}',\n        empty: '#{field} is not an empty array',\n    },\n    object: {\n        deepEqual: '#{field} is not deep equal to expected value',\n        hasKeys: '#{field} does not contain required fields',\n        empty: '#{field} is not an empty object',\n    },\n    boolean: {\n        true: 'Expect true but got `#{value}`',\n        false: 'Expect false but got `#{value}`',\n    },\n};\n\nexport { defaultValidateLocale as default };\n", "var opt = Object.prototype.toString;\nfunction isArray(obj) {\n    return opt.call(obj) === '[object Array]';\n}\nfunction isObject(obj) {\n    return opt.call(obj) === '[object Object]';\n}\nfunction isString(obj) {\n    return opt.call(obj) === '[object String]';\n}\nfunction isNumber(obj) {\n    return opt.call(obj) === '[object Number]' && obj === obj; // eslint-disable-line\n}\nfunction isBoolean(obj) {\n    return opt.call(obj) === '[object Boolean]';\n}\nfunction isFunction(obj) {\n    return opt.call(obj) === '[object Function]';\n}\nfunction isEmptyObject(obj) {\n    return isObject(obj) && Object.keys(obj).length === 0;\n}\nfunction isEmptyValue(obj) {\n    return obj === undefined || obj === null || obj === '';\n}\nfunction isEmptyArray(obj) {\n    return isArray(obj) && !obj.length;\n}\nvar isEqual = function (obj, other) {\n    if (typeof obj !== 'object' || typeof other !== 'object') {\n        return obj === other;\n    }\n    if (isFunction(obj) && isFunction(other)) {\n        return obj === other || obj.toString() === other.toString();\n    }\n    if (Object.keys(obj).length !== Object.keys(other).length) {\n        return false;\n    }\n    for (var key in obj) {\n        var result = isEqual(obj[key], other[key]);\n        if (!result)\n            { return false; }\n    }\n    return true;\n};\n\nexport { isArray, isBoolean, isEmptyArray, isEmptyObject, isEmptyValue, isEqual, isFunction, isNumber, isObject, isString };\n", "import { isObject } from './is.js';\n\nvar mergeTemplate = function (defaultValidateMessages, validateMessages) {\n    var result = Object.assign({}, defaultValidateMessages);\n    Object.keys(validateMessages || {}).forEach(function (key) {\n        var defaultValue = result[key];\n        var newValue = validateMessages === null || validateMessages === void 0 ? void 0 : validateMessages[key];\n        result[key] = isObject(defaultValue)\n            ? Object.assign(Object.assign({}, defaultValue), newValue) : newValue || defaultValue;\n    });\n    return result;\n};\nvar getTemplate = function (validateMessages, keyPath) {\n    var keys = keyPath.split('.');\n    var result = validateMessages;\n    for (var i = 0; i < keys.length; i++) {\n        result = result && result[keys[i]];\n        if (result === undefined) {\n            return result;\n        }\n    }\n    return result;\n};\n\nexport { getTemplate, mergeTemplate };\n", "import { isObject, isFunction, isString, isArray, isEmptyValue, isEmptyArray } from '../is.js';\nimport { getTemplate, mergeTemplate } from '../util.js';\nimport defaultValidateLocale from '../locale/en-US.js';\n\n/**\n * @param options.trim trim string value\n * @param options.ignoreEmptyString used form type\n * @param options.message\n * @param options.type\n */\nvar Base = function Base(obj, options) {\n    var this$1$1 = this;\n\n    this.getValidateMsg = function (keyPath, info) {\n        if ( info === void 0 ) info = {};\n\n        var data = Object.assign(Object.assign({}, info), { value: this$1$1.obj, field: this$1$1.field, type: this$1$1.type });\n        var template = getTemplate(this$1$1.validateMessages, keyPath);\n        if (isFunction(template)) {\n            return template(data);\n        }\n        if (isString(template)) {\n            return template.replace(/\\#\\{.+?\\}/g, function (variable) {\n                var key = variable.slice(2, -1);\n                if (key in data) {\n                    if (isObject(data[key]) || isArray(data[key])) {\n                        try {\n                            return JSON.stringify(data[key]);\n                        }\n                        catch (_) {\n                            return data[key];\n                        }\n                    }\n                    return String(data[key]);\n                }\n                return variable;\n            });\n        }\n        return template;\n    };\n    if (isObject(options) && isString(obj) && options.trim) {\n        this.obj = obj.trim();\n    }\n    else if (isObject(options) && options.ignoreEmptyString && obj === '') {\n        this.obj = undefined;\n    }\n    else {\n        this.obj = obj;\n    }\n    this.message = options.message;\n    this.type = options.type;\n    this.error = null;\n    this.field = options.field || options.type;\n    this.validateMessages = mergeTemplate(defaultValidateLocale, options.validateMessages);\n};\n\nvar prototypeAccessors = { not: { configurable: true },isRequired: { configurable: true },end: { configurable: true } };\nprototypeAccessors.not.get = function () {\n    this._not = !this._not;\n    return this;\n};\nprototypeAccessors.isRequired.get = function () {\n    if (isEmptyValue(this.obj) || isEmptyArray(this.obj)) {\n        var message = this.getValidateMsg('required');\n        this.error = {\n            value: this.obj,\n            type: this.type,\n            requiredError: true,\n            message: this.message ||\n                (isObject(message) ? message : (\"\" + (this._not ? '[NOT MODE]:' : '') + message)),\n        };\n    }\n    return this;\n};\nprototypeAccessors.end.get = function () {\n    return this.error;\n};\nBase.prototype.addError = function addError (message) {\n    if (!this.error && message) {\n        this.error = {\n            value: this.obj,\n            type: this.type,\n            message: this.message ||\n                (isObject(message) ? message : (\"\" + (this._not ? '[NOT MODE]:' : '') + message)),\n        };\n    }\n};\nBase.prototype.validate = function validate (expression, errorMessage) {\n    var _expression = this._not ? expression : !expression;\n    if (_expression) {\n        this.addError(errorMessage);\n    }\n    return this;\n};\nBase.prototype.collect = function collect (callback) {\n    callback && callback(this.error);\n};\n\nObject.defineProperties( Base.prototype, prototypeAccessors );\n\nexport { Base as default };\n", "import Base from './base.js';\nimport { isString } from '../is.js';\n\nvar StringValidator = /*@__PURE__*/(function (Base) {\n    function StringValidator(obj, options) {\n        Base.call(this, obj, Object.assign(Object.assign({}, options), { type: 'string' }));\n        this.validate(options && options.strict ? isString(this.obj) : true, this.getValidateMsg('type.string'));\n    }\n\n    if ( Base ) StringValidator.__proto__ = Base;\n    StringValidator.prototype = Object.create( Base && Base.prototype );\n    StringValidator.prototype.constructor = StringValidator;\n\n    var prototypeAccessors = { uppercase: { configurable: true },lowercase: { configurable: true } };\n    StringValidator.prototype.maxLength = function maxLength (length) {\n        return this.obj\n            ? this.validate(this.obj.length <= length, this.getValidateMsg('string.maxLength', { maxLength: length }))\n            : this;\n    };\n    StringValidator.prototype.minLength = function minLength (length) {\n        return this.obj\n            ? this.validate(this.obj.length >= length, this.getValidateMsg('string.minLength', { minLength: length }))\n            : this;\n    };\n    StringValidator.prototype.length = function length (length$1) {\n        return this.obj\n            ? this.validate(this.obj.length === length$1, this.getValidateMsg('string.length', { length: length$1 }))\n            : this;\n    };\n    StringValidator.prototype.match = function match (pattern) {\n        var isRegex = pattern instanceof RegExp;\n        if (isRegex) {\n            pattern.lastIndex = 0;\n        }\n        return this.validate(this.obj === undefined || (isRegex && pattern.test(this.obj)), this.getValidateMsg('string.match', { pattern: pattern }));\n    };\n    prototypeAccessors.uppercase.get = function () {\n        return this.obj\n            ? this.validate(this.obj.toUpperCase() === this.obj, this.getValidateMsg('string.uppercase'))\n            : this;\n    };\n    prototypeAccessors.lowercase.get = function () {\n        return this.obj\n            ? this.validate(this.obj.toLowerCase() === this.obj, this.getValidateMsg('string.lowercase'))\n            : this;\n    };\n\n    Object.defineProperties( StringValidator.prototype, prototypeAccessors );\n\n    return StringValidator;\n}(Base));\n\nexport { StringValidator as default };\n", "import Base from './base.js';\nimport { isEmptyValue, isNumber } from '../is.js';\n\nvar NumberValidator = /*@__PURE__*/(function (Base) {\n    function NumberValidator(obj, options) {\n        Base.call(this, obj, Object.assign(Object.assign({}, options), { type: 'number' }));\n        this.validate(options && options.strict ? isNumber(this.obj) : true, this.getValidateMsg('type.number'));\n    }\n\n    if ( Base ) NumberValidator.__proto__ = Base;\n    NumberValidator.prototype = Object.create( Base && Base.prototype );\n    NumberValidator.prototype.constructor = NumberValidator;\n\n    var prototypeAccessors = { positive: { configurable: true },negative: { configurable: true } };\n    NumberValidator.prototype.min = function min (num) {\n        return !isEmptyValue(this.obj)\n            ? this.validate(this.obj >= num, this.getValidateMsg('number.min', { min: num }))\n            : this;\n    };\n    NumberValidator.prototype.max = function max (num) {\n        return !isEmptyValue(this.obj)\n            ? this.validate(this.obj <= num, this.getValidateMsg('number.max', { max: num }))\n            : this;\n    };\n    NumberValidator.prototype.equal = function equal (num) {\n        return !isEmptyValue(this.obj)\n            ? this.validate(this.obj === num, this.getValidateMsg('number.equal', { equal: num }))\n            : this;\n    };\n    NumberValidator.prototype.range = function range (min, max) {\n        return !isEmptyValue(this.obj)\n            ? this.validate(this.obj >= min && this.obj <= max, this.getValidateMsg('number.range', { min: min, max: max }))\n            : this;\n    };\n    prototypeAccessors.positive.get = function () {\n        return !isEmptyValue(this.obj)\n            ? this.validate(this.obj > 0, this.getValidateMsg('number.positive'))\n            : this;\n    };\n    prototypeAccessors.negative.get = function () {\n        return !isEmptyValue(this.obj)\n            ? this.validate(this.obj < 0, this.getValidateMsg('number.negative'))\n            : this;\n    };\n\n    Object.defineProperties( NumberValidator.prototype, prototypeAccessors );\n\n    return NumberValidator;\n}(Base));\n\nexport { NumberValidator as default };\n", "import Base from './base.js';\nimport { isEqual, isArray, isEmptyArray } from '../is.js';\n\nvar ArrayValidator = /*@__PURE__*/(function (Base) {\n    function ArrayValidator(obj, options) {\n        Base.call(this, obj, Object.assign(Object.assign({}, options), { type: 'array' }));\n        this.validate(options && options.strict ? isArray(this.obj) : true, this.getValidateMsg('type.array', { value: this.obj, type: this.type }));\n    }\n\n    if ( Base ) ArrayValidator.__proto__ = Base;\n    ArrayValidator.prototype = Object.create( Base && Base.prototype );\n    ArrayValidator.prototype.constructor = ArrayValidator;\n\n    var prototypeAccessors = { empty: { configurable: true } };\n    ArrayValidator.prototype.length = function length (num) {\n        return this.obj\n            ? this.validate(this.obj.length === num, this.getValidateMsg('array.length', { value: this.obj, length: num }))\n            : this;\n    };\n    ArrayValidator.prototype.minLength = function minLength (num) {\n        return this.obj\n            ? this.validate(this.obj.length >= num, this.getValidateMsg('array.minLength', { value: this.obj, minLength: num }))\n            : this;\n    };\n    ArrayValidator.prototype.maxLength = function maxLength (num) {\n        return this.obj\n            ? this.validate(this.obj.length <= num, this.getValidateMsg('array.maxLength', { value: this.obj, maxLength: num }))\n            : this;\n    };\n    ArrayValidator.prototype.includes = function includes (arrays) {\n        var this$1$1 = this;\n\n        return this.obj\n            ? this.validate(arrays.every(function (el) { return this$1$1.obj.indexOf(el) !== -1; }), this.getValidateMsg('array.includes', {\n                value: this.obj,\n                includes: arrays,\n            }))\n            : this;\n    };\n    ArrayValidator.prototype.deepEqual = function deepEqual (other) {\n        return this.obj\n            ? this.validate(isEqual(this.obj, other), this.getValidateMsg('array.deepEqual', { value: this.obj, deepEqual: other }))\n            : this;\n    };\n    prototypeAccessors.empty.get = function () {\n        return this.validate(isEmptyArray(this.obj), this.getValidateMsg('array.empty', { value: this.obj }));\n    };\n\n    Object.defineProperties( ArrayValidator.prototype, prototypeAccessors );\n\n    return ArrayValidator;\n}(Base));\n\nexport { ArrayValidator as default };\n", "import Base from './base.js';\nimport { isEqual, isObject, isEmptyObject } from '../is.js';\n\nvar ObjectValidator = /*@__PURE__*/(function (Base) {\n    function ObjectValidator(obj, options) {\n        Base.call(this, obj, Object.assign(Object.assign({}, options), { type: 'object' }));\n        this.validate(options && options.strict ? isObject(this.obj) : true, this.getValidateMsg('type.object'));\n    }\n\n    if ( Base ) ObjectValidator.__proto__ = Base;\n    ObjectValidator.prototype = Object.create( Base && Base.prototype );\n    ObjectValidator.prototype.constructor = ObjectValidator;\n\n    var prototypeAccessors = { empty: { configurable: true } };\n    ObjectValidator.prototype.deepEqual = function deepEqual (other) {\n        return this.obj\n            ? this.validate(isEqual(this.obj, other), this.getValidateMsg('object.deepEqual', { deepEqual: other }))\n            : this;\n    };\n    ObjectValidator.prototype.hasKeys = function hasKeys (keys) {\n        var this$1$1 = this;\n\n        return this.obj\n            ? this.validate(keys.every(function (el) { return this$1$1.obj[el]; }), this.getValidateMsg('object.hasKeys', { keys: keys }))\n            : this;\n    };\n    prototypeAccessors.empty.get = function () {\n        return this.validate(isEmptyObject(this.obj), this.getValidateMsg('object.empty'));\n    };\n\n    Object.defineProperties( ObjectValidator.prototype, prototypeAccessors );\n\n    return ObjectValidator;\n}(Base));\n\nexport { ObjectValidator as default };\n", "import Base from './base.js';\nimport { isBoolean } from '../is.js';\n\nvar BooleanValidator = /*@__PURE__*/(function (Base) {\n    function BooleanValidator(obj, options) {\n        Base.call(this, obj, Object.assign(Object.assign({}, options), { type: 'boolean' }));\n        this.validate(options && options.strict ? isBoolean(this.obj) : true, this.getValidateMsg('type.boolean'));\n    }\n\n    if ( Base ) BooleanValidator.__proto__ = Base;\n    BooleanValidator.prototype = Object.create( Base && Base.prototype );\n    BooleanValidator.prototype.constructor = BooleanValidator;\n\n    var prototypeAccessors = { true: { configurable: true },false: { configurable: true } };\n    prototypeAccessors.true.get = function () {\n        return this.validate(this.obj === true, this.getValidateMsg('boolean.true'));\n    };\n    prototypeAccessors.false.get = function () {\n        return this.validate(this.obj === false, this.getValidateMsg('boolean.false'));\n    };\n\n    Object.defineProperties( BooleanValidator.prototype, prototypeAccessors );\n\n    return BooleanValidator;\n}(Base));\n\nexport { BooleanValidator as default };\n", "import Base from './base.js';\n\nvar regexEmail = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\nvar regexUrl = new RegExp('^(?!mailto:)(?:(?:http|https|ftp)://)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$', 'i');\nvar regexIp = /^(2(5[0-5]{1}|[0-4]\\d{1})|[0-1]?\\d{1,2})(\\.(2(5[0-5]{1}|[0-4]\\d{1})|[0-1]?\\d{1,2})){3}$/;\nvar TypeValidator = /*@__PURE__*/(function (Base) {\n    function TypeValidator(obj, options) {\n        Base.call(this, obj, Object.assign(Object.assign({}, options), { type: 'type' }));\n    }\n\n    if ( Base ) TypeValidator.__proto__ = Base;\n    TypeValidator.prototype = Object.create( Base && Base.prototype );\n    TypeValidator.prototype.constructor = TypeValidator;\n\n    var prototypeAccessors = { email: { configurable: true },url: { configurable: true },ip: { configurable: true } };\n    prototypeAccessors.email.get = function () {\n        this.type = 'email';\n        return this.validate(this.obj === undefined || regexEmail.test(this.obj), this.getValidateMsg('type.email'));\n    };\n    prototypeAccessors.url.get = function () {\n        this.type = 'url';\n        return this.validate(this.obj === undefined || regexUrl.test(this.obj), this.getValidateMsg('type.url'));\n    };\n    prototypeAccessors.ip.get = function () {\n        this.type = 'ip';\n        return this.validate(this.obj === undefined || regexIp.test(this.obj), this.getValidateMsg('type.ip'));\n    };\n\n    Object.defineProperties( TypeValidator.prototype, prototypeAccessors );\n\n    return TypeValidator;\n}(Base));\n\nexport { TypeValidator as default };\n", "import Base from './base.js';\n\nvar CustomValidator = /*@__PURE__*/(function (Base) {\n    function CustomValidator(obj, options) {\n        Base.call(this, obj, Object.assign(Object.assign({}, options), { type: 'custom' }));\n    }\n\n    if ( Base ) CustomValidator.__proto__ = Base;\n    CustomValidator.prototype = Object.create( Base && Base.prototype );\n    CustomValidator.prototype.constructor = CustomValidator;\n\n    var prototypeAccessors = { validate: { configurable: true } };\n    // @ts-ignore\n    prototypeAccessors.validate.get = function () {\n        var _this = this;\n        return function (validator, callback) {\n            var ret;\n            if (validator) {\n                ret = validator(_this.obj, _this.addError.bind(_this));\n                if (ret && ret.then) {\n                    if (callback) {\n                        ret.then(function () {\n                            callback && callback(_this.error);\n                        }, function (e) {\n                            console.error(e);\n                        });\n                    }\n                    return [ret, _this];\n                }\n                else {\n                    callback && callback(_this.error);\n                    return _this.error;\n                }\n            }\n        };\n    };\n\n    Object.defineProperties( CustomValidator.prototype, prototypeAccessors );\n\n    return CustomValidator;\n}(Base));\n\nexport { CustomValidator as default };\n", "import { isObject, isArray } from './is.js';\nimport StringValidator from './rules/string.js';\nimport NumberValidator from './rules/number.js';\nimport ArrayValidator from './rules/array.js';\nimport ObjectValidator from './rules/object.js';\nimport BooleanValidator from './rules/boolean.js';\nimport TypeValidator from './rules/type.js';\nimport CustomValidator from './rules/custom.js';\nimport { mergeTemplate } from './util.js';\nexport { default as DefaultValidateMessage } from './locale/en-US.js';\n\nvar BValidate = function (obj, options) {\n    return new Validate(obj, Object.assign({ field: 'value' }, options));\n};\nBValidate.globalConfig = {};\n// 全局生效校验信息\nBValidate.setGlobalConfig = function (options) {\n    BValidate.globalConfig = options || {};\n};\nvar Validate = function Validate(obj, _options) {\n    var globalConfig = BValidate.globalConfig;\n    var options = Object.assign(Object.assign(Object.assign({}, globalConfig), _options), { validateMessages: mergeTemplate(globalConfig.validateMessages, _options.validateMessages) });\n    this.string = new StringValidator(obj, options);\n    this.number = new NumberValidator(obj, options);\n    this.array = new ArrayValidator(obj, options);\n    this.object = new ObjectValidator(obj, options);\n    this.boolean = new BooleanValidator(obj, options);\n    this.type = new TypeValidator(obj, options);\n    this.custom = new CustomValidator(obj, options);\n};\nvar Schema = function Schema(schema, options) {\n    if ( options === void 0 ) options = {};\n\n    this.schema = schema;\n    this.options = options;\n};\n// 更新校验信息\nSchema.prototype.messages = function messages (validateMessages) {\n    this.options = Object.assign(Object.assign({}, this.options), { validateMessages: mergeTemplate(this.options.validateMessages, validateMessages) });\n};\nSchema.prototype.validate = function validate (values, callback) {\n        var this$1$1 = this;\n\n    if (!isObject(values)) {\n        return;\n    }\n    var promises = [];\n    var errors = null;\n    function setError(key, error) {\n        if (!errors) {\n            errors = {};\n        }\n        if (!errors[key] || error.requiredError) {\n            errors[key] = error;\n        }\n    }\n    if (this.schema) {\n        Object.keys(this.schema).forEach(function (key) {\n            if (isArray(this$1$1.schema[key])) {\n                var loop = function ( i ) {\n                    var rule = this$1$1.schema[key][i];\n                    var type = rule.type;\n                    var message = rule.message;\n                    if (!type && !rule.validator) {\n                        throw (\"You must specify a type to field \" + key + \"!\");\n                    }\n                    var _options = Object.assign(Object.assign({}, this$1$1.options), { message: message, field: key });\n                    if ('ignoreEmptyString' in rule) {\n                        _options.ignoreEmptyString = rule.ignoreEmptyString;\n                    }\n                    if ('strict' in rule) {\n                        _options.strict = rule.strict;\n                    }\n                    var validator = new Validate(values[key], _options);\n                    var bv = validator.type[type] || null;\n                    if (!bv) {\n                        if (rule.validator) {\n                            bv = validator.custom.validate(rule.validator);\n                            if (Object.prototype.toString.call(bv) === '[object Array]' && bv[0].then) {\n                                promises.push({\n                                    function: bv[0],\n                                    _this: bv[1],\n                                    key: key,\n                                });\n                            }\n                            else if (bv) {\n                                setError(key, bv);\n                            }\n                            return;\n                        }\n                        else {\n                            bv = validator[type];\n                        }\n                    }\n                    Object.keys(rule).forEach(function (r) {\n                        if (rule.required) {\n                            bv = bv.isRequired;\n                        }\n                        if (r !== 'message' && bv[r] && rule[r] && typeof bv[r] === 'object') {\n                            bv = bv[r];\n                        }\n                        if (bv[r] && rule[r] !== undefined && typeof bv[r] === 'function') {\n                            bv = bv[r](rule[r]);\n                        }\n                    });\n                    bv.collect(function (error) {\n                        if (error) {\n                            setError(key, error);\n                        }\n                    });\n                    if (errors) {\n                        return 'break';\n                    }\n                };\n\n                    for (var i = 0; i < this$1$1.schema[key].length; i++) {\n                        var returned = loop( i );\n\n                        if ( returned === 'break' ) break;\n                    }\n            }\n        });\n    }\n    if (promises.length > 0) {\n        Promise.all(promises.map(function (a) { return a.function; })).then(function () {\n            promises.forEach(function (promise) {\n                if (promise._this.error) {\n                    setError(promise.key, promise._this.error);\n                }\n            });\n            callback && callback(errors);\n        });\n    }\n    else {\n        callback && callback(errors);\n    }\n};\n\nexport { Schema, BValidate as default };\n"], "mappings": ";AAAA,IAAI,sBAAsB;AAC1B,IAAI,wBAAwB;AAAA,EACxB,UAAU;AAAA,EACV,MAAM;AAAA,IACF,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,IACJ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACH,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACJ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,EACX;AACJ;;;AC9CA,IAAI,MAAM,OAAO,UAAU;AAC3B,SAAS,QAAQ,KAAK;AAClB,SAAO,IAAI,KAAK,GAAG,MAAM;AAC7B;AACA,SAAS,SAAS,KAAK;AACnB,SAAO,IAAI,KAAK,GAAG,MAAM;AAC7B;AACA,SAAS,SAAS,KAAK;AACnB,SAAO,IAAI,KAAK,GAAG,MAAM;AAC7B;AACA,SAAS,SAAS,KAAK;AACnB,SAAO,IAAI,KAAK,GAAG,MAAM,qBAAqB,QAAQ;AAC1D;AACA,SAAS,UAAU,KAAK;AACpB,SAAO,IAAI,KAAK,GAAG,MAAM;AAC7B;AACA,SAAS,WAAW,KAAK;AACrB,SAAO,IAAI,KAAK,GAAG,MAAM;AAC7B;AACA,SAAS,cAAc,KAAK;AACxB,SAAO,SAAS,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE,WAAW;AACxD;AACA,SAAS,aAAa,KAAK;AACvB,SAAO,QAAQ,UAAa,QAAQ,QAAQ,QAAQ;AACxD;AACA,SAAS,aAAa,KAAK;AACvB,SAAO,QAAQ,GAAG,KAAK,CAAC,IAAI;AAChC;AACA,IAAI,UAAU,SAAU,KAAK,OAAO;AAChC,MAAI,OAAO,QAAQ,YAAY,OAAO,UAAU,UAAU;AACtD,WAAO,QAAQ;AAAA,EACnB;AACA,MAAI,WAAW,GAAG,KAAK,WAAW,KAAK,GAAG;AACtC,WAAO,QAAQ,SAAS,IAAI,SAAS,MAAM,MAAM,SAAS;AAAA,EAC9D;AACA,MAAI,OAAO,KAAK,GAAG,EAAE,WAAW,OAAO,KAAK,KAAK,EAAE,QAAQ;AACvD,WAAO;AAAA,EACX;AACA,WAAS,OAAO,KAAK;AACjB,QAAI,SAAS,QAAQ,IAAI,GAAG,GAAG,MAAM,GAAG,CAAC;AACzC,QAAI,CAAC,QACD;AAAE,aAAO;AAAA,IAAO;AAAA,EACxB;AACA,SAAO;AACX;;;AC1CA,IAAI,gBAAgB,SAAU,yBAAyB,kBAAkB;AACrE,MAAI,SAAS,OAAO,OAAO,CAAC,GAAG,uBAAuB;AACtD,SAAO,KAAK,oBAAoB,CAAC,CAAC,EAAE,QAAQ,SAAU,KAAK;AACvD,QAAI,eAAe,OAAO,GAAG;AAC7B,QAAI,WAAW,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,GAAG;AACvG,WAAO,GAAG,IAAI,SAAS,YAAY,IAC7B,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,QAAQ,IAAI,YAAY;AAAA,EACjF,CAAC;AACD,SAAO;AACX;AACA,IAAI,cAAc,SAAU,kBAAkB,SAAS;AACnD,MAAI,OAAO,QAAQ,MAAM,GAAG;AAC5B,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,aAAS,UAAU,OAAO,KAAK,CAAC,CAAC;AACjC,QAAI,WAAW,QAAW;AACtB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;ACZA,IAAI,OAAO,SAASA,MAAK,KAAK,SAAS;AACnC,MAAI,WAAW;AAEf,OAAK,iBAAiB,SAAU,SAAS,MAAM;AAC3C,QAAK,SAAS,OAAS,QAAO,CAAC;AAE/B,QAAI,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,SAAS,KAAK,OAAO,SAAS,OAAO,MAAM,SAAS,KAAK,CAAC;AACrH,QAAI,WAAW,YAAY,SAAS,kBAAkB,OAAO;AAC7D,QAAI,WAAW,QAAQ,GAAG;AACtB,aAAO,SAAS,IAAI;AAAA,IACxB;AACA,QAAI,SAAS,QAAQ,GAAG;AACpB,aAAO,SAAS,QAAQ,cAAc,SAAU,UAAU;AACtD,YAAI,MAAM,SAAS,MAAM,GAAG,EAAE;AAC9B,YAAI,OAAO,MAAM;AACb,cAAI,SAAS,KAAK,GAAG,CAAC,KAAK,QAAQ,KAAK,GAAG,CAAC,GAAG;AAC3C,gBAAI;AACA,qBAAO,KAAK,UAAU,KAAK,GAAG,CAAC;AAAA,YACnC,SACO,GAAG;AACN,qBAAO,KAAK,GAAG;AAAA,YACnB;AAAA,UACJ;AACA,iBAAO,OAAO,KAAK,GAAG,CAAC;AAAA,QAC3B;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AACA,MAAI,SAAS,OAAO,KAAK,SAAS,GAAG,KAAK,QAAQ,MAAM;AACpD,SAAK,MAAM,IAAI,KAAK;AAAA,EACxB,WACS,SAAS,OAAO,KAAK,QAAQ,qBAAqB,QAAQ,IAAI;AACnE,SAAK,MAAM;AAAA,EACf,OACK;AACD,SAAK,MAAM;AAAA,EACf;AACA,OAAK,UAAU,QAAQ;AACvB,OAAK,OAAO,QAAQ;AACpB,OAAK,QAAQ;AACb,OAAK,QAAQ,QAAQ,SAAS,QAAQ;AACtC,OAAK,mBAAmB,cAAc,uBAAuB,QAAQ,gBAAgB;AACzF;AAEA,IAAI,qBAAqB,EAAE,KAAK,EAAE,cAAc,KAAK,GAAE,YAAY,EAAE,cAAc,KAAK,GAAE,KAAK,EAAE,cAAc,KAAK,EAAE;AACtH,mBAAmB,IAAI,MAAM,WAAY;AACrC,OAAK,OAAO,CAAC,KAAK;AAClB,SAAO;AACX;AACA,mBAAmB,WAAW,MAAM,WAAY;AAC5C,MAAI,aAAa,KAAK,GAAG,KAAK,aAAa,KAAK,GAAG,GAAG;AAClD,QAAI,UAAU,KAAK,eAAe,UAAU;AAC5C,SAAK,QAAQ;AAAA,MACT,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,eAAe;AAAA,MACf,SAAS,KAAK,YACT,SAAS,OAAO,IAAI,WAAiB,KAAK,OAAO,gBAAgB,MAAM;AAAA,IAChF;AAAA,EACJ;AACA,SAAO;AACX;AACA,mBAAmB,IAAI,MAAM,WAAY;AACrC,SAAO,KAAK;AAChB;AACA,KAAK,UAAU,WAAW,SAAS,SAAU,SAAS;AAClD,MAAI,CAAC,KAAK,SAAS,SAAS;AACxB,SAAK,QAAQ;AAAA,MACT,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,SAAS,KAAK,YACT,SAAS,OAAO,IAAI,WAAiB,KAAK,OAAO,gBAAgB,MAAM;AAAA,IAChF;AAAA,EACJ;AACJ;AACA,KAAK,UAAU,WAAW,SAAS,SAAU,YAAY,cAAc;AACnE,MAAI,cAAc,KAAK,OAAO,aAAa,CAAC;AAC5C,MAAI,aAAa;AACb,SAAK,SAAS,YAAY;AAAA,EAC9B;AACA,SAAO;AACX;AACA,KAAK,UAAU,UAAU,SAAS,QAAS,UAAU;AACjD,cAAY,SAAS,KAAK,KAAK;AACnC;AAEA,OAAO,iBAAkB,KAAK,WAAW,kBAAmB;;;AC/F5D,IAAI,kBAAgC,SAAUC,OAAM;AAChD,WAASC,iBAAgB,KAAK,SAAS;AACnC,IAAAD,MAAK,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,SAAS,CAAC,CAAC;AAClF,SAAK,SAAS,WAAW,QAAQ,SAAS,SAAS,KAAK,GAAG,IAAI,MAAM,KAAK,eAAe,aAAa,CAAC;AAAA,EAC3G;AAEA,MAAKA,MAAO,CAAAC,iBAAgB,YAAYD;AACxC,EAAAC,iBAAgB,YAAY,OAAO,OAAQD,SAAQA,MAAK,SAAU;AAClE,EAAAC,iBAAgB,UAAU,cAAcA;AAExC,MAAIC,sBAAqB,EAAE,WAAW,EAAE,cAAc,KAAK,GAAE,WAAW,EAAE,cAAc,KAAK,EAAE;AAC/F,EAAAD,iBAAgB,UAAU,YAAY,SAAS,UAAW,QAAQ;AAC9D,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,UAAU,QAAQ,KAAK,eAAe,oBAAoB,EAAE,WAAW,OAAO,CAAC,CAAC,IACvG;AAAA,EACV;AACA,EAAAA,iBAAgB,UAAU,YAAY,SAAS,UAAW,QAAQ;AAC9D,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,UAAU,QAAQ,KAAK,eAAe,oBAAoB,EAAE,WAAW,OAAO,CAAC,CAAC,IACvG;AAAA,EACV;AACA,EAAAA,iBAAgB,UAAU,SAAS,SAAS,OAAQ,UAAU;AAC1D,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,WAAW,UAAU,KAAK,eAAe,iBAAiB,EAAE,QAAQ,SAAS,CAAC,CAAC,IACtG;AAAA,EACV;AACA,EAAAA,iBAAgB,UAAU,QAAQ,SAAS,MAAO,SAAS;AACvD,QAAI,UAAU,mBAAmB;AACjC,QAAI,SAAS;AACT,cAAQ,YAAY;AAAA,IACxB;AACA,WAAO,KAAK,SAAS,KAAK,QAAQ,UAAc,WAAW,QAAQ,KAAK,KAAK,GAAG,GAAI,KAAK,eAAe,gBAAgB,EAAE,QAAiB,CAAC,CAAC;AAAA,EACjJ;AACA,EAAAC,oBAAmB,UAAU,MAAM,WAAY;AAC3C,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,YAAY,MAAM,KAAK,KAAK,KAAK,eAAe,kBAAkB,CAAC,IAC1F;AAAA,EACV;AACA,EAAAA,oBAAmB,UAAU,MAAM,WAAY;AAC3C,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,YAAY,MAAM,KAAK,KAAK,KAAK,eAAe,kBAAkB,CAAC,IAC1F;AAAA,EACV;AAEA,SAAO,iBAAkBD,iBAAgB,WAAWC,mBAAmB;AAEvE,SAAOD;AACX,EAAE,IAAI;;;AC/CN,IAAI,kBAAgC,SAAUE,OAAM;AAChD,WAASC,iBAAgB,KAAK,SAAS;AACnC,IAAAD,MAAK,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,SAAS,CAAC,CAAC;AAClF,SAAK,SAAS,WAAW,QAAQ,SAAS,SAAS,KAAK,GAAG,IAAI,MAAM,KAAK,eAAe,aAAa,CAAC;AAAA,EAC3G;AAEA,MAAKA,MAAO,CAAAC,iBAAgB,YAAYD;AACxC,EAAAC,iBAAgB,YAAY,OAAO,OAAQD,SAAQA,MAAK,SAAU;AAClE,EAAAC,iBAAgB,UAAU,cAAcA;AAExC,MAAIC,sBAAqB,EAAE,UAAU,EAAE,cAAc,KAAK,GAAE,UAAU,EAAE,cAAc,KAAK,EAAE;AAC7F,EAAAD,iBAAgB,UAAU,MAAM,SAAS,IAAK,KAAK;AAC/C,WAAO,CAAC,aAAa,KAAK,GAAG,IACvB,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,eAAe,cAAc,EAAE,KAAK,IAAI,CAAC,CAAC,IAC9E;AAAA,EACV;AACA,EAAAA,iBAAgB,UAAU,MAAM,SAAS,IAAK,KAAK;AAC/C,WAAO,CAAC,aAAa,KAAK,GAAG,IACvB,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,eAAe,cAAc,EAAE,KAAK,IAAI,CAAC,CAAC,IAC9E;AAAA,EACV;AACA,EAAAA,iBAAgB,UAAU,QAAQ,SAAS,MAAO,KAAK;AACnD,WAAO,CAAC,aAAa,KAAK,GAAG,IACvB,KAAK,SAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,gBAAgB,EAAE,OAAO,IAAI,CAAC,CAAC,IACnF;AAAA,EACV;AACA,EAAAA,iBAAgB,UAAU,QAAQ,SAAS,MAAO,KAAK,KAAK;AACxD,WAAO,CAAC,aAAa,KAAK,GAAG,IACvB,KAAK,SAAS,KAAK,OAAO,OAAO,KAAK,OAAO,KAAK,KAAK,eAAe,gBAAgB,EAAE,KAAU,IAAS,CAAC,CAAC,IAC7G;AAAA,EACV;AACA,EAAAC,oBAAmB,SAAS,MAAM,WAAY;AAC1C,WAAO,CAAC,aAAa,KAAK,GAAG,IACvB,KAAK,SAAS,KAAK,MAAM,GAAG,KAAK,eAAe,iBAAiB,CAAC,IAClE;AAAA,EACV;AACA,EAAAA,oBAAmB,SAAS,MAAM,WAAY;AAC1C,WAAO,CAAC,aAAa,KAAK,GAAG,IACvB,KAAK,SAAS,KAAK,MAAM,GAAG,KAAK,eAAe,iBAAiB,CAAC,IAClE;AAAA,EACV;AAEA,SAAO,iBAAkBD,iBAAgB,WAAWC,mBAAmB;AAEvE,SAAOD;AACX,EAAE,IAAI;;;AC7CN,IAAI,iBAA+B,SAAUE,OAAM;AAC/C,WAASC,gBAAe,KAAK,SAAS;AAClC,IAAAD,MAAK,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,QAAQ,CAAC,CAAC;AACjF,SAAK,SAAS,WAAW,QAAQ,SAAS,QAAQ,KAAK,GAAG,IAAI,MAAM,KAAK,eAAe,cAAc,EAAE,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,EAC/I;AAEA,MAAKA,MAAO,CAAAC,gBAAe,YAAYD;AACvC,EAAAC,gBAAe,YAAY,OAAO,OAAQD,SAAQA,MAAK,SAAU;AACjE,EAAAC,gBAAe,UAAU,cAAcA;AAEvC,MAAIC,sBAAqB,EAAE,OAAO,EAAE,cAAc,KAAK,EAAE;AACzD,EAAAD,gBAAe,UAAU,SAAS,SAAS,OAAQ,KAAK;AACpD,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,WAAW,KAAK,KAAK,eAAe,gBAAgB,EAAE,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,IAC5G;AAAA,EACV;AACA,EAAAA,gBAAe,UAAU,YAAY,SAAS,UAAW,KAAK;AAC1D,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,UAAU,KAAK,KAAK,eAAe,mBAAmB,EAAE,OAAO,KAAK,KAAK,WAAW,IAAI,CAAC,CAAC,IACjH;AAAA,EACV;AACA,EAAAA,gBAAe,UAAU,YAAY,SAAS,UAAW,KAAK;AAC1D,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,IAAI,UAAU,KAAK,KAAK,eAAe,mBAAmB,EAAE,OAAO,KAAK,KAAK,WAAW,IAAI,CAAC,CAAC,IACjH;AAAA,EACV;AACA,EAAAA,gBAAe,UAAU,WAAW,SAAS,SAAU,QAAQ;AAC3D,QAAI,WAAW;AAEf,WAAO,KAAK,MACN,KAAK,SAAS,OAAO,MAAM,SAAU,IAAI;AAAE,aAAO,SAAS,IAAI,QAAQ,EAAE,MAAM;AAAA,IAAI,CAAC,GAAG,KAAK,eAAe,kBAAkB;AAAA,MAC3H,OAAO,KAAK;AAAA,MACZ,UAAU;AAAA,IACd,CAAC,CAAC,IACA;AAAA,EACV;AACA,EAAAA,gBAAe,UAAU,YAAY,SAAS,UAAW,OAAO;AAC5D,WAAO,KAAK,MACN,KAAK,SAAS,QAAQ,KAAK,KAAK,KAAK,GAAG,KAAK,eAAe,mBAAmB,EAAE,OAAO,KAAK,KAAK,WAAW,MAAM,CAAC,CAAC,IACrH;AAAA,EACV;AACA,EAAAC,oBAAmB,MAAM,MAAM,WAAY;AACvC,WAAO,KAAK,SAAS,aAAa,KAAK,GAAG,GAAG,KAAK,eAAe,eAAe,EAAE,OAAO,KAAK,IAAI,CAAC,CAAC;AAAA,EACxG;AAEA,SAAO,iBAAkBD,gBAAe,WAAWC,mBAAmB;AAEtE,SAAOD;AACX,EAAE,IAAI;;;AChDN,IAAI,kBAAgC,SAAUE,OAAM;AAChD,WAASC,iBAAgB,KAAK,SAAS;AACnC,IAAAD,MAAK,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,SAAS,CAAC,CAAC;AAClF,SAAK,SAAS,WAAW,QAAQ,SAAS,SAAS,KAAK,GAAG,IAAI,MAAM,KAAK,eAAe,aAAa,CAAC;AAAA,EAC3G;AAEA,MAAKA,MAAO,CAAAC,iBAAgB,YAAYD;AACxC,EAAAC,iBAAgB,YAAY,OAAO,OAAQD,SAAQA,MAAK,SAAU;AAClE,EAAAC,iBAAgB,UAAU,cAAcA;AAExC,MAAIC,sBAAqB,EAAE,OAAO,EAAE,cAAc,KAAK,EAAE;AACzD,EAAAD,iBAAgB,UAAU,YAAY,SAAS,UAAW,OAAO;AAC7D,WAAO,KAAK,MACN,KAAK,SAAS,QAAQ,KAAK,KAAK,KAAK,GAAG,KAAK,eAAe,oBAAoB,EAAE,WAAW,MAAM,CAAC,CAAC,IACrG;AAAA,EACV;AACA,EAAAA,iBAAgB,UAAU,UAAU,SAAS,QAAS,MAAM;AACxD,QAAI,WAAW;AAEf,WAAO,KAAK,MACN,KAAK,SAAS,KAAK,MAAM,SAAU,IAAI;AAAE,aAAO,SAAS,IAAI,EAAE;AAAA,IAAG,CAAC,GAAG,KAAK,eAAe,kBAAkB,EAAE,KAAW,CAAC,CAAC,IAC3H;AAAA,EACV;AACA,EAAAC,oBAAmB,MAAM,MAAM,WAAY;AACvC,WAAO,KAAK,SAAS,cAAc,KAAK,GAAG,GAAG,KAAK,eAAe,cAAc,CAAC;AAAA,EACrF;AAEA,SAAO,iBAAkBD,iBAAgB,WAAWC,mBAAmB;AAEvE,SAAOD;AACX,EAAE,IAAI;;;AC9BN,IAAI,mBAAiC,SAAUE,OAAM;AACjD,WAASC,kBAAiB,KAAK,SAAS;AACpC,IAAAD,MAAK,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,UAAU,CAAC,CAAC;AACnF,SAAK,SAAS,WAAW,QAAQ,SAAS,UAAU,KAAK,GAAG,IAAI,MAAM,KAAK,eAAe,cAAc,CAAC;AAAA,EAC7G;AAEA,MAAKA,MAAO,CAAAC,kBAAiB,YAAYD;AACzC,EAAAC,kBAAiB,YAAY,OAAO,OAAQD,SAAQA,MAAK,SAAU;AACnE,EAAAC,kBAAiB,UAAU,cAAcA;AAEzC,MAAIC,sBAAqB,EAAE,MAAM,EAAE,cAAc,KAAK,GAAE,OAAO,EAAE,cAAc,KAAK,EAAE;AACtF,EAAAA,oBAAmB,KAAK,MAAM,WAAY;AACtC,WAAO,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,eAAe,cAAc,CAAC;AAAA,EAC/E;AACA,EAAAA,oBAAmB,MAAM,MAAM,WAAY;AACvC,WAAO,KAAK,SAAS,KAAK,QAAQ,OAAO,KAAK,eAAe,eAAe,CAAC;AAAA,EACjF;AAEA,SAAO,iBAAkBD,kBAAiB,WAAWC,mBAAmB;AAExE,SAAOD;AACX,EAAE,IAAI;;;ACtBN,IAAI,aAAa;AACjB,IAAI,WAAW,IAAI,OAAO,+YAA+Y,GAAG;AAC5a,IAAI,UAAU;AACd,IAAI,gBAA8B,SAAUE,OAAM;AAC9C,WAASC,eAAc,KAAK,SAAS;AACjC,IAAAD,MAAK,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,OAAO,CAAC,CAAC;AAAA,EACpF;AAEA,MAAKA,MAAO,CAAAC,eAAc,YAAYD;AACtC,EAAAC,eAAc,YAAY,OAAO,OAAQD,SAAQA,MAAK,SAAU;AAChE,EAAAC,eAAc,UAAU,cAAcA;AAEtC,MAAIC,sBAAqB,EAAE,OAAO,EAAE,cAAc,KAAK,GAAE,KAAK,EAAE,cAAc,KAAK,GAAE,IAAI,EAAE,cAAc,KAAK,EAAE;AAChH,EAAAA,oBAAmB,MAAM,MAAM,WAAY;AACvC,SAAK,OAAO;AACZ,WAAO,KAAK,SAAS,KAAK,QAAQ,UAAa,WAAW,KAAK,KAAK,GAAG,GAAG,KAAK,eAAe,YAAY,CAAC;AAAA,EAC/G;AACA,EAAAA,oBAAmB,IAAI,MAAM,WAAY;AACrC,SAAK,OAAO;AACZ,WAAO,KAAK,SAAS,KAAK,QAAQ,UAAa,SAAS,KAAK,KAAK,GAAG,GAAG,KAAK,eAAe,UAAU,CAAC;AAAA,EAC3G;AACA,EAAAA,oBAAmB,GAAG,MAAM,WAAY;AACpC,SAAK,OAAO;AACZ,WAAO,KAAK,SAAS,KAAK,QAAQ,UAAa,QAAQ,KAAK,KAAK,GAAG,GAAG,KAAK,eAAe,SAAS,CAAC;AAAA,EACzG;AAEA,SAAO,iBAAkBD,eAAc,WAAWC,mBAAmB;AAErE,SAAOD;AACX,EAAE,IAAI;;;AC7BN,IAAI,kBAAgC,SAAUE,OAAM;AAChD,WAASC,iBAAgB,KAAK,SAAS;AACnC,IAAAD,MAAK,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,SAAS,CAAC,CAAC;AAAA,EACtF;AAEA,MAAKA,MAAO,CAAAC,iBAAgB,YAAYD;AACxC,EAAAC,iBAAgB,YAAY,OAAO,OAAQD,SAAQA,MAAK,SAAU;AAClE,EAAAC,iBAAgB,UAAU,cAAcA;AAExC,MAAIC,sBAAqB,EAAE,UAAU,EAAE,cAAc,KAAK,EAAE;AAE5D,EAAAA,oBAAmB,SAAS,MAAM,WAAY;AAC1C,QAAI,QAAQ;AACZ,WAAO,SAAU,WAAW,UAAU;AAClC,UAAI;AACJ,UAAI,WAAW;AACX,cAAM,UAAU,MAAM,KAAK,MAAM,SAAS,KAAK,KAAK,CAAC;AACrD,YAAI,OAAO,IAAI,MAAM;AACjB,cAAI,UAAU;AACV,gBAAI,KAAK,WAAY;AACjB,0BAAY,SAAS,MAAM,KAAK;AAAA,YACpC,GAAG,SAAU,GAAG;AACZ,sBAAQ,MAAM,CAAC;AAAA,YACnB,CAAC;AAAA,UACL;AACA,iBAAO,CAAC,KAAK,KAAK;AAAA,QACtB,OACK;AACD,sBAAY,SAAS,MAAM,KAAK;AAChC,iBAAO,MAAM;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO,iBAAkBD,iBAAgB,WAAWC,mBAAmB;AAEvE,SAAOD;AACX,EAAE,IAAI;;;AC7BN,IAAI,YAAY,SAAU,KAAK,SAAS;AACpC,SAAO,IAAI,SAAS,KAAK,OAAO,OAAO,EAAE,OAAO,QAAQ,GAAG,OAAO,CAAC;AACvE;AACA,UAAU,eAAe,CAAC;AAE1B,UAAU,kBAAkB,SAAU,SAAS;AAC3C,YAAU,eAAe,WAAW,CAAC;AACzC;AACA,IAAI,WAAW,SAASE,UAAS,KAAK,UAAU;AAC5C,MAAI,eAAe,UAAU;AAC7B,MAAI,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,QAAQ,GAAG,EAAE,kBAAkB,cAAc,aAAa,kBAAkB,SAAS,gBAAgB,EAAE,CAAC;AACnL,OAAK,SAAS,IAAI,gBAAgB,KAAK,OAAO;AAC9C,OAAK,SAAS,IAAI,gBAAgB,KAAK,OAAO;AAC9C,OAAK,QAAQ,IAAI,eAAe,KAAK,OAAO;AAC5C,OAAK,SAAS,IAAI,gBAAgB,KAAK,OAAO;AAC9C,OAAK,UAAU,IAAI,iBAAiB,KAAK,OAAO;AAChD,OAAK,OAAO,IAAI,cAAc,KAAK,OAAO;AAC1C,OAAK,SAAS,IAAI,gBAAgB,KAAK,OAAO;AAClD;AACA,IAAI,SAAS,SAASC,QAAO,QAAQ,SAAS;AAC1C,MAAK,YAAY,OAAS,WAAU,CAAC;AAErC,OAAK,SAAS;AACd,OAAK,UAAU;AACnB;AAEA,OAAO,UAAU,WAAW,SAAS,SAAU,kBAAkB;AAC7D,OAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,EAAE,kBAAkB,cAAc,KAAK,QAAQ,kBAAkB,gBAAgB,EAAE,CAAC;AACtJ;AACA,OAAO,UAAU,WAAW,SAASC,UAAU,QAAQ,UAAU;AACzD,MAAI,WAAW;AAEnB,MAAI,CAAC,SAAS,MAAM,GAAG;AACnB;AAAA,EACJ;AACA,MAAI,WAAW,CAAC;AAChB,MAAI,SAAS;AACb,WAAS,SAAS,KAAK,OAAO;AAC1B,QAAI,CAAC,QAAQ;AACT,eAAS,CAAC;AAAA,IACd;AACA,QAAI,CAAC,OAAO,GAAG,KAAK,MAAM,eAAe;AACrC,aAAO,GAAG,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,MAAI,KAAK,QAAQ;AACb,WAAO,KAAK,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AAC5C,UAAI,QAAQ,SAAS,OAAO,GAAG,CAAC,GAAG;AAC/B,YAAI,OAAO,SAAWC,IAAI;AACtB,cAAI,OAAO,SAAS,OAAO,GAAG,EAAEA,EAAC;AACjC,cAAI,OAAO,KAAK;AAChB,cAAI,UAAU,KAAK;AACnB,cAAI,CAAC,QAAQ,CAAC,KAAK,WAAW;AAC1B,kBAAO,sCAAsC,MAAM;AAAA,UACvD;AACA,cAAI,WAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,OAAO,GAAG,EAAE,SAAkB,OAAO,IAAI,CAAC;AAClG,cAAI,uBAAuB,MAAM;AAC7B,qBAAS,oBAAoB,KAAK;AAAA,UACtC;AACA,cAAI,YAAY,MAAM;AAClB,qBAAS,SAAS,KAAK;AAAA,UAC3B;AACA,cAAI,YAAY,IAAI,SAAS,OAAO,GAAG,GAAG,QAAQ;AAClD,cAAI,KAAK,UAAU,KAAK,IAAI,KAAK;AACjC,cAAI,CAAC,IAAI;AACL,gBAAI,KAAK,WAAW;AAChB,mBAAK,UAAU,OAAO,SAAS,KAAK,SAAS;AAC7C,kBAAI,OAAO,UAAU,SAAS,KAAK,EAAE,MAAM,oBAAoB,GAAG,CAAC,EAAE,MAAM;AACvE,yBAAS,KAAK;AAAA,kBACV,UAAU,GAAG,CAAC;AAAA,kBACd,OAAO,GAAG,CAAC;AAAA,kBACX;AAAA,gBACJ,CAAC;AAAA,cACL,WACS,IAAI;AACT,yBAAS,KAAK,EAAE;AAAA,cACpB;AACA;AAAA,YACJ,OACK;AACD,mBAAK,UAAU,IAAI;AAAA,YACvB;AAAA,UACJ;AACA,iBAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,GAAG;AACnC,gBAAI,KAAK,UAAU;AACf,mBAAK,GAAG;AAAA,YACZ;AACA,gBAAI,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,OAAO,GAAG,CAAC,MAAM,UAAU;AAClE,mBAAK,GAAG,CAAC;AAAA,YACb;AACA,gBAAI,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM,UAAa,OAAO,GAAG,CAAC,MAAM,YAAY;AAC/D,mBAAK,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAAA,YACtB;AAAA,UACJ,CAAC;AACD,aAAG,QAAQ,SAAU,OAAO;AACxB,gBAAI,OAAO;AACP,uBAAS,KAAK,KAAK;AAAA,YACvB;AAAA,UACJ,CAAC;AACD,cAAI,QAAQ;AACR,mBAAO;AAAA,UACX;AAAA,QACJ;AAEI,iBAAS,IAAI,GAAG,IAAI,SAAS,OAAO,GAAG,EAAE,QAAQ,KAAK;AAClD,cAAI,WAAW,KAAM,CAAE;AAEvB,cAAK,aAAa,QAAU;AAAA,QAChC;AAAA,MACR;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,SAAS,SAAS,GAAG;AACrB,YAAQ,IAAI,SAAS,IAAI,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAU,CAAC,CAAC,EAAE,KAAK,WAAY;AAC5E,eAAS,QAAQ,SAAU,SAAS;AAChC,YAAI,QAAQ,MAAM,OAAO;AACrB,mBAAS,QAAQ,KAAK,QAAQ,MAAM,KAAK;AAAA,QAC7C;AAAA,MACJ,CAAC;AACD,kBAAY,SAAS,MAAM;AAAA,IAC/B,CAAC;AAAA,EACL,OACK;AACD,gBAAY,SAAS,MAAM;AAAA,EAC/B;AACJ;", "names": ["Base", "Base", "StringValidator", "prototypeAccessors", "Base", "NumberValidator", "prototypeAccessors", "Base", "ArrayValidator", "prototypeAccessors", "Base", "ObjectValidator", "prototypeAccessors", "Base", "BooleanValidator", "prototypeAccessors", "Base", "TypeValidator", "prototypeAccessors", "Base", "CustomValidator", "prototypeAccessors", "Validate", "<PERSON><PERSON><PERSON>", "validate", "i"]}