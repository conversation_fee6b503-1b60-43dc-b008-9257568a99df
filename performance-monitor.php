<?php
/**
 * SaiAdmin 实时性能监控脚本
 * 持续监控系统性能并生成实时报告
 */

require_once __DIR__ . '/webman/vendor/autoload.php';

class RealTimePerformanceMonitor
{
    private $config = [
        'check_interval' => 10,     // 检查间隔（秒）
        'alert_thresholds' => [
            'cpu_usage' => 80,      // CPU使用率阈值
            'memory_usage' => 80,   // 内存使用率阈值
            'disk_usage' => 90,     // 磁盘使用率阈值
            'response_time' => 3000, // 响应时间阈值（毫秒）
            'error_rate' => 5,      // 错误率阈值（%）
        ],
        'endpoints' => [
            'frontend' => 'http://localhost:8889/',
            'backend' => 'http://localhost:8787/',
            'api_health' => 'http://localhost:8787/core/captcha',
        ],
        'log_file' => 'performance_monitor.log',
        'alert_file' => 'performance_alerts.log',
    ];

    private $metrics = [];
    private $alerts = [];
    private $startTime;

    public function __construct()
    {
        $this->startTime = time();
        echo "🚀 SaiAdmin 实时性能监控启动\n";
        echo "监控间隔: {$this->config['check_interval']}秒\n";
        echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
        echo "========================================\n\n";
    }

    /**
     * 开始监控
     */
    public function start()
    {
        while (true) {
            $this->collectMetrics();
            $this->analyzeMetrics();
            $this->displayStatus();
            $this->logMetrics();

            sleep($this->config['check_interval']);
        }
    }

    /**
     * 收集性能指标
     */
    private function collectMetrics()
    {
        $timestamp = time();

        $this->metrics[$timestamp] = [
            'system' => $this->getSystemMetrics(),
            'database' => $this->getDatabaseMetrics(),
            'web_services' => $this->getWebServiceMetrics(),
            'application' => $this->getApplicationMetrics(),
        ];

        // 保持最近1小时的数据
        $this->cleanOldMetrics();
    }

    /**
     * 获取系统指标
     */
    private function getSystemMetrics()
    {
        $metrics = [
            'timestamp' => time(),
            'cpu_usage' => $this->getCpuUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'load_average' => $this->getLoadAverage(),
        ];

        return $metrics;
    }

    /**
     * 获取CPU使用率
     */
    private function getCpuUsage()
    {
        if (PHP_OS_FAMILY === 'Windows') {
            // Windows系统
            $output = @shell_exec('wmic cpu get loadpercentage /value');
            if (preg_match('/LoadPercentage=(\d+)/', $output, $matches)) {
                return (float)$matches[1];
            }
        } else {
            // Linux系统
            $load = sys_getloadavg();
            return $load[0] * 100 / 4; // 假设4核CPU
        }

        return 0;
    }

    /**
     * 获取内存使用率
     */
    private function getMemoryUsage()
    {
        if (PHP_OS_FAMILY === 'Windows') {
            $output = @shell_exec('wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value');
            preg_match('/FreePhysicalMemory=(\d+)/', $output, $free);
            preg_match('/TotalVisibleMemorySize=(\d+)/', $output, $total);

            if (isset($free[1]) && isset($total[1])) {
                $freeMemory = (float)$free[1];
                $totalMemory = (float)$total[1];
                return (($totalMemory - $freeMemory) / $totalMemory) * 100;
            }
        } else {
            $meminfo = file_get_contents('/proc/meminfo');
            preg_match('/MemTotal:\s+(\d+)/', $meminfo, $total);
            preg_match('/MemAvailable:\s+(\d+)/', $meminfo, $available);

            if (isset($total[1]) && isset($available[1])) {
                return (($total[1] - $available[1]) / $total[1]) * 100;
            }
        }

        return 0;
    }

    /**
     * 获取磁盘使用率
     */
    private function getDiskUsage()
    {
        $totalBytes = disk_total_space('.');
        $freeBytes = disk_free_space('.');

        if ($totalBytes && $freeBytes) {
            return (($totalBytes - $freeBytes) / $totalBytes) * 100;
        }

        return 0;
    }

    /**
     * 获取负载平均值
     */
    private function getLoadAverage()
    {
        if (function_exists('sys_getloadavg')) {
            return sys_getloadavg();
        }

        return [0, 0, 0];
    }

    /**
     * 获取数据库指标
     */
    private function getDatabaseMetrics()
    {
        try {
            $startTime = microtime(true);

            $pdo = new PDO(
                'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
                'root',
                '5GeNi1v7P7Xcur5W',
                [PDO::ATTR_TIMEOUT => 5]
            );

            $connectionTime = (microtime(true) - $startTime) * 1000;

            // 获取数据库状态
            $stmt = $pdo->query('SHOW STATUS LIKE "Threads_connected"');
            $connections = $stmt->fetch(PDO::FETCH_ASSOC)['Value'] ?? 0;

            $stmt = $pdo->query('SHOW STATUS LIKE "Queries"');
            $queries = $stmt->fetch(PDO::FETCH_ASSOC)['Value'] ?? 0;

            $stmt = $pdo->query('SHOW STATUS LIKE "Slow_queries"');
            $slowQueries = $stmt->fetch(PDO::FETCH_ASSOC)['Value'] ?? 0;

            return [
                'connection_time' => $connectionTime,
                'connections' => (int)$connections,
                'total_queries' => (int)$queries,
                'slow_queries' => (int)$slowQueries,
                'status' => 'online'
            ];

        } catch (Exception $e) {
            return [
                'connection_time' => 0,
                'connections' => 0,
                'total_queries' => 0,
                'slow_queries' => 0,
                'status' => 'offline',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取Web服务指标
     */
    private function getWebServiceMetrics()
    {
        $metrics = [];

        foreach ($this->config['endpoints'] as $name => $url) {
            $metrics[$name] = $this->checkEndpoint($url);
        }

        return $metrics;
    }

    /**
     * 检查端点状态
     */
    private function checkEndpoint($url)
    {
        $startTime = microtime(true);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'SaiAdmin Performance Monitor'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        $responseTime = (microtime(true) - $startTime) * 1000;

        return [
            'response_time' => $responseTime,
            'http_code' => $httpCode,
            'status' => $httpCode == 200 ? 'online' : 'offline',
            'error' => $error,
            'response_size' => strlen($response)
        ];
    }

    /**
     * 获取应用程序指标
     */
    private function getApplicationMetrics()
    {
        return [
            'php_memory_usage' => memory_get_usage(true),
            'php_peak_memory' => memory_get_peak_usage(true),
            'uptime' => time() - $this->startTime,
            'php_version' => PHP_VERSION,
        ];
    }

    /**
     * 分析指标并生成告警
     */
    private function analyzeMetrics()
    {
        $latest = end($this->metrics);
        $alerts = [];

        // 检查CPU使用率
        if ($latest['system']['cpu_usage'] > $this->config['alert_thresholds']['cpu_usage']) {
            $alerts[] = [
                'type' => 'cpu_high',
                'message' => "CPU使用率过高: {$latest['system']['cpu_usage']}%",
                'severity' => 'warning'
            ];
        }

        // 检查内存使用率
        if ($latest['system']['memory_usage'] > $this->config['alert_thresholds']['memory_usage']) {
            $alerts[] = [
                'type' => 'memory_high',
                'message' => "内存使用率过高: " . round($latest['system']['memory_usage'], 2) . "%",
                'severity' => 'warning'
            ];
        }

        // 检查磁盘使用率
        if ($latest['system']['disk_usage'] > $this->config['alert_thresholds']['disk_usage']) {
            $alerts[] = [
                'type' => 'disk_high',
                'message' => "磁盘使用率过高: " . round($latest['system']['disk_usage'], 2) . "%",
                'severity' => 'critical'
            ];
        }

        // 检查数据库连接
        if ($latest['database']['status'] === 'offline') {
            $alerts[] = [
                'type' => 'database_offline',
                'message' => "数据库连接失败: " . ($latest['database']['error'] ?? '未知错误'),
                'severity' => 'critical'
            ];
        }

        // 检查Web服务响应时间
        foreach ($latest['web_services'] as $service => $metrics) {
            if ($metrics['response_time'] > $this->config['alert_thresholds']['response_time']) {
                $alerts[] = [
                    'type' => 'slow_response',
                    'message' => "{$service} 响应时间过慢: " . round($metrics['response_time'], 2) . "ms",
                    'severity' => 'warning'
                ];
            }

            if ($metrics['status'] === 'offline') {
                $alerts[] = [
                    'type' => 'service_offline',
                    'message' => "{$service} 服务不可用",
                    'severity' => 'critical'
                ];
            }
        }

        // 记录告警
        if (!empty($alerts)) {
            $this->alerts[time()] = $alerts;
            $this->logAlerts($alerts);
        }
    }

    /**
     * 显示实时状态
     */
    private function displayStatus()
    {
        $latest = end($this->metrics);

        // 清屏（Windows）
        if (PHP_OS_FAMILY === 'Windows') {
            if (function_exists('system')) {
                system('cls');
            } else {
                echo "\033[2J\033[H"; // ANSI清屏
            }
        } else {
            if (function_exists('system')) {
                system('clear');
            } else {
                echo "\033[2J\033[H"; // ANSI清屏
            }
        }

        echo "🚀 SaiAdmin 实时性能监控 - " . date('Y-m-d H:i:s') . "\n";
        echo "运行时间: " . $this->formatUptime($latest['application']['uptime']) . "\n";
        echo "========================================\n\n";

        // 系统状态
        echo "💻 系统状态:\n";
        echo sprintf("  CPU使用率: %.1f%%\n", $latest['system']['cpu_usage']);
        echo sprintf("  内存使用率: %.1f%%\n", $latest['system']['memory_usage']);
        echo sprintf("  磁盘使用率: %.1f%%\n", $latest['system']['disk_usage']);
        echo sprintf("  负载平均: %.2f, %.2f, %.2f\n",
            $latest['system']['load_average'][0],
            $latest['system']['load_average'][1],
            $latest['system']['load_average'][2]
        );
        echo "\n";

        // 数据库状态
        echo "🗄️ 数据库状态:\n";
        echo sprintf("  状态: %s\n", $latest['database']['status'] === 'online' ? '✅ 在线' : '❌ 离线');
        echo sprintf("  连接时间: %.2fms\n", $latest['database']['connection_time']);
        echo sprintf("  活跃连接: %d\n", $latest['database']['connections']);
        echo sprintf("  慢查询: %d\n", $latest['database']['slow_queries']);
        echo "\n";

        // Web服务状态
        echo "🌐 Web服务状态:\n";
        foreach ($latest['web_services'] as $service => $metrics) {
            $status = $metrics['status'] === 'online' ? '✅' : '❌';
            echo sprintf("  %s: %s (%.2fms)\n", $service, $status, $metrics['response_time']);
        }
        echo "\n";

        // 应用程序状态
        echo "📱 应用程序状态:\n";
        echo sprintf("  PHP内存使用: %s\n", $this->formatBytes($latest['application']['php_memory_usage']));
        echo sprintf("  PHP峰值内存: %s\n", $this->formatBytes($latest['application']['php_peak_memory']));
        echo sprintf("  PHP版本: %s\n", $latest['application']['php_version']);
        echo "\n";

        // 最近告警
        $recentAlerts = array_slice($this->alerts, -3, 3, true);
        if (!empty($recentAlerts)) {
            echo "⚠️ 最近告警:\n";
            foreach ($recentAlerts as $timestamp => $alerts) {
                foreach ($alerts as $alert) {
                    $icon = $alert['severity'] === 'critical' ? '🔴' : '🟡';
                    echo sprintf("  %s [%s] %s\n", $icon, date('H:i:s', $timestamp), $alert['message']);
                }
            }
        } else {
            echo "✅ 无告警\n";
        }

        echo "\n按 Ctrl+C 停止监控\n";
    }

    /**
     * 记录指标到文件
     */
    private function logMetrics()
    {
        $latest = end($this->metrics);
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'metrics' => $latest
        ];

        file_put_contents(
            $this->config['log_file'],
            json_encode($logEntry) . "\n",
            FILE_APPEND | LOCK_EX
        );
    }

    /**
     * 记录告警到文件
     */
    private function logAlerts($alerts)
    {
        foreach ($alerts as $alert) {
            $logEntry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'alert' => $alert
            ];

            file_put_contents(
                $this->config['alert_file'],
                json_encode($logEntry) . "\n",
                FILE_APPEND | LOCK_EX
            );
        }
    }

    /**
     * 清理旧指标
     */
    private function cleanOldMetrics()
    {
        $cutoff = time() - 3600; // 保留1小时
        foreach ($this->metrics as $timestamp => $metrics) {
            if ($timestamp < $cutoff) {
                unset($this->metrics[$timestamp]);
            }
        }

        foreach ($this->alerts as $timestamp => $alerts) {
            if ($timestamp < $cutoff) {
                unset($this->alerts[$timestamp]);
            }
        }
    }

    /**
     * 格式化运行时间
     */
    private function formatUptime($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * 格式化字节数
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
}

// 启动监控
$monitor = new RealTimePerformanceMonitor();
$monitor->start();
