{"name": "saiadmin/yii2-saiadmin", "description": "SaiAdmin based on Yii 2.0 Framework", "keywords": ["yii2", "framework", "admin", "<PERSON><PERSON><PERSON><PERSON>"], "homepage": "https://saiadmin.com", "type": "project", "license": "MIT", "support": {"issues": "https://github.com/saiadmin/yii2-saiadmin/issues", "source": "https://github.com/saiadmin/yii2-saiadmin"}, "minimum-stability": "stable", "require": {"php": ">=7.4.0", "yiisoft/yii2": "~2.0.45", "yiisoft/yii2-bootstrap4": "~2.0.0", "yiisoft/yii2-swiftmailer": "~2.1.0"}, "require-dev": {"yiisoft/yii2-debug": "^2.1", "yiisoft/yii2-gii": "^2.2", "yiisoft/yii2-faker": "~2.0.0"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}], "autoload": {"psr-4": {"app\\": ""}}, "autoload-dev": {"psr-4": {"app\\tests\\": "tests/"}}, "scripts": {"post-install-cmd": ["yii\\composer\\Installer::postInstall"], "post-create-project-cmd": ["yii\\composer\\Installer::postCreateProject", "yii\\composer\\Installer::postInstall"]}, "extra": {"yii\\composer\\Installer::postCreateProject": {"setPermission": [["runtime", "web/assets"], "0777"]}, "yii\\composer\\Installer::postInstall": {"generateCookieValidationKey": ["config/web.php"]}}}