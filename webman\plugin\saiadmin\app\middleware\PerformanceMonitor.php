<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - API性能监控中间件
// +----------------------------------------------------------------------
// | Author: AI Assistant (深度优化版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use plugin\saiadmin\app\cache\RedisManager;

/**
 * API性能监控中间件
 */
class PerformanceMonitor implements MiddlewareInterface
{
    /**
     * 性能阈值配置
     */
    private $thresholds = [
        'response_time' => [
            'warning' => 1000,  // 1秒警告
            'critical' => 3000, // 3秒严重
        ],
        'memory_usage' => [
            'warning' => 50 * 1024 * 1024,  // 50MB警告
            'critical' => 100 * 1024 * 1024, // 100MB严重
        ],
        'query_count' => [
            'warning' => 10,    // 10个查询警告
            'critical' => 20,   // 20个查询严重
        ]
    ];
    
    /**
     * 监控数据
     */
    private static $metrics = [];
    
    public function process(Request $request, callable $handler): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $startQueries = $this->getQueryCount();
        
        // 生成请求ID
        $requestId = uniqid('req_');
        $request->requestId = $requestId;
        
        // 记录请求开始
        $this->logRequestStart($request, $requestId);
        
        try {
            // 执行请求
            $response = $handler($request);
            
            // 计算性能指标
            $metrics = $this->calculateMetrics(
                $startTime, 
                $startMemory, 
                $startQueries, 
                $request
            );
            
            // 添加性能头
            $this->addPerformanceHeaders($response, $metrics);
            
            // 记录性能数据
            $this->recordMetrics($request, $metrics, 'success');
            
            // 检查性能阈值
            $this->checkThresholds($request, $metrics);
            
            return $response;
            
        } catch (\Exception $e) {
            // 记录错误性能数据
            $metrics = $this->calculateMetrics(
                $startTime, 
                $startMemory, 
                $startQueries, 
                $request
            );
            
            $this->recordMetrics($request, $metrics, 'error', $e->getMessage());
            
            throw $e;
        }
    }
    
    /**
     * 计算性能指标
     */
    private function calculateMetrics(float $startTime, int $startMemory, int $startQueries, Request $request): array
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endQueries = $this->getQueryCount();
        
        return [
            'request_id' => $request->requestId,
            'uri' => $request->uri(),
            'method' => $request->method(),
            'response_time' => round(($endTime - $startTime) * 1000, 2), // ms
            'memory_usage' => $endMemory - $startMemory,
            'peak_memory' => memory_get_peak_usage(true),
            'query_count' => $endQueries - $startQueries,
            'timestamp' => time(),
            'user_agent' => $request->header('user-agent', ''),
            'ip' => $this->getClientIp($request),
        ];
    }
    
    /**
     * 添加性能响应头
     */
    private function addPerformanceHeaders(Response $response, array $metrics): void
    {
        $response->withHeaders([
            'X-Response-Time' => $metrics['response_time'] . 'ms',
            'X-Memory-Usage' => $this->formatBytes($metrics['memory_usage']),
            'X-Query-Count' => $metrics['query_count'],
            'X-Request-ID' => $metrics['request_id'],
        ]);
    }
    
    /**
     * 记录性能指标
     */
    private function recordMetrics(Request $request, array $metrics, string $status, string $error = ''): void
    {
        $data = array_merge($metrics, [
            'status' => $status,
            'error' => $error,
        ]);
        
        // 存储到Redis
        try {
            $redis = RedisManager::getConnection();
            
            // 实时性能数据
            $key = "performance:realtime:" . date('Y-m-d-H-i');
            $redis->lpush($key, json_encode($data));
            $redis->expire($key, 3600); // 1小时过期
            
            // 性能统计数据
            $this->updatePerformanceStats($data);
            
            // 慢请求记录
            if ($metrics['response_time'] > $this->thresholds['response_time']['warning']) {
                $slowKey = "performance:slow:" . date('Y-m-d');
                $redis->lpush($slowKey, json_encode($data));
                $redis->expire($slowKey, 86400 * 7); // 7天过期
            }
            
        } catch (\Exception $e) {
            error_log("性能数据记录失败: " . $e->getMessage());
        }
    }
    
    /**
     * 更新性能统计
     */
    private function updatePerformanceStats(array $data): void
    {
        try {
            $redis = RedisManager::getConnection();
            $statsKey = "performance:stats:" . date('Y-m-d');
            
            // 更新计数器
            $redis->hincrby($statsKey, 'total_requests', 1);
            
            if ($data['status'] === 'success') {
                $redis->hincrby($statsKey, 'success_requests', 1);
            } else {
                $redis->hincrby($statsKey, 'error_requests', 1);
            }
            
            // 更新响应时间统计
            $this->updateResponseTimeStats($redis, $statsKey, $data['response_time']);
            
            // 更新内存使用统计
            $this->updateMemoryStats($redis, $statsKey, $data['memory_usage']);
            
            $redis->expire($statsKey, 86400 * 30); // 30天过期
            
        } catch (\Exception $e) {
            error_log("性能统计更新失败: " . $e->getMessage());
        }
    }
    
    /**
     * 更新响应时间统计
     */
    private function updateResponseTimeStats($redis, string $statsKey, float $responseTime): void
    {
        // 分级统计
        if ($responseTime < 100) {
            $redis->hincrby($statsKey, 'response_time_0_100ms', 1);
        } elseif ($responseTime < 500) {
            $redis->hincrby($statsKey, 'response_time_100_500ms', 1);
        } elseif ($responseTime < 1000) {
            $redis->hincrby($statsKey, 'response_time_500_1000ms', 1);
        } elseif ($responseTime < 3000) {
            $redis->hincrby($statsKey, 'response_time_1000_3000ms', 1);
        } else {
            $redis->hincrby($statsKey, 'response_time_3000ms_plus', 1);
        }
        
        // 平均响应时间计算
        $currentAvg = $redis->hget($statsKey, 'avg_response_time') ?: 0;
        $totalRequests = $redis->hget($statsKey, 'total_requests') ?: 1;
        
        $newAvg = (($currentAvg * ($totalRequests - 1)) + $responseTime) / $totalRequests;
        $redis->hset($statsKey, 'avg_response_time', round($newAvg, 2));
    }
    
    /**
     * 更新内存统计
     */
    private function updateMemoryStats($redis, string $statsKey, int $memoryUsage): void
    {
        $currentMax = $redis->hget($statsKey, 'max_memory_usage') ?: 0;
        if ($memoryUsage > $currentMax) {
            $redis->hset($statsKey, 'max_memory_usage', $memoryUsage);
        }
        
        $currentAvg = $redis->hget($statsKey, 'avg_memory_usage') ?: 0;
        $totalRequests = $redis->hget($statsKey, 'total_requests') ?: 1;
        
        $newAvg = (($currentAvg * ($totalRequests - 1)) + $memoryUsage) / $totalRequests;
        $redis->hset($statsKey, 'avg_memory_usage', round($newAvg));
    }
    
    /**
     * 检查性能阈值
     */
    private function checkThresholds(Request $request, array $metrics): void
    {
        $alerts = [];
        
        // 检查响应时间
        if ($metrics['response_time'] > $this->thresholds['response_time']['critical']) {
            $alerts[] = [
                'type' => 'critical',
                'metric' => 'response_time',
                'value' => $metrics['response_time'],
                'threshold' => $this->thresholds['response_time']['critical'],
            ];
        } elseif ($metrics['response_time'] > $this->thresholds['response_time']['warning']) {
            $alerts[] = [
                'type' => 'warning',
                'metric' => 'response_time',
                'value' => $metrics['response_time'],
                'threshold' => $this->thresholds['response_time']['warning'],
            ];
        }
        
        // 检查内存使用
        if ($metrics['memory_usage'] > $this->thresholds['memory_usage']['critical']) {
            $alerts[] = [
                'type' => 'critical',
                'metric' => 'memory_usage',
                'value' => $metrics['memory_usage'],
                'threshold' => $this->thresholds['memory_usage']['critical'],
            ];
        } elseif ($metrics['memory_usage'] > $this->thresholds['memory_usage']['warning']) {
            $alerts[] = [
                'type' => 'warning',
                'metric' => 'memory_usage',
                'value' => $metrics['memory_usage'],
                'threshold' => $this->thresholds['memory_usage']['warning'],
            ];
        }
        
        // 检查查询数量
        if ($metrics['query_count'] > $this->thresholds['query_count']['critical']) {
            $alerts[] = [
                'type' => 'critical',
                'metric' => 'query_count',
                'value' => $metrics['query_count'],
                'threshold' => $this->thresholds['query_count']['critical'],
            ];
        } elseif ($metrics['query_count'] > $this->thresholds['query_count']['warning']) {
            $alerts[] = [
                'type' => 'warning',
                'metric' => 'query_count',
                'value' => $metrics['query_count'],
                'threshold' => $this->thresholds['query_count']['warning'],
            ];
        }
        
        // 发送告警
        if (!empty($alerts)) {
            $this->sendAlerts($request, $metrics, $alerts);
        }
    }
    
    /**
     * 发送性能告警
     */
    private function sendAlerts(Request $request, array $metrics, array $alerts): void
    {
        try {
            $redis = RedisManager::getConnection();
            $alertData = [
                'request' => $metrics,
                'alerts' => $alerts,
                'timestamp' => time(),
            ];
            
            $alertKey = "performance:alerts:" . date('Y-m-d');
            $redis->lpush($alertKey, json_encode($alertData));
            $redis->expire($alertKey, 86400 * 7); // 7天过期
            
            // 记录到日志
            error_log("性能告警: " . json_encode($alertData, JSON_UNESCAPED_UNICODE));
            
        } catch (\Exception $e) {
            error_log("告警发送失败: " . $e->getMessage());
        }
    }
    
    /**
     * 记录请求开始
     */
    private function logRequestStart(Request $request, string $requestId): void
    {
        error_log("请求开始: {$requestId} {$request->method()} {$request->uri()}");
    }
    
    /**
     * 获取查询数量
     */
    private function getQueryCount(): int
    {
        // 这里需要根据实际的ORM实现来获取查询数量
        // 暂时返回0，实际使用时需要实现
        return 0;
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp(Request $request): string
    {
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];
        
        foreach ($headers as $header) {
            $ip = $request->header(strtolower(str_replace('HTTP_', '', $header)));
            if (!empty($ip) && $ip !== 'unknown') {
                return explode(',', $ip)[0];
            }
        }
        
        return $request->getRemoteIp();
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    /**
     * 获取性能统计
     */
    public static function getPerformanceStats(string $date = null): array
    {
        $date = $date ?: date('Y-m-d');
        
        try {
            $redis = RedisManager::getConnection();
            $statsKey = "performance:stats:{$date}";
            
            return $redis->hgetall($statsKey) ?: [];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
