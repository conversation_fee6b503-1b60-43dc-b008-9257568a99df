@import 'dark.less';
@import 'animation.less';

html, body {
  height: 100%;
}
.arco-layout-sider-children {
  overflow-x: hidden;
}

.arco-switch {
  background-color: var(--color-fill-4);
}
.arco-switch-checked {
  background-color: rgb(var(--primary-6));
}

.layout, .layout-columns-left-panel, .layout-columns-right-panel {
  height: 100%;
}

.layout-columns-left-panel {
  .sider { padding: 5px; height: 100%; overflow-y: auto; overflow-x: hidden }

  .layout-menu {
    position: relative; z-index: 3; overflow: hidden;
  }

  .menu-title {
    height: 51px; padding-left: 10px; font-weight: bold;
    background-color: var(--color-bg-2);
    border-bottom:1px solid var(--color-border-1);
  }
}

.layout-columns-right-panel {
  width: 100%; background-color: var(--color-neutral-2);
  .layout-header {
    background-color: var(--color-bg-2);
    width: 100%; box-shadow: 1px 1px 2px var(--color-neutral-2);
  }
}

.layout-banner-header {
  height: 52px; border-bottom:1px solid var(--color-border-1);
  background-color: var(--color-bg-2);
  .logo {
    width: 220px; padding-bottom: 1px; border-bottom: 0;
  }

  .banner-menus li {
    cursor: pointer; padding: 0px 15px; height: 40px;
    margin-left: 5px; border-radius: 4px; color: var(--color-neutral-10); fill: var(--color-neutral-10);

    .icon {
      width: 1.1em; height: 1.1em; display: inline;
    }
  }

  .banner-menus li:hover {
    background-color: var(--color-neutral-2);
  }
  .banner-menus li.active {
    background-color: rgb(var(--primary-4)); color: var(--color-white);
    fill: var(--color-white);
  }
}
.layout-banner-content {
  .tags {
    border-top: 0;
  }
}

.layout-classic-sider { box-shadow: none; }
.layout-classic-header {
  .layout-classic-header-container {
    background-color: var(--color-bg-2); height: 50px;
  }
}

.ma-menu .arco-menu-inner::-webkit-scrollbar,
.backend-setting .arco-drawer-body::-webkit-scrollbar,
.arco-list::-webkit-scrollbar,
.customer-scrollbar::-webkit-scrollbar
{ 
  width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 7px;
}

.ma-menu .arco-menu-inner::-webkit-scrollbar-thumb,
.backend-setting .arco-drawer-body::-webkit-scrollbar-thumb,
.arco-list::-webkit-scrollbar-thumb,
.customer-scrollbar::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background: var(--color-text-4)
}
.ma-menu .arco-menu-inner::-webkit-scrollbar-thumb:hover,
.backend-setting .arco-drawer-body::-webkit-scrollbar-thumb:hover,
.arco-list::-webkit-scrollbar-thumb:hover,
.customer-scrollbar::-webkit-scrollbar-thumb:hover
{
  border-radius: 10px;
  background: var(--color-text-3)
}
.ma-menu .arco-menu-inner::-webkit-scrollbar-track,
.backend-setting .arco-drawer-body::-webkit-scrollbar-track,
.arco-list::-webkit-scrollbar-track,
.customer-scrollbar::-webkit-scrollbar-track
{
  border-radius: 0;
  background: var(--color-text-5)
}

.tags-container {
  background-color: var(--color-bg-2);
  border-top:1px solid var(--color-border-1);
  .tags {
    border-bottom:1px solid var(--color-border-1);
    background-color: var(--color-bg-2);
    height: 40px; position: relative;
    display: flex; align-items: center;
    overflow: hidden; width: 100%;
    div {
      font-size: 13px; color: var(--color-text-2);
      position: relative; flex-shrink: 0;
      padding: 4px 8px; border-radius: 4px;
      border: 1px solid var(--color-border-2);
      cursor: pointer; transition: all 0.2s; margin-left: 10px;
      display: inline-flex; justify-content: center; align-items: center;

      .tag-icon { margin-left: 5px; font-size:18px; padding:2px; border-radius: 10px;}

      .tag-icon:hover { color: var(--color-white); background-color: rgb(var(--primary-6));}
    }

    div:hover {
      border: 1px solid rgb(var(--primary-3)); color: rgb(var(--primary-6));
    }

    div.active{
      background-color: rgb(var(--primary-1)); color: rgb(var(--primary-6));border: 1px solid rgb(var(--primary-3));
      .tag-icon { color: var(--primary-6);}
      .tag-icon:hover { color: var(--color-white); background-color: rgb(var(--primary-6));}
    }
  }

  &.tags-tool {
    width: 100px;
  }

  .tags-contextmenu {
    position: fixed; border: 1px solid var(--color-border-2);
    padding: 5px 0; z-index: 999;
    width: 170px; background-color: var(--color-bg-5);
    border-radius: 4px;
    
    .arco-divider-horizontal {
      margin: 5px 0;
    }

    li {
      padding: 7px 15px; color: var(--color-text-2); font-size: 13px;
    }
    li:hover {
      background-color: rgb(var(--primary-1));
      cursor: pointer;
    }
    li.disabled {
      color: var(--color-text-4); cursor: no-drop;
    }
  }
}

.work-area {
  background-color: var(--color-neutral-2);
  color: var(--color-text-2);
  height: 100%; overflow-y: auto;
  .content-block-title { color: var(--color-text-1); font-size: 1.3em; }

  .ma-content-block {
    background-color: var(--color-bg-2); border-radius: 2px;
  }
}

.button-menu {
  position: fixed; bottom: 30px; left: 20px; z-index: 100;
  .button-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--color-white);
    font-size: 14px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.1s;
    :deep(.icon) {
      margin-left: 1px !important;
    }
  }
  .button-trigger:nth-child(1) {
    background-color: var(--color-primary-light-4);
  }
  .button-trigger:nth-child(1).button-trigger-active {
    background-color: rgb(var(--primary-6));
  }
}

.ma-ui.max-size {
  .max-size-exit {display: block;}
  .ma-ui-slider, .ma-ui-header, .ma-ui-menu, .ma-ui-tags {display: none;}
}

.ma-ui {
  .max-size-exit {
    position: fixed;
    z-index: 9999;
    top: -45px;
    left: 50%;
    margin-left: -30px;
    cursor: pointer;
    display: none;
    width: 60px;
    height: 60px;
    text-align: center;
    fill: var(--color-white);
    color: var(--color-white);
    font-size: 45px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
  }
  .max-size-exit:hover {
    top: 0px;
  }
}

.tag-primary {
  color: var(--color-white);
  background-color: rgb(var(--primary-6));
}
