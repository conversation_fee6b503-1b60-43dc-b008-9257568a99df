# SaiAdmin MySQL 服务检查脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    SaiAdmin MySQL 服务检查" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查常见的MySQL服务
$mysqlServices = @("mysql", "MySQL80", "MySQL57", "MySQL", "wampmysqld64", "xampp-mysql")
$foundService = $false

Write-Host "检查MySQL服务状态..." -ForegroundColor Yellow
foreach ($service in $mysqlServices) {
    try {
        $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
        if ($svc) {
            $foundService = $true
            Write-Host "✅ 找到服务: $($svc.Name)" -ForegroundColor Green
            Write-Host "   状态: $($svc.Status)" -ForegroundColor $(if($svc.Status -eq "Running"){"Green"}else{"Red"})
            
            if ($svc.Status -ne "Running") {
                Write-Host "   尝试启动服务..." -ForegroundColor Yellow
                try {
                    Start-Service -Name $service
                    Write-Host "   ✅ 服务启动成功" -ForegroundColor Green
                } catch {
                    Write-Host "   ❌ 服务启动失败: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    } catch {
        # 服务不存在，继续检查下一个
    }
}

if (-not $foundService) {
    Write-Host "❌ 未找到MySQL服务" -ForegroundColor Red
    Write-Host ""
    Write-Host "可能的解决方案:" -ForegroundColor Yellow
    Write-Host "1. 安装MySQL服务器" -ForegroundColor White
    Write-Host "2. 使用XAMPP/WAMP等集成环境" -ForegroundColor White
    Write-Host "3. 切换到SQLite数据库" -ForegroundColor White
}

Write-Host ""
Write-Host "检查MySQL连接..." -ForegroundColor Yellow

# 检查MySQL命令是否可用
try {
    $mysqlVersion = & mysql --version 2>$null
    if ($mysqlVersion) {
        Write-Host "✅ MySQL客户端可用: $mysqlVersion" -ForegroundColor Green
        
        # 测试连接
        Write-Host "测试数据库连接..." -ForegroundColor Yellow
        $testConnection = & mysql -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 数据库连接成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 数据库连接失败" -ForegroundColor Red
            Write-Host "   错误可能原因:" -ForegroundColor Yellow
            Write-Host "   - 密码错误" -ForegroundColor White
            Write-Host "   - 用户权限不足" -ForegroundColor White
            Write-Host "   - MySQL服务未完全启动" -ForegroundColor White
        }
    }
} catch {
    Write-Host "❌ MySQL客户端不可用" -ForegroundColor Red
}

Write-Host ""
Write-Host "当前数据库配置:" -ForegroundColor Yellow
Write-Host "- 主机: 127.0.0.1:3306" -ForegroundColor White
Write-Host "- 数据库: saiadmin" -ForegroundColor White
Write-Host "- 用户: root" -ForegroundColor White
Write-Host "- 密码: 5GeNi1v7P7Xcur5W" -ForegroundColor White

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "检查完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
