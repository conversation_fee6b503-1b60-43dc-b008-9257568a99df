// Native JavaScript for Bootstrap v4.0.7 | 2021 © dnp_theme | MIT-License
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).BSN=e()}(this,(function(){"use strict";var t="webkitTransition"in document.head.style?"webkitTransitionEnd":"transitionend",e="webkitTransition"in document.head.style||"transition"in document.head.style,i="webkitTransition"in document.head.style?"webkitTransitionDuration":"transitionDuration",n="webkitTransition"in document.head.style?"webkitTransitionProperty":"transitionProperty";function a(t){var a=getComputedStyle(t),o=a[n],s=a[i],l=s.includes("ms")?1:1e3,c=e&&o&&"none"!==o?parseFloat(s)*l:0;return Number.isNaN(c)?0:c}function o(e,i){var n=0,o=new Event(t),s=a(e);s?(e.addEventListener(t,(function a(o){o.target===e&&(i.apply(e,[o]),e.removeEventListener(t,a),n=1)})),setTimeout((function(){n||e.dispatchEvent(o)}),s+17)):i.apply(e,[o])}function s(t,e){var i=e&&e instanceof Element?e:document;return t instanceof Element?t:i.querySelector(t)}function l(t,e,i){var n=new CustomEvent(t+".bs."+e,{cancelable:!0});return void 0!==i&&Object.keys(i).forEach((function(t){Object.defineProperty(n,t,{value:i[t]})})),n}function c(t){this&&this.dispatchEvent(t)}function r(t){var e,i,n=this,a=l("close","alert"),r=l("closed","alert");function d(t){e[t?"addEventListener":"removeEventListener"]("click",u,!1)}function u(t){i=t&&t.target.closest(".alert"),(e=s('[data-dismiss="alert"]',i))&&i&&(e===t.target||e.contains(t.target))&&n.close()}function m(){d(),i.parentNode.removeChild(i),c.call(i,r)}n.close=function(){if(i&&e&&i.classList.contains("show")){if(c.call(i,a),a.defaultPrevented)return;n.dispose(),i.classList.remove("show"),i.classList.contains("fade")?o(i,m):m()}},n.dispose=function(){d(),delete e.Alert},e=s(t),i=e.closest(".alert"),e.Alert&&e.Alert.dispose(),e.Alert||d(1),n.element=e,e.Alert=n}function d(t){var e,i,n=l("change","button");function a(t){var a=t.target,o=a.closest("LABEL"),s=null;"LABEL"===a.tagName?s=a:o&&(s=o);var l=s&&s.getElementsByTagName("INPUT")[0];if(l){if(c.call(l,n),c.call(e,n),"checkbox"===l.type){if(n.defaultPrevented)return;l.checked?(s.classList.remove("active"),l.getAttribute("checked"),l.removeAttribute("checked"),l.checked=!1):(s.classList.add("active"),l.getAttribute("checked"),l.setAttribute("checked","checked"),l.checked=!0),e.toggled||(e.toggled=!0)}if("radio"===l.type&&!e.toggled){if(n.defaultPrevented)return;(!l.checked||0===t.screenX&&0===t.screenY)&&(s.classList.add("active"),s.classList.add("focus"),l.setAttribute("checked","checked"),l.checked=!0,e.toggled=!0,Array.from(i).forEach((function(t){var e=t.getElementsByTagName("INPUT")[0];t!==s&&t.classList.contains("active")&&(c.call(e,n),t.classList.remove("active"),e.removeAttribute("checked"),e.checked=!1)})))}setTimeout((function(){e.toggled=!1}),50)}}function o(t){32===(t.which||t.keyCode)&&t.target===document.activeElement&&a(t)}function r(t){32===(t.which||t.keyCode)&&t.preventDefault()}function d(t){if("INPUT"===t.target.tagName){var e="focusin"===t.type?"add":"remove";t.target.closest(".btn").classList[e]("focus")}}function u(t){var i=t?"addEventListener":"removeEventListener";e[i]("click",a,!1),e[i]("keyup",o,!1),e[i]("keydown",r,!1),e[i]("focusin",d,!1),e[i]("focusout",d,!1)}this.dispose=function(){u(),delete e.Button},(e=s(t)).Button&&e.Button.dispose(),(i=e.getElementsByClassName("btn")).length&&(e.Button||u(1),e.toggled=!1,e.Button=this,Array.from(i).forEach((function(t){var e=s("input:checked",t);!t.classList.contains("active")&&e&&t.classList.add("active"),t.classList.contains("active")&&!e&&t.classList.remove("active")})))}var u="onmouseleave"in document?["mouseenter","mouseleave"]:["mouseover","mouseout"],m=!!function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){return t=!0}});document.addEventListener("DOMContentLoaded",(function t(){document.removeEventListener("DOMContentLoaded",t,e)}),e)}catch(t){throw Error("Passive events are not supported")}return t}()&&{passive:!0};function f(t){return t.offsetHeight}function h(t,e){var i,n,r,d,h,g,p,v,L,b,y,w,A,T=e||{},E=this;function k(){!1===g.interval||d.classList.contains("paused")||(d.classList.add("paused"),h.isSliding||(clearInterval(h.timer),h.timer=null))}function x(){!1!==g.interval&&d.classList.contains("paused")&&(d.classList.remove("paused"),h.isSliding||(clearInterval(h.timer),h.timer=null,E.cycle()))}function C(t){if(t.preventDefault(),!h.isSliding){var e=t.target;e&&!e.classList.contains("active")&&e.getAttribute("data-slide-to")&&(h.index=+e.getAttribute("data-slide-to"),E.slideTo(h.index))}}function N(t){if(t.preventDefault(),!h.isSliding){var e=t.currentTarget||t.srcElement;e===y?h.index+=1:e===b&&(h.index-=1),E.slideTo(h.index)}}function P(t){var e=t.which;if(!h.isSliding){switch(e){case 39:h.index+=1;break;case 37:h.index-=1;break;default:return}E.slideTo(h.index)}}function H(t){var e=t?"addEventListener":"removeEventListener";g.pause&&g.interval&&(d[e](u[0],k,!1),d[e](u[1],x,!1),d[e]("touchstart",k,m),d[e]("touchend",x,m)),g.touch&&L.length>1&&d[e]("touchstart",B,m),y&&y[e]("click",N,!1),b&&b[e]("click",N,!1),w&&w[e]("click",C,!1),g.keyboard&&window[e]("keydown",P,!1)}function S(t){var e=t?"addEventListener":"removeEventListener";d[e]("touchmove",M,m),d[e]("touchend",I,m)}function B(t){h.isTouch||(h.touchPosition.startX=t.changedTouches[0].pageX,d.contains(t.target)&&(h.isTouch=!0,S(1)))}function M(t){h.isTouch?(h.touchPosition.currentX=t.changedTouches[0].pageX,"touchmove"===t.type&&t.changedTouches.length>1&&t.preventDefault()):t.preventDefault()}function I(t){if(h.isTouch&&!h.isSliding&&(h.touchPosition.endX=h.touchPosition.currentX||t.changedTouches[0].pageX,h.isTouch)){if((!d.contains(t.target)||!d.contains(t.relatedTarget))&&Math.abs(h.touchPosition.startX-h.touchPosition.endX)<75)return;h.touchPosition.currentX<h.touchPosition.startX?h.index+=1:h.touchPosition.currentX>h.touchPosition.startX&&(h.index-=1),h.isTouch=!1,E.slideTo(h.index),S()}}function D(t){Array.from(A).forEach((function(t){return t.classList.remove("active")})),A[t]&&A[t].classList.add("active")}function X(t){if(h.touchPosition){var e=h.index,i=t&&t.target!==L[e]?1e3*t.elapsedTime+100:20,n=E.getActiveIndex(),a="left"===h.direction?"next":"prev";h.isSliding&&setTimeout((function(){h.touchPosition&&(h.isSliding=!1,L[e].classList.add("active"),L[n].classList.remove("active"),L[e].classList.remove("carousel-item-"+a),L[e].classList.remove("carousel-item-"+h.direction),L[n].classList.remove("carousel-item-"+h.direction),c.call(d,v),document.hidden||!g.interval||d.classList.contains("paused")||E.cycle())}),i)}}if(E.cycle=function(){h.timer&&(clearInterval(h.timer),h.timer=null),h.timer=setInterval((function(){var t=h.index||E.getActiveIndex();(function(t){var e=t.getBoundingClientRect(),i=window.innerHeight||document.documentElement.clientHeight;return e.top<=i&&e.bottom>=0})(d)&&(t+=1,E.slideTo(t))}),g.interval)},E.slideTo=function(t){if(!h.isSliding){var e=E.getActiveIndex(),i=t;if(e!==i){e<i||0===e&&i===L.length-1?h.direction="left":(e>i||e===L.length-1&&0===i)&&(h.direction="right"),i<0?i=L.length-1:i>=L.length&&(i=0);var n="left"===h.direction?"next":"prev",s={relatedTarget:L[i],direction:h.direction,from:e,to:i};p=l("slide","carousel",s),v=l("slid","carousel",s),c.call(d,p),p.defaultPrevented||(h.index=i,h.isSliding=!0,clearInterval(h.timer),h.timer=null,D(i),a(L[i])&&d.classList.contains("slide")?(L[i].classList.add("carousel-item-"+n),f(L[i]),L[i].classList.add("carousel-item-"+h.direction),L[e].classList.add("carousel-item-"+h.direction),o(L[i],X)):(L[i].classList.add("active"),f(L[i]),L[e].classList.remove("active"),setTimeout((function(){h.isSliding=!1,g.interval&&d&&!d.classList.contains("paused")&&E.cycle(),c.call(d,v)}),100)))}}},E.getActiveIndex=function(){return Array.from(L).indexOf(d.getElementsByClassName("carousel-item active")[0])||0},E.dispose=function(){var t=["left","right","prev","next"];Array.from(L).forEach((function(e,i){e.classList.contains("active")&&D(i),t.forEach((function(t){return e.classList.remove("carousel-item-"+t)}))})),clearInterval(h.timer),H(),h={},g={},delete d.Carousel},(d=s(t)).Carousel&&d.Carousel.dispose(),L=d.getElementsByClassName("carousel-item"),i=d.getElementsByClassName("carousel-control-prev"),b=i[0],n=d.getElementsByClassName("carousel-control-next"),y=n[0],r=d.getElementsByClassName("carousel-indicators"),w=r[0],A=w&&w.getElementsByTagName("LI")||[],!(L.length<2)){var O=d.getAttribute("data-interval"),R="false"===O?0:+O,W="false"===d.getAttribute("data-touch")?0:1,j="hover"===d.getAttribute("data-pause")||!1,z="true"===d.getAttribute("data-keyboard")||!1,U=T.interval,q=T.touch;(g={}).keyboard=!0===T.keyboard||z,g.pause=!("hover"!==T.pause&&!j)&&"hover",g.touch=q||W,g.interval=5e3,"number"==typeof U?g.interval=U:!1===U||0===R||!1===R?g.interval=0:Number.isNaN(R)||(g.interval=R),E.getActiveIndex()<0&&(L.length&&L[0].classList.add("active"),A.length&&D(0)),(h={}).direction="left",h.index=0,h.timer=null,h.isSliding=!1,h.isTouch=!1,h.touchPosition={startX:0,currentX:0,endX:0},H(1),g.interval&&E.cycle(),d.Carousel=E}}function g(t,e){var i,n,a,r,d,u,m,h=e||{},g=this,p=null,v=null;function L(t,e){c.call(t,u),u.defaultPrevented||(t.isAnimating=!0,t.style.height=t.scrollHeight+"px",t.classList.remove("collapse"),t.classList.remove("show"),t.classList.add("collapsing"),f(t),t.style.height="0px",o(t,(function(){t.isAnimating=!1,t.setAttribute("aria-expanded","false"),e.setAttribute("aria-expanded","false"),t.classList.remove("collapsing"),t.classList.add("collapse"),t.style.height="",c.call(t,m)})))}g.toggle=function(t){(t&&"A"===t.target.tagName||"A"===i.tagName)&&t.preventDefault(),(i.contains(t.target)||t.target===i)&&(v.classList.contains("show")?g.hide():g.show())},g.hide=function(){v.isAnimating||(L(v,i),i.classList.add("collapsed"))},g.show=function(){var t,e,l;p&&(t=p.getElementsByClassName("collapse show"),n=t[0],a=n&&(s('[data-target="#'+n.id+'"]',p)||s('[href="#'+n.id+'"]',p))),v.isAnimating||(a&&n!==v&&(L(n,a),a.classList.add("collapsed")),e=v,l=i,c.call(e,r),r.defaultPrevented||(e.isAnimating=!0,e.classList.add("collapsing"),e.classList.remove("collapse"),e.style.height=e.scrollHeight+"px",o(e,(function(){e.isAnimating=!1,e.setAttribute("aria-expanded","true"),l.setAttribute("aria-expanded","true"),e.classList.remove("collapsing"),e.classList.add("collapse"),e.classList.add("show"),e.style.height="",c.call(e,d)}))),i.classList.remove("collapsed"))},g.dispose=function(){i.removeEventListener("click",g.toggle,!1),delete i.Collapse},(i=s(t)).Collapse&&i.Collapse.dispose();var b=i.getAttribute("data-parent");r=l("show","collapse"),d=l("shown","collapse"),u=l("hide","collapse"),m=l("hidden","collapse"),null!==(v=s(h.target||i.getAttribute("data-target")||i.getAttribute("href")))&&(v.isAnimating=!1);var y=h.parent||b;p=y?i.closest(y):null,i.Collapse||i.addEventListener("click",g.toggle,!1),i.Collapse=g}function p(t){t.focus()}function v(t,e){var i,n,a,o,r,d,u,m,f=this,h=null,g=[];function v(t){(t.hasAttribute("href")&&"#"===t.href.slice(-1)||t.parentNode&&t.hasAttribute("href")&&"#"===t.parentNode.href.slice(-1))&&this.preventDefault()}function L(){var t=i.open?"addEventListener":"removeEventListener";document[t]("click",b,!1),document[t]("keydown",w,!1),document[t]("keyup",A,!1),document[t]("focus",b,!1)}function b(t){var e=t.target;if(e.getAttribute){var n=e&&e.getAttribute("data-toggle")||e.parentNode&&e.parentNode.getAttribute&&e.parentNode.getAttribute("data-toggle");("focus"!==t.type||e!==i&&e!==u&&!u.contains(e))&&(e!==u&&!u.contains(e)||!m&&!n)&&(h=e===i||i.contains(e)?i:null,f.hide(),v.call(t,e))}}function y(t){h=i,f.show(),v.call(t,t.target)}function w(t){var e=t.which||t.keyCode;38!==e&&40!==e||t.preventDefault()}function A(t){var e=t.which||t.keyCode,n=document.activeElement,a=n===i,o=u.contains(n),s=n.parentNode===u||n.parentNode.parentNode===u,l=g.indexOf(n);s&&(a?l=0:38===e?l=l>1?l-1:0:40===e&&(l=l<g.length-1?l+1:l),g[l]&&p(g[l])),(g.length&&s||!g.length&&(o||a)||!o)&&i.open&&27===e&&(f.toggle(),h=null)}f.show=function(){n=l("show","dropdown",{relatedTarget:h}),c.call(d,n),n.defaultPrevented||(u.classList.add("show"),d.classList.add("show"),i.setAttribute("aria-expanded",!0),i.open=!0,i.removeEventListener("click",y,!1),setTimeout((function(){p(u.getElementsByTagName("INPUT")[0]||i),L(),a=l("shown","dropdown",{relatedTarget:h}),c.call(d,a)}),1))},f.hide=function(){o=l("hide","dropdown",{relatedTarget:h}),c.call(d,o),o.defaultPrevented||(u.classList.remove("show"),d.classList.remove("show"),i.setAttribute("aria-expanded",!1),i.open=!1,L(),p(i),setTimeout((function(){i.Dropdown&&i.addEventListener("click",y,!1)}),1),r=l("hidden","dropdown",{relatedTarget:h}),c.call(d,r))},f.toggle=function(){d.classList.contains("show")&&i.open?f.hide():f.show()},f.dispose=function(){d.classList.contains("show")&&i.open&&f.hide(),i.removeEventListener("click",y,!1),delete i.Dropdown},(i=s(t)).Dropdown&&i.Dropdown.dispose(),d=i.parentNode,u=s(".dropdown-menu",d),Array.from(u.children).forEach((function(t){t.children.length&&"A"===t.children[0].tagName&&g.push(t.children[0]),"A"===t.tagName&&g.push(t)})),i.Dropdown||("tabindex"in u||u.setAttribute("tabindex","0"),i.addEventListener("click",y,!1)),m=!0===e||"true"===i.getAttribute("data-persist")||!1,i.open=!1,i.Dropdown=f}function L(t,e){var i,n,r,d,u,h,g,v,L,b,y=e||{},w=this,A=null,T={};function E(){var t=document.body.classList.contains("modal-open"),e=parseInt(getComputedStyle(document.body).paddingRight,10),i=document.documentElement.clientHeight,a=document.documentElement.scrollHeight,o=document.body.clientHeight,s=document.body.scrollHeight,l=i!==a||o!==s,c=n.clientHeight!==n.scrollHeight;g=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),e}(),n.style.paddingRight=!c&&g?g+"px":"",document.body.style.paddingRight=c||l?e+(t?0:g)+"px":"",b.length&&b.forEach((function(e){var i=getComputedStyle(e).paddingRight;e.style.paddingRight=c||l?parseInt(i,10)+(t?0:g)+"px":parseInt(i,10)+"px"}))}function k(){(v=s(".modal-backdrop"))&&!document.getElementsByClassName("modal show")[0]&&(document.body.removeChild(v),v=null),null===v&&(document.body.classList.remove("modal-open"),document.body.style.paddingRight="",n.style.paddingRight="",b.length&&b.forEach((function(t){t.style.paddingRight=""})))}function x(t){var e=t?"addEventListener":"removeEventListener";window[e]("resize",w.update,m),n[e]("click",B,!1),document[e]("keydown",S,!1)}function C(){n.style.display="block",E(),document.getElementsByClassName("modal show")[0]||document.body.classList.add("modal-open"),n.classList.add("show"),n.setAttribute("aria-hidden",!1),n.classList.contains("fade")?o(n,N):N()}function N(){p(n),n.isAnimating=!1,x(1),d=l("shown","modal",{relatedTarget:A}),c.call(n,d)}function P(t){n.style.display="",i&&p(i),v=s(".modal-backdrop"),1!==t&&v&&v.classList.contains("show")&&!document.getElementsByClassName("modal show")[0]?(v.classList.remove("show"),o(v,k)):k(),x(),n.isAnimating=!1,h=l("hidden","modal"),c.call(n,h)}function H(t){if(!n.isAnimating){var e=t.target,a="#"+n.getAttribute("id"),o=e.getAttribute("data-target")||e.getAttribute("href"),s=i.getAttribute("data-target")||i.getAttribute("href");!n.classList.contains("show")&&(e===i&&o===a||i.contains(e)&&s===a)&&(n.modalTrigger=i,A=i,w.show(),t.preventDefault())}}function S(t){var e=t.which;!n.isAnimating&&T.keyboard&&27===e&&n.classList.contains("show")&&w.hide()}function B(t){if(!n.isAnimating){var e=t.target,i="modal"===e.getAttribute("data-dismiss"),a=e.closest('[data-dismiss="modal"]');n.classList.contains("show")&&(a||i||e===n&&"static"!==T.backdrop)&&(w.hide(),A=null,t.preventDefault())}}w.toggle=function(){n.classList.contains("show")?w.hide():w.show()},w.show=function(){if(!(n.classList.contains("show")&&n.isAnimating||(r=l("show","modal",{relatedTarget:A}),c.call(n,r),r.defaultPrevented))){n.isAnimating=!0;var t,e=document.getElementsByClassName("modal show")[0];e&&e!==n&&(e.modalTrigger&&e.modalTrigger.Modal.hide(),e.Modal&&e.Modal.hide()),T.backdrop&&(t=document.createElement("div"),null===(v=s(".modal-backdrop"))&&(t.setAttribute("class","modal-backdrop"+(T.animation?" fade":"")),v=t,document.body.appendChild(v)),v=v),!v||e||v.classList.contains("show")||(f(v),L=a(v),v.classList.add("show")),e?C():setTimeout(C,v&&L?L:0)}},w.hide=function(t){n.classList.contains("show")&&(u=l("hide","modal"),c.call(n,u),u.defaultPrevented||(n.isAnimating=!0,n.classList.remove("show"),n.setAttribute("aria-hidden",!0),n.classList.contains("fade")&&1!==t?o(n,P):P()))},w.setContent=function(t){s(".modal-content",n).innerHTML=t},w.update=function(){n.classList.contains("show")&&E()},w.dispose=function(){w.hide(1),i?(i.removeEventListener("click",H,!1),delete i.Modal):delete n.Modal},i=s(t);var M=s(i.getAttribute("data-target")||i.getAttribute("href"));n=i.classList.contains("modal")?i:M,b=Array.from(document.getElementsByClassName("fixed-top")).concat(Array.from(document.getElementsByClassName("fixed-bottom"))),i.classList.contains("modal")&&(i=null),i&&i.Modal&&i.Modal.dispose(),n&&n.Modal&&n.Modal.dispose(),T.keyboard=!(!1===y.keyboard||"false"===n.getAttribute("data-keyboard")),T.backdrop="static"!==y.backdrop&&"static"!==n.getAttribute("data-backdrop")||"static",T.backdrop=!1!==y.backdrop&&"false"!==n.getAttribute("data-backdrop")&&T.backdrop,T.animation=!!n.classList.contains("fade"),T.content=y.content,n.isAnimating=!1,i&&!i.Modal&&i.addEventListener("click",H,!1),T.content&&w.setContent(T.content.trim()),i?(n.modalTrigger=i,i.Modal=w):n.Modal=w}var b="mousedown";function y(){return{y:window.pageYOffset||document.documentElement.scrollTop,x:window.pageXOffset||document.documentElement.scrollLeft}}function w(t,e,i,n){var a,o,s,l,c=e.offsetWidth,r=e.offsetHeight,d=document.documentElement.clientWidth||document.body.clientWidth,u=document.documentElement.clientHeight||document.body.clientHeight,m=t.getBoundingClientRect(),f=n===document.body?y():{x:n.offsetLeft+n.scrollLeft,y:n.offsetTop+n.scrollTop},h=m.right-m.left,g=m.bottom-m.top,p=e.classList.contains("popover"),v=e.getElementsByClassName("arrow")[0],L=m.top+g/2-r/2<0,b=m.left+h/2-c/2<0,w=m.left+c/2+h/2>=d,A=m.top+r/2+g/2>=u,T=m.top-r<0,E=m.left-c<0,k=m.top+r+g>=u,x=m.left+c+h>=d,C=i;C="right"===(C="left"===(C="bottom"===(C="top"===(C=("left"===C||"right"===C)&&E&&x?"top":C)&&T?"bottom":C)&&k?"top":C)&&E?"right":C)&&x?"left":C,-1===e.className.indexOf(C)&&(e.className=e.className.replace(/\b(top|bottom|left|right)+/,C));var N=v.offsetWidth,P=v.offsetHeight;"left"===C||"right"===C?(o="left"===C?m.left+f.x-c-(p?N:0):m.left+f.x+h,L?(a=m.top+f.y,s=g/2-N):A?(a=m.top+f.y-r+g,s=r-g/2-N):(a=m.top+f.y-r/2+g/2,s=r/2-(p?.9*P:P/2))):"top"!==C&&"bottom"!==C||(a="top"===C?m.top+f.y-r-(p?P:0):m.top+f.y+g,b?(o=0,l=m.left+h/2-N):w?(o=d-1.01*c,l=c-(d-m.left)+h/2-N/2):(o=m.left+f.x-c/2+h/2,l=c/2-(p?N:N/2))),e.style.top=a+"px",e.style.left=o+"px",s&&(v.style.top=s+"px"),l&&(v.style.left=l+"px")}function A(t,e){var i,n,a,r,d,f,h,g,p,v=e||{},L=this,y=null,A=0,T=/(iPhone|iPod|iPad)/.test(navigator.userAgent),E={};function k(t){null!==y&&t.target===s(".close",y)&&L.hide()}function x(t){return v[t]||i.dataset[t]||null}function C(){return x("title")}function N(){return x("content")}function P(){null===y&&i.focus()}function H(t){var e=t?"addEventListener":"removeEventListener";"hover"===E.trigger?(i[e](b,L.show),i[e](u[0],L.show),E.dismissible||i[e](u[1],L.hide)):"click"===E.trigger?i[e](E.trigger,L.toggle):"focus"===E.trigger&&(T&&i[e]("click",P,!1),i[e](E.trigger,L.toggle))}function S(t){y&&y.contains(t.target)||t.target===i||i.contains(t.target)||L.hide()}function B(t){var e=t?"addEventListener":"removeEventListener";E.dismissible?document[e]("click",k,!1):("focus"===E.trigger&&i[e]("blur",L.hide),"hover"===E.trigger&&document[e]("touchstart",S,m)),window[e]("resize",L.hide,m)}function M(){B(1),c.call(i,h)}function I(){B(),E.container.removeChild(y),A=null,y=null,c.call(i,p)}L.toggle=function(){null===y?L.show():L.hide()},L.show=function(){clearTimeout(A),A=setTimeout((function(){if(null===y){if(c.call(i,f),f.defaultPrevented)return;!function(){n=C(),a=(a=N())?a.trim():null,y=document.createElement("div");var t=document.createElement("div");if(t.classList.add("arrow"),y.appendChild(t),null!==a&&null===E.template){if(y.setAttribute("role","tooltip"),null!==n){var e=document.createElement("h3");e.classList.add("popover-header"),e.innerHTML=E.dismissible?n+d:n,y.appendChild(e)}var i=document.createElement("div");i.classList.add("popover-body"),i.innerHTML=E.dismissible&&null===n?a+d:a,y.appendChild(i)}else{var o=document.createElement("div");o.innerHTML=E.template.trim(),y.className=o.firstChild.className,y.innerHTML=o.firstChild.innerHTML;var l=s(".popover-header",y),c=s(".popover-body",y);n&&l&&(l.innerHTML=n.trim()),a&&c&&(c.innerHTML=a.trim())}E.container.appendChild(y),y.style.display="block",y.classList.contains("popover")||y.classList.add("popover"),y.classList.contains(E.animation)||y.classList.add(E.animation),y.classList.contains(r)||y.classList.add(r)}(),w(i,y,E.placement,E.container),y.classList.contains("show")||y.classList.add("show"),E.animation?o(y,M):M()}}),20)},L.hide=function(){clearTimeout(A),A=setTimeout((function(){if(y&&null!==y&&y.classList.contains("show")){if(c.call(i,g),g.defaultPrevented)return;y.classList.remove("show"),E.animation?o(y,I):I()}}),E.delay)},L.dispose=function(){L.hide(),H(),delete i.Popover},(i=s(t)).Popover&&i.Popover.dispose();var D=i.getAttribute("data-trigger"),X=i.getAttribute("data-animation"),O=i.getAttribute("data-placement"),R=i.getAttribute("data-dismissible"),W=i.getAttribute("data-delay"),j=i.getAttribute("data-container");d='<button type="button" class="close">×</button>',f=l("show","popover"),h=l("shown","popover"),g=l("hide","popover"),p=l("hidden","popover");var z=s(v.container),U=s(j),q=i.closest(".modal"),Y=i.closest(".fixed-top"),F=i.closest(".fixed-bottom");E.template=v.template?v.template:null,E.trigger=v.trigger?v.trigger:D||"hover",E.animation=v.animation&&"fade"!==v.animation?v.animation:X||"fade",E.placement=v.placement?v.placement:O||"top",E.delay=parseInt(v.delay||W,10)||200,E.dismissible=!(!v.dismissible&&"true"!==R),E.container=z||U||Y||F||q||document.body,r="bs-popover-"+E.placement,n=C(),((a=N())||E.template)&&(i.Popover||H(1),i.Popover=L)}function T(t,e){var i,n,a,o,r,d=e||{},u=this,f={};function h(){var t,e,l;(a=o.getElementsByTagName("A"),n.scrollTop=n.isWindow?y().y:i.scrollTop,n.length!==a.length||p()!==n.scrollHeight)&&(n.items=[],n.offsets=[],n.scrollHeight=p(),n.maxScroll=n.scrollHeight-(n.isWindow?window.innerHeight:i.getBoundingClientRect().height),Array.from(a).forEach((function(i){t=i.getAttribute("href"),(e=t&&"#"===t.charAt(0)&&"#"!==t.slice(-1)&&s(t))&&(n.items.push(i),l=e.getBoundingClientRect(),n.offsets.push((n.isWindow?l.top+n.scrollTop:e.offsetTop)-f.offset))})),n.length=n.items.length)}function g(t){var e=t?"addEventListener":"removeEventListener";r[e]("scroll",u.refresh,m),window[e]("resize",u.refresh,m)}function p(){return r.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)}function v(){Array.from(a).map((function(t){return t.classList.contains("active")&&t.classList.remove("active")}))}function L(t){var e,a=t;v(),n.activeItem=a,a.classList.add("active");for(var o=[];a.parentNode!==document.body;)((e=(a=a.parentNode).classList).contains("dropdown-menu")||e.contains("nav"))&&o.push(a);o.forEach((function(t){var e=t.previousElementSibling;e&&!e.classList.contains("active")&&e.classList.add("active")})),c.call(i,l("activate","scrollspy",{relatedTarget:n.activeItem}))}u.refresh=function(){if(h(),n.scrollTop>=n.maxScroll){var t=n.items[n.length-1];n.activeItem!==t&&L(t)}else{if(n.activeItem&&n.scrollTop<n.offsets[0]&&n.offsets[0]>0)return n.activeItem=null,void v();for(var e=n.length;e>-1;)n.activeItem!==n.items[e]&&n.scrollTop>=n.offsets[e]&&(void 0===n.offsets[e+1]||n.scrollTop<n.offsets[e+1])&&L(n.items[e]),e-=1}},u.dispose=function(){g(),delete i.ScrollSpy},(i=s(t)).ScrollSpy&&i.ScrollSpy.dispose();var b=i.getAttribute("data-target"),w=i.getAttribute("data-offset");o=s(d.target||b),r=i.clientHeight<i.scrollHeight?i:window,o&&(f.offset=+(d.offset||w)||10,(n={}).length=0,n.items=[],n.offsets=[],n.isWindow=r===window,n.activeItem=null,n.scrollHeight=0,n.maxScroll=0,i.ScrollSpy||g(1),u.refresh(),i.ScrollSpy=u)}function E(t,i){var n,a,r,d,u,m,h,g,p,v,L,b,y,w,A=i||{},T=this,E=!1;function k(){E.style.height="",E.classList.remove("collapsing"),a.isAnimating=!1}function x(){E?y?k():setTimeout((function(){E.style.height=w+"px",f(E),o(E,k)}),50):a.isAnimating=!1,u=l("shown","tab",{relatedTarget:p}),c.call(g,u)}function C(){E&&(v.style.float="left",L.style.float="left",b=v.scrollHeight),d=l("show","tab",{relatedTarget:p}),h=l("hidden","tab",{relatedTarget:g}),c.call(g,d),d.defaultPrevented||(L.classList.add("active"),v.classList.remove("active"),E&&(w=L.scrollHeight,y=w===b,E.classList.add("collapsing"),E.style.height=b+"px",f(E),v.style.float="",L.style.float=""),L.classList.contains("fade")?setTimeout((function(){L.classList.add("show"),o(L,x)}),20):x(),c.call(p,h))}function N(){var t=a.getElementsByClassName("active");return 1!==t.length||t[0].parentNode.classList.contains("dropdown")?t.length>1&&(p=t[t.length-1]):p=t[0],p}function P(){return s(N().getAttribute("href"))}function H(t){t.preventDefault(),g=t.currentTarget,a.isAnimating||T.show()}T.show=function(){if(!(g=g||n).classList.contains("active")){if(L=s(g.getAttribute("href")),p=N(),v=P(),m=l("hide","tab",{relatedTarget:g}),c.call(p,m),m.defaultPrevented)return;a.isAnimating=!0,p.classList.remove("active"),p.setAttribute("aria-selected","false"),g.classList.add("active"),g.setAttribute("aria-selected","true"),r&&(n.parentNode.classList.contains("dropdown-menu")?r.classList.contains("active")||r.classList.add("active"):r.classList.contains("active")&&r.classList.remove("active")),v.classList.contains("fade")?(v.classList.remove("show"),o(v,C)):C()}},T.dispose=function(){n.removeEventListener("click",H,!1),delete n.Tab},(n=s(t)).Tab&&n.Tab.dispose();var S=n.getAttribute("data-height");a=n.closest(".nav"),r=a&&s(".dropdown-toggle",a);var B=!(!e||!1===A.height||"false"===S);a.isAnimating=!1,n.Tab||n.addEventListener("click",H,!1),B&&(E=P().parentNode),n.Tab=T}function k(t,e){var i,n,a,r,d,u,m=e||{},h=this,g=0,p={};function v(){n.classList.remove("showing"),n.classList.add("show"),c.call(n,d),p.autohide&&h.hide()}function L(){n.classList.add("hide"),c.call(n,u)}function b(){n.classList.remove("show"),p.animation?o(n,L):L()}function y(){clearTimeout(g),i.removeEventListener("click",h.hide,!1),delete i.Toast}h.show=function(){if(n&&!n.classList.contains("show")){if(c.call(n,a),a.defaultPrevented)return;p.animation&&n.classList.add("fade"),n.classList.remove("hide"),f(n),n.classList.add("showing"),p.animation?o(n,v):v()}},h.hide=function(t){if(n&&n.classList.contains("show")){if(c.call(n,r),r.defaultPrevented)return;t?b():g=setTimeout(b,p.delay)}},h.dispose=function(){p.animation?o(n,y):y()},(i=s(t)).Toast&&i.Toast.dispose(),n=i.closest(".toast");var w=i.getAttribute("data-animation"),A=i.getAttribute("data-autohide"),T=i.getAttribute("data-delay");a=l("show","toast"),r=l("hide","toast"),d=l("shown","toast"),u=l("hidden","toast"),p.animation=!1===m.animation||"false"===w?0:1,p.autohide=!1===m.autohide||"false"===A?0:1,p.delay=parseInt(m.delay||T,10)||500,i.Toast||i.addEventListener("click",h.hide,!1),i.Toast=h}function x(t,e){var i,n,a,r,d,f,h,g=e||{},p=this,v=null,L=0,y={};function A(){return i.getAttribute("title")||i.getAttribute("data-title")||i.getAttribute("data-original-title")}function T(t){v&&v.contains(t.target)||t.target===i||i.contains(t.target)||p.hide()}function E(t){var e=t?"addEventListener":"removeEventListener";document[e]("touchstart",T,m),window[e]("resize",p.hide,m)}function k(){E(1),c.call(i,d)}function x(){E(),y.container.removeChild(v),v=null,L=null,c.call(i,h)}function C(t){var e=t?"addEventListener":"removeEventListener";i[e](b,p.show,!1),i[e](u[0],p.show,!1),i[e](u[1],p.hide,!1)}p.show=function(){clearTimeout(L),L=setTimeout((function(){if(null===v){if(c.call(i,r),r.defaultPrevented)return;!1!==function(){if(n=A()){if(v=document.createElement("div"),y.template){var t=document.createElement("div");t.innerHTML=y.template.trim(),v.className=t.firstChild.className,v.innerHTML=t.firstChild.innerHTML,s(".tooltip-inner",v).innerHTML=n.trim()}else{var e=document.createElement("div");e.classList.add("arrow"),v.appendChild(e);var i=document.createElement("div");i.classList.add("tooltip-inner"),v.appendChild(i),i.innerHTML=n}v.style.left="0",v.style.top="0",v.setAttribute("role","tooltip"),v.classList.contains("tooltip")||v.classList.add("tooltip"),v.classList.contains(y.animation)||v.classList.add(y.animation),v.classList.contains(a)||v.classList.add(a),y.container.appendChild(v)}}()&&(w(i,v,y.placement,y.container),v.classList.contains("show")||v.classList.add("show"),y.animation?o(v,k):k())}}),20)},p.hide=function(){clearTimeout(L),L=setTimeout((function(){if(v&&v.classList.contains("show")){if(c.call(i,f),f.defaultPrevented)return;v.classList.remove("show"),y.animation?o(v,x):x()}}),y.delay)},p.toggle=function(){v?p.hide():p.show()},p.dispose=function(){C(),p.hide(),i.setAttribute("title",i.getAttribute("data-original-title")),i.removeAttribute("data-original-title"),delete i.Tooltip},(i=s(t)).Tooltip&&i.Tooltip.dispose();var N=i.getAttribute("data-animation"),P=i.getAttribute("data-placement"),H=i.getAttribute("data-delay"),S=i.getAttribute("data-container"),B=s(g.container),M=s(S),I=i.closest(".modal");r=l("show","tooltip"),d=l("shown","tooltip"),f=l("hide","tooltip"),h=l("hidden","tooltip");var D=i.closest(".fixed-top"),X=i.closest(".fixed-bottom");y.animation=g.animation&&"fade"!==g.animation?g.animation:N||"fade",y.placement=g.placement?g.placement:P||"top",y.template=g.template?g.template:null,y.delay=parseInt(g.delay||H,10)||200,y.container=B||M||D||X||I||document.body,a="bs-tooltip-"+y.placement,(n=A())&&(i.Tooltip||(i.setAttribute("data-original-title",n),i.removeAttribute("title"),C(1)),i.Tooltip=p)}var C={};function N(t){var e=t instanceof Element?t:document;Object.keys(C).forEach((function(t){var i,n;i=C[t][0],n=e.querySelectorAll(C[t][1]),Array.from(n).map((function(t){return new i(t)}))}))}C.Alert=[r,'[data-dismiss="alert"]'],C.Button=[d,'[data-toggle="buttons"]'],C.Carousel=[h,'[data-ride="carousel"]'],C.Collapse=[g,'[data-toggle="collapse"]'],C.Dropdown=[v,'[data-toggle="dropdown"]'],C.Modal=[L,'[data-toggle="modal"]'],C.Popover=[A,'[data-toggle="popover"],[data-tip="popover"]'],C.ScrollSpy=[T,'[data-spy="scroll"]'],C.Tab=[E,'[data-toggle="tab"]'],C.Toast=[k,'[data-dismiss="toast"]'],C.Tooltip=[x,'[data-toggle="tooltip"],[data-tip="tooltip"]'],document.body?N():document.addEventListener("DOMContentLoaded",(function t(){N(),document.removeEventListener("DOMContentLoaded",t,!1)}),!1);return{Alert:r,Button:d,Carousel:h,Collapse:g,Dropdown:v,Modal:L,Popover:A,ScrollSpy:T,Tab:E,Toast:k,Tooltip:x,initCallback:N,removeDataAPI:function(t){var e=t instanceof Element?t:document;Object.keys(C).forEach((function(t){var i,n;i=t,n=e.querySelectorAll(C[t][1]),Array.from(n).map((function(t){return t[i].dispose()}))}))},componentsInit:C,Version:"4.0.7"}}));
Alert = BSN.Alert;Button = BSN.Button;Carousel = BSN.Carousel;Collapse = BSN.Collapse;Dropdown = BSN.Dropdown;Modal = BSN.Modal;Popover = BSN.Popover;ScrollSpy = BSN.ScrollSpy;Tab = BSN.Tab;Toast = BSN.Toast;Tooltip = BSN.Tooltip;
