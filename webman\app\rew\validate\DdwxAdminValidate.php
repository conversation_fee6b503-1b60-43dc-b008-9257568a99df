<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\rew\validate;

use think\Validate;

/**
 * 授信验证器
 */
class DdwxAdminValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'choucheng_receivertype' => 'require',
        'choucheng_receivertype1_account' => 'require',
        'choucheng_receivertype1_name' => 'require',
        'choucheng_receivertype2_openidtype' => 'require',
        'choucheng_receivertype2_account' => 'require',
        'choucheng_receivertype2_accountwx' => 'require',
        'choucheng_receivertype2_name' => 'require',
        'buybtn_status' => 'require',
        'othermoney_status' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'choucheng_receivertype' => '必须填写',
        'choucheng_receivertype1_account' => '必须填写',
        'choucheng_receivertype1_name' => '必须填写',
        'choucheng_receivertype2_openidtype' => '必须填写',
        'choucheng_receivertype2_account' => '必须填写',
        'choucheng_receivertype2_accountwx' => '必须填写',
        'choucheng_receivertype2_name' => '必须填写',
        'buybtn_status' => '商城自定义购买按钮 0：关闭 1：开启必须填写',
        'othermoney_status' => '多账户 0：关闭 1：开启必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'choucheng_receivertype',
            'choucheng_receivertype1_account',
            'choucheng_receivertype1_name',
            'choucheng_receivertype2_openidtype',
            'choucheng_receivertype2_account',
            'choucheng_receivertype2_accountwx',
            'choucheng_receivertype2_name',
            'buybtn_status',
            'othermoney_status',
        ],
        'update' => [
            'choucheng_receivertype',
            'choucheng_receivertype1_account',
            'choucheng_receivertype1_name',
            'choucheng_receivertype2_openidtype',
            'choucheng_receivertype2_account',
            'choucheng_receivertype2_accountwx',
            'choucheng_receivertype2_name',
            'buybtn_status',
            'othermoney_status',
        ],
    ];

}
