<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\modules\admin\models\system;

use yii\db\ActiveRecord;

/**
 * 附件模型
 */
class SystemAttachment extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    protected $table = 'sa_system_attachment';

    public function searchOriginNameAttr($query, $value)
    {
        $query->where('origin_name', 'like', '%'.$value.'%');
    }

    public function searchMimeTypeAttr($query, $value)
    {
        $query->where('mime_type', 'like', $value.'/%');
    }

}