{"packages": [{"name": "brick/math", "version": "0.13.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/brick/math/0.13.1/brick-math-0.13.1.zip", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "time": "2025-03-29T13:50:30+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.13.1"}, "install-path": "../brick/math"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/carbonphp/carbon-doctrine-types/3.2.0/carbonphp-carbon-doctrine-types-3.2.0.zip", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "time": "2024-02-09T16:56:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "install-path": "../carbonphp/carbon-doctrine-types"}, {"name": "doctrine/inflector", "version": "2.0.10", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/doctrine/inflector/2.0.10/doctrine-inflector-2.0.10.zip", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "time": "2024-02-18T20:23:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "install-path": "../doctrine/inflector"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/firebase/php-jwt/v6.11.1/firebase-php-jwt-v6.11.1.zip", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2025-04-09T20:32:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "install-path": "../firebase/php-jwt"}, {"name": "godruoyi/php-snowflake", "version": "3.2.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/godruoyi/php-snowflake/3.2.1/godruoyi-php-snowflake-3.2.1.zip", "reference": "b82b313f353e4cf3198b9e6286cd1b5e023e8626", "shasum": ""}, "require": {"php-64bit": ">=8.1"}, "require-dev": {"ext-redis": "*", "ext-swoole": "*", "illuminate/contracts": "^10.0 || ^11.0", "laravel/pint": "^1.10", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10", "predis/predis": "^2.0"}, "time": "2025-05-01T02:35:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Godruoyi\\Snowflake\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An ID Generator for PHP based on Snowflake Algorithm (Twitter announced).", "homepage": "https://github.com/godruoyi/php-snowflake", "keywords": ["Unique ID", "laravel snowflake", "order id", "php snowflake", "php sonyflake", "php unique id", "snowflake algorithm", "sonyflake", "unique order id"], "support": {"issues": "https://github.com/godruoyi/php-snowflake/issues", "source": "https://github.com/godruoyi/php-snowflake/tree/3.2.1"}, "install-path": "../godruoyi/php-snowflake"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/graham-campbell/result-type/v1.1.3/graham-campbell-result-type-v1.1.3.zip", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "time": "2024-07-20T21:45:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "install-path": "../graham-campbell/result-type"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/guzzle/7.9.3/guzzlehttp-guzzle-7.9.3.zip", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2025-03-27T13:37:11+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/promises/2.2.0/guzzlehttp-promises-2.2.0.zip", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "time": "2025-03-27T13:27:01+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/guzzlehttp/psr7/2.7.1/guzzlehttp-psr7-2.7.1.zip", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2025-03-27T12:30:47+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "install-path": "../guzzlehttp/psr7"}, {"name": "illuminate/collections", "version": "v12.21.0", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/illuminate/collections/v12.21.0/illuminate-collections-v12.21.0.zip", "reference": "a048b4fbbef4742ff2eee843971bb8278239c610", "shasum": ""}, "require": {"illuminate/conditionable": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "php": "^8.2"}, "suggest": {"illuminate/http": "Required to convert collections to API resources (^12.0).", "symfony/var-dumper": "Required to use the dump method (^7.2)."}, "time": "2025-07-15T20:29:59+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php", "helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/collections"}, {"name": "illuminate/conditionable", "version": "v12.20.0", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/illuminate/conditionable/v12.20.0/illuminate-conditionable-v12.20.0.zip", "reference": "ec677967c1f2faf90b8428919124d2184a4c9b49", "shasum": ""}, "require": {"php": "^8.2"}, "time": "2025-05-13T15:08:45+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/conditionable"}, {"name": "illuminate/contracts", "version": "v12.20.0", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/illuminate/contracts/v12.20.0/illuminate-contracts-v12.20.0.zip", "reference": "e0fe87237da006e7d26e1167b6241786d68923e8", "shasum": ""}, "require": {"php": "^8.2", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "time": "2025-07-01T15:59:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/contracts"}, {"name": "illuminate/macroable", "version": "v12.21.0", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/illuminate/macroable/v12.21.0/illuminate-macroable-v12.21.0.zip", "reference": "e862e5648ee34004fa56046b746f490dfa86c613", "shasum": ""}, "require": {"php": "^8.2"}, "time": "2024-07-23T16:31:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/macroable"}, {"name": "illuminate/redis", "version": "v12.21.0", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/illuminate/redis/v12.21.0/illuminate-redis-v12.21.0.zip", "reference": "f9bd649332a58298b63e8d254e414833ccfad15f", "shasum": ""}, "require": {"illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "illuminate/support": "^12.0", "php": "^8.2"}, "suggest": {"ext-redis": "Required to use the php<PERSON>is connector (^4.0|^5.0|^6.0).", "predis/predis": "Required to use the predis connector (^2.3|^3.0)."}, "time": "2025-07-16T13:18:38+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Redis\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Redis package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/redis"}, {"name": "illuminate/support", "version": "v12.20.0", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/illuminate/support/v12.20.0/illuminate-support-v12.20.0.zip", "reference": "d538550b5136b1ecb8be9aa73a9044a9e4971546", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "illuminate/collections": "^12.0", "illuminate/conditionable": "^12.0", "illuminate/contracts": "^12.0", "illuminate/macroable": "^12.0", "nesbot/carbon": "^3.8.4", "php": "^8.2", "voku/portable-ascii": "^2.0.2"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"spatie/once": "*"}, "suggest": {"illuminate/filesystem": "Required to use the Composer class (^12.0).", "laravel/serializable-closure": "Required to use the once function (^1.3|^2.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.7).", "league/uri": "Required to use the Uri class (^7.5.1).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the Composer class (^7.2).", "symfony/uid": "Required to use Str::ulid() (^7.2).", "symfony/var-dumper": "Required to use the dd function (^7.2).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.6.1)."}, "time": "2025-07-08T13:53:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php", "helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "install-path": "../illuminate/support"}, {"name": "monolog/monolog", "version": "2.10.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/monolog/monolog/2.10.0/monolog-monolog-2.10.0.zip", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.38 || ^9.6.19", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2024-11-12T12:43:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.10.0"}, "install-path": "../monolog/monolog"}, {"name": "nelexa/zip", "version": "4.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/nelexa/zip/4.0.2/nelexa-zip-4.0.2.zip", "reference": "88a1b6549be813278ff2dd3b6b2ac188827634a7", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.4 || ^8.0", "psr/http-message": "*", "symfony/finder": "*"}, "require-dev": {"ext-bz2": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-iconv": "*", "ext-openssl": "*", "ext-xml": "*", "friendsofphp/php-cs-fixer": "^3.4.0", "guzzlehttp/psr7": "^1.6", "phpunit/phpunit": "^9", "symfony/http-foundation": "*", "symfony/var-dumper": "*", "vimeo/psalm": "^4.6"}, "suggest": {"ext-bz2": "Needed to support BZIP2 compression", "ext-fileinfo": "Needed to get mime-type file", "ext-iconv": "Needed to support convert zip entry name to requested character encoding", "ext-openssl": "Needed to support encrypt zip entries or use ext-mcrypt"}, "time": "2022-06-17T11:17:46+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpZip\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Ne-Lexa", "email": "<EMAIL>", "role": "Developer"}], "description": "PhpZip is a php-library for extended work with ZIP-archives. Open, create, update, delete, extract and get info tool. Supports appending to existing ZIP files, WinZip AES encryption, Traditional PKWARE Encryption, BZIP2 compression, external file attributes and ZIP64 extensions. Alternative ZipArchive. It does not require php-zip extension.", "homepage": "https://github.com/Ne-Lexa/php-zip", "keywords": ["archive", "extract", "unzip", "winzip", "zip", "ziparchive"], "support": {"issues": "https://github.com/Ne-Lexa/php-zip/issues", "source": "https://github.com/Ne-Lexa/php-zip/tree/4.0.2"}, "install-path": "../nelexa/zip"}, {"name": "nesbot/carbon", "version": "3.10.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/nesbot/carbon/3.10.1/nesbot-carbon-3.10.1.zip", "reference": "1fd1935b2d90aef2f093c5e35f7ae1257c448d00", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "<100.0", "ext-json": "*", "php": "^8.1", "psr/clock": "^1.0", "symfony/clock": "^6.3.12 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1 || ^6.0 || ^7.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.75.0", "kylekatarnls/multi-tester": "^2.5.3", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.17", "phpunit/phpunit": "^10.5.46", "squizlabs/php_codesniffer": "^3.13.0"}, "time": "2025-06-21T15:19:35+00:00", "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon"}, "install-path": "../nesbot/carbon"}, {"name": "nikic/fast-route", "version": "v1.3.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/nikic/fast-route/v1.3.0/nikic-fast-route-v1.3.0.zip", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "time": "2018-02-13T20:26:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "install-path": "../nikic/fast-route"}, {"name": "openspout/openspout", "version": "v4.28.5", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/openspout/openspout/v4.28.5/openspout-openspout-v4.28.5.zip", "reference": "ab05a09fe6fce57c90338f83280648a9786ce36b", "shasum": ""}, "require": {"ext-dom": "*", "ext-fileinfo": "*", "ext-filter": "*", "ext-libxml": "*", "ext-xmlreader": "*", "ext-zip": "*", "php": "~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"ext-zlib": "*", "friendsofphp/php-cs-fixer": "^3.68.3", "infection/infection": "^0.29.10", "phpbench/phpbench": "^1.4.0", "phpstan/phpstan": "^2.1.2", "phpstan/phpstan-phpunit": "^2.0.4", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^11.5.4"}, "suggest": {"ext-iconv": "To handle non UTF-8 CSV files (if \"php-mbstring\" is not already installed or is too limited)", "ext-mbstring": "To handle non UTF-8 CSV files (if \"iconv\" is not already installed)"}, "time": "2025-01-30T13:51:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"OpenSpout\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Library to read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way", "homepage": "https://github.com/openspout/openspout", "keywords": ["OOXML", "csv", "excel", "memory", "odf", "ods", "office", "open", "php", "read", "scale", "spreadsheet", "stream", "write", "xlsx"], "support": {"issues": "https://github.com/openspout/openspout/issues", "source": "https://github.com/openspout/openspout/tree/v4.28.5"}, "install-path": "../openspout/openspout"}, {"name": "phpmailer/phpmailer", "version": "v6.10.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpmailer/phpmailer/v6.10.0/phpmailer-phpmailer-v6.10.0.zip", "reference": "bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"decomplexity/SendOauth2": "Adapter for using XOAUTH2 authentication", "ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "time": "2025-04-24T15:19:31+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.10.0"}, "install-path": "../phpmailer/phpmailer"}, {"name": "phpoption/phpoption", "version": "1.9.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpoption/phpoption/1.9.3/phpoption-phpoption-1.9.3.zip", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "time": "2024-07-20T21:41:07+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "install-path": "../phpoption/phpoption"}, {"name": "psr/cache", "version": "3.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/cache/3.0.0/psr-cache-3.0.0.zip", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-02-03T23:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "install-path": "../psr/cache"}, {"name": "psr/clock", "version": "1.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/clock/1.0.0/psr-clock-1.0.0.zip", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "time": "2022-11-25T14:36:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "install-path": "../psr/clock"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/container/2.0.2/psr-container-2.0.2.zip", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "install-path": "../psr/container"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-client/1.0.3/psr-http-client-1.0.3.zip", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.1.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/psr/http-factory/1.1.0/psr-http-factory-1.1.0.zip", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "time": "2024-04-15T12:06:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-message/2.0/psr-http-message-2.0.zip", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "3.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/log/3.0.2/psr-log-3.0.2.zip", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2024-09-11T13:17:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "3.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/simple-cache/3.0.0/psr-simple-cache-3.0.0.zip", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-10-29T13:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "install-path": "../psr/simple-cache"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/ralouphie/getallheaders/3.0.3/ralouphie-getallheaders-3.0.3.zip", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "ramsey/collection", "version": "2.1.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/ramsey/collection/2.1.1/ramsey-collection-2.1.1.zip", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "time": "2025-03-22T05:38:12+00:00", "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "installation-source": "dist", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "install-path": "../ramsey/collection"}, {"name": "ramsey/uuid", "version": "4.9.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/ramsey/uuid/4.9.0/ramsey-uuid-4.9.0.zip", "reference": "4e0e23cc785f0724a0e838279a9eb03f28b092a0", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "time": "2025-06-25T14:20:11+00:00", "type": "library", "extra": {"captainhook": {"force-install": true}}, "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.9.0"}, "install-path": "../ramsey/uuid"}, {"name": "saithink/saiadmin", "version": "5.0.6", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/saithink/saiadmin/5.0.6/saithink-saiadmin-5.0.6.zip", "reference": "5325483e2c7db9289d96b8b83b61b0ef3d94907c", "shasum": ""}, "require": {"godruoyi/php-snowflake": "^3.0.0", "guzzlehttp/guzzle": "^7.9", "openspout/openspout": "^4.0", "php": ">=8.1", "phpmailer/phpmailer": "^6.9", "ramsey/uuid": "^4.0", "tinywan/jwt": "^1.11", "tinywan/storage": "^1.1", "topthink/think-orm": "^2.0.53 || ^3.0.0", "topthink/think-validate": "^3.0", "twig/twig": "^3.20", "vlucas/phpdotenv": "^5.6", "webman/cache": "^2.1", "webman/captcha": "^1.0", "webman/event": "^1.0", "webman/push": "^1.0", "webman/redis": "^2.1", "webman/think-orm": "^2.1", "workerman/crontab": "^1.0", "zoujingli/ip2region": "^2.0"}, "time": "2025-06-20T10:52:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Webman\\saiadmin\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "saithink", "email": "<EMAIL>"}], "description": "webman plugin", "support": {"issues": "https://github.com/saithink/saiadmin/issues", "source": "https://github.com/saithink/saiadmin/tree/5.0.6"}, "install-path": "../saithink/saiadmin"}, {"name": "saithink/saipackage", "version": "1.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/saithink/saipackage/1.0.0/saithink-saipackage-1.0.0.zip", "reference": "5d61afafaaa1d7a286e6bc63c4531c7e4d85aa15", "shasum": ""}, "require": {"nelexa/zip": "4.x", "saithink/saiadmin": "^5.0.1"}, "time": "2025-07-26T14:06:52+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Saithink\\Saipackage\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "saithink", "email": "<EMAIL>"}], "description": "saiadmin plugin", "support": {"issues": "https://github.com/saithink/saipackage/issues", "source": "https://github.com/saithink/saipackage/tree/1.0.0"}, "install-path": "../saithink/saipackage"}, {"name": "symfony/cache", "version": "v7.3.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/cache/v7.3.1/symfony-cache-v7.3.1.zip", "reference": "a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e", "shasum": ""}, "require": {"php": ">=8.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^3.6", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/dependency-injection": "<6.4", "symfony/http-kernel": "<6.4", "symfony/var-dumper": "<6.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "time": "2025-06-27T19:55:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v7.3.1"}, "install-path": "../symfony/cache"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/cache-contracts/v3.6.0/symfony-cache-contracts-v3.6.0.zip", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "time": "2025-03-13T15:25:07+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "install-path": "../symfony/cache-contracts"}, {"name": "symfony/clock", "version": "v7.3.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/clock/v7.3.0/symfony-clock-v7.3.0.zip", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "time": "2024-09-25T14:21:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.3.0"}, "install-path": "../symfony/clock"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/deprecation-contracts/v3.6.0/symfony-deprecation-contracts-v3.6.0.zip", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:21:43+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/finder", "version": "v7.3.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/finder/v7.3.0/symfony-finder-v7.3.0.zip", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "time": "2024-12-30T19:00:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0"}, "install-path": "../symfony/finder"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-ctype/v1.32.0/symfony-polyfill-ctype-v1.32.0.zip", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-mbstring/v1.32.0/symfony-polyfill-mbstring-v1.32.0.zip", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-12-23T08:48:59+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-php80/v1.32.0/symfony-polyfill-php80-v1.32.0.zip", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2025-01-02T08:10:11+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-php83/v1.32.0/symfony-polyfill-php83-v1.32.0.zip", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "install-path": "../symfony/polyfill-php83"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/service-contracts/v3.6.0/symfony-service-contracts-v3.6.0.zip", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "time": "2025-04-25T09:37:31+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "install-path": "../symfony/service-contracts"}, {"name": "symfony/translation", "version": "v7.3.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/translation/v7.3.1/symfony-translation-v7.3.1.zip", "reference": "241d5ac4910d256660238a7ecf250deba4c73063", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"nikic/php-parser": "<5.0", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "time": "2025-06-27T19:55:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.3.1"}, "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/translation-contracts/v3.6.0/symfony-translation-contracts-v3.6.0.zip", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-27T08:32:26+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "install-path": "../symfony/translation-contracts"}, {"name": "symfony/var-exporter", "version": "v7.3.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/var-exporter/v7.3.0/symfony-var-exporter-v7.3.0.zip", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "time": "2025-05-15T09:04:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.3.0"}, "install-path": "../symfony/var-exporter"}, {"name": "tinywan/jwt", "version": "v1.11.3", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/tinywan/jwt/v1.11.3/tinywan-jwt-v1.11.3.zip", "reference": "1b067c998d970c252b8ad113a460922f8108b9ac", "shasum": ""}, "require": {"ext-json": "*", "firebase/php-jwt": "^6.8", "php": "^7.1||^8.0", "workerman/webman-framework": "^1.2.1||^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.6", "illuminate/database": "^8.83", "mockery/mockery": "^1.5", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.0", "topthink/think-orm": "^2.0", "vimeo/psalm": "^4.22", "workerman/webman": "^1.0"}, "time": "2025-04-10T12:05:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Tinywan\\Jwt\\": "src"}}, "license": ["MIT"], "description": "JSON Web Token (JWT) for webman plugin", "support": {"issues": "https://github.com/Tinywan/webman-jwt/issues", "source": "https://github.com/Tinywan/webman-jwt/tree/v1.11.3"}, "install-path": "../tinywan/jwt"}, {"name": "tinywan/storage", "version": "v1.1.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/tinywan/storage/v1.1.1/tinywan-storage-v1.1.1.zip", "reference": "1a78d4b585c34b2c0ea2e3be0e67952aff0e780c", "shasum": ""}, "require": {"php": ">=7.2", "workerman/webman-framework": "^1.2.1||^2.0"}, "require-dev": {"aliyuncs/oss-sdk-php": "^2.4", "friendsofphp/php-cs-fixer": "^3.6", "league/flysystem-aws-s3-v3": "^1.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5", "qcloud/cos-sdk-v5": "^2.5", "qiniu/php-sdk": "^7.4", "workerman/webman": "^1.0"}, "time": "2025-02-11T02:56:31+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Tinywan\\Storage\\": "src"}}, "license": ["MIT"], "description": "webman storage plugin", "support": {"issues": "https://github.com/Tinywan/webman-storage/issues", "source": "https://github.com/Tinywan/webman-storage/tree/v1.1.1"}, "install-path": "../tinywan/storage"}, {"name": "topthink/think-container", "version": "v3.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/topthink/think-container/v3.0.2/topthink-think-container-v3.0.2.zip", "reference": "b2df244be1e7399ad4c8be1ccc40ed57868f730a", "shasum": ""}, "require": {"php": ">=8.0", "psr/container": "^2.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2025-04-07T03:21:51+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": [], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "PHP Container & Facade Manager", "support": {"issues": "https://github.com/top-think/think-container/issues", "source": "https://github.com/top-think/think-container/tree/v3.0.2"}, "install-path": "../topthink/think-container"}, {"name": "topthink/think-helper", "version": "v3.1.11", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/topthink/think-helper/v3.1.11/topthink-think-helper-v3.1.11.zip", "reference": "1d6ada9b9f3130046bf6922fe1bd159c8d88a33c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2025-04-07T06:55:59+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.11"}, "install-path": "../topthink/think-helper"}, {"name": "topthink/think-orm", "version": "v3.0.34", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/topthink/think-orm/v3.0.34/topthink-think-orm-v3.0.34.zip", "reference": "715e55da149fe32a12d68ef10e5b00e70bd3dbec", "shasum": ""}, "require": {"ext-json": "*", "ext-pdo": "*", "php": ">=8.0.0", "psr/log": ">=1.0", "psr/simple-cache": ">=1.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.6|^10"}, "suggest": {"ext-mongodb": "provide mongodb support"}, "time": "2025-01-14T06:03:33+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["stubs/load_stubs.php"], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the PHP Database&ORM Framework", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/top-think/think-orm/issues", "source": "https://github.com/top-think/think-orm/tree/v3.0.34"}, "install-path": "../topthink/think-orm"}, {"name": "topthink/think-validate", "version": "v3.0.7", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/topthink/think-validate/v3.0.7/topthink-think-validate-v3.0.7.zip", "reference": "85063f6d4ef8ed122f17a36179dc3e0949b30988", "shasum": ""}, "require": {"php": ">=8.0", "topthink/think-container": ">=3.0"}, "time": "2025-06-11T05:51:40+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think validate", "support": {"issues": "https://github.com/top-think/think-validate/issues", "source": "https://github.com/top-think/think-validate/tree/v3.0.7"}, "install-path": "../topthink/think-validate"}, {"name": "twig/twig", "version": "v3.21.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/twig/twig/v3.21.1/twig-twig-v3.21.1.zip", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "time": "2025-05-03T07:21:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "install-path": "../twig/twig"}, {"name": "vlucas/phpdotenv", "version": "v5.6.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/vlucas/phpdotenv/v5.6.2/vlucas-phpdotenv-v5.6.2.zip", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "time": "2025-04-30T23:37:27+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.2"}, "install-path": "../vlucas/phpdotenv"}, {"name": "voku/portable-ascii", "version": "2.0.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/voku/portable-ascii/2.0.3/voku-portable-ascii-2.0.3.zip", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "time": "2024-11-21T01:49:47+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.3"}, "install-path": "../voku/portable-ascii"}, {"name": "webman/cache", "version": "v2.1.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/webman/cache/v2.1.2/webman-cache-v2.1.2.zip", "reference": "66a5461ea51d23364403b54ee218b736d26bb03f", "shasum": ""}, "require": {"php": ">=8.1", "psr/simple-cache": "^3.0", "symfony/cache": "^6.0 || ^7.0", "workerman/webman-framework": "^2.1 || dev-master"}, "time": "2025-04-11T01:23:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"support\\": "src/support", "Webman\\Cache\\": "src"}}, "license": ["MIT"], "support": {"issues": "https://github.com/webman-php/cache/issues", "source": "https://github.com/webman-php/cache/tree/v2.1.2"}, "install-path": "../webman/cache"}, {"name": "webman/captcha", "version": "v1.0.5", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/webman/captcha/v1.0.5/webman-captcha-v1.0.5.zip", "reference": "0b2645b813466e4e70bff311511364080bad2ec5", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^6.4"}, "time": "2025-03-01T08:43:36+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Webman\\Captcha\\": "src"}}, "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.gregwar.com/"}, {"name": "<PERSON>", "email": "jeremy.j.living<PERSON>@gmail.com"}], "description": "Captcha generator", "keywords": ["bot", "<PERSON><PERSON>a", "spam"], "support": {"source": "https://github.com/webman-php/captcha/tree/v1.0.5"}, "install-path": "../webman/captcha"}, {"name": "webman/event", "version": "v1.0.5", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/webman/event/v1.0.5/webman-event-v1.0.5.zip", "reference": "b1c3f6b70fd290e48288703d59bead0e28f9fb84", "shasum": ""}, "time": "2023-12-04T09:22:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Webman\\Event\\": "src"}}, "license": ["MIT"], "description": "Webman event plugin", "support": {"issues": "https://github.com/webman-php/event/issues", "source": "https://github.com/webman-php/event/tree/v1.0.5"}, "install-path": "../webman/event"}, {"name": "webman/push", "version": "v1.1.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/webman/push/v1.1.1/webman-push-v1.1.1.zip", "reference": "b9e51d39a6ae232eab6b3f5c48a918857976add8", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2025-05-15T14:32:34+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Webman\\Push\\": "src"}}, "license": ["MIT"], "support": {"issues": "https://github.com/webman-php/push/issues", "source": "https://github.com/webman-php/push/tree/v1.1.1"}, "install-path": "../webman/push"}, {"name": "webman/redis", "version": "v2.1.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/webman/redis/v2.1.3/webman-redis-v2.1.3.zip", "reference": "559eb1692d39c6fef5cf526223fff728be6c0fb9", "shasum": ""}, "require": {"illuminate/redis": "^10.0 || ^11.0 || ^12.0", "workerman/webman-framework": "^2.1 || dev-master"}, "time": "2025-03-14T03:52:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"support\\": "src/support", "Webman\\Redis\\": "src"}}, "license": ["MIT"], "description": "<PERSON>man redis", "support": {"issues": "https://github.com/webman-php/redis/issues", "source": "https://github.com/webman-php/redis/tree/v2.1.3"}, "install-path": "../webman/redis"}, {"name": "webman/think-orm", "version": "v2.1.7", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/webman/think-orm/v2.1.7/webman-think-orm-v2.1.7.zip", "reference": "9380f0fa22b7d28926c5f7b5b8f35b068f27e846", "shasum": ""}, "require": {"php": ">=8.1", "topthink/think-container": "^2.0|^3.0", "topthink/think-orm": "^2.0.53 || ^3.0.0 || ^4.0.30 || dev-master", "workerman/webman-framework": "^2.1 || dev-master"}, "time": "2025-07-11T07:26:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"support\\": "src/support", "Webman\\ThinkOrm\\": "src"}}, "license": ["MIT"], "support": {"issues": "https://github.com/webman-php/think-orm/issues", "source": "https://github.com/webman-php/think-orm/tree/v2.1.7"}, "install-path": "../webman/think-orm"}, {"name": "workerman/coroutine", "version": "v1.1.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/workerman/coroutine/v1.1.3/workerman-coroutine-v1.1.3.zip", "reference": "df8fc428967d512a74a8a7d80355c1d40228c9fa", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^11.0", "psr/log": "*"}, "time": "2025-02-17T03:34:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Workerman\\": "src", "Workerman\\Coroutine\\": "src"}}, "license": ["MIT"], "description": "Workerman coroutine", "support": {"issues": "https://github.com/workerman-php/coroutine/issues", "source": "https://github.com/workerman-php/coroutine/tree/v1.1.3"}, "install-path": "../workerman/coroutine"}, {"name": "workerman/crontab", "version": "v1.0.7", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/workerman/crontab/v1.0.7/workerman-crontab-v1.0.7.zip", "reference": "74f51ca8204e8eb628e57bc0e640561d570da2cb", "shasum": ""}, "require": {"php": ">=7.0", "workerman/workerman": ">=4.0.20"}, "time": "2025-01-15T07:20:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Workerman\\Crontab\\": "./src"}}, "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "A crontab written in PHP based on workerman", "homepage": "http://www.workerman.net", "keywords": ["crontab"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/crontab", "wiki": "http://doc.workerman.net/"}, "install-path": "../workerman/crontab"}, {"name": "workerman/webman-framework", "version": "v2.1.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/workerman/webman-framework/v2.1.2/workerman-webman-framework-v2.1.2.zip", "reference": "f803bd867f07bb0929faef060b59a19a44186bfc", "shasum": ""}, "require": {"ext-json": "*", "nikic/fast-route": "^1.3", "php": ">=8.1", "psr/container": ">=1.0", "psr/log": "^3.0", "workerman/workerman": "^5.1 || dev-master"}, "suggest": {"ext-event": "For better performance. "}, "time": "2025-03-10T11:52:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["./src/support/helpers.php"], "psr-4": {"Webman\\": "./src", "Support\\": "./src/support", "support\\": "./src/support", "Support\\View\\": "./src/support/view", "Support\\Bootstrap\\": "./src/support/bootstrap", "Support\\Exception\\": "./src/support/exception"}}, "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "description": "High performance HTTP Service Framework.", "homepage": "https://www.workerman.net", "keywords": ["High Performance", "http service"], "support": {"email": "<EMAIL>", "forum": "https://wenda.workerman.net/", "issues": "https://github.com/walkor/webman/issues", "source": "https://github.com/walkor/webman-framework", "wiki": "https://doc.workerman.net/"}, "install-path": "../workerman/webman-framework"}, {"name": "workerman/workerman", "version": "v5.1.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/workerman/workerman/v5.1.3/workerman-workerman-v5.1.3.zip", "reference": "371f3a5decb28f1bd3464ae26d47ea1a4cf0a3c5", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "workerman/coroutine": "^1.1 || dev-main"}, "conflict": {"ext-swow": "<v1.0.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.0", "mockery/mockery": "^1.6", "pestphp/pest": "2.x-dev", "phpstan/phpstan": "1.11.x-dev"}, "suggest": {"ext-event": "For better performance. "}, "time": "2025-06-12T13:34:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Workerman\\": "src"}}, "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "https://www.workerman.net", "keywords": ["asynchronous", "event-loop", "framework", "http"], "support": {"email": "<EMAIL>", "forum": "https://www.workerman.net/questions", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "https://www.workerman.net/doc/workerman/"}, "install-path": "../workerman/workerman"}, {"name": "zoujingli/ip2region", "version": "v2.0.6", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/zoujingli/ip2region/v2.0.6/zoujingli-ip2region-v2.0.6.zip", "reference": "66895178be204521e9f5ae9df0ea502893ee53b2", "shasum": ""}, "require": {"php": ">=5.4"}, "time": "2024-08-02T01:01:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["Ip2Region.php", "XdbSearcher.php"]}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://thinkadmin.top"}], "description": "Ip2Region for PHP", "homepage": "https://github.com/zoujingli/Ip2Region", "keywords": ["Ip2Region"], "support": {"issues": "https://github.com/zoujingli/ip2region/issues", "source": "https://github.com/zoujingli/ip2region/tree/v2.0.6"}, "install-path": "../zoujingli/ip2region"}], "dev": true, "dev-package-names": []}