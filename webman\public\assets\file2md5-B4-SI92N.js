import{S as w}from"./spark-md5-D8tidE2e.js";import{n as x}from"./lodash.noop-BeiKyXVG.js";const p=function(d,v={}){const{chunkSize:t=2*1024*1024,raw:m=!1,onProgress:s}=v,n=new w.ArrayBuffer,e=new FileReader,a=d.size,l=Math.ceil(a/t);let o=0,u=0;const f=function(){const r=o*t,i=r+t>=a?a:r+t;e.readAsArrayBuffer(File.prototype.slice.call(d,r,i))},b=function(){e.abort()},k=function(r,i){e.addEventListener("load",h=>{var c;if(n.append((c=h.target)===null||c===void 0?void 0:c.result),o++,u=+(o/l).toFixed(2),s==null||s(u),o<l){f();return}r(n.end(m))}),e.addEventListener("abort",()=>{n.reset(),r("")}),e.addEventListener("error",()=>{n.reset(),i(e.error)}),f()};return p.abort=b,new Promise(k)};p.abort=x;export{p as f};
