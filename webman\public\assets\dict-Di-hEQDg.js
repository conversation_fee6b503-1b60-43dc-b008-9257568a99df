import{g as e}from"./index-DkGLNqVb.js";const a={getPageList(t={}){return e({url:"/core/dictType/index",method:"get",params:t})},save(t={}){return e({url:"/core/dictType/save",method:"post",data:t})},destroy(t){return e({url:"/core/dictType/destroy",method:"delete",data:t})},update(t,r={}){return e({url:"/core/dictType/update?id="+t,method:"put",data:r})},changeStatus(t={}){return e({url:"/core/dictType/changeStatus",method:"post",data:t})}},o={getDict(t){return e({url:"/core/dataDict/index?code="+t,method:"get"})},getPageList(t={}){return e({url:"/core/dictData/index",method:"get",params:t})},addDictData(t={}){return e({url:"/core/dictData/save",method:"post",data:t})},destroyDictData(t){return e({url:"/core/dictData/destroy",method:"delete",data:t})},editDictData(t,r={}){return e({url:"/core/dictData/update?id="+t,method:"put",data:r})},changeStatus(t={}){return e({url:"/core/dictData/changeStatus",method:"post",data:t})}};export{o as a,a as d};
