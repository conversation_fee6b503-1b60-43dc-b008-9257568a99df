import{_ as v,a as h}from"./index-ybrmzYq5.js";import{d as n}from"./dayjs-DUkVwsK-.js";import{a as x}from"./avatar-DvSZjoFF.js";import{r as s,o as b,h as l,n as k,k as g,m as t,t as c,l as d,a1 as o,z as e,y as _}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const y={class:"flex justify-between"},w={class:"ma-content-block rounded-sm flex justify-between w-full p-3"},S={class:"pl-0 flex inline-block"},Y=["src"],j={class:"pl-3 mt-2"},B={class:"content-block-title"},D={class:"leading-5 mt-2"},M={class:"datetime ml-5 hidden md:block"},N={class:"text-3xl text-center"},V={class:"text-base"},z={__name:"st-welcome",setup(C){const r=h();s(!1);const m=s(null),a=s(null);b(()=>{p(),setInterval(()=>p(),1e3)});const p=()=>{m.value=n().format("HH:mm:ss"),a.value=n().format("YYYY年MM月DD日")};return(H,i)=>{const u=l("a-avatar"),f=l("a-tag");return g(),k("div",y,[t("div",w,[t("div",S,[c(u,{size:75,class:"hidden lg:inline-block"},{default:d(()=>[t("img",{src:o(r).user&&o(r).user.avatar?o(r).user.avatar:o(x)},null,8,Y)]),_:1}),t("div",j,[t("div",B,e(o(r).user.nickname||o(r).user.username)+"，欢迎回来！",1),t("div",D,[c(f,{class:"tag-primary"},{default:d(()=>i[0]||(i[0]=[_("免费开源、可商用")])),_:1}),i[1]||(i[1]=_(" 欢迎使用SaiAdmin后台权限管理系统。喜欢的请去点个 ⭐Star，谢谢！ "))])])]),t("div",M,[t("h2",N,e(m.value),1),t("p",V,e(a.value),1)])])])}}},Ut=v(z,[["__scopeId","data-v-eecab710"]]);export{Ut as default};
