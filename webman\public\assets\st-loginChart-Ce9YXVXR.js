import"./echarts-Cz-L25MO.js";import{_ as u,c as d}from"./index-ybrmzYq5.js";import{V as l}from"./zrender-xbpiMqDc.js";import{r as i,h as n,n as h,k as g,t as m,l as _}from"./@vue-9ZIPiVZG.js";import"./tslib-BDyQ-Jie.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const y={class:"ma-content-block p-3 mt-3"},x={__name:"st-loginChart",setup(b){function a(r){return{type:"text",bottom:"8",...r,style:{text:"",textAlign:"center",fill:"#4E5969",fontSize:12}}}const e=i([]),p=i([]),c=i([a({left:"2.6%"}),a({right:0})]),s=i({});return(async()=>{const r=await d.loginChart();e.value=r.data.login_date,p.value=r.data.login_count,s.value={grid:{left:"2.6%",right:"0",top:"10",bottom:"30"},xAxis:{type:"category",offset:2,data:e.value,boundaryGap:!1,axisLabel:{color:"#4E5969",formatter(t,o){return o===0||o===e.value.length-1?"":`${t}`}},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!0,interval:t=>!(t===0||t===e.value.length-1),lineStyle:{color:"#E5E8EF"}},axisPointer:{show:!0,lineStyle:{color:"#23ADFF",width:2}}},yAxis:{type:"value",axisLine:{show:!1},axisLabel:{formatter(t,o){return o===0?t:`${t}`}},splitLine:{show:!0,lineStyle:{type:"dashed",color:"#E5E8EF"}}},tooltip:{trigger:"axis",formatter(t){return`<div class="login-chart">
          <p class="tooltip-title">${t[0].axisValueLabel}</p>
          <div class="content-panel"><span>登录次数</span><span class="tooltip-value">${Number(t[0].value).toLocaleString()}</span></div>
        </div>`}},graphic:{elements:c.value},series:[{data:p.value,type:"line",smooth:!0,symbolSize:12,emphasis:{focus:"series",itemStyle:{borderWidth:2}},lineStyle:{width:3,color:new l(0,0,1,0,[{offset:0,color:"rgba(30, 231, 255, 1)"},{offset:.5,color:"rgba(36, 154, 255, 1)"},{offset:1,color:"rgba(111, 66, 251, 1)"}])},showSymbol:!1,areaStyle:{opacity:.8,color:new l(0,0,0,1,[{offset:0,color:"rgba(17, 126, 255, 0.16)"},{offset:1,color:"rgba(17, 128, 255, 0)"}])}}]}})(),(r,t)=>{const o=n("sa-chart"),f=n("a-card");return g(),h("div",y,[m(f,{bordered:!1,class:"general-card","header-style":{paddingTop:"10px",paddingBottom:0},"body-style":{paddingTop:"20px"},title:"登录统计"},{default:_(()=>[m(o,{height:"300px",option:s.value},null,8,["option"])]),_:1})])}}},Lt=u(x,[["__scopeId","data-v-b61e8c50"]]);export{Lt as default};
