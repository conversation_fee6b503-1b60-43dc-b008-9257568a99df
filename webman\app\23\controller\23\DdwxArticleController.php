<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\23\controller\23;

use plugin\saiadmin\basic\BaseController;
use app\23\logic\23\DdwxArticleLogic;
use app\23\validate\23\DdwxArticleValidate;
use support\Request;
use support\Response;

/**
 * 23控制器
 */
class DdwxArticleController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new DdwxArticleLogic();
        $this->validate = new DdwxArticleValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['name', ''],
            ['subname', ''],
            ['showname', ''],
            ['showsubname', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

}
