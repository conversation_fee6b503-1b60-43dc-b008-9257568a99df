import{t as $}from"./index-DkGLNqVb.js";import{a as d}from"./database-CTMAb1z4.js";import{M as m}from"./@arco-design-uttiljWv.js";import{r as y,a as z,h as p,ba as J,j as v,k as f,l as o,t as r,M as h,y as u,z as S,a1 as P}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const He={__name:"recycle",setup(T,{expose:x}){const w=y(!1),s=y(),i=y([]),l=y({table:""}),C=t=>i.value=t,O=z({api:d.getRecycle,rowSelection:{showCheckedAll:!0,key:"id"},pageSimple:!0,showSearch:!1}),A=async()=>{var t;(t=s.value)==null||t.refresh()},D=async t=>{l.value.table=t,w.value=!0,await A()},R=async()=>{var t,e;if(console.log(i.value),i.value&&i.value.length>0){const n={table:l.value.table,ids:i.value};(await d.delete(n)).code===200&&(m.success("销毁成功！"),(t=s.value)==null||t.clearSelected(),(e=s.value)==null||e.refresh())}else m.error("至少选择一条数据")},j=async()=>{var t,e;if(i.value&&i.value.length>0){const n={table:l.value.table,ids:i.value};(await d.recovery(n)).code===200&&(m.success("恢复成功！"),i.value=[],(t=s.value)==null||t.clearSelected(),(e=s.value)==null||e.refresh())}else m.error("至少选择一条数据")},F=async t=>{var a;const e={table:l.value.table,ids:[t.id]};(await d.delete(e)).code===200&&(m.success("销毁成功！"),(a=s.value)==null||a.refresh())},M=async t=>{var a;const e={table:l.value.table,ids:[t.id]};(await d.recovery(e)).code===200&&(m.success("恢复成功！"),(a=s.value)==null||a.refresh())};return x({open:D}),(t,e)=>{const n=p("icon-delete"),a=p("a-button"),_=p("a-popconfirm"),k=p("icon-undo"),N=p("a-space"),g=p("a-link"),B=p("sa-table"),I=p("a-drawer"),b=J("auth");return f(),v(I,{visible:w.value,"onUpdate:visible":e[0]||(e[0]=c=>w.value=c),width:P($).getDevice()==="mobile"?"100%":"70%",footer:!1},{title:o(()=>[u("回收站数据 【"+S(l.value.table)+"】",1)]),default:o(()=>[r(N,{class:"pl-4"},{default:o(()=>[r(_,{content:"确定要批量销毁数据吗?",position:"bottom",onOk:R},{default:o(()=>[h((f(),v(a,{type:"primary",status:"danger"},{icon:o(()=>[r(n)]),default:o(()=>[e[1]||(e[1]=u(" 批量销毁 "))]),_:1})),[[b,["/core/database/delete"]]])]),_:1}),r(_,{content:"确定要批量恢复数据吗?",position:"bottom",onOk:j},{default:o(()=>[h((f(),v(a,{type:"primary",status:"success"},{icon:o(()=>[r(k)]),default:o(()=>[e[2]||(e[2]=u(" 批量恢复 "))]),_:1})),[[b,["/core/database/recovery"]]])]),_:1})]),_:1}),r(B,{ref_key:"tableRef",ref:s,options:O,searchForm:l.value,onSelectionChange:C,columns:[{title:"删除时间",dataIndex:"delete_time",width:180},{title:"数据详情",dataIndex:"json_data",width:300}]},{json_data:o(({record:c})=>[u(S(JSON.stringify(c)),1)]),operationCell:o(({record:c})=>[r(_,{content:"确定要销毁该数据吗?",position:"bottom",onOk:V=>F(c)},{default:o(()=>[h((f(),v(g,{type:"primary"},{default:o(()=>[r(n),e[3]||(e[3]=u(" 销毁 "))]),_:1})),[[b,["/core/database/delete"]]])]),_:2},1032,["onOk"]),r(_,{content:"确定要恢复该数据吗?",position:"bottom",onOk:V=>M(c)},{default:o(()=>[h((f(),v(g,{type:"primary"},{default:o(()=>[r(k),e[4]||(e[4]=u(" 恢复 "))]),_:1})),[[b,["/core/database/recovery"]]])]),_:2},1032,["onOk"])]),_:1},8,["options","searchForm"])]),_:1},8,["visible","width"])}}};export{He as default};
