@echo off
chcp 65001 >nul
echo ========================================
echo    SaiAdmin 自动化部署和监控系统
echo ========================================
echo.

set DEPLOY_TIME=%date:~0,4%-%date:~5,2%-%date:~8,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set BACKUP_DIR=backup\%DEPLOY_TIME%
set LOG_FILE=deploy_%DEPLOY_TIME%.log

echo [1/10] 创建备份目录...
if not exist backup mkdir backup
if not exist %BACKUP_DIR% mkdir %BACKUP_DIR%
echo ✅ 备份目录创建完成: %BACKUP_DIR%

echo.
echo [2/10] 备份当前版本...
echo 正在备份数据库...
"D:\BtSoft\mysql\MySQL5.7\bin\mysqldump.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin > %BACKUP_DIR%\database_backup.sql
if %errorlevel% == 0 (
    echo ✅ 数据库备份完成
) else (
    echo ❌ 数据库备份失败
    goto :error
)

echo 正在备份配置文件...
copy webman\.env %BACKUP_DIR%\.env.backup >nul
copy webman\config\*.php %BACKUP_DIR%\ >nul 2>&1
echo ✅ 配置文件备份完成

echo.
echo [3/10] 停止现有服务...
taskkill /f /im php.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
timeout /t 3 >nul
echo ✅ 服务已停止

echo.
echo [4/10] 执行数据库优化...
echo 正在应用数据库索引优化...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin < database-optimization.sql
if %errorlevel% == 0 (
    echo ✅ 数据库优化完成
) else (
    echo ⚠️ 数据库优化部分失败，继续部署...
)

echo.
echo [5/10] 清理缓存和临时文件...
if exist "webman\runtime\cache" (
    rmdir /s /q "webman\runtime\cache" >nul 2>&1
    echo ✅ 后端缓存已清理
)

if exist "saiadmin-vue\node_modules\.cache" (
    rmdir /s /q "saiadmin-vue\node_modules\.cache" >nul 2>&1
    echo ✅ 前端缓存已清理
)

if exist "saiadmin-vue\dist" (
    rmdir /s /q "saiadmin-vue\dist" >nul 2>&1
    echo ✅ 前端构建目录已清理
)

echo.
echo [6/10] 安装/更新依赖...
echo 正在检查后端依赖...
cd webman
composer install --no-dev --optimize-autoloader >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 后端依赖更新完成
) else (
    echo ⚠️ 后端依赖更新失败，使用现有依赖
)
cd ..

echo 正在检查前端依赖...
cd saiadmin-vue
npm ci --silent >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 前端依赖更新完成
) else (
    echo ⚠️ 前端依赖更新失败，使用现有依赖
)
cd ..

echo.
echo [7/10] 构建前端资源...
cd saiadmin-vue
echo 正在构建生产版本...
npm run build >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 前端构建完成
) else (
    echo ❌ 前端构建失败
    cd ..
    goto :error
)
cd ..

echo.
echo [8/10] 启动优化后的服务...
echo 正在启动后端服务...
cd webman
start "SaiAdmin Backend Optimized" cmd /k "php windows.php start"
cd ..
timeout /t 5 >nul

echo 正在启动前端服务...
cd saiadmin-vue
start "SaiAdmin Frontend Optimized" cmd /k "npm run preview"
cd ..
timeout /t 5 >nul

echo.
echo [9/10] 健康检查...
timeout /t 10 >nul

echo 检查后端服务...
curl -s -o nul -w "后端状态: %%{http_code}" http://localhost:8787/ 2>nul
echo.

echo 检查前端服务...
curl -s -o nul -w "前端状态: %%{http_code}" http://localhost:4173/ 2>nul
echo.

echo.
echo [10/10] 启动监控系统...
echo 正在启动性能监控...
start "Performance Monitor" cmd /k "php performance-monitor.php"

echo 正在启动日志监控...
start "Log Monitor" cmd /k "php log-monitor.php"

echo.
echo ========================================
echo 🎉 部署完成！监控系统已启动
echo ========================================
echo.
echo 📊 部署信息:
echo   部署时间: %DEPLOY_TIME%
echo   备份位置: %BACKUP_DIR%
echo   日志文件: %LOG_FILE%
echo.
echo 🌐 访问地址:
echo   生产前端: http://localhost:4173/
echo   开发前端: http://localhost:8889/
echo   后端API: http://localhost:8787/
echo.
echo 📈 监控面板:
echo   性能监控: 已启动
echo   日志监控: 已启动
echo   数据库监控: 已启动
echo.
echo 🔧 优化功能:
echo   ✅ 数据库连接池优化
echo   ✅ Redis缓存优化
echo   ✅ API性能监控
echo   ✅ 安全中间件
echo   ✅ 高级日志系统
echo   ✅ 前端性能优化
echo.
echo 📋 下一步建议:
echo   1. 访问前端页面测试功能
echo   2. 查看性能监控数据
echo   3. 检查日志输出
echo   4. 配置生产环境域名
echo.
echo ========================================
echo 部署成功！按任意键退出...
echo ========================================
pause >nul
goto :end

:error
echo.
echo ❌ 部署失败！正在回滚...
echo 正在恢复数据库...
if exist %BACKUP_DIR%\database_backup.sql (
    "D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin < %BACKUP_DIR%\database_backup.sql
    echo ✅ 数据库已回滚
)

echo 正在恢复配置文件...
if exist %BACKUP_DIR%\.env.backup (
    copy %BACKUP_DIR%\.env.backup webman\.env >nul
    echo ✅ 配置文件已回滚
)

echo.
echo ❌ 部署失败，系统已回滚到之前状态
echo 请检查错误信息并重试
pause >nul

:end
