import{a as v}from"./dept-BazEhZT0.js";import{M as F}from"./@arco-design-uttiljWv.js";import{r as n,a as x,h as s,j as M,k as N,l as o,t,y as V}from"./@vue-9ZIPiVZG.js";import"./index-DkGLNqVb.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const qe={__name:"leader",emits:["success"],setup(R,{expose:b,emit:k}){const w=k,c=n(!1),p=n(),u=n(),d=n([]),l=n({username:"",nickname:"",dept_id:null,status:""}),y=r=>{var e;u.value=r.id,c.value=!0,l.value.dept_id=u.value,(e=p.value)==null||e.refresh()},h=async()=>{var i;const r=d.value.map(m=>({user_id:m.id,username:m.username}));(await v.addLeader({id:u.value,users:r})).code===200&&(d.value=[],(i=p.value)==null||i.refresh())},I=async()=>{w("success",!0)},C=x({api:v.getLeaderList,rowSelection:{showCheckedAll:!0},singleLine:!0,delete:{show:!0,auth:["/core/dept/destroy"],func:async r=>{var i;r.id=u.value,(await v.delLeader(r)).code===200&&(F.success("删除成功！"),(i=p.value)==null||i.refresh())}}}),L=x([{title:"用户名",dataIndex:"username"},{title:"用户昵称",dataIndex:"nickname"},{title:"手机",dataIndex:"phone"},{title:"邮箱",dataIndex:"email"},{title:"状态",dataIndex:"status",width:100,type:"dict",dict:"data_status"}]);return b({open:y}),(r,e)=>{const i=s("a-alert"),m=s("a-input"),f=s("a-form-item"),_=s("a-col"),U=s("sa-select"),B=s("sa-user"),S=s("sa-table"),g=s("a-modal");return N(),M(g,{visible:c.value,"onUpdate:visible":e[4]||(e[4]=a=>c.value=a),fullscreen:"",footer:!1,onClose:I},{title:o(()=>e[5]||(e[5]=[V("部门领导列表")])),default:o(()=>[t(i,null,{default:o(()=>e[6]||(e[6]=[V("部门的领导人可以跨部门设置")])),_:1}),t(S,{ref_key:"crudRef",ref:p,options:C,columns:L,searchForm:l.value},{tableSearch:o(()=>[t(_,{sm:8,xs:24},{default:o(()=>[t(f,{field:"username",label:"用户名"},{default:o(()=>[t(m,{modelValue:l.value.username,"onUpdate:modelValue":e[0]||(e[0]=a=>l.value.username=a),placeholder:"请输入用户名","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),t(_,{sm:8,xs:24},{default:o(()=>[t(f,{field:"nickname",label:"用户昵称"},{default:o(()=>[t(m,{modelValue:l.value.nickname,"onUpdate:modelValue":e[1]||(e[1]=a=>l.value.nickname=a),placeholder:"请输入用户昵称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),t(_,{sm:8,xs:24},{default:o(()=>[t(f,{field:"status",label:"状态"},{default:o(()=>[t(U,{modelValue:l.value.status,"onUpdate:modelValue":e[2]||(e[2]=a=>l.value.status=a),dict:"data_status",placeholder:"请选择状态","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),tableBeforeButtons:o(()=>[t(B,{text:"新增领导",onlyId:!1,isEcho:!1,modelValue:d.value,"onUpdate:modelValue":e[3]||(e[3]=a=>d.value=a),onSuccess:h},null,8,["modelValue"])]),_:1},8,["options","columns","searchForm"])]),_:1},8,["visible"])}}};export{qe as default};
