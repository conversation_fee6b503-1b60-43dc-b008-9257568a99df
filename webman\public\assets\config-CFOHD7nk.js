import{g as o}from"./index-DkGLNqVb.js";const u={getConfigList(e){return o({url:"/core/config/index",method:"get",params:e})},destroy(e){return o({url:"/core/config/destroy",method:"delete",data:e})},save(e={}){return o({url:"/core/config/save",method:"post",data:e})},update(e,r={}){return o({url:"/core/config/update?id="+e,method:"put",data:r})},updateByKeys(e){return o({url:"/core/config/updateByKeys",method:"post",data:e})},batchUpdate(e){return o({url:"/core/config/batchUpdate",method:"post",data:e})},getConfigGroupList(e={}){return o({url:"/core/configGroup/index",method:"get",params:e})},saveConfigGroup(e={}){return o({url:"/core/configGroup/save",method:"post",data:e})},updateConfigGroup(e,r={}){return o({url:"/core/configGroup/update?id="+e,method:"put",data:r})},deleteConfigGroup(e={}){return o({url:"/core/configGroup/destroy",method:"delete",data:e})},testEmail(e={}){return o({url:"/core/configGroup/email",method:"post",data:e})}};export{u as c};
