import{r as _,h as d,j as C,k as b,l as t,t as e,n as k,p as H,y as m,F as h,P as D,m as w,z as U,M as be,N as ye}from"./@vue-9ZIPiVZG.js";import{a as M}from"./generate-CvHPqnHt.js";import{a as Ve}from"./database-C9oinknn.js";import{m as xe}from"./menu-CgiEA4rB.js";import{d as ge}from"./dict-C6FxRZf9.js";import we from"./settingComponent-C9EG_z5A.js";import{_ as Ue}from"./index-ybrmzYq5.js";import{M as E}from"./@arco-design-uttiljWv.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const ke=[{name:"一对一",value:"hasOne"},{name:"一对多",value:"hasMany"},{name:"一对一（反向)",value:"belongsTo"},{name:"多对多",value:"belongsToMany"}],qe=[{label:"=",value:"eq"},{label:"!=",value:"neq"},{label:">",value:"gt"},{label:">=",value:"gte"},{label:"<",value:"lt"},{label:"<=",value:"lte"},{label:"LIKE",value:"like"},{label:"IN",value:"in"},{label:"NOT IN",value:"notin"},{label:"BETWEEN",value:"between"}],Ce=[{label:"输入框",value:"input"},{label:"密码框",value:"password"},{label:"文本域",value:"textarea"},{label:"数字输入框",value:"inputNumber"},{label:"标签输入框",value:"inputTag"},{label:"开关",value:"switch"},{label:"滑块",value:"slider"},{label:"数据下拉框",value:"select"},{label:"字典下拉框",value:"saSelect"},{label:"树形下拉框",value:"treeSelect"},{label:"单选框",value:"radio"},{label:"复选框",value:"checkbox"},{label:"日期选择器",value:"date"},{label:"时间选择器",value:"time"},{label:"评分器",value:"rate"},{label:"级联选择器",value:"cascader"},{label:"用户选择器",value:"userSelect"},{label:"省市区联动",value:"cityLinkage"},{label:"图片上传",value:"uploadImage"},{label:"文件上传",value:"uploadFile"},{label:"富文本控件",value:"wangEditor"}],Te={key:0},Ie={class:"flex justify-between w-full"},he={class:"flex justify-between w-full"},De={class:"flex justify-between w-full"},Se={name:"setting:code:update"},Ee=Object.assign(Se,{emits:["success"],setup(Ne,{expose:J,emit:Q}){const v=_({}),j=_(!0),N=_(!1),B=_("base_config"),F=_(),X=_(["uploadFile","uploadImage","editor","codeEditor","wangEditor","cityLinkage","date","userInfo"]),o=_({generate_menus:["save","update","read","delete","recycle","recovery","realDestroy","changeStatus"],columns:[]}),O=_(),Y=Q,f=_({relations:[]}),K=_([]);_([]);const $=_([]);_([]),_([]),_([]);const A=_([]),Z=async u=>{N.value=!0;const a=await Ve.getDataSource();A.value=a.data.map(r=>({label:r,value:r}));const c=await M.readTable(u);v.value=c.data,ue(),j.value=!1},ee=(u,a)=>{o.value.columns.find((c,r)=>{c.column_name==u&&(o.value.columns[r].options=a)}),E.success("组件设置成功")},le=u=>{u.view_type=="uploadImage"||u.view_type=="uploadFile"?u.options={multiple:!1}:u.view_type=="codeEditor"||u.view_type=="editor"||u.view_type=="wangEditor"?u.options={height:400}:u.view_type=="date"?u.options={mode:"date",showTime:!1}:u.view_type=="cityLinkage"?u.options={type:"cascader",mode:"code"}:u.options={}},ae=async u=>{if(o.value.namespace==="saiadmin")return E.error("应用名称不能为saiadmin"),!1;const a=await O.value.validate();if(a){for(let r in a)E.error(a[r].message);return!1}o.value.options=f.value;const c=await M.update(o.value.id,o.value);if(c.code==200)E.success(c.message),Y("success",!0),u(!0);else return!1},T=(u,a)=>o.value.columns.map(c=>{c["is_"+a]=u}),te=()=>{f.value.relations.push({name:"",type:"hasOne",model:"",foreignKey:"",localKey:"",table:""})},oe=u=>f.value.relations.splice(u,1),ue=()=>{for(let u in v.value)u==="generate_menus"?o.value[u]=v.value[u]?v.value[u].split(","):[]:o.value[u]=v.value[u];v.value.options&&v.value.options.relations?f.value.relations=v.value.options.relations:f.value.relations=[],v.value.tpl_category==="tree"&&(f.value.tree_id=v.value.options.tree_id,f.value.tree_name=v.value.options.tree_name,f.value.tree_parent_id=v.value.options.tree_parent_id),M.getTableColumns({table_id:v.value.id}).then(u=>{o.value.columns=[],u.data.map(a=>{a.is_required=a.is_required===2,a.is_insert=a.is_insert===2,a.is_edit=a.is_edit===2,a.is_list=a.is_list===2,a.is_query=a.is_query===2,a.is_sort=a.is_sort===2,o.value.columns.push(a)})}),xe.getList({tree:!0}).then(u=>{K.value=u.data,K.value.unshift({id:0,value:0,label:"顶级菜单"})}),ge.getPageList({saiType:"all"}).then(u=>$.value=u.data)};return J({open:Z}),(u,a)=>{const c=d("a-divider"),r=d("a-input"),s=d("a-form-item"),i=d("a-col"),V=d("a-row"),x=d("a-select"),ne=d("a-textarea"),q=d("a-radio"),R=d("a-radio-group"),de=d("a-cascader"),P=d("a-input-number"),S=d("a-option"),L=d("a-tab-pane"),z=d("a-tag"),W=d("a-alert"),y=d("a-table-column"),g=d("a-checkbox"),I=d("a-tooltip"),G=d("a-link"),se=d("a-space"),ie=d("a-table"),me=d("icon-plus"),pe=d("a-button"),re=d("icon-delete"),_e=d("a-tabs"),fe=d("a-form"),ve=d("a-spin"),ce=d("a-modal");return b(),C(ce,{visible:N.value,"onUpdate:visible":a[26]||(a[26]=l=>N.value=l),"on-before-ok":ae,fullscreen:"","unmount-on-close":""},{title:t(()=>{var l;return[m("编辑生成信息 - "+U((l=v.value)==null?void 0:l.table_comment),1)]}),default:t(()=>[e(ve,{loading:j.value,tip:"加载数据中...",class:"w-full"},{default:t(()=>[e(fe,{model:o.value,ref_key:"formRef",ref:O},{default:t(()=>[e(_e,{"active-key":B.value,"onUpdate:activeKey":a[25]||(a[25]=l=>B.value=l)},{default:t(()=>[e(L,{title:"配置信息",key:"base_config"},{default:t(()=>[e(c,{orientation:"left"},{default:t(()=>a[27]||(a[27]=[m("基础信息")])),_:1}),e(V,{gutter:24},{default:t(()=>[e(i,{span:8},{default:t(()=>[e(s,{label:"表名称",field:"table_name","label-col-flex":"auto","label-col-style":{width:"100px"}},{default:t(()=>[e(r,{modelValue:o.value.table_name,"onUpdate:modelValue":a[0]||(a[0]=l=>o.value.table_name=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(s,{label:"表描述",field:"table_comment","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"表描述必填"}]},{default:t(()=>[e(r,{modelValue:o.value.table_comment,"onUpdate:modelValue":a[1]||(a[1]=l=>o.value.table_comment=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(s,{label:"实体类",field:"class_name","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"实体类必填"}]},{default:t(()=>[e(r,{modelValue:o.value.class_name,"onUpdate:modelValue":a[2]||(a[2]=l=>o.value.class_name=l)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:24},{default:t(()=>[e(i,{span:8},{default:t(()=>[e(s,{label:"业务名称",field:"business_name","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"实体别名必填"}]},{default:t(()=>[e(r,{modelValue:o.value.business_name,"onUpdate:modelValue":a[3]||(a[3]=l=>o.value.business_name=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(s,{label:"数据源",field:"source","label-col-flex":"auto","label-col-style":{width:"94px"}},{default:t(()=>[e(x,{placeholder:"请选择数据源",modelValue:o.value.source,"onUpdate:modelValue":a[4]||(a[4]=l=>o.value.source=l),options:A.value},null,8,["modelValue","options"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(s,{label:"备注信息",field:"remark","label-col-flex":"auto","label-col-style":{width:"94px"}},{default:t(()=>[e(ne,{modelValue:o.value.remark,"onUpdate:modelValue":a[5]||(a[5]=l=>o.value.remark=l)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(c,{orientation:"left"},{default:t(()=>a[28]||(a[28]=[m("生成信息")])),_:1}),e(V,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"应用类型",field:"template","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"应用类型必选"}],extra:"默认app模板,生成文件放app目录下"},{default:t(()=>[e(x,{style:{width:"100%"},modelValue:o.value.template,"onUpdate:modelValue":a[6]||(a[6]=l=>o.value.template=l),options:[{label:"webman应用[app]",value:"app"},{label:"webman插件[plugin]",value:"plugin"}],"allow-clear":"","allow-search":"",placeholder:"请选择生成模板"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"应用名称",field:"namespace","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"应用名称必填"}],extra:"plugin插件名称, 或者app下应用名称, 禁止使用saiadmin"},{default:t(()=>[e(r,{modelValue:o.value.namespace,"onUpdate:modelValue":a[7]||(a[7]=l=>o.value.namespace=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"包名",field:"package_name","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定控制器文件所在控制器目录的二级目录名，如：system"},{default:t(()=>[e(r,{"allow-clear":"",modelValue:o.value.package_name,"onUpdate:modelValue":a[8]||(a[8]=l=>o.value.package_name=l),placeholder:"请输入包名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"生成类型",field:"tpl_category","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"生成类型必填"}],extra:"单表须有主键，树表须指定id、parent_id、name等字段"},{default:t(()=>[e(x,{style:{width:"100%"},modelValue:o.value.tpl_category,"onUpdate:modelValue":a[9]||(a[9]=l=>o.value.tpl_category=l),options:[{label:"单表CRUD",value:"single"},{label:"树表CRUD",value:"tree"}],"allow-clear":"","allow-search":"",placeholder:"请选择所属模块"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"生成路径",field:"generate_path","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"生成路径必填"}],extra:"前端根目录文件夹名称，必须与后端根目录同级"},{default:t(()=>[e(r,{modelValue:o.value.generate_path,"onUpdate:modelValue":a[10]||(a[10]=l=>o.value.generate_path=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"模型类型",field:"generate_model","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"根据不同选择生成不同的模型"},{default:t(()=>[e(R,{modelValue:o.value.generate_model,"onUpdate:modelValue":a[11]||(a[11]=l=>o.value.generate_model=l)},{default:t(()=>[e(q,{value:1},{default:t(()=>a[29]||(a[29]=[m("软删除")])),_:1}),e(q,{value:2},{default:t(()=>a[30]||(a[30]=[m("非软删除")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"所属菜单",field:"belong_menu_id","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"默认为工具菜单栏目下的子菜单。不选择则为顶级菜单栏目"},{default:t(()=>[e(de,{modelValue:o.value.belong_menu_id,"onUpdate:modelValue":a[12]||(a[12]=l=>o.value.belong_menu_id=l),options:K.value,"expand-trigger":"hover",style:{width:"100%"},placeholder:"生成功能所属菜单","allow-search":"","allow-clear":"","check-strictly":""},null,8,["modelValue","options"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"菜单名称",field:"menu_name","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"菜单名称必选"}],extra:"显示在菜单栏目上的菜单名称、以及代码中的业务功能名称"},{default:t(()=>[e(r,{"allow-clear":"",modelValue:o.value.menu_name,"onUpdate:modelValue":a[13]||(a[13]=l=>o.value.menu_name=l),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:24},{default:t(()=>[e(i,{span:8},{default:t(()=>[e(s,{label:"表单样式",field:"component_type","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"设置新增和修改组件显示方式"},{default:t(()=>[e(R,{modelValue:o.value.component_type,"onUpdate:modelValue":a[14]||(a[14]=l=>o.value.component_type=l),type:"button"},{default:t(()=>[e(q,{value:1},{default:t(()=>a[31]||(a[31]=[m("模态框")])),_:1}),e(q,{value:2},{default:t(()=>a[32]||(a[32]=[m("抽屉")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(s,{label:"表单宽度",field:"form_width","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"表单组件的宽度，单位为px"},{default:t(()=>[e(P,{modelValue:o.value.form_width,"onUpdate:modelValue":a[15]||(a[15]=l=>o.value.form_width=l),min:200,max:1e4},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(s,{label:"表单全屏",field:"is_full","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"编辑表单是否全屏"},{default:t(()=>[e(R,{modelValue:o.value.is_full,"onUpdate:modelValue":a[16]||(a[16]=l=>o.value.is_full=l)},{default:t(()=>[e(q,{value:1},{default:t(()=>a[33]||(a[33]=[m("否")])),_:1}),e(q,{value:2},{default:t(()=>a[34]||(a[34]=[m("是")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o.value.tpl_category==="tree"?(b(),k("div",Te,[e(c,{orientation:"left"},{default:t(()=>a[35]||(a[35]=[m("树表配置")])),_:1}),e(V,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"树主ID",field:"tree_id","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定树表的主要ID，一般为主键"},{default:t(()=>[e(x,{style:{width:"100%"},modelValue:f.value.tree_id,"onUpdate:modelValue":a[17]||(a[17]=l=>f.value.tree_id=l),"allow-clear":"","allow-search":"",placeholder:"请选择树表的主ID"},{default:t(()=>[(b(!0),k(h,null,D(o.value.columns,(l,n)=>(b(),C(S,{class:"w-full",label:l.column_name+" - "+l.column_comment,value:l.column_name,key:n},{default:t(()=>[w("div",Ie,[w("span",null,U(l.column_name),1),w("span",null,U(l.column_comment),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"树父ID",field:"tree_parent_id","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定树表的父ID，比如：parent_id"},{default:t(()=>[e(x,{style:{width:"100%"},modelValue:f.value.tree_parent_id,"onUpdate:modelValue":a[18]||(a[18]=l=>f.value.tree_parent_id=l),"allow-clear":"","allow-search":"",placeholder:"请选择树表的父ID"},{default:t(()=>[(b(!0),k(h,null,D(o.value.columns,(l,n)=>(b(),C(S,{class:"w-full",label:l.column_name+" - "+l.column_comment,value:l.column_name,key:n},{default:t(()=>[w("div",he,[w("span",null,U(l.column_name),1),w("span",null,U(l.column_comment),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(s,{label:"树名称",field:"tree_name","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定树显示的名称字段，比如：name"},{default:t(()=>[e(x,{style:{width:"100%"},modelValue:f.value.tree_name,"onUpdate:modelValue":a[19]||(a[19]=l=>f.value.tree_name=l),"allow-clear":"","allow-search":"",placeholder:"请选择树表的主ID"},{default:t(()=>[(b(!0),k(h,null,D(o.value.columns,(l,n)=>(b(),C(S,{class:"w-full",label:l.column_name+" - "+l.column_comment,value:l.column_name,key:n},{default:t(()=>[w("div",De,[w("span",null,U(l.column_name),1),w("span",null,U(l.column_comment),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})])):H("",!0)]),_:1}),e(L,{title:"字段配置",key:"field_config"},{default:t(()=>[e(W,{title:"提示"},{default:t(()=>[a[38]||(a[38]=m(" 使用数组形式字段的组件，请在模型设置 ")),e(z,{class:"tag-primary"},{default:t(()=>a[36]||(a[36]=[m("获取器")])),_:1}),a[39]||(a[39]=m(" 和 ")),e(z,{class:"tag-primary"},{default:t(()=>a[37]||(a[37]=[m("修改器")])),_:1})]),_:1}),e(ie,{data:o.value.columns,pagination:!1,class:"mt-3"},{columns:t(()=>[e(y,{dataIndex:"sort",title:"排序",width:90},{cell:t(({rowIndex:l})=>[e(P,{modelValue:o.value.columns[l].sort,"onUpdate:modelValue":n=>o.value.columns[l].sort=n},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{dataIndex:"column_name",title:"字段名称",width:150,tooltip:""}),e(y,{dataIndex:"column_comment",title:"字段描述",width:160},{cell:t(({rowIndex:l})=>[e(r,{modelValue:o.value.columns[l].column_comment,"onUpdate:modelValue":n=>o.value.columns[l].column_comment=n,"allow-clear":""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{dataIndex:"column_type",title:"物理类型",width:100}),e(y,{dataIndex:"is_required",title:"必填",width:60},{title:t(()=>[a[40]||(a[40]=m("必填 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(g,{onChange:a[20]||(a[20]=l=>T(l,"required"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(g,{modelValue:o.value.columns[l].is_required,"onUpdate:modelValue":n=>o.value.columns[l].is_required=n},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{dataIndex:"is_insert",title:"插入",width:60},{title:t(()=>[a[41]||(a[41]=m("表单 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(g,{onChange:a[21]||(a[21]=l=>T(l,"insert"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(g,{modelValue:o.value.columns[l].is_insert,"onUpdate:modelValue":n=>o.value.columns[l].is_insert=n},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{dataIndex:"is_list",title:"列表",width:60},{title:t(()=>[a[42]||(a[42]=m("列表 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(g,{onChange:a[22]||(a[22]=l=>T(l,"list"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(g,{modelValue:o.value.columns[l].is_list,"onUpdate:modelValue":n=>o.value.columns[l].is_list=n},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{dataIndex:"is_query",title:"查询",width:60},{title:t(()=>[a[43]||(a[43]=m("查询 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(g,{onChange:a[23]||(a[23]=l=>T(l,"query"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(g,{modelValue:o.value.columns[l].is_query,"onUpdate:modelValue":n=>o.value.columns[l].is_query=n},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{dataIndex:"is_sort",title:"排序",width:60},{title:t(()=>[a[44]||(a[44]=m("排序 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(g,{onChange:a[24]||(a[24]=l=>T(l,"sort"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(g,{modelValue:o.value.columns[l].is_sort,"onUpdate:modelValue":n=>o.value.columns[l].is_sort=n},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{dataIndex:"query_type",title:"查询方式",width:150},{cell:t(({rowIndex:l})=>[e(x,{modelValue:o.value.columns[l].query_type,"onUpdate:modelValue":n=>o.value.columns[l].query_type=n,options:qe,"allow-clear":""},null,8,["modelValue","onUpdate:modelValue","options"])]),_:1}),e(y,{dataIndex:"view_type",title:"页面控件",width:220},{cell:t(({record:l,rowIndex:n})=>[e(se,null,{default:t(()=>[e(x,{modelValue:o.value.columns[n].view_type,"onUpdate:modelValue":p=>o.value.columns[n].view_type=p,style:{width:"140px"},options:Ce,onChange:p=>le(o.value.columns[n]),"allow-clear":""},null,8,["modelValue","onUpdate:modelValue","options","onChange"]),X.value.includes(l.view_type)?(b(),C(G,{key:0,onClick:p=>F.value.open(l)},{default:t(()=>a[45]||(a[45]=[m("设置")])),_:2},1032,["onClick"])):H("",!0)]),_:2},1024)]),_:1}),e(y,{dataIndex:"dict_type",title:"数据字典",width:160},{cell:t(({record:l,rowIndex:n})=>[e(x,{modelValue:o.value.columns[n].dict_type,"onUpdate:modelValue":p=>o.value.columns[n].dict_type=p,options:$.value,"allow-clear":"","field-names":{label:"name",value:"code"},placeholder:"选择数据字典",disabled:!["saSelect","radio","checkbox"].includes(l.view_type)},null,8,["modelValue","onUpdate:modelValue","options","disabled"])]),_:1})]),_:1},8,["data"])]),_:1}),e(L,{title:"关联配置",key:"relation_config"},{default:t(()=>[e(W,{title:"提示"},{default:t(()=>a[46]||(a[46]=[m("模型关联支持：一对一、一对多、一对一（反向）、多对多。")])),_:1}),e(pe,{onClick:te,type:"primary",class:"mt-3"},{default:t(()=>[e(me),a[47]||(a[47]=m(" 新增关联"))]),_:1}),(b(!0),k(h,null,D(f.value.relations,(l,n)=>(b(),k("div",{key:n},[e(c,{orientation:"left"},{default:t(()=>[m(U(l.name?l.name:"定义新关联")+" ",1),e(G,{onClick:p=>oe(n),class:"ml-5"},{default:t(()=>[e(re),a[48]||(a[48]=m(" 删除定义"))]),_:2},1032,["onClick"])]),_:2},1024),e(V,{gutter:24},{default:t(()=>[e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(s,{label:"关联类型",field:"type","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定关联类型"},{default:t(()=>[e(x,{modelValue:l.type,"onUpdate:modelValue":p=>l.type=p,"allow-clear":"","allow-search":"",placeholder:"请选择关联类型"},{default:t(()=>[(b(!0),k(h,null,D(ke,p=>(b(),C(S,{key:p.value,value:p.value,label:p.name},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(s,{label:"关联名称",field:"name","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"设置关联名称，且是代码中调用的名称"},{default:t(()=>[e(r,{modelValue:l.name,"onUpdate:modelValue":p=>l.name=p,"allow-clear":"",placeholder:"设置关联名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(V,{gutter:24},{default:t(()=>[e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(s,{label:"关联模型",field:"model","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"选择要关联的模型"},{default:t(()=>[e(r,{modelValue:l.model,"onUpdate:modelValue":p=>l.model=p,"allow-clear":"",placeholder:"设置关联模型"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(s,{label:l.type==="belongsTo"||l.type==="belongsToMany"?"外键":"当前模型主键",field:"localKey","label-col-flex":"auto","label-col-style":{width:"100px"},extra:l.type==="belongsTo"||l.type==="belongsToMany"?"关联模型_id":"当前模型主键"},{default:t(()=>[e(r,{modelValue:l.localKey,"onUpdate:modelValue":p=>l.localKey=p,"allow-clear":"",placeholder:"设置键名"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","extra"])]),_:2},1024)]),_:2},1024),e(V,{gutter:24},{default:t(()=>[be(e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(s,{label:"中间模型",field:"model","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"多对多关联的中间模型"},{default:t(()=>[e(r,{modelValue:l.table,"onUpdate:modelValue":p=>l.table=p,"allow-clear":"",placeholder:"请输入中间模型"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1536),[[ye,l.type==="belongsToMany"]]),e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(s,{label:l.type==="belongsTo"?"关联主键":"外键",field:"foreignKey","label-col-flex":"auto","label-col-style":{width:"100px"},extra:l.type==="belongsTo"?"关联模型主键":"当前模型_id"},{default:t(()=>[e(r,{style:{width:"100%"},modelValue:l.foreignKey,"onUpdate:modelValue":p=>l.foreignKey=p,"allow-clear":"",placeholder:"设置键名"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","extra"])]),_:2},1024)]),_:2},1024)]))),128))]),_:1})]),_:1},8,["active-key"])]),_:1},8,["model"])]),_:1},8,["loading"]),e(we,{ref_key:"settingComponentRef",ref:F,onConfrim:ee},null,512)]),_:1},8,["visible"])}}}),$l=Ue(Ee,[["__scopeId","data-v-0f0ab709"]]);export{$l as default};
