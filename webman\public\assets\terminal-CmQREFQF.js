import{t as M,_ as nt}from"./index-DkGLNqVb.js";import{d as it}from"./pinia-CtMvrpix.js";import{M as T,j as x}from"./@arco-design-uttiljWv.js";import{r as P,h as l,n as b,k as _,t as o,l as a,m as g,j as R,a1 as d,F as U,P as F,p as rt,z as B,y as r}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const lt={VITE_APP_BASE_URL:"",VITE_APP_TOKEN_PREFIX:"token"},ut=(e,s,u)=>{const c=lt,n=c.VITE_APP_BASE_URL,v=M.local.get(c.VITE_APP_TOKEN_PREFIX);return n+"/app/saipackage/index/terminal"+"?command="+e+"&uuid="+s+"&extend="+u+"&token="+v},ct=it("terminal",{state:()=>({show:!1,taskList:[],npmRegistry:"npm",packageManager:"yarn",composerRegistry:"composer"}),getters:{getState(){return{...this.$state}}},actions:{setTaskStatus(e,s){this.taskList[e].status=s,this.setTaskShowMessage(e,!0)},addTaskMessage(e,s){this.taskList[e].message=this.taskList[e].message.concat(s)},setTaskShowMessage(e,s=!this.taskList[e].showMessage){this.taskList[e].showMessage=s},cleanTaskList(){this.taskList=[]},taskCompleted(e){if(typeof this.taskList[e].callback!="function")return;const s=this.taskList[e].status;s==5||s==6?this.taskList[e].callback(5):s==4&&this.taskList[e].callback(4)},findTaskIdxFromUuid(e){for(const s in this.taskList)if(this.taskList[s].uuid==e)return parseInt(s);return!1},findTaskIdxFromGuess(e){if(this.taskList[e])return e;{let s=-1;for(const u in this.taskList)(this.taskList[u].status==2||this.taskList[u].status==3)&&(s=parseInt(u));return s===-1?!1:s}},startEventSource(e){const s=this;window.eventSource=new EventSource(ut(s.taskList[e].command,s.taskList[e].uuid,s.taskList[e].extend)),window.eventSource.onmessage=function(u){const c=JSON.parse(u.data);if(!c||!c.data)return;const n=s.findTaskIdxFromUuid(c.uuid);n!==!1&&(c.data=="exec-error"?(s.setTaskStatus(n,5),window.eventSource.close(),s.taskCompleted(n),s.startTask()):c.data=="exec-completed"?(window.eventSource.close(),s.taskList[n].status!=4&&s.setTaskStatus(n,5),s.taskCompleted(n),s.startTask()):c.data=="connection-success"?s.setTaskStatus(n,3):c.data=="exec-success"?s.setTaskStatus(n,4):s.addTaskMessage(n,c.data))},window.eventSource.onerror=function(){window.eventSource.close();const u=s.findTaskIdxFromGuess(e);u!==!1&&(s.setTaskStatus(u,5),s.taskCompleted(u))}},addNodeTask(e,s="",u=()=>{}){e=e+"."+(this.packageManager=="unknown"?"npm":this.packageManager),this.addTask(e,s,u)},addTask(e,s="",u=()=>{}){if(this.taskList=this.taskList.concat({uuid:M.uuid(),createTime:M.dateFormat(),status:1,command:e,message:[],showMessage:!1,extend:s,callback:u}),this.show===!1){for(const c in this.taskList)if(this.taskList[c].status==5||this.taskList[c].status==6){T.warning({content:"任务列表中存在失败的任务",duration:2e3});break}}this.startTask()},startTask(){let e=null;for(const s in this.taskList){if(this.taskList[s].status==1){e=parseInt(s);break}if(this.taskList[s].status==2||this.taskList[s].status==3)break;this.taskList[s].status!=4&&(this.taskList[s].status==5||this.taskList[s].status==6)}e!==null&&(this.setTaskStatus(e,2),this.startEventSource(e))},retryTask(e){this.taskList[e].message=[],this.setTaskStatus(e,1),this.startTask()},delTask(e){this.taskList[e].status!=2&&this.taskList[e].status!=3&&this.taskList.splice(e,1)}},persist:{key:"storeTerminal"}}),pt={key:1},mt={class:"flex items-center"},dt={class:"font-bold text-lg mr-4"},kt={key:0,class:"exec-message"},ft=["innerHTML"],_t={class:"flex justify-center gap-2"},gt={class:"pb-4"},Tt={__name:"terminal",emits:["success"],setup(e,{expose:s,emit:u}){const c=u,n=ct(),v=P(!1),y=P(!1),A=()=>{n.addNodeTask("test",!0,()=>{})},O=()=>{x.confirm({title:"前端打包发布",content:"确认重新打包前端并发布项目吗？",onOk:()=>{n.addNodeTask("web-build","",()=>{T.success("前端打包发布成功")})}})},$=()=>{x.confirm({title:"前端依赖更新",content:"确认更新前端Node依赖吗？",onOk:()=>{n.addNodeTask("web-install","",()=>{T.success("前端依赖更新成功")})}})},j=()=>{x.confirm({title:"composer包更新",content:"确认更新后端composer包吗？",onOk:()=>{n.addTask("composer.update","",()=>{T.success("composer包更新成功")})}})},H=(m="")=>{n.addNodeTask("web-install",m,()=>{T.success("前端依赖更新成功"),c("success")})},G=(m="")=>{n.addTask("composer.update",m,()=>{T.success("composer包更新成功"),setTimeout(()=>{c("success")},500)})},X=m=>{const t="set-npm-registry."+m;y.value=!1,n.addTask(t,"",()=>{T.success("NPM源设置成功")})},z=m=>{const t="set-composer-registry."+m;y.value=!1,n.addTask(t,"",()=>{T.success("Composer源设置成功")})},D=m=>{switch(m){case 1:return"#a2afb9";case 2:return"#2196f3";case 3:return"#ffc107";case 4:return"#00b42a";case 5:return"#ff0000";case 6:return"#ff4d4f"}},J=m=>{switch(m){case 1:return"等待执行";case 2:return"连接中";case 3:return"执行中";case 4:return"执行成功";case 5:return"执行失败";case 6:return"未知"}},K=m=>m.replace(/\x1b\[([0-9;]+)m/g,function(t,w){const p=[];return w.split(";").forEach(h=>{switch(h=parseInt(h,10),h){case 0:p.push("color:inherit;font-weight:normal;text-decoration:none");break;case 1:p.push("font-weight:bold");break;case 3:p.push("font-style:italic");break;case 4:p.push("text-decoration:underline");break;case 30:p.push("color:black");break;case 31:p.push("color:red");break;case 32:p.push("color:green");break;case 33:p.push("color:yellow");break;case 34:p.push("color:blue");break;case 35:p.push("color:magenta");break;case 36:p.push("color:cyan");break;case 37:p.push("color:white");break}}),p.length?`<span style="${p.join(";")}">`:"</span>"}),q=async()=>{y.value=!0};return s({open:async()=>{v.value=!0},close:()=>{v.value=!1},frontInstall:H,backendInstall:G}),(m,t)=>{const w=l("a-empty"),p=l("a-tag"),h=l("icon-refresh"),f=l("a-button"),I=l("icon-delete"),Q=l("a-collapse-item"),W=l("a-collapse"),Y=l("a-timeline-item"),Z=l("a-timeline"),tt=l("a-divider"),st=l("icon-play-arrow"),V=l("icon-sync"),et=l("icon-share-external"),at=l("icon-settings"),N=l("a-modal"),k=l("a-option"),L=l("a-select"),S=l("a-space");return _(),b("div",null,[o(N,{visible:v.value,"onUpdate:visible":t[1]||(t[1]=i=>v.value=i),width:800,footer:!1},{title:a(()=>t[6]||(t[6]=[r(" 终端执行面板 ")])),default:a(()=>[g("div",null,[d(n).taskList.length==0?(_(),R(w,{key:0,description:"暂无任务"})):(_(),b("div",pt,[o(Z,{labelPosition:"relative"},{default:a(()=>[(_(!0),b(U,null,F(d(n).taskList,(i,E)=>(_(),R(Y,{label:i.createTime},{default:a(()=>[o(W,{"default-active-key":["1"]},{default:a(()=>[o(Q,{key:"1"},{header:a(()=>[g("div",mt,[g("span",dt,B(i.command),1),o(p,{color:D(i.status)},{default:a(()=>[r(B(J(i.status)),1)]),_:2},1032,["color"])])]),extra:a(()=>[o(f,{type:"text",status:"warning",shape:"round",onClick:C=>d(n).retryTask(E)},{icon:a(()=>[o(h)]),_:2},1032,["onClick"]),o(f,{type:"text",status:"danger",shape:"round",onClick:C=>d(n).delTask(E)},{icon:a(()=>[o(I)]),_:2},1032,["onClick"])]),default:a(()=>[i.status==2||i.status==3||i.status>3&&i.showMessage?(_(),b("div",kt,[(_(!0),b(U,null,F(i.message,(C,ot)=>(_(),b("pre",{key:ot,innerHTML:K(C)},null,8,ft))),128))])):rt("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["label"]))),256))]),_:1})])),o(tt),g("div",_t,[o(f,{type:"outline",status:"success",onClick:A},{icon:a(()=>[o(st)]),default:a(()=>[t[7]||(t[7]=r("测试命令 "))]),_:1}),o(f,{type:"outline",onClick:$},{icon:a(()=>[o(V)]),default:a(()=>[t[8]||(t[8]=r("前端依赖更新 "))]),_:1}),o(f,{type:"outline",onClick:j},{icon:a(()=>[o(V)]),default:a(()=>[t[9]||(t[9]=r("后端依赖更新 "))]),_:1}),o(f,{type:"outline",status:"warning",onClick:O},{icon:a(()=>[o(et)]),default:a(()=>[t[10]||(t[10]=r("一键发布 "))]),_:1}),o(f,{type:"outline",onClick:q},{icon:a(()=>[o(at)]),default:a(()=>[t[11]||(t[11]=r("终端设置 "))]),_:1}),o(f,{type:"outline",status:"danger",onClick:t[0]||(t[0]=i=>d(n).cleanTaskList())},{icon:a(()=>[o(I)]),default:a(()=>[t[12]||(t[12]=r("清理任务 "))]),_:1})])])]),_:1},8,["visible"]),o(N,{visible:y.value,"onUpdate:visible":t[5]||(t[5]=i=>y.value=i),footer:!1},{title:a(()=>t[13]||(t[13]=[r(" 终端设置 ")])),default:a(()=>[g("div",gt,[o(S,null,{default:a(()=>[t[17]||(t[17]=g("div",{class:"w-24"},"NPM源",-1)),o(L,{style:{width:"320px"},modelValue:d(n).npmRegistry,"onUpdate:modelValue":t[2]||(t[2]=i=>d(n).npmRegistry=i),onChange:X},{default:a(()=>[o(k,{value:"npm"},{default:a(()=>t[14]||(t[14]=[r("npm官源")])),_:1}),o(k,{value:"taobao"},{default:a(()=>t[15]||(t[15]=[r("taobao")])),_:1}),o(k,{value:"tencent"},{default:a(()=>t[16]||(t[16]=[r("tencent")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(S,{class:"mt-4"},{default:a(()=>[t[21]||(t[21]=g("label",{class:"w-24"},"NPM包管理器",-1)),o(L,{style:{width:"320px"},modelValue:d(n).packageManager,"onUpdate:modelValue":t[3]||(t[3]=i=>d(n).packageManager=i)},{default:a(()=>[o(k,{value:"npm"},{default:a(()=>t[18]||(t[18]=[r("npm")])),_:1}),o(k,{value:"yarn"},{default:a(()=>t[19]||(t[19]=[r("yarn")])),_:1}),o(k,{value:"pnpm"},{default:a(()=>t[20]||(t[20]=[r("pnpm")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(S,{class:"mt-4"},{default:a(()=>[t[26]||(t[26]=g("label",{class:"w-24"},"Composer源",-1)),o(L,{style:{width:"320px"},modelValue:d(n).composerRegistry,"onUpdate:modelValue":t[4]||(t[4]=i=>d(n).composerRegistry=i),onChange:z},{default:a(()=>[o(k,{value:"composer"},{default:a(()=>t[22]||(t[22]=[r("composer官源")])),_:1}),o(k,{value:"tencent"},{default:a(()=>t[23]||(t[23]=[r("tencent")])),_:1}),o(k,{value:"huawei"},{default:a(()=>t[24]||(t[24]=[r("huawei")])),_:1}),o(k,{value:"kkame"},{default:a(()=>t[25]||(t[25]=[r("kkame")])),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["visible"])])}}},Ls=nt(Tt,[["__scopeId","data-v-e41fa5e9"]]);export{Ls as default};
