<?php
/**
 * 示例数据表迁移
 */
use yii\db\Migration;

class m250803_063345_create_example_table extends Migration
{
    public function safeUp()
    {
        $this->createTable("{{%example}}", [
            "id" => $this->primaryKey(),
            "name" => $this->string()->notNull(),
            "description" => $this->text(),
            "status" => $this->smallInteger()->defaultValue(1),
            "created_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP"),
            "updated_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        ]);
        
        // 插入示例数据
        $this->insert("{{%example}}", [
            "name" => "示例数据1",
            "description" => "这是一个示例数据",
            "status" => 1
        ]);
        
        $this->insert("{{%example}}", [
            "name" => "示例数据2", 
            "description" => "这是另一个示例数据",
            "status" => 1
        ]);
    }

    public function safeDown()
    {
        $this->dropTable("{{%example}}");
    }
}