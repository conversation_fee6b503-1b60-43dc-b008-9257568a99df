@echo off
echo ========================================
echo    SaiAdmin 数据库配置助手
echo ========================================
echo.

echo 当前数据库配置信息：
echo - 主机: 127.0.0.1:3306
echo - 数据库: saiadmin
echo - 用户: root
echo - 密码: 5GeNi1v7P7Xcur5W
echo.

echo 检查 MySQL 服务状态...
sc query mysql >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MySQL 服务已安装
    sc query mysql | find "RUNNING" >nul
    if %errorlevel% == 0 (
        echo ✅ MySQL 服务正在运行
    ) else (
        echo ⚠️ MySQL 服务未运行，尝试启动...
        net start mysql
    )
) else (
    echo ❌ MySQL 服务未安装或未找到
    echo.
    echo 请确保已安装 MySQL 并且服务名称正确
    echo 常见的 MySQL 服务名称：
    echo - mysql
    echo - MySQL80 (MySQL 8.0)
    echo - MySQL57 (MySQL 5.7)
    echo - wampmysqld64 (WAMP)
    echo - xampp-mysql (XAMPP)
)
echo.

echo 测试数据库连接...
mysql -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "SELECT 1;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 数据库连接成功
    echo.
    echo 检查数据库是否存在...
    mysql -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "USE saiadmin; SELECT 'Database exists' as status;" 2>nul
    if %errorlevel% == 0 (
        echo ✅ 数据库 'saiadmin' 已存在
    ) else (
        echo ⚠️ 数据库 'saiadmin' 不存在，是否创建？
        set /p create_db="输入 Y 创建数据库，或按任意键跳过: "
        if /i "%create_db%"=="Y" (
            mysql -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "CREATE DATABASE IF NOT EXISTS saiadmin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
            echo ✅ 数据库 'saiadmin' 创建完成
        )
    )
) else (
    echo ❌ 数据库连接失败
    echo.
    echo 可能的原因：
    echo 1. MySQL 服务未启动
    echo 2. 用户名或密码错误
    echo 3. 端口号错误
    echo 4. 防火墙阻止连接
    echo.
    echo 建议检查：
    echo 1. 确认 MySQL 服务正在运行
    echo 2. 验证用户名和密码
    echo 3. 检查 MySQL 配置文件
)
echo.

echo ========================================
echo 配置完成！
echo.
echo 如果仍有问题，请：
echo 1. 检查 MySQL 错误日志
echo 2. 确认用户权限设置
echo 3. 验证网络连接
echo ========================================
pause
