import{f as O}from"./file2md5-B4-SI92N.js";import{h as c,c as S}from"./index-ybrmzYq5.js";import{M as x}from"./@arco-design-uttiljWv.js";import{r as h,a as C,h as f,j as F,k as b,l as o,m as s,n as k,t as i,y as r,z as d,u as q,O as D}from"./@vue-9ZIPiVZG.js";const M={getAppList(t={}){return c({url:"/app/saipackage/install/index",method:"get",params:t})},uploadApp(t={}){return c({url:"/app/saipackage/install/upload",method:"post",data:t})},installApp(t={}){return c({url:"/app/saipackage/install/install",method:"post",data:t})},uninstallApp(t={}){return c({url:"/app/saipackage/install/uninstall",method:"post",data:t})},reloadBackend(t={}){return c({url:"/app/saipackage/install/reload",method:"post",data:t})}},N={class:"flex flex-col items-center mb-24"},V={key:0,class:"mt-10 w-[600px]"},P={key:1,class:"mt-10 w-[600px]"},T={style:{"background-color":"var(--color-fill-2)",border:"1px dashed var(--color-fill-4)"},class:"rounded text-center p-7 w-full"},E=8*1024*1024,H={__name:"install-box",emits:["success"],setup(t,{expose:w,emit:y}){const A=y,_=h(!1);h(!1);const g={app:"",title:"",about:"",author:"",version:"",state:0,update:0},e=C({...g}),B=async p=>{if(!p.fileItem)return;let l=!0;const a=p.fileItem.file;if(a.size>E&&(x.warning(a.name+"超出文件大小限制"),l=!1),l){const v=await O(a),n=new FormData;n.append("file",a),n.append("hash",v),n.append("mode","local");const u=await S.uploadFile(n);if(u.code==200){const m=await M.uploadApp({file:u.data.storage_path});m.code==200&&(Object.assign(e,m.data),x.success("上传成功"),A("success"))}}},j=async()=>{_.value=!0,Object.assign(e,g),await z()},z=async()=>{};return w({open:j}),(p,l)=>{const a=f("a-descriptions-item"),v=f("a-descriptions"),n=f("icon-upload"),u=f("a-upload");return b(),F(D("a-modal"),{visible:_.value,"onUpdate:visible":l[0]||(l[0]=m=>_.value=m),width:800,title:"上传插件包-安装插件","mask-closable":!1,footer:!1},{default:o(()=>[s("div",N,[l[2]||(l[2]=s("div",{class:"w-[400px]"},[s("div",{class:"text-lg text-red-500 font-bold"}," 请您务必确认模块包文件来自官方渠道或经由官方认证的模块作者，否则系统可能被破坏，因为： "),s("div",{class:"text-red-500"},"1. 模块可以修改和新增系统文件"),s("div",{class:"text-red-500"},"2. 模块可以执行sql命令和代码"),s("div",{class:"text-red-500"},"3. 模块可以安装新的前后端依赖")],-1)),e&&e.app?(b(),k("div",V,[i(v,{column:1,bordered:""},{default:o(()=>[i(a,{label:"应用标识"},{default:o(()=>[r(d(e==null?void 0:e.app),1)]),_:1}),i(a,{label:"应用名称"},{default:o(()=>[r(d(e==null?void 0:e.title),1)]),_:1}),i(a,{label:"应用描述"},{default:o(()=>[r(d(e==null?void 0:e.about),1)]),_:1}),i(a,{label:"作者"},{default:o(()=>[r(d(e==null?void 0:e.author),1)]),_:1}),i(a,{label:"版本"},{default:o(()=>[r(d(e==null?void 0:e.version),1)]),_:1})]),_:1})])):(b(),k("div",P,[i(u,{"custom-request":B,"show-file-list":!1,accept:".zip,.rar",draggable:!0},{"upload-button":o(()=>[q(p.$slots,"customer",{},()=>[s("div",T,[s("div",null,[i(n,{class:"text-3xl text-gray-400"}),l[1]||(l[1]=s("div",null,[r("将插件包文件拖到此处，或"),s("span",{style:{color:"#3370ff","margin-left":"10px"}},"点击上传")],-1))])])])]),_:3})]))])]),_:3},40,["visible"])}}},J=Object.freeze(Object.defineProperty({__proto__:null,default:H},Symbol.toStringTag,{value:"Module"}));export{H as _,J as i,M as s};
