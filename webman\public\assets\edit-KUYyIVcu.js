import{t as H}from"./index-DkGLNqVb.js";import{a as C}from"./crontab-D2UudUxl.js";import{M as J}from"./@arco-design-uttiljWv.js";import{r as y,c as K,a as Q,h as n,j as D,k as p,l as a,t as l,n as k,p as V,y as u,F as b,m as x,a1 as q,O as W}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const ce={__name:"edit",emits:["success"],setup(X,{expose:F,emit:O}){const R=O,g=y();y();const U=y(""),w=y(!1),N=y(!1);let T=K(()=>"定时任务"+(U.value=="add"?"-新增":"-编辑"));const j=[{label:"URL任务GET",value:1},{label:"URL任务POST",value:2},{label:"类任务",value:3}],B={id:"",name:"",type:"",rule:"",task_style:1,month:1,day:1,week:1,hour:1,minute:1,second:1,target:"",parameter:"",status:1,remark:""},t=Q({...B}),E={name:[{required:!0,message:"任务名称不能为空"}],type:[{required:!0,message:"任务类型不能为空"}],task_style:[{required:!0,message:"定时规则不能为空"}],target:[{required:!0,message:"调用目标不能为空"}]},L=async(s="add")=>{U.value=s,Object.assign(t,B),g.value.clearValidate(),w.value=!0,await M()},f=s=>{const e=s.match(/\d+/);return e?Number.parseInt(e[0]):0},M=async()=>{},P=async s=>{for(const i in t)s[i]!=null&&s[i]!=null&&(t[i]=s[i]);const e=t.rule.split(" ");t.second=f(e[0]),t.minute=f(e[1]),t.hour=f(e[2]),t.day=f(e[3]),t.month=f(e[4]),t.week=f(e[5])},G=async s=>{var i;if(!await((i=g.value)==null?void 0:i.validate())){N.value=!0;let r={...t},d={};U.value==="add"?(r.id=void 0,d=await C.save(r)):d=await C.update(r.id,r),d.code===200&&(J.success("操作成功"),R("success"),s(!0)),setTimeout(()=>{N.value=!1},500)}s(!1)},I=()=>w.value=!1;return F({open:L,setFormData:P}),(s,e)=>{const i=n("a-input"),r=n("a-form-item"),d=n("a-select"),m=n("a-option"),v=n("a-input-number"),S=n("a-space"),_=n("a-textarea"),z=n("sa-radio"),A=n("a-form");return p(),D(W("a-modal"),{visible:w.value,"onUpdate:visible":e[13]||(e[13]=o=>w.value=o),width:q(H).getDevice()==="mobile"?"100%":"700px",title:q(T),"mask-closable":!1,"ok-loading":N.value,onCancel:I,onBeforeOk:G},{default:a(()=>[l(A,{ref_key:"formRef",ref:g,model:t,rules:E,"auto-label-width":!0},{default:a(()=>[l(r,{label:"任务名称",field:"name"},{default:a(()=>[l(i,{modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.name=o),placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1}),l(r,{label:"任务类型",field:"type"},{default:a(()=>[l(d,{modelValue:t.type,"onUpdate:modelValue":e[1]||(e[1]=o=>t.type=o),options:j,placeholder:"请选择任务类型"},null,8,["modelValue"])]),_:1}),l(r,{label:"定时规则",field:"task_style"},{default:a(()=>[l(S,null,{default:a(()=>[l(d,{modelValue:t.task_style,"onUpdate:modelValue":e[2]||(e[2]=o=>t.task_style=o),style:{width:"100px"}},{default:a(()=>[l(m,{value:1},{default:a(()=>e[14]||(e[14]=[u("每天")])),_:1}),l(m,{value:2},{default:a(()=>e[15]||(e[15]=[u("每小时")])),_:1}),l(m,{value:3},{default:a(()=>e[16]||(e[16]=[u("N小时")])),_:1}),l(m,{value:4},{default:a(()=>e[17]||(e[17]=[u("N分钟")])),_:1}),l(m,{value:5},{default:a(()=>e[18]||(e[18]=[u("N秒")])),_:1}),l(m,{value:6},{default:a(()=>e[19]||(e[19]=[u("每周")])),_:1}),l(m,{value:7},{default:a(()=>e[20]||(e[20]=[u("每月")])),_:1}),l(m,{value:8},{default:a(()=>e[21]||(e[21]=[u("每年")])),_:1})]),_:1},8,["modelValue"]),t.task_style==8?(p(),k(b,{key:0},[l(v,{modelValue:t.month,"onUpdate:modelValue":e[3]||(e[3]=o=>t.month=o),precision:0,min:1,max:12,style:{width:"80px"}},null,8,["modelValue"]),e[22]||(e[22]=x("span",null,"月",-1))],64)):V("",!0),t.task_style>6?(p(),k(b,{key:1},[l(v,{modelValue:t.day,"onUpdate:modelValue":e[4]||(e[4]=o=>t.day=o),precision:0,min:1,max:31,style:{width:"80px"}},null,8,["modelValue"]),e[23]||(e[23]=x("span",null,"日",-1))],64)):V("",!0),t.task_style==6?(p(),D(d,{key:2,modelValue:t.week,"onUpdate:modelValue":e[5]||(e[5]=o=>t.week=o),style:{width:"80px"}},{default:a(()=>[l(m,{value:1},{default:a(()=>e[24]||(e[24]=[u("周一")])),_:1}),l(m,{value:2},{default:a(()=>e[25]||(e[25]=[u("周二")])),_:1}),l(m,{value:3},{default:a(()=>e[26]||(e[26]=[u("周三")])),_:1}),l(m,{value:4},{default:a(()=>e[27]||(e[27]=[u("周四")])),_:1}),l(m,{value:5},{default:a(()=>e[28]||(e[28]=[u("周五")])),_:1}),l(m,{value:6},{default:a(()=>e[29]||(e[29]=[u("周六")])),_:1}),l(m,{value:0},{default:a(()=>e[30]||(e[30]=[u("周日")])),_:1})]),_:1},8,["modelValue"])):V("",!0),[1,3,6,7,8].includes(t.task_style)?(p(),k(b,{key:3},[l(v,{modelValue:t.hour,"onUpdate:modelValue":e[6]||(e[6]=o=>t.hour=o),precision:0,min:0,max:23,style:{width:"80px"}},null,8,["modelValue"]),e[31]||(e[31]=x("span",null,"时",-1))],64)):V("",!0),t.task_style!=5?(p(),k(b,{key:4},[l(v,{modelValue:t.minute,"onUpdate:modelValue":e[7]||(e[7]=o=>t.minute=o),precision:0,min:0,max:59,style:{width:"80px"}},null,8,["modelValue"]),e[32]||(e[32]=x("span",null,"分",-1))],64)):V("",!0),t.task_style==5?(p(),k(b,{key:5},[l(v,{modelValue:t.second,"onUpdate:modelValue":e[8]||(e[8]=o=>t.second=o),precision:0,min:0,max:59,style:{width:"80px"}},null,8,["modelValue"]),e[33]||(e[33]=x("span",null,"秒",-1))],64)):V("",!0)]),_:1})]),_:1}),l(r,{label:"调用目标",field:"target"},{default:a(()=>[l(_,{modelValue:t.target,"onUpdate:modelValue":e[9]||(e[9]=o=>t.target=o),placeholder:"请输入调用目标"},null,8,["modelValue"])]),_:1}),l(r,{label:"任务参数",field:"parameter"},{default:a(()=>[l(_,{modelValue:t.parameter,"onUpdate:modelValue":e[10]||(e[10]=o=>t.parameter=o),placeholder:"请输入任务参数"},null,8,["modelValue"])]),_:1}),l(r,{label:"状态",field:"status"},{default:a(()=>[l(z,{modelValue:t.status,"onUpdate:modelValue":e[11]||(e[11]=o=>t.status=o),dict:"data_status",placeholder:"请选择状态"},null,8,["modelValue"])]),_:1}),l(r,{label:"备注",field:"remark"},{default:a(()=>[l(_,{modelValue:t.remark,"onUpdate:modelValue":e[12]||(e[12]=o=>t.remark=o),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},40,["visible","width","title","ok-loading"])}}};export{ce as default};
