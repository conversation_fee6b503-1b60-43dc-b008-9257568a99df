import{m as _}from"./menu-CBqt8CnT.js";import M from"./edit-CqvT7GJF.js";import{_ as g}from"./index-DkGLNqVb.js";import{M as B}from"./@arco-design-uttiljWv.js";import{r as c,a as f,o as D,h as r,ba as R,n as A,k as h,t as e,l as n,M as E,p as N,j as S,y as U}from"./@vue-9ZIPiVZG.js";import"./monaco-editor-nMXQdunA.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const j={class:"ma-content-block lg:flex justify-between"},$={__name:"index",setup(L){const m=c(),l=c(),s=c({name:"",code:"",status:""}),v=o=>{var t,a;(t=l.value)==null||t.open(),(a=l.value)==null||a.setFormData({parent_id:o})},w=f({api:_.getList,rowSelection:{showCheckedAll:!0},operationColumnWidth:200,add:{show:!0,auth:["/core/menu/save"],func:async()=>{var o;(o=l.value)==null||o.open()}},edit:{show:!0,auth:["/core/menu/update"],func:async o=>{var t,a;(t=l.value)==null||t.open("edit"),(a=l.value)==null||a.setFormData(o)}},delete:{show:!0,auth:["/core/menu/destroy"],func:async o=>{var a;(await _.destroy(o)).code===200&&(B.success("删除成功！"),(a=m.value)==null||a.refresh())}},isExpand:!0}),x=f([{title:"菜单名称",dataIndex:"name",width:180},{title:"菜单类型",dataIndex:"type",type:"dict",dict:"menu_type",width:100},{title:"图标",dataIndex:"icon",width:80},{title:"菜单标识",dataIndex:"code",width:150},{title:"路由地址",dataIndex:"route",width:150},{title:"视图组件",dataIndex:"component",width:200},{title:"排序",dataIndex:"sort",width:80},{title:"隐藏",dataIndex:"is_hidden",type:"dict",dict:"yes_or_no",width:80},{title:"状态",dataIndex:"status",type:"dict",dict:"data_status",width:80},{title:"创建时间",dataIndex:"create_time",width:180}]),y=async()=>{},u=async()=>{var o;(o=m.value)==null||o.refresh()};return D(async()=>{y(),u()}),(o,t)=>{const a=r("a-input"),p=r("a-form-item"),d=r("a-col"),k=r("sa-select"),V=r("sa-icon"),I=r("icon-plus"),b=r("a-link"),C=r("sa-table"),F=R("auth");return h(),A("div",j,[e(C,{ref_key:"crudRef",ref:m,options:w,columns:x,searchForm:s.value},{tableSearch:n(()=>[e(d,{span:8},{default:n(()=>[e(p,{field:"name",label:"菜单名称"},{default:n(()=>[e(a,{modelValue:s.value.name,"onUpdate:modelValue":t[0]||(t[0]=i=>s.value.name=i),placeholder:"请输入菜单名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:n(()=>[e(p,{field:"code",label:"菜单标识"},{default:n(()=>[e(a,{modelValue:s.value.code,"onUpdate:modelValue":t[1]||(t[1]=i=>s.value.code=i),placeholder:"请输入菜单标识","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:n(()=>[e(p,{field:"status",label:"状态"},{default:n(()=>[e(k,{modelValue:s.value.status,"onUpdate:modelValue":t[2]||(t[2]=i=>s.value.status=i),dict:"data_status",placeholder:"请选择状态","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),icon:n(({record:i})=>[e(V,{icon:i.icon},null,8,["icon"])]),operationBeforeExtend:n(({record:i})=>[i.type==="M"?E((h(),S(b,{key:0,onClick:P=>v(i.id)},{default:n(()=>[e(I),t[3]||(t[3]=U(" 新增 "))]),_:2},1032,["onClick"])),[[F,["/core/menu/save"]]]):N("",!0)]),_:1},8,["options","columns","searchForm"]),e(M,{ref_key:"editRef",ref:l,onSuccess:u},null,512)])}}},Ht=g($,[["__scopeId","data-v-6c957dec"]]);export{Ht as default};
