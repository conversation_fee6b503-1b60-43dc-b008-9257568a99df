import{c as _}from"./config-ZWMRzLkl.js";import{M as O}from"./index-D4ERJytk.js";import{M as E}from"./@arco-design-uttiljWv.js";import{r as u,a as N,h as n,j as V,k as y,l as r,t as a,p as R,a1 as G,O as I}from"./@vue-9ZIPiVZG.js";import"./index-ybrmzYq5.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const L=[{label:"文本框",value:"input"},{label:"文本域",value:"textarea"},{label:"下拉选择框",value:"select"},{label:"单选框",value:"radio"},{label:"图片上传",value:"uploadImage"},{label:"文件上传",value:"uploadFile"},{label:"富文本编辑器",value:"wangEditor"}],Ke={__name:"edit",emits:["success"],setup(P,{expose:k,emit:w}){const x=w,s=u(!1),d=u(!1),f=u(""),c=u(),v=u([]),g={id:null,group_id:"",name:"",key:"",value:"",input_type:"input",config_select_data:"",sort:100,remark:""},t=N({...g}),U={group_id:[{required:!0,message:"所属组不能为空"}],name:[{required:!0,message:"配置标题不能为空"}],key:[{required:!0,message:"配置标识不能为空"}],input_type:[{required:!0,message:"组件类型不能为空"}]},C=async(i="add")=>{f.value=i,Object.assign(t,g),c.value.clearValidate(),s.value=!0,await D()},D=async()=>{const i=await _.getConfigGroupList();v.value=i.data},q=async i=>{for(const e in t)i[e]!=null&&i[e]!=null&&(t[e]=i[e])},M=async i=>{var p;if(!await((p=c.value)==null?void 0:p.validate())){d.value=!0;let l={...t},m={};f.value==="add"?(l.id=void 0,m=await _.save(l)):(l.config_select_data&&typeof l.config_select_data=="string"&&(l.config_select_data=l.config_select_data.replace(/\r|\n|\s/g,""),l.config_select_data=l.config_select_data.replace(",]","]")),m=await _.update(l.id,l)),m.code===200&&(E.success("操作成功"),x("success"),i(!0)),setTimeout(()=>{d.value=!1},500)}i(!1)},j=()=>s.value=!1;return k({open:C,setFormData:q}),(i,e)=>{const p=n("a-select"),l=n("a-form-item"),m=n("a-input"),b=n("a-textarea"),B=n("a-input-number"),F=n("a-form");return y(),V(I("a-modal"),{visible:s.value,"onUpdate:visible":e[8]||(e[8]=o=>s.value=o),width:800,title:"参数配置"+(f.value=="add"?"-新增":"-编辑"),"mask-closable":!1,"ok-loading":d.value,onCancel:j,onBeforeOk:M},{default:r(()=>[a(F,{ref_key:"formRef",ref:c,model:t,rules:U,"auto-label-width":!0},{default:r(()=>[a(l,{label:"配置组",field:"group_id"},{default:r(()=>[a(p,{modelValue:t.group_id,"onUpdate:modelValue":e[0]||(e[0]=o=>t.group_id=o),options:v.value,"field-names":{label:"name",value:"id"},placeholder:"请选择配置组",disabled:""},null,8,["modelValue","options"])]),_:1}),a(l,{label:"配置标题",field:"name"},{default:r(()=>[a(m,{modelValue:t.name,"onUpdate:modelValue":e[1]||(e[1]=o=>t.name=o),placeholder:"请输入配置标题"},null,8,["modelValue"])]),_:1}),a(l,{label:"配置标识",field:"key"},{default:r(()=>[a(m,{modelValue:t.key,"onUpdate:modelValue":e[2]||(e[2]=o=>t.key=o),placeholder:"请输入配置标识"},null,8,["modelValue"])]),_:1}),a(l,{label:"配置值",field:"value"},{default:r(()=>[a(b,{modelValue:t.value,"onUpdate:modelValue":e[3]||(e[3]=o=>t.value=o),placeholder:"请输入配置值"},null,8,["modelValue"])]),_:1}),a(l,{label:"排序",field:"sort"},{default:r(()=>[a(B,{modelValue:t.sort,"onUpdate:modelValue":e[4]||(e[4]=o=>t.sort=o),min:0,max:999,placeholder:"请输入排序"},null,8,["modelValue"])]),_:1}),a(l,{label:"输入组件",field:"input_type"},{default:r(()=>[a(p,{modelValue:t.input_type,"onUpdate:modelValue":e[5]||(e[5]=o=>t.input_type=o),options:G(L),placeholder:"请选择输入组件"},null,8,["modelValue","options"])]),_:1}),a(l,{label:"配置说明",field:"remark"},{default:r(()=>[a(b,{modelValue:t.remark,"onUpdate:modelValue":e[6]||(e[6]=o=>t.remark=o),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1}),["select","radio"].includes(t.input_type)?(y(),V(l,{key:0,label:"配置数据",field:"config_select_data",extra:'用于配置下拉、单选、复选的数据，格式例子：[{"label":"数据一", "value":"shuju1"},...]'},{default:r(()=>[a(O,{modelValue:t.config_select_data,"onUpdate:modelValue":e[7]||(e[7]=o=>t.config_select_data=o),height:200,placeholder:"请输入配置数据"},null,8,["modelValue"])]),_:1})):R("",!0)]),_:1},8,["model"])]),_:1},40,["visible","title","ok-loading"])}}};export{Ke as default};
