<?php
/**
 * SaiAdmin 基础控制器 (Yii 2.0 兼容)
 */
namespace app\components\base;

use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;

/**
 * 基础控制器类
 */
class BaseController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            "access" => [
                "class" => AccessControl::class,
                "rules" => [
                    [
                        "allow" => true,
                        "roles" => ["@"],
                    ],
                ],
            ],
            "verbs" => [
                "class" => VerbFilter::class,
                "actions" => [
                    "delete" => ["POST"],
                ],
            ],
        ];
    }

    /**
     * 返回成功响应
     * @param mixed $data 数据
     * @param string $message 消息
     * @param int $code 状态码
     * @return array
     */
    protected function success($data = [], $message = "操作成功", $code = 200)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            "code" => $code,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }

    /**
     * 返回失败响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 数据
     * @return array
     */
    protected function fail($message = "操作失败", $code = 400, $data = [])
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            "code" => $code,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }

    /**
     * 获取分页参数
     * @return array
     */
    protected function getPaginationParams()
    {
        $request = Yii::$app->request;
        return [
            "page" => (int) $request->get("page", 1),
            "pageSize" => (int) $request->get("pageSize", 20),
        ];
    }

    /**
     * 获取排序参数
     * @return array
     */
    protected function getSortParams()
    {
        $request = Yii::$app->request;
        return [
            "sort" => $request->get("sort", "id"),
            "order" => $request->get("order", "desc"),
        ];
    }
}