<?php
/**
 * SaiAdmin 代码质量检查脚本
 * 用于检测代码中的潜在问题和优化建议
 */

class CodeQualityChecker
{
    private $issues = [];
    private $suggestions = [];
    
    public function __construct()
    {
        echo "🔍 SaiAdmin 代码质量检查工具\n";
        echo "================================\n\n";
    }
    
    /**
     * 执行完整检查
     */
    public function runFullCheck()
    {
        $this->checkDatabaseQueries();
        $this->checkSecurityIssues();
        $this->checkPerformanceIssues();
        $this->checkCodeDuplication();
        $this->generateReport();
    }
    
    /**
     * 检查数据库查询问题
     */
    private function checkDatabaseQueries()
    {
        echo "📊 检查数据库查询优化...\n";
        
        $files = $this->getPhpFiles('webman/plugin/saiadmin/app');
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // 检查N+1查询问题
            if (preg_match_all('/\$\w+->(\w+)\(\)->toArray\(\)/', $content, $matches)) {
                $this->issues[] = [
                    'type' => 'database',
                    'level' => 'warning',
                    'file' => $file,
                    'message' => '可能存在N+1查询问题，建议使用with()预加载关联数据',
                    'line' => $this->getLineNumber($content, $matches[0][0])
                ];
            }
            
            // 检查原生SQL查询
            if (preg_match_all('/whereRaw\(/', $content, $matches)) {
                $this->issues[] = [
                    'type' => 'database',
                    'level' => 'info',
                    'file' => $file,
                    'message' => '使用了原生SQL查询，请确保已做SQL注入防护',
                    'line' => $this->getLineNumber($content, 'whereRaw(')
                ];
            }
            
            // 检查缺少索引的查询
            if (preg_match_all('/where\([\'"](\w+)[\'"]/', $content, $matches)) {
                foreach ($matches[1] as $field) {
                    if (!in_array($field, ['id', 'status', 'create_time'])) {
                        $this->suggestions[] = [
                            'type' => 'database',
                            'file' => $file,
                            'message' => "字段 '{$field}' 可能需要添加数据库索引以提升查询性能"
                        ];
                    }
                }
            }
        }
    }
    
    /**
     * 检查安全问题
     */
    private function checkSecurityIssues()
    {
        echo "🔒 检查安全问题...\n";
        
        $files = $this->getPhpFiles('webman/plugin/saiadmin/app');
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // 检查直接输出用户输入
            if (preg_match_all('/echo\s+\$_(GET|POST|REQUEST)/', $content, $matches)) {
                $this->issues[] = [
                    'type' => 'security',
                    'level' => 'error',
                    'file' => $file,
                    'message' => '直接输出用户输入可能导致XSS攻击',
                    'line' => $this->getLineNumber($content, $matches[0][0])
                ];
            }
            
            // 检查SQL拼接
            if (preg_match_all('/\$sql\s*\.=.*\$/', $content, $matches)) {
                $this->issues[] = [
                    'type' => 'security',
                    'level' => 'error',
                    'file' => $file,
                    'message' => 'SQL字符串拼接可能导致SQL注入攻击',
                    'line' => $this->getLineNumber($content, $matches[0][0])
                ];
            }
            
            // 检查文件上传安全
            if (preg_match_all('/move_uploaded_file/', $content, $matches)) {
                $this->suggestions[] = [
                    'type' => 'security',
                    'file' => $file,
                    'message' => '文件上传功能请确保验证文件类型和大小限制'
                ];
            }
        }
    }
    
    /**
     * 检查性能问题
     */
    private function checkPerformanceIssues()
    {
        echo "⚡ 检查性能问题...\n";
        
        $files = $this->getPhpFiles('webman/plugin/saiadmin/app');
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // 检查循环中的数据库查询
            if (preg_match_all('/for\s*\(.*\)\s*\{[^}]*\$\w+->find/', $content, $matches)) {
                $this->issues[] = [
                    'type' => 'performance',
                    'level' => 'warning',
                    'file' => $file,
                    'message' => '循环中执行数据库查询会影响性能，建议批量查询',
                    'line' => $this->getLineNumber($content, $matches[0][0])
                ];
            }
            
            // 检查大数组操作
            if (preg_match_all('/array_merge\s*\(.*\$\w+.*\)/', $content, $matches)) {
                $this->suggestions[] = [
                    'type' => 'performance',
                    'file' => $file,
                    'message' => '大数组合并操作可能影响性能，考虑使用array_merge_recursive或其他优化方法'
                ];
            }
            
            // 检查未使用的变量
            preg_match_all('/\$(\w+)\s*=/', $content, $assignments);
            preg_match_all('/\$(\w+)(?![a-zA-Z0-9_])/', $content, $usages);
            
            if (!empty($assignments[1])) {
                $assigned = array_unique($assignments[1]);
                $used = array_unique($usages[1]);
                $unused = array_diff($assigned, $used);
                
                foreach ($unused as $var) {
                    if (!in_array($var, ['this', 'request', 'response'])) {
                        $this->suggestions[] = [
                            'type' => 'performance',
                            'file' => $file,
                            'message' => "未使用的变量: \${$var}"
                        ];
                    }
                }
            }
        }
    }
    
    /**
     * 检查代码重复
     */
    private function checkCodeDuplication()
    {
        echo "📋 检查代码重复...\n";
        
        $files = $this->getPhpFiles('webman/plugin/saiadmin/app');
        $codeBlocks = [];
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            $lines = explode("\n", $content);
            
            // 检查重复的代码块（5行以上）
            for ($i = 0; $i < count($lines) - 5; $i++) {
                $block = implode("\n", array_slice($lines, $i, 5));
                $block = trim(preg_replace('/\s+/', ' ', $block));
                
                if (strlen($block) > 50) { // 忽略太短的代码块
                    if (!isset($codeBlocks[$block])) {
                        $codeBlocks[$block] = [];
                    }
                    $codeBlocks[$block][] = ['file' => $file, 'line' => $i + 1];
                }
            }
        }
        
        foreach ($codeBlocks as $block => $locations) {
            if (count($locations) > 1) {
                $this->issues[] = [
                    'type' => 'duplication',
                    'level' => 'info',
                    'message' => '发现重复代码块，建议提取为公共方法',
                    'locations' => $locations,
                    'code' => substr($block, 0, 100) . '...'
                ];
            }
        }
    }
    
    /**
     * 生成检查报告
     */
    private function generateReport()
    {
        echo "\n📋 代码质量检查报告\n";
        echo "================================\n\n";
        
        // 统计问题数量
        $errorCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'error'));
        $warningCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'warning'));
        $infoCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'info'));
        
        echo "📊 问题统计:\n";
        echo "  ❌ 错误: {$errorCount}\n";
        echo "  ⚠️  警告: {$warningCount}\n";
        echo "  ℹ️  信息: {$infoCount}\n";
        echo "  💡 建议: " . count($this->suggestions) . "\n\n";
        
        // 输出详细问题
        if (!empty($this->issues)) {
            echo "🔍 详细问题:\n";
            foreach ($this->issues as $issue) {
                $icon = $issue['level'] === 'error' ? '❌' : ($issue['level'] === 'warning' ? '⚠️' : 'ℹ️');
                echo "{$icon} [{$issue['type']}] {$issue['message']}\n";
                if (isset($issue['file'])) {
                    echo "   文件: {$issue['file']}" . (isset($issue['line']) ? ":{$issue['line']}" : "") . "\n";
                }
                echo "\n";
            }
        }
        
        // 输出优化建议
        if (!empty($this->suggestions)) {
            echo "💡 优化建议:\n";
            foreach ($this->suggestions as $suggestion) {
                echo "💡 [{$suggestion['type']}] {$suggestion['message']}\n";
                echo "   文件: {$suggestion['file']}\n\n";
            }
        }
        
        // 生成HTML报告
        $this->generateHtmlReport();
    }
    
    /**
     * 生成HTML报告
     */
    private function generateHtmlReport()
    {
        $html = $this->getHtmlTemplate();
        file_put_contents('code-quality-report.html', $html);
        echo "📄 详细报告已生成: code-quality-report.html\n";
    }
    
    /**
     * 获取PHP文件列表
     */
    private function getPhpFiles($directory)
    {
        $files = [];
        if (is_dir($directory)) {
            $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
            foreach ($iterator as $file) {
                if ($file->getExtension() === 'php') {
                    $files[] = $file->getPathname();
                }
            }
        }
        return $files;
    }
    
    /**
     * 获取代码行号
     */
    private function getLineNumber($content, $search)
    {
        $lines = explode("\n", $content);
        foreach ($lines as $num => $line) {
            if (strpos($line, $search) !== false) {
                return $num + 1;
            }
        }
        return 0;
    }
    
    /**
     * 获取HTML模板
     */
    private function getHtmlTemplate()
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>SaiAdmin 代码质量报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .error { border-color: #f56c6c; background: #fef0f0; }
        .warning { border-color: #e6a23c; background: #fdf6ec; }
        .info { border-color: #409eff; background: #ecf5ff; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SaiAdmin 代码质量报告</h1>
        <p>生成时间: ' . date('Y-m-d H:i:s') . '</p>
    </div>
    <!-- 这里可以添加更详细的HTML内容 -->
</body>
</html>';
    }
}

// 执行检查
if (php_sapi_name() === 'cli') {
    $checker = new CodeQualityChecker();
    $checker->runFullCheck();
}
