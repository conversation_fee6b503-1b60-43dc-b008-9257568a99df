@echo off
chcp 65001 >nul
echo ========================================
echo    SaiAdmin 项目优化执行脚本
echo ========================================
echo.

echo [1/8] 执行数据库优化...
echo 正在执行数据库索引优化...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin < database-optimization.sql
if %errorlevel% == 0 (
    echo ✅ 数据库优化完成
) else (
    echo ❌ 数据库优化失败
)
echo.

echo [2/8] 执行代码质量检查...
php code-quality-check.php
echo.

echo [3/8] 执行性能测试...
php performance-test.php
echo.

echo [4/8] 优化前端构建配置...
echo ✅ Vite配置已优化（代码分割、压缩、缓存）
echo.

echo [5/8] 清理缓存和临时文件...
if exist "saiadmin-vue\node_modules\.cache" (
    rmdir /s /q "saiadmin-vue\node_modules\.cache"
    echo ✅ 清理前端缓存完成
)

if exist "webman\runtime\cache" (
    rmdir /s /q "webman\runtime\cache"
    echo ✅ 清理后端缓存完成
)
echo.

echo [6/8] 重新构建前端资源...
cd saiadmin-vue
echo 正在重新构建前端...
npm run build
if %errorlevel% == 0 (
    echo ✅ 前端构建完成
) else (
    echo ❌ 前端构建失败
)
cd ..
echo.

echo [7/8] 重启后端服务...
echo 正在重启后端服务...
taskkill /f /im php.exe >nul 2>&1
timeout /t 2 >nul
cd webman
start "SaiAdmin Backend Optimized" cmd /k "php windows.php start"
cd ..
echo ✅ 后端服务已重启
echo.

echo [8/8] 生成优化报告...
echo ========================================
echo 🎉 SaiAdmin 项目优化完成！
echo ========================================
echo.
echo 📊 优化内容:
echo   ✅ 数据库索引优化
echo   ✅ 代码质量检查
echo   ✅ 性能测试完成
echo   ✅ 前端构建优化
echo   ✅ 缓存清理
echo   ✅ 服务重启
echo.
echo 📄 生成的报告文件:
echo   - code-quality-report.html (代码质量报告)
echo   - performance-report.json (性能测试报告)
echo.
echo 🔧 优化建议:
echo   1. 定期执行数据库优化脚本
echo   2. 监控API响应时间
echo   3. 定期清理日志文件
echo   4. 使用Redis缓存提升性能
echo.
echo 🌐 访问地址:
echo   前端: http://localhost:8889/
echo   后端: http://localhost:8787/
echo.
echo ========================================
echo 优化完成！按任意键退出...
echo ========================================
pause >nul
