import{e as g}from"./es-object-atoms-Ditt1eQ6.js";import{r as p}from"./dunder-proto-BvNz4iDg.js";var r,a;function l(){return a||(a=1,r=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),r}var o,c;function y(){if(c)return o;c=1;var e=g;return o=e.getPrototypeOf||null,o}var n,P;function O(){if(P)return n;P=1;var e=l(),f=y(),u=p();return n=e?function(t){return e(t)}:f?function(t){if(!t||typeof t!="object"&&typeof t!="function")throw new TypeError("getProto: not an object");return f(t)}:u?function(t){return u(t)}:null,n}export{y as a,O as b,l as r};
