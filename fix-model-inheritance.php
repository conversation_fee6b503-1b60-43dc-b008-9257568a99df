<?php
/**
 * 修复模型继承问题
 * 将使用BaseModel的模型改为使用BaseNormalModelFixed
 */

echo "🔧 修复模型继承问题\n";
echo "========================================\n\n";

// 递归搜索所有PHP文件
function findPHPFiles($dir) {
    $files = array();
    if (is_dir($dir)) {
        $handle = opendir($dir);
        while (($file = readdir($handle)) !== false) {
            if ($file != '.' && $file != '..') {
                $path = $dir . '/' . $file;
                if (is_dir($path)) {
                    $files = array_merge($files, findPHPFiles($path));
                } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                    $files[] = $path;
                }
            }
        }
        closedir($handle);
    }
    return $files;
}

// 查找所有PHP文件
$phpFiles = findPHPFiles('webman');
$modifiedFiles = array();
$errorFiles = array();

echo "[1/3] 扫描PHP文件...\n";
echo "  📁 找到 " . count($phpFiles) . " 个PHP文件\n\n";

echo "[2/3] 检查和修复模型文件...\n";

foreach ($phpFiles as $file) {
    $content = file_get_contents($file);
    $originalContent = $content;
    $modified = false;
    
    // 检查是否是模型文件且继承了BaseModel
    if (strpos($content, 'extends BaseModel') !== false) {
        echo "  🔍 发现BaseModel继承: " . $file . "\n";
        
        // 检查是否已经导入了BaseNormalModelFixed
        if (strpos($content, 'use plugin\saiadmin\basic\BaseNormalModelFixed') === false) {
            // 添加导入
            $content = str_replace(
                'use plugin\saiadmin\basic\BaseModel;',
                "use plugin\saiadmin\basic\BaseModel;\nuse plugin\saiadmin\basic\BaseNormalModelFixed;",
                $content
            );
        }
        
        // 替换继承关系
        $content = str_replace(
            'extends BaseModel',
            'extends BaseNormalModelFixed',
            $content
        );
        
        $modified = true;
    }
    
    // 检查是否有delete_time相关的代码
    if (strpos($content, 'delete_time') !== false) {
        echo "  ⚠️ 发现delete_time引用: " . $file . "\n";
        
        // 替换delete_time为deleted_at（如果需要）
        $content = str_replace('delete_time', 'deleted_at', $content);
        $modified = true;
    }
    
    // 检查是否有create_time/update_time需要替换为created_at/updated_at
    if (strpos($content, 'create_time') !== false || strpos($content, 'update_time') !== false) {
        echo "  🔄 更新时间字段命名: " . $file . "\n";
        
        $content = str_replace('create_time', 'created_at', $content);
        $content = str_replace('update_time', 'updated_at', $content);
        $modified = true;
    }
    
    // 如果有修改，保存文件
    if ($modified && $content !== $originalContent) {
        if (file_put_contents($file, $content)) {
            $modifiedFiles[] = $file;
            echo "    ✅ 已修复\n";
        } else {
            $errorFiles[] = $file;
            echo "    ❌ 修复失败\n";
        }
    }
}

echo "\n[3/3] 创建示例模型文件...\n";

// 创建示例的ArticleCategory模型
$articleCategoryModel = '<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: SaiAdmin Team (修复版本)
// +----------------------------------------------------------------------
namespace app\model;

use plugin\saiadmin\basic\BaseNormalModelFixed;

/**
 * 文章分类表模型 (修复版本)
 */
class ArticleCategory extends BaseNormalModelFixed
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = "id";

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = "sa_article_category";

    /**
     * 可填充字段
     * @var array
     */
    protected $fillable = array(
        "parent_id",
        "category_name", 
        "category_desc",
        "image",
        "sort",
        "status",
        "created_at",
        "updated_at"
    );

    /**
     * 分类标题 搜索器
     */
    public function searchCategoryNameAttr($query, $value)
    {
        $query->where("category_name", "like", "%".$value."%");
    }

    /**
     * 父级ID 搜索器
     */
    public function searchParentIdAttr($query, $value)
    {
        $query->where("parent_id", $value);
    }

    /**
     * 状态 搜索器
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where("status", $value);
    }

    /**
     * 状态 获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = array(0 => "禁用", 1 => "启用");
        return isset($status[$data["status"]]) ? $status[$data["status"]] : "未知";
    }

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(self::class, "parent_id", "id");
    }

    /**
     * 获取父分类
     */
    public function parent()
    {
        return $this->belongsTo(self::class, "parent_id", "id");
    }

    /**
     * 获取所有子分类（递归）
     */
    public function allChildren()
    {
        return $this->children()->with("allChildren");
    }
}';

// 确保目录存在
if (!is_dir('webman/app/model')) {
    mkdir('webman/app/model', 0755, true);
}

file_put_contents('webman/app/model/ArticleCategoryFixed.php', $articleCategoryModel);
echo "  ✅ 创建了示例模型: ArticleCategoryFixed.php\n";

// 创建数据库表创建脚本
$createTableSql = '-- 创建文章分类表（修复版本）
DROP TABLE IF EXISTS sa_article_category;

CREATE TABLE sa_article_category (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT "主键ID",
    parent_id int(11) NOT NULL DEFAULT 0 COMMENT "父级ID", 
    category_name varchar(50) NOT NULL DEFAULT "" COMMENT "分类名称",
    category_desc varchar(200) DEFAULT "" COMMENT "分类描述",
    image varchar(255) DEFAULT "" COMMENT "分类图片",
    sort int(11) NOT NULL DEFAULT 0 COMMENT "排序",
    status tinyint(1) NOT NULL DEFAULT 1 COMMENT "状态 0禁用 1启用",
    created_by int(11) DEFAULT 0 COMMENT "创建者",
    updated_by int(11) DEFAULT 0 COMMENT "更新者", 
    created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
    PRIMARY KEY (id),
    KEY idx_parent_id (parent_id),
    KEY idx_status (status),
    KEY idx_sort (sort),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="文章分类表";

-- 插入示例数据
INSERT INTO sa_article_category (parent_id, category_name, category_desc, sort, status) VALUES
(0, "技术文章", "技术相关文章分类", 1, 1),
(0, "生活随笔", "生活感悟和随笔", 2, 1),
(1, "PHP开发", "PHP开发技术文章", 1, 1),
(1, "前端开发", "前端开发技术文章", 2, 1);';

file_put_contents('create-article-category-table.sql', $createTableSql);
echo "  ✅ 创建了数据库表脚本: create-article-category-table.sql\n";

echo "\n========================================\n";
echo "🎉 修复完成！\n";
echo "========================================\n\n";

echo "📊 修复统计:\n";
echo "  ✅ 成功修复: " . count($modifiedFiles) . " 个文件\n";
echo "  ❌ 修复失败: " . count($errorFiles) . " 个文件\n\n";

if (!empty($modifiedFiles)) {
    echo "📁 已修复的文件:\n";
    foreach ($modifiedFiles as $file) {
        echo "  - " . $file . "\n";
    }
    echo "\n";
}

if (!empty($errorFiles)) {
    echo "❌ 修复失败的文件:\n";
    foreach ($errorFiles as $file) {
        echo "  - " . $file . "\n";
    }
    echo "\n";
}

echo "🔧 下一步操作:\n";
echo "1. 执行数据库脚本: create-article-category-table.sql\n";
echo "2. 检查模型文件是否正确继承BaseNormalModelFixed\n";
echo "3. 测试CRUD功能是否正常\n";
echo "4. 如果仍有问题，检查表前缀配置\n\n";

echo "💡 关键修复点:\n";
echo "- BaseModel → BaseNormalModelFixed (移除软删除)\n";
echo "- delete_time → deleted_at (如果需要)\n";
echo "- create_time → created_at\n";
echo "- update_time → updated_at\n";
echo "- 确保表名前缀正确 (sa_)\n";
