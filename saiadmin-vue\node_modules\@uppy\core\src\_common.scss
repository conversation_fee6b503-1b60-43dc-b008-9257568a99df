/**
* General Uppy styles that apply to everything inside the .uppy-Root container
*/

.uppy-Root {
  position: relative;
  box-sizing: border-box;
  color: $gray-800;
  font-family: $font-family-base;
  line-height: 1;
  text-align: left;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// One selector uses the dir attribute declared by the page. If that does not exist, Up<PERSON> adds a
// fallback dir attribute on the root element itself, and we need a second selector to match that.
[dir="rtl"] .uppy-Root,
.uppy-Root[dir="rtl"] {
  text-align: right;
}

.uppy-Root *,
.uppy-Root *::before,
.uppy-Root *::after {
  box-sizing: inherit;
}

.uppy-Root [hidden] {
  display: none;
}

// Utilities

.uppy-u-reset {
  top: auto;
  left: auto;
  z-index: auto;
  display: inline;
  float: none;
  clear: none;
  min-width: 0;
  max-width: none;
  min-height: 0;
  max-height: none;
  margin: 0;
  padding: 0;
  overflow: visible;
  overflow-x: visible;
  overflow-y: visible;
  color: inherit;
  font-weight: normal;
  font-size: inherit;
  font-family: inherit;
  font-style: normal;
  font-variant: normal;
  font-stretch: normal;
  line-height: 1;
  letter-spacing: normal;
  white-space: normal;
  text-align: left;
  text-transform: none;
  text-decoration: none;
  text-indent: 0;
  text-shadow: none;
  vertical-align: baseline;
  hyphens: none;
  unicode-bidi: normal;
  list-style: none;
  empty-cells: show;
  background: none;
  border: 0;
  border: medium none currentColor;
  border-radius: 0;
  border-image: none;
  border-collapse: separate;
  border-spacing: 0;
  outline: medium none invert;
  box-shadow: none;
  transform: none;
  transform-origin: 50% 50% 0;
  transform-style: flat;
  backface-visibility: visible;
  visibility: visible;
  cursor: auto;
  opacity: 1;
  transition: none 0s ease 0s;
  -webkit-appearance: none;
}

[dir="rtl"] .uppy-u-reset {
  text-align: right;
}

// Inputs

.uppy-c-textInput {
  padding: 6px 8px;
  font-size: 14px;
  line-height: 1.5;
  background-color: $white;
  border: 1px solid #ddd;
  border-radius: 4px;

  .uppy-size--md & {
    padding: 8px 10px;
  }

  &:focus {
    border-color: rgba($blue, 0.6);
    outline: none;
    box-shadow: 0 0 0 3px rgba($blue, 0.15);
  }

  [data-uppy-theme="dark"] & {
    color: $gray-200;
    background-color: $gray-800;
    border-color: $gray-800;
  }

  [data-uppy-theme="dark"] &:focus {
    border-color: $gray-700;
    box-shadow: none;
  }
}

// Icon

// https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4
.uppy-c-icon {
  display: inline-block;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
  fill: currentColor;
}

// Buttons

.uppy-c-btn {
  display: inline-block;
  font-weight: 500;
  font-size: 16px;
  font-family: inherit;
  line-height: 1;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  transition-duration: 0.3s;
  transition-property: background-color, color;
  user-select: none;

  // Override right-to-left variant of the uppy-u-reset class
  [dir="rtl"] & { text-align: center; }
}

.uppy-c-btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.uppy-c-btn::-moz-focus-inner {
  border: 0;
}

.uppy-c-btn-primary {
  padding: 10px 18px;
  color: $white;
  font-size: 14px;
  background-color: $blue;
  border-radius: 4px;

  &:hover {
    background-color: darken($blue, 10%);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($blue, 0.4);
  }

  .uppy-size--md & {
    padding: 13px 22px;
  }

  [data-uppy-theme="dark"] & {
    color: $gray-200;

    @include blue-border-focus--dark;
  }
}

.uppy-c-btn-link {
  padding: 10px 15px;
  color: $gray-700;
  font-size: 14px;
  line-height: 1;
  background-color: transparent;
  border-radius: 4px;

  &:hover {
    color: $gray-800;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($blue, 0.25);
  }

  .uppy-size--md & {
    padding: 13px 18px;
  }

  [data-uppy-theme="dark"] & {
    color: $gray-200;

    @include blue-border-focus--dark;
  }

  [data-uppy-theme="dark"] &:hover {
    color: $gray-500;
  }
}

.uppy-c-btn--small {
  padding: 7px 16px;
  font-size: 0.9em;
  border-radius: 2px;

  .uppy-size--md & {
    padding: 8px 10px;
    border-radius: 2px;
  }
}
