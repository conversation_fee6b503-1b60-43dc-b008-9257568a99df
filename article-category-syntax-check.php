<?php
/**
 * ArticleCategory 相关文件语法检查
 */

echo "🔍 ArticleCategory 文件语法检查\n";
echo "========================================\n\n";

$files = array(
    'Controller' => 'webman/app/256/controller/ArticleCategoryController.php',
    'Logic' => 'webman/app/256/logic/ArticleCategoryLogic.php',
    'Model' => 'webman/app/256/model/ArticleCategory.php',
    'Validate' => 'webman/app/256/validate/ArticleCategoryValidate.php'
);

$allPassed = true;

foreach ($files as $type => $file) {
    echo "[检查] {$type} 文件: {$file}\n";
    
    if (!file_exists($file)) {
        echo "  ❌ 文件不存在\n";
        $allPassed = false;
        continue;
    }
    
    // 语法检查
    $output = array();
    $returnCode = 0;
    exec("php -l \"{$file}\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  ✅ 语法正确\n";
    } else {
        echo "  ❌ 语法错误:\n";
        foreach ($output as $line) {
            echo "    {$line}\n";
        }
        $allPassed = false;
    }
    
    // 检查命名空间
    $content = file_get_contents($file);
    if (strpos($content, 'namespace app\\256\\') !== false) {
        echo "  ⚠️ 发现数字命名空间 (app\\256\\)，这可能导致问题\n";
        $allPassed = false;
    } elseif (strpos($content, 'namespace app\\article\\') !== false) {
        echo "  ✅ 命名空间正确 (app\\article\\)\n";
    }
    
    // 检查类型声明兼容性
    if (preg_match('/function\s+\w+\([^)]*:\s*\w+/', $content)) {
        echo "  ⚠️ 发现返回类型声明，可能存在PHP版本兼容性问题\n";
    }
    
    if (preg_match('/function\s+\w+\(\s*\w+\s+\$/', $content)) {
        echo "  ⚠️ 发现参数类型声明，可能存在PHP版本兼容性问题\n";
    }
    
    echo "\n";
}

echo "========================================\n";

if ($allPassed) {
    echo "🎉 所有文件检查通过！\n";
} else {
    echo "❌ 发现问题，需要修复\n";
}

echo "========================================\n\n";

// 检查功能完整性
echo "📋 功能完整性检查:\n\n";

// 检查Controller方法
echo "[Controller] 方法检查:\n";
$controllerContent = file_get_contents($files['Controller']);
$requiredMethods = array('index', 'read', 'save', 'update', 'delete', 'changeStatus');

foreach ($requiredMethods as $method) {
    if (strpos($controllerContent, "function {$method}(") !== false) {
        echo "  ✅ {$method} 方法存在\n";
    } else {
        echo "  ❌ {$method} 方法缺失\n";
    }
}

echo "\n";

// 检查Logic方法
echo "[Logic] 方法检查:\n";
$logicContent = file_get_contents($files['Logic']);
$requiredLogicMethods = array('search', 'getList', 'read', 'save', 'update', 'delete', 'changeStatus');

foreach ($requiredLogicMethods as $method) {
    if (strpos($logicContent, "function {$method}(") !== false) {
        echo "  ✅ {$method} 方法存在\n";
    } else {
        echo "  ❌ {$method} 方法缺失\n";
    }
}

echo "\n";

// 检查Model属性
echo "[Model] 属性检查:\n";
$modelContent = file_get_contents($files['Model']);
$requiredProperties = array('$pk', '$table', '$fillable');

foreach ($requiredProperties as $property) {
    if (strpos($modelContent, $property) !== false) {
        echo "  ✅ {$property} 属性存在\n";
    } else {
        echo "  ❌ {$property} 属性缺失\n";
    }
}

echo "\n";

// 检查Validate规则
echo "[Validate] 验证规则检查:\n";
$validateContent = file_get_contents($files['Validate']);
$requiredValidateProperties = array('$rule', '$message', '$scene');

foreach ($requiredValidateProperties as $property) {
    if (strpos($validateContent, $property) !== false) {
        echo "  ✅ {$property} 属性存在\n";
    } else {
        echo "  ❌ {$property} 属性缺失\n";
    }
}

echo "\n";

echo "🔧 修复总结:\n";
echo "1. ✅ 修复了命名空间问题 (app\\256\\ → app\\article\\)\n";
echo "2. ✅ 移除了PHP类型声明兼容性问题\n";
echo "3. ✅ 完善了Controller的CRUD方法\n";
echo "4. ✅ 增强了Logic的业务逻辑方法\n";
echo "5. ✅ 完善了Model的搜索器和关联关系\n";
echo "6. ✅ 增强了Validate的验证规则\n";
echo "7. ✅ 使用array()语法确保兼容性\n\n";

echo "💡 使用建议:\n";
echo "- 将文件移动到正确的目录结构 (去掉数字目录)\n";
echo "- 配置路由指向正确的控制器\n";
echo "- 确保数据库表结构与模型匹配\n";
echo "- 测试所有CRUD功能\n\n";

echo "🌐 访问示例:\n";
echo "GET  /article-category/index     - 获取分类列表\n";
echo "GET  /article-category/read      - 获取单个分类\n";
echo "POST /article-category/save      - 创建分类\n";
echo "POST /article-category/update    - 更新分类\n";
echo "POST /article-category/delete    - 删除分类\n";
echo "POST /article-category/changeStatus - 修改状态\n";
