<?php
/**
 * Sai<PERSON>dmin 日志监控脚本
 * 实时监控系统日志并分析异常
 */

class LogMonitor
{
    private $config = [
        'log_files' => [
            'webman/runtime/logs/workerman.log',
            'webman/runtime/logs/error.log',
            'performance_monitor.log',
            'performance_alerts.log'
        ],
        'check_interval' => 5,      // 检查间隔（秒）
        'max_lines' => 1000,        // 最大显示行数
        'alert_keywords' => [
            'error', 'exception', 'fatal', 'critical', 
            'warning', 'fail', 'timeout', 'denied'
        ],
        'ignore_patterns' => [
            '/Notice:/',
            '/Deprecated:/',
        ]
    ];
    
    private $filePositions = [];
    private $alerts = [];
    private $stats = [
        'total_lines' => 0,
        'error_count' => 0,
        'warning_count' => 0,
        'last_activity' => null
    ];
    
    public function __construct()
    {
        echo "📋 SaiAdmin 日志监控启动\n";
        echo "监控文件: " . count($this->config['log_files']) . " 个\n";
        echo "检查间隔: {$this->config['check_interval']}秒\n";
        echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
        echo "========================================\n\n";
        
        // 初始化文件位置
        foreach ($this->config['log_files'] as $file) {
            if (file_exists($file)) {
                $this->filePositions[$file] = filesize($file);
            } else {
                $this->filePositions[$file] = 0;
            }
        }
    }
    
    /**
     * 开始监控
     */
    public function start()
    {
        while (true) {
            $this->checkLogs();
            $this->displayStatus();
            sleep($this->config['check_interval']);
        }
    }
    
    /**
     * 检查日志文件
     */
    private function checkLogs()
    {
        foreach ($this->config['log_files'] as $file) {
            if (!file_exists($file)) {
                continue;
            }
            
            $currentSize = filesize($file);
            $lastPosition = $this->filePositions[$file];
            
            if ($currentSize > $lastPosition) {
                $this->processNewLines($file, $lastPosition, $currentSize);
                $this->filePositions[$file] = $currentSize;
            } elseif ($currentSize < $lastPosition) {
                // 文件被轮转或清空
                $this->filePositions[$file] = 0;
                $this->processNewLines($file, 0, $currentSize);
            }
        }
    }
    
    /**
     * 处理新的日志行
     */
    private function processNewLines($file, $start, $end)
    {
        $handle = fopen($file, 'r');
        if (!$handle) {
            return;
        }
        
        fseek($handle, $start);
        
        while (($line = fgets($handle)) !== false && ftell($handle) <= $end) {
            $this->processLogLine(trim($line), $file);
        }
        
        fclose($handle);
    }
    
    /**
     * 处理单行日志
     */
    private function processLogLine($line, $file)
    {
        if (empty($line)) {
            return;
        }
        
        $this->stats['total_lines']++;
        $this->stats['last_activity'] = time();
        
        // 检查是否需要忽略
        foreach ($this->config['ignore_patterns'] as $pattern) {
            if (preg_match($pattern, $line)) {
                return;
            }
        }
        
        // 分析日志级别和内容
        $logInfo = $this->parseLogLine($line, $file);
        
        // 检查告警关键词
        $this->checkAlerts($logInfo);
        
        // 更新统计
        $this->updateStats($logInfo);
    }
    
    /**
     * 解析日志行
     */
    private function parseLogLine($line, $file)
    {
        $info = [
            'file' => basename($file),
            'line' => $line,
            'timestamp' => time(),
            'level' => 'info',
            'message' => $line
        ];
        
        // 尝试解析时间戳
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            $info['parsed_time'] = $matches[1];
        }
        
        // 尝试解析日志级别
        if (preg_match('/\b(DEBUG|INFO|NOTICE|WARNING|ERROR|CRITICAL|ALERT|EMERGENCY)\b/i', $line, $matches)) {
            $info['level'] = strtolower($matches[1]);
        }
        
        // 检查关键词
        $lowerLine = strtolower($line);
        foreach ($this->config['alert_keywords'] as $keyword) {
            if (strpos($lowerLine, $keyword) !== false) {
                $info['alert_keyword'] = $keyword;
                break;
            }
        }
        
        return $info;
    }
    
    /**
     * 检查告警
     */
    private function checkAlerts($logInfo)
    {
        if (isset($logInfo['alert_keyword']) || in_array($logInfo['level'], ['error', 'critical', 'alert', 'emergency'])) {
            $alert = [
                'timestamp' => $logInfo['timestamp'],
                'file' => $logInfo['file'],
                'level' => $logInfo['level'],
                'message' => $logInfo['message'],
                'keyword' => $logInfo['alert_keyword'] ?? null
            ];
            
            $this->alerts[] = $alert;
            
            // 保持最近100个告警
            if (count($this->alerts) > 100) {
                array_shift($this->alerts);
            }
            
            // 记录告警到文件
            $this->logAlert($alert);
        }
    }
    
    /**
     * 更新统计信息
     */
    private function updateStats($logInfo)
    {
        switch ($logInfo['level']) {
            case 'error':
            case 'critical':
            case 'alert':
            case 'emergency':
                $this->stats['error_count']++;
                break;
            case 'warning':
                $this->stats['warning_count']++;
                break;
        }
    }
    
    /**
     * 显示监控状态
     */
    private function displayStatus()
    {
        // 清屏
        if (PHP_OS_FAMILY === 'Windows') {
            system('cls');
        } else {
            system('clear');
        }
        
        echo "📋 SaiAdmin 日志监控 - " . date('Y-m-d H:i:s') . "\n";
        echo "========================================\n\n";
        
        // 统计信息
        echo "📊 统计信息:\n";
        echo sprintf("  总日志行数: %d\n", $this->stats['total_lines']);
        echo sprintf("  错误数量: %d\n", $this->stats['error_count']);
        echo sprintf("  警告数量: %d\n", $this->stats['warning_count']);
        echo sprintf("  最后活动: %s\n", 
            $this->stats['last_activity'] ? date('H:i:s', $this->stats['last_activity']) : '无'
        );
        echo "\n";
        
        // 文件状态
        echo "📁 监控文件:\n";
        foreach ($this->config['log_files'] as $file) {
            $status = file_exists($file) ? '✅' : '❌';
            $size = file_exists($file) ? $this->formatBytes(filesize($file)) : '0B';
            echo sprintf("  %s %s (%s)\n", $status, basename($file), $size);
        }
        echo "\n";
        
        // 最近告警
        $recentAlerts = array_slice($this->alerts, -10);
        if (!empty($recentAlerts)) {
            echo "⚠️ 最近告警 (最新10条):\n";
            foreach (array_reverse($recentAlerts) as $alert) {
                $icon = $this->getAlertIcon($alert['level']);
                $time = date('H:i:s', $alert['timestamp']);
                $message = strlen($alert['message']) > 80 
                    ? substr($alert['message'], 0, 80) . '...' 
                    : $alert['message'];
                echo sprintf("  %s [%s] %s: %s\n", $icon, $time, $alert['file'], $message);
            }
        } else {
            echo "✅ 无告警\n";
        }
        echo "\n";
        
        // 实时日志流（最新5行）
        echo "📄 实时日志流:\n";
        $this->displayRecentLogs();
        
        echo "\n按 Ctrl+C 停止监控\n";
    }
    
    /**
     * 显示最近的日志
     */
    private function displayRecentLogs()
    {
        $recentLogs = [];
        
        foreach ($this->config['log_files'] as $file) {
            if (!file_exists($file)) {
                continue;
            }
            
            $lines = $this->getTailLines($file, 3);
            foreach ($lines as $line) {
                if (!empty(trim($line))) {
                    $recentLogs[] = [
                        'file' => basename($file),
                        'line' => trim($line),
                        'time' => time()
                    ];
                }
            }
        }
        
        // 按时间排序并显示最新的5条
        usort($recentLogs, function($a, $b) {
            return $b['time'] - $a['time'];
        });
        
        $recentLogs = array_slice($recentLogs, 0, 5);
        
        foreach ($recentLogs as $log) {
            $message = strlen($log['line']) > 100 
                ? substr($log['line'], 0, 100) . '...' 
                : $log['line'];
            echo sprintf("  📝 %s: %s\n", $log['file'], $message);
        }
        
        if (empty($recentLogs)) {
            echo "  (暂无新日志)\n";
        }
    }
    
    /**
     * 获取文件末尾几行
     */
    private function getTailLines($file, $lines)
    {
        $handle = fopen($file, 'r');
        if (!$handle) {
            return [];
        }
        
        $linecounter = $lines;
        $pos = -2;
        $beginning = false;
        $text = [];
        
        while ($linecounter > 0) {
            $t = " ";
            while ($t != "\n") {
                if (fseek($handle, $pos, SEEK_END) == -1) {
                    $beginning = true;
                    break;
                }
                $t = fgetc($handle);
                $pos--;
            }
            $linecounter--;
            if ($beginning) {
                rewind($handle);
            }
            $text[$lines - $linecounter - 1] = fgets($handle);
            if ($beginning) break;
        }
        
        fclose($handle);
        return array_reverse($text);
    }
    
    /**
     * 获取告警图标
     */
    private function getAlertIcon($level)
    {
        switch ($level) {
            case 'critical':
            case 'alert':
            case 'emergency':
                return '🔴';
            case 'error':
                return '🟠';
            case 'warning':
                return '🟡';
            default:
                return '🔵';
        }
    }
    
    /**
     * 记录告警到文件
     */
    private function logAlert($alert)
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s', $alert['timestamp']),
            'alert' => $alert
        ];
        
        file_put_contents(
            'log_alerts.log',
            json_encode($logEntry) . "\n",
            FILE_APPEND | LOCK_EX
        );
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    /**
     * 获取监控统计
     */
    public function getStats()
    {
        return [
            'stats' => $this->stats,
            'alerts_count' => count($this->alerts),
            'monitored_files' => count($this->config['log_files']),
            'active_files' => count(array_filter($this->config['log_files'], 'file_exists'))
        ];
    }
}

// 启动日志监控
$monitor = new LogMonitor();
$monitor->start();
