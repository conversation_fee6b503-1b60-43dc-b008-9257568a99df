import{e as m,r as F,f as M,c as P,j as c,k as h,t as a,H,p as C,M as j,z,bF as W,F as T,P as N,h as S,u as K}from"./@vue-9ZIPiVZG.js";/*!
  * vue-color-kit v1.0.6
  * (c) 2023 
  * @license MIT
  */function y(e){let t={r:0,g:0,b:0,a:1};/#/.test(e)?t=Y(e):/rgb/.test(e)?t=D(e):typeof e=="string"?t=D(`rgba(${e})`):Object.prototype.toString.call(e)==="[object Object]"&&(t=e);const{r:s,g:r,b:n,a:i}=t,{h:o,s:l,v:u}=G(t);return{r:s,g:r,b:n,a:i===void 0?1:i,h:o,s:l,v:u}}function A(e){const t=document.createElement("canvas"),s=t.getContext("2d"),r=e*2;return t.width=r,t.height=r,s.fillStyle="#ffffff",s.fillRect(0,0,r,r),s.fillStyle="#ccd5db",s.fillRect(0,0,e,e),s.fillRect(e,e,e,e),t}function w(e,t,s,r,n,i){const o=e==="l",l=t.createLinearGradient(0,0,o?s:0,o?0:r);l.addColorStop(.01,n),l.addColorStop(.99,i),t.fillStyle=l,t.fillRect(0,0,s,r)}function V({r:e,g:t,b:s},r){const n=o=>("0"+Number(o).toString(16)).slice(-2),i=`#${n(e)}${n(t)}${n(s)}`;return r?i.toUpperCase():i}function Y(e){e=e.slice(1);const t=s=>parseInt(s,16)||0;return{r:t(e.slice(0,2)),g:t(e.slice(2,4)),b:t(e.slice(4,6))}}function D(e){return typeof e=="string"?(e=(/rgba?\((.*?)\)/.exec(e)||["","0,0,0,1"])[1].split(","),{r:Number(e[0])||0,g:Number(e[1])||0,b:Number(e[2])||0,a:Number(e[3]?e[3]:1)}):e}function G({r:e,g:t,b:s}){e=e/255,t=t/255,s=s/255;const r=Math.max(e,t,s),n=Math.min(e,t,s),i=r-n;let o=0;r===n?o=0:r===e?t>=s?o=60*(t-s)/i:o=60*(t-s)/i+360:r===t?o=60*(s-e)/i+120:r===s&&(o=60*(e-t)/i+240),o=Math.floor(o);let l=parseFloat((r===0?0:1-n/r).toFixed(2)),u=parseFloat(r.toFixed(2));return{h:o,s:l,v:u}}var B=m({props:{color:{type:String,default:"#000000"},hsv:{type:Object,default:null},size:{type:Number,default:152}},emits:["selectSaturation"],data(){return{slideSaturationStyle:{}}},mounted(){this.renderColor(),this.renderSlide()},methods:{renderColor(){const e=this.$refs.canvasSaturation,t=this.size,s=e.getContext("2d");e.width=t,e.height=t,s.fillStyle=this.color,s.fillRect(0,0,t,t),w("l",s,t,t,"#FFFFFF","rgba(255,255,255,0)"),w("p",s,t,t,"rgba(0,0,0,0)","#000000")},renderSlide(){this.slideSaturationStyle={left:this.hsv.s*this.size-5+"px",top:(1-this.hsv.v)*this.size-5+"px"}},selectSaturation(e){const{top:t,left:s}=this.$el.getBoundingClientRect(),r=e.target.getContext("2d"),n=o=>{let l=o.clientX-s,u=o.clientY-t;l<0&&(l=0),u<0&&(u=0),l>this.size&&(l=this.size),u>this.size&&(u=this.size),this.slideSaturationStyle={left:l-5+"px",top:u-5+"px"};const d=r.getImageData(Math.min(l,this.size-1),Math.min(u,this.size-1),1,1),[p,g,v]=d.data;this.$emit("selectSaturation",{r:p,g,b:v})};n(e);const i=()=>{document.removeEventListener("mousemove",n),document.removeEventListener("mouseup",i)};document.addEventListener("mousemove",n),document.addEventListener("mouseup",i)}}});const U={ref:"canvasSaturation"};function X(e,t,s,r,n,i){return h(),c("div",{class:"saturation",onMousedown:t[1]||(t[1]=H((...o)=>e.selectSaturation&&e.selectSaturation(...o),["prevent","stop"]))},[a("canvas",U,null,512),a("div",{style:e.slideSaturationStyle,class:"slide"},null,4)],32)}B.render=X;B.__file="src/color/Saturation.vue";var L=m({props:{hsv:{type:Object,default:null},width:{type:Number,default:15},height:{type:Number,default:152}},emits:["selectHue"],data(){return{slideHueStyle:{}}},mounted(){this.renderColor(),this.renderSlide()},methods:{renderColor(){const e=this.$refs.canvasHue,t=this.width,s=this.height,r=e.getContext("2d");e.width=t,e.height=s;const n=r.createLinearGradient(0,0,0,s);n.addColorStop(0,"#FF0000"),n.addColorStop(.17*1,"#FF00FF"),n.addColorStop(.17*2,"#0000FF"),n.addColorStop(.17*3,"#00FFFF"),n.addColorStop(.17*4,"#00FF00"),n.addColorStop(.17*5,"#FFFF00"),n.addColorStop(1,"#FF0000"),r.fillStyle=n,r.fillRect(0,0,t,s)},renderSlide(){this.slideHueStyle={top:(1-this.hsv.h/360)*this.height-2+"px"}},selectHue(e){const{top:t}=this.$el.getBoundingClientRect(),s=e.target.getContext("2d"),r=i=>{let o=i.clientY-t;o<0&&(o=0),o>this.height&&(o=this.height),this.slideHueStyle={top:o-2+"px"};const l=s.getImageData(0,Math.min(o,this.height-1),1,1),[u,d,p]=l.data;this.$emit("selectHue",{r:u,g:d,b:p})};r(e);const n=()=>{document.removeEventListener("mousemove",r),document.removeEventListener("mouseup",n)};document.addEventListener("mousemove",r),document.addEventListener("mouseup",n)}}});const q={ref:"canvasHue"};function J(e,t,s,r,n,i){return h(),c("div",{class:"hue",onMousedown:t[1]||(t[1]=H((...o)=>e.selectHue&&e.selectHue(...o),["prevent","stop"]))},[a("canvas",q,null,512),a("div",{style:e.slideHueStyle,class:"slide"},null,4)],32)}L.render=J;L.__file="src/color/Hue.vue";var E=m({props:{color:{type:String,default:"#000000"},rgba:{type:Object,default:null},width:{type:Number,default:15},height:{type:Number,default:152}},emits:["selectAlpha"],data(){return{slideAlphaStyle:{},alphaSize:5}},watch:{color(){this.renderColor()},"rgba.a"(){this.renderSlide()}},mounted(){this.renderColor(),this.renderSlide()},methods:{renderColor(){const e=this.$refs.canvasAlpha,t=this.width,s=this.height,r=this.alphaSize,n=A(r),i=e.getContext("2d");e.width=t,e.height=s,i.fillStyle=i.createPattern(n,"repeat"),i.fillRect(0,0,t,s),w("p",i,t,s,"rgba(255,255,255,0)",this.color)},renderSlide(){this.slideAlphaStyle={top:this.rgba.a*this.height-2+"px"}},selectAlpha(e){const{top:t}=this.$el.getBoundingClientRect(),s=n=>{let i=n.clientY-t;i<0&&(i=0),i>this.height&&(i=this.height);let o=parseFloat((i/this.height).toFixed(2));this.$emit("selectAlpha",o)};s(e);const r=()=>{document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",r)};document.addEventListener("mousemove",s),document.addEventListener("mouseup",r)}}});const Q={ref:"canvasAlpha"};function Z(e,t,s,r,n,i){return h(),c("div",{class:"color-alpha",onMousedown:t[1]||(t[1]=H((...o)=>e.selectAlpha&&e.selectAlpha(...o),["prevent","stop"]))},[a("canvas",Q,null,512),a("div",{style:e.slideAlphaStyle,class:"slide"},null,4)],32)}E.render=Z;E.__file="src/color/Alpha.vue";var x=m({props:{color:{type:String,default:"#000000"},width:{type:Number,default:100},height:{type:Number,default:30}},data(){return{alphaSize:5}},watch:{color(){this.renderColor()}},mounted(){this.renderColor()},methods:{renderColor(){const e=this.$el,t=this.width,s=this.height,r=this.alphaSize,n=A(r),i=e.getContext("2d");e.width=t,e.height=s,i.fillStyle=i.createPattern(n,"repeat"),i.fillRect(0,0,t,s),i.fillStyle=this.color,i.fillRect(0,0,t,s)}}});function _(e,t,s,r,n,i){return h(),c("canvas")}x.render=_;x.__file="src/color/Preview.vue";var O=m({props:{suckerCanvas:{type:Object,default:null},suckerArea:{type:Array,default:()=>[]}},data(){return{isOpenSucker:!1,suckerPreview:null,isSucking:!1}},watch:{suckerCanvas(e){this.isSucking=!1,this.suckColor(e)}},methods:{openSucker(){this.isOpenSucker?this.keydownHandler({keyCode:27}):(this.isOpenSucker=!0,this.isSucking=!0,this.$emit("openSucker",!0),document.addEventListener("keydown",this.keydownHandler))},keydownHandler(e){e.keyCode===27&&(this.isOpenSucker=!1,this.isSucking=!1,this.$emit("openSucker",!1),document.removeEventListener("keydown",this.keydownHandler),document.removeEventListener("mousemove",this.mousemoveHandler),document.removeEventListener("mouseup",this.mousemoveHandler),this.suckerPreview&&(document.body.removeChild(this.suckerPreview),this.suckerPreview=null))},mousemoveHandler(e){const{clientX:t,clientY:s}=e,{top:r,left:n,width:i,height:o}=this.suckerCanvas.getBoundingClientRect(),l=t-n,u=s-r,p=this.suckerCanvas.getContext("2d").getImageData(Math.min(l,i-1),Math.min(u,o-1),1,1);let[g,v,b,$]=p.data;$=parseFloat(($/255).toFixed(2));const f=this.suckerPreview.style;Object.assign(f,{position:"absolute",left:t+20+"px",top:s-36+"px",width:"24px",height:"24px",borderRadius:"50%",border:"2px solid #fff",boxShadow:"0 0 8px 0 rgba(0, 0, 0, 0.16)",background:`rgba(${g}, ${v}, ${b}, ${$})`,zIndex:95}),this.suckerArea.length&&t>=this.suckerArea[0]&&s>=this.suckerArea[1]&&t<=this.suckerArea[2]&&s<=this.suckerArea[3]?f.display="":f.display="none"},suckColor(e){e&&e.tagName!=="CANVAS"||(this.suckerPreview=document.createElement("div"),this.suckerPreview&&document.body.appendChild(this.suckerPreview),document.addEventListener("mousemove",this.mousemoveHandler),document.addEventListener("mouseup",this.mousemoveHandler),e.addEventListener("click",t=>{const{clientX:s,clientY:r}=t,{top:n,left:i,width:o,height:l}=e.getBoundingClientRect(),u=s-i,d=r-n,g=e.getContext("2d").getImageData(Math.min(u,o-1),Math.min(d,l-1),1,1);let[v,b,$,f]=g.data;f=parseFloat((f/255).toFixed(2)),this.$emit("selectSucker",{r:v,g:b,b:$,a:f})}))}}});const ee=a("path",{d:"M13.1,8.2l5.6,5.6c0.4,0.4,0.5,1.1,0.1,1.5s-1.1,0.5-1.5,0.1c0,0-0.1,0-0.1-0.1l-1.4-1.4l-7.7,7.7C7.9,21.9,7.6,22,7.3,22H3.1C2.5,22,2,21.5,2,20.9l0,0v-4.2c0-0.3,0.1-0.6,0.3-0.8l5.8-5.8C8.5,9.7,9.2,9.6,9.7,10s0.5,1.1,0.1,1.5c0,0,0,0.1-0.1,0.1l-5.5,5.5v2.7h2.7l7.4-7.4L8.7,6.8c-0.5-0.4-0.5-1-0.1-1.5s1.1-0.5,1.5-0.1c0,0,0.1,0,0.1,0.1l1.4,1.4l3.5-3.5c1.6-1.6,4.1-1.6,5.8-0.1c1.6,1.6,1.6,4.1,0.1,5.8L20.9,9l-3.6,3.6c-0.4,0.4-1.1,0.5-1.5,0.1"},null,-1),te={key:1,class:"sucker",viewBox:"-16 -16 68 68",xmlns:"http://www.w3.org/2000/svg",stroke:"#9099a4"},se=a("g",{fill:"none","fill-rule":"evenodd"},[a("g",{transform:"translate(1 1)","stroke-width":"4"},[a("circle",{"stroke-opacity":".5",cx:"18",cy:"18",r:"18"}),a("path",{d:"M36 18c0-9.94-8.06-18-18-18"},[a("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})])])],-1);function oe(e,t,s,r,n,i){return h(),c("div",null,[e.isSucking?C("v-if",!0):(h(),c("svg",{key:0,class:[{active:e.isOpenSucker},"sucker"],xmlns:"http://www.w3.org/2000/svg",viewBox:"-12 -12 48 48",onClick:t[1]||(t[1]=(...o)=>e.openSucker&&e.openSucker(...o))},[ee],2)),e.isSucking?(h(),c("svg",te,[se])):C("v-if",!0)])}O.render=oe;O.__file="src/color/Sucker.vue";var R=m({props:{name:{type:String,default:""},color:{type:String,default:""}},emits:["inputColor","inputFocus","inputBlur"],setup(e,{emit:t}){return{modelColor:P({get(){return e.color||""},set(i){t("inputColor",i)}}),handleFocus:i=>{t("inputFocus",i)},handleBlur:i=>{t("inputBlur",i)}}}});const re={class:"color-type"},ie={class:"name"};function ne(e,t,s,r,n,i){return h(),c("div",re,[a("span",ie,z(e.name),1),j(a("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>e.modelColor=o),class:"value",onFocus:t[2]||(t[2]=(...o)=>e.handleFocus&&e.handleFocus(...o)),onBlur:t[3]||(t[3]=(...o)=>e.handleBlur&&e.handleBlur(...o))},null,544),[[W,e.modelColor]])])}R.render=ne;R.__file="src/color/Box.vue";var I=m({name:"ColorPicker",props:{color:{type:String,default:"#000000"},colorsDefault:{type:Array,default:()=>[]},colorsHistoryKey:{type:String,default:""}},emits:["selectColor"],setup(e,{emit:t}){const s=F(),r=F([]),n=F();e.colorsHistoryKey&&localStorage&&(r.value=JSON.parse(localStorage.getItem(e.colorsHistoryKey))||[]),n.value=A(4).toDataURL(),M(()=>{i(s.value)});function i(l){if(!l)return;const u=r.value||[],d=u.indexOf(l);d>=0&&u.splice(d,1),u.length>=8&&(u.length=7),u.unshift(l),r.value=u||[],localStorage&&e.colorsHistoryKey&&localStorage.setItem(e.colorsHistoryKey,JSON.stringify(u))}function o(l){t("selectColor",l)}return{setColorsHistory:i,colorsHistory:r,color:s,imgAlphaBase64:n,selectColor:o}}});const le={class:"colors"},ae={key:0,class:"colors history"};function ue(e,t,s,r,n,i){return h(),c("div",null,[a("ul",le,[(h(!0),c(T,null,N(e.colorsDefault,o=>(h(),c("li",{key:o,class:"item",onClick:l=>e.selectColor(o)},[a("div",{style:{background:`url(${e.imgAlphaBase64})`},class:"alpha"},null,4),a("div",{style:{background:o},class:"color"},null,4)],8,["onClick"]))),128))]),e.colorsHistory.length?(h(),c("ul",ae,[(h(!0),c(T,null,N(e.colorsHistory,o=>(h(),c("li",{key:o,class:"item",onClick:l=>e.selectColor(o)},[a("div",{style:{background:`url(${e.imgAlphaBase64})`},class:"alpha"},null,4),a("div",{style:{background:o},class:"color"},null,4)],8,["onClick"]))),128))])):C("v-if",!0)])}I.render=ue;I.__file="src/color/Colors.vue";var k=m({components:{Saturation:B,Hue:L,Alpha:E,Preview:x,Sucker:O,Box:R,Colors:I},emits:["changeColor","openSucker","inputFocus","inputBlur"],props:{color:{type:String,default:"#000000"},theme:{type:String,default:"dark"},suckerHide:{type:Boolean,default:!0},suckerCanvas:{type:null,default:null},suckerArea:{type:Array,default:()=>[]},colorsDefault:{type:Array,default:()=>["#000000","#FFFFFF","#FF1900","#F47365","#FFB243","#FFE623","#6EFF2A","#1BC7B1","#00BEFF","#2E81FF","#5D61FF","#FF89CF","#FC3CAD","#BF3DCE","#8E00A7","rgba(0,0,0,0)"]},colorsHistoryKey:{type:String,default:"vue-colorpicker-history"}},data(){return{hueWidth:15,hueHeight:152,previewHeight:30,modelRgba:"",modelHex:"",r:0,g:0,b:0,a:1,h:0,s:0,v:0}},computed:{isLightTheme(){return this.theme==="light"},totalWidth(){return this.hueHeight+(this.hueWidth+8)*2},previewWidth(){return this.totalWidth-(this.suckerHide?0:this.previewHeight)},rgba(){return{r:this.r,g:this.g,b:this.b,a:this.a}},hsv(){return{h:this.h,s:this.s,v:this.v}},rgbString(){return`rgb(${this.r}, ${this.g}, ${this.b})`},rgbaStringShort(){return`${this.r}, ${this.g}, ${this.b}, ${this.a}`},rgbaString(){return`rgba(${this.rgbaStringShort})`},hexString(){return V(this.rgba,!0)}},created(){Object.assign(this,y(this.color)),this.setText(),this.$watch("rgba",()=>{this.$emit("changeColor",{rgba:this.rgba,hsv:this.hsv,hex:this.modelHex})})},methods:{selectSaturation(e){const{r:t,g:s,b:r,h:n,s:i,v:o}=y(e);Object.assign(this,{r:t,g:s,b:r,h:n,s:i,v:o}),this.setText()},handleFocus(e){this.$emit("inputFocus",e)},handleBlur(e){this.$emit("inputBlur",e)},selectHue(e){const{r:t,g:s,b:r,h:n,s:i,v:o}=y(e);Object.assign(this,{r:t,g:s,b:r,h:n,s:i,v:o}),this.setText(),this.$nextTick(()=>{this.$refs.saturation.renderColor(),this.$refs.saturation.renderSlide()})},selectAlpha(e){this.a=e,this.setText()},inputHex(e){const{r:t,g:s,b:r,a:n,h:i,s:o,v:l}=y(e);Object.assign(this,{r:t,g:s,b:r,a:n,h:i,s:o,v:l}),this.modelHex=e,this.modelRgba=this.rgbaStringShort,this.$nextTick(()=>{this.$refs.saturation.renderColor(),this.$refs.saturation.renderSlide(),this.$refs.hue.renderSlide()})},inputRgba(e){const{r:t,g:s,b:r,a:n,h:i,s:o,v:l}=y(e);Object.assign(this,{r:t,g:s,b:r,a:n,h:i,s:o,v:l}),this.modelHex=this.hexString,this.modelRgba=e,this.$nextTick(()=>{this.$refs.saturation.renderColor(),this.$refs.saturation.renderSlide(),this.$refs.hue.renderSlide()})},setText(){this.modelHex=this.hexString,this.modelRgba=this.rgbaStringShort},openSucker(e){this.$emit("openSucker",e)},selectSucker(e){const{r:t,g:s,b:r,a:n,h:i,s:o,v:l}=y(e);Object.assign(this,{r:t,g:s,b:r,a:n,h:i,s:o,v:l}),this.setText(),this.$nextTick(()=>{this.$refs.saturation.renderColor(),this.$refs.saturation.renderSlide(),this.$refs.hue.renderSlide()})},selectColor(e){const{r:t,g:s,b:r,a:n,h:i,s:o,v:l}=y(e);Object.assign(this,{r:t,g:s,b:r,a:n,h:i,s:o,v:l}),this.setText(),this.$nextTick(()=>{this.$refs.saturation.renderColor(),this.$refs.saturation.renderSlide(),this.$refs.hue.renderSlide()})}}});const ce={class:"color-set"};function he(e,t,s,r,n,i){const o=S("Saturation"),l=S("Hue"),u=S("Alpha"),d=S("Preview"),p=S("Sucker"),g=S("Box"),v=S("Colors");return h(),c("div",{class:["hu-color-picker",{light:e.isLightTheme}],style:{width:e.totalWidth+"px"}},[a("div",ce,[a(o,{ref:"saturation",color:e.rgbString,hsv:e.hsv,size:e.hueHeight,onSelectSaturation:e.selectSaturation},null,8,["color","hsv","size","onSelectSaturation"]),a(l,{ref:"hue",hsv:e.hsv,width:e.hueWidth,height:e.hueHeight,onSelectHue:e.selectHue},null,8,["hsv","width","height","onSelectHue"]),a(u,{ref:"alpha",color:e.rgbString,rgba:e.rgba,width:e.hueWidth,height:e.hueHeight,onSelectAlpha:e.selectAlpha},null,8,["color","rgba","width","height","onSelectAlpha"])]),a("div",{style:{height:e.previewHeight+"px"},class:"color-show"},[a(d,{color:e.rgbaString,width:e.previewWidth,height:e.previewHeight},null,8,["color","width","height"]),e.suckerHide?C("v-if",!0):(h(),c(p,{key:0,"sucker-canvas":e.suckerCanvas,"sucker-area":e.suckerArea,onOpenSucker:e.openSucker,onSelectSucker:e.selectSucker},null,8,["sucker-canvas","sucker-area","onOpenSucker","onSelectSucker"]))],4),a(g,{name:"HEX",color:e.modelHex,onInputColor:e.inputHex,onInputFocus:e.handleFocus,onInputBlur:e.handleBlur},null,8,["color","onInputColor","onInputFocus","onInputBlur"]),a(g,{name:"RGBA",color:e.modelRgba,onInputColor:e.inputRgba,onInputFocus:e.handleFocus,onInputBlur:e.handleBlur},null,8,["color","onInputColor","onInputFocus","onInputBlur"]),a(v,{color:e.rgbaString,"colors-default":e.colorsDefault,"colors-history-key":e.colorsHistoryKey,onSelectColor:e.selectColor},null,8,["color","colors-default","colors-history-key","onSelectColor"]),C(" custom options "),K(e.$slots,"default")],6)}k.render=he;k.__file="src/color/ColorPicker.vue";k.install=e=>{e.component(k.name,k)};export{k as s};
