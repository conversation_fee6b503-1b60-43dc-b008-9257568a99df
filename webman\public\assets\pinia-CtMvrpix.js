import{a4 as M,r as V,a5 as B,i as $,a6 as G,a as T,Y as p,a7 as D,a3 as tt,$ as et,a0 as st,w as nt,C as ot,v as ct,c as rt}from"./@vue-9ZIPiVZG.js";/*!
 * pinia v2.3.1
 * (c) 2025 <PERSON>
 * @license MIT
 */let J;const R=t=>J=t,K=Symbol();function L(t){return t&&typeof t=="object"&&Object.prototype.toString.call(t)==="[object Object]"&&typeof t.toJSON!="function"}var E;(function(t){t.direct="direct",t.patchObject="patch object",t.patchFunction="patch function"})(E||(E={}));function yt(){const t=M(!0),c=t.run(()=>V({}));let s=[],e=[];const r=B({install(u){R(r),r._a=u,u.provide(K,r),u.config.globalProperties.$pinia=r,e.forEach(f=>s.push(f)),e=[]},use(u){return this._a?s.push(u):e.push(u),this},_p:s,_a:null,_e:t,_s:new Map,state:c});return r}const Y=()=>{};function A(t,c,s,e=Y){t.push(c);const r=()=>{const u=t.indexOf(c);u>-1&&(t.splice(u,1),e())};return!s&&et()&&st(r),r}function C(t,...c){t.slice().forEach(s=>{s(...c)})}const ut=t=>t(),H=Symbol(),k=Symbol();function O(t,c){t instanceof Map&&c instanceof Map?c.forEach((s,e)=>t.set(e,s)):t instanceof Set&&c instanceof Set&&c.forEach(t.add,t);for(const s in c){if(!c.hasOwnProperty(s))continue;const e=c[s],r=t[s];L(r)&&L(e)&&t.hasOwnProperty(s)&&!p(e)&&!D(e)?t[s]=O(r,e):t[s]=e}return t}const at=Symbol();function ft(t){return!L(t)||!t.hasOwnProperty(at)}const{assign:y}=Object;function lt(t){return!!(p(t)&&t.effect)}function it(t,c,s,e){const{state:r,actions:u,getters:f}=c,a=s.state.value[t];let P;function b(){a||(s.state.value[t]=r?r():{});const S=ct(s.state.value[t]);return y(S,u,Object.keys(f||{}).reduce((v,_)=>(v[_]=B(rt(()=>{R(s);const m=s._s.get(t);return f[_].call(m,m)})),v),{}))}return P=q(t,b,c,s,e,!0),P}function q(t,c,s={},e,r,u){let f;const a=y({actions:{}},s),P={deep:!0};let b,S,v=[],_=[],m;const d=e.state.value[t];!u&&!d&&(e.state.value[t]={}),V({});let W;function N(o){let n;b=S=!1,typeof o=="function"?(o(e.state.value[t]),n={type:E.patchFunction,storeId:t,events:m}):(O(e.state.value[t],o),n={type:E.patchObject,payload:o,storeId:t,events:m});const l=W=Symbol();ot().then(()=>{W===l&&(b=!0)}),S=!0,C(v,n,e.state.value[t])}const z=u?function(){const{state:n}=s,l=n?n():{};this.$patch(j=>{y(j,l)})}:Y;function Q(){f.stop(),v=[],_=[],e._s.delete(t)}const F=(o,n="")=>{if(H in o)return o[k]=n,o;const l=function(){R(e);const j=Array.from(arguments),w=[],I=[];function X(i){w.push(i)}function Z(i){I.push(i)}C(_,{args:j,name:l[k],store:h,after:X,onError:Z});let x;try{x=o.apply(this&&this.$id===t?this:h,j)}catch(i){throw C(I,i),i}return x instanceof Promise?x.then(i=>(C(w,i),i)).catch(i=>(C(I,i),Promise.reject(i))):(C(w,x),x)};return l[H]=!0,l[k]=n,l},U={_p:e,$id:t,$onAction:A.bind(null,_),$patch:N,$reset:z,$subscribe(o,n={}){const l=A(v,o,n.detached,()=>j()),j=f.run(()=>nt(()=>e.state.value[t],w=>{(n.flush==="sync"?S:b)&&o({storeId:t,type:E.direct,events:m},w)},y({},P,n)));return l},$dispose:Q},h=T(U);e._s.set(t,h);const g=(e._a&&e._a.runWithContext||ut)(()=>e._e.run(()=>(f=M()).run(()=>c({action:F}))));for(const o in g){const n=g[o];if(p(n)&&!lt(n)||D(n))u||(d&&ft(n)&&(p(n)?n.value=d[o]:O(n,d[o])),e.state.value[t][o]=n);else if(typeof n=="function"){const l=F(n,o);g[o]=l,a.actions[o]=n}}return y(h,g),y(tt(h),g),Object.defineProperty(h,"$state",{get:()=>e.state.value[t],set:o=>{N(n=>{y(n,o)})}}),e._p.forEach(o=>{y(h,f.run(()=>o({store:h,app:e._a,pinia:e,options:a})))}),d&&u&&s.hydrate&&s.hydrate(h.$state,d),b=!0,S=!0,h}/*! #__NO_SIDE_EFFECTS__ */function St(t,c,s){let e,r;const u=typeof c=="function";typeof t=="string"?(e=t,r=u?s:c):(r=t,e=t.id);function f(a,P){const b=G();return a=a||(b?$(K,null):null),a&&R(a),a=J,a._s.has(e)||(u?q(e,c,r,a):it(e,r,a)),a._s.get(e)}return f.$id=e,f}export{yt as c,St as d};
