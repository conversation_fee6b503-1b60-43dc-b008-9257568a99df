import{a as c}from"./crontab-D2UudUxl.js";import N from"./logList-uYU5rm9Q.js";import O from"./edit-KUYyIVcu.js";import{M as f}from"./@arco-design-uttiljWv.js";import{r as u,a as g,o as P,h as n,ba as T,n as j,k as b,t as e,l as s,M as z,j as A,y as x,m as G,z as W}from"./@vue-9ZIPiVZG.js";import"./index-DkGLNqVb.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const q={class:"ma-content-block lg:flex justify-between"},te={__name:"index",setup(H){const r=u(),p=u(),h=u(),l=u({name:"",type:"",status:""}),v=[{label:"URL任务GET",value:1},{label:"URL任务POST",value:2},{label:"类任务",value:3}],V=async(o,t)=>{const i=await c.changeStatus({id:t,status:o});i.code===200&&(f.success(i.message),r.value.refresh())},k=async o=>{const t=await c.run({id:o.id});t.code===200&&(f.success(t.message),r.value.refresh())},C=o=>{h.value.open(o.id)},R=g({api:c.getPageList,rowSelection:{showCheckedAll:!0},operationColumnWidth:280,add:{show:!0,auth:["/tool/crontab/save"],func:async()=>{var o;(o=p.value)==null||o.open()}},edit:{show:!0,auth:["/tool/crontab/update"],func:async o=>{var t,i;(t=p.value)==null||t.open("edit"),(i=p.value)==null||i.setFormData(o)}},delete:{show:!0,auth:["/tool/crontab/destroy"],func:async o=>{var i;(await c.destroy(o)).code===200&&(f.success("删除成功！"),(i=r.value)==null||i.refresh())}}}),S=g([{title:"任务名称",dataIndex:"name",width:180},{title:"任务类型",dataIndex:"type",type:"dict",options:v,width:140},{title:"定时规则",dataIndex:"rule",width:260},{title:"调用目标",dataIndex:"target",width:260},{title:"状态",dataIndex:"status",dict:"data_status",width:120},{title:"创建时间",dataIndex:"create_time",width:180}]),U=async()=>{},y=async()=>{var o;(o=r.value)==null||o.refresh()};return P(async()=>{U(),y()}),(o,t)=>{const i=n("a-input"),d=n("a-form-item"),_=n("a-col"),I=n("a-select"),B=n("sa-select"),M=n("sa-switch"),D=n("icon-caret-right"),w=n("a-link"),F=n("a-popconfirm"),L=n("icon-history"),$=n("sa-table"),E=T("auth");return b(),j("div",q,[e($,{ref_key:"crudRef",ref:r,options:R,columns:S,searchForm:l.value},{tableSearch:s(()=>[e(_,{sm:8,xs:24},{default:s(()=>[e(d,{field:"name",label:"任务名称"},{default:s(()=>[e(i,{modelValue:l.value.name,"onUpdate:modelValue":t[0]||(t[0]=a=>l.value.name=a),placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{sm:8,xs:24},{default:s(()=>[e(d,{field:"type",label:"任务类型"},{default:s(()=>[e(I,{modelValue:l.value.type,"onUpdate:modelValue":t[1]||(t[1]=a=>l.value.type=a),options:v,"allow-clear":"",placeholder:"请选择任务类型"},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{sm:8,xs:24},{default:s(()=>[e(d,{field:"status",label:"状态"},{default:s(()=>[e(B,{modelValue:l.value.status,"onUpdate:modelValue":t[2]||(t[2]=a=>l.value.status=a),dict:"data_status","allow-clear":"",placeholder:"请选择状态"},null,8,["modelValue"])]),_:1})]),_:1})]),rule:s(({record:a})=>[G("span",null,W(a.rule),1)]),status:s(({record:a})=>[e(M,{modelValue:a.status,"onUpdate:modelValue":m=>a.status=m,onChange:m=>V(m,a.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),operationBeforeExtend:s(({record:a})=>[e(F,{content:"确定立刻执行一次?",position:"bottom",onOk:m=>k(a)},{default:s(()=>[z((b(),A(w,null,{default:s(()=>[e(D),t[3]||(t[3]=x(" 执行一次"))]),_:1})),[[E,["/tool/crontab/run"]]])]),_:2},1032,["onOk"]),e(w,{onClick:m=>C(a)},{default:s(()=>[e(L),t[4]||(t[4]=x(" 日志 "))]),_:2},1032,["onClick"])]),_:1},8,["options","columns","searchForm"]),e(O,{ref_key:"editRef",ref:p,onSuccess:y},null,512),e(N,{ref_key:"logsRef",ref:h},null,512)])}}};export{te as default};
