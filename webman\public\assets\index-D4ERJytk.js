import{_ as c,u as f,m as g}from"./index-ybrmzYq5.js";import{e as n}from"./monaco-editor-nMXQdunA.js";import{r as y,w as V,o as v,a3 as u,n as _,k as h,q as M}from"./@vue-9ZIPiVZG.js";const S={__name:"index",props:{modelValue:{type:[String,Object,Array],default:()=>""},defaultModelValue:{type:String,default:""},valueType:{type:String,default:"value"},miniMap:{type:Boolean,default:!1},isBind:{type:Boolean,default:!1},height:{type:Number,default:400},language:{type:String,default:"javascript"},readonly:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(s,{expose:d,emit:r}){const i=f(),e=s,p={tabSize:4,automaticLayout:!0,scrollBeyondLastLine:!1,language:e.language,theme:i.mode==="light"?"vs":"vs-dark",autoIndent:!0,minimap:{enabled:e.miniMap},readOnly:e.readonly,folding:!0,acceptSuggestionOnCommitCharacter:!0,acceptSuggestionOnEnter:!0,contextmenu:!0},m=r,o=y();let t;const a=()=>{var l;e.valueType==="value"&&typeof e.modelValue=="string"?t.setValue(e.modelValue):e.valueType==="value"&&((l=e.modelValue)==null?void 0:l._onWillDispose)===void 0?t.setValue(g(e.modelValue)):e.modelValue?t.setModel(u(e.modelValue)):t.setModel(n.createModel(e.defaultModelValue,e.language))};return V(()=>e.modelValue,()=>a()),v(()=>{t=n.create(o.value,p),a(),t.onDidBlurEditorText(()=>{m("update:modelValue",u(e.valueType==="value"?t.getValue():t.getModel()))})}),d({getInstance:()=>t,initEditorValue:a}),(l,B)=>(h(),_("div",{class:"editor",ref_key:"dom",ref:o,style:M("width: 100%; height: "+e.height+"px")},null,4))}},T=c(S,[["__scopeId","data-v-15ba5e65"]]);export{T as M};
