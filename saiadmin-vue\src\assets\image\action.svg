<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><g fill="none"><path fill="url(#fluentColorNews166)" d="M13 4a2 2 0 0 1 2 2v4.5a2.5 2.5 0 0 1-2.5 2.5l-.023-9z"/><path fill="url(#fluentColorNews160)" d="M1 4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8.95q-.243.05-.5.05h-9A2.5 2.5 0 0 1 1 10.5z"/><path fill="url(#fluentColorNews161)" d="M1 4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8.95q-.243.05-.5.05h-9A2.5 2.5 0 0 1 1 10.5z"/><path fill="url(#fluentColorNews162)" d="M1 4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8.95q-.243.05-.5.05h-9A2.5 2.5 0 0 1 1 10.5z"/><path fill="url(#fluentColorNews163)" d="M3.5 7a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5z"/><path fill="url(#fluentColorNews164)" d="M7.5 7a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1zm0 2a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1z"/><path fill="url(#fluentColorNews165)" d="M3.5 5a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1z"/><defs><linearGradient id="fluentColorNews160" x1="4.429" x2="13.346" y1=".308" y2="12.311" gradientUnits="userSpaceOnUse"><stop stop-color="#3bd5ff"/><stop offset="1" stop-color="#367af2"/></linearGradient><linearGradient id="fluentColorNews161" x1="7.857" x2="7.857" y1="10.885" y2="13" gradientUnits="userSpaceOnUse"><stop offset=".181" stop-color="#2764e7" stop-opacity="0"/><stop offset="1" stop-color="#2764e7"/></linearGradient><linearGradient id="fluentColorNews162" x1="7.429" x2="11.535" y1="5.385" y2="16.126" gradientUnits="userSpaceOnUse"><stop stop-color="#dcf8ff" stop-opacity="0"/><stop offset="1" stop-color="#ff6ce8" stop-opacity="0.7"/></linearGradient><linearGradient id="fluentColorNews163" x1="3.286" x2="4.787" y1="6.853" y2="9.857" gradientUnits="userSpaceOnUse"><stop stop-color="#defcff"/><stop offset="1" stop-color="#9ff0f9"/></linearGradient><linearGradient id="fluentColorNews164" x1="7.35" x2="7.728" y1="7.053" y2="10.301" gradientUnits="userSpaceOnUse"><stop stop-color="#fdfdfd"/><stop offset="1" stop-color="#cceaff"/></linearGradient><linearGradient id="fluentColorNews165" x1="3.7" x2="3.721" y1="5.018" y2="6.115" gradientUnits="userSpaceOnUse"><stop stop-color="#fdfdfd"/><stop offset="1" stop-color="#cceaff"/></linearGradient><radialGradient id="fluentColorNews166" cx="0" cy="0" r="1" gradientTransform="matrix(-4.03753 4.94997 -7.38959 -6.02744 16.514 5.35)" gradientUnits="userSpaceOnUse"><stop stop-color="#068beb"/><stop offset=".617" stop-color="#0056cf"/><stop offset=".974" stop-color="#0027a7"/></radialGradient></defs></g></svg>