import{g as r}from"./index-DkGLNqVb.js";const o={getList(e={}){return r({url:"/core/menu/index",method:"get",params:e})},accessMenu(e={}){return r({url:"/core/menu/accessMenu",method:"get",params:e})},save(e={}){return r({url:"/core/menu/save",method:"post",data:e})},destroy(e){return r({url:"/core/menu/destroy",method:"delete",data:e})},update(e,t={}){return r({url:"/core/menu/update?id="+e,method:"put",data:t})}};export{o as m};
