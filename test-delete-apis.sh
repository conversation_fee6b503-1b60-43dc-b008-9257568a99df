#!/bin/bash
# SaiAdmin 代码生成器删除功能 curl 测试

BASE_URL="http://localhost:8787"
TOKEN="your_token_here"

# 单个删除
echo "测试: 单个删除"
curl -X DELETE \
  "${BASE_URL}/admin/tool/generateTables/delete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{"ids":"1"}' \
  | jq .

# 批量删除
echo "测试: 批量删除"
curl -X DELETE \
  "${BASE_URL}/admin/tool/generateTables/batchDelete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{"ids":[1,2,3]}' \
  | jq .

# 清空所有
echo "测试: 清空所有"
curl -X DELETE \
  "${BASE_URL}/admin/tool/generateTables/clear" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  | jq .

# 删除生成文件
echo "测试: 删除生成文件"
curl -X DELETE \
  "${BASE_URL}/admin/tool/generateTables/deleteGeneratedFiles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{"id":"1"}' \
  | jq .

