<?php
/**
 * SaiAdmin 系统功能全面测试脚本
 * 测试所有核心功能模块
 */

echo "🧪 SaiAdmin 系统功能测试\n";
echo "========================================\n\n";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

/**
 * 测试函数
 */
function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "🔍 测试: {$testName}... ";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "✅ 通过\n";
            $passedTests++;
            $testResults[$testName] = ['status' => 'PASS', 'message' => '测试通过'];
        } else {
            echo "❌ 失败\n";
            $testResults[$testName] = ['status' => 'FAIL', 'message' => '测试失败'];
        }
    } catch (Exception $e) {
        echo "❌ 异常: " . $e->getMessage() . "\n";
        $testResults[$testName] = ['status' => 'ERROR', 'message' => $e->getMessage()];
    }
}

// 1. 数据库连接测试
runTest("数据库连接", function() {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
        'root',
        '5GeNi1v7P7Xcur5W'
    );
    return $pdo !== null;
});

// 2. 数据库表结构测试
runTest("数据库表结构", function() {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
        'root',
        '5GeNi1v7P7Xcur5W'
    );
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    return count($tables) >= 20; // 至少20个表
});

// 3. 用户数据测试
runTest("用户数据完整性", function() {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
        'root',
        '5GeNi1v7P7Xcur5W'
    );
    $stmt = $pdo->query('SELECT COUNT(*) FROM sa_system_user');
    $userCount = $stmt->fetchColumn();
    return $userCount >= 3; // 至少3个用户
});

// 4. 数据库索引测试
runTest("数据库索引优化", function() {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
        'root',
        '5GeNi1v7P7Xcur5W'
    );
    $stmt = $pdo->query('SHOW INDEX FROM sa_system_user');
    $indexes = $stmt->fetchAll();
    return count($indexes) >= 10; // 至少10个索引
});

// 5. 后端API测试
runTest("后端API响应", function() {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'http://localhost:8787/',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return $httpCode === 200 && !empty($response);
});

// 6. 验证码API测试
runTest("验证码API", function() {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'http://localhost:8787/core/captcha',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return $httpCode === 200;
});

// 7. 前端服务测试
runTest("前端服务", function() {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'http://localhost:8889/',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return $httpCode === 200;
});

// 8. WebSocket服务测试
runTest("WebSocket服务", function() {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'http://localhost:3131/',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5,
        CURLOPT_CONNECTTIMEOUT => 5
    ]);
    curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    // WebSocket可能返回不同的状态码，只要能连接就算成功
    return $httpCode > 0;
});

// 9. 配置文件测试
runTest("配置文件完整性", function() {
    $configFiles = [
        'webman/.env',
        'webman/config/think-orm.php',
        'saiadmin-vue/package.json',
        'saiadmin-vue/vite.config.js'
    ];
    
    foreach ($configFiles as $file) {
        if (!file_exists($file)) {
            return false;
        }
    }
    return true;
});

// 10. 优化文件测试
runTest("优化文件存在性", function() {
    $optimizedFiles = [
        'webman/plugin/saiadmin/app/logic/system/SystemUserLogicOptimized.php',
        'webman/plugin/saiadmin/app/middleware/SecurityMiddleware.php',
        'webman/plugin/saiadmin/app/cache/OptimizedCache.php',
        'webman/plugin/saiadmin/app/database/ConnectionPool.php',
        'webman/plugin/saiadmin/app/cache/RedisManager.php'
    ];
    
    foreach ($optimizedFiles as $file) {
        if (!file_exists($file)) {
            return false;
        }
    }
    return true;
});

// 11. 性能监控文件测试
runTest("监控脚本可用性", function() {
    $monitorFiles = [
        'performance-monitor.php',
        'log-monitor.php',
        'quick-performance-test.php',
        'code-quality-check.php'
    ];
    
    foreach ($monitorFiles as $file) {
        if (!file_exists($file)) {
            return false;
        }
    }
    return true;
});

// 12. 数据库查询性能测试
runTest("数据库查询性能", function() {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
        'root',
        '5GeNi1v7P7Xcur5W'
    );
    
    $startTime = microtime(true);
    $stmt = $pdo->prepare('SELECT * FROM sa_system_user WHERE id = ?');
    $stmt->execute([1]);
    $queryTime = (microtime(true) - $startTime) * 1000;
    
    return $queryTime < 50; // 查询时间小于50ms
});

// 13. 内存使用测试
runTest("内存使用合理性", function() {
    $memoryUsage = memory_get_usage(true);
    return $memoryUsage < 50 * 1024 * 1024; // 小于50MB
});

// 14. PHP扩展测试
runTest("PHP扩展支持", function() {
    $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'curl', 'mbstring'];
    
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            return false;
        }
    }
    return true;
});

// 15. 文件权限测试
runTest("文件写入权限", function() {
    $testFile = 'test_write_permission.tmp';
    $result = file_put_contents($testFile, 'test');
    if ($result !== false) {
        unlink($testFile);
        return true;
    }
    return false;
});

echo "\n";
echo "========================================\n";
echo "🧪 测试完成！\n";
echo "========================================\n\n";

// 生成测试报告
echo "📊 测试统计:\n";
echo "  总测试数: {$totalTests}\n";
echo "  通过数: {$passedTests}\n";
echo "  失败数: " . ($totalTests - $passedTests) . "\n";
echo "  成功率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";

// 详细结果
echo "📋 详细结果:\n";
foreach ($testResults as $testName => $result) {
    $icon = $result['status'] === 'PASS' ? '✅' : '❌';
    echo "  {$icon} {$testName}: {$result['status']}\n";
    if ($result['status'] !== 'PASS') {
        echo "     原因: {$result['message']}\n";
    }
}

// 保存测试报告
$report = [
    'timestamp' => date('Y-m-d H:i:s'),
    'total_tests' => $totalTests,
    'passed_tests' => $passedTests,
    'success_rate' => round(($passedTests / $totalTests) * 100, 2),
    'results' => $testResults
];

file_put_contents('system-test-report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n";
echo "📄 测试报告已保存: system-test-report.json\n";

// 系统健康评估
echo "\n";
echo "🏥 系统健康评估:\n";

$successRate = ($passedTests / $totalTests) * 100;

if ($successRate >= 90) {
    echo "  🟢 系统状态: 优秀 ({$successRate}%)\n";
    echo "  💡 建议: 系统运行良好，可以投入生产使用\n";
} elseif ($successRate >= 80) {
    echo "  🟡 系统状态: 良好 ({$successRate}%)\n";
    echo "  💡 建议: 系统基本正常，建议修复失败的测试项\n";
} elseif ($successRate >= 70) {
    echo "  🟠 系统状态: 一般 ({$successRate}%)\n";
    echo "  💡 建议: 存在一些问题，需要进行优化\n";
} else {
    echo "  🔴 系统状态: 需要改进 ({$successRate}%)\n";
    echo "  💡 建议: 系统存在较多问题，需要全面检查\n";
}

echo "\n";
echo "🌐 访问地址:\n";
echo "  前端: http://localhost:8889/\n";
echo "  后端: http://localhost:8787/\n";
echo "  管理员: admin / admin123\n";

echo "\n";
echo "🎉 SaiAdmin 系统功能测试完成！\n";
