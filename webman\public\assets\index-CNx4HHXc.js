import{_ as Z,s as x}from"./install-box-UcToEVc_.js";import h from"./terminal-C7g1OY6E.js";import{_ as tt}from"./index-ybrmzYq5.js";import{M as ot}from"./@arco-design-uttiljWv.js";import{r as y,o as et,h as s,j as p,k as r,l as o,t as n,m as l,y as a,z as g,s as S,p as w}from"./@vue-9ZIPiVZG.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./pinia-CtMvrpix.js";import"./@wangeditor-Bg8kJaak.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const nt={class:"flex"},it={class:"version-value"},at={class:"version-value"},st={class:"ma-content-block"},lt={class:"ma-content-block p-2"},pt={__name:"index",setup(rt){const d=y({}),b=y(!1),N=y(),c=y(),I=y([]),E=async()=>{var i;(i=N.value)==null||i.open()},U=async i=>{x.installApp({appName:i.app}).then(t=>{t.code==200&&(ot.success("安装成功"),v(),x.reloadBackend())})},j=async i=>{await x.uninstallApp({appName:i.app}),v()},D=i=>{var u;const t="module-install:"+i.app;(u=c.value)==null||u.open(),setTimeout(()=>{var _;(_=c.value)==null||_.frontInstall(t)},500)},q=i=>{var u;const t="module-install:"+i.app;(u=c.value)==null||u.open(),setTimeout(()=>{var _;(_=c.value)==null||_.backendInstall(t)},500)},G=async()=>{var i;(i=c.value)==null||i.open()},H=[{title:"插件标识",slotName:"app",width:120},{title:"插件名称",dataIndex:"title",width:150},{title:"插件描述",dataIndex:"about",ellipsis:!0,tooltip:!0},{title:"作者",dataIndex:"author",width:120},{title:"版本",dataIndex:"version",width:100},{title:"插件状态",slotName:"state",width:100},{title:"前端依赖",slotName:"npm",width:120},{title:"后端依赖",slotName:"composer",width:120},{title:"操作",slotName:"optional",width:150}],v=async()=>{b.value=!0;const i=await x.getAppList();I.value=i.data.data,d.value=i.data.version,b.value=!1};return et(async()=>{v()}),(i,t)=>{const u=s("a-alert"),_=s("icon-refresh"),C=s("a-button"),J=s("icon-upload"),K=s("icon-computer"),B=s("a-space"),P=s("a-divider"),k=s("a-link"),m=s("a-tag"),z=s("icon-download"),Q=s("icon-cloud-download"),T=s("a-popconfirm"),W=s("icon-delete"),X=s("a-table"),Y=s("a-layout-content");return r(),p(Y,{class:"flex flex-col"},{default:o(()=>[n(u,null,{default:o(()=>t[0]||(t[0]=[a(" 仅支持上传由插件市场下载的zip压缩包进行安装，请您务必确认插件包文件来自官方渠道或经由官方认证的插件作者！ ")])),_:1}),n(B,{class:"ma-content-block py-3 px-2"},{default:o(()=>{var e,f,$,O,R,V,A,F,L,M;return[n(C,{type:"outline",onClick:v},{icon:o(()=>[n(_)]),_:1}),n(C,{type:"outline",onClick:E},{icon:o(()=>[n(J)]),default:o(()=>[t[1]||(t[1]=a(" 上传插件包 "))]),_:1}),n(C,{type:"outline",status:"danger",onClick:G},{icon:o(()=>[n(K)]),_:1}),l("div",nt,[t[2]||(t[2]=l("div",{class:"version-title"},"saiadmin版本",-1)),l("div",it,g((f=(e=d.value)==null?void 0:e.saiadmin_version)==null?void 0:f.describe),1),t[3]||(t[3]=l("div",{class:"version-title"},"说明",-1)),l("div",{class:S(["version-value",[(($=d.value.saiadmin_version)==null?void 0:$.notes)=="正常"?"":"text-red-500"]])},g((R=(O=d.value)==null?void 0:O.saiadmin_version)==null?void 0:R.notes),3),t[4]||(t[4]=l("div",{class:"version-title"},"saipackage安装器",-1)),l("div",at,g((A=(V=d.value)==null?void 0:V.saipackage_version)==null?void 0:A.describe),1),t[5]||(t[5]=l("div",{class:"version-title"},"说明",-1)),l("div",{class:S(["version-value",[((F=d.value.saipackage_version)==null?void 0:F.notes)=="正常"?"":"text-red-500"]])},g((M=(L=d.value)==null?void 0:L.saipackage_version)==null?void 0:M.notes),3)])]}),_:1}),l("div",st,[n(P,{orientation:"left"},{default:o(()=>t[6]||(t[6]=[a("已安装列表")])),_:1})]),l("div",lt,[n(X,{loading:b.value,columns:H,data:I.value,class:"mt-2",size:"medium",pagination:!1},{app:o(({record:e})=>[n(k,{href:e.website,target:"_blank"},{default:o(()=>[a(g(e.app),1)]),_:2},1032,["href"])]),state:o(({record:e})=>[e.state==0?(r(),p(m,{key:0,color:"red"},{default:o(()=>t[7]||(t[7]=[a("已卸载")])),_:1})):w("",!0),e.state==1?(r(),p(m,{key:1,color:"green"},{default:o(()=>t[8]||(t[8]=[a("已安装")])),_:1})):w("",!0),e.state==2?(r(),p(m,{key:2,color:"blue"},{default:o(()=>t[9]||(t[9]=[a("等待安装")])),_:1})):w("",!0),e.state==4?(r(),p(m,{key:3,color:"orange"},{default:o(()=>t[10]||(t[10]=[a("等待安装依赖")])),_:1})):w("",!0)]),npm:o(({record:e})=>[l("div",null,[e.npm_dependent_wait_install&&e.npm_dependent_wait_install==1?(r(),p(k,{key:0,onClick:f=>D(e)},{default:o(()=>[n(z),t[11]||(t[11]=a("点击安装 "))]),_:2},1032,["onClick"])):e.state==2?(r(),p(m,{key:1,color:"blue"},{default:o(()=>t[12]||(t[12]=[a("-")])),_:1})):(r(),p(m,{key:2,color:"green"},{default:o(()=>t[13]||(t[13]=[a("已安装")])),_:1}))])]),composer:o(({record:e})=>[l("div",null,[e.composer_dependent_wait_install&&e.composer_dependent_wait_install==1?(r(),p(k,{key:0,onClick:f=>q(e)},{default:o(()=>[n(z),t[14]||(t[14]=a("点击安装 "))]),_:2},1032,["onClick"])):e.state==2?(r(),p(m,{key:1,color:"blue"},{default:o(()=>t[15]||(t[15]=[a("-")])),_:1})):(r(),p(m,{key:2,color:"green"},{default:o(()=>t[16]||(t[16]=[a("已安装")])),_:1}))])]),optional:o(({record:e})=>[n(B,{size:"mini"},{default:o(()=>[n(T,{content:"确定要安装当前插件吗?",position:"bottom",onOk:f=>U(e)},{default:o(()=>[n(k,{status:"warning"},{default:o(()=>[n(Q),t[17]||(t[17]=a("安装"))]),_:1})]),_:2},1032,["onOk"]),n(T,{content:"确定要卸载当前插件吗?",position:"bottom",onOk:f=>j(e)},{default:o(()=>[n(k,{status:"danger"},{default:o(()=>[n(W),t[18]||(t[18]=a("卸载"))]),_:1})]),_:2},1032,["onOk"])]),_:2},1024)]),_:1},8,["loading","data"])]),n(Z,{ref_key:"installFormRef",ref:N,onSuccess:v},null,512),n(h,{ref_key:"terminalRef",ref:c,onSuccess:v},null,512)]),_:1})}}},fo=tt(pt,[["__scopeId","data-v-73c8c583"]]);export{fo as default};
