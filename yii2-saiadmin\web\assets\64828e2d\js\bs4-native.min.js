// Native Javascript for Bootstrap 4 v2.0.25 | © dnp_theme | MIT-License
!function(t,n){if("function"==typeof define&&define.amd)define([],n);else if("object"==typeof module&&module.exports)module.exports=n();else{var e=n();t.<PERSON><PERSON>=e.<PERSON>,t.<PERSON><PERSON>=e.<PERSON>,t.<PERSON>=e<PERSON>,t.<PERSON><PERSON>=e.<PERSON>,t.Dropdown=e.Dropdown,t.<PERSON>=e.<PERSON>,t.Pop<PERSON>=e.Pop<PERSON>,t.ScrollSpy=e.<PERSON>roll<PERSON>,t.Tab=e.Tab,t.Tooltip=e.Tooltip}}(this,function(){"use strict";var t="undefined"!=typeof global?global:this||window,n=document,e=n.documentElement,i="body",o=t.BSN={},a=o.supports=[],l="data-toggle",r="data-dismiss",c="data-spy",u="data-ride",s="Alert",f="Button",h="Carousel",d="Collapse",v="Dropdown",p="Modal",m="Popover",g="ScrollSpy",w="Tab",b="Tooltip",y="data-backdrop",T="data-keyboard",x="data-target",C="data-interval",A="data-height",k="data-pause",I="data-title",N="data-original-title",L="data-dismissible",E="data-trigger",S="data-animation",B="data-container",D="data-placement",M="data-delay",P="backdrop",H="keyboard",O="delay",W="content",j="target",q="interval",R="pause",U="animation",z="placement",F="container",X="offsetTop",Y="offsetLeft",G="scrollTop",J="scrollLeft",K="clientWidth",Q="clientHeight",V="offsetWidth",Z="offsetHeight",$="innerWidth",_="innerHeight",tt="scrollHeight",nt="height",et="aria-expanded",it="aria-hidden",ot="click",at="hover",lt="keydown",rt="keyup",ct="resize",ut="scroll",st="show",ft="shown",ht="hide",dt="hidden",vt="close",pt="closed",mt="slid",gt="slide",wt="change",bt="getAttribute",yt="setAttribute",Tt="hasAttribute",xt="createElement",Ct="appendChild",At="innerHTML",kt="getElementsByTagName",It="preventDefault",Nt="getBoundingClientRect",Lt="querySelectorAll",Et="getElementsByClassName",St="getComputedStyle",Bt="indexOf",Dt="parentNode",Mt="length",Pt="toLowerCase",Ht="Transition",Ot="Duration",Wt="Webkit",jt="style",qt="push",Rt="tabindex",Ut="contains",zt="active",Ft="show",Xt="collapsing",Yt="left",Gt="right",Jt="top",Kt="bottom",Qt="onmouseleave"in n?["mouseenter","mouseleave"]:["mouseover","mouseout"],Vt=/\b(top|bottom|left|right)+/,Zt=0,$t="fixed-top",_t="fixed-bottom",tn=Wt+Ht in e[jt]||Ht[Pt]()in e[jt],nn=Wt+Ht in e[jt]?Wt[Pt]()+Ht+"End":Ht[Pt]()+"end",en=Wt+Ot in e[jt]?Wt[Pt]()+Ht+Ot:Ht[Pt]()+Ot,on=function(t){t.focus?t.focus():t.setActive()},an=function(t,n){t.classList.add(n)},ln=function(t,n){t.classList.remove(n)},rn=function(t,n){return t.classList[Ut](n)},cn=function(t,n){return[].slice.call(t[Et](n))},un=function(t,e){var i=e?e:n;return"object"==typeof t?t:i.querySelector(t)},sn=function(t,e){var i=e.charAt(0),o=e.substr(1);if("."===i){for(;t&&t!==n;t=t[Dt])if(null!==un(e,t[Dt])&&rn(t,o))return t}else if("#"===i)for(;t&&t!==n;t=t[Dt])if(t.id===o)return t;return!1},fn=function(t,n,e){t.addEventListener(n,e,!1)},hn=function(t,n,e){t.removeEventListener(n,e,!1)},dn=function(t,n,e){fn(t,n,function i(o){e(o),hn(t,n,i)})},vn=function(n){var e=t[St](n)[en];return e=parseFloat(e),e="number"!=typeof e||isNaN(e)?0:1e3*e,e+50},pn=function(t,n){var e=0,i=vn(t);tn&&dn(t,nn,function(t){n(t),e=1}),setTimeout(function(){!e&&n()},i)},mn=function(t,n,e){var i=new CustomEvent(t+".bs."+n);i.relatedTarget=e,this.dispatchEvent(i)},gn=function(){return{y:t.pageYOffset||e[G],x:t.pageXOffset||e[J]}},wn=function(t,o,a,l){var r,c,u,s,f,h,d={w:o[V],h:o[Z]},v=e[K]||n[i][K],p=e[Q]||n[i][Q],m=t[Nt](),g=l===n[i]?gn():{x:l[Y]+l[J],y:l[X]+l[G]},w={w:m[Gt]-m[Yt],h:m[Kt]-m[Jt]},b=rn(o,"popover"),y=un(".arrow",o),T=m[Jt]+w.h/2-d.h/2<0,x=m[Yt]+w.w/2-d.w/2<0,C=m[Yt]+d.w/2+w.w/2>=v,A=m[Jt]+d.h/2+w.h/2>=p,k=m[Jt]-d.h<0,I=m[Yt]-d.w<0,N=m[Jt]+d.h+w.h>=p,L=m[Yt]+d.w+w.w>=v;a=(a===Yt||a===Gt)&&I&&L?Jt:a,a=a===Jt&&k?Kt:a,a=a===Kt&&N?Jt:a,a=a===Yt&&I?Gt:a,a=a===Gt&&L?Yt:a,o.className[Bt](a)===-1&&(o.className=o.className.replace(Vt,a)),f=y[V],h=y[Z],a===Yt||a===Gt?(c=a===Yt?m[Yt]+g.x-d.w-(b?f:0):m[Yt]+g.x+w.w,T?(r=m[Jt]+g.y,u=w.h/2-f):A?(r=m[Jt]+g.y-d.h+w.h,u=d.h-w.h/2-f):(r=m[Jt]+g.y-d.h/2+w.h/2,u=d.h/2-(b?.9*h:h/2))):a!==Jt&&a!==Kt||(r=a===Jt?m[Jt]+g.y-d.h-(b?h:0):m[Jt]+g.y+w.h,x?(c=0,s=m[Yt]+w.w/2-f):C?(c=v-1.01*d.w,s=d.w-(v-m[Yt])+w.w/2-f/2):(c=m[Yt]+g.x-d.w/2+w.w/2,s=d.w/2-f/2)),o[jt][Jt]=r+"px",o[jt][Yt]=c+"px",u&&(y[jt][Jt]=u+"px"),s&&(y[jt][Yt]=s+"px")};o.version="2.0.25";var bn=function(t){t=un(t);var n=this,e="alert",i=sn(t,"."+e),o=function(){rn(i,"fade")?pn(i,l):l()},a=function(o){i=sn(o[j],"."+e),t=un("["+r+'="'+e+'"]',i),t&&i&&(t===o[j]||t[Ut](o[j]))&&n.close()},l=function(){mn.call(i,pt,e),hn(t,ot,a),i[Dt].removeChild(i)};this.close=function(){i&&t&&rn(i,Ft)&&(mn.call(i,vt,e),ln(i,Ft),i&&o())},s in t||fn(t,ot,a),t[s]=n};a[qt]([s,bn,"["+r+'="alert"]']);var yn=function(t){t=un(t);var e=!1,i="button",o="checked",a="LABEL",l="INPUT",r=function(t){var e=t.which||t.keyCode;32===e&&t[j]===n.activeElement&&u(t)},c=function(t){var n=t.which||t.keyCode;32===n&&t[It]()},u=function(n){var r=n[j].tagName===a?n[j]:n[j][Dt].tagName===a?n[j][Dt]:null;if(r){var c=n[j],u=cn(c[Dt],"btn"),s=r[kt](l)[0];if(s){if("checkbox"===s.type&&(s[o]?(ln(r,zt),s[bt](o),s.removeAttribute(o),s[o]=!1):(an(r,zt),s[bt](o),s[yt](o,o),s[o]=!0),e||(e=!0,mn.call(s,wt,i),mn.call(t,wt,i))),"radio"===s.type&&!e&&!s[o]){an(r,zt),s[yt](o,o),s[o]=!0,mn.call(s,wt,i),mn.call(t,wt,i),e=!0;for(var f=0,h=u[Mt];f<h;f++){var d=u[f],v=d[kt](l)[0];d!==r&&rn(d,zt)&&(ln(d,zt),v.removeAttribute(o),v[o]=!1,mn.call(v,wt,i))}}setTimeout(function(){e=!1},50)}}};f in t||(fn(t,ot,u),un("["+Rt+"]",t)&&fn(t,rt,r),fn(t,lt,c));for(var s=cn(t,"btn"),h=s[Mt],d=0;d<h;d++)!rn(s[d],zt)&&un("input:checked",s[d])&&an(s[d],zt);t[f]=this};a[qt]([f,yn,"["+l+'="buttons"]']);var Tn=function(i,o){i=un(i),o=o||{};var a=i[bt](C),l=o[q],r="false"===a?0:parseInt(a),c=i[bt](k)===at||!1,u="true"===i[bt](T)||!1,s="carousel",f="paused",d="direction",v="carousel-item",p="data-slide-to";this[H]=o[H]===!0||u,this[R]=!(o[R]!==at&&!c)&&at,this[q]="number"==typeof l?l:l===!1||0===r||r===!1?0:isNaN(r)?5e3:r;var m=this,g=i.index=0,w=i.timer=0,b=!1,y=cn(i,v),x=y[Mt],A=this[d]=Yt,I=cn(i,s+"-control-prev")[0],N=cn(i,s+"-control-next")[0],L=un("."+s+"-indicators",i),E=L&&L[kt]("LI")||[];if(!(x<2)){var S=function(){m[q]===!1||rn(i,f)||(an(i,f),!b&&(clearInterval(w),w=null))},B=function(){m[q]!==!1&&rn(i,f)&&(ln(i,f),!b&&(clearInterval(w),w=null),!b&&m.cycle())},D=function(t){if(t[It](),!b){var n=t[j];if(!n||rn(n,zt)||!n[bt](p))return!1;g=parseInt(n[bt](p),10),m.slideTo(g)}},M=function(t){if(t[It](),!b){var n=t.currentTarget||t.srcElement;n===N?g++:n===I&&g--,m.slideTo(g)}},P=function(t){if(!b){switch(t.which){case 39:g++;break;case 37:g--;break;default:return}m.slideTo(g)}},O=function(){var n=i[Nt](),o=t[_]||e[Q];return n[Jt]<=o&&n[Kt]>=0},W=function(t){for(var n=0,e=E[Mt];n<e;n++)ln(E[n],zt);E[t]&&an(E[t],zt)};this.cycle=function(){w&&(clearInterval(w),w=null),w=setInterval(function(){O()&&(g++,m.slideTo(g))},this[q])},this.slideTo=function(t){if(!b){var e,o=this.getActiveIndex();o!==t&&(o<t||0===o&&t===x-1?A=m[d]=Yt:(o>t||o===x-1&&0===t)&&(A=m[d]=Gt),t<0?t=x-1:t>=x&&(t=0),g=t,e=A===Yt?"next":"prev",mn.call(i,gt,s,y[t]),b=!0,clearInterval(w),w=null,W(t),tn&&rn(i,"slide")?(an(y[t],v+"-"+e),y[t][V],an(y[t],v+"-"+A),an(y[o],v+"-"+A),dn(y[t],nn,function(a){var l=a[j]!==y[t]?1e3*a.elapsedTime+100:20;b&&setTimeout(function(){b=!1,an(y[t],zt),ln(y[o],zt),ln(y[t],v+"-"+e),ln(y[t],v+"-"+A),ln(y[o],v+"-"+A),mn.call(i,mt,s,y[t]),n.hidden||!m[q]||rn(i,f)||m.cycle()},l)})):(an(y[t],zt),y[t][V],ln(y[o],zt),setTimeout(function(){b=!1,m[q]&&!rn(i,f)&&m.cycle(),mn.call(i,mt,s,y[t])},100)))}},this.getActiveIndex=function(){return y[Bt](cn(i,v+" active")[0])||0},h in i||(m[R]&&m[q]&&(fn(i,Qt[0],S),fn(i,Qt[1],B),fn(i,"touchstart",S),fn(i,"touchend",B)),N&&fn(N,ot,M),I&&fn(I,ot,M),L&&fn(L,ot,D),m[H]===!0&&fn(t,lt,P)),m.getActiveIndex()<0&&(y[Mt]&&an(y[0],zt),E[Mt]&&W(0)),m[q]&&m.cycle(),i[h]=m}};a[qt]([h,Tn,"["+u+'="carousel"]']);var xn=function(t,n){t=un(t),n=n||{};var e,i,o=null,a=null,r=this,c=t[bt]("data-parent"),u="collapse",s="collapsed",f="isAnimating",h=function(t,n){mn.call(t,st,u),t[f]=!0,an(t,Xt),ln(t,u),t[jt][nt]=t[tt]+"px",pn(t,function(){t[f]=!1,t[yt](et,"true"),n[yt](et,"true"),ln(t,Xt),an(t,u),an(t,Ft),t[jt][nt]="",mn.call(t,ft,u)})},v=function(t,n){mn.call(t,ht,u),t[f]=!0,t[jt][nt]=t[tt]+"px",ln(t,u),ln(t,Ft),an(t,Xt),t[V],t[jt][nt]="0px",pn(t,function(){t[f]=!1,t[yt](et,"false"),n[yt](et,"false"),ln(t,Xt),an(t,u),t[jt][nt]="",mn.call(t,dt,u)})},p=function(){var n=t.href&&t[bt]("href"),e=t[bt](x),i=n||e&&"#"===e.charAt(0)&&e;return i&&un(i)};this.toggle=function(t){t[It](),rn(a,Ft)?r.hide():r.show()},this.hide=function(){a[f]||(v(a,t),an(t,s))},this.show=function(){o&&(e=un("."+u+"."+Ft,o),i=e&&(un("["+l+'="'+u+'"]['+x+'="#'+e.id+'"]',o)||un("["+l+'="'+u+'"][href="#'+e.id+'"]',o))),(!a[f]||e&&!e[f])&&(i&&e!==a&&(v(e,i),an(i,s)),h(a,t),ln(t,s))},d in t||fn(t,ot,r.toggle),a=p(),a[f]=!1,o=un(n.parent)||c&&sn(t,c),t[d]=r};a[qt]([d,xn,"["+l+'="collapse"]']);var Cn=function(t,e){t=un(t),this.persist=e===!0||"true"===t[bt]("data-persist")||!1;var i=this,o="children",a=t[Dt],l="dropdown",r="open",c=null,u=un(".dropdown-menu",a),s=function(){for(var t=u[o],n=[],e=0;e<t[Mt];e++)t[e][o][Mt]&&"A"===t[e][o][0].tagName&&n[qt](t[e][o][0]),"A"===t[e].tagName&&n[qt](t[e]);return n}(),f=function(t){(t.href&&"#"===t.href.slice(-1)||t[Dt]&&t[Dt].href&&"#"===t[Dt].href.slice(-1))&&this[It]()},h=function(){var e=t[r]?fn:hn;e(n,ot,d),e(n,lt,m),e(n,rt,g)},d=function(n){var e=n[j],o=e&&(v in e||v in e[Dt]);(e!==u&&!u[Ut](e)||!i.persist&&!o)&&(c=e===t||t[Ut](e)?t:null,b(),f.call(n,e))},p=function(n){c=t,w(),f.call(n,n[j])},m=function(t){var n=t.which||t.keyCode;38!==n&&40!==n||t[It]()},g=function(e){var o=e.which||e.keyCode,a=n.activeElement,l=s[Bt](a),f=a===t,h=u[Ut](a),d=a[Dt]===u||a[Dt][Dt]===u;(d||f)&&(l=f?0:38===o?l>1?l-1:0:40===o&&l<s[Mt]-1?l+1:l,s[l]&&on(s[l])),(s[Mt]&&d||!s[Mt]&&(h||f)||!h)&&t[r]&&27===o&&(i.toggle(),c=null)},w=function(){mn.call(a,st,l,c),an(u,Ft),an(a,Ft),t[yt](et,!0),mn.call(a,ft,l,c),t[r]=!0,hn(t,ot,p),setTimeout(function(){on(u[kt]("INPUT")[0]||t),h()},1)},b=function(){mn.call(a,ht,l,c),ln(u,Ft),ln(a,Ft),t[yt](et,!1),mn.call(a,dt,l,c),t[r]=!1,h(),on(t),setTimeout(function(){fn(t,ot,p)},1)};t[r]=!1,this.toggle=function(){rn(a,Ft)&&t[r]?b():w()},v in t||(!Rt in u&&u[yt](Rt,"0"),fn(t,ot,p)),t[v]=i};a[qt]([v,Cn,"["+l+'="dropdown"]']);var An=function(o,a){o=un(o);var l,c=o[bt](x)||o[bt]("href"),u=un(c),s=rn(o,"modal")?o:u,f="modal",h="static",d="paddingLeft",v="paddingRight",m="modal-backdrop";if(rn(o,"modal")&&(o=null),s){a=a||{},this[H]=a[H]!==!1&&"false"!==s[bt](T),this[P]=a[P]!==h&&s[bt](y)!==h||h,this[P]=a[P]!==!1&&"false"!==s[bt](y)&&this[P],this[W]=a[W];var g,w,b,C,A=this,k=null,I=cn(e,$t).concat(cn(e,_t)),N=function(){var n=e[Nt]();return t[$]||n[Gt]-Math.abs(n[Yt])},L=function(){var e,o=t[St](n[i]),a=parseInt(o[v],10);if(g&&(n[i][jt][v]=a+b+"px",I[Mt]))for(var l=0;l<I[Mt];l++)e=t[St](I[l])[v],I[l][jt][v]=parseInt(e)+b+"px"},E=function(){if(n[i][jt][v]="",I[Mt])for(var t=0;t<I[Mt];t++)I[t][jt][v]=""},S=function(){var t,e=n[xt]("div");return e.className=f+"-scrollbar-measure",n[i][Ct](e),t=e[V]-e[K],n[i].removeChild(e),t},B=function(){g=n[i][K]<N(),w=s[tt]>e[Q],b=S()},D=function(){s[jt][d]=!g&&w?b+"px":"",s[jt][v]=g&&!w?b+"px":""},M=function(){s[jt][d]="",s[jt][v]=""},O=function(){Zt=1;var t=n[xt]("div");C=un("."+m),null===C&&(t[yt]("class",m+" fade"),C=t,n[i][Ct](C))},q=function(){C=un("."+m),C&&null!==C&&"object"==typeof C&&(Zt=0,n[i].removeChild(C),C=null),mn.call(s,dt,f)},R=function(){rn(s,Ft)?fn(n,lt,G):hn(n,lt,G)},U=function(){rn(s,Ft)?fn(t,ct,A.update):hn(t,ct,A.update)},z=function(){rn(s,Ft)?fn(s,ot,J):hn(s,ot,J)},F=function(){on(s),mn.call(s,ft,f,k)},X=function(){s[jt].display="",o&&on(o),function(){cn(n,f+" "+Ft)[0]||(M(),E(),ln(n[i],f+"-open"),C&&rn(C,"fade")?(ln(C,Ft),pn(C,q)):q(),U(),z(),R())}()},Y=function(t){var n=t[j];n=n[Tt](x)||n[Tt]("href")?n:n[Dt],n!==o||rn(s,Ft)||(s.modalTrigger=o,k=o,A.show(),t[It]())},G=function(t){A[H]&&27==t.which&&rn(s,Ft)&&A.hide()},J=function(t){var n=t[j];rn(s,Ft)&&(n[Dt][bt](r)===f||n[bt](r)===f||n===s&&A[P]!==h)&&(A.hide(),k=null,t[It]())};this.toggle=function(){rn(s,Ft)?this.hide():this.show()},this.show=function(){mn.call(s,st,f,k);var t=cn(n,f+" "+Ft)[0];t&&t!==s&&t.modalTrigger[p].hide(),this[P]&&!Zt&&O(),C&&Zt&&!rn(C,Ft)&&(C[V],l=vn(C),an(C,Ft)),setTimeout(function(){s[jt].display="block",B(),L(),D(),an(n[i],f+"-open"),an(s,Ft),s[yt](it,!1),U(),z(),R(),rn(s,"fade")?pn(s,F):F()},tn&&C?l:0)},this.hide=function(){mn.call(s,ht,f),C=un("."+m),l=C&&vn(C),ln(s,Ft),s[yt](it,!0),setTimeout(function(){rn(s,"fade")?pn(s,X):X()},tn&&C?l:0)},this.setContent=function(t){un("."+f+"-content",s)[At]=t},this.update=function(){rn(s,Ft)&&(B(),L(),D())},!o||p in o||fn(o,ot,Y),A[W]&&A.setContent(A[W]),!!o&&(o[p]=A)}};a[qt]([p,An,"["+l+'="modal"]']);var kn=function(e,o){e=un(e),o=o||{};var a=e[bt](E),l=e[bt](S),r=e[bt](D),c=e[bt](L),u=e[bt](M),s=e[bt](B),f="popover",h="template",d="trigger",v="class",p="div",g="fade",w="data-content",b="dismissible",y='<button type="button" class="close">×</button>',T=un(o[F]),x=un(s),C=sn(e,".modal"),A=sn(e,"."+$t),k=sn(e,"."+_t);this[h]=o[h]?o[h]:null,this[d]=o[d]?o[d]:a||at,this[U]=o[U]&&o[U]!==g?o[U]:l||g,this[z]=o[z]?o[z]:r||Jt,this[O]=parseInt(o[O]||u)||200,this[b]=!(!o[b]&&"true"!==c),this[F]=T?T:x?x:A?A:k?k:C?C:n[i];var N=this,P=e[bt](I)||null,H=e[bt](w)||null;if(H||this[h]){var W=null,q=0,R=this[z],X=function(t){null!==W&&t[j]===un(".close",W)&&N.hide()},Y=function(){N[F].removeChild(W),q=null,W=null},G=function(){P=e[bt](I),H=e[bt](w),W=n[xt](p);var t=n[xt](p);if(t[yt](v,"arrow"),W[Ct](t),null!==H&&null===N[h]){if(W[yt]("role","tooltip"),null!==P){var i=n[xt]("h3");i[yt](v,f+"-header"),i[At]=N[b]?P+y:P,W[Ct](i)}var o=n[xt](p);o[yt](v,f+"-body"),o[At]=N[b]&&null===P?H+y:H,W[Ct](o)}else{var a=n[xt](p);a[At]=N[h],W[At]=a.firstChild[At]}N[F][Ct](W),W[jt].display="block",W[yt](v,f+" bs-"+f+"-"+R+" "+N[U])},J=function(){!rn(W,Ft)&&an(W,Ft)},K=function(){wn(e,W,R,N[F])},Q=function(i){ot!=N[d]&&"focus"!=N[d]||!N[b]&&i(e,"blur",N.hide),N[b]&&i(n,ot,X),i(t,ct,N.hide)},V=function(){Q(fn),mn.call(e,ft,f)},Z=function(){Q(hn),Y(),mn.call(e,dt,f)};this.toggle=function(){null===W?N.show():N.hide()},this.show=function(){clearTimeout(q),q=setTimeout(function(){null===W&&(R=N[z],G(),K(),J(),mn.call(e,st,f),N[U]?pn(W,V):V())},20)},this.hide=function(){clearTimeout(q),q=setTimeout(function(){W&&null!==W&&rn(W,Ft)&&(mn.call(e,ht,f),ln(W,Ft),N[U]?pn(W,Z):Z())},N[O])},m in e||(N[d]===at?(fn(e,Qt[0],N.show),N[b]||fn(e,Qt[1],N.hide)):ot!=N[d]&&"focus"!=N[d]||fn(e,N[d],N.toggle)),e[m]=N}};a[qt]([m,kn,"["+l+'="popover"]']);var In=function(n,e){n=un(n);var i=un(n[bt](x)),o=n[bt]("data-offset");if(e=e||{},e[j]||i){for(var a,l=this,r=e[j]&&un(e[j])||i,c=r&&r[kt]("A"),u=parseInt(o||e.offset)||10,s=[],f=[],h=n[Z]<n[tt]?n:t,d=h===t,v=0,p=c[Mt];v<p;v++){var m=c[v][bt]("href"),w=m&&"#"===m.charAt(0)&&"#"!==m.slice(-1)&&un(m);w&&(s[qt](c[v]),f[qt](w))}var b=function(t){var e=s[t],i=f[t],o=e[Dt][Dt],l=rn(o,"dropdown")&&o[kt]("A")[0],r=d&&i[Nt](),c=rn(e,zt)||!1,h=(d?r[Jt]+a:i[X])-u,v=d?r[Kt]+a-u:f[t+1]?f[t+1][X]-u:n[tt],p=a>=h&&v>a;if(!c&&p)rn(e,zt)||(an(e,zt),l&&!rn(l,zt)&&an(l,zt),mn.call(n,"activate","scrollspy",s[t]));else if(p){if(!p&&!c||c&&p)return}else rn(e,zt)&&(ln(e,zt),l&&rn(l,zt)&&!cn(e[Dt],zt).length&&ln(l,zt))},y=function(){a=d?gn().y:n[G];for(var t=0,e=s[Mt];t<e;t++)b(t)};this.refresh=function(){y()},g in n||(fn(h,ut,l.refresh),fn(t,ct,l.refresh)),l.refresh(),n[g]=l}};a[qt]([g,In,"["+c+'="scroll"]']);var Nn=function(t,n){t=un(t);var e=t[bt](A),i="tab",o="height",a="float",r="isAnimating";n=n||{},this[o]=!!tn&&(n[o]||"true"===e);var c,u,s,f,h,d,v,p=this,m=sn(t,".nav"),g=!1,b=m&&un(".dropdown-toggle",m),y=function(){g[jt][o]="",ln(g,Xt),m[r]=!1},T=function(){g?d?y():setTimeout(function(){g[jt][o]=v+"px",g[V],pn(g,y)},50):m[r]=!1,mn.call(c,ft,i,u)},x=function(){g&&(s[jt][a]=Yt,f[jt][a]=Yt,h=s[tt]),an(f,zt),mn.call(c,st,i,u),ln(s,zt),mn.call(u,dt,i,c),g&&(v=f[tt],d=v===h,an(g,Xt),g[jt][o]=h+"px",g[Z],s[jt][a]="",f[jt][a]=""),rn(f,"fade")?setTimeout(function(){an(f,Ft),pn(f,T)},20):T()};if(m){m[r]=!1;var C=function(){var t,n=cn(m,zt);return 1!==n[Mt]||rn(n[0][Dt],"dropdown")?n[Mt]>1&&(t=n[n[Mt]-1]):t=n[0],t},k=function(){return un(C()[bt]("href"))},I=function(t){var n=t[j][bt]("href");t[It](),c=t[j][bt](l)===i||n&&"#"===n.charAt(0)?t[j]:t[j][Dt],!m[r]&&!rn(c,zt)&&p.show()};this.show=function(){c=c||t,f=un(c[bt]("href")),u=C(),s=k(),m[r]=!0,ln(u,zt),an(c,zt),b&&(rn(t[Dt],"dropdown-menu")?rn(b,zt)||an(b,zt):rn(b,zt)&&ln(b,zt)),mn.call(u,ht,i,c),rn(s,"fade")?(ln(s,Ft),pn(s,x)):x()},w in t||fn(t,ot,I),p[o]&&(g=k()[Dt]),t[w]=p}};a[qt]([w,Nn,"["+l+'="tab"]']);var Ln=function(e,o){e=un(e),o=o||{};var a=e[bt](S),l=e[bt](D),r=e[bt](M),c=e[bt](B),u="tooltip",s="class",f="title",h="fade",d="div",v=un(o[F]),p=un(c),m=sn(e,".modal"),g=sn(e,"."+$t),w=sn(e,"."+_t);this[U]=o[U]&&o[U]!==h?o[U]:a||h,this[z]=o[z]?o[z]:l||Jt,this[O]=parseInt(o[O]||r)||200,this[F]=v?v:p?p:g?g:w?w:m?m:n[i];var y=this,T=0,x=this[z],C=null,A=e[bt](f)||e[bt](I)||e[bt](N);if(A&&""!=A){var k=function(){y[F].removeChild(C),C=null,T=null},L=function(){if(A=e[bt](f)||e[bt](I)||e[bt](N),!A||""==A)return!1;C=n[xt](d),C[yt]("role",u);var t=n[xt](d);t[yt](s,"arrow"),C[Ct](t);var i=n[xt](d);i[yt](s,u+"-inner"),C[Ct](i),i[At]=A,y[F][Ct](C),C[yt](s,u+" bs-"+u+"-"+x+" "+y[U])},E=function(){wn(e,C,x,y[F])},P=function(){!rn(C,Ft)&&an(C,Ft)},H=function(){fn(t,ct,y.hide),mn.call(e,ft,u)},W=function(){hn(t,ct,y.hide),k(),mn.call(e,dt,u)};this.show=function(){clearTimeout(T),T=setTimeout(function(){if(null===C){if(x=y[z],0==L())return;E(),P(),mn.call(e,st,u),y[U]?pn(C,H):H()}},20)},this.hide=function(){clearTimeout(T),T=setTimeout(function(){C&&rn(C,Ft)&&(mn.call(e,ht,u),ln(C,Ft),y[U]?pn(C,W):W())},y[O])},this.toggle=function(){C?y.hide():y.show()},b in e||(e[yt](N,A),e.removeAttribute(f),fn(e,Qt[0],y.show),fn(e,Qt[1],y.hide)),e[b]=y}};a[qt]([b,Ln,"["+l+'="tooltip"]']);var En=function(t,n){for(var e=0,i=n[Mt];e<i;e++)new t(n[e])},Sn=o.initCallback=function(t){t=t||n;for(var e=0,i=a[Mt];e<i;e++)En(a[e][1],t[Lt](a[e][2]))};return n[i]?Sn():fn(n,"DOMContentLoaded",function(){Sn()}),{Alert:bn,Button:yn,Carousel:Tn,Collapse:xn,Dropdown:Cn,Modal:An,Popover:kn,ScrollSpy:In,Tab:Nn,Tooltip:Ln}});
