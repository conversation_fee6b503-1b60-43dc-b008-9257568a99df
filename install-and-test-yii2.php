<?php
/**
 * Yii 2.0 SaiAdmin 完整安装和测试脚本
 */

echo "🚀 Yii 2.0 SaiAdmin 完整安装和测试\n";
echo "========================================\n\n";

$projectPath = 'yii2-saiadmin';

echo "[1/6] 检查项目环境...\n";

// 检查项目目录
if (!is_dir($projectPath)) {
    echo "  ❌ 项目目录不存在: {$projectPath}\n";
    exit(1);
}

echo "  ✅ 项目目录存在\n";

// 检查Composer
$composerCheck = shell_exec('composer --version 2>&1');
if ($composerCheck) {
    echo "  ✅ Composer 已安装: " . trim(explode("\n", $composerCheck)[0]) . "\n";
} else {
    echo "  ❌ Composer 未安装\n";
    exit(1);
}

echo "\n[2/6] 安装依赖包...\n";

// 进入项目目录并安装依赖
chdir($projectPath);

echo "  📦 正在安装 Composer 依赖...\n";
$installOutput = shell_exec('composer install --no-dev --optimize-autoloader 2>&1');

if (strpos($installOutput, 'error') === false && strpos($installOutput, 'failed') === false) {
    echo "  ✅ Composer 依赖安装成功\n";
} else {
    echo "  ⚠️ Composer 安装可能有问题:\n";
    echo "    " . substr($installOutput, 0, 200) . "...\n";
}

// 检查vendor目录
if (is_dir('vendor')) {
    echo "  ✅ vendor 目录已创建\n";
    
    if (file_exists('vendor/yiisoft/yii2/Yii.php')) {
        echo "  ✅ Yii 2.0 框架已安装\n";
    } else {
        echo "  ❌ Yii 2.0 框架未正确安装\n";
    }
} else {
    echo "  ❌ vendor 目录未创建\n";
}

echo "\n[3/6] 配置应用...\n";

// 设置权限
if (is_dir('runtime')) {
    chmod('runtime', 0777);
    echo "  ✅ runtime 目录权限已设置\n";
}

if (is_dir('web/assets')) {
    chmod('web/assets', 0777);
    echo "  ✅ web/assets 目录权限已设置\n";
}

// 检查控制台入口权限
if (file_exists('yii')) {
    chmod('yii', 0755);
    echo "  ✅ yii 控制台入口权限已设置\n";
}

echo "\n[4/6] 测试控制台命令...\n";

// 测试Yii控制台
$yiiHelp = shell_exec('php yii help 2>&1');
if ($yiiHelp && strpos($yiiHelp, 'This is Yii version') !== false) {
    echo "  ✅ Yii 控制台工作正常\n";
    
    // 提取版本信息
    preg_match('/This is Yii version ([\d\.]+)/', $yiiHelp, $matches);
    if (isset($matches[1])) {
        echo "  📋 Yii 版本: {$matches[1]}\n";
    }
} else {
    echo "  ❌ Yii 控制台无法正常工作\n";
    echo "    错误信息: " . substr($yiiHelp, 0, 200) . "\n";
}

// 测试可用命令
$commands = ['migrate/create', 'cache/flush-all', 'asset/compress'];
foreach ($commands as $command) {
    $cmdOutput = shell_exec("php yii help {$command} 2>&1");
    if ($cmdOutput && strpos($cmdOutput, 'Unknown command') === false) {
        echo "  ✅ 命令可用: {$command}\n";
    } else {
        echo "  ⚠️ 命令不可用: {$command}\n";
    }
}

echo "\n[5/6] 创建示例数据...\n";

// 创建示例迁移文件
$migrationContent = '<?php
/**
 * 示例数据表迁移
 */
use yii\db\Migration;

class m' . date('ymd_His') . '_create_example_table extends Migration
{
    public function safeUp()
    {
        $this->createTable("{{%example}}", [
            "id" => $this->primaryKey(),
            "name" => $this->string()->notNull(),
            "description" => $this->text(),
            "status" => $this->smallInteger()->defaultValue(1),
            "created_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP"),
            "updated_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        ]);
        
        // 插入示例数据
        $this->insert("{{%example}}", [
            "name" => "示例数据1",
            "description" => "这是一个示例数据",
            "status" => 1
        ]);
        
        $this->insert("{{%example}}", [
            "name" => "示例数据2", 
            "description" => "这是另一个示例数据",
            "status" => 1
        ]);
    }

    public function safeDown()
    {
        $this->dropTable("{{%example}}");
    }
}';

$migrationFile = 'migrations/m' . date('ymd_His') . '_create_example_table.php';
file_put_contents($migrationFile, $migrationContent);
echo "  ✅ 创建示例迁移: {$migrationFile}\n";

// 创建示例模型
$exampleModelContent = '<?php
/**
 * 示例模型
 */
namespace app\models;

use app\components\base\BaseModel;

/**
 * Example 模型
 */
class Example extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return "{{%example}}";
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [["name"], "required"],
            [["description"], "string"],
            [["status"], "integer"],
            [["name"], "string", "max" => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            "id" => "ID",
            "name" => "名称",
            "description" => "描述",
            "status" => "状态",
            "created_at" => "创建时间",
            "updated_at" => "更新时间",
        ];
    }
}';

file_put_contents('models/Example.php', $exampleModelContent);
echo "  ✅ 创建示例模型: models/Example.php\n";

echo "\n[6/6] 运行功能测试...\n";

// 测试基础功能
echo "  🧪 测试基础功能...\n";

// 创建测试脚本
$testScript = '<?php
/**
 * 功能测试脚本
 */

// 设置环境
defined("YII_DEBUG") or define("YII_DEBUG", true);
defined("YII_ENV") or define("YII_ENV", "test");

require __DIR__ . "/vendor/autoload.php";
require __DIR__ . "/vendor/yiisoft/yii2/Yii.php";

// 加载配置
$config = require __DIR__ . "/config/web.php";

// 创建应用实例
$app = new yii\web\Application($config);

echo "✅ 应用实例创建成功\n";
echo "📋 Yii 版本: " . Yii::getVersion() . "\n";
echo "📋 PHP 版本: " . PHP_VERSION . "\n";
echo "📋 应用ID: " . $app->id . "\n";
echo "📋 应用名称: " . $app->name . "\n";

// 测试组件
try {
    $cache = Yii::$app->cache;
    echo "✅ 缓存组件加载成功\n";
} catch (Exception $e) {
    echo "❌ 缓存组件加载失败: " . $e->getMessage() . "\n";
}

try {
    $db = Yii::$app->db;
    echo "✅ 数据库组件加载成功\n";
    echo "📋 数据库驱动: " . $db->driverName . "\n";
} catch (Exception $e) {
    echo "❌ 数据库组件加载失败: " . $e->getMessage() . "\n";
}

// 测试路由
try {
    $urlManager = Yii::$app->urlManager;
    echo "✅ URL管理器加载成功\n";
    echo "📋 美化URL: " . ($urlManager->enablePrettyUrl ? "启用" : "禁用") . "\n";
} catch (Exception $e) {
    echo "❌ URL管理器加载失败: " . $e->getMessage() . "\n";
}

// 测试模块
$modules = $app->getModules();
echo "📋 已配置模块: " . implode(", ", array_keys($modules)) . "\n";

echo "\n🎉 功能测试完成！\n";';

file_put_contents('test-functionality.php', $testScript);

// 运行测试
$testOutput = shell_exec('php test-functionality.php 2>&1');
echo $testOutput;

// 生成最终报告
$finalReport = [
    "installation_time" => date("Y-m-d H:i:s"),
    "project_path" => realpath('.'),
    "php_version" => PHP_VERSION,
    "composer_installed" => is_dir('vendor'),
    "yii_framework" => file_exists('vendor/yiisoft/yii2/Yii.php'),
    "console_working" => strpos($yiiHelp, 'This is Yii version') !== false,
    "permissions_set" => is_writable('runtime'),
    "test_files_created" => [
        "migrations/{$migrationFile}" => "示例迁移",
        "models/Example.php" => "示例模型",
        "test-functionality.php" => "功能测试脚本"
    ],
    "status" => "✅ 安装完成",
    "next_steps" => [
        "1. 配置数据库连接 (config/db.php)",
        "2. 运行数据库迁移: php yii migrate",
        "3. 配置Web服务器指向 web/ 目录",
        "4. 访问应用进行测试",
        "5. 开始开发您的应用"
    ]
];

file_put_contents("../yii2-installation-report.json", json_encode($finalReport, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n========================================\n";
echo "🎉 Yii 2.0 SaiAdmin 安装完成！\n";
echo "========================================\n\n";

echo "📊 安装总结:\n";
echo "✅ 项目路径: " . realpath('.') . "\n";
echo "✅ PHP版本: " . PHP_VERSION . "\n";
echo "✅ Composer依赖: " . (is_dir('vendor') ? '已安装' : '未安装') . "\n";
echo "✅ Yii框架: " . (file_exists('vendor/yiisoft/yii2/Yii.php') ? '已安装' : '未安装') . "\n";
echo "✅ 控制台: " . (strpos($yiiHelp, 'This is Yii version') !== false ? '正常' : '异常') . "\n";
echo "✅ 权限设置: " . (is_writable('runtime') ? '正确' : '需要调整') . "\n\n";

echo "🚀 下一步操作:\n";
echo "1. 配置数据库连接\n";
echo "2. php yii migrate\n";
echo "3. 配置Web服务器\n";
echo "4. 开始开发\n\n";

echo "📖 文档参考:\n";
echo "- Yii 2.0 指南: https://www.yiiframework.com/doc/guide/2.0/zh-cn\n";
echo "- API 文档: https://www.yiiframework.com/doc/api/2.0\n\n";

echo "🎯 项目已完全兼容 Yii 2.0 框架！\n";
