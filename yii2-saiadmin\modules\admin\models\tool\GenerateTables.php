<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\modules\admin\models\tool;

use yii\db\ActiveRecord;
/**
 * 代码生成业务模型
 * Class GenerateTables
 * @package app\model
 */
class GenerateTables extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    protected $table = 'sa_tool_generate_tables';

    public function getOptionsAttr($value)
    {
        return json_decode($value ?? '', true);
    }

}