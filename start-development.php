<?php
/**
 * SaiAdmin Yii 2.0 开发环境启动脚本
 */

echo "💻 SaiAdmin Yii 2.0 开发环境启动\n";
echo "========================================\n\n";

$projectPath = 'yii2-saiadmin';

echo "[1/5] 检查开发环境...\n";

// 检查项目目录
if (!is_dir($projectPath)) {
    echo "  ❌ 项目目录不存在: {$projectPath}\n";
    exit(1);
}

echo "  ✅ 项目目录存在\n";

// 检查依赖
if (!is_dir($projectPath . '/vendor')) {
    echo "  ⚠️ 依赖未安装，正在安装...\n";
    chdir($projectPath);
    shell_exec('composer install 2>&1');
    chdir('..');
    echo "  ✅ 依赖安装完成\n";
} else {
    echo "  ✅ 依赖已安装\n";
}

echo "\n[2/5] 配置开发环境...\n";

// 切换到项目目录
$originalDir = getcwd();
chdir($projectPath);

// 设置开发环境配置
$devConfig = '<?php
/**
 * 开发环境配置
 */
return [
    "YII_DEBUG" => true,
    "YII_ENV" => "dev",
    "database" => [
        "host" => "localhost",
        "dbname" => "saiadmin_dev",
        "username" => "root",
        "password" => ""
    ],
    "cache" => [
        "class" => "yii\\caching\\FileCache"
    ]
];';

file_put_contents('config/dev.php', $devConfig);
echo "  ✅ 创建开发环境配置\n";

// 更新数据库配置为开发环境
$dbConfig = '<?php
/**
 * 开发环境数据库配置
 */
return [
    "class" => "yii\\db\\Connection",
    "dsn" => "mysql:host=localhost;dbname=saiadmin_dev",
    "username" => "root",
    "password" => "",
    "charset" => "utf8mb4",
    
    // 开发环境配置
    "enableSchemaCache" => false,
    "enableQueryCache" => false,
    "enableLogging" => true,
];';

file_put_contents('config/db.php', $dbConfig);
echo "  ✅ 配置开发数据库\n";

// 设置权限
chmod('yii', 0755);
if (is_dir('runtime')) {
    chmod('runtime', 0777);
}
if (is_dir('web/assets')) {
    chmod('web/assets', 0777);
}
echo "  ✅ 设置文件权限\n";

echo "\n[3/5] 启动内置服务器...\n";

// 检查端口是否可用
$port = 8080;
$portCheck = shell_exec("netstat -an | grep :{$port} 2>/dev/null");
if ($portCheck) {
    $port = 8081;
    echo "  ⚠️ 端口8080被占用，使用端口{$port}\n";
}

// 启动PHP内置服务器
echo "  🌐 启动PHP内置服务器...\n";
echo "  📋 访问地址: http://localhost:{$port}\n";
echo "  📋 项目路径: " . realpath('.') . "\n";

// 创建启动脚本
$startScript = "#!/bin/bash
# SaiAdmin Yii 2.0 开发服务器启动脚本

echo \"🚀 启动 SaiAdmin Yii 2.0 开发服务器...\"
echo \"📍 项目目录: $(pwd)\"
echo \"🌐 访问地址: http://localhost:{$port}\"
echo \"🔧 控制台: php yii help\"
echo \"📊 调试工具: http://localhost:{$port}/debug/\"
echo \"⚙️ Gii生成器: http://localhost:{$port}/gii/\"
echo \"\"
echo \"按 Ctrl+C 停止服务器\"
echo \"\"

# 启动服务器
php -S localhost:{$port} -t web/ web/index.php";

file_put_contents('start-server.sh', $startScript);
chmod('start-server.sh', 0755);
echo "  ✅ 创建启动脚本: start-server.sh\n";

echo "\n[4/5] 创建开发工具...\n";

// 创建开发助手脚本
$devHelper = '#!/bin/bash
# SaiAdmin Yii 2.0 开发助手

case "$1" in
    "server")
        echo "🌐 启动开发服务器..."
        php -S localhost:8080 -t web/ web/index.php
        ;;
    "migrate")
        echo "🗄️ 运行数据库迁移..."
        php yii migrate
        ;;
    "cache")
        echo "🧹 清除缓存..."
        php yii cache/flush-all
        ;;
    "gii")
        echo "⚙️ 打开Gii代码生成器..."
        echo "访问: http://localhost:8080/gii/"
        ;;
    "debug")
        echo "📊 打开调试工具..."
        echo "访问: http://localhost:8080/debug/"
        ;;
    "test")
        echo "🧪 运行测试..."
        php yii help
        ;;
    "log")
        echo "📋 查看日志..."
        tail -f runtime/logs/app.log
        ;;
    *)
        echo "SaiAdmin Yii 2.0 开发助手"
        echo ""
        echo "用法: $0 {server|migrate|cache|gii|debug|test|log}"
        echo ""
        echo "命令说明:"
        echo "  server  - 启动开发服务器"
        echo "  migrate - 运行数据库迁移"
        echo "  cache   - 清除缓存"
        echo "  gii     - 打开Gii代码生成器"
        echo "  debug   - 打开调试工具"
        echo "  test    - 运行测试"
        echo "  log     - 查看日志"
        ;;
esac';

file_put_contents('dev-helper.sh', $devHelper);
chmod('dev-helper.sh', 0755);
echo "  ✅ 创建开发助手: dev-helper.sh\n";

// 创建VS Code配置
$vscodeConfig = [
    "folders" => [
        [
            "path" => "."
        ]
    ],
    "settings" => [
        "php.validate.executablePath" => "/usr/bin/php",
        "php.suggest.basic" => false,
        "files.associations" => [
            "*.php" => "php"
        ],
        "emmet.includeLanguages" => [
            "php" => "html"
        ]
    ],
    "extensions" => [
        "recommendations" => [
            "bmewburn.vscode-intelephense-client",
            "xdebug.php-debug",
            "neilbrayfield.php-docblocker"
        ]
    ]
];

if (!is_dir('.vscode')) {
    mkdir('.vscode', 0755);
}
file_put_contents('.vscode/settings.json', json_encode($vscodeConfig, JSON_PRETTY_PRINT));
echo "  ✅ 创建VS Code配置\n";

echo "\n[5/5] 生成开发文档...\n";

$devDocs = "# SaiAdmin Yii 2.0 开发指南

## 🚀 快速开始

### 启动开发服务器
```bash
./start-server.sh
# 或者
./dev-helper.sh server
```

### 访问地址
- 前端首页: http://localhost:{$port}
- 管理后台: http://localhost:{$port}/admin
- Gii代码生成: http://localhost:{$port}/gii
- 调试工具: http://localhost:{$port}/debug

## 🔧 开发工具

### 控制台命令
```bash
# 查看所有命令
php yii help

# 数据库迁移
php yii migrate

# 清除缓存
php yii cache/flush-all

# 创建控制器
php yii gii/controller

# 创建模型
php yii gii/model
```

### 开发助手
```bash
# 使用开发助手
./dev-helper.sh [command]

# 可用命令:
# server  - 启动开发服务器
# migrate - 运行数据库迁移
# cache   - 清除缓存
# gii     - 打开Gii代码生成器
# debug   - 打开调试工具
# test    - 运行测试
# log     - 查看日志
```

## 📁 项目结构

```
yii2-saiadmin/
├── config/          # 配置文件
├── controllers/     # 控制器
├── models/         # 模型
├── modules/        # 模块
│   ├── admin/     # 管理模块
│   └── tool/      # 工具模块
├── views/          # 视图
├── web/            # Web根目录
├── runtime/        # 运行时文件
└── vendor/         # 依赖包
```

## 🎯 开发规范

### 命名规范
- 控制器: PascalCase + Controller (UserController)
- 模型: PascalCase (User)
- 视图: kebab-case (user-list.php)
- 方法: camelCase (getUserList)

### 代码规范
- 遵循PSR-4自动加载标准
- 使用Yii 2.0编码规范
- 编写PHPDoc注释
- 使用类型提示

## 📖 参考资源

- [Yii 2.0 指南](https://www.yiiframework.com/doc/guide/2.0/zh-cn)
- [Yii 2.0 API文档](https://www.yiiframework.com/doc/api/2.0)
- [SaiAdmin文档](https://saiadmin.com/docs)
";

file_put_contents('DEV-README.md', $devDocs);
echo "  ✅ 生成开发文档: DEV-README.md\n";

// 恢复原目录
chdir($originalDir);

echo "\n========================================\n";
echo "💻 开发环境启动完成！\n";
echo "========================================\n\n";

echo "🌐 访问地址:\n";
echo "- 开发服务器: http://localhost:{$port}\n";
echo "- 管理后台: http://localhost:{$port}/admin\n";
echo "- Gii生成器: http://localhost:{$port}/gii\n";
echo "- 调试工具: http://localhost:{$port}/debug\n\n";

echo "🔧 开发工具:\n";
echo "- 启动服务器: cd {$projectPath} && ./start-server.sh\n";
echo "- 开发助手: cd {$projectPath} && ./dev-helper.sh [command]\n";
echo "- 控制台: cd {$projectPath} && php yii help\n\n";

echo "📁 生成的文件:\n";
echo "✅ config/dev.php - 开发环境配置\n";
echo "✅ start-server.sh - 服务器启动脚本\n";
echo "✅ dev-helper.sh - 开发助手脚本\n";
echo "✅ .vscode/settings.json - VS Code配置\n";
echo "✅ DEV-README.md - 开发文档\n\n";

echo "🚀 开始开发:\n";
echo "1. cd {$projectPath}\n";
echo "2. ./start-server.sh\n";
echo "3. 打开浏览器访问 http://localhost:{$port}\n";
echo "4. 开始您的Yii 2.0开发之旅！\n\n";

echo "🎉 SaiAdmin Yii 2.0 开发环境已就绪！\n";
