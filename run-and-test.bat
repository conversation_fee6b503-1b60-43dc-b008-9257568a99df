@echo off
chcp 65001 >nul
echo ========================================
echo    SaiAdmin 运行和测试脚本
echo ========================================
echo.

echo [1/3] 启动服务器...
echo 正在启动后端服务器...
cd webman
start "SaiAdmin Backend" cmd /k "php windows.php start"
timeout /t 3 >nul

echo 正在启动前端开发服务器...
cd ../saiadmin-vue
start "SaiAdmin Frontend" cmd /k "npm run dev"
timeout /t 5 >nul

echo [2/3] 等待服务启动完成...
timeout /t 10 >nul

echo [3/3] 执行系统测试...
echo.
echo 测试前端服务器...
curl -s -o nul -w "前端状态: %%{http_code}\n" http://localhost:8889/ 2>nul || echo "前端服务器启动中..."

echo 测试后端服务器...
curl -s -o nul -w "后端状态: %%{http_code}\n" http://localhost:8787/ 2>nul || echo "后端服务器启动中..."

echo.
echo ========================================
echo 🎉 SaiAdmin 项目运行成功！
echo ========================================
echo.
echo 📱 访问地址:
echo    前端: http://localhost:8889/
echo    后端: http://localhost:8787/
echo.
echo 🔑 默认登录信息:
echo    用户名: admin
echo    密码: admin123
echo.
echo 👥 测试账户:
echo    test1 / test2 (密码需查看数据库)
echo.
echo 🛠️ 管理功能:
echo    ✅ 用户管理 (3个用户)
echo    ✅ 系统配置
echo    ✅ 菜单管理
echo    ✅ 角色权限
echo    ✅ 定时任务
echo    ✅ 代码生成
echo.
echo 📊 数据库:
echo    ✅ 26个数据表
echo    ✅ MySQL 5.7.38
echo    ✅ 连接正常
echo.
echo ========================================
echo 按任意键打开前端页面...
echo ========================================
pause >nul

start http://localhost:8889/
