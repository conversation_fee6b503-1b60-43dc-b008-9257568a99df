<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: SaiAdmin Team (修复版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\basic;

use think\Model;

/**
 * 普通模型基类 (不使用软删除)
 * @package plugin\saiadmin\basic
 */
class BaseNormalModel extends Model
{
    // 添加时间
    protected $createTime = "created_at";
    // 更新时间
    protected $updateTime = "updated_at";
    // 只读字段
    protected $readonly = array("created_by", "created_at");

    /**
     * 时间范围搜索
     */
    public function searchCreatedAtAttr($query, $value)
    {
        $query->whereTime("created_at", "between", $value);
    }

    /**
     * 新增前
     */
    public static function onBeforeInsert($model)
    {
        if (function_exists("getCurrentInfo")) {
            $info = getCurrentInfo();
            $info && $model->setAttr("created_by", $info["id"]);
        }
    }

    /**
     * 写入前
     */
    public static function onBeforeWrite($model)
    {
        if (function_exists("getCurrentInfo")) {
            $info = getCurrentInfo();
            $info && $model->setAttr("updated_by", $info["id"]);
        }
    }
}