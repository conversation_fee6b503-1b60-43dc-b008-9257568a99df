<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 安全中间件
// +----------------------------------------------------------------------
// | Author: AI Assistant (安全优化版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use plugin\saiadmin\exception\ApiException;

/**
 * 安全中间件
 */
class SecurityMiddleware implements MiddlewareInterface
{
    /**
     * 请求频率限制
     */
    private static $requestCounts = [];
    private static $lastCleanup = 0;
    
    /**
     * 危险操作检测模式
     */
    private $dangerousPatterns = [
        // SQL注入检测
        '/(\bunion\b|\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b|\bcreate\b|\balter\b)/i',
        // XSS检测
        '/<script[^>]*>.*?<\/script>/is',
        '/javascript:/i',
        '/on\w+\s*=/i',
        // 路径遍历检测
        '/\.\.[\/\\\\]/i',
        // 命令注入检测
        '/(\||&|;|\$\(|\`)/i'
    ];

    public function process(Request $request, callable $handler): Response
    {
        // 1. 请求频率限制
        $this->rateLimiting($request);
        
        // 2. 输入验证和过滤
        $this->validateInput($request);
        
        // 3. 安全头设置
        $response = $handler($request);
        $this->setSecurityHeaders($response);
        
        // 4. 响应内容安全检查
        $this->validateResponse($response);
        
        return $response;
    }

    /**
     * 请求频率限制
     */
    private function rateLimiting(Request $request): void
    {
        $clientIp = $this->getClientIp($request);
        $currentTime = time();
        
        // 清理过期记录
        if ($currentTime - self::$lastCleanup > 60) {
            $this->cleanupOldRecords($currentTime);
            self::$lastCleanup = $currentTime;
        }
        
        // 检查请求频率
        $key = $clientIp . '_' . floor($currentTime / 60); // 按分钟统计
        if (!isset(self::$requestCounts[$key])) {
            self::$requestCounts[$key] = 0;
        }
        
        self::$requestCounts[$key]++;
        
        // 限制：每分钟最多100个请求
        if (self::$requestCounts[$key] > 100) {
            throw new ApiException('请求过于频繁，请稍后再试', 429);
        }
        
        // 特殊接口更严格的限制
        $uri = $request->uri();
        if (in_array($uri, ['/core/login', '/core/captcha'])) {
            $strictKey = $clientIp . '_strict_' . floor($currentTime / 60);
            if (!isset(self::$requestCounts[$strictKey])) {
                self::$requestCounts[$strictKey] = 0;
            }
            self::$requestCounts[$strictKey]++;
            
            // 登录接口：每分钟最多10次
            if (self::$requestCounts[$strictKey] > 10) {
                throw new ApiException('登录尝试过于频繁，请稍后再试', 429);
            }
        }
    }

    /**
     * 输入验证和过滤
     */
    private function validateInput(Request $request): void
    {
        // 检查请求方法
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
        if (!in_array($request->method(), $allowedMethods)) {
            throw new ApiException('不支持的请求方法', 405);
        }
        
        // 检查Content-Type
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            $contentType = $request->header('content-type', '');
            if (!empty($contentType) && 
                !str_contains($contentType, 'application/json') && 
                !str_contains($contentType, 'application/x-www-form-urlencoded') &&
                !str_contains($contentType, 'multipart/form-data')) {
                throw new ApiException('不支持的Content-Type', 415);
            }
        }
        
        // 检查请求大小
        $contentLength = $request->header('content-length', 0);
        if ($contentLength > 10 * 1024 * 1024) { // 10MB限制
            throw new ApiException('请求体过大', 413);
        }
        
        // 危险输入检测
        $this->detectDangerousInput($request);
    }

    /**
     * 危险输入检测
     */
    private function detectDangerousInput(Request $request): void
    {
        $inputs = array_merge(
            $request->get(),
            $request->post(),
            [$request->rawBody()]
        );
        
        foreach ($inputs as $key => $value) {
            if (is_string($value)) {
                foreach ($this->dangerousPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        // 记录安全日志
                        $this->logSecurityEvent($request, 'dangerous_input', [
                            'field' => $key,
                            'value' => substr($value, 0, 100),
                            'pattern' => $pattern
                        ]);
                        throw new ApiException('检测到危险输入', 400);
                    }
                }
            }
        }
    }

    /**
     * 设置安全响应头
     */
    private function setSecurityHeaders(Response $response): void
    {
        $response->withHeaders([
            // 防止XSS攻击
            'X-XSS-Protection' => '1; mode=block',
            // 防止MIME类型嗅探
            'X-Content-Type-Options' => 'nosniff',
            // 防止点击劫持
            'X-Frame-Options' => 'DENY',
            // 强制HTTPS
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
            // 内容安全策略
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            // 引用策略
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            // 权限策略
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
            // 移除服务器信息
            'Server' => 'SaiAdmin'
        ]);
    }

    /**
     * 响应内容验证
     */
    private function validateResponse(Response $response): void
    {
        $content = $response->rawBody();
        
        // 检查是否意外泄露敏感信息
        $sensitivePatterns = [
            '/password["\']?\s*[:=]\s*["\']?[^"\'\s,}]+/i',
            '/token["\']?\s*[:=]\s*["\']?[^"\'\s,}]+/i',
            '/secret["\']?\s*[:=]\s*["\']?[^"\'\s,}]+/i',
            '/key["\']?\s*[:=]\s*["\']?[^"\'\s,}]+/i'
        ];
        
        foreach ($sensitivePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                // 记录安全事件但不阻止响应
                $this->logSecurityEvent(request(), 'sensitive_data_leak', [
                    'pattern' => $pattern
                ]);
            }
        }
    }

    /**
     * 获取客户端真实IP
     */
    private function getClientIp(Request $request): string
    {
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];
        
        foreach ($headers as $header) {
            $ip = $request->header(strtolower(str_replace('HTTP_', '', $header)));
            if (!empty($ip) && $ip !== 'unknown') {
                // 取第一个IP（如果有多个）
                $ip = explode(',', $ip)[0];
                if (filter_var(trim($ip), FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return trim($ip);
                }
            }
        }
        
        return $request->getRemoteIp();
    }

    /**
     * 清理过期记录
     */
    private function cleanupOldRecords(int $currentTime): void
    {
        $expireTime = $currentTime - 300; // 5分钟前
        foreach (self::$requestCounts as $key => $count) {
            $keyTime = (int)substr($key, strrpos($key, '_') + 1);
            if ($keyTime < $expireTime) {
                unset(self::$requestCounts[$key]);
            }
        }
    }

    /**
     * 记录安全事件
     */
    private function logSecurityEvent(Request $request, string $event, array $data = []): void
    {
        $logData = [
            'event' => $event,
            'ip' => $this->getClientIp($request),
            'uri' => $request->uri(),
            'method' => $request->method(),
            'user_agent' => $request->header('user-agent'),
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $data
        ];
        
        // 这里可以写入到专门的安全日志文件或数据库
        error_log('SECURITY_EVENT: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
    }
}
