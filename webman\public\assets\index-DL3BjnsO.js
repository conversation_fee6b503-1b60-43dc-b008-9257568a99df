import{h as A,n as V,k as m,t,l as a,a1 as b,m as y,z as x,F as H,P as ee,j as S,p as L,r as M,y as B,w as Y,o as G,C as ue,s as K,H as Be,q as ne,G as de,M as me,N as pe,T as Me,ae as Ue,O as Ve,a as $e}from"./@vue-9ZIPiVZG.js";import{u as Q,_ as le,a as oe,i as Qe,t as _e,c as qe,b as ye,d as Ae,r as ze,e as Je,f as Ye}from"./index-DkGLNqVb.js";import{i as Ie}from"./resize-observer-polyfill-B1PUzC5B.js";import{_ as ie}from"./logo-B7uA2Tfd.js";import{u as se,b as te}from"./vue-router-DXldG2q0.js";import{a as Pe}from"./avatar-DvSZjoFF.js";import{d as Re}from"./pinia-CtMvrpix.js";import{v as Oe}from"./vue-i18n-PeqK6azz.js";import{M as je}from"./@arco-design-uttiljWv.js";import{S as Fe}from"./sortablejs-C83syoBY.js";import{a as Xe}from"./user-pcE09jl3.js";import{s as Ke}from"./vue-color-kit-w75Wyu4C.js";import"./@wangeditor-Bg8kJaak.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./dayjs-DUkVwsK-.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./@intlify-CJ2pDqUV.js";const Ge=Re("iframe",{state:()=>({iframes:[],name:null,show:!0}),getters:{getState(){return{...this.$state}}},actions:{addIframe(e){this.iframes.includes(e)||this.iframes.push(e)},removeIframe(e){const s=this.iframes.indexOf(e);s!==-1&&this.iframes.splice(s,1)},display(){this.show=!0},hidden(){this.show=!1},setName(e){this.name=e},clearIframe(){this.iframes=[]}}});let et={messageList:[]};const We=Re("message",{state:()=>({...et}),getters:{getState(){return{...this.$state}}},actions:{updateMessage(e){this.$patch(e)}}}),tt={class:"ml-2 mt-3.5 hidden lg:block"},nt={class:"flex items-center"},ot={class:"ml-1"},st={class:"flex items-center"},at={class:"ml-1"},Le={__name:"ma-breadcrumb",setup(e){const s=Q(),o=se(),n=te();return(i,r)=>{const u=A("icon-dashboard"),h=A("a-breadcrumb-item"),l=A("sa-icon"),$=A("a-breadcrumb");return m(),V("div",tt,[t($,null,{default:a(()=>[t(h,{class:"cursor-pointer",onClick:r[0]||(r[0]=O=>b(n).push("/dashboard"))},{default:a(()=>[y("div",nt,[t(u,{style:{color:"rgb(var(--color-text-1))"}}),y("span",ot,x(i.$t("menus.dashboard")),1)])]),_:1}),(m(!0),V(H,null,ee(b(o).matched,(O,w)=>(m(),V(H,{key:w},[w>0&&!["/","/home","/dashboard"].includes(O.path)?(m(),S(h,{key:0},{default:a(()=>[y("div",st,[t(l,{icon:O.meta.icon,size:16,style:{color:"rgb(var(--color-text-1))",width:"1rem"}},null,8,["icon"]),y("span",at,x(b(s).i18n?i.$t("menus."+O.name).indexOf(".")>0?O.meta.title:i.$t("menus."+O.name):O.meta.title),1)])]),_:2},1024)):L("",!0)],64))),128))]),_:1})])}}},lt={class:"mgs-nfc rounded p-2 shadow-lg"},At={class:"flex justify-between",style:{"font-size":"13px"}},it=["src"],ct=["innerHTML"],rt={__name:"message-notification",setup(e){const s=We(),o=M({}),n=M(!1),i=async r=>{o.value=r,n.value=!0};return(r,u)=>{const h=A("a-badge"),l=A("a-avatar"),$=A("a-list-item-meta"),O=A("a-list-item"),w=A("a-list"),g=A("a-empty"),d=A("a-tab-pane"),k=A("a-tabs"),E=A("a-typography-title"),v=A("a-space"),R=A("a-typography-paragraph"),Z=A("a-typography"),_=A("a-modal");return m(),V(H,null,[y("div",lt,[t(k,{"default-active-key":"message",type:"rounded"},{default:a(()=>[t(d,{key:"message"},{title:a(()=>[B(x(r.$t("sys.operationMessage.message"))+" ",1),b(s).messageList.length>0?(m(),S(h,{key:0,count:5,dot:"",dotStyle:{width:"5px",height:"5px",top:"-8px"}})):L("",!0)]),default:a(()=>[b(s).messageList.length>0?(m(),S(w,{key:0,"max-height":230,class:"h-full"},{default:a(()=>[(m(!0),V(H,null,ee(b(s).messageList,f=>(m(),S(O,{key:f.id,class:"cursor-pointer",onClick:C=>i(f)},{default:a(()=>[t($,{title:f.title},{description:a(()=>[y("div",At,[y("span",null,"发送人："+x(f.send_user.nickname),1),y("span",null,"时间："+x(f.create_time.substr(0,10)),1)])]),avatar:a(()=>[t(l,{shape:"square"},{default:a(()=>[y("img",{alt:"avatar",src:`${f.send_user.avatar}`||b(Pe)},null,8,it)]),_:2},1024)]),_:2},1032,["title"])]),_:2},1032,["onClick"]))),128))]),_:1})):(m(),S(g,{key:1,class:"h-full"}))]),_:1}),t(d,{key:"todo"},{title:a(()=>[B(x(r.$t("sys.operationMessage.todo")),1)]),default:a(()=>[t(g,{class:"h-full"})]),_:1})]),_:1})]),t(_,{visible:n.value,"onUpdate:visible":u[0]||(u[0]=f=>n.value=f),fullscreen:"",footer:!1},{title:a(()=>u[1]||(u[1]=[B("消息详情")])),default:a(()=>[t(Z,{style:{marginTop:"-30px"}},{default:a(()=>[t(E,{class:"text-center"},{default:a(()=>{var f;return[B(x((f=o.value)==null?void 0:f.title),1)]}),_:1}),t(R,{class:"text-right",style:{"font-size":"13px",color:"var(--color-text-3)"}},{default:a(()=>[t(v,{size:"large"},{default:a(()=>{var f;return[y("span",null,"创建时间："+x((f=o.value)==null?void 0:f.create_time),1)]}),_:1})]),_:1}),t(R,null,{default:a(()=>{var f;return[y("div",{innerHTML:(f=o.value)==null?void 0:f.content},null,8,ct)]}),_:1})]),_:1})]),_:1},8,["visible"])],64)}}},ut=le(rt,[["__scopeId","data-v-403528f9"]]),X=function(e){this.doNotConnect=0,e=e||{},e.heartbeat=e.heartbeat||25e3,e.pingTimeout=e.pingTimeout||1e4,this.config=e,this.uid=0,this.channels={},this.connection=null,this.pingTimeoutTimer=0,X.instances.push(this),this.createConnection()};X.prototype.checkoutPing=function(){var e=this;setTimeout(function(){e.connection.state==="connected"&&(e.connection.send('{"event":"pusher:ping","data":{}}'),e.pingTimeoutTimer&&(clearTimeout(e.pingTimeoutTimer),e.pingTimeoutTimer=0),e.pingTimeoutTimer=setTimeout(function(){e.connection.closeAndClean(),e.connection.doNotConnect||e.connection.waitReconnect()},e.config.pingTimeout))},this.config.heartbeat)};X.prototype.channel=function(e){return this.channels.find(e)};X.prototype.allChannels=function(){return this.channels.all()};X.prototype.createConnection=function(){if(this.connection)throw Error("Connection already exist");var e=this,s=this.config.url;function o(){for(var n in e.channels)e.channels[n].subscribed=!1}this.connection=new ce({url:s,app_key:this.config.app_key,onOpen:function(){e.connection.state="connecting",e.checkoutPing()},onMessage:function(n){e.pingTimeoutTimer&&(clearTimeout(e.pingTimeoutTimer),e.pingTimeoutTimer=0),n=JSON.parse(n.data);var i=n.event,r=n.channel;if(i==="pusher:pong"){e.checkoutPing();return}if(i==="pusher:error")throw Error(n.data.message);var u=JSON.parse(n.data),h;if(i==="pusher_internal:subscription_succeeded"){h=e.channels[r],h.subscribed=!0,h.processQueue(),h.emit("pusher:subscription_succeeded");return}if(i==="pusher:connection_established"&&(e.connection.socket_id=u.socket_id,e.connection.state="connected",e.subscribeAll()),i.indexOf("pusher_internal")!==-1){console.log("Event '"+i+"' not implement");return}h=e.channels[r],h&&h.emit(i,u)},onClose:function(){o()},onError:function(){o()}})};X.prototype.disconnect=function(){this.connection.doNotConnect=1,this.connection.close()};X.prototype.subscribeAll=function(){if(this.connection.state==="connected")for(var e in this.channels)this.channels[e].processSubscribe()};X.prototype.unsubscribe=function(e){this.channels[e]&&(delete this.channels[e],this.connection.state==="connected"&&this.connection.send(JSON.stringify({event:"pusher:unsubscribe",data:{channel:e}})))};X.prototype.unsubscribeAll=function(){var e=Object.keys(this.channels);if(e.length&&this.connection.state==="connected")for(var s in this.channels)this.unsubscribe(s);this.channels={}};X.prototype.subscribe=function(e){return this.channels[e]?this.channels[e]:e.indexOf("private-")===0?De(e,this):e.indexOf("presence-")===0?mt(e,this):dt(e,this)};X.instances=[];function dt(e,s){var o=new ge(s.connection,e);return s.channels[e]=o,o.subscribeCb=function(){s.connection.send(JSON.stringify({event:"pusher:subscribe",data:{channel:e}}))},o}function De(e,s){var o=new ge(s.connection,e);return s.channels[e]=o,o.subscribeCb=function(){ft({url:s.config.auth,type:"POST",data:{channel_name:e,socket_id:s.connection.socket_id},success:function(n){n=JSON.parse(n),n.channel=e,s.connection.send(JSON.stringify({event:"pusher:subscribe",data:n}))},error:function(n){throw Error(n)}})},o.processSubscribe(),o}function mt(e,s){return De(e,s)}function ce(e){this.dispatcher=new Ne,He(this,this.dispatcher);var s=["on","off","emit"];for(var o in s)this[s[o]]=this.dispatcher[s[o]];this.options=e,this.state="initialized",this.doNotConnect=0,this.reconnectInterval=1,this.connection=null,this.reconnectTimer=0,this.connect()}ce.prototype.updateNetworkState=function(e){var s=this.state;this.state=e,s!==e&&this.emit("state_change",{previous:s,current:e})};ce.prototype.connect=function(){if(this.doNotConnect=0,this.state==="connected"){console.log('networkState is "'+this.state+'" and do not need connect');return}this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=0),this.closeAndClean();var e=this.options,s=new WebSocket(e.url+"/app/"+e.app_key);this.updateNetworkState("connecting");var o=this;s.onopen=function(n){if(o.reconnectInterval=1,o.doNotConnect){o.updateNetworkState("disconnected"),s.close();return}e.onOpen&&e.onOpen(n)},e.onMessage&&(s.onmessage=e.onMessage),s.onclose=function(n){s.onmessage=s.onopen=s.onclose=s.onerror=null,o.updateNetworkState("disconnected"),o.doNotConnect||o.waitReconnect(),e.onClose&&e.onClose(n)},s.onerror=function(n){o.close(),o.doNotConnect||o.waitReconnect(),e.onError&&e.onError(n)},this.connection=s};ce.prototype.closeAndClean=function(){if(this.connection){var e=this.connection;e.onmessage=e.onopen=e.onclose=e.onerror=null;try{e.close()}catch{}this.updateNetworkState("disconnected")}};ce.prototype.waitReconnect=function(){if(!(this.state==="connected"||this.state==="connecting")&&!this.doNotConnect){this.updateNetworkState("connecting");var e=this;this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=setTimeout(function(){e.connect()},this.reconnectInterval),this.reconnectInterval<1e3?this.reconnectInterval=1e3:this.reconnectInterval=this.reconnectInterval*2,this.reconnectInterval>2e3&&navigator.onLine&&(e.reconnectInterval=2e3)}};ce.prototype.send=function(e){if(this.state!=="connected"){console.trace('networkState is "'+this.state+'", can not send '+e);return}this.connection.send(e)};ce.prototype.close=function(){this.updateNetworkState("disconnected"),this.connection.close()};var He=function(e,s){for(var o in s)s.hasOwnProperty(o)&&(e[o]=s[o]);function n(){this.constructor=e}e.prototype=s===null?Object.create(s):(n.prototype=s.prototype,new n)};function ge(e,s){this.subscribed=!1,this.dispatcher=new Ne,this.connection=e,this.channelName=s,this.subscribeCb=null,this.queue=[],He(this,this.dispatcher);var o=["on","off","emit"];for(var n in o)this[o[n]]=this.dispatcher[o[n]]}ge.prototype.processSubscribe=function(){this.connection.state==="connected"&&this.subscribeCb()};ge.prototype.processQueue=function(){if(!(this.connection.state!=="connected"||!this.subscribed)){for(var e in this.queue)this.queue[e]();this.queue=[]}};ge.prototype.trigger=function(e,s){if(e.indexOf("client-")!==0)throw new Error("Event '"+e+"' should start with 'client-'");var o=this;this.queue.push(function(){o.connection.send(JSON.stringify({event:e,data:s,channel:o.channelName}))}),this.processQueue()};var ve=function(){var e={};function s(_){for(var f=[],C=1;C<arguments.length;C++)f[C-1]=arguments[C];for(var z=0;z<f.length;z++){var P=f[z];for(var N in P)P[N]&&P[N].constructor&&P[N].constructor===Object?_[N]=s(_[N]||{},P[N]):_[N]=P[N]}return _}e.extend=s;function o(){for(var _=["Push"],f=0;f<arguments.length;f++)typeof arguments[f]=="string"?_.push(arguments[f]):_.push(Z(arguments[f]));return _.join(" : ")}e.stringify=o;function n(_,f){var C=Array.prototype.indexOf;if(_===null)return-1;if(C&&_.indexOf===C)return _.indexOf(f);for(var z=0,P=_.length;z<P;z++)if(_[z]===f)return z;return-1}e.arrayIndexOf=n;function i(_,f){for(var C in _)Object.prototype.hasOwnProperty.call(_,C)&&f(_[C],C,_)}e.objectApply=i;function r(_){var f=[];return i(_,function(C,z){f.push(z)}),f}e.keys=r;function u(_){var f=[];return i(_,function(C){f.push(C)}),f}e.values=u;function h(_,f,C){for(var z=0;z<_.length;z++)f.call(C||window,_[z],z,_)}e.apply=h;function l(_,f){for(var C=[],z=0;z<_.length;z++)C.push(f(_[z],z,_,C));return C}e.map=l;function $(_,f){var C={};return i(_,function(z,P){C[P]=f(z)}),C}e.mapObject=$;function O(_,f){f=f||function(P){return!!P};for(var C=[],z=0;z<_.length;z++)f(_[z],z,_,C)&&C.push(_[z]);return C}e.filter=O;function w(_,f){var C={};return i(_,function(z,P){(f&&f(z,P,_,C)||z)&&(C[P]=z)}),C}e.filterObject=w;function g(_){var f=[];return i(_,function(C,z){f.push([z,C])}),f}e.flatten=g;function d(_,f){for(var C=0;C<_.length;C++)if(f(_[C],C,_))return!0;return!1}e.any=d;function k(_,f){for(var C=0;C<_.length;C++)if(!f(_[C],C,_))return!1;return!0}e.all=k;function E(_){return $(_,function(f){return typeof f=="object"&&(f=Z(f)),encodeURIComponent(base64_1.default(f.toString()))})}e.encodeParamsObject=E;function v(_){var f=w(_,function(C){return C!==void 0});return l(g(E(f)),util_1.default.method("join","=")).join("&")}e.buildQueryString=v;function R(_){var f=[],C=[];return function z(P,N){var U,p,c;switch(typeof P){case"object":if(!P)return null;for(U=0;U<f.length;U+=1)if(f[U]===P)return{$ref:C[U]};if(f.push(P),C.push(N),Object.prototype.toString.apply(P)==="[object Array]")for(c=[],U=0;U<P.length;U+=1)c[U]=z(P[U],N+"["+U+"]");else{c={};for(p in P)Object.prototype.hasOwnProperty.call(P,p)&&(c[p]=z(P[p],N+"["+JSON.stringify(p)+"]"))}return c;case"number":case"string":case"boolean":return P}}(_,"$")}e.decycleObject=R;function Z(_){try{return JSON.stringify(_)}catch{return JSON.stringify(R(_))}}return e.safeJSONStringify=Z,e}(),Ne=function(){function e(s){this.callbacks=new pt,this.global_callbacks=[],this.failThrough=s}return e.prototype.on=function(s,o,n){return this.callbacks.add(s,o,n),this},e.prototype.on_global=function(s){return this.global_callbacks.push(s),this},e.prototype.off=function(s,o,n){return this.callbacks.remove(s,o,n),this},e.prototype.emit=function(s,o){var n;for(n=0;n<this.global_callbacks.length;n++)this.global_callbacks[n](s,o);var i=this.callbacks.get(s);if(i&&i.length>0)for(n=0;n<i.length;n++)i[n].fn.call(i[n].context||window,o);else this.failThrough&&this.failThrough(s,o);return this},e}(),pt=function(){function e(){this._callbacks={}}return e.prototype.get=function(s){return this._callbacks[Se(s)]},e.prototype.add=function(s,o,n){var i=Se(s);this._callbacks[i]=this._callbacks[i]||[],this._callbacks[i].push({fn:o,context:n})},e.prototype.remove=function(s,o,n){if(!s&&!o&&!n){this._callbacks={};return}var i=s?[Se(s)]:ve.keys(this._callbacks);o||n?this.removeCallback(i,o,n):this.removeAllCallbacks(i)},e.prototype.removeCallback=function(s,o,n){ve.apply(s,function(i){this._callbacks[i]=ve.filter(this._callbacks[i]||[],function(r){return o&&o!==r.fn||n&&n!==r.context}),this._callbacks[i].length===0&&delete this._callbacks[i]},this)},e.prototype.removeAllCallbacks=function(s){ve.apply(s,function(o){delete this._callbacks[o]},this)},e}();function Se(e){return"_"+e}function ft(e){e=e||{},e.type=(e.type||"GET").toUpperCase(),e.dataType=e.dataType||"json";var s=ht(e.data),o;window.XMLHttpRequest?o=new XMLHttpRequest:o=ActiveXObject("Microsoft.XMLHTTP"),o.onreadystatechange=function(){if(o.readyState===4){var n=o.status;n>=200&&n<300?e.success&&e.success(o.responseText,o.responseXML):e.error&&e.error(n)}},e.type==="GET"?(o.open("GET",e.url+"?"+s,!0),o.send(null)):e.type==="POST"&&(o.open("POST",e.url,!0),o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.send(s))}function ht(e){var s=[];for(var o in e)s.push(encodeURIComponent(o)+"="+encodeURIComponent(e[o]));return s.join("&")}const _t={VITE_APP_PROXY_PREFIX:"",VITE_APP_WS_APPKEY:"2f97f2e18b6b6e6304ce77fdb779c650",VITE_APP_WS_URL:"ws://127.0.0.1:3131"},yt={class:"mr-2 flex justify-end lg:justify-between w-full lg:w-auto"},gt=["src"],vt={__name:"ma-operation",setup(e){const{t:s}=Oe.useI18n(),o=We(),n=oe(),i=Q();M(null);const r=te(),u=M(!1),h=M(!1),l=M(!1),$=async v=>{if(v==="userCenter"&&r.push({name:"userCenter"}),v==="clearCache"){const R=await qe.clearAllCache();_e.local.remove("dictData"),R.code===200&&je.success(R.message)}v==="logout"&&(h.value=!0,document.querySelector("#app").style.filter="grayscale(1)")},O=async()=>{window.open("https://saas.saithink.top/#/appStore")},w=async()=>{await n.logout(),document.querySelector("#app").style.filter="grayscale(0)",r.push({name:"login"})},g=()=>{document.querySelector("#app").style.filter="grayscale(0)"},d=()=>{_e.screen(document.documentElement),u.value=!u.value};if(i.ws){const v=_t,R=v.VITE_APP_PROXY_PREFIX,Z=v.VITE_APP_WS_URL,_=v.VITE_APP_WS_APPKEY;var k=new X({url:Z,app_key:_,auth:R+"/plugin/webman/push/auth"}),E=k.subscribe("saiadmin");E.on("message",function(f){Qe("新消息提示","您有新的消息，请注意查收！"),o.messageList=f.data})}return(v,R)=>{const Z=A("icon-apps"),_=A("a-button"),f=A("a-tooltip"),C=A("icon-search"),z=A("icon-fullscreen-exit"),P=A("icon-fullscreen"),N=A("icon-notification"),U=A("a-badge"),p=A("a-trigger"),c=A("icon-settings"),T=A("a-space"),I=A("a-avatar"),D=A("icon-user"),j=A("a-doption"),q=A("icon-delete"),ae=A("a-divider"),F=A("icon-poweroff"),re=A("a-dropdown"),fe=A("a-modal");return m(),V("div",yt,[t(T,{class:"mr-0 lg:mr-5",size:"medium"},{default:a(()=>[l.value?(m(),S(f,{key:0,content:v.$t("sys.store")},{default:a(()=>[t(_,{shape:"circle",class:"hidden lg:inline",onClick:O},{icon:a(()=>[t(Z,{size:16,rotate:45})]),_:1})]),_:1},8,["content"])):L("",!0),t(f,{content:v.$t("sys.search")},{default:a(()=>[t(_,{shape:"circle",onClick:R[0]||(R[0]=()=>b(i).searchOpen=!0),class:"hidden lg:inline"},{icon:a(()=>[t(C)]),_:1})]),_:1},8,["content"]),t(f,{content:u.value?v.$t("sys.closeFullScreen"):v.$t("sys.fullScreen")},{default:a(()=>[t(_,{shape:"circle",class:"hidden lg:inline",onClick:d},{icon:a(()=>[u.value?(m(),S(z,{key:0})):(m(),S(P,{key:1}))]),_:1})]),_:1},8,["content"]),t(p,{trigger:"click"},{content:a(()=>[t(ut)]),default:a(()=>[t(_,{shape:"circle"},{icon:a(()=>[b(o).messageList.length>0?(m(),S(U,{key:0,count:5,dot:"",dotStyle:{width:"5px",height:"5px"}},{default:a(()=>[t(N)]),_:1})):(m(),S(N,{key:1}))]),_:1})]),_:1}),t(f,{content:v.$t("sys.pageSetting")},{default:a(()=>[t(_,{shape:"circle",onClick:R[1]||(R[1]=()=>b(i).settingOpen=!0),class:"hidden lg:inline"},{icon:a(()=>[t(c)]),_:1})]),_:1},8,["content"])]),_:1}),t(re,{onSelect:$,trigger:"hover"},{content:a(()=>[t(j,{value:"userCenter"},{default:a(()=>[t(D),B(" "+x(v.$t("sys.userCenter")),1)]),_:1}),t(j,{value:"clearCache"},{default:a(()=>[t(q),B(" "+x(v.$t("sys.clearCache")),1)]),_:1}),t(ae,{style:{margin:"5px 0"}}),t(j,{value:"logout"},{default:a(()=>[t(F),B(" "+x(v.$t("sys.logout")),1)]),_:1})]),default:a(()=>[t(I,{class:"bg-blue-500 text-3xl avatar",style:{top:"-1px"}},{default:a(()=>[y("img",{src:b(n).user&&b(n).user.avatar?b(n).user.avatar:b(Pe)},null,8,gt)]),_:1})]),_:1}),t(fe,{visible:h.value,"onUpdate:visible":R[2]||(R[2]=he=>h.value=he),onOk:w,onCancel:g},{title:a(()=>[B(x(v.$t("sys.logoutAlert")),1)]),default:a(()=>[y("div",null,x(v.$t("sys.logoutMessage")),1)]),_:1},8,["visible"])])}}},be=le(vt,[["__scopeId","data-v-3227f34e"]]),bt={key:0,class:"flex justify-between tags-container",ref:"containerDom"},kt=["onContextmenu","onClick"],wt={key:0,class:"ma-tag-prev"},xt={key:1,class:"ma-tag-next"},Ct={class:"ma-tags-more-contextmenu"},St={__name:"ma-tags",setup(e){const s=se(),o=te(),n=Q(),i=ye(),r=M(null),u=M(!1),h=M(!1),l=M(null),$=M(0),O=M(0),w=["login"];Y(()=>n.tag,c=>{ue(()=>{(r.value.scrollWidth??!1)&&r.value.offsetWidth&&(u.value=r.value.scrollWidth>r.value.offsetWidth)})},{deep:!0}),Y(()=>i.tags,c=>{ue(()=>{(r.value.scrollWidth??!1)&&r.value.offsetWidth&&(u.value=r.value.scrollWidth>r.value.offsetWidth)})},{deep:!0}),Y(()=>s,c=>{w.includes(c.name)||Je({name:c.name,path:c.fullPath,affix:c.meta.affix,title:c.meta.title}),ue(()=>{r.value&&r.value.scrollWidth>r.value.clientWidth&&r.value.querySelector(".active").scrollIntoView()})},{deep:!0}),Y(h,c=>{const T=I=>{const D=document.querySelector(".tags-contextmenu");D&&!D.contains(I.target)&&k()};c?document.body.addEventListener("click",I=>T(I)):document.body.removeEventListener("click",I=>T(I))});const g=c=>{o.push({path:c.path,query:_e.getRequestParams(c.path)})},d=(c,T)=>{l.value=T,h.value=!0,$.value=c.clientX+1,O.value=c.clientY+1,ue(()=>{const I=document.querySelector(".tags-contextmenu");document.body.offsetWidth-c.clientX<I.offsetWidth&&($.value=document.body.offsetWidth-I.offsetWidth+1,O.value=c.clientY+1)})},k=()=>{l.value=null,h.value=!1},E=()=>{const c=l.value;h.value=!1,s.fullPath!=c.fullPath&&o.push({path:c.path,query:_e.getRequestParams(c.path)}),document.getElementById("app").classList.add("max-size")},v=()=>{const c=l.value;h.value=!1,s.fullPath!=c.fullPath&&o.push({path:c.path,query:_e.getRequestParams(c.path)}),ze()},R=()=>{ze()},Z=()=>{[...i.tags].forEach(T=>{(T.affix||s.path==T.path)&&Ae(T)})},_=()=>{l.value.affix||(Ae(l.value),h.value=!1)},f=()=>{const c=l.value;s.path!=c.path&&o.push({path:c.path});const T=[...i.tags];let I=null;T.forEach((D,j)=>{c.path==D.path&&(I=j)}),T.forEach((D,j)=>{if(D.affix||c.path==D.path)return!0;j>I&&Ae(D)}),h.value=!1},C=()=>{const c=l.value;s.path!=c.path&&o.push({path:c.path});const T=[...i.tags];let I=null;T.forEach((D,j)=>{c.path==D.path&&(I=j)}),T.forEach((D,j)=>{if(D.affix||c.path==D.path)return!0;j<I&&Ae(D)}),h.value=!1},z=()=>{[...i.tags].forEach(T=>{if(T.affix||s.path==T.path)return!0;Ae(T)}),h.value=!1},P=()=>{const c=l.value;s.path!=c.path&&o.push({path:c.path}),[...i.tags].forEach(I=>{if(I.affix||c.path==I.path)return!0;Ae(I)}),h.value=!1},N=c=>{const T=c.wheelDelta||c.detail,I=1,D=-1;let j=0;T==3||T<0&&T!=-3?j=I*50:j=D*50,r.value.scrollLeft+=j},U=c=>{const T=r.value.scrollLeft,I=T+c,D=c/50;p(T,I,D)},p=(c,T,I)=>{I>0?c<T?(c+=I,r.value.scrollLeft=c,window.requestAnimationFrame(()=>{p(c,T,I)})):r.value.scrollLeft=T:c>T?(c+=I,r.value.scrollLeft=c,window.requestAnimationFrame(()=>{p(c,T,I)})):r.value.scrollLeft=T};return G(()=>{r.value&&(Fe.create(r.value,{draggable:"a",animation:300}),r.value.addEventListener("mousewheel",N,{passive:!0})||r.value.addEventListener("DOMMouseScroll",N,{passive:!0}),ue(()=>{u.value=r.value.scrollWidth>r.value.offsetWidth}))}),(c,T)=>{const I=A("icon-close"),D=A("IconLeft"),j=A("IconRight"),q=A("icon-refresh"),ae=A("a-divider"),F=A("icon-close-circle"),re=A("icon-close-circle-fill"),fe=A("a-trigger"),he=A("icon-fullscreen"),xe=A("icon-arrow-right"),W=A("icon-arrow-left");return b(n).tag?(m(),V("div",bt,[y("div",{class:K(["menu-tags-wrapper",{"tag-pn":u.value}]),ref:"scrollbarDom"},[y("div",{class:"tags",ref_key:"tags",ref:r},[(m(!0),V(H,null,ee(b(i).tags,J=>(m(),V("div",{key:J.path,onContextmenu:Be(Ce=>d(Ce,J),["prevent"]),class:K(b(s).fullPath==J.path?"active":""),onClick:Ce=>g(J)},[y("span",null,x(J.customTitle?J.customTitle:b(n).i18n?c.$t("menus."+J.name).indexOf(".")>0?J.title:c.$t("menus."+J.name):J.title),1),J.affix?L("",!0):(m(),S(I,{key:0,class:"tag-icon",onClick:Be(Ce=>b(Ae)(J),["stop"])},null,8,["onClick"]))],42,kt))),128))],512),u.value?(m(),V("span",wt,[t(D,{size:20,class:"tag-scroll-icon",onClick:T[0]||(T[0]=J=>U(-500))})])):L("",!0),u.value?(m(),V("span",xt,[t(j,{size:20,class:"tag-scroll-icon",onClick:T[1]||(T[1]=J=>U(500))})])):L("",!0)],2),t(fe,{class:"ma-tags-more-dropdown","popup-translate":[-65,-6],"show-arrow":!0,trigger:"hover"},{content:a(()=>[y("ul",Ct,[y("li",{onClick:R},[t(q),B(" "+x(c.$t("sys.tags.refresh")),1)]),t(ae,{class:"dropdown-divider"}),y("li",{onClick:Z},[t(F),B(" "+x(c.$t("sys.tags.closeTag")),1)]),y("li",{onClick:z},[t(re),B(" "+x(c.$t("sys.tags.closeOtherTag")),1)])])]),default:a(()=>[T[2]||(T[2]=y("span",{class:"ma-tags-more"},[y("span",{class:"ma-tags-more-icon"},[y("i",{class:"ma-box ma-box-t"}),y("i",{class:"ma-box ma-box-b"})])],-1))]),_:1}),h.value?(m(),V("ul",{key:0,class:"tags-contextmenu",style:ne({left:$.value+"px",top:O.value+"px"})},[y("li",{onClick:v},[t(q),B(" "+x(c.$t("sys.tags.refresh")),1)]),y("li",{onClick:E},[t(he),B(" "+x(c.$t("sys.tags.fullscreen")),1)]),t(ae),y("li",{onClick:f},[t(xe),B(" "+x(c.$t("sys.tags.closeRightTag")),1)]),y("li",{onClick:C},[t(W),B(" "+x(c.$t("sys.tags.closeLeftTag")),1)]),y("li",{onClick:_,class:K(l.value.affix?"disabled":"")},[t(F),B(" "+x(c.$t("sys.tags.closeTag")),1)],2),y("li",{onClick:P},[t(re),B(" "+x(c.$t("sys.tags.closeOtherTag")),1)])],4)):L("",!0)],512)):L("",!0)}}},ke=le(St,[["__scopeId","data-v-43af460b"]]),$t={class:"flex justify-between",style:{height:"50px"}},Et={__name:"ma-columns-header",setup(e){return(s,o)=>{const n=A("a-avatar"),i=A("a-layout-header");return m(),S(i,{class:"layout-header flex flex-col operation-area"},{default:a(()=>[y("div",$t,[t(n,{class:"mt-1 ml-2 inline lg:hidden",style:{width:"45px"},size:40},{default:a(()=>o[0]||(o[0]=[y("img",{src:ie,class:"bg-white"},null,-1)])),_:1}),t(Le),t(be)]),t(ke,{class:"hidden lg:flex"})]),_:1})}}},Ze={__name:"children-menu",props:{modelValue:Array},emits:["go"],setup(e,{emit:s}){const o=te(),n=Q(),i=ye(),r=u=>{u.meta&&u.meta.type==="L"?window.open(u.path):(o.push(u.path),i.addTag({name:u.name,title:u.meta.title,path:u.path}))};return(u,h)=>{const l=A("sa-icon"),$=A("a-menu-item"),O=A("children-menu",!0),w=A("a-sub-menu"),g=A("a-layout-content");return m(),S(g,{class:"sys-menus"},{default:a(()=>[(m(!0),V(H,null,ee(e.modelValue,d=>(m(),V(H,{key:d.id},[d.meta.hidden?L("",!0):(m(),V(H,{key:0},[!d.children||d.children.length===0?(m(),S($,{key:d.name,onClick:k=>r(d)},de({default:a(()=>[B(" "+x(b(n).i18n?u.$t(`menus.${d.name}`).indexOf(".")>0?d.meta.title:u.$t(`menus.${d.name}`):d.meta.title),1)]),_:2},[d.meta.icon?{name:"icon",fn:a(()=>[t(l,{icon:d.meta.icon,size:18},null,8,["icon"])]),key:"0"}:void 0]),1032,["onClick"])):(m(),S(w,{key:d.name},de({title:a(()=>[B(x(b(n).i18n?u.$t(`menus.${d.name}`).indexOf(".")>0?d.meta.title:u.$t(`menus.${d.name}`):d.meta.title),1)]),default:a(()=>[d.children?(m(),S(O,{key:0,modelValue:d.children,"onUpdate:modelValue":k=>d.children=k},null,8,["modelValue","onUpdate:modelValue"])):L("",!0)]),_:2},[d.meta.icon?{name:"icon",fn:a(()=>[t(l,{icon:d.meta.icon,size:18},null,8,["icon"])]),key:"0"}:void 0]),1024))],64))],64))),128))]),_:1})}}},Ot={__name:"ma-menu",props:{height:{type:String,default:"100%"}},setup(e,{expose:s}){te();const o=se(),{t:n}=Oe.useI18n(),i=Q();oe();const r=M([]),u=M([]),h=M([]),l=M("");G(()=>{u.value=[o.name],O()}),Y(()=>o,d=>{u.value=[d.name],O()},{deep:!0});const $=d=>{d.children&&d.children.length>0&&(r.value=d.children,i.i18n?l.value=n("menus."+d.name).indexOf(".")>0?d.meta.title:n("menus."+d.name):l.value=d.meta.title)},O=()=>{o.matched[1]&&o.matched[1].meta&&!o.matched[1].meta.breadcrumb?(h.value=[],o.matched.map((d,k)=>{o.matched[0].name==="layout"&&h.value.push("home")})):(h.value=[],o.matched[1]&&o.matched[1].meta&&o.matched[1].meta.breadcrumb.map(d=>{h.value.push(d.name)}))},w=d=>{i.toggleMenu(d)},g=e;return s({loadChildMenu:$,title:l,actives:u,menus:r,openKeys:h,findTopMenuName:O}),(d,k)=>{const E=A("a-menu");return m(),S(E,{class:"ma-menu",style:ne({width:b(i).menuWidth+"px",height:g.height}),breakpoint:"md","open-keys":h.value,"onUpdate:openKeys":k[1]||(k[1]=v=>h.value=v),"selected-keys":u.value,"onUpdate:selectedKeys":k[2]||(k[2]=v=>u.value=v),accordion:!0,"collapsed-width":45,"show-collapse-button":"",collapsed:b(i).menuCollapse,onCollapse:w,"popup-max-height":360,"auto-scroll-into-view":""},{default:a(()=>[t(Ze,{modelValue:r.value,"onUpdate:modelValue":k[0]||(k[0]=v=>r.value=v)},null,8,["modelValue"])]),_:1},8,["style","open-keys","selected-keys","collapsed"])}}},Te=le(Ot,[["__scopeId","data-v-4392256b"]]),Tt={class:"sider customer-scrollbar flex flex-col items-center bg-gray-800 dark:border-blackgray-5"},Bt={class:"mt-1 parent-menu-container"},zt=["onClick"],Mt={class:"layout-menu shadow flex flex-col"},Vt={__name:"ma-columns-menu",setup(e){const s=se(),o=te(),n=M(null),i=Q(),r=oe(),u=M(!1),h=M(""),l=M("flex flex-col parent-menu items-center rounded mt-1 text-gray-200 hover:bg-gray-700 dark:hover:text-gray-50 dark:hover:bg-blackgray-1");G(()=>{$()}),Y(()=>s,w=>{$()},{deep:!0});const $=()=>{var g,d;let w;(d=(g=s.matched[1])==null?void 0:g.meta)!=null&&d.breadcrumb?w=s.matched[1].meta.breadcrumb[0].name:w="home",r.routers&&r.routers.length>0&&r.routers.map((k,E)=>{k.name==w&&O(k,E)})},O=(w,g)=>{var d;if(w.meta.type==="L"){window.open(w.path);return}w.children&&w.children.length>0?(n.value.loadChildMenu(w),u.value=!0):(u.value=!1,o.push(w.path)),h.value=(d=n.value)==null?void 0:d.title,document.querySelectorAll(".parent-menu").forEach((k,E)=>{g!==E?k.classList.remove("active"):k.classList.add("active")})};return(w,g)=>{const d=A("a-avatar"),k=A("sa-icon"),E=A("a-layout-sider");return m(),V(H,null,[y("div",Tt,[t(d,{class:"mt-2",size:40},{default:a(()=>g[0]||(g[0]=[y("img",{src:ie,class:"bg-white"},null,-1)])),_:1}),y("ul",Bt,[(m(!0),V(H,null,ee(b(r).routers,(v,R)=>(m(),V("li",{key:R,class:K(`${l.value}`),onClick:Z=>O(v,R)},[v.meta.icon?(m(),S(k,{key:0,icon:v.meta.icon,class:"mt-1"},null,8,["icon"])):L("",!0),y("span",{class:"mt-0.5",style:ne(b(i).language==="en"?"font-size: 10px":"")},x(b(i).i18n?w.$t(`menus.${v.name}`).indexOf(".")>0?v.meta.title:w.$t(`menus.${v.name}`):v.meta.title),5)],10,zt))),128))])]),me(y("div",Mt,[me(y("div",{class:"menu-title flex items-center"},x(h.value),513),[[pe,!b(i).menuCollapse]]),t(E,{style:ne(`width: ${b(i).menuCollapse?"50px":b(i).menuWidth+"px"};
        height: ${b(i).menuCollapse?"100%":"calc(100% - 51px)"};`)},{default:a(()=>[t(Te,{ref_key:"MaMenuRef",ref:n,class:K(b(i).menuCollapse?"ml-0.5":"")},null,8,["class"])]),_:1},8,["style"])],512),[[pe,u.value]])],64)}}},It={class:"w-full h-full"},Pt=["src"],Rt={__name:"iframe-view",setup(e){const s=Ge(),o=se();Y(()=>o,i=>{n(i)},{deep:!0});const n=i=>{i.meta.type==="I"&&s.addIframe(i)};return n(o),(i,r)=>me((m(),V("div",It,[(m(!0),V(H,null,ee(b(s).iframes,u=>me((m(),V("iframe",{src:u.meta.url,key:u.name,frameborder:"0",class:"w-full h-full"},null,8,Pt)),[[pe,u.meta.url===i.$route.meta.url]])),128))],512)),[[pe,i.$route.meta.type==="I"]])}},we={__name:"ma-workerArea",setup(e){const s=Q(),o=Ye();return(n,i)=>{const r=A("router-view"),u=A("a-watermark"),h=A("a-layout-content");return m(),S(h,{class:"work-area customer-scrollbar relative"},{default:a(()=>[y("div",{class:K(["h-full",{"p-3":n.$route.path.indexOf("maIframe")===-1}])},[t(u,{content:b(s).waterMark?b(s).waterContent:""},{default:a(()=>[t(r,null,{default:a(({Component:l})=>[t(Me,{name:b(s).animation,mode:"out-in"},{default:a(()=>[(m(),S(Ue,{include:b(o).keepAlives},[b(o).show?(m(),S(Ve(l),{key:n.$route.fullPath})):L("",!0)],1032,["include"]))]),_:2},1032,["name"])]),_:1})]),_:1},8,["content"]),t(Rt)],2)]),_:1})}}},jt={id:"layout-columns-left-panel",class:"ma-ui-menu layout-columns-left-panel hidden lg:flex justify-between"},Wt={__name:"index",setup(e){const s=M(0);return G(()=>{const o=document.getElementById("layout-columns-left-panel");new Ie(i=>{for(const r of i)switch(r.target){case o:s.value=r.contentRect.width;break}}).observe(o)}),(o,n)=>{const i=A("a-layout-content");return m(),S(i,{class:"layout flex justify-between"},{default:a(()=>[y("div",jt,[t(Vt)]),y("div",{class:"layout-columns-right-panel flex flex-col",style:ne(`width: calc(100% - ${s.value}px)`)},[t(Et,{class:"ma-ui-header"}),t(we)],4)]),_:1})}}},Lt={class:"flex justify-center logo"},Dt={key:0,class:"ml-2 text-xl mt-2.5"},Ht={__name:"ma-classic-slider",setup(e){const s=M(null),o=oe(),n=Q();return G(()=>{setTimeout(i=>{s.value.menus=o.routers},50)}),(i,r)=>{const u=A("a-avatar"),h=A("a-layout-sider");return m(),S(h,{class:"layout-classic-sider h-full flex flex-col hidden lg:block",style:ne(`width: ${b(n).menuCollapse?"48px":b(n).menuWidth+"px"};`)},{default:a(()=>[y("div",Lt,[t(u,{class:"mt-1",size:40},{default:a(()=>r[0]||(r[0]=[y("img",{src:ie,class:"bg-white"},null,-1)])),_:1}),b(n).menuCollapse?L("",!0):(m(),V("span",Dt,x(i.$title),1))]),t(Te,{ref_key:"MaMenuRef",ref:s,height:"calc(100% - 51px)",class:K(`${b(n).menuCollapse?"ml-1.5":""};`)},null,8,["class"])]),_:1},8,["style"])}}},Nt={class:"flex justify-between layout-classic-header-container"},Zt={__name:"ma-classic-header",setup(e){return(s,o)=>{const n=A("a-avatar"),i=A("a-layout-header");return m(),S(i,{class:"layout-classic-header flex flex-col operation-area"},{default:a(()=>[y("div",Nt,[t(n,{class:"mt-1 ml-2 inline lg:hidden",style:{width:"45px"},size:40},{default:a(()=>o[0]||(o[0]=[y("img",{src:ie,class:"bg-white"},null,-1)])),_:1}),t(Le),t(be)]),t(ke,{class:"hidden lg:flex"})]),_:1})}}},Ut={__name:"index",setup(e){return(s,o)=>{const n=A("a-layout-content"),i=A("a-layout");return m(),S(i,{class:"layout flex justify-between h-full"},{default:a(()=>[t(Ht,{class:"ma-ui-slider"}),t(n,{class:"flex flex-col"},{default:a(()=>[t(Zt,{class:"ma-ui-header"}),t(we)]),_:1})]),_:1})}}},Qt={__name:"sub-menu",props:{menuInfo:Object},emits:["go"],setup(e,{emit:s}){const o=te(),n=ye(),i=Q(),r=u=>{u.meta&&u.meta.type==="L"?window.open(u.path):(o.push(u.path),n.addTag({name:u.name,title:u.meta.title,path:u.path}))};return(u,h)=>{const l=A("sa-icon"),$=A("a-menu-item"),O=A("SubMenu",!0),w=A("a-sub-menu");return m(),S(w,{key:e.menuInfo.name},de({title:a(()=>[B(x(b(i).i18n?u.$t(`menus.${e.menuInfo.name}`).indexOf(".")>0?e.menuInfo.meta.title:u.$t(`menus.${e.menuInfo.name}`):e.menuInfo.meta.title),1)]),default:a(()=>[(m(!0),V(H,null,ee(e.menuInfo.children,g=>(m(),V(H,{key:g.id},[!g.children||g.children.length===0?(m(),S($,{key:g.name,onClick:d=>r(g)},de({default:a(()=>[B(" "+x(b(i).i18n?u.$t(`menus.${g.name}`).indexOf(".")>0?g.meta.title:u.$t(`menus.${g.name}`):g.meta.title),1)]),_:2},[g.meta.icon?{name:"icon",fn:a(()=>[t(l,{icon:g.meta.icon,size:18},null,8,["icon"])]),key:"0"}:void 0]),1032,["onClick"])):(m(),S(O,{key:1,"menu-info":g},null,8,["menu-info"]))],64))),128))]),_:2},[e.menuInfo.meta.icon?{name:"icon",fn:a(()=>[t(l,{icon:e.menuInfo.meta.icon,size:18},null,8,["icon"])]),key:"0"}:void 0]),1024)}}},qt=le(Qt,[["__scopeId","data-v-69e93a02"]]),Jt={__name:"children-banner",props:{modelValue:Array},emits:["go"],setup(e,{emit:s}){const o=te(),n=Q(),i=ye(),r=se(),u=M([]);G(()=>{u.value=[r.name]}),Y(()=>r,l=>{u.value=[l.name]},{deep:!0});const h=l=>{l.meta&&l.meta.type==="L"?window.open(l.path):(o.push(l.path),i.addTag({name:l.name,title:l.meta.title,path:l.path}))};return(l,$)=>{const O=A("sa-icon"),w=A("a-menu-item"),g=A("a-menu"),d=A("a-layout-content");return m(),S(d,{class:"sys-menus"},{default:a(()=>[t(g,{ref:"MaMenuRef",mode:"horizontal",class:"layout-banner-menu hidden lg:flex","popup-max-height":360,"selected-keys":u.value},{default:a(()=>[(m(!0),V(H,null,ee(e.modelValue,k=>(m(),V(H,{key:k.id},[k.meta.hidden?L("",!0):(m(),V(H,{key:0},[!k.children||k.children.length===0?(m(),S(w,{key:k.name,onClick:E=>h(k)},de({default:a(()=>[B(" "+x(b(n).i18n?l.$t(`menus.${k.name}`).indexOf(".")>0?k.meta.title:l.$t(`menus.${k.name}`):k.meta.title),1)]),_:2},[k.meta.icon?{name:"icon",fn:a(()=>[t(O,{icon:k.meta.icon,size:18},null,8,["icon"])]),key:"0"}:void 0]),1032,["onClick"])):(m(),S(qt,{key:1,"menu-info":k},null,8,["menu-info"]))],64))],64))),128))]),_:1},8,["selected-keys"])]),_:1})}}},Yt={class:"flex justify-between md:justify-center logo"},Ft={class:"ml-2 text-xl mt-2.5 hidden md:block"},Xt={class:"flex justify-between w-full layout-banner"},Kt={__name:"index",setup(e){const s=se();M(null);const o=oe();Q();const n=M([]);return G(()=>{n.value=[s.name]}),Y(()=>s,i=>{n.value=[i.name]},{deep:!0}),(i,r)=>{const u=A("a-avatar"),h=A("a-layout-header"),l=A("a-layout");return m(),S(l,{class:"layout flex flex-col h-full"},{default:a(()=>[t(h,{class:"ma-ui-header flex justify-between h-50 layout-banner-header operation-area"},{default:a(()=>[y("div",Yt,[t(u,{class:"mt-1 ml-2 md:ml-0",size:40},{default:a(()=>r[1]||(r[1]=[y("img",{src:ie,class:"bg-white"},null,-1)])),_:1}),y("span",Ft,x(i.$title),1)]),y("div",Xt,[t(Jt,{modelValue:b(o).routers,"onUpdate:modelValue":r[0]||(r[0]=$=>b(o).routers=$)},null,8,["modelValue"]),t(be)])]),_:1}),t(ke,{class:"hidden lg:flex ma-ui-tags"}),t(we)]),_:1})}}},Gt=le(Kt,[["__scopeId","data-v-2cd429a7"]]),en={__name:"top-menu",props:{modelValue:Array,active:String},emits:["go"],setup(e,{expose:s,emit:o}){const n=e;te();const i=o,r=Q();ye(),se();const u=M([n.active??""]);Y(()=>n.active,$=>u.value=[$]);const h=$=>{u.value=[$.name],i("go",$)};return s({updateActive:$=>u.value=[$]}),($,O)=>{const w=A("sa-icon"),g=A("a-menu-item"),d=A("a-menu"),k=A("a-layout-content");return m(),S(k,{class:"sys-menus"},{default:a(()=>[t(d,{ref:"MaMenuRef",mode:"horizontal",class:"layout-banner-menu hidden lg:flex","popup-max-height":360,"selected-keys":u.value},{default:a(()=>[(m(!0),V(H,null,ee(n.modelValue,E=>(m(),V(H,{key:E.id},[E.meta.hidden?L("",!0):(m(),S(g,{key:E.name,onClick:v=>h(E)},de({default:a(()=>[B(" "+x(b(r).i18n?$.$t(`menus.${E.name}`).indexOf(".")>0?E.meta.title:$.$t(`menus.${E.name}`):E.meta.title),1)]),_:2},[E.meta.icon?{name:"icon",fn:a(()=>[t(w,{icon:E.meta.icon,size:18},null,8,["icon"])]),key:"0"}:void 0]),1032,["onClick"]))],64))),128))]),_:1},8,["selected-keys"])]),_:1})}}},tn={class:"flex justify-between md:justify-center logo"},nn={class:"ml-2 text-xl mt-2.5 hidden md:block"},on={class:"flex justify-between w-full layout-banner"},sn={__name:"index",setup(e){const s=se(),o=te(),n=M(null),i=M(null),r=oe(),u=Q(),h=M(!1),l=M();G(()=>{$()}),Y(()=>s,g=>{$()},{deep:!0});const $=()=>{var g,d;(d=(g=s.matched[1])==null?void 0:g.meta)!=null&&d.breadcrumb?l.value=s.matched[1].meta.breadcrumb[0].name:l.value="home",r.routers&&r.routers.length>0&&r.routers.map((k,E)=>{k.name==l.value&&O(k)})},O=g=>{if(g.meta.type==="L"){window.open(g.path);return}g.children&&g.children.length>0?(i.value.loadChildMenu(g),h.value=!0):(h.value=!1,o.push(g.path)),n.value.updateActive(g.name)},w=M(0);return G(()=>{const g=document.getElementById("layout-mixed-left-panel");new Ie(k=>{for(const E of k)switch(E.target){case g:w.value=E.contentRect.width;break}}).observe(g)}),(g,d)=>{const k=A("a-avatar"),E=A("a-layout-header"),v=A("a-layout-sider"),R=A("a-layout");return m(),S(R,{class:"layout flex flex-col h-full"},{default:a(()=>[t(E,{class:"ma-ui-header flex justify-between h-50 layout-banner-header operation-area"},{default:a(()=>[y("div",tn,[t(k,{class:"mt-1 ml-2 md:ml-0",size:40},{default:a(()=>d[1]||(d[1]=[y("img",{src:ie,class:"bg-white"},null,-1)])),_:1}),y("span",nn,x(g.$title),1)]),y("div",on,[t(en,{modelValue:b(r).routers,"onUpdate:modelValue":d[0]||(d[0]=Z=>b(r).routers=Z),active:l.value,onGo:O,ref_key:"topMenuRef",ref:n},null,8,["modelValue","active"]),t(be)])]),_:1}),y("div",{class:"flex",style:ne(`height:calc(100% - ${b(u).tag?"87px":"52px"}); `)},[me(t(v,{id:"layout-mixed-left-panel",class:"layout-classic-sider h-full flex flex-col hidden lg:block",style:ne(`width: ${b(u).menuCollapse?"48px":b(u).menuWidth+"px"};`)},{default:a(()=>[t(Te,{ref_key:"MaMenuRef",ref:i,height:"100%",class:K(`${b(u).menuCollapse?"ml-1.5":""};`)},null,8,["class"])]),_:1},8,["style"]),[[pe,h.value]]),y("div",{class:"w-full",style:ne(`width: calc(100% - ${w.value}px)`)},[t(ke,{class:"hidden lg:flex ma-ui-tags"}),t(we)],4)],4)]),_:1})}}},an=le(sn,[["__scopeId","data-v-7a956764"]]),ln="data:image/jpeg;base64,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",An="data:image/jpeg;base64,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",cn="data:image/jpeg;base64,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",rn="/assets/thumb-DX6EaRRA.jpg",Ee=[{name:"mine",thumb:ln},{name:"classics",thumb:An},{name:"businessGray",thumb:cn},{name:"city",thumb:rn}],un={class:"flex flex-col"},dn={class:"leading-6"},mn={__name:"skin",setup(e,{expose:s}){const o=M(!1),n=Q(),i=()=>o.value=!0,r=()=>o.value=!1,u=l=>n.useSkin(l),h=$e(Ee);return s({open:i}),(l,$)=>{const O=A("a-col"),w=A("a-image"),g=A("a-button"),d=A("a-row"),k=A("a-card"),E=A("a-modal");return m(),S(E,{visible:o.value,"onUpdate:visible":$[0]||($[0]=v=>o.value=v),width:"600px",onCancel:r,footer:!1},{title:a(()=>[B(x(l.$t("sys.changeSkin")),1)]),default:a(()=>[y("div",un,[(m(!0),V(H,null,ee(h,(v,R)=>(m(),S(k,{key:v.name,class:K(R===0?"":"mt-3"),"body-style":{width:"100%",display:"flex",justifyContent:"space-between",padding:"10px"}},{default:a(()=>[t(d,{class:"w-full flex items-center"},{default:a(()=>[t(O,{span:3,class:"flex flex-col text-center"},{default:a(()=>[y("div",dn,x(l.$t(`skin.${v.name}`)),1)]),_:2},1024),t(O,{span:6,class:"flex flex-col text-center"},{default:a(()=>[t(w,{src:v.thumb,class:"rounded border"},null,8,["src"])]),_:2},1024),t(O,{span:12,class:"flex items-center pl-3 pr-3"},{default:a(()=>[B(x(l.$t(`skin.${v.name}Desc`)),1)]),_:2},1024),t(O,{span:3,class:"flex items-center justify-end"},{default:a(()=>[t(g,{type:b(n).skin===v.name?"primary":"secondary",disabled:b(n).skin===v.name,onClick:Z=>u(v.name)},{default:a(()=>[B(x(b(n).skin===v.name?l.$t("skin.activated"):l.$t("skin.use")),1)]),_:2},1032,["type","disabled","onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["class"]))),128))])]),_:1},8,["visible"])}}},pn={class:"title"},fn={class:"title"},hn={__name:"setting",setup(e,{expose:s}){const o=oe(),n=Q(),{t:i}=Oe.useI18n(),r=M(null),u=M(!1),h=M(""),l=$e({mode:n.mode==="dark",tag:n.tag,menuCollapse:n.menuCollapse,menuWidth:n.menuWidth,layout:n.layout,language:n.language,animation:n.animation,i18n:n.i18n,waterMark:n.waterMark,waterContent:n.waterContent,ws:n.ws,roundOpen:n.roundOpen}),$=$e(["#165DFF","#7166f0","#e84a6c","#efbd48","#0bd092","#bb1b1b","#0d9496","#18181b","#0960be","#4e69fd","#f5319d","#c1420b","#43a047","#f53f3f","#344256","#3f3f46"]),O=p=>{n.changeColor(p.hex)};Ee.map(p=>{p.name===n.skin&&(h.value=i("skin."+p.name))}),Y(()=>n.skin,p=>{Ee.map(c=>{c.name===p&&(h.value=i("skin."+c.name))})});const w=()=>u.value=!0,g=()=>u.value=!1,d=p=>n.changeLayout(p),k=p=>n.toggleI18n(p),E=p=>n.toggleWs(p),v=p=>n.toggleRound(p),R=p=>n.changeLanguage(p),Z=p=>n.changeAnimation(p),_=p=>n.toggleMode(p?"dark":"light"),f=p=>n.toggleWater(p),C=p=>n.changeWaterContent(p),z=p=>n.toggleTag(p),P=p=>n.toggleMenu(p),N=p=>n.changeMenuWidth(p);Y(()=>n.menuCollapse,p=>l.menuCollapse=p);const U=async p=>{const c={mode:n.mode,tag:n.tag,menuCollapse:n.menuCollapse,menuWidth:n.menuWidth,layout:n.layout,skin:n.skin,i18n:n.i18n,language:n.language,animation:n.animation,color:n.color,waterMark:n.waterMark,waterContent:n.waterContent,ws:n.ws,roundOpen:n.roundOpen};Xe.updateInfo({id:o.user.id,backend_setting:c}).then(T=>{T.code===200&&je.success(T.message)}),p(!0)};return s({open:w}),(p,c)=>{const T=A("a-divider"),I=A("a-row"),D=A("a-button"),j=A("a-form-item"),q=A("a-option"),ae=A("a-select"),F=A("a-switch"),re=A("a-input"),fe=A("a-input-number"),he=A("a-form"),xe=A("a-drawer");return m(),V(H,null,[t(xe,{class:"backend-setting",visible:u.value,"onUpdate:visible":c[13]||(c[13]=W=>u.value=W),"on-before-ok":U,width:"450px","ok-text":p.$t("sys.saveToBackend"),onCancel:g,unmountOnClose:""},{title:a(()=>[B(x(p.$t("sys.backendSettingTitle")),1)]),default:a(()=>[t(he,{model:l,"auto-label-width":!0},{default:a(()=>[t(I,{class:"flex justify-center mb-5"},{default:a(()=>[t(T,{orientation:"center"},{default:a(()=>[y("span",pn,x(p.$t("sys.systemPrimaryColor")),1)]),_:1}),t(b(Ke),{theme:"dark",color:b(n).color,"sucker-hide":!0,"colors-default":$,onChangeColor:O,style:{width:"218px"}},null,8,["color","colors-default"])]),_:1}),t(T,{orientation:"center"},{default:a(()=>[y("span",fn,x(p.$t("sys.personalizedConfig")),1)]),_:1}),t(j,{label:p.$t("sys.skin"),help:p.$t("sys.skinHelp")},{default:a(()=>[B(x(h.value)+" ",1),t(D,{type:"primary",size:"mini",class:"ml-2",onClick:c[0]||(c[0]=W=>r.value.open())},{default:a(()=>[B(x(p.$t("sys.changeSkin")),1)]),_:1})]),_:1},8,["label","help"]),t(j,{label:p.$t("sys.layouts"),help:p.$t("sys.layoutsHelp")},{default:a(()=>[t(ae,{modelValue:l.layout,"onUpdate:modelValue":c[1]||(c[1]=W=>l.layout=W),onChange:d},{default:a(()=>[t(q,{value:"classic"},{default:a(()=>[B(x(p.$t("sys.layout.classic")),1)]),_:1}),t(q,{value:"columns"},{default:a(()=>[B(x(p.$t("sys.layout.columns")),1)]),_:1}),b(n).skin!=="classics"?(m(),S(q,{key:0,value:"banner"},{default:a(()=>[B(x(p.$t("sys.layout.banner")),1)]),_:1})):L("",!0),b(n).skin!=="classics"?(m(),S(q,{key:1,value:"mixed"},{default:a(()=>[B(x(p.$t("sys.layout.mixed")),1)]),_:1})):L("",!0)]),_:1},8,["modelValue"])]),_:1},8,["label","help"]),t(j,{label:p.$t("sys.round"),help:p.$t("sys.roundHelp")},{default:a(()=>[t(F,{modelValue:l.roundOpen,"onUpdate:modelValue":c[2]||(c[2]=W=>l.roundOpen=W),onChange:v},null,8,["modelValue"])]),_:1},8,["label","help"]),t(j,{label:p.$t("sys.ws"),help:p.$t("sys.wsHelp")},{default:a(()=>[t(F,{modelValue:l.ws,"onUpdate:modelValue":c[3]||(c[3]=W=>l.ws=W),onChange:E},null,8,["modelValue"])]),_:1},8,["label","help"]),t(j,{label:p.$t("sys.i18n"),help:p.$t("sys.i18nHelp")},{default:a(()=>[t(F,{modelValue:l.i18n,"onUpdate:modelValue":c[4]||(c[4]=W=>l.i18n=W),onChange:k},null,8,["modelValue"])]),_:1},8,["label","help"]),l.i18n?(m(),S(j,{key:0,label:p.$t("sys.language"),help:p.$t("sys.languageHelp")},{default:a(()=>[t(ae,{modelValue:l.language,"onUpdate:modelValue":c[5]||(c[5]=W=>l.language=W),onChange:R},{default:a(()=>[t(q,{value:"zh_CN"},{default:a(()=>[B(x(p.$t("sys.chinese")),1)]),_:1}),t(q,{value:"en"},{default:a(()=>[B(x(p.$t("sys.english")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label","help"])):L("",!0),t(j,{label:p.$t("sys.animation"),help:p.$t("sys.animationHelp")},{default:a(()=>[t(ae,{modelValue:l.animation,"onUpdate:modelValue":c[6]||(c[6]=W=>l.animation=W),onChange:Z},{default:a(()=>[t(q,{value:"ma-fade"},{default:a(()=>[B(x(p.$t("sys.animate.fade")),1)]),_:1}),t(q,{value:"ma-slide-left"},{default:a(()=>[B(x(p.$t("sys.animate.sliderLeft")),1)]),_:1}),t(q,{value:"ma-slide-right"},{default:a(()=>[B(x(p.$t("sys.animate.sliderRight")),1)]),_:1}),t(q,{value:"ma-slide-down"},{default:a(()=>[B(x(p.$t("sys.animate.sliderDown")),1)]),_:1}),t(q,{value:"ma-slide-up"},{default:a(()=>[B(x(p.$t("sys.animate.sliderUp")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label","help"]),h.value==="Mine"?(m(),S(j,{key:1,label:p.$t("sys.dark"),help:p.$t("sys.darkHelp")},{default:a(()=>[t(F,{modelValue:l.mode,"onUpdate:modelValue":c[7]||(c[7]=W=>l.mode=W),onChange:_},null,8,["modelValue"])]),_:1},8,["label","help"])):L("",!0),t(j,{label:p.$t("sys.water"),help:p.$t("sys.waterHelp")},{default:a(()=>[t(F,{modelValue:l.waterMark,"onUpdate:modelValue":c[8]||(c[8]=W=>l.waterMark=W),onChange:f},null,8,["modelValue"])]),_:1},8,["label","help"]),l.waterMark?(m(),S(j,{key:2,label:p.$t("sys.waterContent")},{default:a(()=>[t(re,{modelValue:l.waterContent,"onUpdate:modelValue":c[9]||(c[9]=W=>l.waterContent=W),onBlur:C},null,8,["modelValue"])]),_:1},8,["label"])):L("",!0),t(j,{label:p.$t("sys.tag"),help:p.$t("sys.tagHelp")},{default:a(()=>[t(F,{modelValue:l.tag,"onUpdate:modelValue":c[10]||(c[10]=W=>l.tag=W),onChange:z},null,8,["modelValue"])]),_:1},8,["label","help"]),l.layout!=="banner"?(m(),S(j,{key:3,label:p.$t("sys.menuFold"),help:p.$t("sys.menuFoldHelp")},{default:a(()=>[t(F,{modelValue:l.menuCollapse,"onUpdate:modelValue":c[11]||(c[11]=W=>l.menuCollapse=W),onChange:P},null,8,["modelValue"])]),_:1},8,["label","help"])):L("",!0),l.layout!=="banner"?(m(),S(j,{key:4,label:p.$t("sys.menuWidth"),help:p.$t("sys.menuWidthHelp")},{default:a(()=>[t(fe,{modelValue:l.menuWidth,"onUpdate:modelValue":c[12]||(c[12]=W=>l.menuWidth=W),mode:"button",onChange:N},null,8,["modelValue"])]),_:1},8,["label","help"])):L("",!0)]),_:1},8,["model"])]),_:1},8,["visible","ok-text"]),t(mn,{ref_key:"skin",ref:r},null,512)],64)}}},_n={class:"sys-search-container"},yn={class:"ssc-bg"},gn={class:"w-6/12 mx-auto center-box"},vn={class:"mt-10"},bn={class:"mt-5"},kn={class:"mt-10 results shadow-lg customer-scrollbar"},wn=["onClick"],xn={class:"icon-box flex justify-center items-center"},Cn={class:"ml-5 leading-6"},Sn={class:"title"},$n={class:"path"},En={__name:"search",setup(e){const s=Q(),o=te(),n=M(),i=M(o.getRoutes()),r=h=>{i.value=o.getRoutes().filter(l=>!!(l.path&&l.path.indexOf(h)>-1||l.name&&l.name.indexOf(h)>-1||l.meta&&l.meta.title&&l.meta.title.indexOf(h)>-1))},u=h=>{s.searchOpen=!1,o.push(h.path)};return G(()=>{n.value.focus(),document.addEventListener("keydown",h=>{const l=h.keyCode??h.which??h.charCode,$=document.querySelector(".active-search-li"),O=()=>{const d=document.querySelectorAll(".results li");let k={idx:0,path:"/"};return d.forEach((E,v)=>{if(E.className.split(" ").includes("active-search-li")){k.path=E.querySelector(".path").innerHTML,k.idx=v;return}}),k},w=d=>{document.querySelectorAll(".results li")[d].classList.add("active-search-li")},g=d=>{document.querySelectorAll(".results li")[d].classList.remove("active-search-li")};if(s.searchOpen){if(l===40)if($){const d=document.querySelectorAll(".results li");let k=O(),E=k.idx+1;E>=d.length&&(E=0),g(k.idx),w(E)}else{w(0);return}if(l===38)if($){const d=document.querySelectorAll(".results li");let k=O(),E=k.idx-1;E<0&&(E=d.length-1),g(k.idx),w(E)}else{w(document.querySelectorAll(".results li").length-1);return}if(l===13){const d=O();g(d.idx),d.path!=="/"&&u(d)}}ue(()=>{const d=document.querySelector(".results");d&&d.scrollTop!==!1&&(d.scrollTop=(O().idx+1)*80-document.querySelectorAll(".results li").length*10)})})}),(h,l)=>{const $=A("icon-search"),O=A("a-input"),w=A("a-tag"),g=A("a-space"),d=A("icon-caret-up"),k=A("icon-caret-down"),E=A("icon-menu");return m(),V("div",_n,[y("div",yn,[y("div",gn,[l[7]||(l[7]=y("div",{class:"mt-10"},[y("img",{src:ie,width:"100",class:"mx-auto"})],-1)),y("div",vn,[t(O,{size:"large",ref_key:"searchInputRef",ref:n,placeholder:"搜索页面，支持名称、标识以及URL的模糊查询",onInput:r},{prefix:a(()=>[t($)]),_:1},512)]),y("div",bn,[t(g,{size:"large",class:"flex justify-center"},{default:a(()=>[t(g,null,{default:a(()=>[t(w,null,{default:a(()=>l[0]||(l[0]=[B("ALT+S")])),_:1}),t(w,null,{default:a(()=>l[1]||(l[1]=[B("唤醒搜索面板")])),_:1})]),_:1}),t(g,null,{default:a(()=>[t(w,null,{default:a(()=>[t(d)]),_:1}),t(w,null,{default:a(()=>[t(k)]),_:1}),t(w,null,{default:a(()=>l[2]||(l[2]=[B("切换搜索结果")])),_:1})]),_:1}),t(g,null,{default:a(()=>[t(w,null,{default:a(()=>l[3]||(l[3]=[B("Enter")])),_:1}),t(w,null,{default:a(()=>l[4]||(l[4]=[B("进入页面")])),_:1})]),_:1}),t(g,null,{default:a(()=>[t(w,null,{default:a(()=>l[5]||(l[5]=[B("Esc")])),_:1}),t(w,null,{default:a(()=>l[6]||(l[6]=[B("关闭搜索面板")])),_:1})]),_:1})]),_:1})]),y("ul",kn,[(m(!0),V(H,null,ee(i.value,v=>{var R;return m(),V(H,null,[v&&v.path.indexOf(":")===-1&&v.components&&((R=v==null?void 0:v.meta)==null?void 0:R.type)==="M"?(m(),V("li",{key:0,class:"flex items-center",onClick:Z=>u(v)},[y("div",xn,[v.meta.icon?(m(),S(Ve(v.meta.icon),{key:0,class:K(v.meta.icon.indexOf("ma")>0?"icon":"")},null,8,["class"])):(m(),S(E,{key:1}))]),y("div",Cn,[y("div",Sn,x(v.meta.title),1),y("div",$n,x(v.path),1)])],8,wn)):L("",!0)],64)}),256))])])])])}}},On=le(En,[["__scopeId","data-v-da9df33f"]]),Tn={class:"block lg:hidden button-menu"},Bn={__name:"ma-buttonMenu",setup(e){const s=oe(),o=M(!1);return(n,i)=>{const r=A("icon-close"),u=A("icon-menu"),h=A("a-menu"),l=A("a-trigger");return m(),V("div",Tn,[t(l,{trigger:["click"],clickToClose:"",position:"top",popupVisible:o.value,"onUpdate:popupVisible":i[1]||(i[1]=$=>o.value=$)},{content:a(()=>[t(h,{mode:"popButton",showCollapseButton:"","popup-max-height":360},{default:a(()=>[t(Ze,{modelValue:b(s).routers,"onUpdate:modelValue":i[0]||(i[0]=$=>b(s).routers=$)},null,8,["modelValue"])]),_:1})]),default:a(()=>[y("div",{class:K(`button-trigger ${o.value?"button-trigger-active":""}`)},[o.value?(m(),S(r,{key:0})):(m(),S(u,{key:1}))],2)]),_:1},8,["popupVisible"])])}}},Lo={__name:"index",setup(e){const s=Q();oe();const o=M(),n=M();Y(()=>s.settingOpen,r=>{r===!0&&(o.value.open(),s.settingOpen=!1)});const i=()=>{document.getElementById("app").classList.remove("max-size")};return G(()=>{document.addEventListener("keydown",r=>{const u=r.keyCode??r.which??r.charCode;if((r.altKey??r.metaKey)&&u===83){s.searchOpen=!0;return}if(u===27){s.searchOpen=!1;return}})}),(r,u)=>{const h=A("icon-close"),l=A("a-layout-content");return m(),S(l,{class:"h-full main-container"},{default:a(()=>[b(s).layout==="columns"?(m(),S(Wt,{key:0})):L("",!0),b(s).layout==="classic"?(m(),S(Ut,{key:1})):L("",!0),b(s).layout==="banner"?(m(),S(Gt,{key:2})):L("",!0),b(s).layout==="mixed"?(m(),S(an,{key:3})):L("",!0),t(hn,{ref_key:"settingRef",ref:o},null,512),t(Me,{name:"ma-slide-down",mode:"out-in"},{default:a(()=>[me(t(On,{ref_key:"systemSearchRef",ref:n},null,512),[[pe,b(s).searchOpen]])]),_:1}),t(Bn),y("div",{class:"max-size-exit",onClick:i},[t(h)])]),_:1})}}};export{Lo as default};
