<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 高级数据库连接池
// +----------------------------------------------------------------------
// | Author: AI Assistant (深度优化版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\database;

use think\facade\Db;
use Workerman\Timer;

/**
 * 高级数据库连接池管理
 */
class ConnectionPool
{
    /**
     * 连接池配置
     */
    private static $config = [
        'min_connections' => 2,      // 最小连接数
        'max_connections' => 20,     // 最大连接数
        'idle_timeout' => 300,       // 空闲超时时间(秒)
        'max_lifetime' => 3600,      // 连接最大生存时间(秒)
        'health_check_interval' => 60, // 健康检查间隔(秒)
    ];
    
    /**
     * 连接池
     */
    private static $pool = [];
    
    /**
     * 连接状态
     */
    private static $connections = [];
    
    /**
     * 统计信息
     */
    private static $stats = [
        'total_created' => 0,
        'total_destroyed' => 0,
        'active_connections' => 0,
        'idle_connections' => 0,
        'failed_connections' => 0,
        'query_count' => 0,
        'avg_query_time' => 0,
    ];
    
    /**
     * 初始化连接池
     */
    public static function init(array $config = []): void
    {
        self::$config = array_merge(self::$config, $config);
        
        // 创建最小连接数
        for ($i = 0; $i < self::$config['min_connections']; $i++) {
            self::createConnection();
        }
        
        // 启动健康检查定时器
        Timer::add(self::$config['health_check_interval'], [self::class, 'healthCheck']);
        
        // 启动统计定时器
        Timer::add(30, [self::class, 'updateStats']);
        
        echo "✅ 数据库连接池初始化完成 (最小连接: " . self::$config['min_connections'] . ")\n";
    }
    
    /**
     * 获取连接
     */
    public static function getConnection(): ?object
    {
        $startTime = microtime(true);
        
        // 查找可用的空闲连接
        foreach (self::$connections as $id => $conn) {
            if ($conn['status'] === 'idle' && self::isConnectionAlive($conn['connection'])) {
                self::$connections[$id]['status'] = 'active';
                self::$connections[$id]['last_used'] = time();
                self::recordQueryTime($startTime);
                return $conn['connection'];
            }
        }
        
        // 如果没有空闲连接且未达到最大连接数，创建新连接
        if (count(self::$connections) < self::$config['max_connections']) {
            $connection = self::createConnection();
            if ($connection) {
                self::recordQueryTime($startTime);
                return $connection;
            }
        }
        
        // 等待连接释放（简单实现）
        $waitTime = 0;
        while ($waitTime < 5000) { // 最多等待5秒
            usleep(10000); // 等待10ms
            $waitTime += 10;
            
            foreach (self::$connections as $id => $conn) {
                if ($conn['status'] === 'idle' && self::isConnectionAlive($conn['connection'])) {
                    self::$connections[$id]['status'] = 'active';
                    self::$connections[$id]['last_used'] = time();
                    self::recordQueryTime($startTime);
                    return $conn['connection'];
                }
            }
        }
        
        self::$stats['failed_connections']++;
        return null;
    }
    
    /**
     * 释放连接
     */
    public static function releaseConnection($connection): void
    {
        foreach (self::$connections as $id => $conn) {
            if ($conn['connection'] === $connection) {
                self::$connections[$id]['status'] = 'idle';
                self::$connections[$id]['last_used'] = time();
                break;
            }
        }
    }
    
    /**
     * 创建新连接
     */
    private static function createConnection(): ?object
    {
        try {
            $connection = Db::connect();
            
            $id = uniqid('conn_');
            self::$connections[$id] = [
                'connection' => $connection,
                'status' => 'active',
                'created_at' => time(),
                'last_used' => time(),
                'query_count' => 0,
            ];
            
            self::$stats['total_created']++;
            return $connection;
            
        } catch (\Exception $e) {
            self::$stats['failed_connections']++;
            error_log("数据库连接创建失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 检查连接是否存活
     */
    private static function isConnectionAlive($connection): bool
    {
        try {
            // 执行简单查询检查连接
            $connection->query('SELECT 1');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 健康检查
     */
    public static function healthCheck(): void
    {
        $currentTime = time();
        $toRemove = [];
        
        foreach (self::$connections as $id => $conn) {
            // 检查空闲超时
            if ($conn['status'] === 'idle' && 
                ($currentTime - $conn['last_used']) > self::$config['idle_timeout']) {
                $toRemove[] = $id;
                continue;
            }
            
            // 检查连接生存时间
            if (($currentTime - $conn['created_at']) > self::$config['max_lifetime']) {
                $toRemove[] = $id;
                continue;
            }
            
            // 检查连接是否存活
            if (!self::isConnectionAlive($conn['connection'])) {
                $toRemove[] = $id;
                continue;
            }
        }
        
        // 移除无效连接
        foreach ($toRemove as $id) {
            self::destroyConnection($id);
        }
        
        // 确保最小连接数
        $activeCount = count(self::$connections);
        if ($activeCount < self::$config['min_connections']) {
            for ($i = $activeCount; $i < self::$config['min_connections']; $i++) {
                self::createConnection();
            }
        }
    }
    
    /**
     * 销毁连接
     */
    private static function destroyConnection(string $id): void
    {
        if (isset(self::$connections[$id])) {
            unset(self::$connections[$id]);
            self::$stats['total_destroyed']++;
        }
    }
    
    /**
     * 记录查询时间
     */
    private static function recordQueryTime(float $startTime): void
    {
        $queryTime = (microtime(true) - $startTime) * 1000;
        self::$stats['query_count']++;
        
        // 计算平均查询时间
        if (self::$stats['query_count'] > 0) {
            self::$stats['avg_query_time'] = 
                (self::$stats['avg_query_time'] * (self::$stats['query_count'] - 1) + $queryTime) / 
                self::$stats['query_count'];
        }
    }
    
    /**
     * 更新统计信息
     */
    public static function updateStats(): void
    {
        $active = 0;
        $idle = 0;
        
        foreach (self::$connections as $conn) {
            if ($conn['status'] === 'active') {
                $active++;
            } else {
                $idle++;
            }
        }
        
        self::$stats['active_connections'] = $active;
        self::$stats['idle_connections'] = $idle;
    }
    
    /**
     * 获取统计信息
     */
    public static function getStats(): array
    {
        self::updateStats();
        return self::$stats;
    }
    
    /**
     * 获取连接池状态
     */
    public static function getPoolStatus(): array
    {
        return [
            'total_connections' => count(self::$connections),
            'config' => self::$config,
            'connections' => array_map(function($conn) {
                return [
                    'status' => $conn['status'],
                    'created_at' => date('Y-m-d H:i:s', $conn['created_at']),
                    'last_used' => date('Y-m-d H:i:s', $conn['last_used']),
                    'query_count' => $conn['query_count'],
                ];
            }, self::$connections),
            'stats' => self::getStats(),
        ];
    }
    
    /**
     * 关闭连接池
     */
    public static function close(): void
    {
        foreach (self::$connections as $id => $conn) {
            self::destroyConnection($id);
        }
        
        self::$connections = [];
        echo "✅ 数据库连接池已关闭\n";
    }
}
