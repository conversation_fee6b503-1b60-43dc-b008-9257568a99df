import{c as _}from"./config-ZWMRzLkl.js";import I from"./edit-DjscfkpF.js";import{M as F}from"./@arco-design-uttiljWv.js";import{r as n,a as v,h as p,j as B,k as R,l as i,m as N,t as o,y as S}from"./@vue-9ZIPiVZG.js";import"./index-ybrmzYq5.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./index-D4ERJytk.js";const U={class:"h-full"},qe={__name:"manage-config",emits:["close"],setup(D,{expose:h,emit:y}){const w=y,s=n(!1),d=n(),a=n(),r=n({name:"",key:"",group_id:"",orderBy:"sort",orderType:"desc"}),g=()=>{s.value=!1},u=async()=>{var t;(t=d.value)==null||t.refresh(),w("close")},k=t=>{var e;r.value.group_id=t,s.value=!0,(e=d.value)==null||e.refresh()},x=v({api:_.getConfigList,rowSelection:{showCheckedAll:!0},singleLine:!0,add:{show:!0,auth:["/core/config/save"],func:async()=>{var t,e;(t=a.value)==null||t.open(),(e=a.value)==null||e.setFormData({group_id:r.value.group_id})}},edit:{show:!0,auth:["/core/config/update"],func:async t=>{var e,m;(e=a.value)==null||e.open("edit"),(m=a.value)==null||m.setFormData(t)}},delete:{show:!0,auth:["/core/config/destroy"],func:async t=>{(await _.destroy(t)).code===200&&(F.success("删除成功！"),u())}}}),b=v([{title:"配置标题",dataIndex:"name",width:220},{title:"配置标识",dataIndex:"key",width:180},{title:"配置值",dataIndex:"value",width:200},{title:"排序",dataIndex:"sort",width:200},{title:"输入组件",dataIndex:"input_type",width:180},{title:"配置说明",dataIndex:"remark",width:180}]);return h({open:k}),(t,e)=>{const m=p("a-input"),c=p("a-form-item"),f=p("a-col"),V=p("sa-table"),C=p("a-modal");return R(),B(C,{fullscreen:"",visible:s.value,"onUpdate:visible":e[2]||(e[2]=l=>s.value=l),footer:!1,onClose:g},{title:i(()=>e[3]||(e[3]=[S("管理配置")])),default:i(()=>[N("div",U,[o(V,{ref_key:"crudRef",ref:d,options:x,columns:b,searchForm:r.value},{tableSearch:i(()=>[o(f,{span:8},{default:i(()=>[o(c,{field:"name",label:"配置标题"},{default:i(()=>[o(m,{modelValue:r.value.name,"onUpdate:modelValue":e[0]||(e[0]=l=>r.value.name=l),placeholder:"请输入配置标题","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),o(f,{span:8},{default:i(()=>[o(c,{field:"key",label:"配置标识"},{default:i(()=>[o(m,{modelValue:r.value.key,"onUpdate:modelValue":e[1]||(e[1]=l=>r.value.key=l),placeholder:"请输入配置标识","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["options","columns","searchForm"]),o(I,{ref_key:"editRef",ref:a,onSuccess:u},null,512)])]),_:1},8,["visible"])}}};export{qe as default};
