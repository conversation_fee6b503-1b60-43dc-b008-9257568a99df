<?php
/**
 * 功能测试脚本
 */

// 设置环境
defined("YII_DEBUG") or define("YII_DEBUG", true);
defined("YII_ENV") or define("YII_ENV", "test");

require __DIR__ . "/vendor/autoload.php";
require __DIR__ . "/vendor/yiisoft/yii2/Yii.php";

// 加载配置
$config = require __DIR__ . "/config/web.php";

// 创建应用实例
$app = new yii\web\Application($config);

echo "✅ 应用实例创建成功\n";
echo "📋 Yii 版本: " . Yii::getVersion() . "\n";
echo "📋 PHP 版本: " . PHP_VERSION . "\n";
echo "📋 应用ID: " . $app->id . "\n";
echo "📋 应用名称: " . $app->name . "\n";

// 测试组件
try {
    $cache = Yii::$app->cache;
    echo "✅ 缓存组件加载成功\n";
} catch (Exception $e) {
    echo "❌ 缓存组件加载失败: " . $e->getMessage() . "\n";
}

try {
    $db = Yii::$app->db;
    echo "✅ 数据库组件加载成功\n";
    echo "📋 数据库驱动: " . $db->driverName . "\n";
} catch (Exception $e) {
    echo "❌ 数据库组件加载失败: " . $e->getMessage() . "\n";
}

// 测试路由
try {
    $urlManager = Yii::$app->urlManager;
    echo "✅ URL管理器加载成功\n";
    echo "📋 美化URL: " . ($urlManager->enablePrettyUrl ? "启用" : "禁用") . "\n";
} catch (Exception $e) {
    echo "❌ URL管理器加载失败: " . $e->getMessage() . "\n";
}

// 测试模块
$modules = $app->getModules();
echo "📋 已配置模块: " . implode(", ", array_keys($modules)) . "\n";

echo "\n🎉 功能测试完成！\n";