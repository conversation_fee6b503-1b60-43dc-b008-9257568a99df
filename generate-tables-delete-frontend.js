// 前端调用删除功能示例
export default {
  methods: {
    // 单个删除
    async deleteTable(id) {
      try {
        await this.$http.delete(`/admin/tool/generateTables/delete`, { data: { ids: id } });
        this.$message.success("删除成功");
        this.getList();
      } catch (error) {
        this.$message.error("删除失败: " + error.message);
      }
    },

    // 批量删除
    async batchDelete(ids) {
      try {
        await this.$http.delete(`/admin/tool/generateTables/batchDelete`, { data: { ids } });
        this.$message.success("批量删除成功");
        this.getList();
      } catch (error) {
        this.$message.error("批量删除失败: " + error.message);
      }
    },

    // 清空所有
    async clearAll() {
      this.$confirm("确定要清空所有代码生成表吗？此操作不可恢复！", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        try {
          await this.$http.delete(`/admin/tool/generateTables/clear`);
          this.$message.success("清空成功");
          this.getList();
        } catch (error) {
          this.$message.error("清空失败: " + error.message);
        }
      });
    },

    // 删除生成文件
    async deleteGeneratedFiles(id) {
      this.$confirm("确定要删除生成的文件吗？此操作不可恢复！", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        try {
          await this.$http.delete(`/admin/tool/generateTables/deleteGeneratedFiles`, { data: { id } });
          this.$message.success("删除生成文件成功");
        } catch (error) {
          this.$message.error("删除生成文件失败: " + error.message);
        }
      });
    }
  }
};