@echo off
echo ========================================
echo    SaiAdmin 服务测试脚本
echo ========================================
echo.

echo [1/4] 测试前端服务器连接...
curl -s -o nul -w "前端服务器 (%%{http_code}): %%{url_effective}\n" http://localhost:8889/ || echo "前端服务器连接失败"
echo.

echo [2/4] 测试后端服务器连接...
curl -s -o nul -w "后端服务器 (%%{http_code}): %%{url_effective}\n" http://localhost:8787/ || echo "后端服务器连接失败"
echo.

echo [3/4] 测试后端API接口...
curl -s -w "API状态码: %%{http_code}\n" http://localhost:8787/api/common/index || echo "API接口测试失败"
echo.

echo [4/4] 检查数据库连接状态...
echo 数据库配置信息：
echo - 主机: 127.0.0.1:3306
echo - 数据库: saiadmin  
echo - 用户: root
echo.

echo ========================================
echo 测试完成！
echo.
echo 如果看到连接错误，请检查：
echo 1. MySQL服务是否启动
echo 2. 数据库 'saiadmin' 是否存在
echo 3. 用户密码是否正确
echo ========================================
pause
