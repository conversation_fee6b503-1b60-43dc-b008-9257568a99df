import{t as r}from"./es-errors-CFxpeikN.js";import{o as i}from"./object-inspect-Pz2pmunN.js";import{s}from"./side-channel-list-asz5kCf8.js";import{s as o}from"./side-channel-map-DBz1yoQn.js";import{s as h}from"./side-channel-weakmap-CMrfu08b.js";var l=r,p=i,c=s,f=o,d=h,m=d||f||c,k=function(){var n,a={assert:function(e){if(!a.has(e))throw new l("Side channel does not contain "+p(e))},delete:function(e){return!!n&&n.delete(e)},get:function(e){return n&&n.get(e)},has:function(e){return!!n&&n.has(e)},set:function(e,t){n||(n=m()),n.set(e,t)}};return a};export{k as s};
