<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\controller\tool;

use plugin\saiadmin\basic\BaseController;
use plugin\saiadmin\app\logic\tool\GenerateTablesLogic;
use plugin\saiadmin\app\validate\tool\GenerateTablesValidate;
use support\Request;
use support\Response;

/**
 * 代码生成控制器
 */
class GenerateTablesController extends BaseController
{
    /**
     * 构造
     */
    public function __construct()
    {
        $this->logic = new GenerateTablesLogic();
        $this->validate = new GenerateTablesValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        $where = $request->more([
            ['table_name', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * 装载数据表
     * @param Request $request
     * @return Response
     */
    public function loadTable(Request $request)
    {
        $names = $request->input('names', []);
        $source = $request->input('source', '');
        $this->logic->loadTable($names, $source);
        return $this->success('操作成功');
    }

    /**
     * 同步数据表字段信息
     * @param Request $request
     * @return Response
     */
    public function sync(Request $request)
    {
        $id = $request->input('id', '');
        $this->logic->sync($id);
        return $this->success('操作成功');
    }

    /**
     * 代码预览
     */
    public function preview(Request $request)
    {
        $id = $request->input('id', '');
        $data = $this->logic->preview($id);
        return $this->success($data);
    }

    /**
     * 代码生成
     */
    public function generate(Request $request)
    {
        $ids = $request->input('ids', '');
        $data = $this->logic->generate($ids);
        return response()->download($data['download'], $data['filename']);
    }

    /**
     * 生成到模块
     */
    public function generateFile(Request $request): Response
    {
        $id = $request->input('id', '');
        $this->logic->generateFile($id);
        return $this->success('操作成功');
    }

    /**
     * 获取数据表字段信息
     * @param Request $request
     * @return Response
     */
    public function getTableColumns(Request $request)
    {
        $table_id = $request->input('table_id', '');
        $data = $this->logic->getTableColumns($table_id);
        return $this->success($data);
    }

    /**
     * 删除代码生成表
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request)
    {
        $ids = $request->input('ids', '');
        if (empty($ids)) {
            return $this->fail('参数错误');
        }

        try {
            $this->logic->destroy($ids);
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->fail('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量删除代码生成表
     * @param Request $request
     * @return Response
     */
    public function batchDelete(Request $request)
    {
        $ids = $request->input('ids', []);
        if (empty($ids)) {
            return $this->fail('请选择要删除的数据');
        }

        try {
            $this->logic->destroy($ids);
            return $this->success([], '批量删除成功');
        } catch (\Exception $e) {
            return $this->fail('批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 清空代码生成表
     * @param Request $request
     * @return Response
     */
    public function clear(Request $request)
    {
        try {
            // 获取所有表ID
            $allIds = $this->logic->model->column('id');
            if (!empty($allIds)) {
                $this->logic->destroy($allIds);
            }
            return $this->success([], '清空成功');
        } catch (\Exception $e) {
            return $this->fail('清空失败：' . $e->getMessage());
        }
    }

    /**
     * 删除生成的文件
     * @param Request $request
     * @return Response
     */
    public function deleteGeneratedFiles(Request $request)
    {
        $id = $request->input('id', '');
        if (empty($id)) {
            return $this->fail('参数错误');
        }

        try {
            $this->logic->deleteGeneratedFiles($id);
            return $this->success([], '删除生成文件成功');
        } catch (\Exception $e) {
            return $this->fail('删除生成文件失败：' . $e->getMessage());
        }
    }

}