import{g as gt}from"./@arco-design-uttiljWv.js";/*!
  * shared v9.14.3
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */const ht=typeof window<"u";let Tt,Lt;{const e=ht&&window.performance;e&&e.mark&&e.measure&&e.clearMarks&&e.clearMeasures&&(Tt=t=>{e.mark(t)},Lt=(t,n,r)=>{e.measure(t,n,r),e.clearMarks(n),e.clearMarks(r)})}const An=/\{([0-9a-zA-Z]+)\}/g;function Cn(e,...t){return t.length===1&&ue(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(An,(n,r)=>t.hasOwnProperty(r)?t[r]:"")}const Sn=(e,t=!1)=>t?Symbol.for(e):Symbol(e),yn=(e,t,n)=>Ot({l:e,k:t,s:n}),Ot=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),In=e=>typeof e=="number"&&isFinite(e),bn=e=>Ye(e)==="[object Date]",Pn=e=>Ye(e)==="[object RegExp]",kn=e=>Xe(e)&&Object.keys(e).length===0,Dn=Object.assign,Mn=Object.create,Ke=(e=null)=>Mn(e);let et;const Rn=()=>et||(et=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:Ke());function wn(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Fn=Object.prototype.hasOwnProperty;function Un(e,t){return Fn.call(e,t)}const je=Array.isArray,ve=e=>typeof e=="function",vn=e=>typeof e=="string",$n=e=>typeof e=="boolean",Wn=e=>typeof e=="symbol",ue=e=>e!==null&&typeof e=="object",Kn=e=>ue(e)&&ve(e.then)&&ve(e.catch),Ge=Object.prototype.toString,Ye=e=>Ge.call(e),Xe=e=>{if(!ue(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},jn=e=>e==null?"":je(e)||Xe(e)&&e.toString===Ge?JSON.stringify(e,null,2):String(e);function Gn(e,t=""){return e.reduce((n,r,o)=>o===0?n+r:n+t+r,"")}const tt=2;function Yn(e,t=0,n=e.length){const r=e.split(/\r?\n/);let o=0;const i=[];for(let l=0;l<r.length;l++)if(o+=r[l].length+1,o>=t){for(let u=l-tt;u<=l+tt||n>o;u++){if(u<0||u>=r.length)continue;const f=u+1;i.push(`${f}${" ".repeat(3-String(f).length)}|  ${r[u]}`);const g=r[u].length;if(u===l){const T=t-(o-g)+1,L=Math.max(1,n>o?g-T:n-t);i.push("   |  "+" ".repeat(T)+"^".repeat(L))}else if(u>l){if(n>o){const T=Math.max(Math.min(n-o,g),1);i.push("   |  "+"^".repeat(T))}o+=g+1}}break}return i.join(`
`)}function Xn(e){let t=e;return()=>++t}function At(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const nt={};function Hn(e){nt[e]||(nt[e]=!0,At(e))}function Bn(){const e=new Map;return{events:e,on(n,r){const o=e.get(n);o&&o.push(r)||e.set(n,[r])},off(n,r){const o=e.get(n);o&&o.splice(o.indexOf(r)>>>0,1)},emit(n,r){(e.get(n)||[]).slice().map(o=>o(r)),(e.get("*")||[]).slice().map(o=>o(n,r))}}}const Le=e=>!ue(e)||je(e);function Vn(e,t){if(Le(e)||Le(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:o}=n.pop();Object.keys(r).forEach(i=>{i!=="__proto__"&&(ue(r[i])&&!ue(o[i])&&(o[i]=Array.isArray(r[i])?[]:Ke()),Le(o[i])||Le(r[i])?o[i]=r[i]:n.push({src:r[i],des:o[i]}))})}}const xn=Object.freeze(Object.defineProperty({__proto__:null,assign:Dn,create:Ke,createEmitter:Bn,deepCopy:Vn,escapeHtml:wn,format:Cn,friendlyJSONstringify:Ot,generateCodeFrame:Yn,generateFormatCacheKey:yn,getGlobalThis:Rn,hasOwn:Un,inBrowser:ht,incrementer:Xn,isArray:je,isBoolean:$n,isDate:bn,isEmptyObject:kn,isFunction:ve,isNumber:In,isObject:ue,isPlainObject:Xe,isPromise:Kn,isRegExp:Pn,isString:vn,isSymbol:Wn,join:Gn,makeSymbol:Sn,get mark(){return Tt},get measure(){return Lt},objectToString:Ge,toDisplayString:jn,toTypeString:Ye,warn:At,warnOnce:Hn},Symbol.toStringTag,{value:"Module"})),js=gt(xn);/*!
  * core-base v9.14.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const fe=typeof window<"u";let H,me;{const e=fe&&window.performance;e&&e.mark&&e.measure&&e.clearMarks&&e.clearMeasures&&(H=t=>{e.mark(t)},me=(t,n,r)=>{e.measure(t,n,r),e.clearMarks(n),e.clearMarks(r)})}const Jn=/\{([0-9a-zA-Z]+)\}/g;function Se(e,...t){return t.length===1&&$(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(Jn,(n,r)=>t.hasOwnProperty(r)?t[r]:"")}const qn=(e,t,n)=>Qn({l:e,k:t,s:n}),Qn=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),G=e=>typeof e=="number"&&isFinite(e),Zn=e=>St(e)==="[object Date]",rt=e=>St(e)==="[object RegExp]",He=e=>w(e)&&Object.keys(e).length===0,ne=Object.assign,zn=Object.create,X=(e=null)=>zn(e);function st(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const er=Object.prototype.hasOwnProperty;function le(e,t){return er.call(e,t)}const q=Array.isArray,v=e=>typeof e=="function",y=e=>typeof e=="string",Y=e=>typeof e=="boolean",$=e=>e!==null&&typeof e=="object",tr=e=>$(e)&&v(e.then)&&v(e.catch),Ct=Object.prototype.toString,St=e=>Ct.call(e),w=e=>{if(!$(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},nr=e=>e==null?"":q(e)||w(e)&&e.toString===Ct?JSON.stringify(e,null,2):String(e);function Be(e,t=""){return e.reduce((n,r,o)=>o===0?n+r:n+t+r,"")}const at=2;function rr(e,t=0,n=e.length){const r=e.split(/\r?\n/);let o=0;const i=[];for(let l=0;l<r.length;l++)if(o+=r[l].length+1,o>=t){for(let u=l-at;u<=l+at||n>o;u++){if(u<0||u>=r.length)continue;const f=u+1;i.push(`${f}${" ".repeat(3-String(f).length)}|  ${r[u]}`);const g=r[u].length;if(u===l){const T=t-(o-g)+1,L=Math.max(1,n>o?g-T:n-t);i.push("   |  "+" ".repeat(T)+"^".repeat(L))}else if(u>l){if(n>o){const T=Math.max(Math.min(n-o,g),1);i.push("   |  "+"^".repeat(T))}o+=g+1}}break}return i.join(`
`)}function yt(e){let t=e;return()=>++t}function Ne(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ot={};function sr(e){ot[e]||(ot[e]=!0,Ne(e))}function ar(e,t,n){return{line:e,column:t,offset:n}}function Ae(e,t,n){return{start:e,end:t}}const ye={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},or={[ye.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function ir(e,t,...n){const r=Se(or[e],...n||[]),o={message:String(r),code:e};return t&&(o.location=t),o}const S={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},lr={[S.EXPECTED_TOKEN]:"Expected token: '{0}'",[S.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[S.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[S.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[S.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[S.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[S.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[S.EMPTY_PLACEHOLDER]:"Empty placeholder",[S.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[S.INVALID_LINKED_FORMAT]:"Invalid linked format",[S.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[S.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[S.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[S.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[S.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[S.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function ge(e,t,n={}){const{domain:r,messages:o,args:i}=n,l=Se((o||lr)[e]||"",...i||[]),u=new SyntaxError(String(l));return u.code=e,t&&(u.location=t),u.domain=r,u}function cr(e){throw e}const ur=/<\/?[\w\s="/.':;#-\/]+>/,fr=e=>ur.test(e),Z=" ",mr="\r",K=`
`,dr="\u2028",_r="\u2029";function Er(e){const t=e;let n=0,r=1,o=1,i=0;const l=p=>t[p]===mr&&t[p+1]===K,u=p=>t[p]===K,f=p=>t[p]===_r,g=p=>t[p]===dr,T=p=>l(p)||u(p)||f(p)||g(p),L=()=>n,N=()=>r,b=()=>o,k=()=>i,A=p=>l(p)||f(p)||g(p)?K:t[p],C=()=>A(n),P=()=>A(n+i);function I(){return i=0,T(n)&&(r++,o=0),l(n)&&n++,n++,o++,t[n]}function c(){return l(n+i)&&i++,i++,t[n+i]}function m(){n=0,r=1,o=1,i=0}function E(p=0){i=p}function _(){const p=n+i;for(;p!==n;)I();i=0}return{index:L,line:N,column:b,peekOffset:k,charAt:A,currentChar:C,currentPeek:P,next:I,peek:c,reset:m,resetPeek:E,skipToPeek:_}}const ee=void 0,pr=".",it="'",Nr="tokenizer";function gr(e,t={}){const n=t.location!==!1,r=Er(e),o=()=>r.index(),i=()=>ar(r.line(),r.column(),r.index()),l=i(),u=o(),f={currentType:14,offset:u,startLoc:l,endLoc:l,lastType:14,lastOffset:u,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},g=()=>f,{onError:T}=t;function L(s,a,d,...O){const F=g();if(a.column+=d,a.offset+=d,T){const R=n?Ae(F.startLoc,a):null,z=ge(s,R,{domain:Nr,args:O});T(z)}}function N(s,a,d){s.endLoc=i(),s.currentType=a;const O={type:a};return n&&(O.loc=Ae(s.startLoc,s.endLoc)),d!=null&&(O.value=d),O}const b=s=>N(s,14);function k(s,a){return s.currentChar()===a?(s.next(),a):(L(S.EXPECTED_TOKEN,i(),0,a),"")}function A(s){let a="";for(;s.currentPeek()===Z||s.currentPeek()===K;)a+=s.currentPeek(),s.peek();return a}function C(s){const a=A(s);return s.skipToPeek(),a}function P(s){if(s===ee)return!1;const a=s.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a===95}function I(s){if(s===ee)return!1;const a=s.charCodeAt(0);return a>=48&&a<=57}function c(s,a){const{currentType:d}=a;if(d!==2)return!1;A(s);const O=P(s.currentPeek());return s.resetPeek(),O}function m(s,a){const{currentType:d}=a;if(d!==2)return!1;A(s);const O=s.currentPeek()==="-"?s.peek():s.currentPeek(),F=I(O);return s.resetPeek(),F}function E(s,a){const{currentType:d}=a;if(d!==2)return!1;A(s);const O=s.currentPeek()===it;return s.resetPeek(),O}function _(s,a){const{currentType:d}=a;if(d!==8)return!1;A(s);const O=s.currentPeek()===".";return s.resetPeek(),O}function p(s,a){const{currentType:d}=a;if(d!==9)return!1;A(s);const O=P(s.currentPeek());return s.resetPeek(),O}function h(s,a){const{currentType:d}=a;if(!(d===8||d===12))return!1;A(s);const O=s.currentPeek()===":";return s.resetPeek(),O}function D(s,a){const{currentType:d}=a;if(d!==10)return!1;const O=()=>{const R=s.currentPeek();return R==="{"?P(s.peek()):R==="@"||R==="%"||R==="|"||R===":"||R==="."||R===Z||!R?!1:R===K?(s.peek(),O()):M(s,!1)},F=O();return s.resetPeek(),F}function U(s){A(s);const a=s.currentPeek()==="|";return s.resetPeek(),a}function Q(s){const a=A(s),d=s.currentPeek()==="%"&&s.peek()==="{";return s.resetPeek(),{isModulo:d,hasSpace:a.length>0}}function M(s,a=!0){const d=(F=!1,R="",z=!1)=>{const ae=s.currentPeek();return ae==="{"?R==="%"?!1:F:ae==="@"||!ae?R==="%"?!0:F:ae==="%"?(s.peek(),d(F,"%",!0)):ae==="|"?R==="%"||z?!0:!(R===Z||R===K):ae===Z?(s.peek(),d(!0,Z,z)):ae===K?(s.peek(),d(!0,K,z)):!0},O=d();return a&&s.resetPeek(),O}function B(s,a){const d=s.currentChar();return d===ee?ee:a(d)?(s.next(),d):null}function rn(s){const a=s.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===95||a===36}function sn(s){return B(s,rn)}function an(s){const a=s.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===95||a===36||a===45}function on(s){return B(s,an)}function ln(s){const a=s.charCodeAt(0);return a>=48&&a<=57}function cn(s){return B(s,ln)}function un(s){const a=s.charCodeAt(0);return a>=48&&a<=57||a>=65&&a<=70||a>=97&&a<=102}function fn(s){return B(s,un)}function Qe(s){let a="",d="";for(;a=cn(s);)d+=a;return d}function mn(s){C(s);const a=s.currentChar();return a!=="%"&&L(S.EXPECTED_TOKEN,i(),0,a),s.next(),"%"}function Ze(s){let a="";for(;;){const d=s.currentChar();if(d==="{"||d==="}"||d==="@"||d==="|"||!d)break;if(d==="%")if(M(s))a+=d,s.next();else break;else if(d===Z||d===K)if(M(s))a+=d,s.next();else{if(U(s))break;a+=d,s.next()}else a+=d,s.next()}return a}function dn(s){C(s);let a="",d="";for(;a=on(s);)d+=a;return s.currentChar()===ee&&L(S.UNTERMINATED_CLOSING_BRACE,i(),0),d}function _n(s){C(s);let a="";return s.currentChar()==="-"?(s.next(),a+=`-${Qe(s)}`):a+=Qe(s),s.currentChar()===ee&&L(S.UNTERMINATED_CLOSING_BRACE,i(),0),a}function En(s){return s!==it&&s!==K}function pn(s){C(s),k(s,"'");let a="",d="";for(;a=B(s,En);)a==="\\"?d+=Nn(s):d+=a;const O=s.currentChar();return O===K||O===ee?(L(S.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,i(),0),O===K&&(s.next(),k(s,"'")),d):(k(s,"'"),d)}function Nn(s){const a=s.currentChar();switch(a){case"\\":case"'":return s.next(),`\\${a}`;case"u":return ze(s,a,4);case"U":return ze(s,a,6);default:return L(S.UNKNOWN_ESCAPE_SEQUENCE,i(),0,a),""}}function ze(s,a,d){k(s,a);let O="";for(let F=0;F<d;F++){const R=fn(s);if(!R){L(S.INVALID_UNICODE_ESCAPE_SEQUENCE,i(),0,`\\${a}${O}${s.currentChar()}`);break}O+=R}return`\\${a}${O}`}function gn(s){return s!=="{"&&s!=="}"&&s!==Z&&s!==K}function hn(s){C(s);let a="",d="";for(;a=B(s,gn);)d+=a;return d}function Tn(s){let a="",d="";for(;a=sn(s);)d+=a;return d}function Ln(s){const a=d=>{const O=s.currentChar();return O==="{"||O==="%"||O==="@"||O==="|"||O==="("||O===")"||!O||O===Z?d:(d+=O,s.next(),a(d))};return a("")}function De(s){C(s);const a=k(s,"|");return C(s),a}function Me(s,a){let d=null;switch(s.currentChar()){case"{":return a.braceNest>=1&&L(S.NOT_ALLOW_NEST_PLACEHOLDER,i(),0),s.next(),d=N(a,2,"{"),C(s),a.braceNest++,d;case"}":return a.braceNest>0&&a.currentType===2&&L(S.EMPTY_PLACEHOLDER,i(),0),s.next(),d=N(a,3,"}"),a.braceNest--,a.braceNest>0&&C(s),a.inLinked&&a.braceNest===0&&(a.inLinked=!1),d;case"@":return a.braceNest>0&&L(S.UNTERMINATED_CLOSING_BRACE,i(),0),d=Te(s,a)||b(a),a.braceNest=0,d;default:{let F=!0,R=!0,z=!0;if(U(s))return a.braceNest>0&&L(S.UNTERMINATED_CLOSING_BRACE,i(),0),d=N(a,1,De(s)),a.braceNest=0,a.inLinked=!1,d;if(a.braceNest>0&&(a.currentType===5||a.currentType===6||a.currentType===7))return L(S.UNTERMINATED_CLOSING_BRACE,i(),0),a.braceNest=0,Re(s,a);if(F=c(s,a))return d=N(a,5,dn(s)),C(s),d;if(R=m(s,a))return d=N(a,6,_n(s)),C(s),d;if(z=E(s,a))return d=N(a,7,pn(s)),C(s),d;if(!F&&!R&&!z)return d=N(a,13,hn(s)),L(S.INVALID_TOKEN_IN_PLACEHOLDER,i(),0,d.value),C(s),d;break}}return d}function Te(s,a){const{currentType:d}=a;let O=null;const F=s.currentChar();switch((d===8||d===9||d===12||d===10)&&(F===K||F===Z)&&L(S.INVALID_LINKED_FORMAT,i(),0),F){case"@":return s.next(),O=N(a,8,"@"),a.inLinked=!0,O;case".":return C(s),s.next(),N(a,9,".");case":":return C(s),s.next(),N(a,10,":");default:return U(s)?(O=N(a,1,De(s)),a.braceNest=0,a.inLinked=!1,O):_(s,a)||h(s,a)?(C(s),Te(s,a)):p(s,a)?(C(s),N(a,12,Tn(s))):D(s,a)?(C(s),F==="{"?Me(s,a)||O:N(a,11,Ln(s))):(d===8&&L(S.INVALID_LINKED_FORMAT,i(),0),a.braceNest=0,a.inLinked=!1,Re(s,a))}}function Re(s,a){let d={type:14};if(a.braceNest>0)return Me(s,a)||b(a);if(a.inLinked)return Te(s,a)||b(a);switch(s.currentChar()){case"{":return Me(s,a)||b(a);case"}":return L(S.UNBALANCED_CLOSING_BRACE,i(),0),s.next(),N(a,3,"}");case"@":return Te(s,a)||b(a);default:{if(U(s))return d=N(a,1,De(s)),a.braceNest=0,a.inLinked=!1,d;const{isModulo:F,hasSpace:R}=Q(s);if(F)return R?N(a,0,Ze(s)):N(a,4,mn(s));if(M(s))return N(a,0,Ze(s));break}}return d}function On(){const{currentType:s,offset:a,startLoc:d,endLoc:O}=f;return f.lastType=s,f.lastOffset=a,f.lastStartLoc=d,f.lastEndLoc=O,f.offset=o(),f.startLoc=i(),r.currentChar()===ee?N(f,14):Re(r,f)}return{nextToken:On,currentOffset:o,currentPosition:i,context:g}}const hr="parser",Tr=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Lr(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"�"}}}function Or(e={}){const t=e.location!==!1,{onError:n,onWarn:r}=e;function o(c,m,E,_,...p){const h=c.currentPosition();if(h.offset+=_,h.column+=_,n){const D=t?Ae(E,h):null,U=ge(m,D,{domain:hr,args:p});n(U)}}function i(c,m,E,_,...p){const h=c.currentPosition();if(h.offset+=_,h.column+=_,r){const D=t?Ae(E,h):null;r(ir(m,D,p))}}function l(c,m,E){const _={type:c};return t&&(_.start=m,_.end=m,_.loc={start:E,end:E}),_}function u(c,m,E,_){t&&(c.end=m,c.loc&&(c.loc.end=E))}function f(c,m){const E=c.context(),_=l(3,E.offset,E.startLoc);return _.value=m,u(_,c.currentOffset(),c.currentPosition()),_}function g(c,m){const E=c.context(),{lastOffset:_,lastStartLoc:p}=E,h=l(5,_,p);return h.index=parseInt(m,10),c.nextToken(),u(h,c.currentOffset(),c.currentPosition()),h}function T(c,m,E){const _=c.context(),{lastOffset:p,lastStartLoc:h}=_,D=l(4,p,h);return D.key=m,E===!0&&(D.modulo=!0),c.nextToken(),u(D,c.currentOffset(),c.currentPosition()),D}function L(c,m){const E=c.context(),{lastOffset:_,lastStartLoc:p}=E,h=l(9,_,p);return h.value=m.replace(Tr,Lr),c.nextToken(),u(h,c.currentOffset(),c.currentPosition()),h}function N(c){const m=c.nextToken(),E=c.context(),{lastOffset:_,lastStartLoc:p}=E,h=l(8,_,p);return m.type!==12?(o(c,S.UNEXPECTED_EMPTY_LINKED_MODIFIER,E.lastStartLoc,0),h.value="",u(h,_,p),{nextConsumeToken:m,node:h}):(m.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,x(m)),h.value=m.value||"",u(h,c.currentOffset(),c.currentPosition()),{node:h})}function b(c,m){const E=c.context(),_=l(7,E.offset,E.startLoc);return _.value=m,u(_,c.currentOffset(),c.currentPosition()),_}function k(c){const m=c.context(),E=l(6,m.offset,m.startLoc);let _=c.nextToken();if(_.type===9){const p=N(c);E.modifier=p.node,_=p.nextConsumeToken||c.nextToken()}switch(_.type!==10&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(_)),_=c.nextToken(),_.type===2&&(_=c.nextToken()),_.type){case 11:_.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(_)),E.key=b(c,_.value||"");break;case 5:_.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(_)),E.key=T(c,_.value||"");break;case 6:_.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(_)),E.key=g(c,_.value||"");break;case 7:_.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(_)),E.key=L(c,_.value||"");break;default:{o(c,S.UNEXPECTED_EMPTY_LINKED_KEY,m.lastStartLoc,0);const p=c.context(),h=l(7,p.offset,p.startLoc);return h.value="",u(h,p.offset,p.startLoc),E.key=h,u(E,p.offset,p.startLoc),{nextConsumeToken:_,node:E}}}return u(E,c.currentOffset(),c.currentPosition()),{node:E}}function A(c){const m=c.context(),E=m.currentType===1?c.currentOffset():m.offset,_=m.currentType===1?m.endLoc:m.startLoc,p=l(2,E,_);p.items=[];let h=null,D=null;do{const M=h||c.nextToken();switch(h=null,M.type){case 0:M.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(M)),p.items.push(f(c,M.value||""));break;case 6:M.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(M)),p.items.push(g(c,M.value||""));break;case 4:D=!0;break;case 5:M.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(M)),p.items.push(T(c,M.value||"",!!D)),D&&(i(c,ye.USE_MODULO_SYNTAX,m.lastStartLoc,0,x(M)),D=null);break;case 7:M.value==null&&o(c,S.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,x(M)),p.items.push(L(c,M.value||""));break;case 8:{const B=k(c);p.items.push(B.node),h=B.nextConsumeToken||null;break}}}while(m.currentType!==14&&m.currentType!==1);const U=m.currentType===1?m.lastOffset:c.currentOffset(),Q=m.currentType===1?m.lastEndLoc:c.currentPosition();return u(p,U,Q),p}function C(c,m,E,_){const p=c.context();let h=_.items.length===0;const D=l(1,m,E);D.cases=[],D.cases.push(_);do{const U=A(c);h||(h=U.items.length===0),D.cases.push(U)}while(p.currentType!==14);return h&&o(c,S.MUST_HAVE_MESSAGES_IN_PLURAL,E,0),u(D,c.currentOffset(),c.currentPosition()),D}function P(c){const m=c.context(),{offset:E,startLoc:_}=m,p=A(c);return m.currentType===14?p:C(c,E,_,p)}function I(c){const m=gr(c,ne({},e)),E=m.context(),_=l(0,E.offset,E.startLoc);return t&&_.loc&&(_.loc.source=c),_.body=P(m),e.onCacheKey&&(_.cacheKey=e.onCacheKey(c)),E.currentType!==14&&o(m,S.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,c[E.offset]||""),u(_,m.currentOffset(),m.currentPosition()),_}return{parse:I}}function x(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Ar(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:i=>(n.helpers.add(i),i)}}function lt(e,t){for(let n=0;n<e.length;n++)Ve(e[n],t)}function Ve(e,t){switch(e.type){case 1:lt(e.cases,t),t.helper("plural");break;case 2:lt(e.items,t);break;case 6:{Ve(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function Cr(e,t={}){const n=Ar(e);n.helper("normalize"),e.body&&Ve(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function Sr(e){const t=e.body;return t.type===2?ct(t):t.cases.forEach(n=>ct(n)),e}function ct(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=Be(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}const yr="minifier";function _e(e){switch(e.t=e.type,e.type){case 0:{const t=e;_e(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)_e(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)_e(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;_e(t.key),t.k=t.key,delete t.key,t.modifier&&(_e(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw ge(S.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:yr,args:[e.type]})}delete e.type}const Ir="parser";function br(e,t){const{filename:n,breakLineCode:r,needIndent:o}=t,i=t.location!==!1,l={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:o,indentLevel:0};i&&e.loc&&(l.source=e.loc.source);const u=()=>l;function f(A,C){l.code+=A}function g(A,C=!0){const P=C?r:"";f(o?P+"  ".repeat(A):P)}function T(A=!0){const C=++l.indentLevel;A&&g(C)}function L(A=!0){const C=--l.indentLevel;A&&g(C)}function N(){g(l.indentLevel)}return{context:u,push:f,indent:T,deindent:L,newline:N,helper:A=>`_${A}`,needIndent:()=>l.needIndent}}function Pr(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Ee(e,t.key),t.modifier?(e.push(", "),Ee(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function kr(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let i=0;i<o&&(Ee(e,t.items[i]),i!==o-1);i++)e.push(", ");e.deindent(r()),e.push("])")}function Dr(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let i=0;i<o&&(Ee(e,t.cases[i]),i!==o-1);i++)e.push(", ");e.deindent(r()),e.push("])")}}function Mr(e,t){t.body?Ee(e,t.body):e.push("null")}function Ee(e,t){const{helper:n}=e;switch(t.type){case 0:Mr(e,t);break;case 1:Dr(e,t);break;case 2:kr(e,t);break;case 6:Pr(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw ge(S.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:Ir,args:[t.type]})}}const Rr=(e,t={})=>{const n=y(t.mode)?t.mode:"normal",r=y(t.filename)?t.filename:"message.intl";t.sourceMap;const o=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,i=t.needIndent?t.needIndent:n!=="arrow",l=e.helpers||[],u=br(e,{filename:r,breakLineCode:o,needIndent:i});u.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),u.indent(i),l.length>0&&(u.push(`const { ${Be(l.map(T=>`${T}: _${T}`),", ")} } = ctx`),u.newline()),u.push("return "),Ee(u,e),u.deindent(i),u.push("}"),delete e.helpers;const{code:f,map:g}=u.context();return{ast:e,code:f,map:g?g.toJSON():void 0}};function wr(e,t={}){const n=ne({},t),r=!!n.jit,o=!!n.minify,i=n.optimize==null?!0:n.optimize,u=Or(n).parse(e);return r?(i&&Sr(u),o&&_e(u),{ast:u,code:""}):(Cr(u,n),Rr(u,n))}const re=[];re[0]={w:[0],i:[3,0],"[":[4],o:[7]};re[1]={w:[1],".":[2],"[":[4],o:[7]};re[2]={w:[2],i:[3,0],0:[3,0]};re[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};re[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};re[5]={"'":[4,0],o:8,l:[5,0]};re[6]={'"':[4,0],o:8,l:[6,0]};const Fr=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Ur(e){return Fr.test(e)}function vr(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function $r(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Wr(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:Ur(t)?vr(t):"*"+t}function It(e){const t=[];let n=-1,r=0,o=0,i,l,u,f,g,T,L;const N=[];N[0]=()=>{l===void 0?l=u:l+=u},N[1]=()=>{l!==void 0&&(t.push(l),l=void 0)},N[2]=()=>{N[0](),o++},N[3]=()=>{if(o>0)o--,r=4,N[0]();else{if(o=0,l===void 0||(l=Wr(l),l===!1))return!1;N[1]()}};function b(){const k=e[n+1];if(r===5&&k==="'"||r===6&&k==='"')return n++,u="\\"+k,N[0](),!0}for(;r!==null;)if(n++,i=e[n],!(i==="\\"&&b())){if(f=$r(i),L=re[r],g=L[f]||L.l||8,g===8||(r=g[0],g[1]!==void 0&&(T=N[g[1]],T&&(u=i,T()===!1))))return;if(r===7)return t}}const ut=new Map;function bt(e,t){return $(e)?e[t]:null}function Kr(e,t){if(!$(e))return null;let n=ut.get(t);if(n||(n=It(t),n&&ut.set(t,n)),!n)return null;const r=n.length;let o=e,i=0;for(;i<r;){const l=o[n[i]];if(l===void 0||v(o))return null;o=l,i++}return o}const jr=e=>e,Gr=e=>"",Pt="text",Yr=e=>e.length===0?"":Be(e),Xr=nr;function ft(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function Hr(e){const t=G(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(G(e.named.count)||G(e.named.n))?G(e.named.count)?e.named.count:G(e.named.n)?e.named.n:t:t}function Br(e,t){t.count||(t.count=e),t.n||(t.n=e)}function kt(e={}){const t=e.locale,n=Hr(e),r=$(e.pluralRules)&&y(t)&&v(e.pluralRules[t])?e.pluralRules[t]:ft,o=$(e.pluralRules)&&y(t)&&v(e.pluralRules[t])?ft:void 0,i=P=>P[r(n,P.length,o)],l=e.list||[],u=P=>l[P],f=e.named||X();G(e.pluralIndex)&&Br(n,f);const g=P=>f[P];function T(P){const I=v(e.messages)?e.messages(P):$(e.messages)?e.messages[P]:!1;return I||(e.parent?e.parent.message(P):Gr)}const L=P=>e.modifiers?e.modifiers[P]:jr,N=w(e.processor)&&v(e.processor.normalize)?e.processor.normalize:Yr,b=w(e.processor)&&v(e.processor.interpolate)?e.processor.interpolate:Xr,k=w(e.processor)&&y(e.processor.type)?e.processor.type:Pt,C={list:u,named:g,plural:i,linked:(P,...I)=>{const[c,m]=I;let E="text",_="";I.length===1?$(c)?(_=c.modifier||_,E=c.type||E):y(c)&&(_=c||_):I.length===2&&(y(c)&&(_=c||_),y(m)&&(E=m||E));const p=T(P)(C),h=E==="vnode"&&q(p)&&_?p[0]:p;return _?L(_)(h,E):h},message:T,type:k,interpolate:b,normalize:N,values:ne(X(),l,f)};return C}let pe=null;function Vr(e){pe=e}function xr(){return pe}function Dt(e,t,n){pe&&pe.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const Mt=Jr("function:translate");function Jr(e){return t=>pe&&pe.emit(e,t)}const Rt=ye.__EXTEND_POINT__,oe=yt(Rt),j={NOT_FOUND_KEY:Rt,FALLBACK_TO_TRANSLATE:oe(),CANNOT_FORMAT_NUMBER:oe(),FALLBACK_TO_NUMBER_FORMAT:oe(),CANNOT_FORMAT_DATE:oe(),FALLBACK_TO_DATE_FORMAT:oe(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:oe(),__EXTEND_POINT__:oe()},qr={[j.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[j.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[j.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[j.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[j.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[j.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale.",[j.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]:"This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future."};function te(e,...t){return Se(qr[e],...t)}const wt=S.__EXTEND_POINT__,ie=yt(wt),W={INVALID_ARGUMENT:wt,INVALID_DATE_ARGUMENT:ie(),INVALID_ISO_DATE_ARGUMENT:ie(),NOT_SUPPORT_NON_STRING_MESSAGE:ie(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:ie(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:ie(),NOT_SUPPORT_LOCALE_TYPE:ie(),__EXTEND_POINT__:ie()};function J(e){return ge(e,null,{messages:Qr})}const Qr={[W.INVALID_ARGUMENT]:"Invalid arguments",[W.INVALID_DATE_ARGUMENT]:"The date provided is an invalid Date object.Make sure your Date represents a valid date.",[W.INVALID_ISO_DATE_ARGUMENT]:"The argument provided is not a valid ISO date string",[W.NOT_SUPPORT_NON_STRING_MESSAGE]:"Not support non-string message",[W.NOT_SUPPORT_LOCALE_PROMISE_VALUE]:"cannot support promise value",[W.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION]:"cannot support async function",[W.NOT_SUPPORT_LOCALE_TYPE]:"cannot support locale type"};function Ie(e,t){return t.locale!=null?$e(t.locale):$e(e.locale)}let we;function $e(e){if(y(e))return e;if(v(e)){if(e.resolvedOnce&&we!=null)return we;if(e.constructor.name==="Function"){const t=e();if(tr(t))throw J(W.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return we=t}else throw J(W.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw J(W.NOT_SUPPORT_LOCALE_TYPE)}function Ft(e,t,n){return[...new Set([n,...q(t)?t:$(t)?Object.keys(t):y(t)?[t]:[n]])]}function Zr(e,t,n){const r=y(n)?n:Ce,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let i=o.__localeChainCache.get(r);if(!i){i=[];let l=[n];for(;q(l);)l=mt(i,l,t);const u=q(t)||!w(t)?t:t.default?t.default:null;l=y(u)?[u]:u,q(l)&&mt(i,l,!1),o.__localeChainCache.set(r,i)}return i}function mt(e,t,n){let r=!0;for(let o=0;o<t.length&&Y(r);o++){const i=t[o];y(i)&&(r=zr(e,t[o],n))}return r}function zr(e,t,n){let r;const o=t.split("-");do{const i=o.join("-");r=es(e,i,n),o.splice(-1,1)}while(o.length&&r===!0);return r}function es(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const o=t.replace(/!/g,"");e.push(o),(q(n)||w(n))&&n[o]&&(r=n[o])}return r}const Ut="9.14.3",be=-1,Ce="en-US",xe="",dt=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function ts(){return{upper:(e,t)=>t==="text"&&y(e)?e.toUpperCase():t==="vnode"&&$(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&y(e)?e.toLowerCase():t==="vnode"&&$(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&y(e)?dt(e):t==="vnode"&&$(e)&&"__v_isVNode"in e?dt(e.children):e}}let vt;function ns(e){vt=e}let $t;function rs(e){$t=e}let Wt;function ss(e){Wt=e}let Kt=null;const as=e=>{Kt=e},jt=()=>Kt;let Gt=null;const os=e=>{Gt=e},is=()=>Gt;let _t=0;function ls(e={}){const t=v(e.onWarn)?e.onWarn:Ne,n=y(e.version)?e.version:Ut,r=y(e.locale)||v(e.locale)?e.locale:Ce,o=v(r)?Ce:r,i=q(e.fallbackLocale)||w(e.fallbackLocale)||y(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:o,l=w(e.messages)?e.messages:Fe(o),u=w(e.datetimeFormats)?e.datetimeFormats:Fe(o),f=w(e.numberFormats)?e.numberFormats:Fe(o),g=ne(X(),e.modifiers,ts()),T=e.pluralRules||X(),L=v(e.missing)?e.missing:null,N=Y(e.missingWarn)||rt(e.missingWarn)?e.missingWarn:!0,b=Y(e.fallbackWarn)||rt(e.fallbackWarn)?e.fallbackWarn:!0,k=!!e.fallbackFormat,A=!!e.unresolving,C=v(e.postTranslation)?e.postTranslation:null,P=w(e.processor)?e.processor:null,I=Y(e.warnHtmlMessage)?e.warnHtmlMessage:!0,c=!!e.escapeParameter,m=v(e.messageCompiler)?e.messageCompiler:vt;v(e.messageCompiler)&&sr(te(j.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER));const E=v(e.messageResolver)?e.messageResolver:$t||bt,_=v(e.localeFallbacker)?e.localeFallbacker:Wt||Ft,p=$(e.fallbackContext)?e.fallbackContext:void 0,h=e,D=$(h.__datetimeFormatters)?h.__datetimeFormatters:new Map,U=$(h.__numberFormatters)?h.__numberFormatters:new Map,Q=$(h.__meta)?h.__meta:{};_t++;const M={version:n,cid:_t,locale:r,fallbackLocale:i,messages:l,modifiers:g,pluralRules:T,missing:L,missingWarn:N,fallbackWarn:b,fallbackFormat:k,unresolving:A,postTranslation:C,processor:P,warnHtmlMessage:I,escapeParameter:c,messageCompiler:m,messageResolver:E,localeFallbacker:_,fallbackContext:p,onWarn:t,__meta:Q};return M.datetimeFormats=u,M.numberFormats=f,M.__datetimeFormatters=D,M.__numberFormatters=U,M.__v_emitter=h.__v_emitter!=null?h.__v_emitter:void 0,Dt(M,n,Q),M}const Fe=e=>({[e]:X()});function Pe(e,t){return e instanceof RegExp?e.test(t):e}function Yt(e,t){return e instanceof RegExp?e.test(t):e}function ke(e,t,n,r,o){const{missing:i,onWarn:l}=e;{const u=e.__v_emitter;u&&u.emit("missing",{locale:n,key:t,type:o,groupId:`${o}:${t}`})}if(i!==null){const u=i(e,n,t,o);return y(u)?u:t}else return Yt(r,t)&&l(te(j.NOT_FOUND_KEY,{key:t,locale:n})),t}function cs(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Je(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function Xt(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(Je(e,t[r]))return!0;return!1}function Ue(e){return n=>us(n,e)}function us(e,t){const n=ms(t);if(n==null)throw he(0);if(qe(n)===1){const i=_s(n);return e.plural(i.reduce((l,u)=>[...l,Et(e,u)],[]))}else return Et(e,n)}const fs=["b","body"];function ms(e){return se(e,fs)}const ds=["c","cases"];function _s(e){return se(e,ds,[])}function Et(e,t){const n=ps(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const r=gs(t).reduce((o,i)=>[...o,We(e,i)],[]);return e.normalize(r)}}const Es=["s","static"];function ps(e){return se(e,Es)}const Ns=["i","items"];function gs(e){return se(e,Ns,[])}function We(e,t){const n=qe(t);switch(n){case 3:return Oe(t,n);case 9:return Oe(t,n);case 4:{const r=t;if(le(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(le(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw he(n)}case 5:{const r=t;if(le(r,"i")&&G(r.i))return e.interpolate(e.list(r.i));if(le(r,"index")&&G(r.index))return e.interpolate(e.list(r.index));throw he(n)}case 6:{const r=t,o=Os(r),i=Cs(r);return e.linked(We(e,i),o?We(e,o):void 0,e.type)}case 7:return Oe(t,n);case 8:return Oe(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const hs=["t","type"];function qe(e){return se(e,hs)}const Ts=["v","value"];function Oe(e,t){const n=se(e,Ts);if(n)return n;throw he(t)}const Ls=["m","modifier"];function Os(e){return se(e,Ls)}const As=["k","key"];function Cs(e){const t=se(e,As);if(t)return t;throw he(6)}function se(e,t,n){for(let r=0;r<t.length;r++){const o=t[r];if(le(e,o)&&e[o]!=null)return e[o]}return n}function he(e){return new Error(`unhandled node type: ${e}`)}const Ss="Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.";function Ht(e,t){t&&fr(e)&&Ne(Se(Ss,{source:e}))}const Bt=e=>e;let ce=X();function Vt(e){e.code===ye.USE_MODULO_SYNTAX&&Ne(`The use of named interpolation with modulo syntax is deprecated. It will be removed in v10.
reference: https://vue-i18n.intlify.dev/guide/essentials/syntax#rails-i18n-format 
(message compiler warning message: ${e.message})`)}function ys(){ce=X()}function de(e){return $(e)&&qe(e)===0&&(le(e,"b")||le(e,"body"))}function xt(e,t={}){let n=!1;const r=t.onError||cr;return t.onError=o=>{n=!0,r(o)},{...wr(e,t),detectError:n}}const Is=(e,t)=>{if(!y(e))throw J(W.NOT_SUPPORT_NON_STRING_MESSAGE);t.onWarn=Vt;{const n=Y(t.warnHtmlMessage)?t.warnHtmlMessage:!0;Ht(e,n);const o=(t.onCacheKey||Bt)(e),i=ce[o];if(i)return i;const{code:l,detectError:u}=xt(e,t),f=new Function(`return ${l}`)();return u?f:ce[o]=f}};function bs(e,t){if(t.onWarn=Vt,y(e)){const n=Y(t.warnHtmlMessage)?t.warnHtmlMessage:!0;Ht(e,n);const o=(t.onCacheKey||Bt)(e),i=ce[o];if(i)return i;const{ast:l,detectError:u}=xt(e,{...t,location:!0,jit:!0}),f=Ue(l);return u?f:ce[o]=f}else{if(!de(e))return Ne(`the message that is resolve with key '${t.key}' is not supported for jit compilation`),()=>e;const n=e.cacheKey;if(n){const r=ce[n];return r||(ce[n]=Ue(e))}else return Ue(e)}}const pt=()=>"",V=e=>v(e);function Ps(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:o,messageCompiler:i,fallbackLocale:l,messages:u}=e,[f,g]=Qt(...t),T=Y(g.missingWarn)?g.missingWarn:e.missingWarn,L=Y(g.fallbackWarn)?g.fallbackWarn:e.fallbackWarn,N=Y(g.escapeParameter)?g.escapeParameter:e.escapeParameter,b=!!g.resolvedMessage,k=y(g.default)||Y(g.default)?Y(g.default)?i?f:()=>f:g.default:n?i?f:()=>f:"",A=n||k!=="",C=Ie(e,g);N&&ks(g);let[P,I,c]=b?[f,C,u[C]||X()]:Jt(e,f,C,l,L,T),m=P,E=f;if(!b&&!(y(m)||de(m)||V(m))&&A&&(m=k,E=m),!b&&(!(y(m)||de(m)||V(m))||!y(I)))return o?be:f;if(y(m)&&e.messageCompiler==null)return Ne(`The message format compilation is not supported in this build. Because message compiler isn't included. You need to pre-compilation all message format. So translate function return '${f}'.`),f;let _=!1;const p=()=>{_=!0},h=V(m)?m:qt(e,f,I,m,E,p);if(_)return m;const D=ws(e,I,c,g),U=kt(D),Q=Ds(e,h,U),M=r?r(Q,f):Q;{const B={timestamp:Date.now(),key:y(f)?f:V(m)?m.key:"",locale:I||(V(m)?m.locale:""),format:y(m)?m:V(m)?m.source:"",message:M};B.meta=ne({},e.__meta,jt()||{}),Mt(B)}return M}function ks(e){q(e.list)?e.list=e.list.map(t=>y(t)?st(t):t):$(e.named)&&Object.keys(e.named).forEach(t=>{y(e.named[t])&&(e.named[t]=st(e.named[t]))})}function Jt(e,t,n,r,o,i){const{messages:l,onWarn:u,messageResolver:f,localeFallbacker:g}=e,T=g(e,r,n);let L=X(),N,b=null,k=n,A=null;const C="translate";for(let P=0;P<T.length;P++){if(N=A=T[P],n!==N&&!Je(n,N)&&Pe(o,t)&&u(te(j.FALLBACK_TO_TRANSLATE,{key:t,target:N})),n!==N){const E=e.__v_emitter;E&&E.emit("fallback",{type:C,key:t,from:k,to:A,groupId:`${C}:${t}`})}L=l[N]||X();let I=null,c,m;if(fe&&(I=window.performance.now(),c="intlify-message-resolve-start",m="intlify-message-resolve-end",H&&H(c)),(b=f(L,t))===null&&(b=L[t]),fe){const E=window.performance.now(),_=e.__v_emitter;_&&I&&b&&_.emit("message-resolve",{type:"message-resolve",key:t,message:b,time:E-I,groupId:`${C}:${t}`}),c&&m&&H&&me&&(H(m),me("intlify message resolve",c,m))}if(y(b)||de(b)||V(b))break;if(!Xt(N,T)){const E=ke(e,t,N,i,C);E!==t&&(b=E)}k=A}return[b,N,L]}function qt(e,t,n,r,o,i){const{messageCompiler:l,warnHtmlMessage:u}=e;if(V(r)){const N=r;return N.locale=N.locale||n,N.key=N.key||t,N}if(l==null){const N=()=>r;return N.locale=n,N.key=t,N}let f=null,g,T;fe&&(f=window.performance.now(),g="intlify-message-compilation-start",T="intlify-message-compilation-end",H&&H(g));const L=l(r,Ms(e,n,o,r,u,i));if(fe){const N=window.performance.now(),b=e.__v_emitter;b&&f&&b.emit("message-compilation",{type:"message-compilation",message:r,time:N-f,groupId:`translate:${t}`}),g&&T&&H&&me&&(H(T),me("intlify message compilation",g,T))}return L.locale=n,L.key=t,L.source=r,L}function Ds(e,t,n){let r=null,o,i;fe&&(r=window.performance.now(),o="intlify-message-evaluation-start",i="intlify-message-evaluation-end",H&&H(o));const l=t(n);if(fe){const u=window.performance.now(),f=e.__v_emitter;f&&r&&f.emit("message-evaluation",{type:"message-evaluation",value:l,time:u-r,groupId:`translate:${t.key}`}),o&&i&&H&&me&&(H(i),me("intlify message evaluation",o,i))}return l}function Qt(...e){const[t,n,r]=e,o=X();if(!y(t)&&!G(t)&&!V(t)&&!de(t))throw J(W.INVALID_ARGUMENT);const i=G(t)?String(t):(V(t),t);return G(n)?o.plural=n:y(n)?o.default=n:w(n)&&!He(n)?o.named=n:q(n)&&(o.list=n),G(r)?o.plural=r:y(r)?o.default=r:w(r)&&ne(o,r),[i,o]}function Ms(e,t,n,r,o,i){return{locale:t,key:n,warnHtmlMessage:o,onError:l=>{i&&i(l);{const u=Rs(r),f=`Message compilation error: ${l.message}`,g=l.location&&u&&rr(u,l.location.start.offset,l.location.end.offset),T=e.__v_emitter;T&&u&&T.emit("compile-error",{message:u,error:l.message,start:l.location&&l.location.start.offset,end:l.location&&l.location.end.offset,groupId:`translate:${n}`}),console.error(g?`${f}
${g}`:f)}},onCacheKey:l=>qn(t,n,l)}}function Rs(e){if(y(e))return e;if(e.loc&&e.loc.source)return e.loc.source}function ws(e,t,n,r){const{modifiers:o,pluralRules:i,messageResolver:l,fallbackLocale:u,fallbackWarn:f,missingWarn:g,fallbackContext:T}=e,N={locale:t,modifiers:o,pluralRules:i,messages:b=>{let k=l(n,b);if(k==null&&T){const[,,A]=Jt(T,b,t,u,f,g);k=l(A,b)}if(y(k)||de(k)){let A=!1;const P=qt(e,b,t,k,b,()=>{A=!0});return A?pt:P}else return V(k)?k:pt}};return e.processor&&(N.processor=e.processor),r.list&&(N.list=r.list),r.named&&(N.named=r.named),G(r.plural)&&(N.pluralIndex=r.plural),N}const Nt=typeof Intl<"u",Zt={dateTimeFormat:Nt&&typeof Intl.DateTimeFormat<"u",numberFormat:Nt&&typeof Intl.NumberFormat<"u"};function Fs(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:i,localeFallbacker:l}=e,{__datetimeFormatters:u}=e;if(!Zt.dateTimeFormat)return i(te(j.CANNOT_FORMAT_DATE)),xe;const[f,g,T,L]=en(...t),N=Y(T.missingWarn)?T.missingWarn:e.missingWarn,b=Y(T.fallbackWarn)?T.fallbackWarn:e.fallbackWarn,k=!!T.part,A=Ie(e,T),C=l(e,o,A);if(!y(f)||f==="")return new Intl.DateTimeFormat(A,L).format(g);let P={},I,c=null,m=A,E=null;const _="datetime format";for(let D=0;D<C.length;D++){if(I=E=C[D],A!==I&&Pe(b,f)&&i(te(j.FALLBACK_TO_DATE_FORMAT,{key:f,target:I})),A!==I){const U=e.__v_emitter;U&&U.emit("fallback",{type:_,key:f,from:m,to:E,groupId:`${_}:${f}`})}if(P=n[I]||{},c=P[f],w(c))break;ke(e,f,I,N,_),m=E}if(!w(c)||!y(I))return r?be:f;let p=`${I}__${f}`;He(L)||(p=`${p}__${JSON.stringify(L)}`);let h=u.get(p);return h||(h=new Intl.DateTimeFormat(I,ne({},c,L)),u.set(p,h)),k?h.formatToParts(g):h.format(g)}const zt=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function en(...e){const[t,n,r,o]=e,i=X();let l=X(),u;if(y(t)){const f=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!f)throw J(W.INVALID_ISO_DATE_ARGUMENT);const g=f[3]?f[3].trim().startsWith("T")?`${f[1].trim()}${f[3].trim()}`:`${f[1].trim()}T${f[3].trim()}`:f[1].trim();u=new Date(g);try{u.toISOString()}catch{throw J(W.INVALID_ISO_DATE_ARGUMENT)}}else if(Zn(t)){if(isNaN(t.getTime()))throw J(W.INVALID_DATE_ARGUMENT);u=t}else if(G(t))u=t;else throw J(W.INVALID_ARGUMENT);return y(n)?i.key=n:w(n)&&Object.keys(n).forEach(f=>{zt.includes(f)?l[f]=n[f]:i[f]=n[f]}),y(r)?i.locale=r:w(r)&&(l=r),w(o)&&(l=o),[i.key||"",u,i,l]}function Us(e,t,n){const r=e;for(const o in n){const i=`${t}__${o}`;r.__datetimeFormatters.has(i)&&r.__datetimeFormatters.delete(i)}}function vs(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:i,localeFallbacker:l}=e,{__numberFormatters:u}=e;if(!Zt.numberFormat)return i(te(j.CANNOT_FORMAT_NUMBER)),xe;const[f,g,T,L]=nn(...t),N=Y(T.missingWarn)?T.missingWarn:e.missingWarn,b=Y(T.fallbackWarn)?T.fallbackWarn:e.fallbackWarn,k=!!T.part,A=Ie(e,T),C=l(e,o,A);if(!y(f)||f==="")return new Intl.NumberFormat(A,L).format(g);let P={},I,c=null,m=A,E=null;const _="number format";for(let D=0;D<C.length;D++){if(I=E=C[D],A!==I&&Pe(b,f)&&i(te(j.FALLBACK_TO_NUMBER_FORMAT,{key:f,target:I})),A!==I){const U=e.__v_emitter;U&&U.emit("fallback",{type:_,key:f,from:m,to:E,groupId:`${_}:${f}`})}if(P=n[I]||{},c=P[f],w(c))break;ke(e,f,I,N,_),m=E}if(!w(c)||!y(I))return r?be:f;let p=`${I}__${f}`;He(L)||(p=`${p}__${JSON.stringify(L)}`);let h=u.get(p);return h||(h=new Intl.NumberFormat(I,ne({},c,L)),u.set(p,h)),k?h.formatToParts(g):h.format(g)}const tn=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function nn(...e){const[t,n,r,o]=e,i=X();let l=X();if(!G(t))throw J(W.INVALID_ARGUMENT);const u=t;return y(n)?i.key=n:w(n)&&Object.keys(n).forEach(f=>{tn.includes(f)?l[f]=n[f]:i[f]=n[f]}),y(r)?i.locale=r:w(r)&&(l=r),w(o)&&(l=o),[i.key||"",u,i,l]}function $s(e,t,n){const r=e;for(const o in n){const i=`${t}__${o}`;r.__numberFormatters.has(i)&&r.__numberFormatters.delete(i)}}const Ws=Object.freeze(Object.defineProperty({__proto__:null,CompileErrorCodes:S,CoreErrorCodes:W,CoreWarnCodes:j,DATETIME_FORMAT_OPTIONS_KEYS:zt,DEFAULT_LOCALE:Ce,DEFAULT_MESSAGE_DATA_TYPE:Pt,MISSING_RESOLVE_VALUE:xe,NOT_REOSLVED:be,NUMBER_FORMAT_OPTIONS_KEYS:tn,VERSION:Ut,clearCompileCache:ys,clearDateTimeFormat:Us,clearNumberFormat:$s,compile:bs,compileToFunction:Is,createCompileError:ge,createCoreContext:ls,createCoreError:J,createMessageContext:kt,datetime:Fs,fallbackWithLocaleChain:Zr,fallbackWithSimple:Ft,getAdditionalMeta:jt,getDevToolsHook:xr,getFallbackContext:is,getLocale:Ie,getWarnMessage:te,handleMissing:ke,initI18nDevTools:Dt,isAlmostSameLocale:Je,isImplicitFallback:Xt,isMessageAST:de,isMessageFunction:V,isTranslateFallbackWarn:Pe,isTranslateMissingWarn:Yt,number:vs,parse:It,parseDateTimeArgs:en,parseNumberArgs:nn,parseTranslateArgs:Qt,registerLocaleFallbacker:ss,registerMessageCompiler:ns,registerMessageResolver:rs,resolveLocale:$e,resolveValue:Kr,resolveWithKeyValue:bt,setAdditionalMeta:as,setDevToolsHook:Vr,setFallbackContext:os,translate:Ps,translateDevTools:Mt,updateFallbackLocale:cs},Symbol.toStringTag,{value:"Module"})),Gs=gt(Ws);export{js as a,Gs as r};
