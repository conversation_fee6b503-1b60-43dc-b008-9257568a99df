<template>
  <div class="ma-content-block">
    <sa-table ref="crudRef" :options="options" :columns="columns" :searchForm="searchForm">
      <!-- 搜索区 tableSearch -->
      <template #tableSearch>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="choucheng_receivertype">
            <a-input v-model="searchForm.choucheng_receivertype" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="choucheng_receivertype1_account">
            <a-input v-model="searchForm.choucheng_receivertype1_account" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="choucheng_receivertype1_name">
            <a-input v-model="searchForm.choucheng_receivertype1_name" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="choucheng_receivertype2_openidtype">
            <a-input v-model="searchForm.choucheng_receivertype2_openidtype" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="choucheng_receivertype2_account">
            <a-input v-model="searchForm.choucheng_receivertype2_account" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="choucheng_receivertype2_accountwx">
            <a-input v-model="searchForm.choucheng_receivertype2_accountwx" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="choucheng_receivertype2_name">
            <a-input v-model="searchForm.choucheng_receivertype2_name" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
      </template>

      <!-- Table 自定义渲染 -->
    </sa-table>

    <!-- 编辑表单 -->
    <edit-form ref="editRef" @success="refresh" />

  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import EditForm from './edit.vue'
import api from '../api/admin'

// 引用定义
const crudRef = ref()
const editRef = ref()
const viewRef = ref()

// 搜索表单
const searchForm = ref({
  choucheng_receivertype: '',
  choucheng_receivertype1_account: '',
  choucheng_receivertype1_name: '',
  choucheng_receivertype2_openidtype: '',
  choucheng_receivertype2_account: '',
  choucheng_receivertype2_accountwx: '',
  choucheng_receivertype2_name: '',
})

// SaTable 基础配置
const options = reactive({
  api: api.getPageList,
  rowSelection: { showCheckedAll: true },
  add: {
    show: true,
    auth: ['/rew/DdwxAdmin/save'],
    func: async () => {
      editRef.value?.open()
    },
  },
  edit: {
    show: true,
    auth: ['/rew/DdwxAdmin/update'],
    func: async (record) => {
      editRef.value?.open('edit')
      editRef.value?.setFormData(record)
    },
  },
  delete: {
    show: true,
    auth: ['/rew/DdwxAdmin/destroy'],
    func: async (params) => {
      const resp = await api.destroy(params)
      if (resp.code === 200) {
        Message.success(`删除成功！`)
        crudRef.value?.refresh()
      }
    },
  },
})

// SaTable 列配置
const columns = reactive([
  { title: '', dataIndex: 'createtime', width: 180 },
  { title: '', dataIndex: 'token', width: 180 },
  { title: '', dataIndex: 'chouchengset', width: 180 },
  { title: '', dataIndex: 'chouchengrate', width: 180 },
  { title: '', dataIndex: 'chouchengmin', width: 180 },
  { title: '', dataIndex: 'chouchengmoney', width: 180 },
  { title: '', dataIndex: 'linkman', width: 180 },
  { title: '', dataIndex: 'tel', width: 180 },
  { title: '', dataIndex: 'province', width: 180 },
  { title: '', dataIndex: 'city', width: 180 },
  { title: '', dataIndex: 'area', width: 180 },
  { title: '', dataIndex: 'address', width: 180 },
  { title: '0公众号 1小程序', dataIndex: 'platform', width: 180 },
  { title: '', dataIndex: 'status', width: 180 },
  { title: '', dataIndex: 'endtime', width: 180 },
  { title: '', dataIndex: 'pid', width: 180 },
  { title: '', dataIndex: 'copyright', width: 180 },
  { title: '', dataIndex: 'domain', width: 180 },
  { title: '', dataIndex: 'image_search', type: 'image', width: 120 },
  { title: '', dataIndex: 'agent_card', width: 180 },
  { title: '', dataIndex: 'order_show_onlychildren', width: 180 },
  { title: '', dataIndex: 'choucheng_receivertype', width: 180 },
  { title: '', dataIndex: 'choucheng_receivertype1_account', width: 180 },
  { title: '', dataIndex: 'choucheng_receivertype1_name', width: 180 },
  { title: '', dataIndex: 'choucheng_receivertype2_openidtype', width: 180 },
  { title: '', dataIndex: 'choucheng_receivertype2_account', width: 180 },
  { title: '', dataIndex: 'choucheng_receivertype2_accountwx', width: 180 },
  { title: '', dataIndex: 'choucheng_receivertype2_name', width: 180 },
  { title: '', dataIndex: 'commission_frozen', width: 180 },
  { title: '', dataIndex: 'score', width: 180 },
  { title: '商城自定义购买按钮 0：关闭 1：开启', dataIndex: 'buybtn_status', width: 180 },
  { title: '多账户 0：关闭 1：开启', dataIndex: 'othermoney_status', width: 180 },
  { title: '', dataIndex: 'jushuitan_status', width: 180 },
  { title: '外链参数，外部链接是否携带系统aid和mid', dataIndex: 'with_system_param', width: 180 },
  { title: '', dataIndex: 'need_school', width: 180 },
  { title: '贡献开关', dataIndex: 'member_gongxian_status', width: 180 },
  { title: '股东投资分红状态', dataIndex: 'shareholder_status', width: 180 },
  { title: '', dataIndex: 'group_id', width: 180 },
  { title: '', dataIndex: 'file_image_total', width: 180 },
  { title: '', dataIndex: 'file_video_total', width: 180 },
  { title: '', dataIndex: 'file_other_total', width: 180 },
  { title: '', dataIndex: 'file_upload_total', width: 180 },
  { title: '', dataIndex: 'file_upload_limit', width: 180 },
  { title: '服务商流量主分账比例', dataIndex: 'ad_ratio', width: 180 },
])

// 页面数据初始化
const initPage = async () => {}

// SaTable 数据请求
const refresh = async () => {
  crudRef.value?.refresh()
}

// 页面加载完成执行
onMounted(async () => {
  initPage()
  refresh()
})
</script>
