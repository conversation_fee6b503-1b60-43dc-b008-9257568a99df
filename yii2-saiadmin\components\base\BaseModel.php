<?php
/**
 * SaiAdmin 基础模型 (Yii 2.0 兼容)
 */
namespace app\components\base;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\behaviors\BlameableBehavior;

/**
 * 基础 ActiveRecord 模型
 */
class BaseModel extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            [
                "class" => TimestampBehavior::class,
                "createdAtAttribute" => "created_at",
                "updatedAtAttribute" => "updated_at",
                "value" => function() {
                    return date("Y-m-d H:i:s");
                }
            ],
            [
                "class" => BlameableBehavior::class,
                "createdByAttribute" => "created_by",
                "updatedByAttribute" => "updated_by",
                "value" => function() {
                    return Yii::$app->user->id ?? 0;
                }
            ],
        ];
    }

    /**
     * 获取表前缀
     * @return string
     */
    public static function getTablePrefix()
    {
        return "sa_";
    }

    /**
     * 搜索范围 - 状态
     * @param \yii\db\ActiveQuery $query
     * @param int $status
     * @return \yii\db\ActiveQuery
     */
    public function scopeStatus($query, $status)
    {
        return $query->andWhere(["status" => $status]);
    }

    /**
     * 搜索范围 - 时间范围
     * @param \yii\db\ActiveQuery $query
     * @param array $timeRange
     * @return \yii\db\ActiveQuery
     */
    public function scopeTimeRange($query, $timeRange)
    {
        if (!empty($timeRange) && count($timeRange) === 2) {
            return $query->andWhere(["between", "created_at", $timeRange[0], $timeRange[1]]);
        }
        return $query;
    }

    /**
     * 软删除
     * @return bool
     */
    public function softDelete()
    {
        $this->deleted_at = date("Y-m-d H:i:s");
        return $this->save(false);
    }

    /**
     * 批量插入
     * @param array $data
     * @return bool
     */
    public static function batchInsert($data)
    {
        if (empty($data)) {
            return false;
        }
        
        $columns = array_keys($data[0]);
        $values = array_values($data);
        
        return Yii::$app->db->createCommand()
            ->batchInsert(static::tableName(), $columns, $values)
            ->execute() > 0;
    }
}