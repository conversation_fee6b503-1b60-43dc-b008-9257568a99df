<?php
/**
 * 演示控制器 - Yii 2.0 风格
 */
namespace app\controllers;

use Yii;
use app\components\base\BaseController;
use yii\web\Response;

/**
 * 演示控制器
 */
class DemoController extends BaseController
{
    /**
     * 演示首页
     */
    public function actionIndex()
    {
        return $this->render("index", [
            "title" => "SaiAdmin Yii 2.0 演示",
            "message" => "欢迎使用 SaiAdmin Yii 2.0 框架！",
            "features" => [
                "完全兼容 Yii 2.0",
                "模块化架构设计", 
                "丰富的组件系统",
                "强大的代码生成器",
                "完善的调试工具"
            ]
        ]);
    }
    
    /**
     * API演示
     */
    public function actionApi()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        return $this->success([
            "framework" => "SaiAdmin Yii 2.0",
            "version" => Yii::getVersion(),
            "php_version" => PHP_VERSION,
            "timestamp" => time(),
            "features" => [
                "RESTful API",
                "JSON响应格式",
                "统一错误处理",
                "请求验证",
                "响应缓存"
            ]
        ]);
    }
    
    /**
     * 表单演示
     */
    public function actionForm()
    {
        $model = new \yii\base\DynamicModel(["name", "email", "message"]);
        $model->addRule(["name", "email"], "required")
              ->addRule("email", "email")
              ->addRule("message", "string", ["max" => 500]);
        
        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            Yii::$app->session->setFlash("success", "表单提交成功！");
            return $this->refresh();
        }
        
        return $this->render("form", [
            "model" => $model
        ]);
    }
    
    /**
     * 数据库演示
     */
    public function actionDatabase()
    {
        try {
            $db = Yii::$app->db;
            $tables = $db->createCommand("SHOW TABLES")->queryAll();
            
            return $this->render("database", [
                "connection" => [
                    "driver" => $db->driverName,
                    "dsn" => $db->dsn,
                    "status" => "连接成功"
                ],
                "tables" => $tables
            ]);
        } catch (Exception $e) {
            return $this->render("database", [
                "connection" => [
                    "status" => "连接失败",
                    "error" => $e->getMessage()
                ],
                "tables" => []
            ]);
        }
    }
    
    /**
     * 缓存演示
     */
    public function actionCache()
    {
        $cache = Yii::$app->cache;
        $key = "demo_cache_" . date("Y-m-d");
        
        $data = $cache->get($key);
        if ($data === false) {
            $data = [
                "generated_at" => date("Y-m-d H:i:s"),
                "random_number" => rand(1000, 9999),
                "cache_key" => $key
            ];
            $cache->set($key, $data, 3600); // 缓存1小时
        }
        
        return $this->render("cache", [
            "cache_data" => $data,
            "cache_info" => [
                "class" => get_class($cache),
                "key" => $key,
                "ttl" => "3600秒"
            ]
        ]);
    }
}