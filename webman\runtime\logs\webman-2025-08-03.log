[2025-08-03 05:51:06] default.ERROR: 127.0.0.1 GET 127.0.0.1:8787/tesxt/DdwxArticle/index?page=1&limit=10&name=&subname=&showname=&showsubname=
think\db\exception\PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ddwx_article.delete_time' in 'where clause' in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1255): think\db\PDOConnection->getPDOStatement('SELECT COUNT(*)...', Array, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1288): think\db\PDOConnection->value(Object(think\db\Query), 'COUNT(*) AS thi...', 0, false)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(35): think\db\PDOConnection->aggregate(Object(think\db\Query), 'COUNT', 'COUNT(*) AS thi...', false)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(51): think\db\BaseQuery->aggregate('COUNT', '*')
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(842): think\db\BaseQuery->count()
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(182): think\db\BaseQuery->paginate(10, false, Array)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\app\tesxt\controller\DdwxArticleController.php(44): plugin\saiadmin\basic\BaseLogic->getList(Object(think\db\Query))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): app\tesxt\controller\DdwxArticleController->index(Object(support\Request))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #609)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#15 {main} [] []
