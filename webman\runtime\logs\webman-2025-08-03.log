[2025-08-03 05:51:06] default.ERROR: 127.0.0.1 GET 127.0.0.1:8787/tesxt/DdwxArticle/index?page=1&limit=10&name=&subname=&showname=&showsubname=
think\db\exception\PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ddwx_article.delete_time' in 'where clause' in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1255): think\db\PDOConnection->getPDOStatement('SELECT COUNT(*)...', Array, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1288): think\db\PDOConnection->value(Object(think\db\Query), 'COUNT(*) AS thi...', 0, false)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(35): think\db\PDOConnection->aggregate(Object(think\db\Query), 'COUNT', 'COUNT(*) AS thi...', false)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(51): think\db\BaseQuery->aggregate('COUNT', '*')
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(842): think\db\BaseQuery->count()
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(182): think\db\BaseQuery->paginate(10, false, Array)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\app\tesxt\controller\DdwxArticleController.php(44): plugin\saiadmin\basic\BaseLogic->getList(Object(think\db\Query))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): app\tesxt\controller\DdwxArticleController->index(Object(support\Request))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #609)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#15 {main} [] []
[2025-08-03 06:08:37] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '1' for column 'start_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(180): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #471)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:08:41] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '1' for column 'start_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #497)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:10:01] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '04:00:06' for column 'start_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(180): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #406)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:10:04] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '04:00:06' for column 'start_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #436)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:10:08] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '04:00:06' for column 'start_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #437)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:11:09] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '' for column 'start_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(180): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #406)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:11:13] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '' for column 'start_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #436)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:11:21] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect datetime value: '' for column 'end_prepayment_at' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #437)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:11:26] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'pay_limit' cannot be null in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #438)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:11:32] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'pay_limit' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #439)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
[2025-08-03 06:11:39] default.ERROR: 127.0.0.1 POST 127.0.0.1:8787/erw/ZjhjBdAdvanceGoods/save
think\db\exception\PDOException: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'pay_limit' at row 1 in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `zj...', Array, true, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `zj...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `zj...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\BaseQuery.php(1266): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(811): think\db\BaseQuery->insert(Array, true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(1529): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(806): think\db\BaseQuery->transaction(Object(Closure))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(621): think\Model->insertData(NULL)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseLogic.php(103): think\Model->save(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\basic\BaseController.php(115): plugin\saiadmin\basic\BaseLogic->add(Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\basic\BaseController->save(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #440)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#19 {main} [] []
