<?php
/**
 * 表单演示视图
 */
use yii\helpers\Html;
use yii\widgets\ActiveForm;

$this->title = "表单演示";
?>

<div class="demo-form">
    <h1><?= Html::encode($this->title) ?></h1>
    
    <?php if (Yii::$app->session->hasFlash("success")): ?>
        <div class="alert alert-success">
            <?= Yii::$app->session->getFlash("success") ?>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-6">
            <?php $form = ActiveForm::begin(); ?>
            
            <?= $form->field($model, "name")->textInput(["placeholder" => "请输入姓名"]) ?>
            
            <?= $form->field($model, "email")->textInput(["placeholder" => "请输入邮箱"]) ?>
            
            <?= $form->field($model, "message")->textarea(["rows" => 4, "placeholder" => "请输入消息内容"]) ?>
            
            <div class="form-group">
                <?= Html::submitButton("提交", ["class" => "btn btn-primary"]) ?>
                <?= Html::a("返回", ["demo/index"], ["class" => "btn btn-secondary"]) ?>
            </div>
            
            <?php ActiveForm::end(); ?>
        </div>
        
        <div class="col-md-6">
            <h4>📝 表单特性</h4>
            <ul>
                <li>客户端验证</li>
                <li>服务端验证</li>
                <li>CSRF保护</li>
                <li>Flash消息</li>
                <li>Bootstrap样式</li>
            </ul>
        </div>
    </div>
</div>