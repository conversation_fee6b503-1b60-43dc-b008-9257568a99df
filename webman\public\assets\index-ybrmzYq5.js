const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-CjSAeS3E.js","assets/statistics-C7hjx8ku.js","assets/st-count-DHnX2X1-.js","assets/@vue-9ZIPiVZG.js","assets/@wangeditor-Bg8kJaak.js","assets/@wangeditor-nHDhGvq6.css","assets/pinia-CtMvrpix.js","assets/axios-BOAqGR8s.js","assets/@arco-design-uttiljWv.js","assets/resize-observer-polyfill-B1PUzC5B.js","assets/compute-scroll-into-view-1gs_hJI2.js","assets/b-tween-BtQQsX34.js","assets/dayjs-DUkVwsK-.js","assets/number-precision-BW_FzNZC.js","assets/scroll-into-view-if-needed-SxpMpKWN.js","assets/b-validate-DHOn5MGm.js","assets/vue-QIJ1KGct.js","assets/color-JIMhKyf3.js","assets/color-string-Ckj7g19G.js","assets/color-name-BQ5IbGbl.js","assets/simple-swizzle-BUB9Iq-C.js","assets/is-arrayish-BII_q15j.js","assets/@arco-design-BiEPdq2w.css","assets/crypto-js-B6um_4t4.js","assets/lodash-fWIJiXPB.js","assets/qs-CGfOb-kZ.js","assets/side-channel-0xN0c_x9.js","assets/es-errors-CFxpeikN.js","assets/object-inspect-Pz2pmunN.js","assets/side-channel-list-asz5kCf8.js","assets/side-channel-map-DBz1yoQn.js","assets/get-intrinsic-CCph2EoF.js","assets/es-object-atoms-Ditt1eQ6.js","assets/math-intrinsics-Cv-yPkyD.js","assets/gopd-fcd2-aIC.js","assets/es-define-property-bDCdrV83.js","assets/has-symbols-BaUvM3gb.js","assets/get-proto-cKMTtFGz.js","assets/dunder-proto-CiSsr-aM.js","assets/call-bind-apply-helpers-DFdvRtIg.js","assets/function-bind-CHqF18-c.js","assets/hasown-DwiY0sux.js","assets/call-bound-BMZ_xw6V.js","assets/side-channel-weakmap-CMrfu08b.js","assets/vue-router-DXldG2q0.js","assets/nprogress-DxiOKil-.js","assets/nprogress-CSXic_Zd.css","assets/monaco-editor-nMXQdunA.js","assets/monaco-editor-DLvI6UQ2.css","assets/file2md5-B4-SI92N.js","assets/spark-md5-D8tidE2e.js","assets/lodash.noop-BeiKyXVG.js","assets/vue-color-kit-w75Wyu4C.js","assets/vue-color-kit-dVVLog6c.css","assets/vue-clipboard3-DpvFlCWw.js","assets/clipboard-ehac6u_s.js","assets/vue-echarts-B-rvonkO.js","assets/resize-detector-G6vbKCU7.js","assets/echarts-Cz-L25MO.js","assets/tslib-BDyQ-Jie.js","assets/zrender-xbpiMqDc.js","assets/@iconify-BfRLEUc9.js","assets/vue-i18n-PeqK6azz.js","assets/@intlify-CJ2pDqUV.js","assets/st-count-BYk9DI1y.css","assets/st-welcome-CzRY8ecE.js","assets/avatar-DvSZjoFF.js","assets/st-welcome-DlDaAGsu.css","assets/st-loginChart-Ce9YXVXR.js","assets/st-loginChart-2_NUfV4F.css","assets/st-saiadmin-75xpyoM0.js","assets/st-announced-CLQoQ4wT.js","assets/work-panel-BO2MRm6y.js","assets/index-DRV136vu.js","assets/user-BW-rYcwt.js","assets/modifyPassword-CrighWRv.js","assets/userInfomation-DALxJbDz.js","assets/index-BhCz8zJ3.css","assets/index-CgeF7ukd.js","assets/logo-B7uA2Tfd.js","assets/sortablejs-C83syoBY.js","assets/index-Dp4EISnj.css","assets/login-BtLUskZ_.js","assets/login-45LPtqKq.css","assets/404-C8qIY0DY.js","assets/404-D4vdWXN8.css","assets/editInfo-DXBGScbZ.js","assets/table-BHRaYvrI.js","assets/menu-CgiEA4rB.js","assets/dict-C6FxRZf9.js","assets/settingComponent-BvoGVFoj.js","assets/settingComponent-CO66zv16.css","assets/vars-CZGqfX5Y.js","assets/editInfo-Y-e0zUFW.css","assets/formDesign-BDBYukjt.js","assets/table-BjY8BMqW.js","assets/table-CVTajxM4.css","assets/formDesign-BSnEilW5.css","assets/loadTable-hzlR-U5D.js","assets/preview-Bqm1bMFJ.js","assets/index-D4ERJytk.js","assets/index-BZxaLGo4.css","assets/preview-R7hQO_hS.css","assets/index-XqCN5tvw.js","assets/index-DzkS705W.js","assets/index-CC62xq0j.css","assets/add-group-D5MnfTKt.js","assets/config-ZWMRzLkl.js","assets/edit-DjscfkpF.js","assets/manage-config-DHBeLiDi.js","assets/index-ChWaTs2V.js","assets/index-C6WqqdCm.js","assets/database-C9oinknn.js","assets/recycle-08ccYGa5.js","assets/struct-BuDD2Spc.js","assets/edit-D_EZLa8V.js","assets/dept-B7mu9jEv.js","assets/index-CYLgbsWO.js","assets/leader-CBXB0tWK.js","assets/dataList-BdaY1nZd.js","assets/edit-data-D-Kheltb.js","assets/edit-DyIQuM4D.js","assets/index-SzGozy2_.js","assets/emailLog-C2m1eBdk.js","assets/loginLog-BblWFeAK.js","assets/operLog-CpGgCm9d.js","assets/edit-Dj6-BOG2.js","assets/index-DpRapO2z.js","assets/index-CYYsyvFj.css","assets/index-CaZcXSlq.js","assets/index-D5MGc_nr.css","assets/edit-CyUWeZne.js","assets/index-BlmJTHZZ.js","assets/edit-a5eHS414.js","assets/post-DszuaI2s.js","assets/index-D8t8-Vcv.js","assets/view-Df7wOQNg.js","assets/menuPermission-B9LCPnXg.js","assets/role-C_2eDkr0.js","assets/menuPermission-Dvq43lIN.css","assets/edit-C4LjrAZO.js","assets/index-BFIUdcGG.js","assets/edit-xZRcGvRF.js","assets/index-DaXTbXVv.js","assets/editInfo-ChAN-DIk.js","assets/generate-CvHPqnHt.js","assets/settingComponent-C9EG_z5A.js","assets/settingComponent-jp-QAGGD.css","assets/editInfo-6g9YpDTS.css","assets/loadTable-BFTONwXN.js","assets/preview-F38rEx2-.js","assets/preview-vHikicIV.css","assets/index-Ymm_9TE1.js","assets/edit-D9KKKNcV.js","assets/crontab-DHsN78bF.js","assets/index-BqLpuig7.js","assets/logList-DmhpOLmd.js","assets/view-DkYlBne4.js","assets/index-CNx4HHXc.js","assets/install-box-UcToEVc_.js","assets/terminal-C7g1OY6E.js","assets/terminal-BsIK5D1b.css","assets/index-FIDUFMiE.css"])))=>i.map(i=>d[i]);
var uo=Object.defineProperty;var bo=(e,a,o)=>a in e?uo(e,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[a]=o;var Ke=(e,a,o)=>bo(e,typeof a!="symbol"?a+"":a,o);import{X as ea,r as _,c as ze,w as X,a2 as po,A as fo,h as s,n as C,k as r,t as l,a1 as B,q as Ee,Y as ho,l as d,y as L,a as ca,j as A,C as je,p as U,F as oe,P as de,z as V,i as go,m as q,B as vo,o as da,f as yo,ba as _o,u as K,M as pe,S as na,U as ia,E as le,G as la,O as Pe,s as ve,_ as ko}from"./@vue-9ZIPiVZG.js";import{Z as wo,T as xo,E as So}from"./@wangeditor-Bg8kJaak.js";import{d as qe,c as Ao}from"./pinia-CtMvrpix.js";import{a as ja}from"./axios-BOAqGR8s.js";import{C as ce}from"./crypto-js-B6um_4t4.js";import{l as J}from"./lodash-fWIJiXPB.js";import{q as Eo}from"./qs-CGfOb-kZ.js";import{M as W,b as aa,d as Io,e as To,f as Co,l as Vo,h as Lo,N as ta,A as Oo,i as Ro}from"./@arco-design-uttiljWv.js";import{c as jo,a as Po}from"./vue-router-DXldG2q0.js";import{N as De}from"./nprogress-DxiOKil-.js";import{_ as w}from"./monaco-editor-nMXQdunA.js";import{f as ma}from"./file2md5-B4-SI92N.js";import{s as Do}from"./vue-color-kit-w75Wyu4C.js";import{u as ra}from"./vue-clipboard3-DpvFlCWw.js";import{H as zo}from"./vue-echarts-B-rvonkO.js";import{u as qo,a as Mo,b as Fo,c as $o,d as No,e as Uo,f as Bo,g as Ho,h as Wo,j as Go,k as Yo,l as Jo}from"./echarts-Cz-L25MO.js";import{I as Pa}from"./@iconify-BfRLEUc9.js";import{v as Qo}from"./vue-i18n-PeqK6azz.js";import{d as Da,z as Zo,r as Xo}from"./dayjs-DUkVwsK-.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./clipboard-ehac6u_s.js";import"./resize-detector-G6vbKCU7.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@intlify-CJ2pDqUV.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const c of i)if(c.type==="childList")for(const t of c.addedNodes)t.tagName==="LINK"&&t.rel==="modulepreload"&&n(t)}).observe(document,{childList:!0,subtree:!0});function o(i){const c={};return i.integrity&&(c.integrity=i.integrity),i.referrerPolicy&&(c.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?c.credentials="include":i.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function n(i){if(i.ep)return;i.ep=!0;const c=o(i);fetch(i.href,c)}})();const Be=[{code:"11",name:"北京市",children:[{code:"1101",name:"市辖区",children:[{code:"110101",name:"东城区"},{code:"110102",name:"西城区"},{code:"110105",name:"朝阳区"},{code:"110106",name:"丰台区"},{code:"110107",name:"石景山区"},{code:"110108",name:"海淀区"},{code:"110109",name:"门头沟区"},{code:"110111",name:"房山区"},{code:"110112",name:"通州区"},{code:"110113",name:"顺义区"},{code:"110114",name:"昌平区"},{code:"110115",name:"大兴区"},{code:"110116",name:"怀柔区"},{code:"110117",name:"平谷区"},{code:"110118",name:"密云区"},{code:"110119",name:"延庆区"}]}]},{code:"12",name:"天津市",children:[{code:"1201",name:"市辖区",children:[{code:"120101",name:"和平区"},{code:"120102",name:"河东区"},{code:"120103",name:"河西区"},{code:"120104",name:"南开区"},{code:"120105",name:"河北区"},{code:"120106",name:"红桥区"},{code:"120110",name:"东丽区"},{code:"120111",name:"西青区"},{code:"120112",name:"津南区"},{code:"120113",name:"北辰区"},{code:"120114",name:"武清区"},{code:"120115",name:"宝坻区"},{code:"120116",name:"滨海新区"},{code:"120117",name:"宁河区"},{code:"120118",name:"静海区"},{code:"120119",name:"蓟州区"}]}]},{code:"13",name:"河北省",children:[{code:"1301",name:"石家庄市",children:[{code:"130102",name:"长安区"},{code:"130104",name:"桥西区"},{code:"130105",name:"新华区"},{code:"130107",name:"井陉矿区"},{code:"130108",name:"裕华区"},{code:"130109",name:"藁城区"},{code:"130110",name:"鹿泉区"},{code:"130111",name:"栾城区"},{code:"130121",name:"井陉县"},{code:"130123",name:"正定县"},{code:"130125",name:"行唐县"},{code:"130126",name:"灵寿县"},{code:"130127",name:"高邑县"},{code:"130128",name:"深泽县"},{code:"130129",name:"赞皇县"},{code:"130130",name:"无极县"},{code:"130131",name:"平山县"},{code:"130132",name:"元氏县"},{code:"130133",name:"赵县"},{code:"130171",name:"石家庄高新技术产业开发区"},{code:"130172",name:"石家庄循环化工园区"},{code:"130181",name:"辛集市"},{code:"130183",name:"晋州市"},{code:"130184",name:"新乐市"}]},{code:"1302",name:"唐山市",children:[{code:"130202",name:"路南区"},{code:"130203",name:"路北区"},{code:"130204",name:"古冶区"},{code:"130205",name:"开平区"},{code:"130207",name:"丰南区"},{code:"130208",name:"丰润区"},{code:"130209",name:"曹妃甸区"},{code:"130224",name:"滦南县"},{code:"130225",name:"乐亭县"},{code:"130227",name:"迁西县"},{code:"130229",name:"玉田县"},{code:"130271",name:"河北唐山芦台经济开发区"},{code:"130272",name:"唐山市汉沽管理区"},{code:"130273",name:"唐山高新技术产业开发区"},{code:"130274",name:"河北唐山海港经济开发区"},{code:"130281",name:"遵化市"},{code:"130283",name:"迁安市"},{code:"130284",name:"滦州市"}]},{code:"1303",name:"秦皇岛市",children:[{code:"130302",name:"海港区"},{code:"130303",name:"山海关区"},{code:"130304",name:"北戴河区"},{code:"130306",name:"抚宁区"},{code:"130321",name:"青龙满族自治县"},{code:"130322",name:"昌黎县"},{code:"130324",name:"卢龙县"},{code:"130371",name:"秦皇岛市经济技术开发区"},{code:"130372",name:"北戴河新区"}]},{code:"1304",name:"邯郸市",children:[{code:"130402",name:"邯山区"},{code:"130403",name:"丛台区"},{code:"130404",name:"复兴区"},{code:"130406",name:"峰峰矿区"},{code:"130407",name:"肥乡区"},{code:"130408",name:"永年区"},{code:"130423",name:"临漳县"},{code:"130424",name:"成安县"},{code:"130425",name:"大名县"},{code:"130426",name:"涉县"},{code:"130427",name:"磁县"},{code:"130430",name:"邱县"},{code:"130431",name:"鸡泽县"},{code:"130432",name:"广平县"},{code:"130433",name:"馆陶县"},{code:"130434",name:"魏县"},{code:"130435",name:"曲周县"},{code:"130471",name:"邯郸经济技术开发区"},{code:"130473",name:"邯郸冀南新区"},{code:"130481",name:"武安市"}]},{code:"1305",name:"邢台市",children:[{code:"130502",name:"襄都区"},{code:"130503",name:"信都区"},{code:"130505",name:"任泽区"},{code:"130506",name:"南和区"},{code:"130522",name:"临城县"},{code:"130523",name:"内丘县"},{code:"130524",name:"柏乡县"},{code:"130525",name:"隆尧县"},{code:"130528",name:"宁晋县"},{code:"130529",name:"巨鹿县"},{code:"130530",name:"新河县"},{code:"130531",name:"广宗县"},{code:"130532",name:"平乡县"},{code:"130533",name:"威县"},{code:"130534",name:"清河县"},{code:"130535",name:"临西县"},{code:"130571",name:"河北邢台经济开发区"},{code:"130581",name:"南宫市"},{code:"130582",name:"沙河市"}]},{code:"1306",name:"保定市",children:[{code:"130602",name:"竞秀区"},{code:"130606",name:"莲池区"},{code:"130607",name:"满城区"},{code:"130608",name:"清苑区"},{code:"130609",name:"徐水区"},{code:"130623",name:"涞水县"},{code:"130624",name:"阜平县"},{code:"130626",name:"定兴县"},{code:"130627",name:"唐县"},{code:"130628",name:"高阳县"},{code:"130629",name:"容城县"},{code:"130630",name:"涞源县"},{code:"130631",name:"望都县"},{code:"130632",name:"安新县"},{code:"130633",name:"易县"},{code:"130634",name:"曲阳县"},{code:"130635",name:"蠡县"},{code:"130636",name:"顺平县"},{code:"130637",name:"博野县"},{code:"130638",name:"雄县"},{code:"130671",name:"保定高新技术产业开发区"},{code:"130672",name:"保定白沟新城"},{code:"130681",name:"涿州市"},{code:"130682",name:"定州市"},{code:"130683",name:"安国市"},{code:"130684",name:"高碑店市"}]},{code:"1307",name:"张家口市",children:[{code:"130702",name:"桥东区"},{code:"130703",name:"桥西区"},{code:"130705",name:"宣化区"},{code:"130706",name:"下花园区"},{code:"130708",name:"万全区"},{code:"130709",name:"崇礼区"},{code:"130722",name:"张北县"},{code:"130723",name:"康保县"},{code:"130724",name:"沽源县"},{code:"130725",name:"尚义县"},{code:"130726",name:"蔚县"},{code:"130727",name:"阳原县"},{code:"130728",name:"怀安县"},{code:"130730",name:"怀来县"},{code:"130731",name:"涿鹿县"},{code:"130732",name:"赤城县"},{code:"130771",name:"张家口经济开发区"},{code:"130772",name:"张家口市察北管理区"},{code:"130773",name:"张家口市塞北管理区"}]},{code:"1308",name:"承德市",children:[{code:"130802",name:"双桥区"},{code:"130803",name:"双滦区"},{code:"130804",name:"鹰手营子矿区"},{code:"130821",name:"承德县"},{code:"130822",name:"兴隆县"},{code:"130824",name:"滦平县"},{code:"130825",name:"隆化县"},{code:"130826",name:"丰宁满族自治县"},{code:"130827",name:"宽城满族自治县"},{code:"130828",name:"围场满族蒙古族自治县"},{code:"130871",name:"承德高新技术产业开发区"},{code:"130881",name:"平泉市"}]},{code:"1309",name:"沧州市",children:[{code:"130902",name:"新华区"},{code:"130903",name:"运河区"},{code:"130921",name:"沧县"},{code:"130922",name:"青县"},{code:"130923",name:"东光县"},{code:"130924",name:"海兴县"},{code:"130925",name:"盐山县"},{code:"130926",name:"肃宁县"},{code:"130927",name:"南皮县"},{code:"130928",name:"吴桥县"},{code:"130929",name:"献县"},{code:"130930",name:"孟村回族自治县"},{code:"130971",name:"河北沧州经济开发区"},{code:"130972",name:"沧州高新技术产业开发区"},{code:"130973",name:"沧州渤海新区"},{code:"130981",name:"泊头市"},{code:"130982",name:"任丘市"},{code:"130983",name:"黄骅市"},{code:"130984",name:"河间市"}]},{code:"1310",name:"廊坊市",children:[{code:"131002",name:"安次区"},{code:"131003",name:"广阳区"},{code:"131022",name:"固安县"},{code:"131023",name:"永清县"},{code:"131024",name:"香河县"},{code:"131025",name:"大城县"},{code:"131026",name:"文安县"},{code:"131028",name:"大厂回族自治县"},{code:"131071",name:"廊坊经济技术开发区"},{code:"131081",name:"霸州市"},{code:"131082",name:"三河市"}]},{code:"1311",name:"衡水市",children:[{code:"131102",name:"桃城区"},{code:"131103",name:"冀州区"},{code:"131121",name:"枣强县"},{code:"131122",name:"武邑县"},{code:"131123",name:"武强县"},{code:"131124",name:"饶阳县"},{code:"131125",name:"安平县"},{code:"131126",name:"故城县"},{code:"131127",name:"景县"},{code:"131128",name:"阜城县"},{code:"131171",name:"河北衡水高新技术产业开发区"},{code:"131172",name:"衡水滨湖新区"},{code:"131182",name:"深州市"}]}]},{code:"14",name:"山西省",children:[{code:"1401",name:"太原市",children:[{code:"140105",name:"小店区"},{code:"140106",name:"迎泽区"},{code:"140107",name:"杏花岭区"},{code:"140108",name:"尖草坪区"},{code:"140109",name:"万柏林区"},{code:"140110",name:"晋源区"},{code:"140121",name:"清徐县"},{code:"140122",name:"阳曲县"},{code:"140123",name:"娄烦县"},{code:"140171",name:"山西转型综合改革示范区"},{code:"140181",name:"古交市"}]},{code:"1402",name:"大同市",children:[{code:"140212",name:"新荣区"},{code:"140213",name:"平城区"},{code:"140214",name:"云冈区"},{code:"140215",name:"云州区"},{code:"140221",name:"阳高县"},{code:"140222",name:"天镇县"},{code:"140223",name:"广灵县"},{code:"140224",name:"灵丘县"},{code:"140225",name:"浑源县"},{code:"140226",name:"左云县"},{code:"140271",name:"山西大同经济开发区"}]},{code:"1403",name:"阳泉市",children:[{code:"140302",name:"城区"},{code:"140303",name:"矿区"},{code:"140311",name:"郊区"},{code:"140321",name:"平定县"},{code:"140322",name:"盂县"}]},{code:"1404",name:"长治市",children:[{code:"140403",name:"潞州区"},{code:"140404",name:"上党区"},{code:"140405",name:"屯留区"},{code:"140406",name:"潞城区"},{code:"140423",name:"襄垣县"},{code:"140425",name:"平顺县"},{code:"140426",name:"黎城县"},{code:"140427",name:"壶关县"},{code:"140428",name:"长子县"},{code:"140429",name:"武乡县"},{code:"140430",name:"沁县"},{code:"140431",name:"沁源县"},{code:"140471",name:"山西长治高新技术产业园区"}]},{code:"1405",name:"晋城市",children:[{code:"140502",name:"城区"},{code:"140521",name:"沁水县"},{code:"140522",name:"阳城县"},{code:"140524",name:"陵川县"},{code:"140525",name:"泽州县"},{code:"140581",name:"高平市"}]},{code:"1406",name:"朔州市",children:[{code:"140602",name:"朔城区"},{code:"140603",name:"平鲁区"},{code:"140621",name:"山阴县"},{code:"140622",name:"应县"},{code:"140623",name:"右玉县"},{code:"140671",name:"山西朔州经济开发区"},{code:"140681",name:"怀仁市"}]},{code:"1407",name:"晋中市",children:[{code:"140702",name:"榆次区"},{code:"140703",name:"太谷区"},{code:"140721",name:"榆社县"},{code:"140722",name:"左权县"},{code:"140723",name:"和顺县"},{code:"140724",name:"昔阳县"},{code:"140725",name:"寿阳县"},{code:"140727",name:"祁县"},{code:"140728",name:"平遥县"},{code:"140729",name:"灵石县"},{code:"140781",name:"介休市"}]},{code:"1408",name:"运城市",children:[{code:"140802",name:"盐湖区"},{code:"140821",name:"临猗县"},{code:"140822",name:"万荣县"},{code:"140823",name:"闻喜县"},{code:"140824",name:"稷山县"},{code:"140825",name:"新绛县"},{code:"140826",name:"绛县"},{code:"140827",name:"垣曲县"},{code:"140828",name:"夏县"},{code:"140829",name:"平陆县"},{code:"140830",name:"芮城县"},{code:"140881",name:"永济市"},{code:"140882",name:"河津市"}]},{code:"1409",name:"忻州市",children:[{code:"140902",name:"忻府区"},{code:"140921",name:"定襄县"},{code:"140922",name:"五台县"},{code:"140923",name:"代县"},{code:"140924",name:"繁峙县"},{code:"140925",name:"宁武县"},{code:"140926",name:"静乐县"},{code:"140927",name:"神池县"},{code:"140928",name:"五寨县"},{code:"140929",name:"岢岚县"},{code:"140930",name:"河曲县"},{code:"140931",name:"保德县"},{code:"140932",name:"偏关县"},{code:"140971",name:"五台山风景名胜区"},{code:"140981",name:"原平市"}]},{code:"1410",name:"临汾市",children:[{code:"141002",name:"尧都区"},{code:"141021",name:"曲沃县"},{code:"141022",name:"翼城县"},{code:"141023",name:"襄汾县"},{code:"141024",name:"洪洞县"},{code:"141025",name:"古县"},{code:"141026",name:"安泽县"},{code:"141027",name:"浮山县"},{code:"141028",name:"吉县"},{code:"141029",name:"乡宁县"},{code:"141030",name:"大宁县"},{code:"141031",name:"隰县"},{code:"141032",name:"永和县"},{code:"141033",name:"蒲县"},{code:"141034",name:"汾西县"},{code:"141081",name:"侯马市"},{code:"141082",name:"霍州市"}]},{code:"1411",name:"吕梁市",children:[{code:"141102",name:"离石区"},{code:"141121",name:"文水县"},{code:"141122",name:"交城县"},{code:"141123",name:"兴县"},{code:"141124",name:"临县"},{code:"141125",name:"柳林县"},{code:"141126",name:"石楼县"},{code:"141127",name:"岚县"},{code:"141128",name:"方山县"},{code:"141129",name:"中阳县"},{code:"141130",name:"交口县"},{code:"141181",name:"孝义市"},{code:"141182",name:"汾阳市"}]}]},{code:"15",name:"内蒙古自治区",children:[{code:"1501",name:"呼和浩特市",children:[{code:"150102",name:"新城区"},{code:"150103",name:"回民区"},{code:"150104",name:"玉泉区"},{code:"150105",name:"赛罕区"},{code:"150121",name:"土默特左旗"},{code:"150122",name:"托克托县"},{code:"150123",name:"和林格尔县"},{code:"150124",name:"清水河县"},{code:"150125",name:"武川县"},{code:"150172",name:"呼和浩特经济技术开发区"}]},{code:"1502",name:"包头市",children:[{code:"150202",name:"东河区"},{code:"150203",name:"昆都仑区"},{code:"150204",name:"青山区"},{code:"150205",name:"石拐区"},{code:"150206",name:"白云鄂博矿区"},{code:"150207",name:"九原区"},{code:"150221",name:"土默特右旗"},{code:"150222",name:"固阳县"},{code:"150223",name:"达尔罕茂明安联合旗"},{code:"150271",name:"包头稀土高新技术产业开发区"}]},{code:"1503",name:"乌海市",children:[{code:"150302",name:"海勃湾区"},{code:"150303",name:"海南区"},{code:"150304",name:"乌达区"}]},{code:"1504",name:"赤峰市",children:[{code:"150402",name:"红山区"},{code:"150403",name:"元宝山区"},{code:"150404",name:"松山区"},{code:"150421",name:"阿鲁科尔沁旗"},{code:"150422",name:"巴林左旗"},{code:"150423",name:"巴林右旗"},{code:"150424",name:"林西县"},{code:"150425",name:"克什克腾旗"},{code:"150426",name:"翁牛特旗"},{code:"150428",name:"喀喇沁旗"},{code:"150429",name:"宁城县"},{code:"150430",name:"敖汉旗"}]},{code:"1505",name:"通辽市",children:[{code:"150502",name:"科尔沁区"},{code:"150521",name:"科尔沁左翼中旗"},{code:"150522",name:"科尔沁左翼后旗"},{code:"150523",name:"开鲁县"},{code:"150524",name:"库伦旗"},{code:"150525",name:"奈曼旗"},{code:"150526",name:"扎鲁特旗"},{code:"150571",name:"通辽经济技术开发区"},{code:"150581",name:"霍林郭勒市"}]},{code:"1506",name:"鄂尔多斯市",children:[{code:"150602",name:"东胜区"},{code:"150603",name:"康巴什区"},{code:"150621",name:"达拉特旗"},{code:"150622",name:"准格尔旗"},{code:"150623",name:"鄂托克前旗"},{code:"150624",name:"鄂托克旗"},{code:"150625",name:"杭锦旗"},{code:"150626",name:"乌审旗"},{code:"150627",name:"伊金霍洛旗"}]},{code:"1507",name:"呼伦贝尔市",children:[{code:"150702",name:"海拉尔区"},{code:"150703",name:"扎赉诺尔区"},{code:"150721",name:"阿荣旗"},{code:"150722",name:"莫力达瓦达斡尔族自治旗"},{code:"150723",name:"鄂伦春自治旗"},{code:"150724",name:"鄂温克族自治旗"},{code:"150725",name:"陈巴尔虎旗"},{code:"150726",name:"新巴尔虎左旗"},{code:"150727",name:"新巴尔虎右旗"},{code:"150781",name:"满洲里市"},{code:"150782",name:"牙克石市"},{code:"150783",name:"扎兰屯市"},{code:"150784",name:"额尔古纳市"},{code:"150785",name:"根河市"}]},{code:"1508",name:"巴彦淖尔市",children:[{code:"150802",name:"临河区"},{code:"150821",name:"五原县"},{code:"150822",name:"磴口县"},{code:"150823",name:"乌拉特前旗"},{code:"150824",name:"乌拉特中旗"},{code:"150825",name:"乌拉特后旗"},{code:"150826",name:"杭锦后旗"}]},{code:"1509",name:"乌兰察布市",children:[{code:"150902",name:"集宁区"},{code:"150921",name:"卓资县"},{code:"150922",name:"化德县"},{code:"150923",name:"商都县"},{code:"150924",name:"兴和县"},{code:"150925",name:"凉城县"},{code:"150926",name:"察哈尔右翼前旗"},{code:"150927",name:"察哈尔右翼中旗"},{code:"150928",name:"察哈尔右翼后旗"},{code:"150929",name:"四子王旗"},{code:"150981",name:"丰镇市"}]},{code:"1522",name:"兴安盟",children:[{code:"152201",name:"乌兰浩特市"},{code:"152202",name:"阿尔山市"},{code:"152221",name:"科尔沁右翼前旗"},{code:"152222",name:"科尔沁右翼中旗"},{code:"152223",name:"扎赉特旗"},{code:"152224",name:"突泉县"}]},{code:"1525",name:"锡林郭勒盟",children:[{code:"152501",name:"二连浩特市"},{code:"152502",name:"锡林浩特市"},{code:"152522",name:"阿巴嘎旗"},{code:"152523",name:"苏尼特左旗"},{code:"152524",name:"苏尼特右旗"},{code:"152525",name:"东乌珠穆沁旗"},{code:"152526",name:"西乌珠穆沁旗"},{code:"152527",name:"太仆寺旗"},{code:"152528",name:"镶黄旗"},{code:"152529",name:"正镶白旗"},{code:"152530",name:"正蓝旗"},{code:"152531",name:"多伦县"},{code:"152571",name:"乌拉盖管委会"}]},{code:"1529",name:"阿拉善盟",children:[{code:"152921",name:"阿拉善左旗"},{code:"152922",name:"阿拉善右旗"},{code:"152923",name:"额济纳旗"},{code:"152971",name:"内蒙古阿拉善高新技术产业开发区"}]}]},{code:"21",name:"辽宁省",children:[{code:"2101",name:"沈阳市",children:[{code:"210102",name:"和平区"},{code:"210103",name:"沈河区"},{code:"210104",name:"大东区"},{code:"210105",name:"皇姑区"},{code:"210106",name:"铁西区"},{code:"210111",name:"苏家屯区"},{code:"210112",name:"浑南区"},{code:"210113",name:"沈北新区"},{code:"210114",name:"于洪区"},{code:"210115",name:"辽中区"},{code:"210123",name:"康平县"},{code:"210124",name:"法库县"},{code:"210181",name:"新民市"}]},{code:"2102",name:"大连市",children:[{code:"210202",name:"中山区"},{code:"210203",name:"西岗区"},{code:"210204",name:"沙河口区"},{code:"210211",name:"甘井子区"},{code:"210212",name:"旅顺口区"},{code:"210213",name:"金州区"},{code:"210214",name:"普兰店区"},{code:"210224",name:"长海县"},{code:"210281",name:"瓦房店市"},{code:"210283",name:"庄河市"}]},{code:"2103",name:"鞍山市",children:[{code:"210302",name:"铁东区"},{code:"210303",name:"铁西区"},{code:"210304",name:"立山区"},{code:"210311",name:"千山区"},{code:"210321",name:"台安县"},{code:"210323",name:"岫岩满族自治县"},{code:"210381",name:"海城市"}]},{code:"2104",name:"抚顺市",children:[{code:"210402",name:"新抚区"},{code:"210403",name:"东洲区"},{code:"210404",name:"望花区"},{code:"210411",name:"顺城区"},{code:"210421",name:"抚顺县"},{code:"210422",name:"新宾满族自治县"},{code:"210423",name:"清原满族自治县"}]},{code:"2105",name:"本溪市",children:[{code:"210502",name:"平山区"},{code:"210503",name:"溪湖区"},{code:"210504",name:"明山区"},{code:"210505",name:"南芬区"},{code:"210521",name:"本溪满族自治县"},{code:"210522",name:"桓仁满族自治县"}]},{code:"2106",name:"丹东市",children:[{code:"210602",name:"元宝区"},{code:"210603",name:"振兴区"},{code:"210604",name:"振安区"},{code:"210624",name:"宽甸满族自治县"},{code:"210681",name:"东港市"},{code:"210682",name:"凤城市"}]},{code:"2107",name:"锦州市",children:[{code:"210702",name:"古塔区"},{code:"210703",name:"凌河区"},{code:"210711",name:"太和区"},{code:"210726",name:"黑山县"},{code:"210727",name:"义县"},{code:"210781",name:"凌海市"},{code:"210782",name:"北镇市"}]},{code:"2108",name:"营口市",children:[{code:"210802",name:"站前区"},{code:"210803",name:"西市区"},{code:"210804",name:"鲅鱼圈区"},{code:"210811",name:"老边区"},{code:"210881",name:"盖州市"},{code:"210882",name:"大石桥市"}]},{code:"2109",name:"阜新市",children:[{code:"210902",name:"海州区"},{code:"210903",name:"新邱区"},{code:"210904",name:"太平区"},{code:"210905",name:"清河门区"},{code:"210911",name:"细河区"},{code:"210921",name:"阜新蒙古族自治县"},{code:"210922",name:"彰武县"}]},{code:"2110",name:"辽阳市",children:[{code:"211002",name:"白塔区"},{code:"211003",name:"文圣区"},{code:"211004",name:"宏伟区"},{code:"211005",name:"弓长岭区"},{code:"211011",name:"太子河区"},{code:"211021",name:"辽阳县"},{code:"211081",name:"灯塔市"}]},{code:"2111",name:"盘锦市",children:[{code:"211102",name:"双台子区"},{code:"211103",name:"兴隆台区"},{code:"211104",name:"大洼区"},{code:"211122",name:"盘山县"}]},{code:"2112",name:"铁岭市",children:[{code:"211202",name:"银州区"},{code:"211204",name:"清河区"},{code:"211221",name:"铁岭县"},{code:"211223",name:"西丰县"},{code:"211224",name:"昌图县"},{code:"211281",name:"调兵山市"},{code:"211282",name:"开原市"}]},{code:"2113",name:"朝阳市",children:[{code:"211302",name:"双塔区"},{code:"211303",name:"龙城区"},{code:"211321",name:"朝阳县"},{code:"211322",name:"建平县"},{code:"211324",name:"喀喇沁左翼蒙古族自治县"},{code:"211381",name:"北票市"},{code:"211382",name:"凌源市"}]},{code:"2114",name:"葫芦岛市",children:[{code:"211402",name:"连山区"},{code:"211403",name:"龙港区"},{code:"211404",name:"南票区"},{code:"211421",name:"绥中县"},{code:"211422",name:"建昌县"},{code:"211481",name:"兴城市"}]}]},{code:"22",name:"吉林省",children:[{code:"2201",name:"长春市",children:[{code:"220102",name:"南关区"},{code:"220103",name:"宽城区"},{code:"220104",name:"朝阳区"},{code:"220105",name:"二道区"},{code:"220106",name:"绿园区"},{code:"220112",name:"双阳区"},{code:"220113",name:"九台区"},{code:"220122",name:"农安县"},{code:"220171",name:"长春经济技术开发区"},{code:"220172",name:"长春净月高新技术产业开发区"},{code:"220173",name:"长春高新技术产业开发区"},{code:"220174",name:"长春汽车经济技术开发区"},{code:"220182",name:"榆树市"},{code:"220183",name:"德惠市"},{code:"220184",name:"公主岭市"}]},{code:"2202",name:"吉林市",children:[{code:"220202",name:"昌邑区"},{code:"220203",name:"龙潭区"},{code:"220204",name:"船营区"},{code:"220211",name:"丰满区"},{code:"220221",name:"永吉县"},{code:"220271",name:"吉林经济开发区"},{code:"220272",name:"吉林高新技术产业开发区"},{code:"220273",name:"吉林中国新加坡食品区"},{code:"220281",name:"蛟河市"},{code:"220282",name:"桦甸市"},{code:"220283",name:"舒兰市"},{code:"220284",name:"磐石市"}]},{code:"2203",name:"四平市",children:[{code:"220302",name:"铁西区"},{code:"220303",name:"铁东区"},{code:"220322",name:"梨树县"},{code:"220323",name:"伊通满族自治县"},{code:"220382",name:"双辽市"}]},{code:"2204",name:"辽源市",children:[{code:"220402",name:"龙山区"},{code:"220403",name:"西安区"},{code:"220421",name:"东丰县"},{code:"220422",name:"东辽县"}]},{code:"2205",name:"通化市",children:[{code:"220502",name:"东昌区"},{code:"220503",name:"二道江区"},{code:"220521",name:"通化县"},{code:"220523",name:"辉南县"},{code:"220524",name:"柳河县"},{code:"220581",name:"梅河口市"},{code:"220582",name:"集安市"}]},{code:"2206",name:"白山市",children:[{code:"220602",name:"浑江区"},{code:"220605",name:"江源区"},{code:"220621",name:"抚松县"},{code:"220622",name:"靖宇县"},{code:"220623",name:"长白朝鲜族自治县"},{code:"220681",name:"临江市"}]},{code:"2207",name:"松原市",children:[{code:"220702",name:"宁江区"},{code:"220721",name:"前郭尔罗斯蒙古族自治县"},{code:"220722",name:"长岭县"},{code:"220723",name:"乾安县"},{code:"220771",name:"吉林松原经济开发区"},{code:"220781",name:"扶余市"}]},{code:"2208",name:"白城市",children:[{code:"220802",name:"洮北区"},{code:"220821",name:"镇赉县"},{code:"220822",name:"通榆县"},{code:"220871",name:"吉林白城经济开发区"},{code:"220881",name:"洮南市"},{code:"220882",name:"大安市"}]},{code:"2224",name:"延边朝鲜族自治州",children:[{code:"222401",name:"延吉市"},{code:"222402",name:"图们市"},{code:"222403",name:"敦化市"},{code:"222404",name:"珲春市"},{code:"222405",name:"龙井市"},{code:"222406",name:"和龙市"},{code:"222424",name:"汪清县"},{code:"222426",name:"安图县"}]}]},{code:"23",name:"黑龙江省",children:[{code:"2301",name:"哈尔滨市",children:[{code:"230102",name:"道里区"},{code:"230103",name:"南岗区"},{code:"230104",name:"道外区"},{code:"230108",name:"平房区"},{code:"230109",name:"松北区"},{code:"230110",name:"香坊区"},{code:"230111",name:"呼兰区"},{code:"230112",name:"阿城区"},{code:"230113",name:"双城区"},{code:"230123",name:"依兰县"},{code:"230124",name:"方正县"},{code:"230125",name:"宾县"},{code:"230126",name:"巴彦县"},{code:"230127",name:"木兰县"},{code:"230128",name:"通河县"},{code:"230129",name:"延寿县"},{code:"230183",name:"尚志市"},{code:"230184",name:"五常市"}]},{code:"2302",name:"齐齐哈尔市",children:[{code:"230202",name:"龙沙区"},{code:"230203",name:"建华区"},{code:"230204",name:"铁锋区"},{code:"230205",name:"昂昂溪区"},{code:"230206",name:"富拉尔基区"},{code:"230207",name:"碾子山区"},{code:"230208",name:"梅里斯达斡尔族区"},{code:"230221",name:"龙江县"},{code:"230223",name:"依安县"},{code:"230224",name:"泰来县"},{code:"230225",name:"甘南县"},{code:"230227",name:"富裕县"},{code:"230229",name:"克山县"},{code:"230230",name:"克东县"},{code:"230231",name:"拜泉县"},{code:"230281",name:"讷河市"}]},{code:"2303",name:"鸡西市",children:[{code:"230302",name:"鸡冠区"},{code:"230303",name:"恒山区"},{code:"230304",name:"滴道区"},{code:"230305",name:"梨树区"},{code:"230306",name:"城子河区"},{code:"230307",name:"麻山区"},{code:"230321",name:"鸡东县"},{code:"230381",name:"虎林市"},{code:"230382",name:"密山市"}]},{code:"2304",name:"鹤岗市",children:[{code:"230402",name:"向阳区"},{code:"230403",name:"工农区"},{code:"230404",name:"南山区"},{code:"230405",name:"兴安区"},{code:"230406",name:"东山区"},{code:"230407",name:"兴山区"},{code:"230421",name:"萝北县"},{code:"230422",name:"绥滨县"}]},{code:"2305",name:"双鸭山市",children:[{code:"230502",name:"尖山区"},{code:"230503",name:"岭东区"},{code:"230505",name:"四方台区"},{code:"230506",name:"宝山区"},{code:"230521",name:"集贤县"},{code:"230522",name:"友谊县"},{code:"230523",name:"宝清县"},{code:"230524",name:"饶河县"}]},{code:"2306",name:"大庆市",children:[{code:"230602",name:"萨尔图区"},{code:"230603",name:"龙凤区"},{code:"230604",name:"让胡路区"},{code:"230605",name:"红岗区"},{code:"230606",name:"大同区"},{code:"230621",name:"肇州县"},{code:"230622",name:"肇源县"},{code:"230623",name:"林甸县"},{code:"230624",name:"杜尔伯特蒙古族自治县"},{code:"230671",name:"大庆高新技术产业开发区"}]},{code:"2307",name:"伊春市",children:[{code:"230717",name:"伊美区"},{code:"230718",name:"乌翠区"},{code:"230719",name:"友好区"},{code:"230722",name:"嘉荫县"},{code:"230723",name:"汤旺县"},{code:"230724",name:"丰林县"},{code:"230725",name:"大箐山县"},{code:"230726",name:"南岔县"},{code:"230751",name:"金林区"},{code:"230781",name:"铁力市"}]},{code:"2308",name:"佳木斯市",children:[{code:"230803",name:"向阳区"},{code:"230804",name:"前进区"},{code:"230805",name:"东风区"},{code:"230811",name:"郊区"},{code:"230822",name:"桦南县"},{code:"230826",name:"桦川县"},{code:"230828",name:"汤原县"},{code:"230881",name:"同江市"},{code:"230882",name:"富锦市"},{code:"230883",name:"抚远市"}]},{code:"2309",name:"七台河市",children:[{code:"230902",name:"新兴区"},{code:"230903",name:"桃山区"},{code:"230904",name:"茄子河区"},{code:"230921",name:"勃利县"}]},{code:"2310",name:"牡丹江市",children:[{code:"231002",name:"东安区"},{code:"231003",name:"阳明区"},{code:"231004",name:"爱民区"},{code:"231005",name:"西安区"},{code:"231025",name:"林口县"},{code:"231071",name:"牡丹江经济技术开发区"},{code:"231081",name:"绥芬河市"},{code:"231083",name:"海林市"},{code:"231084",name:"宁安市"},{code:"231085",name:"穆棱市"},{code:"231086",name:"东宁市"}]},{code:"2311",name:"黑河市",children:[{code:"231102",name:"爱辉区"},{code:"231123",name:"逊克县"},{code:"231124",name:"孙吴县"},{code:"231181",name:"北安市"},{code:"231182",name:"五大连池市"},{code:"231183",name:"嫩江市"}]},{code:"2312",name:"绥化市",children:[{code:"231202",name:"北林区"},{code:"231221",name:"望奎县"},{code:"231222",name:"兰西县"},{code:"231223",name:"青冈县"},{code:"231224",name:"庆安县"},{code:"231225",name:"明水县"},{code:"231226",name:"绥棱县"},{code:"231281",name:"安达市"},{code:"231282",name:"肇东市"},{code:"231283",name:"海伦市"}]},{code:"2327",name:"大兴安岭地区",children:[{code:"232701",name:"漠河市"},{code:"232721",name:"呼玛县"},{code:"232722",name:"塔河县"},{code:"232761",name:"加格达奇区"},{code:"232762",name:"松岭区"},{code:"232763",name:"新林区"},{code:"232764",name:"呼中区"}]}]},{code:"31",name:"上海市",children:[{code:"3101",name:"市辖区",children:[{code:"310101",name:"黄浦区"},{code:"310104",name:"徐汇区"},{code:"310105",name:"长宁区"},{code:"310106",name:"静安区"},{code:"310107",name:"普陀区"},{code:"310109",name:"虹口区"},{code:"310110",name:"杨浦区"},{code:"310112",name:"闵行区"},{code:"310113",name:"宝山区"},{code:"310114",name:"嘉定区"},{code:"310115",name:"浦东新区"},{code:"310116",name:"金山区"},{code:"310117",name:"松江区"},{code:"310118",name:"青浦区"},{code:"310120",name:"奉贤区"},{code:"310151",name:"崇明区"}]}]},{code:"32",name:"江苏省",children:[{code:"3201",name:"南京市",children:[{code:"320102",name:"玄武区"},{code:"320104",name:"秦淮区"},{code:"320105",name:"建邺区"},{code:"320106",name:"鼓楼区"},{code:"320111",name:"浦口区"},{code:"320113",name:"栖霞区"},{code:"320114",name:"雨花台区"},{code:"320115",name:"江宁区"},{code:"320116",name:"六合区"},{code:"320117",name:"溧水区"},{code:"320118",name:"高淳区"}]},{code:"3202",name:"无锡市",children:[{code:"320205",name:"锡山区"},{code:"320206",name:"惠山区"},{code:"320211",name:"滨湖区"},{code:"320213",name:"梁溪区"},{code:"320214",name:"新吴区"},{code:"320281",name:"江阴市"},{code:"320282",name:"宜兴市"}]},{code:"3203",name:"徐州市",children:[{code:"320302",name:"鼓楼区"},{code:"320303",name:"云龙区"},{code:"320305",name:"贾汪区"},{code:"320311",name:"泉山区"},{code:"320312",name:"铜山区"},{code:"320321",name:"丰县"},{code:"320322",name:"沛县"},{code:"320324",name:"睢宁县"},{code:"320371",name:"徐州经济技术开发区"},{code:"320381",name:"新沂市"},{code:"320382",name:"邳州市"}]},{code:"3204",name:"常州市",children:[{code:"320402",name:"天宁区"},{code:"320404",name:"钟楼区"},{code:"320411",name:"新北区"},{code:"320412",name:"武进区"},{code:"320413",name:"金坛区"},{code:"320481",name:"溧阳市"}]},{code:"3205",name:"苏州市",children:[{code:"320505",name:"虎丘区"},{code:"320506",name:"吴中区"},{code:"320507",name:"相城区"},{code:"320508",name:"姑苏区"},{code:"320509",name:"吴江区"},{code:"320571",name:"苏州工业园区"},{code:"320581",name:"常熟市"},{code:"320582",name:"张家港市"},{code:"320583",name:"昆山市"},{code:"320585",name:"太仓市"}]},{code:"3206",name:"南通市",children:[{code:"320612",name:"通州区"},{code:"320613",name:"崇川区"},{code:"320614",name:"海门区"},{code:"320623",name:"如东县"},{code:"320671",name:"南通经济技术开发区"},{code:"320681",name:"启东市"},{code:"320682",name:"如皋市"},{code:"320685",name:"海安市"}]},{code:"3207",name:"连云港市",children:[{code:"320703",name:"连云区"},{code:"320706",name:"海州区"},{code:"320707",name:"赣榆区"},{code:"320722",name:"东海县"},{code:"320723",name:"灌云县"},{code:"320724",name:"灌南县"},{code:"320771",name:"连云港经济技术开发区"},{code:"320772",name:"连云港高新技术产业开发区"}]},{code:"3208",name:"淮安市",children:[{code:"320803",name:"淮安区"},{code:"320804",name:"淮阴区"},{code:"320812",name:"清江浦区"},{code:"320813",name:"洪泽区"},{code:"320826",name:"涟水县"},{code:"320830",name:"盱眙县"},{code:"320831",name:"金湖县"},{code:"320871",name:"淮安经济技术开发区"}]},{code:"3209",name:"盐城市",children:[{code:"320902",name:"亭湖区"},{code:"320903",name:"盐都区"},{code:"320904",name:"大丰区"},{code:"320921",name:"响水县"},{code:"320922",name:"滨海县"},{code:"320923",name:"阜宁县"},{code:"320924",name:"射阳县"},{code:"320925",name:"建湖县"},{code:"320971",name:"盐城经济技术开发区"},{code:"320981",name:"东台市"}]},{code:"3210",name:"扬州市",children:[{code:"321002",name:"广陵区"},{code:"321003",name:"邗江区"},{code:"321012",name:"江都区"},{code:"321023",name:"宝应县"},{code:"321071",name:"扬州经济技术开发区"},{code:"321081",name:"仪征市"},{code:"321084",name:"高邮市"}]},{code:"3211",name:"镇江市",children:[{code:"321102",name:"京口区"},{code:"321111",name:"润州区"},{code:"321112",name:"丹徒区"},{code:"321171",name:"镇江新区"},{code:"321181",name:"丹阳市"},{code:"321182",name:"扬中市"},{code:"321183",name:"句容市"}]},{code:"3212",name:"泰州市",children:[{code:"321202",name:"海陵区"},{code:"321203",name:"高港区"},{code:"321204",name:"姜堰区"},{code:"321271",name:"泰州医药高新技术产业开发区"},{code:"321281",name:"兴化市"},{code:"321282",name:"靖江市"},{code:"321283",name:"泰兴市"}]},{code:"3213",name:"宿迁市",children:[{code:"321302",name:"宿城区"},{code:"321311",name:"宿豫区"},{code:"321322",name:"沭阳县"},{code:"321323",name:"泗阳县"},{code:"321324",name:"泗洪县"},{code:"321371",name:"宿迁经济技术开发区"}]}]},{code:"33",name:"浙江省",children:[{code:"3301",name:"杭州市",children:[{code:"330102",name:"上城区"},{code:"330105",name:"拱墅区"},{code:"330106",name:"西湖区"},{code:"330108",name:"滨江区"},{code:"330109",name:"萧山区"},{code:"330110",name:"余杭区"},{code:"330111",name:"富阳区"},{code:"330112",name:"临安区"},{code:"330113",name:"临平区"},{code:"330114",name:"钱塘区"},{code:"330122",name:"桐庐县"},{code:"330127",name:"淳安县"},{code:"330182",name:"建德市"}]},{code:"3302",name:"宁波市",children:[{code:"330203",name:"海曙区"},{code:"330205",name:"江北区"},{code:"330206",name:"北仑区"},{code:"330211",name:"镇海区"},{code:"330212",name:"鄞州区"},{code:"330213",name:"奉化区"},{code:"330225",name:"象山县"},{code:"330226",name:"宁海县"},{code:"330281",name:"余姚市"},{code:"330282",name:"慈溪市"}]},{code:"3303",name:"温州市",children:[{code:"330302",name:"鹿城区"},{code:"330303",name:"龙湾区"},{code:"330304",name:"瓯海区"},{code:"330305",name:"洞头区"},{code:"330324",name:"永嘉县"},{code:"330326",name:"平阳县"},{code:"330327",name:"苍南县"},{code:"330328",name:"文成县"},{code:"330329",name:"泰顺县"},{code:"330371",name:"温州经济技术开发区"},{code:"330381",name:"瑞安市"},{code:"330382",name:"乐清市"},{code:"330383",name:"龙港市"}]},{code:"3304",name:"嘉兴市",children:[{code:"330402",name:"南湖区"},{code:"330411",name:"秀洲区"},{code:"330421",name:"嘉善县"},{code:"330424",name:"海盐县"},{code:"330481",name:"海宁市"},{code:"330482",name:"平湖市"},{code:"330483",name:"桐乡市"}]},{code:"3305",name:"湖州市",children:[{code:"330502",name:"吴兴区"},{code:"330503",name:"南浔区"},{code:"330521",name:"德清县"},{code:"330522",name:"长兴县"},{code:"330523",name:"安吉县"}]},{code:"3306",name:"绍兴市",children:[{code:"330602",name:"越城区"},{code:"330603",name:"柯桥区"},{code:"330604",name:"上虞区"},{code:"330624",name:"新昌县"},{code:"330681",name:"诸暨市"},{code:"330683",name:"嵊州市"}]},{code:"3307",name:"金华市",children:[{code:"330702",name:"婺城区"},{code:"330703",name:"金东区"},{code:"330723",name:"武义县"},{code:"330726",name:"浦江县"},{code:"330727",name:"磐安县"},{code:"330781",name:"兰溪市"},{code:"330782",name:"义乌市"},{code:"330783",name:"东阳市"},{code:"330784",name:"永康市"}]},{code:"3308",name:"衢州市",children:[{code:"330802",name:"柯城区"},{code:"330803",name:"衢江区"},{code:"330822",name:"常山县"},{code:"330824",name:"开化县"},{code:"330825",name:"龙游县"},{code:"330881",name:"江山市"}]},{code:"3309",name:"舟山市",children:[{code:"330902",name:"定海区"},{code:"330903",name:"普陀区"},{code:"330921",name:"岱山县"},{code:"330922",name:"嵊泗县"}]},{code:"3310",name:"台州市",children:[{code:"331002",name:"椒江区"},{code:"331003",name:"黄岩区"},{code:"331004",name:"路桥区"},{code:"331022",name:"三门县"},{code:"331023",name:"天台县"},{code:"331024",name:"仙居县"},{code:"331081",name:"温岭市"},{code:"331082",name:"临海市"},{code:"331083",name:"玉环市"}]},{code:"3311",name:"丽水市",children:[{code:"331102",name:"莲都区"},{code:"331121",name:"青田县"},{code:"331122",name:"缙云县"},{code:"331123",name:"遂昌县"},{code:"331124",name:"松阳县"},{code:"331125",name:"云和县"},{code:"331126",name:"庆元县"},{code:"331127",name:"景宁畲族自治县"},{code:"331181",name:"龙泉市"}]}]},{code:"34",name:"安徽省",children:[{code:"3401",name:"合肥市",children:[{code:"340102",name:"瑶海区"},{code:"340103",name:"庐阳区"},{code:"340104",name:"蜀山区"},{code:"340111",name:"包河区"},{code:"340121",name:"长丰县"},{code:"340122",name:"肥东县"},{code:"340123",name:"肥西县"},{code:"340124",name:"庐江县"},{code:"340171",name:"合肥高新技术产业开发区"},{code:"340172",name:"合肥经济技术开发区"},{code:"340173",name:"合肥新站高新技术产业开发区"},{code:"340181",name:"巢湖市"}]},{code:"3402",name:"芜湖市",children:[{code:"340202",name:"镜湖区"},{code:"340207",name:"鸠江区"},{code:"340209",name:"弋江区"},{code:"340210",name:"湾沚区"},{code:"340212",name:"繁昌区"},{code:"340223",name:"南陵县"},{code:"340271",name:"芜湖经济技术开发区"},{code:"340272",name:"安徽芜湖三山经济开发区"},{code:"340281",name:"无为市"}]},{code:"3403",name:"蚌埠市",children:[{code:"340302",name:"龙子湖区"},{code:"340303",name:"蚌山区"},{code:"340304",name:"禹会区"},{code:"340311",name:"淮上区"},{code:"340321",name:"怀远县"},{code:"340322",name:"五河县"},{code:"340323",name:"固镇县"},{code:"340371",name:"蚌埠市高新技术开发区"},{code:"340372",name:"蚌埠市经济开发区"}]},{code:"3404",name:"淮南市",children:[{code:"340402",name:"大通区"},{code:"340403",name:"田家庵区"},{code:"340404",name:"谢家集区"},{code:"340405",name:"八公山区"},{code:"340406",name:"潘集区"},{code:"340421",name:"凤台县"},{code:"340422",name:"寿县"}]},{code:"3405",name:"马鞍山市",children:[{code:"340503",name:"花山区"},{code:"340504",name:"雨山区"},{code:"340506",name:"博望区"},{code:"340521",name:"当涂县"},{code:"340522",name:"含山县"},{code:"340523",name:"和县"}]},{code:"3406",name:"淮北市",children:[{code:"340602",name:"杜集区"},{code:"340603",name:"相山区"},{code:"340604",name:"烈山区"},{code:"340621",name:"濉溪县"}]},{code:"3407",name:"铜陵市",children:[{code:"340705",name:"铜官区"},{code:"340706",name:"义安区"},{code:"340711",name:"郊区"},{code:"340722",name:"枞阳县"}]},{code:"3408",name:"安庆市",children:[{code:"340802",name:"迎江区"},{code:"340803",name:"大观区"},{code:"340811",name:"宜秀区"},{code:"340822",name:"怀宁县"},{code:"340825",name:"太湖县"},{code:"340826",name:"宿松县"},{code:"340827",name:"望江县"},{code:"340828",name:"岳西县"},{code:"340871",name:"安徽安庆经济开发区"},{code:"340881",name:"桐城市"},{code:"340882",name:"潜山市"}]},{code:"3410",name:"黄山市",children:[{code:"341002",name:"屯溪区"},{code:"341003",name:"黄山区"},{code:"341004",name:"徽州区"},{code:"341021",name:"歙县"},{code:"341022",name:"休宁县"},{code:"341023",name:"黟县"},{code:"341024",name:"祁门县"}]},{code:"3411",name:"滁州市",children:[{code:"341102",name:"琅琊区"},{code:"341103",name:"南谯区"},{code:"341122",name:"来安县"},{code:"341124",name:"全椒县"},{code:"341125",name:"定远县"},{code:"341126",name:"凤阳县"},{code:"341171",name:"中新苏滁高新技术产业开发区"},{code:"341172",name:"滁州经济技术开发区"},{code:"341181",name:"天长市"},{code:"341182",name:"明光市"}]},{code:"3412",name:"阜阳市",children:[{code:"341202",name:"颍州区"},{code:"341203",name:"颍东区"},{code:"341204",name:"颍泉区"},{code:"341221",name:"临泉县"},{code:"341222",name:"太和县"},{code:"341225",name:"阜南县"},{code:"341226",name:"颍上县"},{code:"341271",name:"阜阳合肥现代产业园区"},{code:"341272",name:"阜阳经济技术开发区"},{code:"341282",name:"界首市"}]},{code:"3413",name:"宿州市",children:[{code:"341302",name:"埇桥区"},{code:"341321",name:"砀山县"},{code:"341322",name:"萧县"},{code:"341323",name:"灵璧县"},{code:"341324",name:"泗县"},{code:"341371",name:"宿州马鞍山现代产业园区"},{code:"341372",name:"宿州经济技术开发区"}]},{code:"3415",name:"六安市",children:[{code:"341502",name:"金安区"},{code:"341503",name:"裕安区"},{code:"341504",name:"叶集区"},{code:"341522",name:"霍邱县"},{code:"341523",name:"舒城县"},{code:"341524",name:"金寨县"},{code:"341525",name:"霍山县"}]},{code:"3416",name:"亳州市",children:[{code:"341602",name:"谯城区"},{code:"341621",name:"涡阳县"},{code:"341622",name:"蒙城县"},{code:"341623",name:"利辛县"}]},{code:"3417",name:"池州市",children:[{code:"341702",name:"贵池区"},{code:"341721",name:"东至县"},{code:"341722",name:"石台县"},{code:"341723",name:"青阳县"}]},{code:"3418",name:"宣城市",children:[{code:"341802",name:"宣州区"},{code:"341821",name:"郎溪县"},{code:"341823",name:"泾县"},{code:"341824",name:"绩溪县"},{code:"341825",name:"旌德县"},{code:"341871",name:"宣城市经济开发区"},{code:"341881",name:"宁国市"},{code:"341882",name:"广德市"}]}]},{code:"35",name:"福建省",children:[{code:"3501",name:"福州市",children:[{code:"350102",name:"鼓楼区"},{code:"350103",name:"台江区"},{code:"350104",name:"仓山区"},{code:"350105",name:"马尾区"},{code:"350111",name:"晋安区"},{code:"350112",name:"长乐区"},{code:"350121",name:"闽侯县"},{code:"350122",name:"连江县"},{code:"350123",name:"罗源县"},{code:"350124",name:"闽清县"},{code:"350125",name:"永泰县"},{code:"350128",name:"平潭县"},{code:"350181",name:"福清市"}]},{code:"3502",name:"厦门市",children:[{code:"350203",name:"思明区"},{code:"350205",name:"海沧区"},{code:"350206",name:"湖里区"},{code:"350211",name:"集美区"},{code:"350212",name:"同安区"},{code:"350213",name:"翔安区"}]},{code:"3503",name:"莆田市",children:[{code:"350302",name:"城厢区"},{code:"350303",name:"涵江区"},{code:"350304",name:"荔城区"},{code:"350305",name:"秀屿区"},{code:"350322",name:"仙游县"}]},{code:"3504",name:"三明市",children:[{code:"350404",name:"三元区"},{code:"350405",name:"沙县区"},{code:"350421",name:"明溪县"},{code:"350423",name:"清流县"},{code:"350424",name:"宁化县"},{code:"350425",name:"大田县"},{code:"350426",name:"尤溪县"},{code:"350428",name:"将乐县"},{code:"350429",name:"泰宁县"},{code:"350430",name:"建宁县"},{code:"350481",name:"永安市"}]},{code:"3505",name:"泉州市",children:[{code:"350502",name:"鲤城区"},{code:"350503",name:"丰泽区"},{code:"350504",name:"洛江区"},{code:"350505",name:"泉港区"},{code:"350521",name:"惠安县"},{code:"350524",name:"安溪县"},{code:"350525",name:"永春县"},{code:"350526",name:"德化县"},{code:"350527",name:"金门县"},{code:"350581",name:"石狮市"},{code:"350582",name:"晋江市"},{code:"350583",name:"南安市"}]},{code:"3506",name:"漳州市",children:[{code:"350602",name:"芗城区"},{code:"350603",name:"龙文区"},{code:"350604",name:"龙海区"},{code:"350605",name:"长泰区"},{code:"350622",name:"云霄县"},{code:"350623",name:"漳浦县"},{code:"350624",name:"诏安县"},{code:"350626",name:"东山县"},{code:"350627",name:"南靖县"},{code:"350628",name:"平和县"},{code:"350629",name:"华安县"}]},{code:"3507",name:"南平市",children:[{code:"350702",name:"延平区"},{code:"350703",name:"建阳区"},{code:"350721",name:"顺昌县"},{code:"350722",name:"浦城县"},{code:"350723",name:"光泽县"},{code:"350724",name:"松溪县"},{code:"350725",name:"政和县"},{code:"350781",name:"邵武市"},{code:"350782",name:"武夷山市"},{code:"350783",name:"建瓯市"}]},{code:"3508",name:"龙岩市",children:[{code:"350802",name:"新罗区"},{code:"350803",name:"永定区"},{code:"350821",name:"长汀县"},{code:"350823",name:"上杭县"},{code:"350824",name:"武平县"},{code:"350825",name:"连城县"},{code:"350881",name:"漳平市"}]},{code:"3509",name:"宁德市",children:[{code:"350902",name:"蕉城区"},{code:"350921",name:"霞浦县"},{code:"350922",name:"古田县"},{code:"350923",name:"屏南县"},{code:"350924",name:"寿宁县"},{code:"350925",name:"周宁县"},{code:"350926",name:"柘荣县"},{code:"350981",name:"福安市"},{code:"350982",name:"福鼎市"}]}]},{code:"36",name:"江西省",children:[{code:"3601",name:"南昌市",children:[{code:"360102",name:"东湖区"},{code:"360103",name:"西湖区"},{code:"360104",name:"青云谱区"},{code:"360111",name:"青山湖区"},{code:"360112",name:"新建区"},{code:"360113",name:"红谷滩区"},{code:"360121",name:"南昌县"},{code:"360123",name:"安义县"},{code:"360124",name:"进贤县"}]},{code:"3602",name:"景德镇市",children:[{code:"360202",name:"昌江区"},{code:"360203",name:"珠山区"},{code:"360222",name:"浮梁县"},{code:"360281",name:"乐平市"}]},{code:"3603",name:"萍乡市",children:[{code:"360302",name:"安源区"},{code:"360313",name:"湘东区"},{code:"360321",name:"莲花县"},{code:"360322",name:"上栗县"},{code:"360323",name:"芦溪县"}]},{code:"3604",name:"九江市",children:[{code:"360402",name:"濂溪区"},{code:"360403",name:"浔阳区"},{code:"360404",name:"柴桑区"},{code:"360423",name:"武宁县"},{code:"360424",name:"修水县"},{code:"360425",name:"永修县"},{code:"360426",name:"德安县"},{code:"360428",name:"都昌县"},{code:"360429",name:"湖口县"},{code:"360430",name:"彭泽县"},{code:"360481",name:"瑞昌市"},{code:"360482",name:"共青城市"},{code:"360483",name:"庐山市"}]},{code:"3605",name:"新余市",children:[{code:"360502",name:"渝水区"},{code:"360521",name:"分宜县"}]},{code:"3606",name:"鹰潭市",children:[{code:"360602",name:"月湖区"},{code:"360603",name:"余江区"},{code:"360681",name:"贵溪市"}]},{code:"3607",name:"赣州市",children:[{code:"360702",name:"章贡区"},{code:"360703",name:"南康区"},{code:"360704",name:"赣县区"},{code:"360722",name:"信丰县"},{code:"360723",name:"大余县"},{code:"360724",name:"上犹县"},{code:"360725",name:"崇义县"},{code:"360726",name:"安远县"},{code:"360728",name:"定南县"},{code:"360729",name:"全南县"},{code:"360730",name:"宁都县"},{code:"360731",name:"于都县"},{code:"360732",name:"兴国县"},{code:"360733",name:"会昌县"},{code:"360734",name:"寻乌县"},{code:"360735",name:"石城县"},{code:"360781",name:"瑞金市"},{code:"360783",name:"龙南市"}]},{code:"3608",name:"吉安市",children:[{code:"360802",name:"吉州区"},{code:"360803",name:"青原区"},{code:"360821",name:"吉安县"},{code:"360822",name:"吉水县"},{code:"360823",name:"峡江县"},{code:"360824",name:"新干县"},{code:"360825",name:"永丰县"},{code:"360826",name:"泰和县"},{code:"360827",name:"遂川县"},{code:"360828",name:"万安县"},{code:"360829",name:"安福县"},{code:"360830",name:"永新县"},{code:"360881",name:"井冈山市"}]},{code:"3609",name:"宜春市",children:[{code:"360902",name:"袁州区"},{code:"360921",name:"奉新县"},{code:"360922",name:"万载县"},{code:"360923",name:"上高县"},{code:"360924",name:"宜丰县"},{code:"360925",name:"靖安县"},{code:"360926",name:"铜鼓县"},{code:"360981",name:"丰城市"},{code:"360982",name:"樟树市"},{code:"360983",name:"高安市"}]},{code:"3610",name:"抚州市",children:[{code:"361002",name:"临川区"},{code:"361003",name:"东乡区"},{code:"361021",name:"南城县"},{code:"361022",name:"黎川县"},{code:"361023",name:"南丰县"},{code:"361024",name:"崇仁县"},{code:"361025",name:"乐安县"},{code:"361026",name:"宜黄县"},{code:"361027",name:"金溪县"},{code:"361028",name:"资溪县"},{code:"361030",name:"广昌县"}]},{code:"3611",name:"上饶市",children:[{code:"361102",name:"信州区"},{code:"361103",name:"广丰区"},{code:"361104",name:"广信区"},{code:"361123",name:"玉山县"},{code:"361124",name:"铅山县"},{code:"361125",name:"横峰县"},{code:"361126",name:"弋阳县"},{code:"361127",name:"余干县"},{code:"361128",name:"鄱阳县"},{code:"361129",name:"万年县"},{code:"361130",name:"婺源县"},{code:"361181",name:"德兴市"}]}]},{code:"37",name:"山东省",children:[{code:"3701",name:"济南市",children:[{code:"370102",name:"历下区"},{code:"370103",name:"市中区"},{code:"370104",name:"槐荫区"},{code:"370105",name:"天桥区"},{code:"370112",name:"历城区"},{code:"370113",name:"长清区"},{code:"370114",name:"章丘区"},{code:"370115",name:"济阳区"},{code:"370116",name:"莱芜区"},{code:"370117",name:"钢城区"},{code:"370124",name:"平阴县"},{code:"370126",name:"商河县"},{code:"370171",name:"济南高新技术产业开发区"}]},{code:"3702",name:"青岛市",children:[{code:"370202",name:"市南区"},{code:"370203",name:"市北区"},{code:"370211",name:"黄岛区"},{code:"370212",name:"崂山区"},{code:"370213",name:"李沧区"},{code:"370214",name:"城阳区"},{code:"370215",name:"即墨区"},{code:"370271",name:"青岛高新技术产业开发区"},{code:"370281",name:"胶州市"},{code:"370283",name:"平度市"},{code:"370285",name:"莱西市"}]},{code:"3703",name:"淄博市",children:[{code:"370302",name:"淄川区"},{code:"370303",name:"张店区"},{code:"370304",name:"博山区"},{code:"370305",name:"临淄区"},{code:"370306",name:"周村区"},{code:"370321",name:"桓台县"},{code:"370322",name:"高青县"},{code:"370323",name:"沂源县"}]},{code:"3704",name:"枣庄市",children:[{code:"370402",name:"市中区"},{code:"370403",name:"薛城区"},{code:"370404",name:"峄城区"},{code:"370405",name:"台儿庄区"},{code:"370406",name:"山亭区"},{code:"370481",name:"滕州市"}]},{code:"3705",name:"东营市",children:[{code:"370502",name:"东营区"},{code:"370503",name:"河口区"},{code:"370505",name:"垦利区"},{code:"370522",name:"利津县"},{code:"370523",name:"广饶县"},{code:"370571",name:"东营经济技术开发区"},{code:"370572",name:"东营港经济开发区"}]},{code:"3706",name:"烟台市",children:[{code:"370602",name:"芝罘区"},{code:"370611",name:"福山区"},{code:"370612",name:"牟平区"},{code:"370613",name:"莱山区"},{code:"370614",name:"蓬莱区"},{code:"370671",name:"烟台高新技术产业开发区"},{code:"370672",name:"烟台经济技术开发区"},{code:"370681",name:"龙口市"},{code:"370682",name:"莱阳市"},{code:"370683",name:"莱州市"},{code:"370685",name:"招远市"},{code:"370686",name:"栖霞市"},{code:"370687",name:"海阳市"}]},{code:"3707",name:"潍坊市",children:[{code:"370702",name:"潍城区"},{code:"370703",name:"寒亭区"},{code:"370704",name:"坊子区"},{code:"370705",name:"奎文区"},{code:"370724",name:"临朐县"},{code:"370725",name:"昌乐县"},{code:"370772",name:"潍坊滨海经济技术开发区"},{code:"370781",name:"青州市"},{code:"370782",name:"诸城市"},{code:"370783",name:"寿光市"},{code:"370784",name:"安丘市"},{code:"370785",name:"高密市"},{code:"370786",name:"昌邑市"}]},{code:"3708",name:"济宁市",children:[{code:"370811",name:"任城区"},{code:"370812",name:"兖州区"},{code:"370826",name:"微山县"},{code:"370827",name:"鱼台县"},{code:"370828",name:"金乡县"},{code:"370829",name:"嘉祥县"},{code:"370830",name:"汶上县"},{code:"370831",name:"泗水县"},{code:"370832",name:"梁山县"},{code:"370871",name:"济宁高新技术产业开发区"},{code:"370881",name:"曲阜市"},{code:"370883",name:"邹城市"}]},{code:"3709",name:"泰安市",children:[{code:"370902",name:"泰山区"},{code:"370911",name:"岱岳区"},{code:"370921",name:"宁阳县"},{code:"370923",name:"东平县"},{code:"370982",name:"新泰市"},{code:"370983",name:"肥城市"}]},{code:"3710",name:"威海市",children:[{code:"371002",name:"环翠区"},{code:"371003",name:"文登区"},{code:"371071",name:"威海火炬高技术产业开发区"},{code:"371072",name:"威海经济技术开发区"},{code:"371073",name:"威海临港经济技术开发区"},{code:"371082",name:"荣成市"},{code:"371083",name:"乳山市"}]},{code:"3711",name:"日照市",children:[{code:"371102",name:"东港区"},{code:"371103",name:"岚山区"},{code:"371121",name:"五莲县"},{code:"371122",name:"莒县"},{code:"371171",name:"日照经济技术开发区"}]},{code:"3713",name:"临沂市",children:[{code:"371302",name:"兰山区"},{code:"371311",name:"罗庄区"},{code:"371312",name:"河东区"},{code:"371321",name:"沂南县"},{code:"371322",name:"郯城县"},{code:"371323",name:"沂水县"},{code:"371324",name:"兰陵县"},{code:"371325",name:"费县"},{code:"371326",name:"平邑县"},{code:"371327",name:"莒南县"},{code:"371328",name:"蒙阴县"},{code:"371329",name:"临沭县"},{code:"371371",name:"临沂高新技术产业开发区"}]},{code:"3714",name:"德州市",children:[{code:"371402",name:"德城区"},{code:"371403",name:"陵城区"},{code:"371422",name:"宁津县"},{code:"371423",name:"庆云县"},{code:"371424",name:"临邑县"},{code:"371425",name:"齐河县"},{code:"371426",name:"平原县"},{code:"371427",name:"夏津县"},{code:"371428",name:"武城县"},{code:"371471",name:"德州经济技术开发区"},{code:"371472",name:"德州运河经济开发区"},{code:"371481",name:"乐陵市"},{code:"371482",name:"禹城市"}]},{code:"3715",name:"聊城市",children:[{code:"371502",name:"东昌府区"},{code:"371503",name:"茌平区"},{code:"371521",name:"阳谷县"},{code:"371522",name:"莘县"},{code:"371524",name:"东阿县"},{code:"371525",name:"冠县"},{code:"371526",name:"高唐县"},{code:"371581",name:"临清市"}]},{code:"3716",name:"滨州市",children:[{code:"371602",name:"滨城区"},{code:"371603",name:"沾化区"},{code:"371621",name:"惠民县"},{code:"371622",name:"阳信县"},{code:"371623",name:"无棣县"},{code:"371625",name:"博兴县"},{code:"371681",name:"邹平市"}]},{code:"3717",name:"菏泽市",children:[{code:"371702",name:"牡丹区"},{code:"371703",name:"定陶区"},{code:"371721",name:"曹县"},{code:"371722",name:"单县"},{code:"371723",name:"成武县"},{code:"371724",name:"巨野县"},{code:"371725",name:"郓城县"},{code:"371726",name:"鄄城县"},{code:"371728",name:"东明县"},{code:"371771",name:"菏泽经济技术开发区"},{code:"371772",name:"菏泽高新技术开发区"}]}]},{code:"41",name:"河南省",children:[{code:"4101",name:"郑州市",children:[{code:"410102",name:"中原区"},{code:"410103",name:"二七区"},{code:"410104",name:"管城回族区"},{code:"410105",name:"金水区"},{code:"410106",name:"上街区"},{code:"410108",name:"惠济区"},{code:"410122",name:"中牟县"},{code:"410171",name:"郑州经济技术开发区"},{code:"410172",name:"郑州高新技术产业开发区"},{code:"410173",name:"郑州航空港经济综合实验区"},{code:"410181",name:"巩义市"},{code:"410182",name:"荥阳市"},{code:"410183",name:"新密市"},{code:"410184",name:"新郑市"},{code:"410185",name:"登封市"}]},{code:"4102",name:"开封市",children:[{code:"410202",name:"龙亭区"},{code:"410203",name:"顺河回族区"},{code:"410204",name:"鼓楼区"},{code:"410205",name:"禹王台区"},{code:"410212",name:"祥符区"},{code:"410221",name:"杞县"},{code:"410222",name:"通许县"},{code:"410223",name:"尉氏县"},{code:"410225",name:"兰考县"}]},{code:"4103",name:"洛阳市",children:[{code:"410302",name:"老城区"},{code:"410303",name:"西工区"},{code:"410304",name:"瀍河回族区"},{code:"410305",name:"涧西区"},{code:"410307",name:"偃师区"},{code:"410308",name:"孟津区"},{code:"410311",name:"洛龙区"},{code:"410323",name:"新安县"},{code:"410324",name:"栾川县"},{code:"410325",name:"嵩县"},{code:"410326",name:"汝阳县"},{code:"410327",name:"宜阳县"},{code:"410328",name:"洛宁县"},{code:"410329",name:"伊川县"},{code:"410371",name:"洛阳高新技术产业开发区"}]},{code:"4104",name:"平顶山市",children:[{code:"410402",name:"新华区"},{code:"410403",name:"卫东区"},{code:"410404",name:"石龙区"},{code:"410411",name:"湛河区"},{code:"410421",name:"宝丰县"},{code:"410422",name:"叶县"},{code:"410423",name:"鲁山县"},{code:"410425",name:"郏县"},{code:"410471",name:"平顶山高新技术产业开发区"},{code:"410472",name:"平顶山市城乡一体化示范区"},{code:"410481",name:"舞钢市"},{code:"410482",name:"汝州市"}]},{code:"4105",name:"安阳市",children:[{code:"410502",name:"文峰区"},{code:"410503",name:"北关区"},{code:"410505",name:"殷都区"},{code:"410506",name:"龙安区"},{code:"410522",name:"安阳县"},{code:"410523",name:"汤阴县"},{code:"410526",name:"滑县"},{code:"410527",name:"内黄县"},{code:"410571",name:"安阳高新技术产业开发区"},{code:"410581",name:"林州市"}]},{code:"4106",name:"鹤壁市",children:[{code:"410602",name:"鹤山区"},{code:"410603",name:"山城区"},{code:"410611",name:"淇滨区"},{code:"410621",name:"浚县"},{code:"410622",name:"淇县"},{code:"410671",name:"鹤壁经济技术开发区"}]},{code:"4107",name:"新乡市",children:[{code:"410702",name:"红旗区"},{code:"410703",name:"卫滨区"},{code:"410704",name:"凤泉区"},{code:"410711",name:"牧野区"},{code:"410721",name:"新乡县"},{code:"410724",name:"获嘉县"},{code:"410725",name:"原阳县"},{code:"410726",name:"延津县"},{code:"410727",name:"封丘县"},{code:"410771",name:"新乡高新技术产业开发区"},{code:"410772",name:"新乡经济技术开发区"},{code:"410773",name:"新乡市平原城乡一体化示范区"},{code:"410781",name:"卫辉市"},{code:"410782",name:"辉县市"},{code:"410783",name:"长垣市"}]},{code:"4108",name:"焦作市",children:[{code:"410802",name:"解放区"},{code:"410803",name:"中站区"},{code:"410804",name:"马村区"},{code:"410811",name:"山阳区"},{code:"410821",name:"修武县"},{code:"410822",name:"博爱县"},{code:"410823",name:"武陟县"},{code:"410825",name:"温县"},{code:"410871",name:"焦作城乡一体化示范区"},{code:"410882",name:"沁阳市"},{code:"410883",name:"孟州市"}]},{code:"4109",name:"濮阳市",children:[{code:"410902",name:"华龙区"},{code:"410922",name:"清丰县"},{code:"410923",name:"南乐县"},{code:"410926",name:"范县"},{code:"410927",name:"台前县"},{code:"410928",name:"濮阳县"},{code:"410971",name:"河南濮阳工业园区"},{code:"410972",name:"濮阳经济技术开发区"}]},{code:"4110",name:"许昌市",children:[{code:"411002",name:"魏都区"},{code:"411003",name:"建安区"},{code:"411024",name:"鄢陵县"},{code:"411025",name:"襄城县"},{code:"411071",name:"许昌经济技术开发区"},{code:"411081",name:"禹州市"},{code:"411082",name:"长葛市"}]},{code:"4111",name:"漯河市",children:[{code:"411102",name:"源汇区"},{code:"411103",name:"郾城区"},{code:"411104",name:"召陵区"},{code:"411121",name:"舞阳县"},{code:"411122",name:"临颍县"},{code:"411171",name:"漯河经济技术开发区"}]},{code:"4112",name:"三门峡市",children:[{code:"411202",name:"湖滨区"},{code:"411203",name:"陕州区"},{code:"411221",name:"渑池县"},{code:"411224",name:"卢氏县"},{code:"411271",name:"河南三门峡经济开发区"},{code:"411281",name:"义马市"},{code:"411282",name:"灵宝市"}]},{code:"4113",name:"南阳市",children:[{code:"411302",name:"宛城区"},{code:"411303",name:"卧龙区"},{code:"411321",name:"南召县"},{code:"411322",name:"方城县"},{code:"411323",name:"西峡县"},{code:"411324",name:"镇平县"},{code:"411325",name:"内乡县"},{code:"411326",name:"淅川县"},{code:"411327",name:"社旗县"},{code:"411328",name:"唐河县"},{code:"411329",name:"新野县"},{code:"411330",name:"桐柏县"},{code:"411371",name:"南阳高新技术产业开发区"},{code:"411372",name:"南阳市城乡一体化示范区"},{code:"411381",name:"邓州市"}]},{code:"4114",name:"商丘市",children:[{code:"411402",name:"梁园区"},{code:"411403",name:"睢阳区"},{code:"411421",name:"民权县"},{code:"411422",name:"睢县"},{code:"411423",name:"宁陵县"},{code:"411424",name:"柘城县"},{code:"411425",name:"虞城县"},{code:"411426",name:"夏邑县"},{code:"411471",name:"豫东综合物流产业聚集区"},{code:"411472",name:"河南商丘经济开发区"},{code:"411481",name:"永城市"}]},{code:"4115",name:"信阳市",children:[{code:"411502",name:"浉河区"},{code:"411503",name:"平桥区"},{code:"411521",name:"罗山县"},{code:"411522",name:"光山县"},{code:"411523",name:"新县"},{code:"411524",name:"商城县"},{code:"411525",name:"固始县"},{code:"411526",name:"潢川县"},{code:"411527",name:"淮滨县"},{code:"411528",name:"息县"},{code:"411571",name:"信阳高新技术产业开发区"}]},{code:"4116",name:"周口市",children:[{code:"411602",name:"川汇区"},{code:"411603",name:"淮阳区"},{code:"411621",name:"扶沟县"},{code:"411622",name:"西华县"},{code:"411623",name:"商水县"},{code:"411624",name:"沈丘县"},{code:"411625",name:"郸城县"},{code:"411627",name:"太康县"},{code:"411628",name:"鹿邑县"},{code:"411671",name:"河南周口经济开发区"},{code:"411681",name:"项城市"}]},{code:"4117",name:"驻马店市",children:[{code:"411702",name:"驿城区"},{code:"411721",name:"西平县"},{code:"411722",name:"上蔡县"},{code:"411723",name:"平舆县"},{code:"411724",name:"正阳县"},{code:"411725",name:"确山县"},{code:"411726",name:"泌阳县"},{code:"411727",name:"汝南县"},{code:"411728",name:"遂平县"},{code:"411729",name:"新蔡县"},{code:"411771",name:"河南驻马店经济开发区"}]},{code:"4190",name:"省直辖县级行政区划",children:[{code:"419001",name:"济源市"}]}]},{code:"42",name:"湖北省",children:[{code:"4201",name:"武汉市",children:[{code:"420102",name:"江岸区"},{code:"420103",name:"江汉区"},{code:"420104",name:"硚口区"},{code:"420105",name:"汉阳区"},{code:"420106",name:"武昌区"},{code:"420107",name:"青山区"},{code:"420111",name:"洪山区"},{code:"420112",name:"东西湖区"},{code:"420113",name:"汉南区"},{code:"420114",name:"蔡甸区"},{code:"420115",name:"江夏区"},{code:"420116",name:"黄陂区"},{code:"420117",name:"新洲区"}]},{code:"4202",name:"黄石市",children:[{code:"420202",name:"黄石港区"},{code:"420203",name:"西塞山区"},{code:"420204",name:"下陆区"},{code:"420205",name:"铁山区"},{code:"420222",name:"阳新县"},{code:"420281",name:"大冶市"}]},{code:"4203",name:"十堰市",children:[{code:"420302",name:"茅箭区"},{code:"420303",name:"张湾区"},{code:"420304",name:"郧阳区"},{code:"420322",name:"郧西县"},{code:"420323",name:"竹山县"},{code:"420324",name:"竹溪县"},{code:"420325",name:"房县"},{code:"420381",name:"丹江口市"}]},{code:"4205",name:"宜昌市",children:[{code:"420502",name:"西陵区"},{code:"420503",name:"伍家岗区"},{code:"420504",name:"点军区"},{code:"420505",name:"猇亭区"},{code:"420506",name:"夷陵区"},{code:"420525",name:"远安县"},{code:"420526",name:"兴山县"},{code:"420527",name:"秭归县"},{code:"420528",name:"长阳土家族自治县"},{code:"420529",name:"五峰土家族自治县"},{code:"420581",name:"宜都市"},{code:"420582",name:"当阳市"},{code:"420583",name:"枝江市"}]},{code:"4206",name:"襄阳市",children:[{code:"420602",name:"襄城区"},{code:"420606",name:"樊城区"},{code:"420607",name:"襄州区"},{code:"420624",name:"南漳县"},{code:"420625",name:"谷城县"},{code:"420626",name:"保康县"},{code:"420682",name:"老河口市"},{code:"420683",name:"枣阳市"},{code:"420684",name:"宜城市"}]},{code:"4207",name:"鄂州市",children:[{code:"420702",name:"梁子湖区"},{code:"420703",name:"华容区"},{code:"420704",name:"鄂城区"}]},{code:"4208",name:"荆门市",children:[{code:"420802",name:"东宝区"},{code:"420804",name:"掇刀区"},{code:"420822",name:"沙洋县"},{code:"420881",name:"钟祥市"},{code:"420882",name:"京山市"}]},{code:"4209",name:"孝感市",children:[{code:"420902",name:"孝南区"},{code:"420921",name:"孝昌县"},{code:"420922",name:"大悟县"},{code:"420923",name:"云梦县"},{code:"420981",name:"应城市"},{code:"420982",name:"安陆市"},{code:"420984",name:"汉川市"}]},{code:"4210",name:"荆州市",children:[{code:"421002",name:"沙市区"},{code:"421003",name:"荆州区"},{code:"421022",name:"公安县"},{code:"421024",name:"江陵县"},{code:"421071",name:"荆州经济技术开发区"},{code:"421081",name:"石首市"},{code:"421083",name:"洪湖市"},{code:"421087",name:"松滋市"},{code:"421088",name:"监利市"}]},{code:"4211",name:"黄冈市",children:[{code:"421102",name:"黄州区"},{code:"421121",name:"团风县"},{code:"421122",name:"红安县"},{code:"421123",name:"罗田县"},{code:"421124",name:"英山县"},{code:"421125",name:"浠水县"},{code:"421126",name:"蕲春县"},{code:"421127",name:"黄梅县"},{code:"421171",name:"龙感湖管理区"},{code:"421181",name:"麻城市"},{code:"421182",name:"武穴市"}]},{code:"4212",name:"咸宁市",children:[{code:"421202",name:"咸安区"},{code:"421221",name:"嘉鱼县"},{code:"421222",name:"通城县"},{code:"421223",name:"崇阳县"},{code:"421224",name:"通山县"},{code:"421281",name:"赤壁市"}]},{code:"4213",name:"随州市",children:[{code:"421303",name:"曾都区"},{code:"421321",name:"随县"},{code:"421381",name:"广水市"}]},{code:"4228",name:"恩施土家族苗族自治州",children:[{code:"422801",name:"恩施市"},{code:"422802",name:"利川市"},{code:"422822",name:"建始县"},{code:"422823",name:"巴东县"},{code:"422825",name:"宣恩县"},{code:"422826",name:"咸丰县"},{code:"422827",name:"来凤县"},{code:"422828",name:"鹤峰县"}]},{code:"4290",name:"省直辖县级行政区划",children:[{code:"429004",name:"仙桃市"},{code:"429005",name:"潜江市"},{code:"429006",name:"天门市"},{code:"429021",name:"神农架林区"}]}]},{code:"43",name:"湖南省",children:[{code:"4301",name:"长沙市",children:[{code:"430102",name:"芙蓉区"},{code:"430103",name:"天心区"},{code:"430104",name:"岳麓区"},{code:"430105",name:"开福区"},{code:"430111",name:"雨花区"},{code:"430112",name:"望城区"},{code:"430121",name:"长沙县"},{code:"430181",name:"浏阳市"},{code:"430182",name:"宁乡市"}]},{code:"4302",name:"株洲市",children:[{code:"430202",name:"荷塘区"},{code:"430203",name:"芦淞区"},{code:"430204",name:"石峰区"},{code:"430211",name:"天元区"},{code:"430212",name:"渌口区"},{code:"430223",name:"攸县"},{code:"430224",name:"茶陵县"},{code:"430225",name:"炎陵县"},{code:"430271",name:"云龙示范区"},{code:"430281",name:"醴陵市"}]},{code:"4303",name:"湘潭市",children:[{code:"430302",name:"雨湖区"},{code:"430304",name:"岳塘区"},{code:"430321",name:"湘潭县"},{code:"430371",name:"湖南湘潭高新技术产业园区"},{code:"430372",name:"湘潭昭山示范区"},{code:"430373",name:"湘潭九华示范区"},{code:"430381",name:"湘乡市"},{code:"430382",name:"韶山市"}]},{code:"4304",name:"衡阳市",children:[{code:"430405",name:"珠晖区"},{code:"430406",name:"雁峰区"},{code:"430407",name:"石鼓区"},{code:"430408",name:"蒸湘区"},{code:"430412",name:"南岳区"},{code:"430421",name:"衡阳县"},{code:"430422",name:"衡南县"},{code:"430423",name:"衡山县"},{code:"430424",name:"衡东县"},{code:"430426",name:"祁东县"},{code:"430471",name:"衡阳综合保税区"},{code:"430472",name:"湖南衡阳高新技术产业园区"},{code:"430473",name:"湖南衡阳松木经济开发区"},{code:"430481",name:"耒阳市"},{code:"430482",name:"常宁市"}]},{code:"4305",name:"邵阳市",children:[{code:"430502",name:"双清区"},{code:"430503",name:"大祥区"},{code:"430511",name:"北塔区"},{code:"430522",name:"新邵县"},{code:"430523",name:"邵阳县"},{code:"430524",name:"隆回县"},{code:"430525",name:"洞口县"},{code:"430527",name:"绥宁县"},{code:"430528",name:"新宁县"},{code:"430529",name:"城步苗族自治县"},{code:"430581",name:"武冈市"},{code:"430582",name:"邵东市"}]},{code:"4306",name:"岳阳市",children:[{code:"430602",name:"岳阳楼区"},{code:"430603",name:"云溪区"},{code:"430611",name:"君山区"},{code:"430621",name:"岳阳县"},{code:"430623",name:"华容县"},{code:"430624",name:"湘阴县"},{code:"430626",name:"平江县"},{code:"430671",name:"岳阳市屈原管理区"},{code:"430681",name:"汨罗市"},{code:"430682",name:"临湘市"}]},{code:"4307",name:"常德市",children:[{code:"430702",name:"武陵区"},{code:"430703",name:"鼎城区"},{code:"430721",name:"安乡县"},{code:"430722",name:"汉寿县"},{code:"430723",name:"澧县"},{code:"430724",name:"临澧县"},{code:"430725",name:"桃源县"},{code:"430726",name:"石门县"},{code:"430771",name:"常德市西洞庭管理区"},{code:"430781",name:"津市市"}]},{code:"4308",name:"张家界市",children:[{code:"430802",name:"永定区"},{code:"430811",name:"武陵源区"},{code:"430821",name:"慈利县"},{code:"430822",name:"桑植县"}]},{code:"4309",name:"益阳市",children:[{code:"430902",name:"资阳区"},{code:"430903",name:"赫山区"},{code:"430921",name:"南县"},{code:"430922",name:"桃江县"},{code:"430923",name:"安化县"},{code:"430971",name:"益阳市大通湖管理区"},{code:"430972",name:"湖南益阳高新技术产业园区"},{code:"430981",name:"沅江市"}]},{code:"4310",name:"郴州市",children:[{code:"431002",name:"北湖区"},{code:"431003",name:"苏仙区"},{code:"431021",name:"桂阳县"},{code:"431022",name:"宜章县"},{code:"431023",name:"永兴县"},{code:"431024",name:"嘉禾县"},{code:"431025",name:"临武县"},{code:"431026",name:"汝城县"},{code:"431027",name:"桂东县"},{code:"431028",name:"安仁县"},{code:"431081",name:"资兴市"}]},{code:"4311",name:"永州市",children:[{code:"431102",name:"零陵区"},{code:"431103",name:"冷水滩区"},{code:"431122",name:"东安县"},{code:"431123",name:"双牌县"},{code:"431124",name:"道县"},{code:"431125",name:"江永县"},{code:"431126",name:"宁远县"},{code:"431127",name:"蓝山县"},{code:"431128",name:"新田县"},{code:"431129",name:"江华瑶族自治县"},{code:"431171",name:"永州经济技术开发区"},{code:"431173",name:"永州市回龙圩管理区"},{code:"431181",name:"祁阳市"}]},{code:"4312",name:"怀化市",children:[{code:"431202",name:"鹤城区"},{code:"431221",name:"中方县"},{code:"431222",name:"沅陵县"},{code:"431223",name:"辰溪县"},{code:"431224",name:"溆浦县"},{code:"431225",name:"会同县"},{code:"431226",name:"麻阳苗族自治县"},{code:"431227",name:"新晃侗族自治县"},{code:"431228",name:"芷江侗族自治县"},{code:"431229",name:"靖州苗族侗族自治县"},{code:"431230",name:"通道侗族自治县"},{code:"431271",name:"怀化市洪江管理区"},{code:"431281",name:"洪江市"}]},{code:"4313",name:"娄底市",children:[{code:"431302",name:"娄星区"},{code:"431321",name:"双峰县"},{code:"431322",name:"新化县"},{code:"431381",name:"冷水江市"},{code:"431382",name:"涟源市"}]},{code:"4331",name:"湘西土家族苗族自治州",children:[{code:"433101",name:"吉首市"},{code:"433122",name:"泸溪县"},{code:"433123",name:"凤凰县"},{code:"433124",name:"花垣县"},{code:"433125",name:"保靖县"},{code:"433126",name:"古丈县"},{code:"433127",name:"永顺县"},{code:"433130",name:"龙山县"}]}]},{code:"44",name:"广东省",children:[{code:"4401",name:"广州市",children:[{code:"440103",name:"荔湾区"},{code:"440104",name:"越秀区"},{code:"440105",name:"海珠区"},{code:"440106",name:"天河区"},{code:"440111",name:"白云区"},{code:"440112",name:"黄埔区"},{code:"440113",name:"番禺区"},{code:"440114",name:"花都区"},{code:"440115",name:"南沙区"},{code:"440117",name:"从化区"},{code:"440118",name:"增城区"}]},{code:"4402",name:"韶关市",children:[{code:"440203",name:"武江区"},{code:"440204",name:"浈江区"},{code:"440205",name:"曲江区"},{code:"440222",name:"始兴县"},{code:"440224",name:"仁化县"},{code:"440229",name:"翁源县"},{code:"440232",name:"乳源瑶族自治县"},{code:"440233",name:"新丰县"},{code:"440281",name:"乐昌市"},{code:"440282",name:"南雄市"}]},{code:"4403",name:"深圳市",children:[{code:"440303",name:"罗湖区"},{code:"440304",name:"福田区"},{code:"440305",name:"南山区"},{code:"440306",name:"宝安区"},{code:"440307",name:"龙岗区"},{code:"440308",name:"盐田区"},{code:"440309",name:"龙华区"},{code:"440310",name:"坪山区"},{code:"440311",name:"光明区"}]},{code:"4404",name:"珠海市",children:[{code:"440402",name:"香洲区"},{code:"440403",name:"斗门区"},{code:"440404",name:"金湾区"}]},{code:"4405",name:"汕头市",children:[{code:"440507",name:"龙湖区"},{code:"440511",name:"金平区"},{code:"440512",name:"濠江区"},{code:"440513",name:"潮阳区"},{code:"440514",name:"潮南区"},{code:"440515",name:"澄海区"},{code:"440523",name:"南澳县"}]},{code:"4406",name:"佛山市",children:[{code:"440604",name:"禅城区"},{code:"440605",name:"南海区"},{code:"440606",name:"顺德区"},{code:"440607",name:"三水区"},{code:"440608",name:"高明区"}]},{code:"4407",name:"江门市",children:[{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}]},{code:"4408",name:"湛江市",children:[{code:"440802",name:"赤坎区"},{code:"440803",name:"霞山区"},{code:"440804",name:"坡头区"},{code:"440811",name:"麻章区"},{code:"440823",name:"遂溪县"},{code:"440825",name:"徐闻县"},{code:"440881",name:"廉江市"},{code:"440882",name:"雷州市"},{code:"440883",name:"吴川市"}]},{code:"4409",name:"茂名市",children:[{code:"440902",name:"茂南区"},{code:"440904",name:"电白区"},{code:"440981",name:"高州市"},{code:"440982",name:"化州市"},{code:"440983",name:"信宜市"}]},{code:"4412",name:"肇庆市",children:[{code:"441202",name:"端州区"},{code:"441203",name:"鼎湖区"},{code:"441204",name:"高要区"},{code:"441223",name:"广宁县"},{code:"441224",name:"怀集县"},{code:"441225",name:"封开县"},{code:"441226",name:"德庆县"},{code:"441284",name:"四会市"}]},{code:"4413",name:"惠州市",children:[{code:"441302",name:"惠城区"},{code:"441303",name:"惠阳区"},{code:"441322",name:"博罗县"},{code:"441323",name:"惠东县"},{code:"441324",name:"龙门县"}]},{code:"4414",name:"梅州市",children:[{code:"441402",name:"梅江区"},{code:"441403",name:"梅县区"},{code:"441422",name:"大埔县"},{code:"441423",name:"丰顺县"},{code:"441424",name:"五华县"},{code:"441426",name:"平远县"},{code:"441427",name:"蕉岭县"},{code:"441481",name:"兴宁市"}]},{code:"4415",name:"汕尾市",children:[{code:"441502",name:"城区"},{code:"441521",name:"海丰县"},{code:"441523",name:"陆河县"},{code:"441581",name:"陆丰市"}]},{code:"4416",name:"河源市",children:[{code:"441602",name:"源城区"},{code:"441621",name:"紫金县"},{code:"441622",name:"龙川县"},{code:"441623",name:"连平县"},{code:"441624",name:"和平县"},{code:"441625",name:"东源县"}]},{code:"4417",name:"阳江市",children:[{code:"441702",name:"江城区"},{code:"441704",name:"阳东区"},{code:"441721",name:"阳西县"},{code:"441781",name:"阳春市"}]},{code:"4418",name:"清远市",children:[{code:"441802",name:"清城区"},{code:"441803",name:"清新区"},{code:"441821",name:"佛冈县"},{code:"441823",name:"阳山县"},{code:"441825",name:"连山壮族瑶族自治县"},{code:"441826",name:"连南瑶族自治县"},{code:"441881",name:"英德市"},{code:"441882",name:"连州市"}]},{code:"4419",name:"东莞市",children:[{code:"441900003",name:"东城街道"},{code:"441900004",name:"南城街道"},{code:"441900005",name:"万江街道"},{code:"441900006",name:"莞城街道"},{code:"441900101",name:"石碣镇"},{code:"441900102",name:"石龙镇"},{code:"441900103",name:"茶山镇"},{code:"441900104",name:"石排镇"},{code:"441900105",name:"企石镇"},{code:"441900106",name:"横沥镇"},{code:"441900107",name:"桥头镇"},{code:"441900108",name:"谢岗镇"},{code:"441900109",name:"东坑镇"},{code:"441900110",name:"常平镇"},{code:"441900111",name:"寮步镇"},{code:"441900112",name:"樟木头镇"},{code:"441900113",name:"大朗镇"},{code:"441900114",name:"黄江镇"},{code:"441900115",name:"清溪镇"},{code:"441900116",name:"塘厦镇"},{code:"441900117",name:"凤岗镇"},{code:"441900118",name:"大岭山镇"},{code:"441900119",name:"长安镇"},{code:"441900121",name:"虎门镇"},{code:"441900122",name:"厚街镇"},{code:"441900123",name:"沙田镇"},{code:"441900124",name:"道滘镇"},{code:"441900125",name:"洪梅镇"},{code:"441900126",name:"麻涌镇"},{code:"441900127",name:"望牛墩镇"},{code:"441900128",name:"中堂镇"},{code:"441900129",name:"高埗镇"},{code:"441900401",name:"松山湖"},{code:"441900402",name:"东莞港"},{code:"441900403",name:"东莞生态园"},{code:"441900404",name:"东莞滨海湾新区"}]},{code:"4420",name:"中山市",children:[{code:"442000001",name:"石岐街道"},{code:"442000002",name:"东区街道"},{code:"442000003",name:"中山港街道"},{code:"442000004",name:"西区街道"},{code:"442000005",name:"南区街道"},{code:"442000006",name:"五桂山街道"},{code:"442000007",name:"民众街道"},{code:"442000008",name:"南朗街道"},{code:"442000101",name:"黄圃镇"},{code:"442000103",name:"东凤镇"},{code:"442000105",name:"古镇镇"},{code:"442000106",name:"沙溪镇"},{code:"442000107",name:"坦洲镇"},{code:"442000108",name:"港口镇"},{code:"442000109",name:"三角镇"},{code:"442000110",name:"横栏镇"},{code:"442000111",name:"南头镇"},{code:"442000112",name:"阜沙镇"},{code:"442000114",name:"三乡镇"},{code:"442000115",name:"板芙镇"},{code:"442000116",name:"大涌镇"},{code:"442000117",name:"神湾镇"},{code:"442000118",name:"小榄镇"}]},{code:"4451",name:"潮州市",children:[{code:"445102",name:"湘桥区"},{code:"445103",name:"潮安区"},{code:"445122",name:"饶平县"}]},{code:"4452",name:"揭阳市",children:[{code:"445202",name:"榕城区"},{code:"445203",name:"揭东区"},{code:"445222",name:"揭西县"},{code:"445224",name:"惠来县"},{code:"445281",name:"普宁市"}]},{code:"4453",name:"云浮市",children:[{code:"445302",name:"云城区"},{code:"445303",name:"云安区"},{code:"445321",name:"新兴县"},{code:"445322",name:"郁南县"},{code:"445381",name:"罗定市"}]}]},{code:"45",name:"广西壮族自治区",children:[{code:"4501",name:"南宁市",children:[{code:"450102",name:"兴宁区"},{code:"450103",name:"青秀区"},{code:"450105",name:"江南区"},{code:"450107",name:"西乡塘区"},{code:"450108",name:"良庆区"},{code:"450109",name:"邕宁区"},{code:"450110",name:"武鸣区"},{code:"450123",name:"隆安县"},{code:"450124",name:"马山县"},{code:"450125",name:"上林县"},{code:"450126",name:"宾阳县"},{code:"450181",name:"横州市"}]},{code:"4502",name:"柳州市",children:[{code:"450202",name:"城中区"},{code:"450203",name:"鱼峰区"},{code:"450204",name:"柳南区"},{code:"450205",name:"柳北区"},{code:"450206",name:"柳江区"},{code:"450222",name:"柳城县"},{code:"450223",name:"鹿寨县"},{code:"450224",name:"融安县"},{code:"450225",name:"融水苗族自治县"},{code:"450226",name:"三江侗族自治县"}]},{code:"4503",name:"桂林市",children:[{code:"450302",name:"秀峰区"},{code:"450303",name:"叠彩区"},{code:"450304",name:"象山区"},{code:"450305",name:"七星区"},{code:"450311",name:"雁山区"},{code:"450312",name:"临桂区"},{code:"450321",name:"阳朔县"},{code:"450323",name:"灵川县"},{code:"450324",name:"全州县"},{code:"450325",name:"兴安县"},{code:"450326",name:"永福县"},{code:"450327",name:"灌阳县"},{code:"450328",name:"龙胜各族自治县"},{code:"450329",name:"资源县"},{code:"450330",name:"平乐县"},{code:"450332",name:"恭城瑶族自治县"},{code:"450381",name:"荔浦市"}]},{code:"4504",name:"梧州市",children:[{code:"450403",name:"万秀区"},{code:"450405",name:"长洲区"},{code:"450406",name:"龙圩区"},{code:"450421",name:"苍梧县"},{code:"450422",name:"藤县"},{code:"450423",name:"蒙山县"},{code:"450481",name:"岑溪市"}]},{code:"4505",name:"北海市",children:[{code:"450502",name:"海城区"},{code:"450503",name:"银海区"},{code:"450512",name:"铁山港区"},{code:"450521",name:"合浦县"}]},{code:"4506",name:"防城港市",children:[{code:"450602",name:"港口区"},{code:"450603",name:"防城区"},{code:"450621",name:"上思县"},{code:"450681",name:"东兴市"}]},{code:"4507",name:"钦州市",children:[{code:"450702",name:"钦南区"},{code:"450703",name:"钦北区"},{code:"450721",name:"灵山县"},{code:"450722",name:"浦北县"}]},{code:"4508",name:"贵港市",children:[{code:"450802",name:"港北区"},{code:"450803",name:"港南区"},{code:"450804",name:"覃塘区"},{code:"450821",name:"平南县"},{code:"450881",name:"桂平市"}]},{code:"4509",name:"玉林市",children:[{code:"450902",name:"玉州区"},{code:"450903",name:"福绵区"},{code:"450921",name:"容县"},{code:"450922",name:"陆川县"},{code:"450923",name:"博白县"},{code:"450924",name:"兴业县"},{code:"450981",name:"北流市"}]},{code:"4510",name:"百色市",children:[{code:"451002",name:"右江区"},{code:"451003",name:"田阳区"},{code:"451022",name:"田东县"},{code:"451024",name:"德保县"},{code:"451026",name:"那坡县"},{code:"451027",name:"凌云县"},{code:"451028",name:"乐业县"},{code:"451029",name:"田林县"},{code:"451030",name:"西林县"},{code:"451031",name:"隆林各族自治县"},{code:"451081",name:"靖西市"},{code:"451082",name:"平果市"}]},{code:"4511",name:"贺州市",children:[{code:"451102",name:"八步区"},{code:"451103",name:"平桂区"},{code:"451121",name:"昭平县"},{code:"451122",name:"钟山县"},{code:"451123",name:"富川瑶族自治县"}]},{code:"4512",name:"河池市",children:[{code:"451202",name:"金城江区"},{code:"451203",name:"宜州区"},{code:"451221",name:"南丹县"},{code:"451222",name:"天峨县"},{code:"451223",name:"凤山县"},{code:"451224",name:"东兰县"},{code:"451225",name:"罗城仫佬族自治县"},{code:"451226",name:"环江毛南族自治县"},{code:"451227",name:"巴马瑶族自治县"},{code:"451228",name:"都安瑶族自治县"},{code:"451229",name:"大化瑶族自治县"}]},{code:"4513",name:"来宾市",children:[{code:"451302",name:"兴宾区"},{code:"451321",name:"忻城县"},{code:"451322",name:"象州县"},{code:"451323",name:"武宣县"},{code:"451324",name:"金秀瑶族自治县"},{code:"451381",name:"合山市"}]},{code:"4514",name:"崇左市",children:[{code:"451402",name:"江州区"},{code:"451421",name:"扶绥县"},{code:"451422",name:"宁明县"},{code:"451423",name:"龙州县"},{code:"451424",name:"大新县"},{code:"451425",name:"天等县"},{code:"451481",name:"凭祥市"}]}]},{code:"46",name:"海南省",children:[{code:"4601",name:"海口市",children:[{code:"460105",name:"秀英区"},{code:"460106",name:"龙华区"},{code:"460107",name:"琼山区"},{code:"460108",name:"美兰区"}]},{code:"4602",name:"三亚市",children:[{code:"460202",name:"海棠区"},{code:"460203",name:"吉阳区"},{code:"460204",name:"天涯区"},{code:"460205",name:"崖州区"}]},{code:"4603",name:"三沙市",children:[{code:"460321",name:"西沙群岛"},{code:"460322",name:"南沙群岛"},{code:"460323",name:"中沙群岛的岛礁及其海域"}]},{code:"4604",name:"儋州市",children:[{code:"460400100",name:"那大镇"},{code:"460400101",name:"和庆镇"},{code:"460400102",name:"南丰镇"},{code:"460400103",name:"大成镇"},{code:"460400104",name:"雅星镇"},{code:"460400105",name:"兰洋镇"},{code:"460400106",name:"光村镇"},{code:"460400107",name:"木棠镇"},{code:"460400108",name:"海头镇"},{code:"460400109",name:"峨蔓镇"},{code:"460400111",name:"王五镇"},{code:"460400112",name:"白马井镇"},{code:"460400113",name:"中和镇"},{code:"460400114",name:"排浦镇"},{code:"460400115",name:"东成镇"},{code:"460400116",name:"新州镇"},{code:"460400499",name:"洋浦经济开发区"},{code:"460400500",name:"华南热作学院"}]},{code:"4690",name:"省直辖县级行政区划",children:[{code:"469001",name:"五指山市"},{code:"469002",name:"琼海市"},{code:"469005",name:"文昌市"},{code:"469006",name:"万宁市"},{code:"469007",name:"东方市"},{code:"469021",name:"定安县"},{code:"469022",name:"屯昌县"},{code:"469023",name:"澄迈县"},{code:"469024",name:"临高县"},{code:"469025",name:"白沙黎族自治县"},{code:"469026",name:"昌江黎族自治县"},{code:"469027",name:"乐东黎族自治县"},{code:"469028",name:"陵水黎族自治县"},{code:"469029",name:"保亭黎族苗族自治县"},{code:"469030",name:"琼中黎族苗族自治县"}]}]},{code:"50",name:"重庆市",children:[{code:"5001",name:"市辖区",children:[{code:"500101",name:"万州区"},{code:"500102",name:"涪陵区"},{code:"500103",name:"渝中区"},{code:"500104",name:"大渡口区"},{code:"500105",name:"江北区"},{code:"500106",name:"沙坪坝区"},{code:"500107",name:"九龙坡区"},{code:"500108",name:"南岸区"},{code:"500109",name:"北碚区"},{code:"500110",name:"綦江区"},{code:"500111",name:"大足区"},{code:"500112",name:"渝北区"},{code:"500113",name:"巴南区"},{code:"500114",name:"黔江区"},{code:"500115",name:"长寿区"},{code:"500116",name:"江津区"},{code:"500117",name:"合川区"},{code:"500118",name:"永川区"},{code:"500119",name:"南川区"},{code:"500120",name:"璧山区"},{code:"500151",name:"铜梁区"},{code:"500152",name:"潼南区"},{code:"500153",name:"荣昌区"},{code:"500154",name:"开州区"},{code:"500155",name:"梁平区"},{code:"500156",name:"武隆区"}]},{code:"5002",name:"县",children:[{code:"500229",name:"城口县"},{code:"500230",name:"丰都县"},{code:"500231",name:"垫江县"},{code:"500233",name:"忠县"},{code:"500235",name:"云阳县"},{code:"500236",name:"奉节县"},{code:"500237",name:"巫山县"},{code:"500238",name:"巫溪县"},{code:"500240",name:"石柱土家族自治县"},{code:"500241",name:"秀山土家族苗族自治县"},{code:"500242",name:"酉阳土家族苗族自治县"},{code:"500243",name:"彭水苗族土家族自治县"}]}]},{code:"51",name:"四川省",children:[{code:"5101",name:"成都市",children:[{code:"510104",name:"锦江区"},{code:"510105",name:"青羊区"},{code:"510106",name:"金牛区"},{code:"510107",name:"武侯区"},{code:"510108",name:"成华区"},{code:"510112",name:"龙泉驿区"},{code:"510113",name:"青白江区"},{code:"510114",name:"新都区"},{code:"510115",name:"温江区"},{code:"510116",name:"双流区"},{code:"510117",name:"郫都区"},{code:"510118",name:"新津区"},{code:"510121",name:"金堂县"},{code:"510129",name:"大邑县"},{code:"510131",name:"蒲江县"},{code:"510181",name:"都江堰市"},{code:"510182",name:"彭州市"},{code:"510183",name:"邛崃市"},{code:"510184",name:"崇州市"},{code:"510185",name:"简阳市"}]},{code:"5103",name:"自贡市",children:[{code:"510302",name:"自流井区"},{code:"510303",name:"贡井区"},{code:"510304",name:"大安区"},{code:"510311",name:"沿滩区"},{code:"510321",name:"荣县"},{code:"510322",name:"富顺县"}]},{code:"5104",name:"攀枝花市",children:[{code:"510402",name:"东区"},{code:"510403",name:"西区"},{code:"510411",name:"仁和区"},{code:"510421",name:"米易县"},{code:"510422",name:"盐边县"}]},{code:"5105",name:"泸州市",children:[{code:"510502",name:"江阳区"},{code:"510503",name:"纳溪区"},{code:"510504",name:"龙马潭区"},{code:"510521",name:"泸县"},{code:"510522",name:"合江县"},{code:"510524",name:"叙永县"},{code:"510525",name:"古蔺县"}]},{code:"5106",name:"德阳市",children:[{code:"510603",name:"旌阳区"},{code:"510604",name:"罗江区"},{code:"510623",name:"中江县"},{code:"510681",name:"广汉市"},{code:"510682",name:"什邡市"},{code:"510683",name:"绵竹市"}]},{code:"5107",name:"绵阳市",children:[{code:"510703",name:"涪城区"},{code:"510704",name:"游仙区"},{code:"510705",name:"安州区"},{code:"510722",name:"三台县"},{code:"510723",name:"盐亭县"},{code:"510725",name:"梓潼县"},{code:"510726",name:"北川羌族自治县"},{code:"510727",name:"平武县"},{code:"510781",name:"江油市"}]},{code:"5108",name:"广元市",children:[{code:"510802",name:"利州区"},{code:"510811",name:"昭化区"},{code:"510812",name:"朝天区"},{code:"510821",name:"旺苍县"},{code:"510822",name:"青川县"},{code:"510823",name:"剑阁县"},{code:"510824",name:"苍溪县"}]},{code:"5109",name:"遂宁市",children:[{code:"510903",name:"船山区"},{code:"510904",name:"安居区"},{code:"510921",name:"蓬溪县"},{code:"510923",name:"大英县"},{code:"510981",name:"射洪市"}]},{code:"5110",name:"内江市",children:[{code:"511002",name:"市中区"},{code:"511011",name:"东兴区"},{code:"511024",name:"威远县"},{code:"511025",name:"资中县"},{code:"511071",name:"内江经济开发区"},{code:"511083",name:"隆昌市"}]},{code:"5111",name:"乐山市",children:[{code:"511102",name:"市中区"},{code:"511111",name:"沙湾区"},{code:"511112",name:"五通桥区"},{code:"511113",name:"金口河区"},{code:"511123",name:"犍为县"},{code:"511124",name:"井研县"},{code:"511126",name:"夹江县"},{code:"511129",name:"沐川县"},{code:"511132",name:"峨边彝族自治县"},{code:"511133",name:"马边彝族自治县"},{code:"511181",name:"峨眉山市"}]},{code:"5113",name:"南充市",children:[{code:"511302",name:"顺庆区"},{code:"511303",name:"高坪区"},{code:"511304",name:"嘉陵区"},{code:"511321",name:"南部县"},{code:"511322",name:"营山县"},{code:"511323",name:"蓬安县"},{code:"511324",name:"仪陇县"},{code:"511325",name:"西充县"},{code:"511381",name:"阆中市"}]},{code:"5114",name:"眉山市",children:[{code:"511402",name:"东坡区"},{code:"511403",name:"彭山区"},{code:"511421",name:"仁寿县"},{code:"511423",name:"洪雅县"},{code:"511424",name:"丹棱县"},{code:"511425",name:"青神县"}]},{code:"5115",name:"宜宾市",children:[{code:"511502",name:"翠屏区"},{code:"511503",name:"南溪区"},{code:"511504",name:"叙州区"},{code:"511523",name:"江安县"},{code:"511524",name:"长宁县"},{code:"511525",name:"高县"},{code:"511526",name:"珙县"},{code:"511527",name:"筠连县"},{code:"511528",name:"兴文县"},{code:"511529",name:"屏山县"}]},{code:"5116",name:"广安市",children:[{code:"511602",name:"广安区"},{code:"511603",name:"前锋区"},{code:"511621",name:"岳池县"},{code:"511622",name:"武胜县"},{code:"511623",name:"邻水县"},{code:"511681",name:"华蓥市"}]},{code:"5117",name:"达州市",children:[{code:"511702",name:"通川区"},{code:"511703",name:"达川区"},{code:"511722",name:"宣汉县"},{code:"511723",name:"开江县"},{code:"511724",name:"大竹县"},{code:"511725",name:"渠县"},{code:"511771",name:"达州经济开发区"},{code:"511781",name:"万源市"}]},{code:"5118",name:"雅安市",children:[{code:"511802",name:"雨城区"},{code:"511803",name:"名山区"},{code:"511822",name:"荥经县"},{code:"511823",name:"汉源县"},{code:"511824",name:"石棉县"},{code:"511825",name:"天全县"},{code:"511826",name:"芦山县"},{code:"511827",name:"宝兴县"}]},{code:"5119",name:"巴中市",children:[{code:"511902",name:"巴州区"},{code:"511903",name:"恩阳区"},{code:"511921",name:"通江县"},{code:"511922",name:"南江县"},{code:"511923",name:"平昌县"},{code:"511971",name:"巴中经济开发区"}]},{code:"5120",name:"资阳市",children:[{code:"512002",name:"雁江区"},{code:"512021",name:"安岳县"},{code:"512022",name:"乐至县"}]},{code:"5132",name:"阿坝藏族羌族自治州",children:[{code:"513201",name:"马尔康市"},{code:"513221",name:"汶川县"},{code:"513222",name:"理县"},{code:"513223",name:"茂县"},{code:"513224",name:"松潘县"},{code:"513225",name:"九寨沟县"},{code:"513226",name:"金川县"},{code:"513227",name:"小金县"},{code:"513228",name:"黑水县"},{code:"513230",name:"壤塘县"},{code:"513231",name:"阿坝县"},{code:"513232",name:"若尔盖县"},{code:"513233",name:"红原县"}]},{code:"5133",name:"甘孜藏族自治州",children:[{code:"513301",name:"康定市"},{code:"513322",name:"泸定县"},{code:"513323",name:"丹巴县"},{code:"513324",name:"九龙县"},{code:"513325",name:"雅江县"},{code:"513326",name:"道孚县"},{code:"513327",name:"炉霍县"},{code:"513328",name:"甘孜县"},{code:"513329",name:"新龙县"},{code:"513330",name:"德格县"},{code:"513331",name:"白玉县"},{code:"513332",name:"石渠县"},{code:"513333",name:"色达县"},{code:"513334",name:"理塘县"},{code:"513335",name:"巴塘县"},{code:"513336",name:"乡城县"},{code:"513337",name:"稻城县"},{code:"513338",name:"得荣县"}]},{code:"5134",name:"凉山彝族自治州",children:[{code:"513401",name:"西昌市"},{code:"513402",name:"会理市"},{code:"513422",name:"木里藏族自治县"},{code:"513423",name:"盐源县"},{code:"513424",name:"德昌县"},{code:"513426",name:"会东县"},{code:"513427",name:"宁南县"},{code:"513428",name:"普格县"},{code:"513429",name:"布拖县"},{code:"513430",name:"金阳县"},{code:"513431",name:"昭觉县"},{code:"513432",name:"喜德县"},{code:"513433",name:"冕宁县"},{code:"513434",name:"越西县"},{code:"513435",name:"甘洛县"},{code:"513436",name:"美姑县"},{code:"513437",name:"雷波县"}]}]},{code:"52",name:"贵州省",children:[{code:"5201",name:"贵阳市",children:[{code:"520102",name:"南明区"},{code:"520103",name:"云岩区"},{code:"520111",name:"花溪区"},{code:"520112",name:"乌当区"},{code:"520113",name:"白云区"},{code:"520115",name:"观山湖区"},{code:"520121",name:"开阳县"},{code:"520122",name:"息烽县"},{code:"520123",name:"修文县"},{code:"520181",name:"清镇市"}]},{code:"5202",name:"六盘水市",children:[{code:"520201",name:"钟山区"},{code:"520203",name:"六枝特区"},{code:"520204",name:"水城区"},{code:"520281",name:"盘州市"}]},{code:"5203",name:"遵义市",children:[{code:"520302",name:"红花岗区"},{code:"520303",name:"汇川区"},{code:"520304",name:"播州区"},{code:"520322",name:"桐梓县"},{code:"520323",name:"绥阳县"},{code:"520324",name:"正安县"},{code:"520325",name:"道真仡佬族苗族自治县"},{code:"520326",name:"务川仡佬族苗族自治县"},{code:"520327",name:"凤冈县"},{code:"520328",name:"湄潭县"},{code:"520329",name:"余庆县"},{code:"520330",name:"习水县"},{code:"520381",name:"赤水市"},{code:"520382",name:"仁怀市"}]},{code:"5204",name:"安顺市",children:[{code:"520402",name:"西秀区"},{code:"520403",name:"平坝区"},{code:"520422",name:"普定县"},{code:"520423",name:"镇宁布依族苗族自治县"},{code:"520424",name:"关岭布依族苗族自治县"},{code:"520425",name:"紫云苗族布依族自治县"}]},{code:"5205",name:"毕节市",children:[{code:"520502",name:"七星关区"},{code:"520521",name:"大方县"},{code:"520523",name:"金沙县"},{code:"520524",name:"织金县"},{code:"520525",name:"纳雍县"},{code:"520526",name:"威宁彝族回族苗族自治县"},{code:"520527",name:"赫章县"},{code:"520581",name:"黔西市"}]},{code:"5206",name:"铜仁市",children:[{code:"520602",name:"碧江区"},{code:"520603",name:"万山区"},{code:"520621",name:"江口县"},{code:"520622",name:"玉屏侗族自治县"},{code:"520623",name:"石阡县"},{code:"520624",name:"思南县"},{code:"520625",name:"印江土家族苗族自治县"},{code:"520626",name:"德江县"},{code:"520627",name:"沿河土家族自治县"},{code:"520628",name:"松桃苗族自治县"}]},{code:"5223",name:"黔西南布依族苗族自治州",children:[{code:"522301",name:"兴义市"},{code:"522302",name:"兴仁市"},{code:"522323",name:"普安县"},{code:"522324",name:"晴隆县"},{code:"522325",name:"贞丰县"},{code:"522326",name:"望谟县"},{code:"522327",name:"册亨县"},{code:"522328",name:"安龙县"}]},{code:"5226",name:"黔东南苗族侗族自治州",children:[{code:"522601",name:"凯里市"},{code:"522622",name:"黄平县"},{code:"522623",name:"施秉县"},{code:"522624",name:"三穗县"},{code:"522625",name:"镇远县"},{code:"522626",name:"岑巩县"},{code:"522627",name:"天柱县"},{code:"522628",name:"锦屏县"},{code:"522629",name:"剑河县"},{code:"522630",name:"台江县"},{code:"522631",name:"黎平县"},{code:"522632",name:"榕江县"},{code:"522633",name:"从江县"},{code:"522634",name:"雷山县"},{code:"522635",name:"麻江县"},{code:"522636",name:"丹寨县"}]},{code:"5227",name:"黔南布依族苗族自治州",children:[{code:"522701",name:"都匀市"},{code:"522702",name:"福泉市"},{code:"522722",name:"荔波县"},{code:"522723",name:"贵定县"},{code:"522725",name:"瓮安县"},{code:"522726",name:"独山县"},{code:"522727",name:"平塘县"},{code:"522728",name:"罗甸县"},{code:"522729",name:"长顺县"},{code:"522730",name:"龙里县"},{code:"522731",name:"惠水县"},{code:"522732",name:"三都水族自治县"}]}]},{code:"53",name:"云南省",children:[{code:"5301",name:"昆明市",children:[{code:"530102",name:"五华区"},{code:"530103",name:"盘龙区"},{code:"530111",name:"官渡区"},{code:"530112",name:"西山区"},{code:"530113",name:"东川区"},{code:"530114",name:"呈贡区"},{code:"530115",name:"晋宁区"},{code:"530124",name:"富民县"},{code:"530125",name:"宜良县"},{code:"530126",name:"石林彝族自治县"},{code:"530127",name:"嵩明县"},{code:"530128",name:"禄劝彝族苗族自治县"},{code:"530129",name:"寻甸回族彝族自治县"},{code:"530181",name:"安宁市"}]},{code:"5303",name:"曲靖市",children:[{code:"530302",name:"麒麟区"},{code:"530303",name:"沾益区"},{code:"530304",name:"马龙区"},{code:"530322",name:"陆良县"},{code:"530323",name:"师宗县"},{code:"530324",name:"罗平县"},{code:"530325",name:"富源县"},{code:"530326",name:"会泽县"},{code:"530381",name:"宣威市"}]},{code:"5304",name:"玉溪市",children:[{code:"530402",name:"红塔区"},{code:"530403",name:"江川区"},{code:"530423",name:"通海县"},{code:"530424",name:"华宁县"},{code:"530425",name:"易门县"},{code:"530426",name:"峨山彝族自治县"},{code:"530427",name:"新平彝族傣族自治县"},{code:"530428",name:"元江哈尼族彝族傣族自治县"},{code:"530481",name:"澄江市"}]},{code:"5305",name:"保山市",children:[{code:"530502",name:"隆阳区"},{code:"530521",name:"施甸县"},{code:"530523",name:"龙陵县"},{code:"530524",name:"昌宁县"},{code:"530581",name:"腾冲市"}]},{code:"5306",name:"昭通市",children:[{code:"530602",name:"昭阳区"},{code:"530621",name:"鲁甸县"},{code:"530622",name:"巧家县"},{code:"530623",name:"盐津县"},{code:"530624",name:"大关县"},{code:"530625",name:"永善县"},{code:"530626",name:"绥江县"},{code:"530627",name:"镇雄县"},{code:"530628",name:"彝良县"},{code:"530629",name:"威信县"},{code:"530681",name:"水富市"}]},{code:"5307",name:"丽江市",children:[{code:"530702",name:"古城区"},{code:"530721",name:"玉龙纳西族自治县"},{code:"530722",name:"永胜县"},{code:"530723",name:"华坪县"},{code:"530724",name:"宁蒗彝族自治县"}]},{code:"5308",name:"普洱市",children:[{code:"530802",name:"思茅区"},{code:"530821",name:"宁洱哈尼族彝族自治县"},{code:"530822",name:"墨江哈尼族自治县"},{code:"530823",name:"景东彝族自治县"},{code:"530824",name:"景谷傣族彝族自治县"},{code:"530825",name:"镇沅彝族哈尼族拉祜族自治县"},{code:"530826",name:"江城哈尼族彝族自治县"},{code:"530827",name:"孟连傣族拉祜族佤族自治县"},{code:"530828",name:"澜沧拉祜族自治县"},{code:"530829",name:"西盟佤族自治县"}]},{code:"5309",name:"临沧市",children:[{code:"530902",name:"临翔区"},{code:"530921",name:"凤庆县"},{code:"530922",name:"云县"},{code:"530923",name:"永德县"},{code:"530924",name:"镇康县"},{code:"530925",name:"双江拉祜族佤族布朗族傣族自治县"},{code:"530926",name:"耿马傣族佤族自治县"},{code:"530927",name:"沧源佤族自治县"}]},{code:"5323",name:"楚雄彝族自治州",children:[{code:"532301",name:"楚雄市"},{code:"532302",name:"禄丰市"},{code:"532322",name:"双柏县"},{code:"532323",name:"牟定县"},{code:"532324",name:"南华县"},{code:"532325",name:"姚安县"},{code:"532326",name:"大姚县"},{code:"532327",name:"永仁县"},{code:"532328",name:"元谋县"},{code:"532329",name:"武定县"}]},{code:"5325",name:"红河哈尼族彝族自治州",children:[{code:"532501",name:"个旧市"},{code:"532502",name:"开远市"},{code:"532503",name:"蒙自市"},{code:"532504",name:"弥勒市"},{code:"532523",name:"屏边苗族自治县"},{code:"532524",name:"建水县"},{code:"532525",name:"石屏县"},{code:"532527",name:"泸西县"},{code:"532528",name:"元阳县"},{code:"532529",name:"红河县"},{code:"532530",name:"金平苗族瑶族傣族自治县"},{code:"532531",name:"绿春县"},{code:"532532",name:"河口瑶族自治县"}]},{code:"5326",name:"文山壮族苗族自治州",children:[{code:"532601",name:"文山市"},{code:"532622",name:"砚山县"},{code:"532623",name:"西畴县"},{code:"532624",name:"麻栗坡县"},{code:"532625",name:"马关县"},{code:"532626",name:"丘北县"},{code:"532627",name:"广南县"},{code:"532628",name:"富宁县"}]},{code:"5328",name:"西双版纳傣族自治州",children:[{code:"532801",name:"景洪市"},{code:"532822",name:"勐海县"},{code:"532823",name:"勐腊县"}]},{code:"5329",name:"大理白族自治州",children:[{code:"532901",name:"大理市"},{code:"532922",name:"漾濞彝族自治县"},{code:"532923",name:"祥云县"},{code:"532924",name:"宾川县"},{code:"532925",name:"弥渡县"},{code:"532926",name:"南涧彝族自治县"},{code:"532927",name:"巍山彝族回族自治县"},{code:"532928",name:"永平县"},{code:"532929",name:"云龙县"},{code:"532930",name:"洱源县"},{code:"532931",name:"剑川县"},{code:"532932",name:"鹤庆县"}]},{code:"5331",name:"德宏傣族景颇族自治州",children:[{code:"533102",name:"瑞丽市"},{code:"533103",name:"芒市"},{code:"533122",name:"梁河县"},{code:"533123",name:"盈江县"},{code:"533124",name:"陇川县"}]},{code:"5333",name:"怒江傈僳族自治州",children:[{code:"533301",name:"泸水市"},{code:"533323",name:"福贡县"},{code:"533324",name:"贡山独龙族怒族自治县"},{code:"533325",name:"兰坪白族普米族自治县"}]},{code:"5334",name:"迪庆藏族自治州",children:[{code:"533401",name:"香格里拉市"},{code:"533422",name:"德钦县"},{code:"533423",name:"维西傈僳族自治县"}]}]},{code:"54",name:"西藏自治区",children:[{code:"5401",name:"拉萨市",children:[{code:"540102",name:"城关区"},{code:"540103",name:"堆龙德庆区"},{code:"540104",name:"达孜区"},{code:"540121",name:"林周县"},{code:"540122",name:"当雄县"},{code:"540123",name:"尼木县"},{code:"540124",name:"曲水县"},{code:"540127",name:"墨竹工卡县"},{code:"540171",name:"格尔木藏青工业园区"},{code:"540172",name:"拉萨经济技术开发区"},{code:"540173",name:"西藏文化旅游创意园区"},{code:"540174",name:"达孜工业园区"}]},{code:"5402",name:"日喀则市",children:[{code:"540202",name:"桑珠孜区"},{code:"540221",name:"南木林县"},{code:"540222",name:"江孜县"},{code:"540223",name:"定日县"},{code:"540224",name:"萨迦县"},{code:"540225",name:"拉孜县"},{code:"540226",name:"昂仁县"},{code:"540227",name:"谢通门县"},{code:"540228",name:"白朗县"},{code:"540229",name:"仁布县"},{code:"540230",name:"康马县"},{code:"540231",name:"定结县"},{code:"540232",name:"仲巴县"},{code:"540233",name:"亚东县"},{code:"540234",name:"吉隆县"},{code:"540235",name:"聂拉木县"},{code:"540236",name:"萨嘎县"},{code:"540237",name:"岗巴县"}]},{code:"5403",name:"昌都市",children:[{code:"540302",name:"卡若区"},{code:"540321",name:"江达县"},{code:"540322",name:"贡觉县"},{code:"540323",name:"类乌齐县"},{code:"540324",name:"丁青县"},{code:"540325",name:"察雅县"},{code:"540326",name:"八宿县"},{code:"540327",name:"左贡县"},{code:"540328",name:"芒康县"},{code:"540329",name:"洛隆县"},{code:"540330",name:"边坝县"}]},{code:"5404",name:"林芝市",children:[{code:"540402",name:"巴宜区"},{code:"540421",name:"工布江达县"},{code:"540422",name:"米林县"},{code:"540423",name:"墨脱县"},{code:"540424",name:"波密县"},{code:"540425",name:"察隅县"},{code:"540426",name:"朗县"}]},{code:"5405",name:"山南市",children:[{code:"540502",name:"乃东区"},{code:"540521",name:"扎囊县"},{code:"540522",name:"贡嘎县"},{code:"540523",name:"桑日县"},{code:"540524",name:"琼结县"},{code:"540525",name:"曲松县"},{code:"540526",name:"措美县"},{code:"540527",name:"洛扎县"},{code:"540528",name:"加查县"},{code:"540529",name:"隆子县"},{code:"540530",name:"错那县"},{code:"540531",name:"浪卡子县"}]},{code:"5406",name:"那曲市",children:[{code:"540602",name:"色尼区"},{code:"540621",name:"嘉黎县"},{code:"540622",name:"比如县"},{code:"540623",name:"聂荣县"},{code:"540624",name:"安多县"},{code:"540625",name:"申扎县"},{code:"540626",name:"索县"},{code:"540627",name:"班戈县"},{code:"540628",name:"巴青县"},{code:"540629",name:"尼玛县"},{code:"540630",name:"双湖县"}]},{code:"5425",name:"阿里地区",children:[{code:"542521",name:"普兰县"},{code:"542522",name:"札达县"},{code:"542523",name:"噶尔县"},{code:"542524",name:"日土县"},{code:"542525",name:"革吉县"},{code:"542526",name:"改则县"},{code:"542527",name:"措勤县"}]}]},{code:"61",name:"陕西省",children:[{code:"6101",name:"西安市",children:[{code:"610102",name:"新城区"},{code:"610103",name:"碑林区"},{code:"610104",name:"莲湖区"},{code:"610111",name:"灞桥区"},{code:"610112",name:"未央区"},{code:"610113",name:"雁塔区"},{code:"610114",name:"阎良区"},{code:"610115",name:"临潼区"},{code:"610116",name:"长安区"},{code:"610117",name:"高陵区"},{code:"610118",name:"鄠邑区"},{code:"610122",name:"蓝田县"},{code:"610124",name:"周至县"}]},{code:"6102",name:"铜川市",children:[{code:"610202",name:"王益区"},{code:"610203",name:"印台区"},{code:"610204",name:"耀州区"},{code:"610222",name:"宜君县"}]},{code:"6103",name:"宝鸡市",children:[{code:"610302",name:"渭滨区"},{code:"610303",name:"金台区"},{code:"610304",name:"陈仓区"},{code:"610305",name:"凤翔区"},{code:"610323",name:"岐山县"},{code:"610324",name:"扶风县"},{code:"610326",name:"眉县"},{code:"610327",name:"陇县"},{code:"610328",name:"千阳县"},{code:"610329",name:"麟游县"},{code:"610330",name:"凤县"},{code:"610331",name:"太白县"}]},{code:"6104",name:"咸阳市",children:[{code:"610402",name:"秦都区"},{code:"610403",name:"杨陵区"},{code:"610404",name:"渭城区"},{code:"610422",name:"三原县"},{code:"610423",name:"泾阳县"},{code:"610424",name:"乾县"},{code:"610425",name:"礼泉县"},{code:"610426",name:"永寿县"},{code:"610428",name:"长武县"},{code:"610429",name:"旬邑县"},{code:"610430",name:"淳化县"},{code:"610431",name:"武功县"},{code:"610481",name:"兴平市"},{code:"610482",name:"彬州市"}]},{code:"6105",name:"渭南市",children:[{code:"610502",name:"临渭区"},{code:"610503",name:"华州区"},{code:"610522",name:"潼关县"},{code:"610523",name:"大荔县"},{code:"610524",name:"合阳县"},{code:"610525",name:"澄城县"},{code:"610526",name:"蒲城县"},{code:"610527",name:"白水县"},{code:"610528",name:"富平县"},{code:"610581",name:"韩城市"},{code:"610582",name:"华阴市"}]},{code:"6106",name:"延安市",children:[{code:"610602",name:"宝塔区"},{code:"610603",name:"安塞区"},{code:"610621",name:"延长县"},{code:"610622",name:"延川县"},{code:"610625",name:"志丹县"},{code:"610626",name:"吴起县"},{code:"610627",name:"甘泉县"},{code:"610628",name:"富县"},{code:"610629",name:"洛川县"},{code:"610630",name:"宜川县"},{code:"610631",name:"黄龙县"},{code:"610632",name:"黄陵县"},{code:"610681",name:"子长市"}]},{code:"6107",name:"汉中市",children:[{code:"610702",name:"汉台区"},{code:"610703",name:"南郑区"},{code:"610722",name:"城固县"},{code:"610723",name:"洋县"},{code:"610724",name:"西乡县"},{code:"610725",name:"勉县"},{code:"610726",name:"宁强县"},{code:"610727",name:"略阳县"},{code:"610728",name:"镇巴县"},{code:"610729",name:"留坝县"},{code:"610730",name:"佛坪县"}]},{code:"6108",name:"榆林市",children:[{code:"610802",name:"榆阳区"},{code:"610803",name:"横山区"},{code:"610822",name:"府谷县"},{code:"610824",name:"靖边县"},{code:"610825",name:"定边县"},{code:"610826",name:"绥德县"},{code:"610827",name:"米脂县"},{code:"610828",name:"佳县"},{code:"610829",name:"吴堡县"},{code:"610830",name:"清涧县"},{code:"610831",name:"子洲县"},{code:"610881",name:"神木市"}]},{code:"6109",name:"安康市",children:[{code:"610902",name:"汉滨区"},{code:"610921",name:"汉阴县"},{code:"610922",name:"石泉县"},{code:"610923",name:"宁陕县"},{code:"610924",name:"紫阳县"},{code:"610925",name:"岚皋县"},{code:"610926",name:"平利县"},{code:"610927",name:"镇坪县"},{code:"610929",name:"白河县"},{code:"610981",name:"旬阳市"}]},{code:"6110",name:"商洛市",children:[{code:"611002",name:"商州区"},{code:"611021",name:"洛南县"},{code:"611022",name:"丹凤县"},{code:"611023",name:"商南县"},{code:"611024",name:"山阳县"},{code:"611025",name:"镇安县"},{code:"611026",name:"柞水县"}]}]},{code:"62",name:"甘肃省",children:[{code:"6201",name:"兰州市",children:[{code:"620102",name:"城关区"},{code:"620103",name:"七里河区"},{code:"620104",name:"西固区"},{code:"620105",name:"安宁区"},{code:"620111",name:"红古区"},{code:"620121",name:"永登县"},{code:"620122",name:"皋兰县"},{code:"620123",name:"榆中县"},{code:"620171",name:"兰州新区"}]},{code:"6202",name:"嘉峪关市",children:[{code:"620201001",name:"雄关街道"},{code:"620201002",name:"钢城街道"},{code:"620201100",name:"新城镇"},{code:"620201101",name:"峪泉镇"},{code:"620201102",name:"文殊镇"}]},{code:"6203",name:"金昌市",children:[{code:"620302",name:"金川区"},{code:"620321",name:"永昌县"}]},{code:"6204",name:"白银市",children:[{code:"620402",name:"白银区"},{code:"620403",name:"平川区"},{code:"620421",name:"靖远县"},{code:"620422",name:"会宁县"},{code:"620423",name:"景泰县"}]},{code:"6205",name:"天水市",children:[{code:"620502",name:"秦州区"},{code:"620503",name:"麦积区"},{code:"620521",name:"清水县"},{code:"620522",name:"秦安县"},{code:"620523",name:"甘谷县"},{code:"620524",name:"武山县"},{code:"620525",name:"张家川回族自治县"}]},{code:"6206",name:"武威市",children:[{code:"620602",name:"凉州区"},{code:"620621",name:"民勤县"},{code:"620622",name:"古浪县"},{code:"620623",name:"天祝藏族自治县"}]},{code:"6207",name:"张掖市",children:[{code:"620702",name:"甘州区"},{code:"620721",name:"肃南裕固族自治县"},{code:"620722",name:"民乐县"},{code:"620723",name:"临泽县"},{code:"620724",name:"高台县"},{code:"620725",name:"山丹县"}]},{code:"6208",name:"平凉市",children:[{code:"620802",name:"崆峒区"},{code:"620821",name:"泾川县"},{code:"620822",name:"灵台县"},{code:"620823",name:"崇信县"},{code:"620825",name:"庄浪县"},{code:"620826",name:"静宁县"},{code:"620881",name:"华亭市"}]},{code:"6209",name:"酒泉市",children:[{code:"620902",name:"肃州区"},{code:"620921",name:"金塔县"},{code:"620922",name:"瓜州县"},{code:"620923",name:"肃北蒙古族自治县"},{code:"620924",name:"阿克塞哈萨克族自治县"},{code:"620981",name:"玉门市"},{code:"620982",name:"敦煌市"}]},{code:"6210",name:"庆阳市",children:[{code:"621002",name:"西峰区"},{code:"621021",name:"庆城县"},{code:"621022",name:"环县"},{code:"621023",name:"华池县"},{code:"621024",name:"合水县"},{code:"621025",name:"正宁县"},{code:"621026",name:"宁县"},{code:"621027",name:"镇原县"}]},{code:"6211",name:"定西市",children:[{code:"621102",name:"安定区"},{code:"621121",name:"通渭县"},{code:"621122",name:"陇西县"},{code:"621123",name:"渭源县"},{code:"621124",name:"临洮县"},{code:"621125",name:"漳县"},{code:"621126",name:"岷县"}]},{code:"6212",name:"陇南市",children:[{code:"621202",name:"武都区"},{code:"621221",name:"成县"},{code:"621222",name:"文县"},{code:"621223",name:"宕昌县"},{code:"621224",name:"康县"},{code:"621225",name:"西和县"},{code:"621226",name:"礼县"},{code:"621227",name:"徽县"},{code:"621228",name:"两当县"}]},{code:"6229",name:"临夏回族自治州",children:[{code:"622901",name:"临夏市"},{code:"622921",name:"临夏县"},{code:"622922",name:"康乐县"},{code:"622923",name:"永靖县"},{code:"622924",name:"广河县"},{code:"622925",name:"和政县"},{code:"622926",name:"东乡族自治县"},{code:"622927",name:"积石山保安族东乡族撒拉族自治县"}]},{code:"6230",name:"甘南藏族自治州",children:[{code:"623001",name:"合作市"},{code:"623021",name:"临潭县"},{code:"623022",name:"卓尼县"},{code:"623023",name:"舟曲县"},{code:"623024",name:"迭部县"},{code:"623025",name:"玛曲县"},{code:"623026",name:"碌曲县"},{code:"623027",name:"夏河县"}]}]},{code:"63",name:"青海省",children:[{code:"6301",name:"西宁市",children:[{code:"630102",name:"城东区"},{code:"630103",name:"城中区"},{code:"630104",name:"城西区"},{code:"630105",name:"城北区"},{code:"630106",name:"湟中区"},{code:"630121",name:"大通回族土族自治县"},{code:"630123",name:"湟源县"}]},{code:"6302",name:"海东市",children:[{code:"630202",name:"乐都区"},{code:"630203",name:"平安区"},{code:"630222",name:"民和回族土族自治县"},{code:"630223",name:"互助土族自治县"},{code:"630224",name:"化隆回族自治县"},{code:"630225",name:"循化撒拉族自治县"}]},{code:"6322",name:"海北藏族自治州",children:[{code:"632221",name:"门源回族自治县"},{code:"632222",name:"祁连县"},{code:"632223",name:"海晏县"},{code:"632224",name:"刚察县"}]},{code:"6323",name:"黄南藏族自治州",children:[{code:"632301",name:"同仁市"},{code:"632322",name:"尖扎县"},{code:"632323",name:"泽库县"},{code:"632324",name:"河南蒙古族自治县"}]},{code:"6325",name:"海南藏族自治州",children:[{code:"632521",name:"共和县"},{code:"632522",name:"同德县"},{code:"632523",name:"贵德县"},{code:"632524",name:"兴海县"},{code:"632525",name:"贵南县"}]},{code:"6326",name:"果洛藏族自治州",children:[{code:"632621",name:"玛沁县"},{code:"632622",name:"班玛县"},{code:"632623",name:"甘德县"},{code:"632624",name:"达日县"},{code:"632625",name:"久治县"},{code:"632626",name:"玛多县"}]},{code:"6327",name:"玉树藏族自治州",children:[{code:"632701",name:"玉树市"},{code:"632722",name:"杂多县"},{code:"632723",name:"称多县"},{code:"632724",name:"治多县"},{code:"632725",name:"囊谦县"},{code:"632726",name:"曲麻莱县"}]},{code:"6328",name:"海西蒙古族藏族自治州",children:[{code:"632801",name:"格尔木市"},{code:"632802",name:"德令哈市"},{code:"632803",name:"茫崖市"},{code:"632821",name:"乌兰县"},{code:"632822",name:"都兰县"},{code:"632823",name:"天峻县"},{code:"632857",name:"大柴旦行政委员会"}]}]},{code:"64",name:"宁夏回族自治区",children:[{code:"6401",name:"银川市",children:[{code:"640104",name:"兴庆区"},{code:"640105",name:"西夏区"},{code:"640106",name:"金凤区"},{code:"640121",name:"永宁县"},{code:"640122",name:"贺兰县"},{code:"640181",name:"灵武市"}]},{code:"6402",name:"石嘴山市",children:[{code:"640202",name:"大武口区"},{code:"640205",name:"惠农区"},{code:"640221",name:"平罗县"}]},{code:"6403",name:"吴忠市",children:[{code:"640302",name:"利通区"},{code:"640303",name:"红寺堡区"},{code:"640323",name:"盐池县"},{code:"640324",name:"同心县"},{code:"640381",name:"青铜峡市"}]},{code:"6404",name:"固原市",children:[{code:"640402",name:"原州区"},{code:"640422",name:"西吉县"},{code:"640423",name:"隆德县"},{code:"640424",name:"泾源县"},{code:"640425",name:"彭阳县"}]},{code:"6405",name:"中卫市",children:[{code:"640502",name:"沙坡头区"},{code:"640521",name:"中宁县"},{code:"640522",name:"海原县"}]}]},{code:"65",name:"新疆维吾尔自治区",children:[{code:"6501",name:"乌鲁木齐市",children:[{code:"650102",name:"天山区"},{code:"650103",name:"沙依巴克区"},{code:"650104",name:"新市区"},{code:"650105",name:"水磨沟区"},{code:"650106",name:"头屯河区"},{code:"650107",name:"达坂城区"},{code:"650109",name:"米东区"},{code:"650121",name:"乌鲁木齐县"}]},{code:"6502",name:"克拉玛依市",children:[{code:"650202",name:"独山子区"},{code:"650203",name:"克拉玛依区"},{code:"650204",name:"白碱滩区"},{code:"650205",name:"乌尔禾区"}]},{code:"6504",name:"吐鲁番市",children:[{code:"650402",name:"高昌区"},{code:"650421",name:"鄯善县"},{code:"650422",name:"托克逊县"}]},{code:"6505",name:"哈密市",children:[{code:"650502",name:"伊州区"},{code:"650521",name:"巴里坤哈萨克自治县"},{code:"650522",name:"伊吾县"}]},{code:"6523",name:"昌吉回族自治州",children:[{code:"652301",name:"昌吉市"},{code:"652302",name:"阜康市"},{code:"652323",name:"呼图壁县"},{code:"652324",name:"玛纳斯县"},{code:"652325",name:"奇台县"},{code:"652327",name:"吉木萨尔县"},{code:"652328",name:"木垒哈萨克自治县"}]},{code:"6527",name:"博尔塔拉蒙古自治州",children:[{code:"652701",name:"博乐市"},{code:"652702",name:"阿拉山口市"},{code:"652722",name:"精河县"},{code:"652723",name:"温泉县"}]},{code:"6528",name:"巴音郭楞蒙古自治州",children:[{code:"652801",name:"库尔勒市"},{code:"652822",name:"轮台县"},{code:"652823",name:"尉犁县"},{code:"652824",name:"若羌县"},{code:"652825",name:"且末县"},{code:"652826",name:"焉耆回族自治县"},{code:"652827",name:"和静县"},{code:"652828",name:"和硕县"},{code:"652829",name:"博湖县"},{code:"652871",name:"库尔勒经济技术开发区"}]},{code:"6529",name:"阿克苏地区",children:[{code:"652901",name:"阿克苏市"},{code:"652902",name:"库车市"},{code:"652922",name:"温宿县"},{code:"652924",name:"沙雅县"},{code:"652925",name:"新和县"},{code:"652926",name:"拜城县"},{code:"652927",name:"乌什县"},{code:"652928",name:"阿瓦提县"},{code:"652929",name:"柯坪县"}]},{code:"6530",name:"克孜勒苏柯尔克孜自治州",children:[{code:"653001",name:"阿图什市"},{code:"653022",name:"阿克陶县"},{code:"653023",name:"阿合奇县"},{code:"653024",name:"乌恰县"}]},{code:"6531",name:"喀什地区",children:[{code:"653101",name:"喀什市"},{code:"653121",name:"疏附县"},{code:"653122",name:"疏勒县"},{code:"653123",name:"英吉沙县"},{code:"653124",name:"泽普县"},{code:"653125",name:"莎车县"},{code:"653126",name:"叶城县"},{code:"653127",name:"麦盖提县"},{code:"653128",name:"岳普湖县"},{code:"653129",name:"伽师县"},{code:"653130",name:"巴楚县"},{code:"653131",name:"塔什库尔干塔吉克自治县"}]},{code:"6532",name:"和田地区",children:[{code:"653201",name:"和田市"},{code:"653221",name:"和田县"},{code:"653222",name:"墨玉县"},{code:"653223",name:"皮山县"},{code:"653224",name:"洛浦县"},{code:"653225",name:"策勒县"},{code:"653226",name:"于田县"},{code:"653227",name:"民丰县"}]},{code:"6540",name:"伊犁哈萨克自治州",children:[{code:"654002",name:"伊宁市"},{code:"654003",name:"奎屯市"},{code:"654004",name:"霍尔果斯市"},{code:"654021",name:"伊宁县"},{code:"654022",name:"察布查尔锡伯自治县"},{code:"654023",name:"霍城县"},{code:"654024",name:"巩留县"},{code:"654025",name:"新源县"},{code:"654026",name:"昭苏县"},{code:"654027",name:"特克斯县"},{code:"654028",name:"尼勒克县"}]},{code:"6542",name:"塔城地区",children:[{code:"654201",name:"塔城市"},{code:"654202",name:"乌苏市"},{code:"654203",name:"沙湾市"},{code:"654221",name:"额敏县"},{code:"654224",name:"托里县"},{code:"654225",name:"裕民县"},{code:"654226",name:"和布克赛尔蒙古自治县"}]},{code:"6543",name:"阿勒泰地区",children:[{code:"654301",name:"阿勒泰市"},{code:"654321",name:"布尔津县"},{code:"654322",name:"富蕴县"},{code:"654323",name:"福海县"},{code:"654324",name:"哈巴河县"},{code:"654325",name:"青河县"},{code:"654326",name:"吉木乃县"}]},{code:"6590",name:"自治区直辖县级行政区划",children:[{code:"659001",name:"石河子市"},{code:"659002",name:"阿拉尔市"},{code:"659003",name:"图木舒克市"},{code:"659004",name:"五家渠市"},{code:"659005",name:"北屯市"},{code:"659006",name:"铁门关市"},{code:"659007",name:"双河市"},{code:"659008",name:"可克达拉市"},{code:"659009",name:"昆玉市"},{code:"659010",name:"胡杨河市"},{code:"659011",name:"新星市"}]}]}],Ko=(e="default")=>{let a="";switch(e){case"default":a="#35495E";break;case"primary":a="#3488ff";break;case"success":a="#43B883";break;case"warning":a="#e6a23c";break;case"danger":a="#f56c6c";break}return a},v={};v.local={set(e,a){let o=JSON.stringify(a);return localStorage.setItem(e,o)},get(e){let a=localStorage.getItem(e);try{a=JSON.parse(a)}catch{return null}return a},remove(e){return localStorage.removeItem(e)},clear(){return localStorage.clear()}};v.session={set(e,a){let o=JSON.stringify(a);return sessionStorage.setItem(e,o)},get(e){let a=sessionStorage.getItem(e);try{a=JSON.parse(a)}catch{return null}return a},remove(e){return sessionStorage.removeItem(e)},clear(){return sessionStorage.clear()}};v.cookie={set(e,a,o={}){var n={expires:null,path:null,domain:null,...o},i=`${e}=${escape(a)}`;if(n.expires){var c=new Date;c.setTime(c.getTime()+parseInt(n.expires)*1e3),i+=`;expires=${c.toGMTString()}`}n.path&&(i+=`;path=${n.path}`),n.domain&&(i+=`;domain=${n.domain}`),document.cookie=i},get(e){var a=document.cookie.match(new RegExp("(^| )"+e+"=([^;]*)(;|$)"));return a!=null?unescape(a[2]):null},remove(e){var a=new Date;a.setTime(a.getTime()-1),document.cookie=`${e}=;expires=${a.toGMTString()}`}};v.screen=e=>{!!(document.webkitIsFullScreen||document.mozFullScreen||document.msFullscreenElement||document.fullscreenElement)?document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen?document.msExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen&&document.webkitExitFullscreen():e.requestFullscreen?e.requestFullscreen():e.msRequestFullscreen?e.msRequestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullscreen&&e.webkitRequestFullscreen()};v.getDevice=function(){const e="ontouchstart"in window||navigator.maxTouchPoints>0,a=window.innerWidth<768;return e&&a?"mobile":"desktop"};v.parseImage=e=>e===void 0?"/not-image.png":typeof e=="string"&&e!==null?e:e!==null?e[0]:"/not-image.png";v.cityToCode=function(e,a=void 0,o=void 0,n=" / "){try{let i=Be.filter(m=>e==m.code)[0];if(!a)return i.name;let c=i.children.filter(m=>a==m.code)[0];if(!o)return[i.name,c.name].join(n);let t=c.children.filter(m=>o==m.code)[0];return[i.name,c.name,t.name].join(n)}catch{return""}};v.objCopy=e=>{if(e!==void 0)return JSON.parse(JSON.stringify(e))};v.generateId=function(){return Math.floor(Math.random()*1e5+Math.random()*2e4+Math.random()*5e3)};v.uuid=()=>{const e=[];for(let o=0;o<=15;o++)e[o]=o.toString(16);let a="";for(let o=1;o<=36;o++)o===9||o===14||o===19||o===24?a+="-":o===15?a+=4:o===20?a+=e[Math.random()*4|8]:a+=e[Math.random()*16|0];return a};v.dateFormat=(e,a="yyyy-MM-dd hh:mm:ss",o="-")=>{if(e||(e=Number(new Date)),e.toString().length==10&&(e=e*1e3),e=new Date(e),e.valueOf()<1)return o;let n={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};/(y+)/.test(a)&&(a=a.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length)));for(let i in n)new RegExp("("+i+")").test(a)&&(a=a.replace(RegExp.$1,RegExp.$1.length==1?n[i]:("00"+n[i]).substr((""+n[i]).length)));return a};v.groupSeparator=e=>(e=e+"",e.includes(".")||(e+="."),e.replace(/(\d)(?=(\d{3})+\.)/g,function(a,o){return o+","}).replace(/\.$/,""));v.md5=e=>ce.MD5(e).toString();v.base64={encode(e){return ce.enc.Base64.stringify(ce.enc.Utf8.parse(e))},decode(e){return ce.enc.Base64.parse(e).toString(ce.enc.Utf8)}};v.aes={encode(e,a){return ce.AES.encrypt(e,ce.enc.Utf8.parse(a),{mode:ce.mode.ECB,padding:ce.pad.Pkcs7}).toString()},decode(e,a){const o=ce.AES.decrypt(e,ce.enc.Utf8.parse(a),{mode:ce.mode.ECB,padding:ce.pad.Pkcs7});return ce.enc.Utf8.stringify(o)}};v.capsule=(e,a,o="primary")=>{console.log(`%c ${e} %c ${a} %c`,"background:#35495E; padding: 1px; border-radius: 3px 0 0 3px; color: #fff;",`background:${Ko(o)}; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff;`,"background:transparent")};v.formatSize=e=>{if(typeof e>"u")return"0";let a=["B","KB","MB","GB","TB","PB"],o=0;for(let n=0;e>=1024&&n<5;n++)e/=1024,o=n;return Math.round(e,2)+a[o]};v.download=(e,a="")=>{const o=document.createElement("a");let n=a,i=e;e.headers&&e.data&&(i=new Blob([e.data],{type:e.headers["content-type"].replace(";charset=utf8","")}),a||(n=decodeURI(e.headers["content-disposition"]).match(/filename=\"(.+)/gi)[0].replace(/filename=\"/gi,""),n=n.replace('"',""))),o.href=URL.createObjectURL(i),o.setAttribute("download",n),document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(o.href)};v.httpBuild=(e,a=!1)=>{let o=a?"?":"",n=[];for(let i in e){let c=e[i];["",void 0,null].includes(c)||(c.constructor===Array?c.forEach(t=>{n.push(encodeURIComponent(i)+"[]="+encodeURIComponent(t))}):n.push(encodeURIComponent(i)+"="+encodeURIComponent(c)))}return n.length?o+n.join("&"):""};v.getRequestParams=e=>{const a=new Object;if(e.indexOf("?")!=-1){const o=e.split("?")[1].split("&");for(let n=0;n<o.length;n++){const i=o[n].split("=");a[i[0]]=decodeURIComponent(i[1])}}return a};v.attachUrl=e=>e;v.viewImage=e=>e;v.showFile=e=>e;v.getToken=()=>v.local.get("token");v.toUnixTime=e=>Math.floor(new Date(e).getTime()/1e3);v.getColor=(e,a,o=[])=>{if(!a)return"";if(o&&o.length>0){const n=a.findIndex(i=>i.value==e);return o[n]??""}else{const n=a.find(i=>i.value==e);return(n==null?void 0:n.color)??""}};v.getLabel=(e,a)=>{if(!a)return"";const o=a.find(n=>n.value==e);return(o==null?void 0:o.label)??""};const en=[{name:"dashboard",path:"/dashboard",meta:{title:"仪表盘",icon:"icon-dashboard",type:"M",affix:!0},component:()=>w(()=>import("./index-CjSAeS3E.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72]))},{name:"userCenter",path:"/usercenter",meta:{title:"个人信息",icon:"icon-user",type:"M"},component:()=>w(()=>import("./index-DRV136vu.js"),__vite__mapDeps([73,74,75,44,3,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,76,77]))},{name:"appStore",path:"https://saas.saithink.top/#/appStore",meta:{title:"插件市场",icon:"icon-apps",type:"L"}}],Ta={name:"home",path:"/home",meta:{title:"首页",icon:"icon-home",hidden:!1,type:"M"}},za=[{name:"layout",path:"/",component:()=>w(()=>import("./index-CgeF7ukd.js"),__vite__mapDeps([78,3,9,79,44,66,6,62,63,8,10,11,12,13,14,15,16,17,18,19,20,21,22,80,74,52,53,4,5,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,54,55,56,57,58,59,60,61,81])),redirect:"dashboard",children:en},{name:"login",path:"/login",component:()=>w(()=>import("./login-BtLUskZ_.js"),__vite__mapDeps([82,79,44,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,83])),meta:{title:"登录"}},{path:"/:pathMatch(.*)*",hidden:!0,meta:{title:"访问的页面不存在"},component:()=>w(()=>import("./404-C8qIY0DY.js"),__vite__mapDeps([84,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,85]))}],an="SaiAdmin",on="/",nn=["login"],te=jo({history:Po(),routes:za});te.beforeEach(async(e,a,o)=>{De.start();const n=sa();let i=e.meta.title?e.meta.title:e.name;if(document.title=`${i} - ${an}`,v.local.get("token")){if(e.name==="login"){o({path:on});return}!n.user&&n.user==null?await n.requestUserInfo()&&o({path:e.path,query:e.query}):o()}else nn.includes(e.name)?o():o({name:"login",query:{redirect:e.fullPath}})});te.afterEach((e,a)=>{De.done()});te.onError(e=>{De.done()});const cn={VITE_APP_PROXY_PREFIX:"",VITE_APP_TOKEN_PREFIX:"token"};function dn(){const e=ja.create();return e.interceptors.request.use(a=>a,a=>Promise.reject(a)),e.interceptors.response.use(a=>a,a=>{Promise.reject(a.response??null)}),e}function ln(){const e=ja.create();return e.interceptors.request.use(a=>a,a=>(console.log(a),Promise.reject(a))),e.interceptors.response.use(a=>(a.headers["content-disposition"]||!/^application\/json/.test(a.headers["content-type"]))&&a.status===200?a:(a.data.size?(a.data.code=500,a.data.message="服务器内部错误",a.data.success=!1):a.data.code&&a.data.code!==200&&(a.data.code===401?Re(()=>{W.error({content:a.data.message||a.data.msg,icon:()=>ea(aa.IconFaceFrownFill)}),v.local.clear(),te.push({name:"login"})})():W.error({content:a.data.message||a.data.msg,icon:()=>ea(aa.IconFaceFrownFill)})),a.data),a=>{const o=n=>{W.error({content:a.response&&a.response.data&&a.response.data.message?a.response.data.message:n,icon:()=>ea(aa.IconFaceFrownFill)})};if(a.response&&a.response.data)switch(a.response.status){case 404:o("服务器资源不存在");break;case 500:o("服务器内部错误");break;case 401:Re(()=>{o("登录状态已过期，需要重新登录"),v.local.clear(),te.push({name:"login"})})();break;case 403:o("没有权限访问该资源");break;default:o("未知错误！")}else o("请求超时，服务器无响应！");return Promise.resolve({code:a.response.status||500,message:a.response.statusText||"未知错误"})}),e}function Re(e,a=1500){return function(){let o=this;Re.timer||(e.apply(o,arguments),Re.timer=setTimeout(function(){Re.timer=null},a))}}function tn(e){return Eo.stringify(e,{allowDots:!0,encode:!1})}function mn(e){return e?`Bearer ${e}`:null}function rn(e,a){return function(o){const n=cn,i=v.local.get(n.VITE_APP_TOKEN_PREFIX),c=v.local.get("setting"),t={headers:Object.assign({Authorization:mn(i),"Accept-Language":(c==null?void 0:c.language)||"zh_CN","Content-Type":J.get(o,"headers.Content-Type","application/json;charset=UTF-8")},o.headers),timeout:1e4,data:{}};delete o.headers;const m=Object.assign(t,o);return J.isEmpty(m.params)||(m.url=m.url+"?"+tn(m.params),m.params={}),/^(http|https)/g.test(m.url)?a(m):(m.baseURL=n.VITE_APP_PROXY_PREFIX,e(m))}}const sn=ln(),un=dn(),Y=rn(sn,un),Ca={getCaptch(){return Y({url:"/core/captcha",method:"get"})},login(e={}){return Y({url:"/core/login",method:"post",data:e})},logout(e={}){return Y({url:"/core/logout",method:"post",data:e})},getInfo(e={}){return Y({url:"/core/system/user",method:"get",data:e})}},sa=qe("user",{state:()=>({codes:void 0,roles:void 0,routers:void 0,user:void 0,menus:void 0}),getters:{getState(){return{...this.$state}}},actions:{setToken(e){v.local.set("token",e)},getToken(){return v.local.get("token")},clearToken(){v.local.remove("token")},setInfo(e){this.$patch(e)},resetUserInfo(){this.$reset()},setMenu(e){qa(Ma(e)).map(o=>{J.isUndefined(o.meta.layout)||o.meta.layout?te.addRoute("layout",o):te.addRoute(o)})},requestUserInfo(){return new Promise((e,a)=>{Ca.getInfo().then(async o=>{!o||!o.data?(this.clearToken(),await te.push({name:"login"}),a(!1)):(this.setInfo(o.data),await Ie().initData(),Ta.children=za[0].children,this.setMenu(this.routers),this.routers=Fa(this.routers),this.routers.unshift(Ta),await this.setApp(),e(o.data))})})},login(e){return Ca.login(e).then(a=>a.code===200?(this.setToken(a.data.access_token),!0):!1).catch(a=>(console.error(a),!1))},async logout(){const e=ba();v.local.remove("tags"),e.clearTags(),this.clearToken(),this.resetUserInfo()},async setApp(){const e=ua(),a=typeof this.user.backend_setting=="string"?JSON.parse(this.user.backend_setting):this.user.backend_setting;e.toggleMode((a==null?void 0:a.mode)??e.mode),e.toggleMenu((a==null?void 0:a.menuCollapse)??e.menuCollapse),e.toggleTag((a==null?void 0:a.tag)??e.tag),e.toggleRound((a==null?void 0:a.roundOpen)??e.roundOpen),e.toggleWs((a==null?void 0:a.ws)??e.ws),e.changeMenuWidth((a==null?void 0:a.menuWidth)??e.menuWidth),e.changeLayout((a==null?void 0:a.layout)??e.layout),e.useSkin((a==null?void 0:a.skin)??e.skin),e.changeColor((a==null?void 0:a.color)??e.color),e.toggleWater((a==null?void 0:a.waterMark)??e.waterMark),e.changeWaterContent((a==null?void 0:a.waterContent)??e.waterContent)}}}),qa=(e,a=[])=>{let o=[];return e.forEach(n=>{const i={...n};if(i.children){let c=[...a];c.push(n);let t={...n};t.meta.breadcrumb=c,delete t.children,o.push(t),qa(i.children,c).map(b=>{o.push(b)})}else{let c=[...a];c.push(i),i.meta.breadcrumb=c,o.push(i)}}),o},bn=Object.assign({"../../views/dashboard/components/components/st-announced.vue":()=>w(()=>import("./st-announced-CLQoQ4wT.js"),__vite__mapDeps([71,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/dashboard/components/components/st-count.vue":()=>w(()=>import("./st-count-DHnX2X1-.js"),__vite__mapDeps([2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64])),"../../views/dashboard/components/components/st-loginChart.vue":()=>w(()=>import("./st-loginChart-Ce9YXVXR.js"),__vite__mapDeps([68,58,59,60,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,61,62,63,69])),"../../views/dashboard/components/components/st-saiadmin.vue":()=>w(()=>import("./st-saiadmin-75xpyoM0.js"),__vite__mapDeps([70,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/dashboard/components/components/st-welcome.vue":()=>w(()=>import("./st-welcome-CzRY8ecE.js"),__vite__mapDeps([65,12,8,3,9,10,11,13,14,15,16,17,18,19,20,21,22,66,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,67])),"../../views/dashboard/components/statistics.vue":()=>w(()=>import("./statistics-C7hjx8ku.js"),__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71])),"../../views/dashboard/components/work-panel.vue":()=>w(()=>import("./work-panel-BO2MRm6y.js"),__vite__mapDeps([72,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/dashboard/index.vue":()=>w(()=>import("./index-CjSAeS3E.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72])),"../../views/dashboard/userCenter/components/modifyPassword.vue":()=>w(()=>import("./modifyPassword-CrighWRv.js"),__vite__mapDeps([75,74,44,3,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/dashboard/userCenter/components/userInfomation.vue":()=>w(()=>import("./userInfomation-DALxJbDz.js"),__vite__mapDeps([76,74,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/dashboard/userCenter/index.vue":()=>w(()=>import("./index-DRV136vu.js"),__vite__mapDeps([73,74,75,44,3,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,76,77])),"../../views/login.vue":()=>w(()=>import("./login-BtLUskZ_.js"),__vite__mapDeps([82,79,44,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,83])),"../../views/saicode/components/editInfo.vue":()=>w(()=>import("./editInfo-DXBGScbZ.js"),__vite__mapDeps([86,3,87,88,89,90,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,91,92,93])),"../../views/saicode/components/formDesign.vue":()=>w(()=>import("./formDesign-BDBYukjt.js"),__vite__mapDeps([94,24,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,80,92,87,89,95,4,5,6,7,23,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,96,97])),"../../views/saicode/components/loadTable.vue":()=>w(()=>import("./loadTable-hzlR-U5D.js"),__vite__mapDeps([98,87,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/saicode/components/preview.vue":()=>w(()=>import("./preview-Bqm1bMFJ.js"),__vite__mapDeps([99,87,100,47,48,3,101,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,102])),"../../views/saicode/components/settingComponent.vue":()=>w(()=>import("./settingComponent-BvoGVFoj.js"),__vite__mapDeps([90,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,91])),"../../views/saicode/components/table.vue":()=>w(()=>import("./table-BjY8BMqW.js"),__vite__mapDeps([95,24,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,87,4,5,6,7,23,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,96])),"../../views/saicode/index.vue":()=>w(()=>import("./index-XqCN5tvw.js"),__vite__mapDeps([103,87,98,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,99,100,101,102,86,88,89,90,91,92,93,94,80,95,96,97])),"../../views/system/attachment/index.vue":()=>w(()=>import("./index-DzkS705W.js"),__vite__mapDeps([104,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,105])),"../../views/system/config/components/add-group.vue":()=>w(()=>import("./add-group-D5MnfTKt.js"),__vite__mapDeps([106,107,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/config/components/edit.vue":()=>w(()=>import("./edit-DjscfkpF.js"),__vite__mapDeps([108,107,100,47,48,3,101,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/config/components/manage-config.vue":()=>w(()=>import("./manage-config-DHBeLiDi.js"),__vite__mapDeps([109,107,108,100,47,48,3,101,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/config/index.vue":()=>w(()=>import("./index-ChWaTs2V.js"),__vite__mapDeps([110,3,107,106,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,109,108,100,101])),"../../views/system/database/index.vue":()=>w(()=>import("./index-C6WqqdCm.js"),__vite__mapDeps([111,112,113,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,114])),"../../views/system/database/recycle.vue":()=>w(()=>import("./recycle-08ccYGa5.js"),__vite__mapDeps([113,112,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/database/struct.vue":()=>w(()=>import("./struct-BuDD2Spc.js"),__vite__mapDeps([114,112,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/dept/edit.vue":()=>w(()=>import("./edit-D_EZLa8V.js"),__vite__mapDeps([115,116,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/dept/index.vue":()=>w(()=>import("./index-CYLgbsWO.js"),__vite__mapDeps([117,115,116,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,118])),"../../views/system/dept/leader.vue":()=>w(()=>import("./leader-CBXB0tWK.js"),__vite__mapDeps([118,116,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/dict/dataList.vue":()=>w(()=>import("./dataList-BdaY1nZd.js"),__vite__mapDeps([119,89,120,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/dict/edit-data.vue":()=>w(()=>import("./edit-data-D-Kheltb.js"),__vite__mapDeps([120,89,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/dict/edit.vue":()=>w(()=>import("./edit-DyIQuM4D.js"),__vite__mapDeps([121,89,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/dict/index.vue":()=>w(()=>import("./index-SzGozy2_.js"),__vite__mapDeps([122,89,121,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,119,120])),"../../views/system/logs/emailLog.vue":()=>w(()=>import("./emailLog-C2m1eBdk.js"),__vite__mapDeps([123,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/logs/loginLog.vue":()=>w(()=>import("./loginLog-BblWFeAK.js"),__vite__mapDeps([124,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/logs/operLog.vue":()=>w(()=>import("./operLog-CpGgCm9d.js"),__vite__mapDeps([125,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/menu/edit.vue":()=>w(()=>import("./edit-Dj6-BOG2.js"),__vite__mapDeps([126,47,48,88,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/menu/index.vue":()=>w(()=>import("./index-DpRapO2z.js"),__vite__mapDeps([127,88,126,47,48,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,128])),"../../views/system/monitor/server/index.vue":()=>w(()=>import("./index-CaZcXSlq.js"),__vite__mapDeps([129,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,130])),"../../views/system/notice/edit.vue":()=>w(()=>import("./edit-CyUWeZne.js").then(e=>e.e),__vite__mapDeps([131,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22])),"../../views/system/notice/index.vue":()=>w(()=>import("./index-BlmJTHZZ.js"),__vite__mapDeps([132,131,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/post/edit.vue":()=>w(()=>import("./edit-a5eHS414.js"),__vite__mapDeps([133,134,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/post/index.vue":()=>w(()=>import("./index-D8t8-Vcv.js"),__vite__mapDeps([135,133,134,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,136])),"../../views/system/post/view.vue":()=>w(()=>import("./view-Df7wOQNg.js"),__vite__mapDeps([136,134,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/role/components/menuPermission.vue":()=>w(()=>import("./menuPermission-B9LCPnXg.js"),__vite__mapDeps([137,138,88,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,139])),"../../views/system/role/edit.vue":()=>w(()=>import("./edit-C4LjrAZO.js"),__vite__mapDeps([140,138,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/role/index.vue":()=>w(()=>import("./index-BFIUdcGG.js"),__vite__mapDeps([141,138,137,88,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,139,140])),"../../views/system/user/edit.vue":()=>w(()=>import("./edit-xZRcGvRF.js"),__vite__mapDeps([142,74,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/system/user/index.vue":()=>w(()=>import("./index-DaXTbXVv.js"),__vite__mapDeps([143,74,142,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,66])),"../../views/tool/code/components/editInfo.vue":()=>w(()=>import("./editInfo-ChAN-DIk.js"),__vite__mapDeps([144,3,145,112,88,89,146,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,147,148])),"../../views/tool/code/components/loadTable.vue":()=>w(()=>import("./loadTable-BFTONwXN.js"),__vite__mapDeps([149,112,145,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/tool/code/components/preview.vue":()=>w(()=>import("./preview-F38rEx2-.js"),__vite__mapDeps([150,145,100,47,48,3,101,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,151])),"../../views/tool/code/components/settingComponent.vue":()=>w(()=>import("./settingComponent-C9EG_z5A.js"),__vite__mapDeps([146,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,147])),"../../views/tool/code/index.vue":()=>w(()=>import("./index-Ymm_9TE1.js"),__vite__mapDeps([152,145,149,112,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,150,100,101,151,144,88,89,146,147,148])),"../../views/tool/crontab/edit.vue":()=>w(()=>import("./edit-D9KKKNcV.js"),__vite__mapDeps([153,154,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/tool/crontab/index.vue":()=>w(()=>import("./index-BqLpuig7.js"),__vite__mapDeps([155,154,156,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,153])),"../../views/tool/crontab/logList.vue":()=>w(()=>import("./logList-DmhpOLmd.js"),__vite__mapDeps([156,154,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,6,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/tool/crontab/view.vue":()=>w(()=>import("./view-DkYlBne4.js"),__vite__mapDeps([157,134,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])),"../../views/tool/install/index.vue":()=>w(()=>import("./index-CNx4HHXc.js"),__vite__mapDeps([158,159,49,50,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,51,160,6,4,5,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,52,53,54,55,56,57,58,59,60,61,62,63,161,162])),"../../views/tool/install/install-box.vue":()=>w(()=>import("./install-box-UcToEVc_.js").then(e=>e.i),__vite__mapDeps([159,49,50,8,3,9,10,11,12,13,14,15,16,17,18,19,20,21,22,51])),"../../views/tool/install/terminal.vue":()=>w(()=>import("./terminal-C7g1OY6E.js"),__vite__mapDeps([160,6,3,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,4,5,7,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,161]))}),Ma=e=>{const a=[];return e.forEach(o=>{if(o.meta.type!=="B"){o.meta.type==="I"&&(o.meta.url=o.path,o.path=`/maIframe/${o.name}`);const n={path:o.path,name:o.name,hidden:o.hidden===1,meta:o.meta,children:o.children?Ma(o.children):null,component:bn[`../../views/${o.component}.vue`]};a.push(n)}}),a},Fa=e=>{let a=[];return e.forEach(o=>{if(o.meta.type!=="B"&&!o.meta.hidden){let n=o;o.children&&o.children.length>0&&(n.children=Fa(o.children)),a.push(n)}}),a};let N={mode:"light",tag:!0,menuCollapse:!1,menuWidth:230,layout:"classic",skin:"mine",i18n:!1,language:"zh_CN",animation:"ma-slide-down",color:"#7166F0",settingOpen:!1,searchOpen:!1,roundOpen:!0,waterMark:!0,waterContent:"saiadmin",ws:!1,registerWangEditorButtonFlag:!1};v.local.get("setting")?N=v.local.get("setting"):v.local.set("setting",N);document.body.setAttribute("arco-theme",N.mode);document.body.setAttribute("mine-skin",N.skin);const ua=qe("app",{state:()=>({...N}),getters:{appCurrentSetting(){return{...this.$state}}},actions:{updateSettings(e){this.$patch(e)},toggleMode(e){this.mode=e,document.getElementsByTagName("html")[0].className=this.mode,document.body.setAttribute("arco-theme",this.mode),N.mode=this.mode,this.changeColor(this.color),v.local.set("setting",N)},toggleMenu(e){this.menuCollapse=e,N.menuCollapse=this.menuCollapse,v.local.set("setting",N)},toggleRound(e){this.roundOpen=e,this.roundOpen?(document.body.style.setProperty("--border-radius-small","4px"),document.body.style.setProperty("--border-radius-medium","6px")):(document.body.style.setProperty("--border-radius-small","2px"),document.body.style.setProperty("--border-radius-medium","4px")),N.roundOpen=this.roundOpen,v.local.set("setting",N)},toggleWater(e){this.waterMark=e,N.waterMark=this.waterMark,v.local.set("setting",N)},changeWaterContent(e){this.waterContent=e.target?e.target.value:e,N.waterContent=this.waterContent,v.local.set("setting",N)},toggleTag(e){this.tag=e,N.tag=this.tag,v.local.set("setting",N)},toggleI18n(e){this.i18n=e,N.i18n=this.i18n,v.local.set("setting",N)},toggleWs(e){this.ws=e,N.ws=this.ws,v.local.set("setting",N)},changeMenuWidth(e){this.menuWidth=e,N.menuWidth=this.menuWidth,v.local.set("setting",N)},changeLayout(e){this.layout=e,N.layout=this.layout,v.local.set("setting",N)},changeLanguage(e){this.language=e,N.language=this.language,v.local.set("setting",N),window.location.reload()},changeColor(e){if(!/^#[0-9A-Za-z]{6}/.test(e))return;this.color=e,Io(this.color,{list:!0,dark:this.mode==="dark"}).forEach((o,n)=>{const i=To(o);document.body.style.setProperty(`--primary-${n+1}`,i),document.body.style.setProperty(`--arcoblue-${n+1}`,i)}),N.color=this.color,v.local.set("setting",N)},changeAnimation(e){this.animation=e,N.animation=this.animation,v.local.set("setting",N)},useSkin(e){this.skin=e,N.skin=this.skin,document.body.setAttribute("mine-skin",this.skin),v.local.set("setting",N)},setRegisterWangEditorButtonFlag(e){this.registerWangEditorButtonFlag=e}}}),oa=[{name:"dashboard",title:"仪表盘",path:"/dashboard",affix:!0}],ba=qe("tag",{state:()=>({tags:!v.local.get("tags")||v.local.get("tags").length===0?oa:v.local.get("tags")}),getters:{getState(){return{...this.$state}}},actions:{addTag(e){!this.tags.find(o=>o.path===e.path)&&e.path&&this.tags.push(e),this.updateTagsToLocal()},removeTag(e){let a=0;return this.tags.map((o,n)=>{o.path===e.path&&!o.affix&&(this.tags[n+1]?a=n:n>0&&(a=n-1),this.tags.splice(n,1))}),this.updateTagsToLocal(),this.tags[a]},updateTag(e){this.tags.map(a=>{a.path==e.path&&(a=Object.assign(a,e))}),this.updateTagsToLocal()},updateTagTitle(e,a){this.tags.map(o=>{o.path==e&&(o.customTitle=a)}),this.updateTagsToLocal()},updateTagsToLocal(){v.local.set("tags",this.tags)},clearTags(){this.tags=oa,v.local.set("tags",oa)}}}),pa=qe("keepAlive",{state:()=>({keepAlives:[],show:!0}),getters:{getState(){return{...this.$state}}},actions:{addKeepAlive(e){e.path.indexOf("maIframe")>-1||this.keepAlives.includes(e.name)||this.keepAlives.push(e.name)},removeKeepAlive(e){const a=this.keepAlives.indexOf(e.name);a!==-1&&this.keepAlives.splice(a,1)},display(){this.show=!0},hidden(){this.show=!1},clearKeepAlive(){this.keepAlives=[]}}}),me={getUserList(e={}){return Y({url:"/core/system/getUserList",method:"get",params:e})},getUserInfoByIds(e={}){return Y({url:"/core/system/getUserInfoByIds",method:"post",data:e})},getNoticeList(e={}){return Y({url:"/core/system/notice",method:"get",params:e})},getStatistics(e={}){return Y({url:"/core/system/statistics",method:"get",params:e})},loginChart(e={}){return Y({url:"/core/system/loginChart",method:"get",params:e})},clearAllCache(){return Y({url:"/core/system/clearAllCache",method:"get"})},uploadImage(e={}){return Y({url:"/core/system/uploadImage",method:"post",timeout:3e4,data:e})},uploadFile(e={}){return Y({url:"/core/system/uploadFile",method:"post",timeout:3e4,data:e})},saveNetWorkImage(e={}){return Y({url:"/core/system/saveNetworkImage",method:"post",data:e})},getLoginLogList(e={}){return Y({url:"/core/system/getLoginLogList",method:"get",params:e})},getOperationLogList(e={}){return Y({url:"/core/system/getOperationLogList",method:"get",params:e})},getResourceList(e={}){return Y({url:"/core/system/getResourceList",method:"get",params:e})},importExcel(e,a){return Y({url:e,method:"post",data:a,timeout:30*1e3})},download(e,a="post"){return Y({url:e,method:a,responseType:"blob"})},commonGet(e,a={}){return Y({url:e,method:"get",params:a})},dictAll(){return Y({url:"/core/system/dictAll",method:"get"})},downloadById(e){return Y({url:"/core/system/downloadById?id="+e,responseType:"blob",method:"get"})},downloadByHash(e){return Y({url:"/core/system/downloadByHash?hash="+e,responseType:"blob",method:"get"})}},Ie=qe("dict",{state:()=>({data:void 0}),getters:{getState(){return{...this.$state}}},actions:{setInfo(e){this.$patch(e)},async initData(){const{data:e}=await me.dictAll();this.data=e}}}),pn=Ao(),fn={style:{"z-index":"100",border:"1px solid #ccc",width:"100%"}},hn={__name:"index",props:{modelValue:{type:String},component:Object,height:{type:Number,default:300},mode:{type:String,default:"default"},customField:{type:String,default:void 0}},emits:["update:modelValue","change"],setup(e,{emit:a}){const o=_(!1),n=ua(),i=e,c=a;let t=n.appCurrentSetting.registerWangEditorButtonFlag;const m=_([]),b=_();let x=ze({get(){return i.modelValue},set(f){c("update:modelValue",f)}});X(()=>x.value,f=>c("change",f)),X(()=>m.value,f=>{f.map(E=>{if(E.indexOf(".jpg")>-1||E.indexOf(".png")>-1||E.indexOf(".bmp")>-1||E.indexOf(".jpeg")>-1||E.indexOf(".svg")>-1||E.indexOf(".gif")>-1){const M={type:"image",src:E,href:"",alt:"",style:{},children:[{text:""}]};S.value.insertNode(M)}}),b.value.clearSelecteds(),o.value=!1});const S=po(),g={};g.excludeKeys=["group-video","insertImage"];class h{constructor(){this.title="资源选择器",this.tag="button"}getValue(E){return""}isActive(E){return!1}isDisabled(E){return!1}exec(E,M){E.emit("click_menu")}}const p={key:"menu1",factory(){return new h}};(t===void 0||t===!1)&&(wo.registerMenu(p),n.setRegisterWangEditorButtonFlag(!0)),g.insertKeys={index:1,keys:["menu1"]};const k={placeholder:"请输入内容...",MENU_CONF:{},hoverbarKeys:{link:{menuKeys:["imageWidth30","imageWidth50","imageWidth100","|","imageFloatNone","imageFloatLeft","imageFloatRight","|","editImage","viewImageLink","deleteImage"]}}};k.MENU_CONF.uploadImage={async customUpload(f,E){O(f,"image","uploadImage").then(M=>{E(v.attachUrl(M.url))})}};const O=async(f,E,M,j={})=>{const u=await ma(f),H=new FormData;H.append(E,f),H.append("isChunk",!1),H.append("hash",u);for(let ee in j)H.append(ee,j[ee]);return(await me[M](H)).data},I=f=>{S.value=f,S.value.on("click_menu",()=>{o.value=!0})};return fo(()=>{const f=S.value;f!=null&&f.destroy()}),(f,E)=>{const M=s("sa-resource"),j=s("a-modal");return r(),C("div",fn,[l(B(xo),{style:{"border-bottom":"1px solid #ccc"},editor:S.value,defaultConfig:g,mode:e.mode},null,8,["editor","mode"]),l(B(So),{style:Ee({height:i.height+"px",overflowY:"hidden"}),modelValue:B(x),"onUpdate:modelValue":E[0]||(E[0]=u=>ho(x)?x.value=u:x=u),defaultConfig:k,mode:i.mode,onOnCreated:I},null,8,["style","modelValue","mode"]),l(j,{style:{"z-index":"1000"},visible:o.value,"onUpdate:visible":E[2]||(E[2]=u=>o.value=u),"render-to-body":!1,width:1080,footer:!1,draggable:""},{title:d(()=>E[3]||(E[3]=[L("资源选择器")])),default:d(()=>[l(M,{modelValue:m.value,"onUpdate:modelValue":E[1]||(E[1]=u=>m.value=u),multiple:"",ref_key:"resource",ref:b,returnType:"url"},null,8,["modelValue"])]),_:1},8,["visible"])])}}},gn={__name:"index",props:{modelValue:String,placeholder:{type:String,default:"请选择颜色"}},emits:["update:modelValue"],setup(e,{emit:a}){const o=e,n=a,i=ze({get(){return o.modelValue},set(b){n("update:modelValue",b)}}),c=b=>{i.value=b.hex},t=async()=>{try{await ra().toClipboard(i.value),W.success("复制成功")}catch{W.error("复制失败")}},m=ca(["#165DFF","#F53F3F","#F77234","#F7BA1E","#00B42A","#14C9C9","#3491FA","#722ED1","#F5319D","#D91AD9","#34C759","#43a047","#7cb342","#c0ca33","#86909c","#6d4c41"]);return(b,x)=>{const S=s("a-button"),g=s("a-trigger"),h=s("a-input"),p=s("icon-copy"),k=s("a-tooltip"),O=s("a-input-group");return r(),A(O,{class:"w-full"},{default:d(()=>[l(g,{position:"bottom",trigger:"click","auto-fit-position":"","unmount-on-close":!1},{content:d(()=>[l(B(Do),{theme:"dark",color:i.value,"sucker-hide":!0,"colors-default":m,onChangeColor:c,style:{width:"218px"}},null,8,["color","colors-default"])]),default:d(()=>[l(S,{type:"primary"},{default:d(()=>x[1]||(x[1]=[L("选择颜色")])),_:1})]),_:1}),l(h,{modelValue:i.value,"onUpdate:modelValue":x[0]||(x[0]=I=>i.value=I),style:Ee(`color: ${i.value}`),placeholder:o.placeholder},null,8,["modelValue","style","placeholder"]),l(k,{content:"复制"},{default:d(()=>[l(S,{onClick:t},{icon:d(()=>[l(p,{class:"cursor-pointer"})]),_:1})]),_:1})]),_:1})}}},vn={__name:"index",props:{modelValue:[Number,String,Object],type:{type:String,default:"select"},mode:{type:String,default:"name"}},emits:["update:modelValue"],setup(e,{emit:a}){const o=_(),n=_({province:[],city:[],area:[]}),i=_([]),c=_([]),t=_([]),m=a,b=e;b.type==="select"&&(i.value=Be.map(h=>({code:h.code,name:h.name})));const x=(h,p=!0)=>{p&&(n.value.city=[],n.value.area=[],t.value=[],c.value=[]),Be.map(k=>{b.mode=="name"&&h==k.name&&(c.value=k.children),b.mode=="code"&&h==k.code&&(c.value=k.children)})},S=(h,p=!0)=>{p&&(n.value.area=[],t.value=[]),c.value.map(k=>{b.mode=="name"&&h==k.name&&(t.value=k.children),b.mode=="code"&&h==k.code&&(t.value=k.children)})},g=()=>{b.type==="select"&&o.value&&J.isObject(o.value)&&(n.value.province=o.value.province?o.value.province:"",n.value.city=o.value.city?o.value.city:"",n.value.area=o.value.area?o.value.area:"",n.value.province&&x(n.value.province,!1),n.value.city&&n.value.province&&S(n.value.city,!1))};return o.value=b.modelValue,X(()=>b.modelValue,h=>{o.value=h,g()},{deep:!0}),X(()=>o.value,h=>m("update:modelValue",h)),X(()=>n.value,h=>m("update:modelValue",h),{deep:!0}),g(),(h,p)=>{const k=s("a-cascader"),O=s("a-select"),I=s("a-space");return b.type==="cascader"?(r(),A(k,{key:0,modelValue:o.value,"onUpdate:modelValue":p[0]||(p[0]=f=>o.value=f),"field-names":b.mode=="name"?{value:"name",label:"name"}:{value:"code",label:"name"},options:B(Be),"allow-search":"","check-strictly":"","expand-trigger":"hover","path-mode":"",placeholder:"请选择省市区"},null,8,["modelValue","field-names","options"])):(r(),A(I,{key:1},{default:d(()=>[l(O,{modelValue:n.value.province,"onUpdate:modelValue":p[1]||(p[1]=f=>n.value.province=f),"field-names":b.mode=="name"?{value:"name",label:"name"}:{value:"code",label:"name"},options:i.value,style:{width:"220px"},"allow-clear":"","allow-search":"",placeholder:"请选择省/直辖市/自治区",onChange:x,onClear:p[2]||(p[2]=()=>{n.value.city=[],n.value.area=[],n.value.province=[],n.value.city=[],n.value.area=[],i.value.value=[]})},null,8,["modelValue","field-names","options"]),l(O,{modelValue:n.value.city,"onUpdate:modelValue":p[3]||(p[3]=f=>n.value.city=f),"field-names":b.mode=="name"?{value:"name",label:"name"}:{value:"code",label:"name"},options:c.value,style:{width:"220px"},"allow-clear":"","allow-search":"",placeholder:"请选择地级市/市辖区",onChange:S,onClear:p[4]||(p[4]=()=>{n.value.city=[],n.value.area=[],n.value.city=[],n.value.area=[]})},null,8,["modelValue","field-names","options"]),l(O,{modelValue:n.value.area,"onUpdate:modelValue":p[5]||(p[5]=f=>n.value.area=f),"field-names":b.mode=="name"?{value:"name",label:"name"}:{value:"code",label:"name"},options:t.value,style:{width:"220px"},"allow-clear":"","allow-search":"",placeholder:"请选择区县",onClear:p[6]||(p[6]=()=>{n.value.area=[],n.value.area=[]})},null,8,["modelValue","field-names","options"])]),_:1}))}}},yn={__name:"index",props:{options:{type:Object,default(){return{}}},autoresize:{type:Boolean,default:!0},width:{type:String,default:"100%"},height:{type:String,default:"100%"}},setup(e){const a=_(!1);return je(()=>{a.value=!0}),(o,n)=>a.value?(r(),A(B(zo),{key:0,option:e.options,autoresize:e.autoresize,style:Ee({width:e.width,height:e.height})},null,8,["option","autoresize","style"])):U("",!0)}},_n={__name:"index",props:{modelValue:{type:Array,default:()=>[]},dict:{type:String,default:""},disabled:{type:Boolean,default:!1},direction:{type:String,default:"horizontal"}},emits:["update:modelValue","change"],setup(e,{emit:a}){const o=Ie().data,n=a,i=_(),c=e;X(()=>c.modelValue,m=>{i.value=m},{immediate:!0}),X(()=>i.value,m=>{n("update:modelValue",i.value)});const t=async m=>{n("change",m)};return(m,b)=>{const x=s("a-checkbox"),S=s("a-checkbox-group");return r(),A(S,{modelValue:i.value,"onUpdate:modelValue":b[0]||(b[0]=g=>i.value=g),direction:c.direction,disabled:c.disabled,onChange:b[1]||(b[1]=g=>t(g))},{default:d(()=>[(r(!0),C(oe,null,de(B(o)[c.dict]??[],(g,h)=>(r(),A(x,{value:g.value},{default:d(()=>[L(V(g.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue","direction","disabled"])}}},kn={__name:"index",props:{modelValue:{type:[String,Number]},type:{type:String,default:"radio"},dict:{type:String,default:""},disabled:{type:Boolean,default:!1},direction:{type:String,default:"horizontal"},allowNull:{type:Boolean,default:!1},nullValue:{type:[String,Number],default:""},nullLabel:{type:String,default:"全部"}},emits:["update:modelValue","change"],setup(e,{emit:a}){const o=Ie().data,n=a,i=_(),c=e;X(()=>c.modelValue,m=>{c.dict!==""?i.value=m+"":i.value=m},{immediate:!0}),X(()=>i.value,m=>{n("update:modelValue",i.value)});const t=async m=>{n("update:modelValue",m),n("change",m)};return(m,b)=>{const x=s("a-radio"),S=s("a-radio-group");return r(),A(S,{modelValue:i.value,"onUpdate:modelValue":b[0]||(b[0]=g=>i.value=g),direction:c.direction,type:c.type,disabled:c.disabled,onChange:b[1]||(b[1]=g=>t(g))},{default:d(()=>[c.allowNull?(r(),A(x,{key:0,value:c.nullValue},{default:d(()=>[L(V(c.nullLabel),1)]),_:1},8,["value"])):U("",!0),(r(!0),C(oe,null,de(B(o)[c.dict]??[],(g,h)=>(r(),A(x,{value:g.value},{default:d(()=>[L(V(g.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue","direction","type","disabled"])}}},wn={__name:"index",props:{modelValue:{type:[String,Number]},fieldNames:{type:Object,default:{value:"value",label:"label"}},size:{type:String,default:"medium"},style:{type:Object,default:{}},dict:{type:String,default:""},placeholder:{type:String,default:"请选择"},disabled:{type:Boolean,default:!1},allowClear:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(e,{emit:a}){const o=Ie().data,n=a,i=_(),c=e;X(()=>c.modelValue,m=>{c.dict!==""?i.value=m+"":i.value=m},{immediate:!0}),X(()=>i.value,m=>{n("update:modelValue",i.value)});const t=async m=>{n("update:modelValue",m),n("change",m)};return(m,b)=>{const x=s("a-select");return r(),A(x,{modelValue:i.value,"onUpdate:modelValue":b[0]||(b[0]=S=>i.value=S),size:c.size,options:B(o)[c.dict]??[],placeholder:c.placeholder,style:Ee(c.style),disabled:c.disabled,"allow-clear":c.allowClear,onChange:b[1]||(b[1]=S=>t(S))},null,8,["modelValue","size","options","placeholder","style","disabled","allow-clear"])}}},xn={__name:"index",props:{modelValue:{type:[String,Number,Boolean]},size:{type:String,default:"medium"},type:{type:String,default:"round"},valType:{type:String,default:"string"},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},checkedValue:{type:[String,Number,Boolean],default:"1"},uncheckedValue:{type:[String,Number,Boolean],default:"2"},checkedColor:{type:String,default:""},uncheckedColor:{type:String,default:""},checkedText:{type:String,default:"启用"},uncheckedText:{type:String,default:"禁用"}},emits:["update:modelValue","change"],setup(e,{emit:a}){const o=a,n=_(),i=e;X(()=>i.modelValue,t=>{i.valType==="string"?n.value=t+"":n.value=t},{immediate:!0}),X(()=>n.value,t=>{o("update:modelValue",n.value)});const c=async t=>{o("change",t)};return(t,m)=>{const b=s("a-switch");return r(),A(b,{modelValue:n.value,"onUpdate:modelValue":m[0]||(m[0]=x=>n.value=x),size:i.size,disabled:i.disabled,loading:i.loading,type:i.type,"checked-value":i.checkedValue,"unchecked-value":i.uncheckedValue,"checked-color":i.checkedColor,"unchecked-color":i.uncheckedColor,onChange:m[1]||(m[1]=x=>c(x))},{checked:d(()=>[L(V(i.checkedText),1)]),unchecked:d(()=>[L(V(i.uncheckedText),1)]),_:1},8,["modelValue","size","disabled","loading","type","checked-value","unchecked-value","checked-color","unchecked-color"])}}},Sn={id:void 0,pk:"id",api:()=>{},pageSizeOption:[10,20,30,50,100],rowSelection:void 0,bordered:{wrapper:!0,cell:!1},pageSize:10,expandAllRows:!1,showSummary:!1,stripe:!0,size:"large",isExpand:!1,showTools:!0,pageLayout:"fixed",height:0,pageSimple:!1,showSort:!0,showSearch:!0,searchText:"搜索",resetText:"重置",singleLine:!1,view:{func:void 0,auth:[],text:"查看",show:!1},add:{func:void 0,auth:[],text:"新增",show:!1},edit:{func:void 0,auth:[],text:"编辑",show:!1},delete:{func:void 0,auth:[],text:"删除",show:!1,batch:!0},import:{url:void 0,params:{},templateUrl:void 0,auth:[],text:"导入",show:!1},export:{url:void 0,auth:[],text:"导出",show:!1},columnAlign:"left",showIndex:!1,indexLabel:"序号",indexColumnWidth:70,indexColumnFixed:"left",operationColumn:!0,operationColumnWidth:190,operationColumnText:"操作"};class An{constructor(a,o={}){Ke(this,"dom",null);Ke(this,"options",{});if(this.options=this.extend({noPrint:".no-print"},o),typeof a=="string")try{this.dom=document.querySelector(a)}catch{this.dom=document.createElement("div"),this.dom.innerHTML=a}else this.isDOM(a),this.dom=this.isDOM(a)?a:a.$el;this.init()}init(){this.writeIframe(this.getStyle()+this.getHtml())}extend(a,o){for(let n in o)a[n]=o[n];return a}getStyle(){let a="",o=document.querySelectorAll("style,link");for(let n=0;n<o.length;n++)a+=o[n].outerHTML;return a+="<style>"+(this.options.noPrint?this.options.noPrint:".no-print")+"{ display: none; }</style>",a+="<style>html, body{ background-color: #fff; }</style>",a}getHtml(){const a=document.querySelectorAll("input"),o=document.querySelectorAll("textarea"),n=document.querySelectorAll("select");for(let i=0;i<a.length;i++)a[i].type==="checkbox"||a[i].type==="radio"?a[i].checked===!0?a[i].setAttribute("checked","checked"):a[i].removeAttribute("checked"):(a[i].type,a[i].setAttribute("value",a[i].value));for(let i=0;i<o.length;i++)o[i].type==="textarea"&&(o[i].innerHTML=o[i].value);for(let i=0;i<n.length;i++)if(n[i].type==="select-one"){let c=n[i].children;for(let t in c)c[t].tagName==="OPTION"&&(c[t].selected===!0?c[t].setAttribute("selected","selected"):c[t].removeAttribute("selected"))}return this.dom.outerHTML}writeIframe(a){let o,n,i=document.createElement("iframe"),c=document.body.appendChild(i);i.id="myIframe",i.setAttribute("style","position:absolute; width:0; height:0; top:-10px; left:-10px;"),o=c.contentWindow??c.contentDocument,n=c.contentDocument??c.contentWindow.document,n.open(),n.write(a),n.close();const t=this;i.onload=()=>{t.toPrint(o),setTimeout(()=>{document.body.removeChild(i)},100)}}toPrint(a){try{setTimeout(()=>{a.focus();try{a.document.execCommand("print",!1,null)||a.print()}catch{a.print()}a.close()},10)}catch(o){console.log("err",o)}}isDOM(a){return typeof HTMLElement=="object"?a instanceof HTMLElement:a&&typeof a=="object"&&a.nodeType===1&&typeof a.nodeName=="string"}}const En={style:{"background-color":"var(--color-fill-2)",border:"1px dashed var(--color-fill-4)"},class:"rounded text-center p-7"},In={class:"mt-5 italic text-right"},Tn={__name:"import",emits:["success"],setup(e,{expose:a,emit:o}){const n=_(!1),i=go("options"),c=o,t=()=>n.value=!0,m=()=>n.value=!1,b=S=>{W.info("文件上传导入中...");const g=new FormData;g.append("file",S.fileItem.file),i.import.params&&Object.keys(i.import.params).forEach(h=>{g.append(h,i.import.params[h])}),me.importExcel(i.import.url,g).then(async h=>{h.code===200&&W.success(h.message||"导入成功"),c("success"),m()})},x=()=>{W.info("请求服务器下载文件中...");const S=i.import.templateUrl;/^(http|https)/g.test(S)?window.open(S):me.download(S).then(g=>{v.download(g),W.success("请求成功，文件开始下载")})};return a({open:t,close:m}),(S,g)=>{const h=s("icon-upload"),p=s("a-upload"),k=s("a-link"),O=s("a-modal");return r(),A(O,{visible:n.value,"onUpdate:visible":g[0]||(g[0]=I=>n.value=I),width:B(v).getDevice()==="mobile"?"100%":"600px",footer:!1,onCancel:m,draggable:""},{title:d(()=>g[1]||(g[1]=[L("导入")])),default:d(()=>[l(p,{draggable:"","custom-request":b,"show-file-list":!1,accept:".xlsx,.xls"},{"upload-button":d(()=>[q("div",En,[q("div",null,[l(h,{class:"text-5xl text-gray-400"}),g[2]||(g[2]=q("div",{class:"text-red-600 font-bold"},"导入Excel",-1)),g[3]||(g[3]=L(" 将文件拖到此处，或")),g[4]||(g[4]=q("span",{style:{color:"#3370ff"}},"点击上传",-1)),g[5]||(g[5]=L("，只能上传 xls/xlsx 文件 "))])])]),_:1}),q("div",In,[l(k,{onClick:x},{default:d(()=>g[6]||(g[6]=[L("下载Excel模板")])),_:1})])]),_:1},8,["visible","width"])}}},Va="data:image/png;base64,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",ye=(e,a)=>{const o=e.__vccOpts||e;for(const[n,i]of a)o[n]=i;return o},Cn={class:"_crud-content"},Vn={id:"tableSetting"},Ln={style:{"margin-right":"4px"}},On={class:"title"},Rn={key:0},jn=["src"],Pn={key:0},Dn=["src"],zn={key:0,class:"mt-2 text-right"},qn={__name:"index",props:{data:{type:[Function,Array],default:()=>null},options:{type:Object,default:{}},columns:{type:Array,default:[]},searchForm:{type:Object,default:()=>{}}},emits:["resetSearch"],setup(e,{expose:a,emit:o}){const n=e,i=o,c=_(),t=_(),m=_(),b=_(0),x=_(),S=_(!1),g=_(!0),h=_(!0),p=_(),k=_(!1),O=_([]),I=_(),f=_(!1),E=_(!1),M=_(!1),j=_("../../assets/not-image.png"),u=_(Object.assign(JSON.parse(JSON.stringify(Sn)),n.options)),H=_({page:1,limit:u.value.pageSize}),re=_(n.searchForm),ee=_(n.columns),ne=ca({total:0,data:[]});vo("options",u.value);const F=(y,z)=>y.indexOf(".")>-1?J.get(z,y):z[y],P=()=>{var y;(y=I.value)==null||y.selectAll(!1)},Q=y=>{O.value=y},Z=()=>ne.data,_e=()=>ne.total,fe=({data:y})=>{if(u.value.showSummary&&J.isArray(u.value.summary)){const z=u.value.summary;let G={},Ve={},se={},Le=y.length||0;z.map(D=>{D.action&&D.action==="text"?G[D.dataIndex]=D.text:(G[D.dataIndex]=0,Ve[D.dataIndex]=(D==null?void 0:D.prefixText)??"",se[D.dataIndex]=(D==null?void 0:D.suffixText)??"",y.map(ke=>{ke[D.dataIndex]&&(D.action&&D.action==="sum"&&(G[D.dataIndex]+=parseFloat(ke[D.dataIndex])),D.action&&D.action==="avg"&&(G[D.dataIndex]+=parseFloat(ke[D.dataIndex])/Le))}))});for(let D in G)/^\d+(\.\d+)?$/.test(G[D])&&(G[D]=Ve[D]+v.groupSeparator(G[D].toFixed(2))+se[D]);return[G]}},Me=y=>{const z=y+1;return H.value.page===1?z:(H.value.page-1)*H.value.limit+z},Fe=async y=>{H.value.page=y,await he()},He=async y=>{H.value.page=1,H.value.limit=y,await he()},$e=async()=>{await he()},Ne=async()=>{var y;(y=c.value)==null||y.resetFields(),i("resetSearch"),await he()},We=()=>{k.value=!k.value,k.value?I.value.expandAll(!0):I.value.expandAll(!1)},Ge=async(y,z)=>{z?(H.value.orderBy=y,H.value.orderType=z==="ascend"?"asc":"desc",f.value=!0):(H.value.orderBy=void 0,H.value.orderType=void 0,f.value=!1),await he()},Ye=async()=>{g.value=!g.value,await je(()=>{b.value=t.value.offsetHeight,u.value.pageLayout==="fixed"&&Je()})},$=()=>{new An(m.value)},Ce=(y,z,G)=>{z.dataIndex!="__operation"&&(y?z.sortable={sortDirections:["ascend","descend"]}:z.sortable=void 0)},fa=async()=>{var y;if(J.isUndefined(u.value.id)&&(u.value.id="SaCrud_"+Math.floor(Math.random()*1e5+Math.random()*2e4+Math.random()*5e3)),u.value.showIndex&&ee.value.length>0&&ee.value[0].dataIndex!=="__index"&&ee.value.unshift({title:u.value.indexLabel,dataIndex:"__index",width:u.value.indexColumnWidth,fixed:u.value.indexColumnFixed}),ee.value.length>0&&ee.value[ee.value.length-1].dataIndex!=="__operation"&&u.value.operationColumn&&((y=ee.value)==null||y.push({title:u.value.operationColumnText||"操作",dataIndex:"__operation",slotName:"__operation",align:"center",fixed:"right",width:u.value.operationColumnWidth??150})),ee.value.forEach(z=>{z.sortable&&(z.checked=!0)}),f.value){const z=J.cloneDeep(re.value);J.isUndefined(z.orderBy)||(delete z.orderBy,delete z.orderType),H.value=Object.assign(H.value,z)}else H.value=Object.assign(H.value,re.value);u.value.singleLine?h.value=u.value.singleLine:h.value=!(Object.getOwnPropertyNames(n.searchForm).length>3),p.value=u.value.api},he=async()=>{await ha()},ha=async()=>{if(S.value=!0,fa(),J.isFunction(p.value)){const y=await p.value(H.value);y.data&&y.data.data?(ne.total=y.data.total,ne.data=y.data.data):(ne.total=0,ne.data=y.data)}else console.error("sa-table error：crud.api is not Function.");S.value=!1},Ua=()=>{u.value.add.func&&J.isFunction(u.value.add.func)?u.value.add.func():console.error("sa-table error：crud.add.func is not Function.")},ga=y=>{u.value.edit.func&&J.isFunction(u.value.edit.func)?u.value.edit.func(y):console.error("sa-table error：crud.edit.func is not Function.")},Ba=y=>{u.value.view.func&&J.isFunction(u.value.view.func)?u.value.view.func(y):console.error("sa-table error：crud.view.func is not Function.")},va=async y=>{const z={ids:[y[u.value.pk]]};u.value.delete.func&&J.isFunction(u.value.delete.func)?u.value.delete.func(z):console.error("sa-table error：crud.delete.func is not Function.")},Ha=async()=>{var z;const y={ids:O.value};O.value&&O.value.length>0?u.value.delete.func&&J.isFunction(u.value.delete.func)?(u.value.delete.func(y),(z=I.value)==null||z.selectAll(!1)):console.error("sa-table error：crud.delete.func is not Function."):W.error("至少选择一条数据")},Wa=()=>x.value.open(),Ga=()=>{W.info("请求服务器下载文件中...");const y=H.value,z=G=>Y({url:G,data:y,method:"post",timeout:60*1e3,responseType:"blob"});E.value=!0,z(u.value.export.url).then(G=>{G&&G.status==200?(v.download(G),W.success("请求成功，文件开始下载")):W.error("请前往服务端安装Excel导出库")}).catch(()=>{W.error("请求服务器错误，下载失败")}).finally(()=>{E.value=!1})},ya=async(y,z,G)=>{j.value=z[G],M.value=!0},_a=y=>typeof y=="string"&&y!==null?y:y!==null?y[0]:Va,ka=()=>{b.value=t.value.offsetHeight,Je()},Je=()=>{let z=(u.value.height?u.value.height:document.querySelector(".work-area").offsetHeight)-b.value-160+(g.value?0:15);m.value.style.height=z+"px"};return da(async()=>{g.value=u.value.showSearch??!0,u.value.pageLayout==="fixed"&&await je(()=>{window.addEventListener("resize",ka,!1),b.value=t.value.offsetHeight,Je()})}),yo(()=>{u.value.pageLayout==="fixed"&&window.removeEventListener("resize",ka,!1)}),a({requestData:ha,refresh:he,setSelecteds:Q,clearSelected:P,tableRef:I,getTableData:Z,getTableTotal:_e}),(y,z)=>{const G=s("a-row"),Ve=s("a-form"),se=s("a-col"),Le=s("icon-search"),D=s("a-button"),ke=s("icon-refresh"),we=s("a-space"),wa=s("a-divider"),Ya=s("icon-plus"),Qe=s("icon-delete"),Ze=s("a-popconfirm"),Ja=s("icon-upload"),Qa=s("icon-download"),Za=s("icon-expand"),Xa=s("icon-shrink"),Ue=s("a-tooltip"),Ka=s("icon-printer"),eo=s("icon-sort"),ao=s("icon-sort-ascending"),oo=s("a-checkbox"),no=s("a-popover"),xa=s("icon-edit"),Oe=s("a-link"),Sa=s("a-scrollbar"),Aa=s("sa-dict"),Ea=s("a-avatar"),Xe=s("a-table-column"),io=s("icon-eye"),co=s("a-table"),lo=s("a-pagination"),to=s("a-card"),mo=s("a-image-preview-group"),ro=s("a-image-preview"),so=s("a-layout-content"),ue=_o("auth");return r(),A(so,{class:"flex flex-col lg:h-full relative w-full"},{default:d(()=>[l(to,{bordered:!1},{default:d(()=>[q("div",{ref_key:"crudHeaderRef",ref:t},[g.value?(r(),C(oe,{key:0},[B(v).getDevice()==="mobile"?(r(),A(G,{key:0},{default:d(()=>[l(se,{xs:24,sm:8},{default:d(()=>[l(Ve,{model:re.value,ref_key:"searchFormRef",ref:c,"auto-label-width":!0},{default:d(()=>[l(G,{gutter:10},{default:d(()=>[K(y.$slots,"tableSearch",{},void 0,!0)]),_:3})]),_:3},8,["model"])]),_:3}),l(se,{xs:24,sm:8,style:{textAlign:"right",marginBottom:"15px"}},{default:d(()=>[l(we,{direction:"horizontal",size:20},{default:d(()=>[l(D,{type:"primary",onClick:$e},{icon:d(()=>[l(Le)]),default:d(()=>[L(" "+V(u.value.searchText||"搜索"),1)]),_:1}),l(D,{onClick:Ne},{icon:d(()=>[l(ke)]),default:d(()=>[L(" "+V(u.value.resetText||"重置"),1)]),_:1})]),_:1})]),_:1})]),_:3})):(r(),A(G,{key:1},{default:d(()=>[l(se,{flex:1},{default:d(()=>[l(Ve,{model:re.value,ref_key:"searchFormRef",ref:c,"auto-label-width":!0},{default:d(()=>[l(G,{gutter:10},{default:d(()=>[K(y.$slots,"tableSearch",{},void 0,!0)]),_:3})]),_:3},8,["model"])]),_:3}),h.value?U("",!0):(r(),A(wa,{key:0,style:{height:"84px"},direction:"vertical"})),l(se,{flex:h.value?"185px":"80px",style:{textAlign:"right"}},{default:d(()=>[l(we,{direction:h.value?"horizontal":"vertical",size:h.value?10:20},{default:d(()=>[l(D,{type:"primary",onClick:$e},{icon:d(()=>[l(Le)]),default:d(()=>[L(" "+V(u.value.searchText||"搜索"),1)]),_:1}),l(D,{onClick:Ne},{icon:d(()=>[l(ke)]),default:d(()=>[L(" "+V(u.value.resetText||"重置"),1)]),_:1})]),_:1},8,["direction","size"])]),_:1},8,["flex"])]),_:3})),l(wa,{style:{"margin-top":"0","margin-bottom":"15px"}})],64)):U("",!0)],512),q("div",Cn,[u.value.pageSimple?U("",!0):(r(),A(G,{key:0,style:{"margin-bottom":"10px"}},{default:d(()=>[l(se,{xs:24,sm:18},{default:d(()=>[l(we,{wrap:!0},{default:d(()=>[K(y.$slots,"tableBeforeButtons",{},void 0,!0),u.value.add.show?pe((r(),A(D,{key:0,type:"primary",onClick:Ua},{icon:d(()=>[l(Ya)]),default:d(()=>[L(" "+V(u.value.add.text||"新增"),1)]),_:1})),[[ue,u.value.add.auth||[]]]):U("",!0),u.value.delete.show&&u.value.rowSelection?(r(),A(Ze,{key:1,content:"确定要删除数据吗?",position:"bottom",onOk:Ha},{default:d(()=>[pe((r(),A(D,{type:"primary",status:"danger"},{icon:d(()=>[l(Qe)]),default:d(()=>[L(" "+V(u.value.delete.text||"删除"),1)]),_:1})),[[ue,u.value.delete.auth||[]]])]),_:1})):U("",!0),u.value.import.show?pe((r(),A(D,{key:2,onClick:Wa},{icon:d(()=>[l(Ja)]),default:d(()=>[L(" "+V(u.value.import.text||"导入"),1)]),_:1})),[[ue,u.value.import.auth||[]]]):U("",!0),u.value.export.show?pe((r(),A(D,{key:3,loading:E.value,onClick:Ga},{icon:d(()=>[l(Qa)]),default:d(()=>[L(" "+V(u.value.export.text||"导出"),1)]),_:1},8,["loading"])),[[ue,u.value.export.auth||[]]]):U("",!0),u.value.isExpand?(r(),A(D,{key:4,type:"secondary",onClick:We},{icon:d(()=>[k.value?(r(),A(Xa,{key:1})):(r(),A(Za,{key:0}))]),default:d(()=>[L(" "+V(k.value?" 折叠":" 展开"),1)]),_:1})):U("",!0),K(y.$slots,"tableAfterButtons",{},void 0,!0)]),_:3})]),_:3}),l(se,{xs:24,sm:6,style:Ee({textAlign:"right",marginTop:B(v).getDevice()==="mobile"?"15px":"0"})},{default:d(()=>[u.value.showTools?(r(),A(we,{key:0},{default:d(()=>[K(y.$slots,"tools",{},void 0,!0),l(Ue,{content:"刷新表格",onClick:he},{default:d(()=>[l(D,{shape:"circle"},{default:d(()=>[l(ke)]),_:1})]),_:1}),l(Ue,{content:"显隐搜索"},{default:d(()=>[l(D,{shape:"circle",onClick:Ye},{default:d(()=>[l(Le)]),_:1})]),_:1}),l(Ue,{content:"打印表格"},{default:d(()=>[l(D,{shape:"circle",onClick:$},{default:d(()=>[l(Ka)]),_:1})]),_:1}),u.value.showSort?(r(),A(Ue,{key:0,content:"字段排序"},{default:d(()=>[l(no,{trigger:"click",position:"br"},{content:d(()=>[q("div",Vn,[(r(!0),C(oe,null,de(ee.value,(ie,R)=>(r(),C("div",{key:ie.dataIndex,class:"setting"},[q("div",Ln,[l(ao)]),q("div",null,[l(oo,{modelValue:ie.checked,"onUpdate:modelValue":ge=>ie.checked=ge,onChange:ge=>Ce(ge,ie,R)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),q("div",On,V(ie.title==="#"?"序列号":ie.title),1)]))),128))])]),default:d(()=>[l(D,{shape:"circle"},{default:d(()=>[l(eo)]),_:1})]),_:1})]),_:1})):U("",!0)]),_:3})):U("",!0)]),_:3},8,["style"])]),_:3})),q("div",{ref_key:"crudContentRef",ref:m},[K(y.$slots,"crudContent",na(ia(ne)),()=>{var ie;return[(r(),A(co,le(y.$attrs,{ref_key:"tableRef",ref:I,key:u.value.pk,rowSelection:u.value.rowSelection??void 0,"row-key":((ie=u.value.rowSelection)==null?void 0:ie.key)??u.value.pk,pagination:!1,columns:ee.value,loading:S.value,size:u.value.size,stripe:u.value.stripe,data:ne.data,scroll:{x:"100%",y:"100%"},bordered:u.value.bordered,"default-expand-all-rows":u.value.expandAllRows,summary:u.value.showSummary&&fe,onSelectionChange:Q,onSorterChange:Ge}),la({columns:d(()=>[(r(!0),C(oe,null,de(ee.value,(R,ge)=>(r(),C(oe,{key:ge},[R.children?(r(),A(Xe,{key:0,title:R.title},{default:d(()=>[(r(!0),C(oe,null,de(R.children,(T,xe)=>(r(),A(Xe,{title:T.title,"data-index":T.dataIndex,width:T.width,"min-width":T.minWidth,ellipsis:T.ellipsis??!0,filterable:T.filterable,"cell-class":T.cellClass,"header-cell-class":T.headerCellClass,"body-cell-class":T.bodyCellClass,"summary-cell-class":T.summaryCellClass,"cell-style":T.cellStyle,"header-cell-style":T.headerCellStyle,"body-cell-style":T.bodyCellStyle,"summary-cell-style":T.summaryCellStyle,tooltip:T.dataIndex==="__operation"?!1:T.tooltip??!0,align:T.align||u.value.columnAlign,fixed:T.fixed,sortable:T.sortable},{cell:d(({record:ae,column:be,rowIndex:Se})=>[T.dataIndex==="__index"?(r(),C("span",Rn,V(Me(Se)),1)):T.dataIndex==="__operation"?(r(),A(Sa,{key:1,type:"track",style:{overflow:"auto"}},{default:d(()=>[l(we,{size:"mini"},{default:d(()=>[K(y.$slots,"operationBeforeExtend",le({ref_for:!0},{record:ae,column:be,rowIndex:Se}),void 0,!0),K(y.$slots,"operationCell",le({ref_for:!0},{record:ae,column:be,rowIndex:Se}),()=>[u.value.edit.show?pe((r(),A(Oe,{key:0,type:"primary",onClick:Ia=>ga(ae)},{default:d(()=>[l(xa),L(V(u.value.edit.text||"编辑"),1)]),_:2},1032,["onClick"])),[[ue,u.value.edit.auth||[]]]):U("",!0),u.value.delete.show?(r(),A(Ze,{key:1,content:"确定要删除该数据吗?",position:"bottom",onOk:Ia=>va(ae)},{default:d(()=>[pe((r(),A(Oe,{type:"primary"},{default:d(()=>[l(Qe),L(" "+V(u.value.delete.text||"删除"),1)]),_:1})),[[ue,u.value.delete.auth||[]]])]),_:2},1032,["onOk"])):U("",!0)],!0),K(y.$slots,"operationAfterExtend",le({ref_for:!0},{record:ae,column:be,rowIndex:Se}),void 0,!0)]),_:2},1024)]),_:2},1024)):T.type==="dict"?K(y.$slots,T.dataIndex,le({key:2,ref_for:!0},{record:ae,column:be,rowIndex:Se}),()=>[l(Aa,{value:ae[T.dataIndex],render:T.render||"tag",colors:T.colors||[],dict:T.dict||[],options:T.options??[]},null,8,["value","render","colors","dict","options"])],!0):T.type==="image"?(r(),A(Ea,{key:3,onClick:Ia=>ya(T,ae,T.dataIndex),size:R.size||64,shape:"square"},{default:d(()=>[q("img",{src:_a(ae[T.dataIndex]),style:{"object-fit":"contain",cursor:"pointer"}},null,8,jn)]),_:2},1032,["onClick","size"])):K(y.$slots,T.dataIndex,le({key:4,ref_for:!0},{record:ae,column:be,rowIndex:Se}),()=>[q("span",null,V(F(T.dataIndex,ae)),1)],!0)]),_:2},1032,["title","data-index","width","min-width","ellipsis","filterable","cell-class","header-cell-class","body-cell-class","summary-cell-class","cell-style","header-cell-style","body-cell-style","summary-cell-style","tooltip","align","fixed","sortable"]))),256))]),_:2},1032,["title"])):(r(),A(Xe,{key:1,title:R.title,"data-index":R.dataIndex,width:R.width,ellipsis:R.ellipsis??!0,filterable:R.filterable,"cell-class":R.cellClass,"header-cell-class":R.headerCellClass,"body-cell-class":R.bodyCellClass,"summary-cell-class":R.summaryCellClass,"cell-style":R.cellStyle,"header-cell-style":R.headerCellStyle,"body-cell-style":R.bodyCellStyle,"summary-cell-style":R.summaryCellStyle,tooltip:R.dataIndex==="__operation"?!1:R.tooltip??!0,align:R.align||u.value.columnAlign,fixed:R.fixed,sortable:R.sortable},{cell:d(({record:T,column:xe,rowIndex:ae})=>[R.dataIndex==="__index"?(r(),C("span",Pn,V(Me(ae)),1)):R.dataIndex==="__operation"?(r(),A(Sa,{key:1,type:"track",style:{overflow:"auto"}},{default:d(()=>[l(we,{size:"mini"},{default:d(()=>[K(y.$slots,"operationBeforeExtend",le({ref_for:!0},{record:T,column:xe,rowIndex:ae}),void 0,!0),K(y.$slots,"operationCell",le({ref_for:!0},{record:T,column:xe,rowIndex:ae}),()=>[u.value.view.show?pe((r(),A(Oe,{key:0,type:"primary",onClick:be=>Ba(T)},{default:d(()=>[l(io),L(V(u.value.view.text||"查看"),1)]),_:2},1032,["onClick"])),[[ue,u.value.view.auth||[]]]):U("",!0),u.value.edit.show?pe((r(),A(Oe,{key:1,type:"primary",onClick:be=>ga(T)},{default:d(()=>[l(xa),L(V(u.value.edit.text||"编辑"),1)]),_:2},1032,["onClick"])),[[ue,u.value.edit.auth||[]]]):U("",!0),u.value.delete.show?(r(),A(Ze,{key:2,content:"确定要删除该数据吗?",position:"bottom",onOk:be=>va(T)},{default:d(()=>[pe((r(),A(Oe,{type:"primary"},{default:d(()=>[l(Qe),L(" "+V(u.value.delete.text||"删除"),1)]),_:1})),[[ue,u.value.delete.auth||[]]])]),_:2},1032,["onOk"])):U("",!0)],!0),K(y.$slots,"operationAfterExtend",le({ref_for:!0},{record:T,column:xe,rowIndex:ae}),void 0,!0)]),_:2},1024)]),_:2},1024)):R.type==="dict"?K(y.$slots,R.dataIndex,le({key:2,ref_for:!0},{record:T,column:xe,rowIndex:ae}),()=>[l(Aa,{value:T[R.dataIndex],render:R.render||"tag",colors:R.colors||[],dict:R.dict||[],options:R.options??[]},null,8,["value","render","colors","dict","options"])],!0):R.type==="image"?(r(),A(Ea,{key:3,onClick:be=>ya(R,T,R.dataIndex),size:R.size||64,shape:"square"},{default:d(()=>[q("img",{src:_a(T[R.dataIndex]),style:{"object-fit":"contain",cursor:"pointer"}},null,8,Dn)]),_:2},1032,["onClick","size"])):K(y.$slots,R.dataIndex,le({key:4,ref_for:!0},{record:T,column:xe,rowIndex:ae}),()=>[q("span",null,V(F(R.dataIndex,T)),1)],!0)]),_:2},1032,["title","data-index","width","ellipsis","filterable","cell-class","header-cell-class","body-cell-class","summary-cell-class","cell-style","header-cell-style","body-cell-style","summary-cell-style","tooltip","align","fixed","sortable"]))],64))),128))]),_:2},[u.value.showSummary?{name:"summary-cell",fn:d(({column:R,record:ge,rowIndex:T})=>[K(y.$slots,"summaryCell",na(ia({record:ge,column:R,rowIndex:T})),()=>[L(V(ge[R.dataIndex]),1)],!0)]),key:"0"}:void 0]),1040,["rowSelection","row-key","columns","loading","size","stripe","data","bordered","default-expand-all-rows","summary"]))]},!0)],512)]),ne.total>0?(r(),C("div",zn,[l(lo,{total:ne.total,"show-total":"","show-jumper":"","show-page-size":"","page-size-options":u.value.pageSizeOption,onPageSizeChange:He,onChange:Fe,current:H.value.page,"onUpdate:current":z[0]||(z[0]=ie=>H.value.page=ie),"page-size":H.value.limit,style:{display:"inline-flex"}},null,8,["total","page-size-options","current","page-size"])])):U("",!0)]),_:3}),l(Tn,{ref_key:"crudImportRef",ref:x,onSuccess:he},null,512),typeof j.value=="object"&&j.value!==null?(r(),A(mo,{key:0,srcList:j.value,visible:M.value,"onUpdate:visible":z[1]||(z[1]=ie=>M.value=ie)},null,8,["srcList","visible"])):(r(),A(ro,{key:1,src:j.value?j.value:B(Va),visible:M.value,"onUpdate:visible":z[2]||(z[2]=ie=>M.value=ie)},null,8,["src","visible"]))]),_:3})}}},Mn=ye(qn,[["__scopeId","data-v-84e3b8a3"]]),Fn={__name:"index",props:{modelValue:{type:Array},data:{type:Array},border:{type:Boolean,default:!0},searchPlaceholder:{type:String},fieldNames:{type:Object,default:()=>({title:"label",key:"value"})},icon:{type:String,default:void 0}},emits:["update:modelValue","click"],setup(e,{expose:a,emit:o}){const n=_([]),i=_(),c=_(!1),t=o,m=e,b=ze({get(){return m.modelValue},set(p){t("update:modelValue",p)}});X(()=>m.data,p=>{n.value=p},{immediate:!0,deep:!0});const x=(p,k)=>{b.value=[p],t("click",p,k)},S=()=>n.value=m.data,g=p=>{if(!p||p==="")return n.value=Object.assign(m.data,[]),!1;n.value=h(p)},h=p=>{const k=O=>{let I=[];return O.map(f=>{if(f[m.fieldNames.title].indexOf(p)!==-1)I.push(f);else if(f.children&&f.children.length>0){const E=k(f.children);I.push(...E)}return I}),I};return k(n.value)};return a({saTree:i}),(p,k)=>{const O=s("a-input"),I=s("a-button"),f=s("a-input-group"),E=s("a-tree");return r(),C("div",{class:ve(["flex flex-col w-full",m.border?"slider-border p-2":""])},[l(f,{class:"mb-2 w-full",size:"mini"},{default:d(()=>[l(O,{placeholder:m==null?void 0:m.searchPlaceholder,"allow-clear":"",onInput:g,onClear:S},null,8,["placeholder"]),l(I,{onClick:k[0]||(k[0]=()=>{c.value?i.value.expandAll(!1):i.value.expandAll(!0),c.value=!c.value})},{default:d(()=>[L(V(c.value?"折叠":"展开"),1)]),_:1}),K(p.$slots,"treeAfterButtons",{},void 0,!0)]),_:3}),l(E,le({blockNode:"",ref_key:"saTree",ref:i,data:n.value,class:"h-full w-full",onSelect:x,"field-names":m.fieldNames,"selected-keys":b.value,"onUpdate:selectedKeys":k[1]||(k[1]=M=>b.value=M)},p.$attrs),la({_:2},[m.icon?{name:"icon",fn:d(()=>[(r(),A(Pe(m.icon)))]),key:"0"}:void 0,de(p.$slots,(M,j)=>({name:j,fn:d(u=>[K(p.$slots,j,na(ia(u)),void 0,!0)])}))]),1040,["data","field-names","selected-keys"])],2)}}},$n=ye(Fn,[["__scopeId","data-v-fced5bd7"]]),Nn={class:"w-full resource-container h-full lg:flex lg:justify-between rounded-sm"},Un={class:"w-full lg:mt-2 flex flex-col"},Bn={class:"lg:flex lg:justify-between"},Hn={class:"flex"},Wn=["onClick"],Gn=["src"],Yn={key:1,class:"text-3xl w-full h-full flex items-center justify-center"},Jn={class:"file-name"},Qn={class:"lg:flex lg:justify-between"},Zn={__name:"index",props:{modelValue:{type:[String,Array]},multiple:{type:Boolean,default:!0},onlyData:{type:Boolean,default:!0},returnType:{type:String,default:"url"}},emits:["update:modelValue"],setup(e,{expose:a,emit:o}){const n=Ie().data,i=_([]),c=_("all"),t=_(),m=_([]),b=_(!1),x=_(),S=_({total:1,currentPage:1}),g=_(!0),h=_(21),p=_(),k=_(),O=_(),I=o,f=e;da(async()=>{const F=n.attachment_type;i.value=[{label:"所有",value:"all"},...F],await E({page:1}),f.multiple&&(k.value=[])});const E=async(F={})=>{var Z,_e,fe;const P=Object.assign(F,{limit:h.value});g.value=!0;const Q=await me.getResourceList(P);S.value={total:((Z=Q==null?void 0:Q.data)==null?void 0:Z.total)??0,currentPage:((_e=Q==null?void 0:Q.data)==null?void 0:_e.current_page)??21},m.value=(fe=Q==null?void 0:Q.data)==null?void 0:fe.data,g.value=!1},M=async F=>{c.value=F,await E({mime_type:F==="all"?void 0:F})},j=async()=>{await E({origin_name:p.value})},u=(F,P)=>{const Q=O.value.children,Z="item rounded-sm";/^(http|https)/g.test(F.url)||(F.url=v.attachUrl(F.url)),Q[P].className.indexOf("active")!==-1?(Q[P].className=Z,f.multiple?k.value.map((_e,fe)=>{k.value.splice(fe,1)}):k.value=""):f.multiple?(Q[P].className=Z+" active",k.value.push(f.onlyData?F[f.returnType]:F)):document.querySelectorAll(".item.active").length<1&&(Q[P].className=Z+" active",k.value=f.onlyData?F[f.returnType]:F)},H=()=>{if(O.value&&O.value.children){const F=O.value.children;for(let P=0;P<F.length;P++)F[P].className="item rounded-sm"}f.multiple?k.value=[]:k.value=void 0},re=()=>{const F=f.multiple?Object.assign([],k.value):k.value;I("update:modelValue",F)},ee=async F=>{await E({page:F})},ne=async F=>{if(!x.value){W.error("输入地址不能为空"),F(!1);return}const P=await me.saveNetWorkImage({url:x.value});P.code===200?(W.success(P.message),await E(),x.value=void 0,F(!0)):(W.error(P.message),F(!1))};return X(()=>t,async()=>await E(),{deep:!0}),a({clearSelecteds:H}),(F,P)=>{const Q=s("a-input"),Z=s("a-image"),_e=s("a-modal"),fe=s("sa-upload-file"),Me=s("icon-image"),Fe=s("a-button"),He=s("a-radio"),$e=s("a-radio-group"),Ne=s("a-tooltip"),We=s("a-empty"),Ge=s("a-spin"),Ye=s("a-pagination");return r(),C("div",Nn,[l(_e,{visible:b.value,"onUpdate:visible":P[1]||(P[1]=$=>b.value=$),"ok-text":"保存","on-before-ok":ne,draggable:""},{title:d(()=>P[8]||(P[8]=[L("保存网络图片")])),default:d(()=>[l(Q,{modelValue:x.value,"onUpdate:modelValue":P[0]||(P[0]=$=>x.value=$),class:"mb-3",placeholder:"请粘贴网络图片地址","allow-clear":""},null,8,["modelValue"]),l(Z,{src:x.value,width:"100%",style:{"min-height":"150px"}},null,8,["src"])]),_:1},8,["visible"]),q("div",Un,[q("div",Bn,[q("div",Hn,[l(fe,{modelValue:t.value,"onUpdate:modelValue":P[2]||(P[2]=$=>t.value=$),multiple:"","show-list":!1},null,8,["modelValue"]),l(Fe,{class:"ml-3",onClick:P[3]||(P[3]=$=>b.value=!0)},{default:d(()=>[l(Me),P[9]||(P[9]=L(" 保存网络图片 "))]),_:1}),l($e,{type:"button",modelValue:c.value,"onUpdate:modelValue":P[4]||(P[4]=$=>c.value=$),onChange:M,class:"ml-4"},{default:d(()=>[(r(!0),C(oe,null,de(i.value,($,Ce)=>(r(),A(He,{key:Ce,value:$.value},{default:d(()=>[L(V($.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),l(Q,{modelValue:p.value,"onUpdate:modelValue":P[5]||(P[5]=$=>p.value=$),class:"input-search lg:mt-0 mt-2",placeholder:"文件名搜索",onPressEnter:j},null,8,["modelValue"])]),l(Ge,{loading:g.value,tip:"资源加载中",class:"h-full"},{default:d(()=>[m.value&&m.value.length>0?(r(),C("div",{key:0,class:"resource-list mt-2",ref_key:"rl",ref:O},[(r(!0),C(oe,null,de(m.value,($,Ce)=>(r(),C("div",{class:"item rounded-sm",key:$.hash,onClick:fa=>u($,Ce)},[$.mime_type.indexOf("image")!==-1?(r(),C("img",{key:0,src:/^(http|https)/g.test($.url)?$.url:F.$tool.attachUrl($.url)},null,8,Gn)):(r(),C("div",Yn,V(`.${$.suffix}`),1)),l(Ne,{position:"bottom"},{content:d(()=>[q("div",null,"存储名称："+V($.object_name),1),q("div",null,"存储目录："+V($.storage_path),1),q("div",null,"上传时间："+V($.create_time),1),q("div",null,"文件大小："+V($.size_info),1),q("div",null,"存储模式："+V(B(v).getLabel($.storage_mode,B(n).upload_mode)),1)]),default:d(()=>[q("div",Jn,V($.origin_name),1)]),_:2},1024)],8,Wn))),128))],512)):(r(),A(We,{key:1,class:"mt-10"}))]),_:1},8,["loading"]),q("div",Qn,[l(Ye,{total:S.value.total,current:S.value.currentPage,"onUpdate:current":P[6]||(P[6]=$=>S.value.currentPage=$),"page-size":h.value,"onUpdate:pageSize":P[7]||(P[7]=$=>h.value=$),onChange:ee},null,8,["total","current","page-size"]),l(Fe,{type:"primary",onClick:re,class:"mt-3 lg:mt-0"},{default:d(()=>P[10]||(P[10]=[L("确定")])),_:1})])])])}}},Xn=ye(Zn,[["__scopeId","data-v-1bd7fcee"]]),Kn={class:"inline-block"},ei={class:"trigger-content"},ai={key:2},oi={__name:"button",props:{modelValue:{type:[String,Array]},multiple:{type:Boolean,default:!0},onlyData:{type:Boolean,default:!0},width:{type:Number,default:1080}},emits:["update:modelValue"],setup(e,{emit:a}){const o=_(),n=_(!1),i=_(""),c=a,t=e;return X(()=>t.modelValue,m=>{o.value=m},{immediate:!0}),X(()=>o.value,m=>{c("update:modelValue",o.value),t.multiple?i.value=J.isArray(o)?o.value.join(","):[]:i.value=o.value,n.value=!1},{immediate:!0,deep:!0}),(m,b)=>{const x=s("a-input"),S=s("a-button"),g=s("a-empty"),h=s("a-image"),p=s("a-space"),k=s("a-image-preview-group"),O=s("a-trigger"),I=s("icon-experiment"),f=s("a-input-group"),E=s("sa-resource"),M=s("a-modal");return r(),C("div",Kn,[l(f,{class:"w-full"},{default:d(()=>[l(O,{position:"bottom","auto-fit-position":"","unmount-on-close":!1},{content:d(()=>[q("div",ei,[o.value&&o.value.length==0?(r(),A(g,{key:0})):o.value&&!B(J.isArray)(o.value)?(r(),A(h,{key:1,src:i.value},null,8,["src"])):(r(),C("div",ai,[l(k,{infinite:""},{default:d(()=>[l(p,null,{default:d(()=>[(r(!0),C(oe,null,de(o.value,(j,u)=>(r(),A(h,{src:j,width:"100%",key:u},null,8,["src"]))),128))]),_:1})]),_:1})]))])]),default:d(()=>[t.multiple?(r(),A(S,{key:1},{default:d(()=>b[4]||(b[4]=[L("预览已选")])),_:1})):(r(),A(x,{key:0,modelValue:i.value,"onUpdate:modelValue":b[0]||(b[0]=j=>i.value=j),placeholder:"请点击左侧按钮选择资源",readonly:""},null,8,["modelValue"]))]),_:1}),l(S,{type:"primary",onClick:b[1]||(b[1]=j=>n.value=!0)},{default:d(()=>[l(I),b[5]||(b[5]=L(" 资源选择器"))]),_:1})]),_:1}),l(M,{visible:n.value,"onUpdate:visible":b[3]||(b[3]=j=>n.value=j),width:t.width,footer:!1,draggable:""},{title:d(()=>b[6]||(b[6]=[L("资源选择器")])),default:d(()=>[l(E,{modelValue:o.value,"onUpdate:modelValue":b[2]||(b[2]=j=>o.value=j),multiple:t.multiple,"only-data":t.onlyData},null,8,["modelValue","multiple","only-data"])]),_:1},8,["visible","width"])])}}},ni=ye(oi,[["__scopeId","data-v-88908e87"]]),ii={key:0},ci={key:2},di={__name:"index",props:{value:{type:[String,Number,Array]},render:{type:String,default:"tag"},dict:{type:String,default:""},options:{type:Array,default:[]},colors:{type:Array,default:[]}},setup(e){const a=Ie().data,o=_(),n=e;return X(()=>n.value,i=>{o.value=i},{immediate:!0}),(i,c)=>{const t=s("a-tag");return r(),C("div",null,[n.render==="span"?(r(),C("span",ii,[Array.isArray(o.value)?(r(),C(oe,{key:0},[L(V(o.value.map(m=>B(v).getLabel(m,n.options.length>0?n.options:B(a)[n.dict])).join(", ")),1)],64)):(r(),C(oe,{key:1},[L(V(B(v).getLabel(o.value,n.options.length>0?n.options:B(a)[n.dict])),1)],64))])):U("",!0),n.render==="tag"?(r(),C(oe,{key:1},[Array.isArray(o.value)?(r(!0),C(oe,{key:0},de(o.value,(m,b)=>(r(),A(t,{key:b,class:"mr-2",color:B(v).getColor(m,n.options.length>0?n.options:B(a)[n.dict],n.colors||[])},{default:d(()=>[L(V(B(v).getLabel(m,n.options.length>0?n.options:B(a)[n.dict])),1)]),_:2},1032,["color"]))),128)):o.value!==""?(r(),A(t,{key:1,color:B(v).getColor(o.value,n.options.length>0?n.options:B(a)[n.dict],n.colors||[])},{default:d(()=>[L(V(B(v).getLabel(o.value,n.options.length>0?n.options:B(a)[n.dict])),1)]),_:1},8,["color"])):(r(),C("span",ci))],64)):U("",!0)])}}},li={class:"ma-content-block"},ti={__name:"index",props:{modelValue:{type:Array},isEcho:{type:Boolean,default:!1},multiple:{type:Boolean,default:!0},onlyId:{type:Boolean,default:!0},text:{type:String,default:"选择用户"}},emits:["update:modelValue","success"],setup(e,{emit:a}){const o=e,n=a,i=_(!1),c=_(),t=_([]),m=_([]),b=_([]),x=async()=>{var I;i.value=!0,S(),await je(),(I=c.value)==null||I.refresh(),setTimeout(()=>{t.value=o.modelValue},500)},S=async()=>{const I=await me.commonGet("/core/dept/index?tree=true");b.value=I.data};da(()=>{o.isEcho&&o.onlyId&&(t.value=o.modelValue)}),X(()=>o.modelValue,I=>{o.isEcho&&o.onlyId&&(t.value=I),I.length==0&&(m.value=[])});const g=I=>{t.value=I},h=async I=>{if(J.isArray(t.value)&&t.value.length>0){const f=await me.getUserInfoByIds({ids:t.value});!J.isEmpty(f)&&J.isArray(f.data)&&(m.value=f.data.map(E=>`${E.username}(${E.id})`),o.onlyId?n("update:modelValue",t.value):n("update:modelValue",f.data),n("success",!0),W.success("选择成功"))}else n("update:modelValue",[]),m.value=[];I(!0)},p=_({username:"",phone:"",dept_id:""}),k=_({api:me.getUserList,pageSimple:!0,operationColumn:!1,rowSelection:o.multiple?{type:"checkbox",showCheckedAll:!0}:{type:"radio"}}),O=_([{title:"账户",dataIndex:"username",width:120},{title:"昵称",dataIndex:"nickname",width:120},{title:"手机",dataIndex:"phone",width:120},{title:"邮箱",dataIndex:"email",width:180}]);return(I,f)=>{const E=s("icon-select-all"),M=s("a-button"),j=s("a-tag"),u=s("a-input-tag"),H=s("a-space"),re=s("a-input"),ee=s("a-form-item"),ne=s("a-col"),F=s("a-tree-select"),P=s("sa-table"),Q=s("a-modal");return r(),C("div",li,[l(H,{class:"flex"},{default:d(()=>[l(M,{type:"primary",onClick:x},{icon:d(()=>[l(E)]),default:d(()=>[L(V(o.text),1)]),_:1}),o.isEcho?(r(),A(j,{key:0,size:"large",color:"blue"},{default:d(()=>[L("已选择 "+V(B(J.isArray)(t.value)?t.value.length:0)+" 位",1)]),_:1})):U("",!0),o.isEcho?(r(),A(u,{key:1,modelValue:m.value,"onUpdate:modelValue":f[0]||(f[0]=Z=>m.value=Z),style:{width:"320px"},placeholder:"请点击前面按钮"+o.text,"max-tag-count":3,disabled:""},null,8,["modelValue","placeholder"])):U("",!0)]),_:1}),l(Q,{visible:i.value,"onUpdate:visible":f[5]||(f[5]=Z=>i.value=Z),width:"100%",draggable:"","on-before-ok":h,unmountOnClose:""},{title:d(()=>[L(V(o.text),1)]),default:d(()=>[l(P,{ref_key:"crudRef",ref:c,options:k.value,columns:O.value,searchForm:p.value,"selected-keys":t.value,"onUpdate:selectedKeys":f[4]||(f[4]=Z=>t.value=Z),onSelectionChange:g},{tableSearch:d(()=>[l(ne,{sm:8,xs:24},{default:d(()=>[l(ee,{field:"username",label:"账户"},{default:d(()=>[l(re,{modelValue:p.value.username,"onUpdate:modelValue":f[1]||(f[1]=Z=>p.value.username=Z),placeholder:"请输入账户"},null,8,["modelValue"])]),_:1})]),_:1}),l(ne,{sm:8,xs:24},{default:d(()=>[l(ee,{field:"phone",label:"手机"},{default:d(()=>[l(re,{modelValue:p.value.phone,"onUpdate:modelValue":f[2]||(f[2]=Z=>p.value.phone=Z),placeholder:"请输入手机"},null,8,["modelValue"])]),_:1})]),_:1}),l(ne,{sm:8,xs:24},{default:d(()=>[l(ee,{field:"dept_id",label:"部门"},{default:d(()=>[l(F,{modelValue:p.value.dept_id,"onUpdate:modelValue":f[3]||(f[3]=Z=>p.value.dept_id=Z),data:b.value,"field-names":{key:"value",title:"label"},"allow-clear":"",placeholder:"请选择所属部门"},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1},8,["options","columns","searchForm","selected-keys"])]),_:1},8,["visible"])])}}},mi=ye(ti,[["__scopeId","data-v-88751d8a"]]),ri={class:"icon text-3xl"},si={class:"title"},ui={class:"icon text-3xl"},bi={class:"title"},pi={__name:"index",props:{modelValue:{type:[String,Array],default:()=>{}},rounded:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},title:{type:String,default:"本地上传"},icon:{type:String,default:"icon-plus"},size:{type:Number,default:4*1024*1024},limit:{type:Number,default:0},mode:{type:String,default:"system"},tip:{type:String,default:void 0},accept:{type:String,default:".jpg,.jpeg,.gif,.png,.svg,.bpm"}},emits:["update:modelValue"],setup(e,{emit:a}){const o=e,n=a,i=_([]),c=_(),t=_({}),m=async g=>{if(!g.fileItem)return;o.multiple||(t.value=g.fileItem);let h=!0;const p=g.fileItem.file;if(p.size>o.size&&(W.warning(p.name+"超出文件大小限制"),t.value={},h=!1),o.multiple&&o.limit>0&&i.value.length>=o.limit&&(W.warning("最多上传"+o.limit+"张图片"),t.value={},h=!1),h){const k=await ma(p),O=new FormData;O.append("image",p),O.append("isChunk",!1),O.append("hash",k),o.mode==="local"&&O.append("mode","local");const f=(await me.uploadImage(O)).data;if(f)if(!o.multiple)c.value=f.url,n("update:modelValue",c.value);else{i.value.push(f);let E=[];E=i.value.map(M=>M.url),n("update:modelValue",E)}}},b=()=>{t.value={},c.value=void 0,n("update:modelValue",null)},x=g=>{i.value.splice(g,1);let h=[];h=i.value.map(p=>p.url),n("update:modelValue",h)},S=async()=>{o.multiple?J.isArray(o.modelValue)&&o.modelValue.length>0?i.value=o.modelValue.map(g=>({url:g})):i.value=[]:typeof o.modelValue=="string"&&(c.value=o.modelValue,t.value.url=o.modelValue,t.value.percent=100,t.value.status="complete")};return X(()=>o.modelValue,g=>{S()},{deep:!0,immediate:!0}),(g,h)=>{const p=s("icon-delete"),k=s("a-button"),O=s("a-image"),I=s("a-upload"),f=s("a-space");return r(),C("div",{class:ve(["upload-image flex",o.rounded?"rounded-full":""])},[l(f,{wrap:""},{default:d(()=>{var E;return[!o.multiple&&((E=t.value)!=null&&E.url)?(r(),C("div",{key:0,class:ve("image-list "+(o.rounded?"rounded-full":""))},[l(k,{class:"delete",onClick:h[0]||(h[0]=M=>b())},{icon:d(()=>[l(p)]),_:1}),l(O,{width:"130",height:"130",class:ve(o.rounded?"rounded-full":""),src:t.value.url},null,8,["class","src"])],2)):o.multiple?(r(!0),C(oe,{key:1},de(i.value,(M,j)=>(r(),C("div",{class:ve("image-list "+(o.rounded?"rounded-full":"")),key:j},[l(k,{class:"delete",onClick:u=>x(j)},{icon:d(()=>[l(p)]),_:2},1032,["onClick"]),l(O,{width:"130",height:"130",class:ve(o.rounded?"rounded-full":""),src:M.url},null,8,["class","src"])],2))),128)):U("",!0),l(I,{"custom-request":m,"show-file-list":!1,accept:o.accept??".jpg,.jpeg,.gif,.png,.svg,.bpm",disabled:o.disabled,tip:o.tip},{"upload-button":d(()=>[K(g.$slots,"customer",{},()=>[o.multiple&&i.value.length<o.limit?(r(),C("div",{key:0,class:ve("upload-skin "+(o.rounded?"rounded-full":"rounded-sm"))},[q("div",ri,[(r(),A(Pe(o.icon)))]),q("div",si,V(o.title),1)],2)):U("",!0),!o.modelValue&&!o.multiple?(r(),C("div",{key:1,class:ve("upload-skin "+(o.rounded?"rounded-full":"rounded-sm"))},[q("div",ui,[(r(),A(Pe(o.icon)))]),q("div",bi,V(o.title),1)],2)):U("",!0)],!0)]),_:3},8,["accept","disabled","tip"])]}),_:3})],2)}}},fi=ye(pi,[["__scopeId","data-v-40f3610e"]]),hi={class:"upload-file w-full"},gi={style:{"background-color":"var(--color-fill-2)",border:"1px dashed var(--color-fill-4)"},class:"rounded text-center p-7 w-full"},vi={class:"text-red-600 font-bold"},yi={key:0,class:"file-list mt-2"},_i=["href"],ki=["href"],wi={__name:"index",props:{modelValue:{type:[String,Number,Array],default:()=>{}},showList:{type:Boolean,default:!0},draggable:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},title:{type:String,default:"本地上传"},icon:{type:String,default:"icon-plus"},size:{type:Number,default:4*1024*1024},limit:{type:Number,default:0},mode:{type:String,default:"system"},tip:{type:String,default:void 0},accept:{type:String,default:"*"}},emits:["update:modelValue"],setup(e,{emit:a}){const o=e,n=a,i=_([]),c=_(),t=_({}),m=ze(()=>o.disabled?!0:!!(!o.multiple&&t.value&&t.value.url)),b=async h=>{if(!h.fileItem)return;o.multiple||(t.value=h.fileItem);let p=!0;const k=h.fileItem.file;if(k.size>o.size&&(W.warning(k.name+"超出文件大小限制"),t.value={},p=!1),o.multiple&&o.limit>0&&i.value.length>=o.limit&&(W.warning("最多上传"+o.limit+"个文件"),t.value={},p=!1),p){const O=await ma(k),I=new FormData;I.append("file",k),I.append("hash",O),o.mode==="local"&&I.append("mode","local");const E=(await me.uploadFile(I)).data;if(E)if(!o.multiple)c.value=E.url,n("update:modelValue",c.value);else{i.value.push(E);let M=[];M=i.value.map(j=>j.url),n("update:modelValue",M)}}},x=()=>{t.value={},c.value=void 0,n("update:modelValue",null)},S=h=>{i.value.splice(h,1);let p=[];p=i.value.map(k=>k.url),n("update:modelValue",p)},g=async()=>{o.multiple?J.isArray(o.modelValue)&&o.modelValue.length>0?i.value=o.modelValue.map(h=>({url:h,name:h.substring(h.lastIndexOf("/")+1)})):i.value=[]:typeof o.modelValue=="string"&&(c.value=o.modelValue,t.value.url=o.modelValue,t.value.name=o.modelValue.substring(o.modelValue.lastIndexOf("/")+1))};return X(()=>o.modelValue,h=>{g()},{deep:!0,immediate:!0}),(h,p)=>{var M;const k=s("icon-upload"),O=s("a-upload"),I=s("a-tooltip"),f=s("icon-delete"),E=s("a-button");return r(),C("div",null,[q("div",hi,[l(O,{"custom-request":b,"show-file-list":!1,multiple:o.multiple,accept:o.accept,disabled:m.value,tip:o.tip,draggable:o.draggable},la({_:2},[o.draggable?{name:"upload-button",fn:d(()=>[K(h.$slots,"customer",{},()=>[q("div",gi,[q("div",null,[l(k,{class:"text-5xl text-gray-400"}),q("div",vi,V(o.title),1),p[1]||(p[1]=L(" 将文件拖到此处，或")),p[2]||(p[2]=q("span",{style:{color:"#3370ff"}},"点击上传",-1))])])],!0)]),key:"0"}:void 0]),1032,["multiple","accept","disabled","tip","draggable"])]),!o.multiple&&((M=t.value)!=null&&M.url)&&o.showList?(r(),C("div",yi,[l(I,{content:"点击文件名预览/下载",position:"tr"},{default:d(()=>{var j;return[(j=t.value)!=null&&j.url?(r(),C("a",{key:0,href:t.value.url,class:"file-name",target:"_blank"},V(t.value.name),9,_i)):U("",!0)]}),_:1}),l(E,{type:"text",size:"small",onClick:p[0]||(p[0]=j=>x())},{icon:d(()=>[l(f)]),_:1})])):U("",!0),o.showList?(r(!0),C(oe,{key:1},de(i.value,(j,u)=>(r(),C("div",{class:"file-list mt-2",key:u},[l(I,{content:"点击文件名预览/下载",position:"tr"},{default:d(()=>[j!=null&&j.url?(r(),C("a",{key:0,href:j.url,class:"file-name",target:"_blank"},V(j.name),9,ki)):U("",!0)]),_:2},1024),l(E,{type:"text",size:"small",onClick:H=>S(u)},{icon:d(()=>[l(f)]),_:2},1032,["onClick"])]))),128)):U("",!0)])}}},xi=ye(wi,[["__scopeId","data-v-2e9ede3d"]]),Si={__name:"index",props:{icon:{type:String},size:{type:Number,default:24}},setup(e){const a=_(""),o=e;return X(()=>o.icon,n=>{n&&(a.value=n)},{immediate:!0}),(n,i)=>a.value.indexOf(":")===-1?(r(),A(Pe(a.value),{key:0,size:o.size},null,8,["size"])):(r(),A(B(Pa),{key:1,icon:a.value,class:"iconify-icon",style:Ee({fontSize:o.size+"px"})},null,8,["icon","style"]))}},Ai=["bi:0-circle","bi:0-circle-fill","bi:0-square","bi:0-square-fill","bi:1-circle","bi:1-circle-fill","bi:1-square","bi:1-square-fill","bi:2-circle","bi:2-circle-fill","bi:2-square","bi:2-square-fill","bi:3-circle","bi:3-circle-fill","bi:3-square","bi:3-square-fill","bi:4-circle","bi:4-circle-fill","bi:4-square","bi:4-square-fill","bi:5-circle","bi:5-circle-fill","bi:5-square","bi:5-square-fill","bi:6-circle","bi:6-circle-fill","bi:6-square","bi:6-square-fill","bi:7-circle","bi:7-circle-fill","bi:7-square","bi:7-square-fill","bi:8-circle","bi:8-circle-fill","bi:8-square","bi:8-square-fill","bi:9-circle","bi:9-circle-fill","bi:9-square","bi:9-square-fill","bi:activity","bi:airplane","bi:airplane-engines","bi:airplane-engines-fill","bi:airplane-fill","bi:alarm","bi:alarm-fill","bi:alexa","bi:align-bottom","bi:align-center","bi:align-end","bi:align-middle","bi:align-start","bi:align-top","bi:alipay","bi:alphabet","bi:alphabet-uppercase","bi:alt","bi:amazon","bi:amd","bi:android","bi:android2","bi:app","bi:app-indicator","bi:apple","bi:archive","bi:archive-fill","bi:arrow-90deg-down","bi:arrow-90deg-left","bi:arrow-90deg-right","bi:arrow-90deg-up","bi:arrow-bar-down","bi:arrow-bar-left","bi:arrow-bar-right","bi:arrow-bar-up","bi:arrow-clockwise","bi:arrow-counterclockwise","bi:arrow-down","bi:arrow-down-circle","bi:arrow-down-circle-fill","bi:arrow-down-left","bi:arrow-down-left-circle","bi:arrow-down-left-circle-fill","bi:arrow-down-left-square","bi:arrow-down-left-square-fill","bi:arrow-down-right","bi:arrow-down-right-circle","bi:arrow-down-right-circle-fill","bi:arrow-down-right-square","bi:arrow-down-right-square-fill","bi:arrow-down-short","bi:arrow-down-square","bi:arrow-down-square-fill","bi:arrow-down-up","bi:arrow-left","bi:arrow-left-circle","bi:arrow-left-circle-fill","bi:arrow-left-right","bi:arrow-left-short","bi:arrow-left-square","bi:arrow-left-square-fill","bi:arrow-repeat","bi:arrow-return-left","bi:arrow-return-right","bi:arrow-right","bi:arrow-right-circle","bi:arrow-right-circle-fill","bi:arrow-right-short","bi:arrow-right-square","bi:arrow-right-square-fill","bi:arrow-through-heart","bi:arrow-through-heart-fill","bi:arrow-up","bi:arrow-up-circle","bi:arrow-up-circle-fill","bi:arrow-up-left","bi:arrow-up-left-circle","bi:arrow-up-left-circle-fill","bi:arrow-up-left-square","bi:arrow-up-left-square-fill","bi:arrow-up-right","bi:arrow-up-right-circle","bi:arrow-up-right-circle-fill","bi:arrow-up-right-square","bi:arrow-up-right-square-fill","bi:arrow-up-short","bi:arrow-up-square","bi:arrow-up-square-fill","bi:arrows","bi:arrows-angle-contract","bi:arrows-angle-expand","bi:arrows-collapse","bi:arrows-collapse-vertical","bi:arrows-expand","bi:arrows-expand-vertical","bi:arrows-fullscreen","bi:arrows-move","bi:arrows-vertical","bi:aspect-ratio","bi:aspect-ratio-fill","bi:asterisk","bi:at","bi:award","bi:award-fill","bi:back","bi:backpack","bi:backpack-fill","bi:backpack2","bi:backpack2-fill","bi:backpack3","bi:backpack3-fill","bi:backpack4","bi:backpack4-fill","bi:backspace","bi:backspace-fill","bi:backspace-reverse","bi:backspace-reverse-fill","bi:badge-3d","bi:badge-3d-fill","bi:badge-4k","bi:badge-4k-fill","bi:badge-8k","bi:badge-8k-fill","bi:badge-ad","bi:badge-ad-fill","bi:badge-ar","bi:badge-ar-fill","bi:badge-cc","bi:badge-cc-fill","bi:badge-hd","bi:badge-hd-fill","bi:badge-sd","bi:badge-sd-fill","bi:badge-tm","bi:badge-tm-fill","bi:badge-vo","bi:badge-vo-fill","bi:badge-vr","bi:badge-vr-fill","bi:badge-wc","bi:badge-wc-fill","bi:bag","bi:bag-check","bi:bag-check-fill","bi:bag-dash","bi:bag-dash-fill","bi:bag-fill","bi:bag-heart","bi:bag-heart-fill","bi:bag-plus","bi:bag-plus-fill","bi:bag-x","bi:bag-x-fill","bi:balloon","bi:balloon-fill","bi:balloon-heart","bi:balloon-heart-fill","bi:ban","bi:ban-fill","bi:bandaid","bi:bandaid-fill","bi:bank","bi:bank2","bi:bar-chart","bi:bar-chart-fill","bi:bar-chart-line","bi:bar-chart-line-fill","bi:bar-chart-steps","bi:basket","bi:basket-fill","bi:basket2","bi:basket2-fill","bi:basket3","bi:basket3-fill","bi:battery","bi:battery-charging","bi:battery-full","bi:battery-half","bi:behance","bi:bell","bi:bell-fill","bi:bell-slash","bi:bell-slash-fill","bi:bezier","bi:bezier2","bi:bicycle","bi:bing","bi:binoculars","bi:binoculars-fill","bi:blockquote-left","bi:blockquote-right","bi:bluetooth","bi:body-text","bi:book","bi:book-fill","bi:book-half","bi:bookmark","bi:bookmark-check","bi:bookmark-check-fill","bi:bookmark-dash","bi:bookmark-dash-fill","bi:bookmark-fill","bi:bookmark-heart","bi:bookmark-heart-fill","bi:bookmark-plus","bi:bookmark-plus-fill","bi:bookmark-star","bi:bookmark-star-fill","bi:bookmark-x","bi:bookmark-x-fill","bi:bookmarks","bi:bookmarks-fill","bi:bookshelf","bi:boombox","bi:boombox-fill","bi:bootstrap","bi:bootstrap-fill","bi:bootstrap-reboot","bi:border","bi:border-all","bi:border-bottom","bi:border-center","bi:border-inner","bi:border-left","bi:border-middle","bi:border-outer","bi:border-right","bi:border-style","bi:border-top","bi:border-width","bi:bounding-box","bi:bounding-box-circles","bi:box","bi:box-arrow-down","bi:box-arrow-down-left","bi:box-arrow-down-right","bi:box-arrow-in-down","bi:box-arrow-in-down-left","bi:box-arrow-in-down-right","bi:box-arrow-in-left","bi:box-arrow-in-right","bi:box-arrow-in-up","bi:box-arrow-in-up-left","bi:box-arrow-in-up-right","bi:box-arrow-left","bi:box-arrow-right","bi:box-arrow-up","bi:box-arrow-up-left","bi:box-arrow-up-right","bi:box-fill","bi:box-seam","bi:box-seam-fill","bi:box2","bi:box2-fill","bi:box2-heart","bi:box2-heart-fill","bi:boxes","bi:braces","bi:braces-asterisk","bi:bricks","bi:briefcase","bi:briefcase-fill","bi:brightness-alt-high","bi:brightness-alt-high-fill","bi:brightness-alt-low","bi:brightness-alt-low-fill","bi:brightness-high","bi:brightness-high-fill","bi:brightness-low","bi:brightness-low-fill","bi:brilliance","bi:broadcast","bi:broadcast-pin","bi:browser-chrome","bi:browser-edge","bi:browser-firefox","bi:browser-safari","bi:brush","bi:brush-fill","bi:bucket","bi:bucket-fill","bi:bug","bi:bug-fill","bi:building","bi:building-add","bi:building-check","bi:building-dash","bi:building-down","bi:building-exclamation","bi:building-fill","bi:building-fill-add","bi:building-fill-check","bi:building-fill-dash","bi:building-fill-down","bi:building-fill-exclamation","bi:building-fill-gear","bi:building-fill-lock","bi:building-fill-slash","bi:building-fill-up","bi:building-fill-x","bi:building-gear","bi:building-lock","bi:building-slash","bi:building-up","bi:building-x","bi:buildings","bi:buildings-fill","bi:bullseye","bi:bus-front","bi:bus-front-fill","bi:c-circle","bi:c-circle-fill","bi:c-square","bi:c-square-fill","bi:cake","bi:cake-fill","bi:cake2","bi:cake2-fill","bi:calculator","bi:calculator-fill","bi:calendar","bi:calendar-check","bi:calendar-check-fill","bi:calendar-date","bi:calendar-date-fill","bi:calendar-day","bi:calendar-day-fill","bi:calendar-event","bi:calendar-event-fill","bi:calendar-fill","bi:calendar-heart","bi:calendar-heart-fill","bi:calendar-minus","bi:calendar-minus-fill","bi:calendar-month","bi:calendar-month-fill","bi:calendar-plus","bi:calendar-plus-fill","bi:calendar-range","bi:calendar-range-fill","bi:calendar-week","bi:calendar-week-fill","bi:calendar-x","bi:calendar-x-fill","bi:calendar2","bi:calendar2-check","bi:calendar2-check-fill","bi:calendar2-date","bi:calendar2-date-fill","bi:calendar2-day","bi:calendar2-day-fill","bi:calendar2-event","bi:calendar2-event-fill","bi:calendar2-fill","bi:calendar2-heart","bi:calendar2-heart-fill","bi:calendar2-minus","bi:calendar2-minus-fill","bi:calendar2-month","bi:calendar2-month-fill","bi:calendar2-plus","bi:calendar2-plus-fill","bi:calendar2-range","bi:calendar2-range-fill","bi:calendar2-week","bi:calendar2-week-fill","bi:calendar2-x","bi:calendar2-x-fill","bi:calendar3","bi:calendar3-event","bi:calendar3-event-fill","bi:calendar3-fill","bi:calendar3-range","bi:calendar3-range-fill","bi:calendar3-week","bi:calendar3-week-fill","bi:calendar4","bi:calendar4-event","bi:calendar4-range","bi:calendar4-week","bi:camera","bi:camera-fill","bi:camera-reels","bi:camera-reels-fill","bi:camera-video","bi:camera-video-fill","bi:camera-video-off","bi:camera-video-off-fill","bi:camera2","bi:capslock","bi:capslock-fill","bi:capsule","bi:capsule-pill","bi:car-front","bi:car-front-fill","bi:card-checklist","bi:card-heading","bi:card-image","bi:card-list","bi:card-text","bi:caret-down","bi:caret-down-fill","bi:caret-down-square","bi:caret-down-square-fill","bi:caret-left","bi:caret-left-fill","bi:caret-left-square","bi:caret-left-square-fill","bi:caret-right","bi:caret-right-fill","bi:caret-right-square","bi:caret-right-square-fill","bi:caret-up","bi:caret-up-fill","bi:caret-up-square","bi:caret-up-square-fill","bi:cart","bi:cart-check","bi:cart-check-fill","bi:cart-dash","bi:cart-dash-fill","bi:cart-fill","bi:cart-plus","bi:cart-plus-fill","bi:cart-x","bi:cart-x-fill","bi:cart2","bi:cart3","bi:cart4","bi:cash","bi:cash-coin","bi:cash-stack","bi:cassette","bi:cassette-fill","bi:cast","bi:cc-circle","bi:cc-circle-fill","bi:cc-square","bi:cc-square-fill","bi:chat","bi:chat-dots","bi:chat-dots-fill","bi:chat-fill","bi:chat-heart","bi:chat-heart-fill","bi:chat-left","bi:chat-left-dots","bi:chat-left-dots-fill","bi:chat-left-fill","bi:chat-left-heart","bi:chat-left-heart-fill","bi:chat-left-quote","bi:chat-left-quote-fill","bi:chat-left-text","bi:chat-left-text-fill","bi:chat-quote","bi:chat-quote-fill","bi:chat-right","bi:chat-right-dots","bi:chat-right-dots-fill","bi:chat-right-fill","bi:chat-right-heart","bi:chat-right-heart-fill","bi:chat-right-quote","bi:chat-right-quote-fill","bi:chat-right-text","bi:chat-right-text-fill","bi:chat-square","bi:chat-square-dots","bi:chat-square-dots-fill","bi:chat-square-fill","bi:chat-square-heart","bi:chat-square-heart-fill","bi:chat-square-quote","bi:chat-square-quote-fill","bi:chat-square-text","bi:chat-square-text-fill","bi:chat-text","bi:chat-text-fill","bi:check","bi:check-all","bi:check-circle","bi:check-circle-fill","bi:check-lg","bi:check-square","bi:check-square-fill","bi:check2","bi:check2-all","bi:check2-circle","bi:check2-square","bi:chevron-bar-contract","bi:chevron-bar-down","bi:chevron-bar-expand","bi:chevron-bar-left","bi:chevron-bar-right","bi:chevron-bar-up","bi:chevron-compact-down","bi:chevron-compact-left","bi:chevron-compact-right","bi:chevron-compact-up","bi:chevron-contract","bi:chevron-double-down","bi:chevron-double-left","bi:chevron-double-right","bi:chevron-double-up","bi:chevron-down","bi:chevron-expand","bi:chevron-left","bi:chevron-right","bi:chevron-up","bi:circle","bi:circle-fill","bi:circle-half","bi:circle-square","bi:clipboard","bi:clipboard-check","bi:clipboard-check-fill","bi:clipboard-data","bi:clipboard-data-fill","bi:clipboard-fill","bi:clipboard-heart","bi:clipboard-heart-fill","bi:clipboard-minus","bi:clipboard-minus-fill","bi:clipboard-plus","bi:clipboard-plus-fill","bi:clipboard-pulse","bi:clipboard-x","bi:clipboard-x-fill","bi:clipboard2","bi:clipboard2-check","bi:clipboard2-check-fill","bi:clipboard2-data","bi:clipboard2-data-fill","bi:clipboard2-fill","bi:clipboard2-heart","bi:clipboard2-heart-fill","bi:clipboard2-minus","bi:clipboard2-minus-fill","bi:clipboard2-plus","bi:clipboard2-plus-fill","bi:clipboard2-pulse","bi:clipboard2-pulse-fill","bi:clipboard2-x","bi:clipboard2-x-fill","bi:clock","bi:clock-fill","bi:clock-history","bi:cloud","bi:cloud-arrow-down","bi:cloud-arrow-down-fill","bi:cloud-arrow-up","bi:cloud-arrow-up-fill","bi:cloud-check","bi:cloud-check-fill","bi:cloud-download","bi:cloud-download-fill","bi:cloud-drizzle","bi:cloud-drizzle-fill","bi:cloud-fill","bi:cloud-fog","bi:cloud-fog-fill","bi:cloud-fog2","bi:cloud-fog2-fill","bi:cloud-hail","bi:cloud-hail-fill","bi:cloud-haze","bi:cloud-haze-fill","bi:cloud-haze2","bi:cloud-haze2-fill","bi:cloud-lightning","bi:cloud-lightning-fill","bi:cloud-lightning-rain","bi:cloud-lightning-rain-fill","bi:cloud-minus","bi:cloud-minus-fill","bi:cloud-moon","bi:cloud-moon-fill","bi:cloud-plus","bi:cloud-plus-fill","bi:cloud-rain","bi:cloud-rain-fill","bi:cloud-rain-heavy","bi:cloud-rain-heavy-fill","bi:cloud-slash","bi:cloud-slash-fill","bi:cloud-sleet","bi:cloud-sleet-fill","bi:cloud-snow","bi:cloud-snow-fill","bi:cloud-sun","bi:cloud-sun-fill","bi:cloud-upload","bi:cloud-upload-fill","bi:clouds","bi:clouds-fill","bi:cloudy","bi:cloudy-fill","bi:code","bi:code-slash","bi:code-square","bi:coin","bi:collection","bi:collection-fill","bi:collection-play","bi:collection-play-fill","bi:columns","bi:columns-gap","bi:command","bi:compass","bi:compass-fill","bi:cone","bi:cone-striped","bi:controller","bi:cookie","bi:copy","bi:cpu","bi:cpu-fill","bi:credit-card","bi:credit-card-2-back","bi:credit-card-2-back-fill","bi:credit-card-2-front","bi:credit-card-2-front-fill","bi:credit-card-fill","bi:crop","bi:crosshair","bi:crosshair2","bi:cup","bi:cup-fill","bi:cup-hot","bi:cup-hot-fill","bi:cup-straw","bi:currency-bitcoin","bi:currency-dollar","bi:currency-euro","bi:currency-exchange","bi:currency-pound","bi:currency-rupee","bi:currency-yen","bi:cursor","bi:cursor-fill","bi:cursor-text","bi:dash","bi:dash-circle","bi:dash-circle-dotted","bi:dash-circle-fill","bi:dash-lg","bi:dash-square","bi:dash-square-dotted","bi:dash-square-fill","bi:database","bi:database-add","bi:database-check","bi:database-dash","bi:database-down","bi:database-exclamation","bi:database-fill","bi:database-fill-add","bi:database-fill-check","bi:database-fill-dash","bi:database-fill-down","bi:database-fill-exclamation","bi:database-fill-gear","bi:database-fill-lock","bi:database-fill-slash","bi:database-fill-up","bi:database-fill-x","bi:database-gear","bi:database-lock","bi:database-slash","bi:database-up","bi:database-x","bi:device-hdd","bi:device-hdd-fill","bi:device-ssd","bi:device-ssd-fill","bi:diagram-2","bi:diagram-2-fill","bi:diagram-3","bi:diagram-3-fill","bi:diamond","bi:diamond-fill","bi:diamond-half","bi:dice-1","bi:dice-1-fill","bi:dice-2","bi:dice-2-fill","bi:dice-3","bi:dice-3-fill","bi:dice-4","bi:dice-4-fill","bi:dice-5","bi:dice-5-fill","bi:dice-6","bi:dice-6-fill","bi:disc","bi:disc-fill","bi:discord","bi:display","bi:display-fill","bi:displayport","bi:displayport-fill","bi:distribute-horizontal","bi:distribute-vertical","bi:door-closed","bi:door-closed-fill","bi:door-open","bi:door-open-fill","bi:dot","bi:download","bi:dpad","bi:dpad-fill","bi:dribbble","bi:dropbox","bi:droplet","bi:droplet-fill","bi:droplet-half","bi:duffle","bi:duffle-fill","bi:ear","bi:ear-fill","bi:earbuds","bi:easel","bi:easel-fill","bi:easel2","bi:easel2-fill","bi:easel3","bi:easel3-fill","bi:egg","bi:egg-fill","bi:egg-fried","bi:eject","bi:eject-fill","bi:emoji-angry","bi:emoji-angry-fill","bi:emoji-astonished","bi:emoji-astonished-fill","bi:emoji-dizzy","bi:emoji-dizzy-fill","bi:emoji-expressionless","bi:emoji-expressionless-fill","bi:emoji-frown","bi:emoji-frown-fill","bi:emoji-grimace","bi:emoji-grimace-fill","bi:emoji-grin","bi:emoji-grin-fill","bi:emoji-heart-eyes","bi:emoji-heart-eyes-fill","bi:emoji-kiss","bi:emoji-kiss-fill","bi:emoji-laughing","bi:emoji-laughing-fill","bi:emoji-neutral","bi:emoji-neutral-fill","bi:emoji-smile","bi:emoji-smile-fill","bi:emoji-smile-upside-down","bi:emoji-smile-upside-down-fill","bi:emoji-sunglasses","bi:emoji-sunglasses-fill","bi:emoji-surprise","bi:emoji-surprise-fill","bi:emoji-tear","bi:emoji-tear-fill","bi:emoji-wink","bi:emoji-wink-fill","bi:envelope","bi:envelope-arrow-down","bi:envelope-arrow-down-fill","bi:envelope-arrow-up","bi:envelope-arrow-up-fill","bi:envelope-at","bi:envelope-at-fill","bi:envelope-check","bi:envelope-check-fill","bi:envelope-dash","bi:envelope-dash-fill","bi:envelope-exclamation","bi:envelope-exclamation-fill","bi:envelope-fill","bi:envelope-heart","bi:envelope-heart-fill","bi:envelope-open","bi:envelope-open-fill","bi:envelope-open-heart","bi:envelope-open-heart-fill","bi:envelope-paper","bi:envelope-paper-fill","bi:envelope-paper-heart","bi:envelope-paper-heart-fill","bi:envelope-plus","bi:envelope-plus-fill","bi:envelope-slash","bi:envelope-slash-fill","bi:envelope-x","bi:envelope-x-fill","bi:eraser","bi:eraser-fill","bi:escape","bi:ethernet","bi:ev-front","bi:ev-front-fill","bi:ev-station","bi:ev-station-fill","bi:exclamation","bi:exclamation-circle","bi:exclamation-circle-fill","bi:exclamation-diamond","bi:exclamation-diamond-fill","bi:exclamation-lg","bi:exclamation-octagon","bi:exclamation-octagon-fill","bi:exclamation-square","bi:exclamation-square-fill","bi:exclamation-triangle","bi:exclamation-triangle-fill","bi:exclude","bi:explicit","bi:explicit-fill","bi:exposure","bi:eye","bi:eye-fill","bi:eye-slash","bi:eye-slash-fill","bi:eyedropper","bi:eyeglasses","bi:facebook","bi:fan","bi:fast-forward","bi:fast-forward-btn","bi:fast-forward-btn-fill","bi:fast-forward-circle","bi:fast-forward-circle-fill","bi:fast-forward-fill","bi:feather","bi:feather2","bi:file","bi:file-arrow-down","bi:file-arrow-down-fill","bi:file-arrow-up","bi:file-arrow-up-fill","bi:file-bar-graph","bi:file-bar-graph-fill","bi:file-binary","bi:file-binary-fill","bi:file-break","bi:file-break-fill","bi:file-check","bi:file-check-fill","bi:file-code","bi:file-code-fill","bi:file-diff","bi:file-diff-fill","bi:file-earmark","bi:file-earmark-arrow-down","bi:file-earmark-arrow-down-fill","bi:file-earmark-arrow-up","bi:file-earmark-arrow-up-fill","bi:file-earmark-bar-graph","bi:file-earmark-bar-graph-fill","bi:file-earmark-binary","bi:file-earmark-binary-fill","bi:file-earmark-break","bi:file-earmark-break-fill","bi:file-earmark-check","bi:file-earmark-check-fill","bi:file-earmark-code","bi:file-earmark-code-fill","bi:file-earmark-diff","bi:file-earmark-diff-fill","bi:file-earmark-easel","bi:file-earmark-easel-fill","bi:file-earmark-excel","bi:file-earmark-excel-fill","bi:file-earmark-fill","bi:file-earmark-font","bi:file-earmark-font-fill","bi:file-earmark-image","bi:file-earmark-image-fill","bi:file-earmark-lock","bi:file-earmark-lock-fill","bi:file-earmark-lock2","bi:file-earmark-lock2-fill","bi:file-earmark-medical","bi:file-earmark-medical-fill","bi:file-earmark-minus","bi:file-earmark-minus-fill","bi:file-earmark-music","bi:file-earmark-music-fill","bi:file-earmark-pdf","bi:file-earmark-pdf-fill","bi:file-earmark-person","bi:file-earmark-person-fill","bi:file-earmark-play","bi:file-earmark-play-fill","bi:file-earmark-plus","bi:file-earmark-plus-fill","bi:file-earmark-post","bi:file-earmark-post-fill","bi:file-earmark-ppt","bi:file-earmark-ppt-fill","bi:file-earmark-richtext","bi:file-earmark-richtext-fill","bi:file-earmark-ruled","bi:file-earmark-ruled-fill","bi:file-earmark-slides","bi:file-earmark-slides-fill","bi:file-earmark-spreadsheet","bi:file-earmark-spreadsheet-fill","bi:file-earmark-text","bi:file-earmark-text-fill","bi:file-earmark-word","bi:file-earmark-word-fill","bi:file-earmark-x","bi:file-earmark-x-fill","bi:file-earmark-zip","bi:file-earmark-zip-fill","bi:file-easel","bi:file-easel-fill","bi:file-excel","bi:file-excel-fill","bi:file-fill","bi:file-font","bi:file-font-fill","bi:file-image","bi:file-image-fill","bi:file-lock","bi:file-lock-fill","bi:file-lock2","bi:file-lock2-fill","bi:file-medical","bi:file-medical-fill","bi:file-minus","bi:file-minus-fill","bi:file-music","bi:file-music-fill","bi:file-pdf","bi:file-pdf-fill","bi:file-person","bi:file-person-fill","bi:file-play","bi:file-play-fill","bi:file-plus","bi:file-plus-fill","bi:file-post","bi:file-post-fill","bi:file-ppt","bi:file-ppt-fill","bi:file-richtext","bi:file-richtext-fill","bi:file-ruled","bi:file-ruled-fill","bi:file-slides","bi:file-slides-fill","bi:file-spreadsheet","bi:file-spreadsheet-fill","bi:file-text","bi:file-text-fill","bi:file-word","bi:file-word-fill","bi:file-x","bi:file-x-fill","bi:file-zip","bi:file-zip-fill","bi:files","bi:files-alt","bi:filetype-aac","bi:filetype-ai","bi:filetype-bmp","bi:filetype-cs","bi:filetype-css","bi:filetype-csv","bi:filetype-doc","bi:filetype-docx","bi:filetype-exe","bi:filetype-gif","bi:filetype-heic","bi:filetype-html","bi:filetype-java","bi:filetype-jpg","bi:filetype-js","bi:filetype-json","bi:filetype-jsx","bi:filetype-key","bi:filetype-m4p","bi:filetype-md","bi:filetype-mdx","bi:filetype-mov","bi:filetype-mp3","bi:filetype-mp4","bi:filetype-otf","bi:filetype-pdf","bi:filetype-php","bi:filetype-png","bi:filetype-ppt","bi:filetype-pptx","bi:filetype-psd","bi:filetype-py","bi:filetype-raw","bi:filetype-rb","bi:filetype-sass","bi:filetype-scss","bi:filetype-sh","bi:filetype-sql","bi:filetype-svg","bi:filetype-tiff","bi:filetype-tsx","bi:filetype-ttf","bi:filetype-txt","bi:filetype-wav","bi:filetype-woff","bi:filetype-xls","bi:filetype-xlsx","bi:filetype-xml","bi:filetype-yml","bi:film","bi:filter","bi:filter-circle","bi:filter-circle-fill","bi:filter-left","bi:filter-right","bi:filter-square","bi:filter-square-fill","bi:fingerprint","bi:fire","bi:flag","bi:flag-fill","bi:floppy","bi:floppy-fill","bi:floppy2","bi:floppy2-fill","bi:flower1","bi:flower2","bi:flower3","bi:folder","bi:folder-check","bi:folder-fill","bi:folder-minus","bi:folder-plus","bi:folder-symlink","bi:folder-symlink-fill","bi:folder-x","bi:folder2","bi:folder2-open","bi:fonts","bi:forward","bi:forward-fill","bi:front","bi:fuel-pump","bi:fuel-pump-diesel","bi:fuel-pump-diesel-fill","bi:fuel-pump-fill","bi:fullscreen","bi:fullscreen-exit","bi:funnel","bi:funnel-fill","bi:gear","bi:gear-fill","bi:gear-wide","bi:gear-wide-connected","bi:gem","bi:gender-ambiguous","bi:gender-female","bi:gender-male","bi:gender-neuter","bi:gender-trans","bi:geo","bi:geo-alt","bi:geo-alt-fill","bi:geo-fill","bi:gift","bi:gift-fill","bi:git","bi:github","bi:gitlab","bi:globe","bi:globe-americas","bi:globe-asia-australia","bi:globe-central-south-asia","bi:globe-europe-africa","bi:globe2","bi:google","bi:google-play","bi:gpu-card","bi:graph-down","bi:graph-down-arrow","bi:graph-up","bi:graph-up-arrow","bi:grid","bi:grid-1x2","bi:grid-1x2-fill","bi:grid-3x2","bi:grid-3x2-gap","bi:grid-3x2-gap-fill","bi:grid-3x3","bi:grid-3x3-gap","bi:grid-3x3-gap-fill","bi:grid-fill","bi:grip-horizontal","bi:grip-vertical","bi:h-circle","bi:h-circle-fill","bi:h-square","bi:h-square-fill","bi:hammer","bi:hand-index","bi:hand-index-fill","bi:hand-index-thumb","bi:hand-index-thumb-fill","bi:hand-thumbs-down","bi:hand-thumbs-down-fill","bi:hand-thumbs-up","bi:hand-thumbs-up-fill","bi:handbag","bi:handbag-fill","bi:hash","bi:hdd","bi:hdd-fill","bi:hdd-network","bi:hdd-network-fill","bi:hdd-rack","bi:hdd-rack-fill","bi:hdd-stack","bi:hdd-stack-fill","bi:hdmi","bi:hdmi-fill","bi:headphones","bi:headset","bi:headset-vr","bi:heart","bi:heart-arrow","bi:heart-fill","bi:heart-half","bi:heart-pulse","bi:heart-pulse-fill","bi:heartbreak","bi:heartbreak-fill","bi:hearts","bi:heptagon","bi:heptagon-fill","bi:heptagon-half","bi:hexagon","bi:hexagon-fill","bi:hexagon-half","bi:highlighter","bi:highlights","bi:hospital","bi:hospital-fill","bi:hourglass","bi:hourglass-bottom","bi:hourglass-split","bi:hourglass-top","bi:house","bi:house-add","bi:house-add-fill","bi:house-check","bi:house-check-fill","bi:house-dash","bi:house-dash-fill","bi:house-door","bi:house-door-fill","bi:house-down","bi:house-down-fill","bi:house-exclamation","bi:house-exclamation-fill","bi:house-fill","bi:house-gear","bi:house-gear-fill","bi:house-heart","bi:house-heart-fill","bi:house-lock","bi:house-lock-fill","bi:house-slash","bi:house-slash-fill","bi:house-up","bi:house-up-fill","bi:house-x","bi:house-x-fill","bi:houses","bi:houses-fill","bi:hr","bi:hurricane","bi:hypnotize","bi:image","bi:image-alt","bi:image-fill","bi:images","bi:inbox","bi:inbox-fill","bi:inboxes","bi:inboxes-fill","bi:incognito","bi:indent","bi:infinity","bi:info","bi:info-circle","bi:info-circle-fill","bi:info-lg","bi:info-square","bi:info-square-fill","bi:input-cursor","bi:input-cursor-text","bi:instagram","bi:intersect","bi:journal","bi:journal-album","bi:journal-arrow-down","bi:journal-arrow-up","bi:journal-bookmark","bi:journal-bookmark-fill","bi:journal-check","bi:journal-code","bi:journal-medical","bi:journal-minus","bi:journal-plus","bi:journal-richtext","bi:journal-text","bi:journal-x","bi:journals","bi:joystick","bi:justify","bi:justify-left","bi:justify-right","bi:kanban","bi:kanban-fill","bi:key","bi:key-fill","bi:keyboard","bi:keyboard-fill","bi:ladder","bi:lamp","bi:lamp-fill","bi:laptop","bi:laptop-fill","bi:layer-backward","bi:layer-forward","bi:layers","bi:layers-fill","bi:layers-half","bi:layout-sidebar","bi:layout-sidebar-inset","bi:layout-sidebar-inset-reverse","bi:layout-sidebar-reverse","bi:layout-split","bi:layout-text-sidebar","bi:layout-text-sidebar-reverse","bi:layout-text-window","bi:layout-text-window-reverse","bi:layout-three-columns","bi:layout-wtf","bi:life-preserver","bi:lightbulb","bi:lightbulb-fill","bi:lightbulb-off","bi:lightbulb-off-fill","bi:lightning","bi:lightning-charge","bi:lightning-charge-fill","bi:lightning-fill","bi:line","bi:link","bi:link-45deg","bi:linkedin","bi:list","bi:list-check","bi:list-columns","bi:list-columns-reverse","bi:list-nested","bi:list-ol","bi:list-stars","bi:list-task","bi:list-ul","bi:lock","bi:lock-fill","bi:luggage","bi:luggage-fill","bi:lungs","bi:lungs-fill","bi:magic","bi:magnet","bi:magnet-fill","bi:mailbox","bi:mailbox-flag","bi:mailbox2","bi:mailbox2-flag","bi:map","bi:map-fill","bi:markdown","bi:markdown-fill","bi:marker-tip","bi:mask","bi:mastodon","bi:medium","bi:megaphone","bi:megaphone-fill","bi:memory","bi:menu-app","bi:menu-app-fill","bi:menu-button","bi:menu-button-fill","bi:menu-button-wide","bi:menu-button-wide-fill","bi:menu-down","bi:menu-up","bi:messenger","bi:meta","bi:mic","bi:mic-fill","bi:mic-mute","bi:mic-mute-fill","bi:microsoft","bi:microsoft-teams","bi:minecart","bi:minecart-loaded","bi:modem","bi:modem-fill","bi:moisture","bi:moon","bi:moon-fill","bi:moon-stars","bi:moon-stars-fill","bi:mortarboard","bi:mortarboard-fill","bi:motherboard","bi:motherboard-fill","bi:mouse","bi:mouse-fill","bi:mouse2","bi:mouse2-fill","bi:mouse3","bi:mouse3-fill","bi:music-note","bi:music-note-beamed","bi:music-note-list","bi:music-player","bi:music-player-fill","bi:newspaper","bi:nintendo-switch","bi:node-minus","bi:node-minus-fill","bi:node-plus","bi:node-plus-fill","bi:noise-reduction","bi:nut","bi:nut-fill","bi:nvidia","bi:nvme","bi:nvme-fill","bi:octagon","bi:octagon-fill","bi:octagon-half","bi:opencollective","bi:optical-audio","bi:optical-audio-fill","bi:option","bi:outlet","bi:p-circle","bi:p-circle-fill","bi:p-square","bi:p-square-fill","bi:paint-bucket","bi:palette","bi:palette-fill","bi:palette2","bi:paperclip","bi:paragraph","bi:pass","bi:pass-fill","bi:passport","bi:passport-fill","bi:patch-check","bi:patch-check-fill","bi:patch-exclamation","bi:patch-exclamation-fill","bi:patch-minus","bi:patch-minus-fill","bi:patch-plus","bi:patch-plus-fill","bi:patch-question","bi:patch-question-fill","bi:pause","bi:pause-btn","bi:pause-btn-fill","bi:pause-circle","bi:pause-circle-fill","bi:pause-fill","bi:paypal","bi:pc","bi:pc-display","bi:pc-display-horizontal","bi:pc-horizontal","bi:pci-card","bi:pci-card-network","bi:pci-card-sound","bi:peace","bi:peace-fill","bi:pen","bi:pen-fill","bi:pencil","bi:pencil-fill","bi:pencil-square","bi:pentagon","bi:pentagon-fill","bi:pentagon-half","bi:people","bi:people-fill","bi:percent","bi:person","bi:person-add","bi:person-arms-up","bi:person-badge","bi:person-badge-fill","bi:person-bounding-box","bi:person-check","bi:person-check-fill","bi:person-circle","bi:person-dash","bi:person-dash-fill","bi:person-down","bi:person-exclamation","bi:person-fill","bi:person-fill-add","bi:person-fill-check","bi:person-fill-dash","bi:person-fill-down","bi:person-fill-exclamation","bi:person-fill-gear","bi:person-fill-lock","bi:person-fill-slash","bi:person-fill-up","bi:person-fill-x","bi:person-gear","bi:person-heart","bi:person-hearts","bi:person-lines-fill","bi:person-lock","bi:person-plus","bi:person-plus-fill","bi:person-raised-hand","bi:person-rolodex","bi:person-slash","bi:person-square","bi:person-standing","bi:person-standing-dress","bi:person-up","bi:person-vcard","bi:person-vcard-fill","bi:person-video","bi:person-video2","bi:person-video3","bi:person-walking","bi:person-wheelchair","bi:person-workspace","bi:person-x","bi:person-x-fill","bi:phone","bi:phone-fill","bi:phone-flip","bi:phone-landscape","bi:phone-landscape-fill","bi:phone-vibrate","bi:phone-vibrate-fill","bi:pie-chart","bi:pie-chart-fill","bi:piggy-bank","bi:piggy-bank-fill","bi:pin","bi:pin-angle","bi:pin-angle-fill","bi:pin-fill","bi:pin-map","bi:pin-map-fill","bi:pinterest","bi:pip","bi:pip-fill","bi:play","bi:play-btn","bi:play-btn-fill","bi:play-circle","bi:play-circle-fill","bi:play-fill","bi:playstation","bi:plug","bi:plug-fill","bi:plugin","bi:plus","bi:plus-circle","bi:plus-circle-dotted","bi:plus-circle-fill","bi:plus-lg","bi:plus-slash-minus","bi:plus-square","bi:plus-square-dotted","bi:plus-square-fill","bi:postage","bi:postage-fill","bi:postage-heart","bi:postage-heart-fill","bi:postcard","bi:postcard-fill","bi:postcard-heart","bi:postcard-heart-fill","bi:power","bi:prescription","bi:prescription2","bi:printer","bi:printer-fill","bi:projector","bi:projector-fill","bi:puzzle","bi:puzzle-fill","bi:qr-code","bi:qr-code-scan","bi:question","bi:question-circle","bi:question-circle-fill","bi:question-diamond","bi:question-diamond-fill","bi:question-lg","bi:question-octagon","bi:question-octagon-fill","bi:question-square","bi:question-square-fill","bi:quora","bi:quote","bi:r-circle","bi:r-circle-fill","bi:r-square","bi:r-square-fill","bi:radar","bi:radioactive","bi:rainbow","bi:receipt","bi:receipt-cutoff","bi:reception-0","bi:reception-1","bi:reception-2","bi:reception-3","bi:reception-4","bi:record","bi:record-btn","bi:record-btn-fill","bi:record-circle","bi:record-circle-fill","bi:record-fill","bi:record2","bi:record2-fill","bi:recycle","bi:reddit","bi:regex","bi:repeat","bi:repeat-1","bi:reply","bi:reply-all","bi:reply-all-fill","bi:reply-fill","bi:rewind","bi:rewind-btn","bi:rewind-btn-fill","bi:rewind-circle","bi:rewind-circle-fill","bi:rewind-fill","bi:robot","bi:rocket","bi:rocket-fill","bi:rocket-takeoff","bi:rocket-takeoff-fill","bi:router","bi:router-fill","bi:rss","bi:rss-fill","bi:rulers","bi:safe","bi:safe-fill","bi:safe2","bi:safe2-fill","bi:save","bi:save-fill","bi:save2","bi:save2-fill","bi:scissors","bi:scooter","bi:screwdriver","bi:sd-card","bi:sd-card-fill","bi:search","bi:search-heart","bi:search-heart-fill","bi:segmented-nav","bi:send","bi:send-arrow-down","bi:send-arrow-down-fill","bi:send-arrow-up","bi:send-arrow-up-fill","bi:send-check","bi:send-check-fill","bi:send-dash","bi:send-dash-fill","bi:send-exclamation","bi:send-exclamation-fill","bi:send-fill","bi:send-plus","bi:send-plus-fill","bi:send-slash","bi:send-slash-fill","bi:send-x","bi:send-x-fill","bi:server","bi:shadows","bi:share","bi:share-fill","bi:shield","bi:shield-check","bi:shield-exclamation","bi:shield-fill","bi:shield-fill-check","bi:shield-fill-exclamation","bi:shield-fill-minus","bi:shield-fill-plus","bi:shield-fill-x","bi:shield-lock","bi:shield-lock-fill","bi:shield-minus","bi:shield-plus","bi:shield-shaded","bi:shield-slash","bi:shield-slash-fill","bi:shield-x","bi:shift","bi:shift-fill","bi:shop","bi:shop-window","bi:shuffle","bi:sign-dead-end","bi:sign-dead-end-fill","bi:sign-do-not-enter","bi:sign-do-not-enter-fill","bi:sign-intersection","bi:sign-intersection-fill","bi:sign-intersection-side","bi:sign-intersection-side-fill","bi:sign-intersection-t","bi:sign-intersection-t-fill","bi:sign-intersection-y","bi:sign-intersection-y-fill","bi:sign-merge-left","bi:sign-merge-left-fill","bi:sign-merge-right","bi:sign-merge-right-fill","bi:sign-no-left-turn","bi:sign-no-left-turn-fill","bi:sign-no-parking","bi:sign-no-parking-fill","bi:sign-no-right-turn","bi:sign-no-right-turn-fill","bi:sign-railroad","bi:sign-railroad-fill","bi:sign-stop","bi:sign-stop-fill","bi:sign-stop-lights","bi:sign-stop-lights-fill","bi:sign-turn-left","bi:sign-turn-left-fill","bi:sign-turn-right","bi:sign-turn-right-fill","bi:sign-turn-slight-left","bi:sign-turn-slight-left-fill","bi:sign-turn-slight-right","bi:sign-turn-slight-right-fill","bi:sign-yield","bi:sign-yield-fill","bi:signal","bi:signpost","bi:signpost-2","bi:signpost-2-fill","bi:signpost-fill","bi:signpost-split","bi:signpost-split-fill","bi:sim","bi:sim-fill","bi:sim-slash","bi:sim-slash-fill","bi:sina-weibo","bi:skip-backward","bi:skip-backward-btn","bi:skip-backward-btn-fill","bi:skip-backward-circle","bi:skip-backward-circle-fill","bi:skip-backward-fill","bi:skip-end","bi:skip-end-btn","bi:skip-end-btn-fill","bi:skip-end-circle","bi:skip-end-circle-fill","bi:skip-end-fill","bi:skip-forward","bi:skip-forward-btn","bi:skip-forward-btn-fill","bi:skip-forward-circle","bi:skip-forward-circle-fill","bi:skip-forward-fill","bi:skip-start","bi:skip-start-btn","bi:skip-start-btn-fill","bi:skip-start-circle","bi:skip-start-circle-fill","bi:skip-start-fill","bi:skype","bi:slack","bi:slash","bi:slash-circle","bi:slash-circle-fill","bi:slash-lg","bi:slash-square","bi:slash-square-fill","bi:sliders","bi:sliders2","bi:sliders2-vertical","bi:smartwatch","bi:snapchat","bi:snow","bi:snow2","bi:snow3","bi:sort-alpha-down","bi:sort-alpha-down-alt","bi:sort-alpha-up","bi:sort-alpha-up-alt","bi:sort-down","bi:sort-down-alt","bi:sort-numeric-down","bi:sort-numeric-down-alt","bi:sort-numeric-up","bi:sort-numeric-up-alt","bi:sort-up","bi:sort-up-alt","bi:soundwave","bi:sourceforge","bi:speaker","bi:speaker-fill","bi:speedometer","bi:speedometer2","bi:spellcheck","bi:spotify","bi:square","bi:square-fill","bi:square-half","bi:stack","bi:stack-overflow","bi:star","bi:star-fill","bi:star-half","bi:stars","bi:steam","bi:stickies","bi:stickies-fill","bi:sticky","bi:sticky-fill","bi:stop","bi:stop-btn","bi:stop-btn-fill","bi:stop-circle","bi:stop-circle-fill","bi:stop-fill","bi:stoplights","bi:stoplights-fill","bi:stopwatch","bi:stopwatch-fill","bi:strava","bi:stripe","bi:subscript","bi:substack","bi:subtract","bi:suit-club","bi:suit-club-fill","bi:suit-diamond","bi:suit-diamond-fill","bi:suit-heart","bi:suit-heart-fill","bi:suit-spade","bi:suit-spade-fill","bi:suitcase","bi:suitcase-fill","bi:suitcase-lg","bi:suitcase-lg-fill","bi:suitcase2","bi:suitcase2-fill","bi:sun","bi:sun-fill","bi:sunglasses","bi:sunrise","bi:sunrise-fill","bi:sunset","bi:sunset-fill","bi:superscript","bi:symmetry-horizontal","bi:symmetry-vertical","bi:table","bi:tablet","bi:tablet-fill","bi:tablet-landscape","bi:tablet-landscape-fill","bi:tag","bi:tag-fill","bi:tags","bi:tags-fill","bi:taxi-front","bi:taxi-front-fill","bi:telegram","bi:telephone","bi:telephone-fill","bi:telephone-forward","bi:telephone-forward-fill","bi:telephone-inbound","bi:telephone-inbound-fill","bi:telephone-minus","bi:telephone-minus-fill","bi:telephone-outbound","bi:telephone-outbound-fill","bi:telephone-plus","bi:telephone-plus-fill","bi:telephone-x","bi:telephone-x-fill","bi:tencent-qq","bi:terminal","bi:terminal-dash","bi:terminal-fill","bi:terminal-plus","bi:terminal-split","bi:terminal-x","bi:text-center","bi:text-indent-left","bi:text-indent-right","bi:text-left","bi:text-paragraph","bi:text-right","bi:text-wrap","bi:textarea","bi:textarea-resize","bi:textarea-t","bi:thermometer","bi:thermometer-half","bi:thermometer-high","bi:thermometer-low","bi:thermometer-snow","bi:thermometer-sun","bi:threads","bi:threads-fill","bi:three-dots","bi:three-dots-vertical","bi:thunderbolt","bi:thunderbolt-fill","bi:ticket","bi:ticket-detailed","bi:ticket-detailed-fill","bi:ticket-fill","bi:ticket-perforated","bi:ticket-perforated-fill","bi:tiktok","bi:toggle-off","bi:toggle-on","bi:toggle2-off","bi:toggle2-on","bi:toggles","bi:toggles2","bi:tools","bi:tornado","bi:train-freight-front","bi:train-freight-front-fill","bi:train-front","bi:train-front-fill","bi:train-lightrail-front","bi:train-lightrail-front-fill","bi:translate","bi:transparency","bi:trash","bi:trash-fill","bi:trash2","bi:trash2-fill","bi:trash3","bi:trash3-fill","bi:tree","bi:tree-fill","bi:trello","bi:triangle","bi:triangle-fill","bi:triangle-half","bi:trophy","bi:trophy-fill","bi:tropical-storm","bi:truck","bi:truck-flatbed","bi:truck-front","bi:truck-front-fill","bi:tsunami","bi:tv","bi:tv-fill","bi:twitch","bi:twitter","bi:twitter-x","bi:type","bi:type-bold","bi:type-h1","bi:type-h2","bi:type-h3","bi:type-h4","bi:type-h5","bi:type-h6","bi:type-italic","bi:type-strikethrough","bi:type-underline","bi:ubuntu","bi:ui-checks","bi:ui-checks-grid","bi:ui-radios","bi:ui-radios-grid","bi:umbrella","bi:umbrella-fill","bi:unindent","bi:union","bi:unity","bi:universal-access","bi:universal-access-circle","bi:unlock","bi:unlock-fill","bi:upc","bi:upc-scan","bi:upload","bi:usb","bi:usb-c","bi:usb-c-fill","bi:usb-drive","bi:usb-drive-fill","bi:usb-fill","bi:usb-micro","bi:usb-micro-fill","bi:usb-mini","bi:usb-mini-fill","bi:usb-plug","bi:usb-plug-fill","bi:usb-symbol","bi:valentine","bi:valentine2","bi:vector-pen","bi:view-list","bi:view-stacked","bi:vignette","bi:vimeo","bi:vinyl","bi:vinyl-fill","bi:virus","bi:virus2","bi:voicemail","bi:volume-down","bi:volume-down-fill","bi:volume-mute","bi:volume-mute-fill","bi:volume-off","bi:volume-off-fill","bi:volume-up","bi:volume-up-fill","bi:vr","bi:wallet","bi:wallet-fill","bi:wallet2","bi:watch","bi:water","bi:webcam","bi:webcam-fill","bi:wechat","bi:whatsapp","bi:wifi","bi:wifi-1","bi:wifi-2","bi:wifi-off","bi:wikipedia","bi:wind","bi:window","bi:window-dash","bi:window-desktop","bi:window-dock","bi:window-fullscreen","bi:window-plus","bi:window-sidebar","bi:window-split","bi:window-stack","bi:window-x","bi:windows","bi:wordpress","bi:wrench","bi:wrench-adjustable","bi:wrench-adjustable-circle","bi:wrench-adjustable-circle-fill","bi:wrench-adjustable-cricle","bi:x","bi:x-circle","bi:x-circle-fill","bi:x-diamond","bi:x-diamond-fill","bi:x-lg","bi:x-octagon","bi:x-octagon-fill","bi:x-square","bi:x-square-fill","bi:xbox","bi:yelp","bi:yin-yang","bi:youtube","bi:zoom-in","bi:zoom-out"],Ei={class:"w-full"},Ii={key:1,class:"icon-container"},Ti={class:"arco"},Ci=["onClick"],Vi={class:"arco"},Li=["onClick"],Oi={__name:"index",props:{modelValue:{type:String},preview:{type:Boolean,default:!0}},emits:["update:modelValue"],setup(e,{emit:a}){const o=ca([]),n=_(!1),i=e,c=a,t=ze({get(){return i.modelValue},set(b){(/^[^\d].*/.test(b)&&b||!b)&&c("update:modelValue",b)}});for(let b in Co)o.push(b);o.pop();const m=(b,x)=>{t.value=b,n.value=!1};return(b,x)=>{const S=s("a-input"),g=s("sa-icon"),h=s("a-button"),p=s("a-input-group"),k=s("a-tab-pane"),O=s("a-tabs"),I=s("a-modal");return r(),C("div",Ei,[l(p,{class:"w-full"},{default:d(()=>[i.preview?(r(),A(S,{key:0,placeholder:"请点击右侧按钮选择图标","allow-clear":"",modelValue:t.value,"onUpdate:modelValue":x[0]||(x[0]=f=>t.value=f)},null,8,["modelValue"])):U("",!0),i.preview?(r(),C("div",Ii,[t.value?(r(),A(g,{key:0,icon:t.value},null,8,["icon"])):U("",!0)])):U("",!0),l(h,{type:"primary",onClick:x[1]||(x[1]=()=>n.value=!0)},{default:d(()=>x[3]||(x[3]=[L("选择图标")])),_:1})]),_:1}),l(I,{visible:n.value,"onUpdate:visible":x[2]||(x[2]=f=>n.value=f),width:"800px",draggable:"",footer:!1},{title:d(()=>x[4]||(x[4]=[L("选择图标")])),default:d(()=>[l(O,{class:"tabs"},{default:d(()=>[l(k,{key:"arco",title:"Arco Design"},{default:d(()=>[q("ul",Ti,[(r(!0),C(oe,null,de(o,f=>(r(),C("li",{key:f,onClick:E=>m(f,"arco")},[(r(),A(Pe(f)))],8,Ci))),128))])]),_:1}),l(k,{key:"bi",title:"Bootstrap Icons"},{default:d(()=>[q("ul",Vi,[(r(!0),C(oe,null,de(B(Ai),f=>(r(),C("li",{key:f,onClick:E=>m(f,"iconify")},[l(B(Pa),{icon:f},null,8,["icon"])],8,Li))),128))])]),_:1})]),_:1})]),_:1},8,["visible"])])}}},Ri=ye(Oi,[["__scopeId","data-v-97e434b4"]]);qo([Mo,Fo,$o,No,Uo,Bo,Ho,Wo,Go,Yo,Jo]);const ji={install(e){e.component("MaWangEditor",hn),e.component("MaColorPicker",gn),e.component("MaCityLinkage",vn),e.component("SaChart",yn),e.component("SaCheckbox",_n),e.component("SaRadio",kn),e.component("SaSelect",wn),e.component("SaSwitch",xn),e.component("SaTable",Mn),e.component("SaTreeSlider",$n),e.component("SaResource",Xn),e.component("SaResourceButton",ni),e.component("SaDict",di),e.component("SaUser",mi),e.component("SaUploadImage",fi),e.component("SaUploadFile",xi),e.component("SaIcon",Si),e.component("SaIconPicker",Ri)}},Pi={__name:"App",setup(e){const a=ua(),o=_(a.language==="zh_CN"?Vo:Lo);return(n,i)=>{const c=s("router-view"),t=s("a-config-provider");return r(),A(t,{locale:o.value,"update-at-scroll":!0},{default:d(()=>[l(c)]),_:1},8,["locale"])}}},Di={},zi=Object.freeze(Object.defineProperty({__proto__:null,default:Di},Symbol.toStringTag,{value:"Module"})),qi={loadingText:"数据加载中...",searchFileNotice:"文件名搜索",searchResource:"搜索资源类型",saveNetworkImage:"保存网络图片",networkImageNotice:"请粘贴网络图片地址",ok:"确定"},Mi=Object.freeze(Object.defineProperty({__proto__:null,default:qi},Symbol.toStringTag,{value:"Module"})),Fi={openForm:"公共表单",home:"首页",dashboard:"仪表盘",userCenter:"个人中心",message:"消息中心","setting:config":"系统配置",demo:"组件演示",permission:"权限","system:user":"用户管理","system:role":"角色管理","system:dept":"部门管理","system:menu":"菜单管理","system:post":"岗位管理",dataCenter:"数据","system:dict":"数据字典","system:attachment":"附件管理","system:dataMaintain":"数据表维护","system:notice":"系统公告",apps:"应用中心","system:appGroup":"应用分组","system:app":"应用管理",apis:"应用接口","system:apiGroup":"接口分组","system:api":"接口管理",monitor:"监控","system:monitor:server":"服务监控","system:onlineUser":"在线用户","system:cache":"缓存监控","system:monitor:rely":"依赖监控",logs:"日志监控","system:queueLog":"队列日志","system:loginLog":"登录日志","system:operLog":"操作日志","system:apiLog":"接口日志",devTools:"工具","setting:module":"模块管理","setting:code":"代码生成器","setting:code:update":"编辑生成信息","setting:crontab":"定时任务","setting:table":"数据表设计器",systemInterface:"系统接口"},$i=Object.freeze(Object.defineProperty({__proto__:null,default:Fi},Symbol.toStringTag,{value:"Module"})),Ni={mine:"Mine",classics:"经典",businessGray:"商务灰",city:"城市",mineDesc:"以纯净的白色为主，Mine默认皮肤",classicsDesc:"经典的深色侧边栏皮肤",businessGrayDesc:"灰色的百搭与大气，营造商务与稳重",cityDesc:"愿城市每一个角度，都有一份温馨",activated:"已激活",use:"使用"},Ui=Object.freeze(Object.defineProperty({__proto__:null,default:Ni},Symbol.toStringTag,{value:"Module"})),Bi={pageSetting:"页面设置",chinese:"简体中文",english:"English",search:"搜索",store:"应用市场",fullScreen:"全屏",closeFullScreen:"关闭全屏",changeSkin:"换肤",skin:"当前皮肤",layouts:"布局",language:"语言",dark:"黑夜模式",tag:"多标签",water:"水印",waterContent:"水印内容",menuFold:"菜单折叠",menuWidth:"菜单宽度",skinHelp:"设置后台皮肤",layoutsHelp:"设置后台显示方式",languageHelp:"设置页面语言和请求后台语言",darkHelp:"设置页面显示模式",tagHelp:"是否启用多标签方式",waterHelp:"是否显示水印",menuFoldHelp:"系统左侧菜单是否折叠起来",menuWidthHelp:"设置左侧菜单的显示宽度",saveToBackend:"保存到后台",backendSettingTitle:"后台设置",systemPrimaryColor:"系统主色调",personalizedConfig:"个性化配置 ",layout:{classic:"经典",columns:"分栏",banner:"通栏",mixed:"混合"},userCenter:"个人中心",clearCache:"清除缓存",logout:"退出系统",logoutAlert:"退出提示",logoutMessage:"确定要退出登录吗？",operationMessage:{message:"消息",notification:"通知",todo:"待办"},goHome:"回到首页",notFoundPage:"啊哦，访问的页面被火星人劫走了...",login:{slogan:"开箱即用的高质量中后台管理系统",title:"登录",username:"账户",usernameNotice:"请输入账户",password:"密码",passwordNotice:"请输入密码",verifyCode:"请输入验证码",verifyCodeNotice:"请输入正确的验证码",loginBtn:"登录",otherLoginType:"其他登录方式"},verifyCode:{switch:"点击切换验证码",error:"验证码错误",notice:"请输入验证码"},i18n:"开启多语言",i18nHelp:"是否开启多语言功能",ws:"开启Ws",wsHelp:"是否开启Websocket连接",round:"圆角",roundHelp:"是否开启圆角",animation:"切换动画",animationHelp:"工作区页面切换的进场和出场动画效果",animate:{fade:"页面渐隐渐出",sliderLeft:"页面向左渐出",sliderRight:"页面向右渐出",sliderDown:"页面向下渐出",sliderUp:"页面向上渐出"},tags:{refresh:"刷新",fullscreen:"全屏",closeRightTag:"关闭右侧标签",closeLeftTag:"关闭左侧标签",closeTag:"关闭当前标签",closeOtherTag:"关闭其他标签"},noticeTitle:"系统提示",save:"保存",cancel:"取消"},Hi=Object.freeze(Object.defineProperty({__proto__:null,default:Bi},Symbol.toStringTag,{value:"Module"})),Wi={fileHashFail:"获取文件Hash失败，请重试",sizeLimit:"文件大小超过了限制",uploadFailed:"文件上传失败",buttonText:"本地上传",clickUpload:"点击上传",uploadDesc:"将文件拖到此处，或"},Gi=Object.freeze(Object.defineProperty({__proto__:null,default:Wi},Symbol.toStringTag,{value:"Module"})),Yi={name:"菜单管理","system:cache":"系统缓存"},Ji=Object.freeze(Object.defineProperty({__proto__:null,default:Yi},Symbol.toStringTag,{value:"Module"})),Qi={},Zi=Object.freeze(Object.defineProperty({__proto__:null,default:Qi},Symbol.toStringTag,{value:"Module"})),Xi={loadingText:"Loading...",searchFileNotice:"Search file by name",searchResource:"Search resource type",saveNetworkImage:"Save network image",networkImageNotice:"Please paste the web picture address",ok:"OK"},Ki=Object.freeze(Object.defineProperty({__proto__:null,default:Xi},Symbol.toStringTag,{value:"Module"})),ec={openForm:"CRUD",home:"Home",dashboard:"Dashboard",userCenter:"User Center",message:"Message Center","setting:config":"System Setting",demo:"Component Demo",permission:"Permission","system:user":"User Manage","system:role":"Role Manage","system:dept":"Department Manage","system:menu":"Menu Manage","system:post":"Post Manage",dataCenter:"Data Center","system:dict":"Dictionary","system:attachment":"Attached","system:dataMaintain":"Table Maintenance","system:notice":"Notice",apps:"App Center","system:appGroup":"App Group","system:app":"App Manage",apis:"Api Center","system:apiGroup":"Api Group","system:api":"Api Manage",monitor:"Monitor","system:monitor:server":"Server Monitor","system:onlineUser":"Online User","system:cache":"Cache Monitor","system:monitor:rely":"Reliance Monitor",logs:"Logs Monitor","system:queueLog":"Queue Logs","system:loginLog":"Login Logs","system:operLog":"Operation Logs","system:apiLog":"Apis Logs",devTools:"Tools","setting:module":"Module Manage","setting:code":"Code Generator","setting:code:update":"Edit the build information","setting:crontab":"Crontab","setting:table":"Table Designer",systemInterface:"System Apis"},ac=Object.freeze(Object.defineProperty({__proto__:null,default:ec},Symbol.toStringTag,{value:"Module"})),oc={mine:"Mine",classics:"classics",businessGray:"Business gray",city:"City",mineDesc:"Predominantly pure white, Mine defaults to skin",classicsDesc:"Classic dark sidebar skin",businessGrayDesc:"Gray versatility and atmosphere, creating business and stability",cityDesc:"May there be a warmth in every angle of the city",activated:"Activated",use:"Use"},nc=Object.freeze(Object.defineProperty({__proto__:null,default:oc},Symbol.toStringTag,{value:"Module"})),ic={pageSetting:"Page Setting",chinese:"简体中文",english:"English",search:"Search",store:"App Store",fullScreen:"Full Screen",closeFullScreen:"Close Full Screen",changeSkin:"Change Skin",skin:"Skin",layouts:"Layout",language:"Language",dark:"Dark Mode",tag:"Open Tags",water:"Watermark",waterContent:"Watermark content",menuFold:"Menu Fold",menuWidth:"Mene Width",skinHelp:"Set up background skins",layoutsHelp:"Set the background display",languageHelp:"Set the page language and the request background language",darkHelp:"Sets the page display mode",tagHelp:"Whether to enable multi-tab mode",waterHelp:"Whether to display the watermark",menuFoldHelp:"Whether the left menu of the system is collapsed",menuWidthHelp:"Sets the display width of the left menu",saveToBackend:"Save to backend",backendSettingTitle:"Backend setting",systemPrimaryColor:"System Primary Color",personalizedConfig:"Personalized configuration",layout:{classic:"Classic",columns:"Columns",banner:"Banner",mixed:"Mixed"},userCenter:"User Center",clearCache:"Clear Cache",logout:"Logout System",logoutAlert:"Exit prompt",logoutMessage:"Are you sure you want to sign out?",operationMessage:{message:"Message",notification:"Notification",todo:"Todo"},goHome:"Go Home",notFoundPage:"Exit tip Ah oh, the page visited was hijacked by the Martians...",login:{slogan:"High-quality middle and back office management system out of the box",title:"Login System",username:"Username",usernameNotice:"Please enter the username",password:"Passoword",passwordNotice:"Please enter the password",verifyCode:"Please enter the verification code",verifyCodeNotice:"Please enter the correct verification code",loginBtn:"Login in",otherLoginType:"Other ways to sign in"},verifyCode:{switch:"Click Toggle verification code",error:"The verification code is incorrect",notice:"Please enter the verification code"},i18n:"open multi-language",i18nHelp:"Whether to enable the multi-language feature",ws:"open websocket",wsHelp:"Whether to enable the websocket feature",round:"opend round",roundHelp:"Whether to enable the round feature",animation:"Animation",animationHelp:"Page transition animation effect",animate:{fade:"The page fades out",sliderLeft:"The page fades to the left",sliderRight:"The page fades to the right",sliderDown:"The page fades to the down",sliderUp:"The page fades to the up"},tags:{refresh:"Refresh",fullscreen:"Full screen",closeRightTag:"Close right tag",closeLeftTag:"Close left tag",closeTag:"Close current tag",closeOtherTag:"Close other tag"},noticeTitle:"System Prompted",save:"Save",cancel:"Cancel"},cc=Object.freeze(Object.defineProperty({__proto__:null,default:ic},Symbol.toStringTag,{value:"Module"})),dc={fileHashFail:"Get file hash failed, please try again!",sizeLimit:"The file size exceeds the upload limit",uploadFailed:"File upload failed",buttonText:"Local upload",clickUpload:"Click upload",uploadDesc:"Drag the file here, or "},lc=Object.freeze(Object.defineProperty({__proto__:null,default:dc},Symbol.toStringTag,{value:"Module"})),tc=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),Ae=v.local.get("setting"),mc=()=>{const e=()=>{if(Ae.language==="zh_CN")return Object.assign({"./zh_CN/crud.js":zi,"./zh_CN/maResource.js":Mi,"./zh_CN/menus.js":$i,"./zh_CN/skin.js":Ui,"./zh_CN/sys.js":Hi,"./zh_CN/upload.js":Gi,"./zh_CN/user.js":Ji});if(Ae.language==="en")return Object.assign({"./en/crud.js":Zi,"./en/maResource.js":Ki,"./en/menus.js":ac,"./en/skin.js":nc,"./en/sys.js":cc,"./en/upload.js":lc,"./en/user.js":tc})},a=(i,c,t={})=>{const m=i.shift();return i.length>0?(typeof t[m]>"u"&&(t[m]={}),t[m]=a(i,c,t[m])):t[m]=c,t},o=e();let n={[Ae.language]:{}};for(let i in o){const c=i.match(/([A-Za-z0-9_]+)/g);c.shift(),c.pop(),o[i].default&&(n[Ae.language]=a(c,o[i].default,n[Ae.language]))}return n},rc=Qo.createI18n({locale:Ae.language,legacy:!1,globalInjection:!0,fallbackLocale:"zh_CN",messages:mc()}),$a=e=>{const a=sa();return a.codes&&a.codes.includes(e)||a.codes&&a.codes.includes("*")},La=(e,a)=>{const{value:o}=a;if(Array.isArray(o)){if(o.length>0){let n=!1;o.map(i=>{n=$a(i)}),!n&&e.parentNode&&e.parentNode.removeChild(e)}}else throw new Error(`need permission! Like v-auth="['admin','user']"`)},sc={mounted(e,a){La(e,a)},updated(e,a){La(e,a)}},Na=e=>{const a=sa();return a.roles&&a.roles.includes(e)||a.roles&&a.roles.includes("superAdmin")},Oa=(e,a)=>{const{value:o}=a;if(Array.isArray(o)){if(o.length>0){let n=!1;o.map(i=>{n||(n=Na(i))}),!n&&e.parentNode&&e.parentNode.remove()}}else throw new Error(`need role! Like v-role="['seo', 'cfo']"`)},uc={mounted(e,a){Oa(e,a)},updated(e,a){Oa(e,a)}},Ra=(e,a)=>{const{value:o}=a;e.addEventListener("click",async()=>{if(o&&o!=="")try{await ra().toClipboard(o),W.success("已成功复制到剪切板")}catch{W.error("复制失败")}else throw new Error('need for copy content! Like v-copy="Hello World"')})},bc={mounted(e,a){Ra(e,a)},updated(e,a){Ra(e,a)}},pc={install(e){e.directive("auth",sc),e.directive("role",uc),e.directive("copy",bc)}},fc=()=>{const e=te.currentRoute.value,a=pa();De.start(),a.removeKeepAlive(e),a.hidden(),je(()=>{a.addKeepAlive(e),a.display(),De.done()})},hc=e=>{const a=ba(),o=pa();a.addTag(e),o.addKeepAlive(e)},gc=e=>{const a=ba(),o=pa(),n=a.removeTag(e);o.removeKeepAlive(e),te.push({path:n.path,query:v.getRequestParams(n.path)})},vc=(e,a)=>{ta.success({title:e,content:a,closable:!0})},yc=(e,a)=>{ta.info({title:e,content:a,closable:!0})},_c=(e,a)=>{ta.error({title:e,content:a,closable:!0})},kc=e=>$a(e),wc=e=>Na(e),xc=e=>{try{ra().toClipboard(e),W.success("已成功复制到剪切板")}catch{W.error("复制失败")}};function Sc(e,a){var o=null;if(Object.prototype.toString.call(e)!=="[object String]")try{o=JSON.stringify(e)}catch(n){console.error("您传递的json数据格式有误，请核对..."),console.error(n),a(n)}else try{e=e.replace(/(\')/g,'"'),o=JSON.stringify(JSON.parse(e))}catch(n){console.error("您传递的json数据格式有误，请核对...."),console.error(n)}return o}const Ac=(e,a)=>{var o="",n=0,i="  ",c=Sc(e,a);if(!c)return c;var t=[],m=null,b=null,x=[];return c=c.replace(/([\{\}])/g,`\r
$1\r
`),c=c.replace(/([\[\]])/g,`\r
$1\r
`),c=c.replace(/(\,)/g,`$1\r
`),c=c.replace(/(\r\n\r\n)/g,`\r
`),c=c.replace(/\r\n\,/g,","),x=c.split(`\r
`),x.forEach(function(S,g){var h=S.match(/\"/g)?S.match(/\"/g).length:0;h%2&&!m&&(m=g),h%2&&m&&m!=g&&(b=g),m&&b&&(t.push({start:m,end:b}),m=null,b=null)}),t.reverse().forEach(function(S,g){var h=x.slice(S.start,S.end+1);x.splice(S.start,S.end+1-S.start,h.join(""))}),c=x.join(`\r
`),c=c.replace(/\:\r\n\{/g,":{"),c=c.replace(/\:\r\n\[/g,":["),x=c.split(`\r
`),x.forEach(function(S,g){var h=0,p=0,k="";for(S.match(/\{$/)||S.match(/\[$/)?p+=1:S.match(/\}$/)||S.match(/\]$/)||S.match(/\},$/)||S.match(/\],$/)?n!==0&&(n-=1):p=0,h=0;h<n;h++)k+=i;o+=k+S+`
`,n+=p}),o.trim()},Ec=e=>{e({isFull:window.screen.width<768,width:window.screen.width})},Ic=(e,a)=>{const o=document.createElement("script");o.type="text/javascript",o.src=e,o.onload=o.onreadystatechange=function(){(!this.readyState||this.readyState==="loaded"||this.readyState==="complete")&&(a&&a(),o.onload=o.onreadystatechange=null)},document.body.appendChild(o)},Tc=(e,a)=>{const o=document.createElement("link");o.type="text/css",o.rel="stylesheet",o.media="all",o.href=e,o.onload=o.onreadystatechange=function(){(!this.readyState||this.readyState==="loaded"||this.readyState==="complete")&&(a&&a(),o.onload=o.onreadystatechange=null)},document.body.appendChild(o)},Cc=(e,a)=>(a*(e==="0.00"||e===0?10:e)/10).toFixed(2),Vc=(e,a)=>{for(e=e.split("."),a=a.split(".");e.length<a.length;)e.push("0");for(;a.length<e.length;)a.push("0");e=e.map(Number),a=a.map(Number);for(let o=0;o<e.length;o++){if(e[o]<a[o])return-1;if(e[o]>a[o])return 1}return 0},Lc=Object.freeze(Object.defineProperty({__proto__:null,addTag:hc,auth:kc,closeTag:gc,copy:xc,discount:Cc,error:_c,formatJson:Ac,info:yc,loadCss:Tc,loadScript:Ic,refreshTag:fc,role:wc,setModalSizeEvent:Ec,success:vc,versionCompare:Vc},Symbol.toStringTag,{value:"Module"})),Oc="saiadmin-vue",Rc="saiadmin",jc="5.0",Pc="module",Dc="MIT",zc={dev:"vite serve --mode development",build:"vite build",preview:"vite preview",tailwind:"tailwind-config-viewer -o -c tailwind.config.cjs"},qc={"@arco-design/color":"^0.4.0","@arco-design/web-vue":"^2.57.0","@wangeditor/editor":"^5.1.23","@wangeditor/editor-for-vue":"^5.1.12",autoprefixer:"^10.4.17",axios:"^0.27.2","crypto-js":"^4.2.0",dayjs:"^1.11.11",echarts:"^5.4.2",file2md5:"^1.3.0",lodash:"^4.17.21","md-editor-v3":"^4.13.5","monaco-editor":"^0.33.0",nprogress:"^0.2.0",pinia:"^2.1.7","pinia-plugin-persistedstate":"^4.4.1","postcss-import":"^14.1.0",qs:"^6.10.3","resize-observer-polyfill":"^1.5.1",sortablejs:"^1.15.0",tailwindcss:"^3.4.1",vue:"^3.4.19","vue-clipboard3":"^2.0.0","vue-color-kit":"^1.0.5","vue-echarts":"^6.0.2","vue-i18n":"^9.1.10","vue-router":"^4.2.5","vue-to-print":"^1.4.0",vuedraggable:"^4.1.0"},Mc={"@iconify/vue":"^4.2.0","@vitejs/plugin-vue":"^5.0.4","@vitejs/plugin-vue-jsx":"^3.1.0",less:"^4.1.3","less-loader":"^11.1.4","rollup-plugin-visualizer":"^5.12.0","tailwind-config-viewer":"^1.7.3",typescript:"^4.7.4",vite:"^5.1.4","vite-plugin-compression":"^0.5.1"},Fc={name:Oc,admin_name:Rc,version:jc,type:Pc,license:Dc,scripts:zc,dependencies:qc,devDependencies:Mc};Da.locale(Zo);Da.extend(Xo);const Te=ko(Pi);Te.use(Oo,{}).use(Ro).use(te).use(pn).use(rc).use(pc).use(ji);Te.config.globalProperties.$tool=v;Te.config.globalProperties.$common=Lc;Te.config.globalProperties.$title="SaiAdmin";Te.config.globalProperties.$url="/";Te.mount("#app");v.capsule("SaiAdmin",`v${Fc.version} release`);console.log("SaiAdmin 官网  https://saithink.top");export{Va as N,ye as _,sa as a,ba as b,me as c,gc as d,hc as e,pa as f,xc as g,Y as h,yc as i,Ie as j,kc as k,Ca as l,Ac as m,Fc as p,fc as r,v as t,ua as u};
