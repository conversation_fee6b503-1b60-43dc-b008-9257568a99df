import{a as y}from"./database-C9oinknn.js";import P from"./recycle-08ccYGa5.js";import A from"./struct-BuDD2Spc.js";import{M as C}from"./@arco-design-uttiljWv.js";import{r as p,a as x,o as L,h as i,ba as U,n as W,k as r,t as n,l as o,M as c,j as d,y as u}from"./@vue-9ZIPiVZG.js";import"./index-ybrmzYq5.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const q={class:"ma-content-block lg:flex justify-between"},Yt={__name:"index",setup(G){const _=p(),h=p(),v=p(),m=p([]),f=x({name:""}),I=e=>m.value=e,R=async e=>{var t;(t=h.value)==null||t.open(e)},B=async e=>{var t;(t=v.value)==null||t.open(e)},w=async e=>{var s;if(m.value.length===0){C.error("至少要选择一条数据");return}let t;e==="optimize"&&(t=await y.optimize({tables:m.value})),e==="clear"&&(t=await y.fragment({tables:m.value})),t.code==200&&(C.success(t.message),(s=_.value)==null||s.refresh())},S=x({api:y.getPageList,rowSelection:{showCheckedAll:!0,key:"name"},operationColumnWidth:180}),$=x([{title:"表名称",dataIndex:"name",width:200,align:"left"},{title:"表注释",dataIndex:"comment",width:180},{title:"表引擎",dataIndex:"engine",width:120},{title:"数据更新时间",dataIndex:"update_time",width:180},{title:"总行数",dataIndex:"rows",width:120},{title:"碎片大小",dataIndex:"data_free",width:120},{title:"数据大小",dataIndex:"data_length",width:120},{title:"索引大小",dataIndex:"index_length",width:120},{title:"字符集",dataIndex:"collation",width:180},{title:"创建时间",dataIndex:"create_time",width:180}]),V=async()=>{},b=async()=>{var e;(e=_.value)==null||e.refresh()};return L(async()=>{V(),b()}),(e,t)=>{const s=i("a-input"),z=i("a-form-item"),M=i("a-col"),F=i("icon-tool"),k=i("a-button"),T=i("icon-experiment"),j=i("icon-layers"),g=i("a-link"),D=i("icon-storage"),E=i("sa-table"),l=U("auth");return r(),W("div",q,[n(E,{ref_key:"crudRef",ref:_,options:S,columns:$,searchForm:f,onSelectionChange:I},{tableSearch:o(()=>[n(M,{sm:12,xs:24},{default:o(()=>[n(z,{field:"name",label:"表名称"},{default:o(()=>[n(s,{modelValue:f.name,"onUpdate:modelValue":t[0]||(t[0]=a=>f.name=a),placeholder:"请输入表名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),tableBeforeButtons:o(()=>[c((r(),d(k,{onClick:t[1]||(t[1]=a=>w("optimize")),type:"primary",status:"success"},{icon:o(()=>[n(F)]),default:o(()=>[t[3]||(t[3]=u("优化表 "))]),_:1})),[[l,["/core/database/optimize"]]]),c((r(),d(k,{onClick:t[2]||(t[2]=a=>w("clear")),type:"primary",status:"success"},{icon:o(()=>[n(T)]),default:o(()=>[t[4]||(t[4]=u("清理碎片 "))]),_:1})),[[l,["/core/database/fragment"]]])]),operationBeforeExtend:o(({record:a})=>[c((r(),d(g,{onClick:N=>R(a.name)},{default:o(()=>[n(j),t[5]||(t[5]=u(" 表结构 "))]),_:2},1032,["onClick"])),[[l,["/core/database/detailed"]]]),c((r(),d(g,{onClick:N=>B(a.name)},{default:o(()=>[n(D),t[6]||(t[6]=u(" 回收站 "))]),_:2},1032,["onClick"])),[[l,["/core/database/recycle"]]])]),_:1},8,["options","columns","searchForm"]),n(A,{ref_key:"structTableRef",ref:h,onSuccess:b},null,512),n(P,{ref_key:"tableRecycleRef",ref:v,onSuccess:b},null,512)])}}};export{Yt as default};
