import{h as t}from"./index-ybrmzYq5.js";const s={getPageList(e={}){return t({url:"/core/post/index",method:"get",params:e})},read(e){return t({url:"/core/post/read?id="+e,method:"get"})},save(e={}){return t({url:"/core/post/save",method:"post",data:e})},update(e,r={}){return t({url:"/core/post/update?id="+e,method:"put",data:r})},changeStatus(e={}){return t({url:"/core/post/changeStatus",method:"post",data:e})},destroy(e){return t({url:"/core/post/destroy",method:"delete",data:e})}};export{s as a};
