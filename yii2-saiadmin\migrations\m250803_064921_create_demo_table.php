<?php
/**
 * 演示表迁移
 */
use yii\db\Migration;

class m250803_064921_create_demo_table extends Migration
{
    public function safeUp()
    {
        $this->createTable("{{%demo}}", [
            "id" => $this->primaryKey(),
            "title" => $this->string()->notNull()->comment("标题"),
            "content" => $this->text()->comment("内容"),
            "status" => $this->smallInteger()->defaultValue(1)->comment("状态"),
            "created_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP")->comment("创建时间"),
            "updated_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")->comment("更新时间"),
        ], "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\"演示表\"");
        
        // 插入示例数据
        $this->insert("{{%demo}}", [
            "title" => "欢迎使用 SaiAdmin Yii 2.0",
            "content" => "这是一个演示数据，展示了 Yii 2.0 的数据库操作功能。",
            "status" => 1
        ]);
        
        $this->insert("{{%demo}}", [
            "title" => "强大的 ActiveRecord",
            "content" => "Yii 2.0 提供了强大的 ActiveRecord ORM，让数据库操作变得简单。",
            "status" => 1
        ]);
    }

    public function safeDown()
    {
        $this->dropTable("{{%demo}}");
    }
}