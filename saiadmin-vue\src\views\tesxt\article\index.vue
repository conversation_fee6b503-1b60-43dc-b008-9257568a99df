<template>
  <div class="ma-content-block">
    <sa-table ref="crudRef" :options="options" :columns="columns" :searchForm="searchForm">
      <!-- 搜索区 tableSearch -->
      <template #tableSearch>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="name">
            <a-input v-model="searchForm.name" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="subname">
            <a-input v-model="searchForm.subname" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="showname">
            <a-input v-model="searchForm.showname" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :sm="8" :xs="24">
          <a-form-item label="" field="showsubname">
            <a-input v-model="searchForm.showsubname" placeholder="请输入" allow-clear />
          </a-form-item>
        </a-col>
      </template>

      <!-- Table 自定义渲染 -->
    </sa-table>

    <!-- 编辑表单 -->
    <edit-form ref="editRef" @success="refresh" />

  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import EditForm from './edit.vue'
import api from '../api/article'

// 引用定义
const crudRef = ref()
const editRef = ref()
const viewRef = ref()

// 搜索表单
const searchForm = ref({
  name: '',
  subname: '',
  showname: '',
  showsubname: '',
})

// SaTable 基础配置
const options = reactive({
  api: api.getPageList,
  rowSelection: { showCheckedAll: true },
  add: {
    show: true,
    auth: ['/tesxt/DdwxArticle/save'],
    func: async () => {
      editRef.value?.open()
    },
  },
  edit: {
    show: true,
    auth: ['/tesxt/DdwxArticle/update'],
    func: async (record) => {
      editRef.value?.open('edit')
      editRef.value?.setFormData(record)
    },
  },
  delete: {
    show: true,
    auth: ['/tesxt/DdwxArticle/destroy'],
    func: async (params) => {
      const resp = await api.destroy(params)
      if (resp.code === 200) {
        Message.success(`删除成功！`)
        crudRef.value?.refresh()
      }
    },
  },
})

// SaTable 列配置
const columns = reactive([
  { title: '', dataIndex: 'name', width: 180 },
  { title: '', dataIndex: 'subname', width: 180 },
  { title: '', dataIndex: 'pic', width: 180 },
  { title: '', dataIndex: 'author', width: 180 },
  { title: '', dataIndex: 'readcount', width: 180 },
  { title: '', dataIndex: 'sort', width: 180 },
  { title: '', dataIndex: 'createtime', width: 180 },
  { title: '', dataIndex: 'sendtime', width: 180 },
  { title: '', dataIndex: 'reason', width: 180 },
  { title: '', dataIndex: 'showname', width: 180 },
  { title: '', dataIndex: 'showsubname', width: 180 },
  { title: '', dataIndex: 'showreadcount', width: 180 },
  { title: '', dataIndex: 'showsendtime', width: 180 },
  { title: '', dataIndex: 'showauthor', width: 180 },
  { title: '是否可评论', dataIndex: 'canpl', width: 180 },
  { title: '评论是否可回复', dataIndex: 'canplrp', width: 180 },
  { title: '', dataIndex: 'pinglun_check', width: 180 },
  { title: '', dataIndex: 'zan', width: 180 },
  { title: '', dataIndex: 'pcid', width: 180 },
])

// 页面数据初始化
const initPage = async () => {}

// SaTable 数据请求
const refresh = async () => {
  crudRef.value?.refresh()
}

// 页面加载完成执行
onMounted(async () => {
  initPage()
  refresh()
})
</script>
