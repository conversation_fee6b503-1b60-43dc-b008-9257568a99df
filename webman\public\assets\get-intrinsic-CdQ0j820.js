import{e as G}from"./es-object-atoms-Ditt1eQ6.js";import{t as M,s as N,u as j,r as T,a as k,_ as C,e as D}from"./es-errors-CFxpeikN.js";import{s as W,r as q,p as J,m as V,a as z,f as H,b as L}from"./math-intrinsics-Cv-yPkyD.js";import{g as Y}from"./gopd-fcd2-aIC.js";import{e as K}from"./es-define-property-bDCdrV83.js";import{r as Q}from"./has-symbols-BaUvM3gb.js";import{r as X,a as Z,b as rr}from"./get-proto-CEhLFpt-.js";import{r as er,f as tr}from"./call-bind-apply-helpers-CXPkwEps.js";import{f as or}from"./function-bind-BbkWVFrm.js";import{r as ar}from"./hasown-C2NEVhna.js";var r,nr=G,yr=D,ir=C,pr=k,fr=T,d=N,A=M,sr=j,cr=L,lr=H,ur=z,Ar=V,dr=J,vr=q,Pr=W,x=Function,w=function(i){try{return x('"use strict"; return ('+i+").constructor;")()}catch{}},P=Y,gr=K,$=function(){throw new A},mr=P?function(){try{return arguments.callee,$}catch{try{return P(arguments,"callee").get}catch{return $}}}():$,l=Q()(),o=rr(),hr=Z(),Sr=X(),_=er(),g=tr,u={},Er=typeof Uint8Array>"u"||!o?r:o(Uint8Array),s={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?r:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?r:ArrayBuffer,"%ArrayIteratorPrototype%":l&&o?o([][Symbol.iterator]()):r,"%AsyncFromSyncIteratorPrototype%":r,"%AsyncFunction%":u,"%AsyncGenerator%":u,"%AsyncGeneratorFunction%":u,"%AsyncIteratorPrototype%":u,"%Atomics%":typeof Atomics>"u"?r:Atomics,"%BigInt%":typeof BigInt>"u"?r:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?r:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?r:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?r:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":yr,"%eval%":eval,"%EvalError%":ir,"%Float16Array%":typeof Float16Array>"u"?r:Float16Array,"%Float32Array%":typeof Float32Array>"u"?r:Float32Array,"%Float64Array%":typeof Float64Array>"u"?r:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?r:FinalizationRegistry,"%Function%":x,"%GeneratorFunction%":u,"%Int8Array%":typeof Int8Array>"u"?r:Int8Array,"%Int16Array%":typeof Int16Array>"u"?r:Int16Array,"%Int32Array%":typeof Int32Array>"u"?r:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":l&&o?o(o([][Symbol.iterator]())):r,"%JSON%":typeof JSON=="object"?JSON:r,"%Map%":typeof Map>"u"?r:Map,"%MapIteratorPrototype%":typeof Map>"u"||!l||!o?r:o(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":nr,"%Object.getOwnPropertyDescriptor%":P,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?r:Promise,"%Proxy%":typeof Proxy>"u"?r:Proxy,"%RangeError%":pr,"%ReferenceError%":fr,"%Reflect%":typeof Reflect>"u"?r:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?r:Set,"%SetIteratorPrototype%":typeof Set>"u"||!l||!o?r:o(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?r:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":l&&o?o(""[Symbol.iterator]()):r,"%Symbol%":l?Symbol:r,"%SyntaxError%":d,"%ThrowTypeError%":mr,"%TypedArray%":Er,"%TypeError%":A,"%Uint8Array%":typeof Uint8Array>"u"?r:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?r:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?r:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?r:Uint32Array,"%URIError%":sr,"%WeakMap%":typeof WeakMap>"u"?r:WeakMap,"%WeakRef%":typeof WeakRef>"u"?r:WeakRef,"%WeakSet%":typeof WeakSet>"u"?r:WeakSet,"%Function.prototype.call%":g,"%Function.prototype.apply%":_,"%Object.defineProperty%":gr,"%Object.getPrototypeOf%":hr,"%Math.abs%":cr,"%Math.floor%":lr,"%Math.max%":ur,"%Math.min%":Ar,"%Math.pow%":dr,"%Math.round%":vr,"%Math.sign%":Pr,"%Reflect.getPrototypeOf%":Sr};if(o)try{null.error}catch(i){var Ir=o(o(i));s["%Error.prototype%"]=Ir}var br=function i(e){var a;if(e==="%AsyncFunction%")a=w("async function () {}");else if(e==="%GeneratorFunction%")a=w("function* () {}");else if(e==="%AsyncGeneratorFunction%")a=w("async function* () {}");else if(e==="%AsyncGenerator%"){var t=i("%AsyncGeneratorFunction%");t&&(a=t.prototype)}else if(e==="%AsyncIteratorPrototype%"){var n=i("%AsyncGenerator%");n&&o&&(a=o(n.prototype))}return s[e]=a,a},O={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},m=or,F=ar(),Fr=m.call(g,Array.prototype.concat),Rr=m.call(_,Array.prototype.splice),B=m.call(g,String.prototype.replace),R=m.call(g,String.prototype.slice),Ur=m.call(g,RegExp.prototype.exec),wr=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,$r=/\\(\\)?/g,Or=function(e){var a=R(e,0,1),t=R(e,-1);if(a==="%"&&t!=="%")throw new d("invalid intrinsic syntax, expected closing `%`");if(t==="%"&&a!=="%")throw new d("invalid intrinsic syntax, expected opening `%`");var n=[];return B(e,wr,function(p,c,y,h){n[n.length]=y?B(h,$r,"$1"):c||p}),n},Br=function(e,a){var t=e,n;if(F(O,t)&&(n=O[t],t="%"+n[0]+"%"),F(s,t)){var p=s[t];if(p===u&&(p=br(t)),typeof p>"u"&&!a)throw new A("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:t,value:p}}throw new d("intrinsic "+e+" does not exist!")},Wr=function(e,a){if(typeof e!="string"||e.length===0)throw new A("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof a!="boolean")throw new A('"allowMissing" argument must be a boolean');if(Ur(/^%?[^%]*%?$/,e)===null)throw new d("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var t=Or(e),n=t.length>0?t[0]:"",p=Br("%"+n+"%",a),c=p.name,y=p.value,h=!1,U=p.alias;U&&(n=U[0],Rr(t,Fr([0,1],U)));for(var S=1,v=!0;S<t.length;S+=1){var f=t[S],E=R(f,0,1),I=R(f,-1);if((E==='"'||E==="'"||E==="`"||I==='"'||I==="'"||I==="`")&&E!==I)throw new d("property names with quotes must have matching quotes");if((f==="constructor"||!v)&&(h=!0),n+="."+f,c="%"+n+"%",F(s,c))y=s[c];else if(y!=null){if(!(f in y)){if(!a)throw new A("base intrinsic for "+e+" exists, but the property is not available.");return}if(P&&S+1>=t.length){var b=P(y,f);v=!!b,v&&"get"in b&&!("originalValue"in b.get)?y=b.get:y=y[f]}else v=F(y,f),y=y[f];v&&!h&&(s[c]=y)}}return y};export{Wr as g};
