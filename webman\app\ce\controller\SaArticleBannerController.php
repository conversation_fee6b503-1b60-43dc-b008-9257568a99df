<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\ce\controller;

use plugin\saiadmin\basic\BaseController;
use app\ce\logic\SaArticleBannerLogic;
use app\ce\validate\SaArticleBannerValidate;
use support\Request;
use support\Response;

/**
 * 文章轮播图控制器
 */
class SaArticleBannerController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new SaArticleBannerLogic();
        $this->validate = new SaArticleBannerValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['banner_type', ''],
            ['title', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

}
