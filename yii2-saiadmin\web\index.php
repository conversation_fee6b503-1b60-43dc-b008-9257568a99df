<?php
/**
 * <PERSON><PERSON>dmin Yii 2.0 兼容版本 - Web入口文件
 */

// 定义应用环境
defined("YII_DEBUG") or define("YII_DEBUG", true);
defined("YII_ENV") or define("YII_ENV", "dev");

// 加载 Composer 自动加载器
require __DIR__ . "/../vendor/autoload.php";

// 加载 Yii 类文件
require __DIR__ . "/../vendor/yiisoft/yii2/Yii.php";

// 加载应用配置
$config = require __DIR__ . "/../config/web.php";

// 创建并运行应用
(new yii\web\Application($config))->run();