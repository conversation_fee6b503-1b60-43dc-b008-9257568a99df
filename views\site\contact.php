<?php
/**
 * 联系页面视图
 */
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\captcha\Captcha;

$this->title = '联系我们';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="site-contact">
    <h1><?= Html::encode($this->title) ?></h1>

    <?php if (Yii::$app->session->hasFlash('contactFormSubmitted')): ?>
        <div class="alert alert-success">
            <?= Yii::$app->session->getFlash('contactFormSubmitted') ?>
        </div>
    <?php else: ?>

        <p>
            如果您有任何问题或建议，请通过以下表单联系我们。我们会尽快回复您。
        </p>

        <div class="row">
            <div class="col-lg-5">
                <?php $form = ActiveForm::begin(['id' => 'contact-form']); ?>

                    <?= $form->field($model, 'name')->textInput(['autofocus' => true, 'placeholder' => '请输入您的姓名']) ?>

                    <?= $form->field($model, 'email')->textInput(['placeholder' => '请输入您的邮箱']) ?>

                    <?= $form->field($model, 'subject')->textInput(['placeholder' => '请输入主题']) ?>

                    <?= $form->field($model, 'body')->textarea(['rows' => 6, 'placeholder' => '请输入您的留言内容']) ?>

                    <?= $form->field($model, 'verifyCode')->widget(Captcha::class, [
                        'template' => '<div class="row"><div class="col-lg-3">{image}</div><div class="col-lg-6">{input}</div></div>',
                    ]) ?>

                    <div class="form-group">
                        <?= Html::submitButton('提交', ['class' => 'btn btn-primary', 'name' => 'contact-button']) ?>
                        <?= Html::a('返回首页', ['/'], ['class' => 'btn btn-secondary']) ?>
                    </div>

                <?php ActiveForm::end(); ?>
            </div>
            
            <div class="col-lg-7">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">📞 联系方式</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📧 邮箱</h6>
                                <p><EMAIL></p>
                                
                                <h6>🌐 网站</h6>
                                <p><a href="https://saiadmin.com" target="_blank">https://saiadmin.com</a></p>
                            </div>
                            <div class="col-md-6">
                                <h6>💬 QQ群</h6>
                                <p>123456789</p>
                                
                                <h6>📱 微信群</h6>
                                <p>扫码加入微信群</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title">❓ 常见问题</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="faqAccordion">
                            <div class="card">
                                <div class="card-header" id="faq1">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse1">
                                            如何开始使用 SaiAdmin？
                                        </button>
                                    </h6>
                                </div>
                                <div id="collapse1" class="collapse" data-parent="#faqAccordion">
                                    <div class="card-body">
                                        请参考我们的快速开始指南，或访问演示页面了解功能。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header" id="faq2">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse2">
                                            支持哪些数据库？
                                        </button>
                                    </h6>
                                </div>
                                <div id="collapse2" class="collapse" data-parent="#faqAccordion">
                                    <div class="card-body">
                                        支持 MySQL、PostgreSQL、SQLite 等主流数据库。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php endif; ?>
</div>
