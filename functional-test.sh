#!/bin/bash
# Sai<PERSON>d<PERSON> Yii 2.0 功能测试脚本

echo "🧪 SaiAdmin Yii 2.0 功能测试"
echo "================================"

# 测试控制台命令
echo "📋 测试控制台命令..."
php yii help > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 控制台命令正常"
else
    echo "❌ 控制台命令异常"
    exit 1
fi

# 测试模型加载
echo "🏗️ 测试模型加载..."
php -r "
require_once \"vendor/autoload.php\";
require_once \"vendor/yiisoft/yii2/Yii.php\";
\$config = require \"config/web.php\";
new yii\web\Application(\$config);
try {
    \$user = new app\models\User();
    echo \"✅ 用户模型加载成功\n\";
} catch (Exception \$e) {
    echo \"❌ 用户模型加载失败: \" . \$e->getMessage() . \"\n\";
    exit(1);
}
"

# 测试服务类
echo "🔧 测试服务类..."
php -r "
require_once \"vendor/autoload.php\";
require_once \"vendor/yiisoft/yii2/Yii.php\";
\$config = require \"config/web.php\";
new yii\web\Application(\$config);
try {
    \$service = new app\components\services\UserService();
    echo \"✅ 用户服务加载成功\n\";
} catch (Exception \$e) {
    echo \"❌ 用户服务加载失败: \" . \$e->getMessage() . \"\n\";
    exit(1);
}
"

# 测试Web访问
echo "🌐 测试Web访问..."
if command -v curl &> /dev/null; then
    # 测试演示页面
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/demo)
    if [ "$response" = "200" ]; then
        echo "✅ 演示页面访问正常"
    else
        echo "⚠️ 演示页面访问异常 (HTTP $response)"
    fi
    
    # 测试API接口
    api_response=$(curl -s http://localhost:8080/demo/api)
    if [[ "$api_response" == *"code"* ]]; then
        echo "✅ API接口正常"
    else
        echo "⚠️ API接口异常"
    fi
    
    # 测试用户控制器
    user_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/user)
    if [ "$user_response" = "200" ] || [ "$user_response" = "302" ]; then
        echo "✅ 用户控制器访问正常"
    else
        echo "⚠️ 用户控制器访问异常 (HTTP $user_response)"
    fi
else
    echo "⚠️ curl命令不可用，跳过Web测试"
fi

echo ""
echo "🎯 功能测试完成！"
echo ""
echo "📖 使用指南:"
echo "- 演示页面: http://localhost:8080/demo"
echo "- 用户管理: http://localhost:8080/user"
echo "- API文档: http://localhost:8080/demo/api"
echo "- Gii生成器: http://localhost:8080/gii"
echo ""
echo "🔧 开发命令:"
echo "- 数据库迁移: php yii migrate"
echo "- 清除缓存: php yii cache/flush-all"
echo "- 生成模型: php yii gii/model"
echo "- 生成CRUD: php yii gii/crud"