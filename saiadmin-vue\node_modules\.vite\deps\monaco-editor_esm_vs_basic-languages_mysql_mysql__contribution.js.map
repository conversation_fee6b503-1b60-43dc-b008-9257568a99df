{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/mysql/mysql.contribution.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/mysql/mysql.contribution.ts\nimport { registerLanguage } from \"../_.contribution.js\";\nregisterLanguage({\n  id: \"mysql\",\n  extensions: [],\n  aliases: [\"MySQL\", \"mysql\"],\n  loader: () => {\n    if (false) {\n      return new Promise((resolve, reject) => {\n        __require([\"vs/basic-languages/mysql/mysql\"], resolve, reject);\n      });\n    } else {\n      return import(\"./mysql.js\");\n    }\n  }\n});\n"], "mappings": ";;;;;;;;AASA,iBAAiB;AAAA,EACf,IAAI;AAAA,EACJ,YAAY,CAAC;AAAA,EACb,SAAS,CAAC,SAAS,OAAO;AAAA,EAC1B,QAAQ,MAAM;AACZ,QAAI,OAAO;AACT,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,kBAAU,CAAC,gCAAgC,GAAG,SAAS,MAAM;AAAA,MAC/D,CAAC;AAAA,IACH,OAAO;AACL,aAAO,OAAO,qBAAY;AAAA,IAC5B;AAAA,EACF;AACF,CAAC;", "names": []}