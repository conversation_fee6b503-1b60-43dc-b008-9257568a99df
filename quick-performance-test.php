<?php
/**
 * SaiAdmin 快速性能测试
 * 简化版性能测试，兼容Windows环境
 */

echo "🚀 SaiAdmin 快速性能测试\n";
echo "========================================\n\n";

// 1. 测试数据库性能
echo "[1/4] 测试数据库性能...\n";
try {
    $startTime = microtime(true);
    $pdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
        'root',
        '5GeNi1v7P7Xcur5W',
        [PDO::ATTR_TIMEOUT => 5]
    );
    $connectionTime = (microtime(true) - $startTime) * 1000;
    echo "  ✅ 数据库连接时间: " . round($connectionTime, 2) . "ms\n";
    
    // 测试简单查询
    $startTime = microtime(true);
    $stmt = $pdo->query('SELECT COUNT(*) FROM sa_system_user');
    $userCount = $stmt->fetchColumn();
    $queryTime = (microtime(true) - $startTime) * 1000;
    echo "  ✅ 简单查询时间: " . round($queryTime, 2) . "ms (用户数: {$userCount})\n";
    
    // 测试索引查询
    $startTime = microtime(true);
    $stmt = $pdo->prepare('SELECT * FROM sa_system_user WHERE id = ?');
    $stmt->execute([1]);
    $indexQueryTime = (microtime(true) - $startTime) * 1000;
    echo "  ✅ 索引查询时间: " . round($indexQueryTime, 2) . "ms\n";
    
} catch (Exception $e) {
    echo "  ❌ 数据库测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. 测试API性能
echo "[2/4] 测试API性能...\n";
$endpoints = [
    'backend' => 'http://localhost:8787/',
    'captcha' => 'http://localhost:8787/core/captcha',
];

foreach ($endpoints as $name => $url) {
    $times = [];
    for ($i = 0; $i < 3; $i++) {
        $startTime = microtime(true);
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $responseTime = (microtime(true) - $startTime) * 1000;
        $times[] = $responseTime;
    }
    
    $avgTime = round(array_sum($times) / count($times), 2);
    $status = $httpCode == 200 ? '✅' : '⚠️';
    echo "  {$status} {$name}: 平均{$avgTime}ms [HTTP {$httpCode}]\n";
}

echo "\n";

// 3. 测试内存使用
echo "[3/4] 测试内存使用...\n";
$memoryUsage = memory_get_usage(true);
$peakMemory = memory_get_peak_usage(true);

echo "  📊 当前内存使用: " . formatBytes($memoryUsage) . "\n";
echo "  📈 峰值内存使用: " . formatBytes($peakMemory) . "\n";

// 测试大数据处理
$beforeMemory = memory_get_usage(true);
$largeArray = [];
for ($i = 0; $i < 5000; $i++) {
    $largeArray[] = [
        'id' => $i,
        'name' => 'User ' . $i,
        'data' => str_repeat('x', 50)
    ];
}
$afterMemory = memory_get_usage(true);
$memoryIncrease = $afterMemory - $beforeMemory;
echo "  🔍 大数据处理内存增长: " . formatBytes($memoryIncrease) . "\n";
unset($largeArray);

echo "\n";

// 4. 生成性能报告
echo "[4/4] 生成性能报告...\n";

$report = [
    'timestamp' => date('Y-m-d H:i:s'),
    'database' => [
        'connection_time' => $connectionTime ?? 0,
        'query_time' => $queryTime ?? 0,
        'index_query_time' => $indexQueryTime ?? 0,
    ],
    'api' => $endpoints,
    'memory' => [
        'current' => $memoryUsage,
        'peak' => $peakMemory,
        'large_data_increase' => $memoryIncrease,
    ],
    'php_version' => PHP_VERSION,
];

file_put_contents('quick-performance-report.json', json_encode($report, JSON_PRETTY_PRINT));
echo "  ✅ 报告已保存: quick-performance-report.json\n";

echo "\n";
echo "========================================\n";
echo "🎉 性能测试完成！\n";
echo "========================================\n\n";

// 性能评估
echo "📊 性能评估:\n";

// 数据库性能评估
if (isset($connectionTime)) {
    $dbStatus = $connectionTime < 100 ? '✅ 优秀' : ($connectionTime < 500 ? '⚠️ 一般' : '❌ 需优化');
    echo "  数据库连接: {$dbStatus} ({$connectionTime}ms)\n";
}

if (isset($queryTime)) {
    $queryStatus = $queryTime < 10 ? '✅ 优秀' : ($queryTime < 50 ? '⚠️ 一般' : '❌ 需优化');
    echo "  数据库查询: {$queryStatus} ({$queryTime}ms)\n";
}

// 内存使用评估
$memoryStatus = $peakMemory < 50*1024*1024 ? '✅ 优秀' : '⚠️ 需关注';
echo "  内存使用: {$memoryStatus} (峰值: " . formatBytes($peakMemory) . ")\n";

echo "\n";
echo "💡 优化建议:\n";

if (isset($connectionTime) && $connectionTime > 100) {
    echo "  🔧 数据库连接较慢，建议检查网络或使用连接池\n";
}

if (isset($queryTime) && $queryTime > 50) {
    echo "  🔧 数据库查询较慢，建议添加索引或优化SQL\n";
}

if ($peakMemory > 100*1024*1024) {
    echo "  🔧 内存使用较高，建议优化数据结构\n";
}

echo "  🔧 建议启用Redis缓存提升性能\n";
echo "  🔧 建议使用CDN加速静态资源\n";

echo "\n";
echo "🌐 访问地址:\n";
echo "  前端: http://localhost:8889/\n";
echo "  后端: http://localhost:8787/\n";

function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
