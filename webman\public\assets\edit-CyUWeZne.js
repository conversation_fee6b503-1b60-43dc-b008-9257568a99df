import{h as r,t as M}from"./index-ybrmzYq5.js";import{M as P}from"./@arco-design-uttiljWv.js";import{r as p,c as E,a as F,h as i,j as R,k as T,l as d,t as s,a1 as y,O as z}from"./@vue-9ZIPiVZG.js";const k={getPageList(a={}){return r({url:"/core/notice/index",method:"get",params:a})},save(a={}){return r({url:"/core/notice/save",method:"post",data:a})},read(a){return r({url:"/core/notice/read?id="+a,method:"get"})},destroy(a){return r({url:"/core/notice/destroy",method:"delete",data:a})},update(a,f={}){return r({url:"/core/notice/update?id="+a,method:"put",data:f})},changeStatus(a={}){return r({url:"/core/notice/changeStatus",method:"post",data:a})}},L={__name:"edit",emits:["success"],setup(a,{expose:f,emit:V}){const w=V,_=p(),v=p(""),c=p(!1),g=p(!1);let h=E(()=>"公告管理"+(v.value=="add"?"-新增":"-编辑"));const b={id:"",title:"",type:"1",content:"",remark:"",user:""},t=F({...b}),x={title:[{required:!0,message:"公告标题不能为空"}],type:[{required:!0,message:"公告类型不能为空"}],content:[{required:!0,message:"公告内容不能为空"}]},D=async(o="add")=>{v.value=o,Object.assign(t,b),_.value.clearValidate(),c.value=!0,await O()},O=async()=>{},U=async o=>{for(const e in t)o[e]!=null&&o[e]!=null&&(t[e]=o[e])},j=async o=>{var m;if(!await((m=_.value)==null?void 0:m.validate())){g.value=!0;let l={...t},u={};v.value==="add"?(l.id=void 0,u=await k.save(l)):u=await k.update(l.id,l),u.code===200&&(P.success("操作成功"),w("success"),o(!0)),setTimeout(()=>{g.value=!1},500)}o(!1)},q=()=>c.value=!1;return f({open:D,setFormData:U}),(o,e)=>{const m=i("a-input"),l=i("a-form-item"),u=i("sa-select"),C=i("ma-wangEditor"),S=i("a-textarea"),B=i("a-form");return T(),R(z("a-modal"),{visible:c.value,"onUpdate:visible":e[4]||(e[4]=n=>c.value=n),width:y(M).getDevice()==="mobile"?"100%":"800px",title:y(h),"mask-closable":!1,"ok-loading":g.value,onCancel:q,onBeforeOk:j},{default:d(()=>[s(B,{ref_key:"formRef",ref:_,model:t,rules:x,"auto-label-width":!0},{default:d(()=>[s(l,{label:"公告标题",field:"title"},{default:d(()=>[s(m,{modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=n=>t.title=n),placeholder:"请输入岗位名称"},null,8,["modelValue"])]),_:1}),s(l,{label:"公告类型",field:"type"},{default:d(()=>[s(u,{modelValue:t.type,"onUpdate:modelValue":e[1]||(e[1]=n=>t.type=n),dict:"backend_notice_type",placeholder:"请选择公告类型"},null,8,["modelValue"])]),_:1}),s(l,{label:"公告内容",field:"content"},{default:d(()=>[s(C,{modelValue:t.content,"onUpdate:modelValue":e[2]||(e[2]=n=>t.content=n),placeholder:"请输入公告内容"},null,8,["modelValue"])]),_:1}),s(l,{label:"备注",field:"remark"},{default:d(()=>[s(S,{modelValue:t.remark,"onUpdate:modelValue":e[3]||(e[3]=n=>t.remark=n),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},40,["visible","width","title","ok-loading"])}}},H=Object.freeze(Object.defineProperty({__proto__:null,default:L},Symbol.toStringTag,{value:"Module"}));export{L as _,k as a,H as e};
