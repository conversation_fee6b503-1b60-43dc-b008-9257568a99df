# Nginx 服务器配置
server {
    listen 80;
    server_name saiadmin.local www.saiadmin.local;
    root /var/www/html/saiadmin/web;
    index index.php index.html;

    # 字符集
    charset utf-8;

    # 访问日志
    access_log /var/log/nginx/saiadmin_access.log;
    error_log /var/log/nginx/saiadmin_error.log;

    # 主要位置配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;

        # 安全配置
        fastcgi_param HTTP_PROXY "";
        fastcgi_read_timeout 300;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }

    location ~ ^/(protected|framework|themes/\w+/views) {
        deny all;
    }

    # 禁止访问敏感文件
    location ~* \.(htaccess|htpasswd|svn|git) {
        deny all;
    }
}