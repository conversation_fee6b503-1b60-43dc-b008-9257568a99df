<?php
/**
 * Sai<PERSON>dmin Yii 2.0 实际编码示例
 */

echo "💻 SaiAdmin Yii 2.0 实际编码示例\n";
echo "========================================\n\n";

echo "[1/5] 创建用户管理模块...\n";

// 创建用户控制器
$userControllerContent = '<?php
/**
 * 用户管理控制器
 */
namespace app\controllers;

use Yii;
use app\models\User;
use app\components\base\BaseController;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;

class UserController extends BaseController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            "verbs" => [
                "class" => VerbFilter::class,
                "actions" => [
                    "delete" => ["POST"],
                ],
            ],
            "access" => [
                "class" => AccessControl::class,
                "rules" => [
                    [
                        "allow" => true,
                        "roles" => ["@"],
                    ],
                ],
            ],
        ]);
    }

    /**
     * 用户列表
     */
    public function actionIndex()
    {
        $dataProvider = new ActiveDataProvider([
            "query" => User::find(),
            "pagination" => [
                "pageSize" => 20,
            ],
            "sort" => [
                "defaultOrder" => [
                    "id" => SORT_DESC,
                ]
            ],
        ]);

        if (Yii::$app->request->isAjax) {
            return $this->success([
                "list" => $dataProvider->getModels(),
                "pagination" => [
                    "total" => $dataProvider->getTotalCount(),
                    "page" => $dataProvider->getPagination()->getPage() + 1,
                    "pageSize" => $dataProvider->getPagination()->getPageSize(),
                ]
            ]);
        }

        return $this->render("index", [
            "dataProvider" => $dataProvider,
        ]);
    }

    /**
     * 创建用户
     */
    public function actionCreate()
    {
        $model = new User();
        $model->scenario = User::SCENARIO_CREATE;

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash("success", "用户创建成功");
                
                if (Yii::$app->request->isAjax) {
                    return $this->success($model->toArray(), "用户创建成功");
                }
                
                return $this->redirect(["view", "id" => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail("用户创建失败", 400, $model->errors);
        }

        return $this->render("create", [
            "model" => $model,
        ]);
    }

    /**
     * 更新用户
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->scenario = User::SCENARIO_UPDATE;

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash("success", "用户更新成功");
                
                if (Yii::$app->request->isAjax) {
                    return $this->success($model->toArray(), "用户更新成功");
                }
                
                return $this->redirect(["view", "id" => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail("用户更新失败", 400, $model->errors);
        }

        return $this->render("update", [
            "model" => $model,
        ]);
    }

    /**
     * 查看用户详情
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        
        if (Yii::$app->request->isAjax) {
            return $this->success($model->toArray());
        }
        
        return $this->render("view", [
            "model" => $model,
        ]);
    }

    /**
     * 删除用户
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            Yii::$app->session->setFlash("success", "用户删除成功");
            
            if (Yii::$app->request->isAjax) {
                return $this->success([], "用户删除成功");
            }
        } else {
            if (Yii::$app->request->isAjax) {
                return $this->fail("用户删除失败");
            }
        }

        return $this->redirect(["index"]);
    }

    /**
     * 批量删除
     */
    public function actionBatchDelete()
    {
        $ids = Yii::$app->request->post("ids", []);
        
        if (empty($ids)) {
            return $this->fail("请选择要删除的用户");
        }
        
        $count = User::deleteAll(["id" => $ids]);
        
        return $this->success([], "成功删除 {$count} 个用户");
    }

    /**
     * 查找用户模型
     */
    protected function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException("请求的用户不存在");
    }
}';

file_put_contents('controllers/UserController.php', $userControllerContent);
echo "  ✅ 创建用户控制器: controllers/UserController.php\n";

echo "\n[2/5] 创建高级模型示例...\n";

// 创建增强的用户模型
$enhancedUserModelContent = '<?php
/**
 * 增强用户模型
 */
namespace app\models;

use Yii;
use app\components\base\BaseModel;
use yii\web\IdentityInterface;
use yii\behaviors\TimestampBehavior;
use yii\behaviors\BlameableBehavior;

/**
 * 用户模型
 */
class User extends BaseModel implements IdentityInterface
{
    // 场景常量
    const SCENARIO_CREATE = "create";
    const SCENARIO_UPDATE = "update";
    const SCENARIO_LOGIN = "login";
    
    // 状态常量
    const STATUS_DELETED = 0;
    const STATUS_INACTIVE = 9;
    const STATUS_ACTIVE = 10;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return "{{%user}}";
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            [
                "class" => TimestampBehavior::class,
                "createdAtAttribute" => "created_at",
                "updatedAtAttribute" => "updated_at",
                "value" => function() {
                    return date("Y-m-d H:i:s");
                }
            ],
            [
                "class" => BlameableBehavior::class,
                "createdByAttribute" => "created_by",
                "updatedByAttribute" => "updated_by",
            ],
        ]);
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [["username", "email"], "required"],
            [["username", "email"], "string", "max" => 255],
            [["username", "email"], "unique"],
            ["email", "email"],
            
            ["password", "required", "on" => [self::SCENARIO_CREATE, self::SCENARIO_LOGIN]],
            ["password", "string", "min" => 6],
            
            ["status", "default", "value" => self::STATUS_ACTIVE],
            ["status", "in", "range" => [self::STATUS_ACTIVE, self::STATUS_INACTIVE, self::STATUS_DELETED]],
            
            [["nickname", "avatar"], "string", "max" => 255],
            [["last_login_at"], "safe"],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            "id" => "ID",
            "username" => "用户名",
            "email" => "邮箱",
            "nickname" => "昵称",
            "avatar" => "头像",
            "status" => "状态",
            "last_login_at" => "最后登录时间",
            "created_at" => "创建时间",
            "updated_at" => "更新时间",
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = ["username", "email", "password", "nickname", "status"];
        $scenarios[self::SCENARIO_UPDATE] = ["username", "email", "nickname", "status"];
        $scenarios[self::SCENARIO_LOGIN] = ["username", "password"];
        return $scenarios;
    }

    /**
     * 密码设置器
     */
    public function setPassword($password)
    {
        $this->password_hash = Yii::$app->security->generatePasswordHash($password);
    }

    /**
     * 验证密码
     */
    public function validatePassword($password)
    {
        return Yii::$app->security->validatePassword($password, $this->password_hash);
    }

    /**
     * 生成认证密钥
     */
    public function generateAuthKey()
    {
        $this->auth_key = Yii::$app->security->generateRandomString();
    }

    /**
     * 获取状态文本
     */
    public function getStatusText()
    {
        $statusMap = [
            self::STATUS_DELETED => "已删除",
            self::STATUS_INACTIVE => "未激活",
            self::STATUS_ACTIVE => "正常",
        ];
        
        return isset($statusMap[$this->status]) ? $statusMap[$this->status] : "未知";
    }

    /**
     * 获取头像URL
     */
    public function getAvatarUrl()
    {
        if ($this->avatar) {
            return Yii::getAlias("@web") . "/" . $this->avatar;
        }
        
        // 默认头像
        return "https://www.gravatar.com/avatar/" . md5($this->email) . "?d=identicon&s=64";
    }

    /**
     * 更新最后登录时间
     */
    public function updateLastLogin()
    {
        $this->last_login_at = date("Y-m-d H:i:s");
        $this->save(false);
    }

    // IdentityInterface 实现
    public static function findIdentity($id)
    {
        return static::findOne(["id" => $id, "status" => self::STATUS_ACTIVE]);
    }

    public static function findIdentityByAccessToken($token, $type = null)
    {
        return static::findOne(["access_token" => $token, "status" => self::STATUS_ACTIVE]);
    }

    public function getId()
    {
        return $this->getPrimaryKey();
    }

    public function getAuthKey()
    {
        return $this->auth_key;
    }

    public function validateAuthKey($authKey)
    {
        return $this->getAuthKey() === $authKey;
    }

    /**
     * 根据用户名查找用户
     */
    public static function findByUsername($username)
    {
        return static::findOne(["username" => $username, "status" => self::STATUS_ACTIVE]);
    }

    /**
     * 搜索范围 - 按状态
     */
    public function scopeActive($query)
    {
        return $query->andWhere(["status" => self::STATUS_ACTIVE]);
    }

    /**
     * 搜索范围 - 最近登录
     */
    public function scopeRecentLogin($query, $days = 30)
    {
        return $query->andWhere([">=", "last_login_at", date("Y-m-d", strtotime("-{$days} days"))]);
    }
}';

file_put_contents('models/User.php', $enhancedUserModelContent);
echo "  ✅ 创建增强用户模型: models/User.php\n";

echo "\n[3/5] 创建API控制器...\n";

// 创建API控制器
$apiControllerContent = '<?php
/**
 * API控制器基类
 */
namespace app\controllers\api;

use Yii;
use yii\rest\ActiveController;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\ContentNegotiator;
use yii\web\Response;

/**
 * API基础控制器
 */
class BaseApiController extends ActiveController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        
        // 内容协商
        $behaviors["contentNegotiator"] = [
            "class" => ContentNegotiator::class,
            "formats" => [
                "application/json" => Response::FORMAT_JSON,
                "application/xml" => Response::FORMAT_XML,
            ],
        ];
        
        // 认证
        $behaviors["authenticator"] = [
            "class" => HttpBearerAuth::class,
            "except" => ["options"],
        ];
        
        return $behaviors;
    }

    /**
     * 成功响应
     */
    protected function success($data = [], $message = "操作成功")
    {
        return [
            "code" => 200,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }

    /**
     * 失败响应
     */
    protected function fail($message = "操作失败", $code = 400, $data = [])
    {
        Yii::$app->response->statusCode = $code;
        return [
            "code" => $code,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }
}

/**
 * 用户API控制器
 */
class UserController extends BaseApiController
{
    public $modelClass = "app\models\User";

    /**
     * @inheritdoc
     */
    public function actions()
    {
        $actions = parent::actions();
        
        // 自定义actions
        unset($actions["delete"], $actions["create"], $actions["update"]);
        
        return $actions;
    }

    /**
     * 创建用户
     */
    public function actionCreate()
    {
        $model = new $this->modelClass();
        $model->scenario = "create";
        
        if ($model->load(Yii::$app->request->post(), "") && $model->save()) {
            return $this->success($model->toArray(), "用户创建成功");
        }
        
        return $this->fail("用户创建失败", 422, $model->errors);
    }

    /**
     * 更新用户
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->scenario = "update";
        
        if ($model->load(Yii::$app->request->post(), "") && $model->save()) {
            return $this->success($model->toArray(), "用户更新成功");
        }
        
        return $this->fail("用户更新失败", 422, $model->errors);
    }

    /**
     * 删除用户
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            return $this->success([], "用户删除成功");
        }
        
        return $this->fail("用户删除失败");
    }

    /**
     * 用户登录
     */
    public function actionLogin()
    {
        $username = Yii::$app->request->post("username");
        $password = Yii::$app->request->post("password");
        
        if (empty($username) || empty($password)) {
            return $this->fail("用户名和密码不能为空", 422);
        }
        
        $user = User::findByUsername($username);
        if (!$user || !$user->validatePassword($password)) {
            return $this->fail("用户名或密码错误", 401);
        }
        
        // 生成访问令牌
        $token = Yii::$app->security->generateRandomString(32);
        $user->access_token = $token;
        $user->updateLastLogin();
        
        return $this->success([
            "user" => $user->toArray(["id", "username", "email", "nickname"]),
            "token" => $token,
            "expires_in" => 3600 * 24 * 7 // 7天
        ], "登录成功");
    }

    /**
     * 用户信息
     */
    public function actionProfile()
    {
        $user = Yii::$app->user->identity;
        
        return $this->success([
            "user" => $user->toArray(),
            "permissions" => [], // 用户权限
            "roles" => [] // 用户角色
        ]);
    }
}';

// 创建API目录
if (!is_dir('controllers/api')) {
    mkdir('controllers/api', 0755, true);
}

file_put_contents('controllers/api/UserController.php', $apiControllerContent);
echo "  ✅ 创建API控制器: controllers/api/UserController.php\n";

echo "\n[4/5] 创建服务层...\n";

// 创建用户服务
$userServiceContent = '<?php
/**
 * 用户服务类
 */
namespace app\components\services;

use Yii;
use app\models\User;
use yii\base\Component;
use yii\db\Transaction;

/**
 * 用户服务
 */
class UserService extends Component
{
    /**
     * 创建用户
     */
    public function createUser($userData)
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            $user = new User();
            $user->scenario = User::SCENARIO_CREATE;
            $user->load($userData, "");
            
            if (!$user->save()) {
                throw new \Exception("用户创建失败: " . implode(", ", $user->getFirstErrors()));
            }
            
            // 生成认证密钥
            $user->generateAuthKey();
            $user->save(false);
            
            // 记录日志
            Yii::info("用户创建成功: " . $user->username, "user");
            
            $transaction->commit();
            return $user;
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 更新用户
     */
    public function updateUser($id, $userData)
    {
        $user = User::findOne($id);
        if (!$user) {
            throw new \Exception("用户不存在");
        }
        
        $user->scenario = User::SCENARIO_UPDATE;
        $user->load($userData, "");
        
        if (!$user->save()) {
            throw new \Exception("用户更新失败: " . implode(", ", $user->getFirstErrors()));
        }
        
        Yii::info("用户更新成功: " . $user->username, "user");
        return $user;
    }

    /**
     * 删除用户
     */
    public function deleteUser($id)
    {
        $user = User::findOne($id);
        if (!$user) {
            throw new \Exception("用户不存在");
        }
        
        // 软删除
        $user->status = User::STATUS_DELETED;
        if (!$user->save(false)) {
            throw new \Exception("用户删除失败");
        }
        
        Yii::info("用户删除成功: " . $user->username, "user");
        return true;
    }

    /**
     * 批量操作
     */
    public function batchOperation($ids, $operation, $data = [])
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            $count = 0;
            
            foreach ($ids as $id) {
                switch ($operation) {
                    case "delete":
                        $this->deleteUser($id);
                        $count++;
                        break;
                        
                    case "activate":
                        $user = User::findOne($id);
                        if ($user) {
                            $user->status = User::STATUS_ACTIVE;
                            $user->save(false);
                            $count++;
                        }
                        break;
                        
                    case "deactivate":
                        $user = User::findOne($id);
                        if ($user) {
                            $user->status = User::STATUS_INACTIVE;
                            $user->save(false);
                            $count++;
                        }
                        break;
                }
            }
            
            $transaction->commit();
            return $count;
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 用户统计
     */
    public function getUserStats()
    {
        return [
            "total" => User::find()->count(),
            "active" => User::find()->where(["status" => User::STATUS_ACTIVE])->count(),
            "inactive" => User::find()->where(["status" => User::STATUS_INACTIVE])->count(),
            "recent_login" => User::find()->where([">=", "last_login_at", date("Y-m-d", strtotime("-7 days"))])->count(),
        ];
    }

    /**
     * 搜索用户
     */
    public function searchUsers($params)
    {
        $query = User::find();
        
        if (!empty($params["username"])) {
            $query->andWhere(["like", "username", $params["username"]]);
        }
        
        if (!empty($params["email"])) {
            $query->andWhere(["like", "email", $params["email"]]);
        }
        
        if (isset($params["status"])) {
            $query->andWhere(["status" => $params["status"]]);
        }
        
        if (!empty($params["date_from"])) {
            $query->andWhere([">=", "created_at", $params["date_from"]]);
        }
        
        if (!empty($params["date_to"])) {
            $query->andWhere(["<=", "created_at", $params["date_to"]]);
        }
        
        return $query;
    }
}';

file_put_contents('components/services/UserService.php', $userServiceContent);
echo "  ✅ 创建用户服务: components/services/UserService.php\n";

echo "\n[5/5] 创建测试脚本...\n";

// 创建功能测试脚本
$functionalTestContent = '#!/bin/bash
# SaiAdmin Yii 2.0 功能测试脚本

echo "🧪 SaiAdmin Yii 2.0 功能测试"
echo "================================"

# 测试控制台命令
echo "📋 测试控制台命令..."
php yii help > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 控制台命令正常"
else
    echo "❌ 控制台命令异常"
    exit 1
fi

# 测试模型加载
echo "🏗️ 测试模型加载..."
php -r "
require_once \"vendor/autoload.php\";
require_once \"vendor/yiisoft/yii2/Yii.php\";
\$config = require \"config/web.php\";
new yii\web\Application(\$config);
try {
    \$user = new app\models\User();
    echo \"✅ 用户模型加载成功\n\";
} catch (Exception \$e) {
    echo \"❌ 用户模型加载失败: \" . \$e->getMessage() . \"\n\";
    exit(1);
}
"

# 测试服务类
echo "🔧 测试服务类..."
php -r "
require_once \"vendor/autoload.php\";
require_once \"vendor/yiisoft/yii2/Yii.php\";
\$config = require \"config/web.php\";
new yii\web\Application(\$config);
try {
    \$service = new app\components\services\UserService();
    echo \"✅ 用户服务加载成功\n\";
} catch (Exception \$e) {
    echo \"❌ 用户服务加载失败: \" . \$e->getMessage() . \"\n\";
    exit(1);
}
"

# 测试Web访问
echo "🌐 测试Web访问..."
if command -v curl &> /dev/null; then
    # 测试演示页面
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/demo)
    if [ "$response" = "200" ]; then
        echo "✅ 演示页面访问正常"
    else
        echo "⚠️ 演示页面访问异常 (HTTP $response)"
    fi
    
    # 测试API接口
    api_response=$(curl -s http://localhost:8080/demo/api)
    if [[ "$api_response" == *"code"* ]]; then
        echo "✅ API接口正常"
    else
        echo "⚠️ API接口异常"
    fi
    
    # 测试用户控制器
    user_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/user)
    if [ "$user_response" = "200" ] || [ "$user_response" = "302" ]; then
        echo "✅ 用户控制器访问正常"
    else
        echo "⚠️ 用户控制器访问异常 (HTTP $user_response)"
    fi
else
    echo "⚠️ curl命令不可用，跳过Web测试"
fi

echo ""
echo "🎯 功能测试完成！"
echo ""
echo "📖 使用指南:"
echo "- 演示页面: http://localhost:8080/demo"
echo "- 用户管理: http://localhost:8080/user"
echo "- API文档: http://localhost:8080/demo/api"
echo "- Gii生成器: http://localhost:8080/gii"
echo ""
echo "🔧 开发命令:"
echo "- 数据库迁移: php yii migrate"
echo "- 清除缓存: php yii cache/flush-all"
echo "- 生成模型: php yii gii/model"
echo "- 生成CRUD: php yii gii/crud"';

file_put_contents('functional-test.sh', $functionalTestContent);
chmod('functional-test.sh', 0755);
echo "  ✅ 创建功能测试脚本: functional-test.sh\n";

echo "\n========================================\n";
echo "💻 实际编码示例创建完成！\n";
echo "========================================\n\n";

echo "📁 创建的文件:\n";
echo "✅ controllers/UserController.php - 完整用户管理控制器\n";
echo "✅ models/User.php - 增强用户模型 (IdentityInterface)\n";
echo "✅ controllers/api/UserController.php - RESTful API控制器\n";
echo "✅ components/services/UserService.php - 业务服务层\n";
echo "✅ functional-test.sh - 功能测试脚本\n\n";

echo "🎯 立即体验:\n";
echo "1. 访问用户管理: http://localhost:8080/user\n";
echo "2. 测试API接口: http://localhost:8080/api/user\n";
echo "3. 运行功能测试: ./functional-test.sh\n";
echo "4. 使用Gii生成更多代码: http://localhost:8080/gii\n\n";

echo "🔧 开发特性:\n";
echo "- ✅ 完整的CRUD操作\n";
echo "- ✅ RESTful API接口\n";
echo "- ✅ 服务层架构\n";
echo "- ✅ 数据验证和场景\n";
echo "- ✅ 事务处理\n";
echo "- ✅ 错误处理\n";
echo "- ✅ 日志记录\n";
echo "- ✅ 权限控制\n\n";

echo "🚀 开始编码:\n";
echo "1. 查看示例代码了解最佳实践\n";
echo "2. 使用Gii生成您的模型和控制器\n";
echo "3. 参考UserService创建您的业务逻辑\n";
echo "4. 开发您的应用功能\n\n";

echo "🎉 SaiAdmin Yii 2.0 编码示例已就绪！\n";
