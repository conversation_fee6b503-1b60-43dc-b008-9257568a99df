import{a as _,_ as b}from"./edit--su55P8f.js";import{M as V}from"./@arco-design-uttiljWv.js";import{r as d,a as f,o as g,h as m,n as F,k as R,t,l as a}from"./@vue-9ZIPiVZG.js";import"./index-DkGLNqVb.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const C={class:"ma-content-block lg:flex justify-between"},Pt={__name:"index",setup(I){const s=d(),l=d(),r=d({title:"",type:"",create_time:[]}),y=f({api:_.getPageList,rowSelection:{showCheckedAll:!0},add:{show:!0,auth:["/core/notice/save"],func:async()=>{var e;(e=l.value)==null||e.open()}},edit:{show:!0,auth:["/core/notice/update"],func:async e=>{var o,i;(o=l.value)==null||o.open("edit"),(i=l.value)==null||i.setFormData(e)}},delete:{show:!0,auth:["/core/notice/destroy"],func:async e=>{var i;(await _.destroy(e)).code===200&&(V.success("删除成功！"),(i=s.value)==null||i.refresh())}}}),h=f([{title:"公告标题",dataIndex:"title",width:500},{title:"公告类型",dataIndex:"type",type:"dict",dict:"backend_notice_type",width:180},{title:"创建时间",dataIndex:"create_time",width:180}]),v=async()=>{},u=async()=>{var e;(e=s.value)==null||e.refresh()};return g(async()=>{v(),u()}),(e,o)=>{const i=m("a-input"),n=m("a-form-item"),c=m("a-col"),w=m("sa-select"),k=m("a-range-picker"),x=m("sa-table");return R(),F("div",C,[t(x,{ref_key:"crudRef",ref:s,options:y,columns:h,searchForm:r.value},{tableSearch:a(()=>[t(c,{sm:8,xs:24},{default:a(()=>[t(n,{field:"title",label:"公告标题"},{default:a(()=>[t(i,{modelValue:r.value.title,"onUpdate:modelValue":o[0]||(o[0]=p=>r.value.title=p),placeholder:"请输入公告名称"},null,8,["modelValue"])]),_:1})]),_:1}),t(c,{sm:8,xs:24},{default:a(()=>[t(n,{field:"type",label:"公告类型"},{default:a(()=>[t(w,{modelValue:r.value.type,"onUpdate:modelValue":o[1]||(o[1]=p=>r.value.type=p),dict:"backend_notice_type",placeholder:"请选择公告类型"},null,8,["modelValue"])]),_:1})]),_:1}),t(c,{sm:8,xs:24},{default:a(()=>[t(n,{field:"create_time",label:"创建时间"},{default:a(()=>[t(k,{modelValue:r.value.create_time,"onUpdate:modelValue":o[2]||(o[2]=p=>r.value.create_time=p),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["options","columns","searchForm"]),t(b,{ref_key:"editRef",ref:l,onSuccess:u},null,512)])}}};export{Pt as default};
