<?php
/**
 * 站点控制器 - Yii 2.0 标准控制器
 */
namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * 站点控制器
 */
class SiteController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => ['logout'],
                'rules' => [
                    [
                        'actions' => ['logout'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'logout' => ['post'],
                ],
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    /**
     * 首页
     */
    public function actionIndex()
    {
        return $this->render('index', [
            'title' => 'SaiAdmin Yii 2.0',
            'message' => '欢迎使用 SaiAdmin Yii 2.0 框架！',
            'features' => [
                '完全兼容 Yii 2.0',
                '模块化架构设计',
                '丰富的组件系统',
                '强大的代码生成器',
                '完善的调试工具',
                'RESTful API 支持',
                '权限管理系统',
                '缓存和日志系统'
            ]
        ]);
    }

    /**
     * 登录页面
     */
    public function actionLogin()
    {
        if (!Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $model = new \yii\base\DynamicModel(['username', 'password', 'rememberMe']);
        $model->addRule(['username', 'password'], 'required')
              ->addRule('rememberMe', 'boolean')
              ->addRule('username', 'validateLogin');

        $model->validateLogin = function ($attribute, $params) use ($model) {
            if (!$model->hasErrors()) {
                // 这里可以添加实际的用户验证逻辑
                if ($model->username !== 'admin' || $model->password !== 'admin') {
                    $model->addError($attribute, '用户名或密码错误');
                }
            }
        };

        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            // 模拟登录成功
            Yii::$app->session->setFlash('success', '登录成功！');
            return $this->goBack();
        }

        $model->password = '';
        return $this->render('login', [
            'model' => $model,
        ]);
    }

    /**
     * 退出登录
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();
        return $this->goHome();
    }

    /**
     * 联系我们
     */
    public function actionContact()
    {
        $model = new \yii\base\DynamicModel(['name', 'email', 'subject', 'body', 'verifyCode']);
        $model->addRule(['name', 'email', 'subject', 'body'], 'required')
              ->addRule('email', 'email')
              ->addRule('verifyCode', 'captcha');

        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            // 这里可以添加发送邮件的逻辑
            Yii::$app->session->setFlash('contactFormSubmitted', '感谢您的留言，我们会尽快回复您！');
            return $this->refresh();
        }

        return $this->render('contact', [
            'model' => $model,
        ]);
    }

    /**
     * 关于我们
     */
    public function actionAbout()
    {
        return $this->render('about', [
            'title' => '关于 SaiAdmin Yii 2.0',
            'content' => [
                'SaiAdmin Yii 2.0 是一个基于 Yii 2.0 框架的快速开发平台。',
                '它提供了完整的后台管理功能，包括用户管理、权限控制、系统设置等。',
                '采用模块化设计，易于扩展和维护。',
                '支持多种数据库，提供丰富的组件和工具。'
            ],
            'features' => [
                '基于 Yii 2.0 框架' => '享受 Yii 2.0 的所有特性和优势',
                '模块化架构' => '清晰的代码结构，易于维护和扩展',
                '权限管理' => '基于 RBAC 的权限控制系统',
                '代码生成' => '强大的 Gii 代码生成工具',
                '调试工具' => '完善的调试和性能分析工具',
                'RESTful API' => '支持现代化的 API 开发',
                '缓存系统' => '多种缓存方案提升性能',
                '国际化' => '支持多语言和本地化'
            ]
        ]);
    }

    /**
     * API 信息
     */
    public function actionApi()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        return [
            'name' => 'SaiAdmin Yii 2.0 API',
            'version' => '1.0.0',
            'framework' => 'Yii ' . Yii::getVersion(),
            'php_version' => PHP_VERSION,
            'timestamp' => time(),
            'endpoints' => [
                'GET /' => '首页',
                'GET /about' => '关于页面',
                'GET /contact' => '联系页面',
                'POST /site/login' => '用户登录',
                'POST /site/logout' => '用户退出',
                'GET /demo' => '功能演示',
                'GET /demo/api' => 'API 演示',
                'GET /user' => '用户管理',
                'GET /gii' => '代码生成器',
                'GET /debug' => '调试工具'
            ],
            'status' => 'running'
        ];
    }

    /**
     * 系统信息
     */
    public function actionInfo()
    {
        return $this->render('info', [
            'system_info' => [
                'Framework' => 'Yii ' . Yii::getVersion(),
                'PHP Version' => PHP_VERSION,
                'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'Operating System' => PHP_OS,
                'Memory Limit' => ini_get('memory_limit'),
                'Max Execution Time' => ini_get('max_execution_time') . 's',
                'Upload Max Filesize' => ini_get('upload_max_filesize'),
                'Post Max Size' => ini_get('post_max_size')
            ],
            'application_info' => [
                'Application ID' => Yii::$app->id,
                'Application Name' => Yii::$app->name,
                'Environment' => YII_ENV,
                'Debug Mode' => YII_DEBUG ? 'Enabled' : 'Disabled',
                'Base Path' => Yii::getAlias('@app'),
                'Web Root' => Yii::getAlias('@webroot'),
                'Runtime Path' => Yii::getAlias('@runtime')
            ]
        ]);
    }
}
