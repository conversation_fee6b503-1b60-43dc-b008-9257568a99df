<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\ceshi\controller\ceshi;

use plugin\saiadmin\basic\BaseController;
use app\ceshi\logic\ceshi\DdwxArticleLogic;
use app\ceshi\validate\ceshi\DdwxArticleValidate;
use support\Request;
use support\Response;

/**
 * 23控制器
 */
class DdwxArticleController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new DdwxArticleLogic();
        $this->validate = new DdwxArticleValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['name', ''],
            ['subname', ''],
            ['showname', ''],
            ['showsubname', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

}
