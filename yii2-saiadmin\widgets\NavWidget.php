<?php
/**
 * <PERSON><PERSON>dmin 导航小部件
 */
namespace app\widgets;

use yii\base\Widget;
use yii\helpers\Html;

/**
 * 导航小部件
 */
class NavWidget extends Widget
{
    public $items = [];
    public $options = [];

    public function init()
    {
        parent::init();
        if (empty($this->options["class"])) {
            $this->options["class"] = "nav nav-pills";
        }
    }

    public function run()
    {
        $items = [];
        foreach ($this->items as $item) {
            $items[] = Html::tag("li", Html::a($item["label"], $item["url"]), ["class" => "nav-item"]);
        }
        
        return Html::tag("ul", implode("\n", $items), $this->options);
    }
}