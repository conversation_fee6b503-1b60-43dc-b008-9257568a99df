2025-08-02 20:35:22 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:25 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:27 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:29 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:31 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:33 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:36 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:38 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:40 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:42 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:45 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:47 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:49 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:51 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:35:53 pid:1 think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:28 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:31 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:34 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:36 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:38 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:40 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:43 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:45 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:48 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:50 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:52 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:54 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:56 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:48:59 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:01 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:04 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:06 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:08 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:10 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:12 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:15 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:17 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:19 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:21 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:23 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:25 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:27 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:29 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:31 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:33 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:36 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:38 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:40 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:42 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:45 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:47 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:50 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:52 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:55 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:49:58 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:01 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:03 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:05 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:08 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:11 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:13 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:15 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:17 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:19 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:22 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:24 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:26 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:28 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:31 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:34 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:36 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:38 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:40 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:42 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:44 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:47 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:49 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:51 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:53 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:55 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:50:58 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:00 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:02 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:04 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:06 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:09 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:11 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:14 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:16 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:18 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:20 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:23 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:26 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:28 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:30 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:33 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:35 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:37 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:39 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
2025-08-02 20:51:42 pid:1 think\db\exception\PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'saiadmin.sa_tool_crontab' doesn't exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\connector\Mysql.php(68): think\db\PDOConnection->getPDOStatement('SHOW FULL COLUM...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(438): think\db\connector\Mysql->getFields('`sa_tool_cronta...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(395): think\db\PDOConnection->getTableFieldsInfo('sa_tool_crontab')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(360): think\db\PDOConnection->getCachedSchemaInfo('127.0.0.1_3306|...', 'sa_tool_crontab', false)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(424): think\db\PDOConnection->getSchemaInfo('sa_tool_crontab')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(513): think\db\PDOConnection->getTableInfo('sa_tool_crontab', 'type')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(60): think\db\PDOConnection->getFieldsType('sa_tool_crontab')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\concern\TableFieldInfo.php(72): think\db\Query->getFieldsType()
#8 [internal function]: think\db\Query->getFieldType('create_time')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): call_user_func_array(Array, Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#13 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\tool\Crontab))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\tool\Crontab))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\tool\CrontabLogic.php(26): think\Model->__construct()
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(22): plugin\saiadmin\app\logic\tool\CrontabLogic->__construct()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\process\Task.php(17): plugin\saiadmin\process\Task->initStart()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(512): plugin\saiadmin\process\Task->onWorkerStart(Object(Workerman\Worker))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\support\helpers.php(558): worker_bind(Object(Workerman\Worker), Object(plugin\saiadmin\process\Task))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2577): {closure}(Object(Workerman\Worker))
#21 [internal function]: Workerman\Worker->Workerman\{closure}()
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(2594): Fiber->start()
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1612): Workerman\Worker->run()
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_plugin.saiadmin.task.php(33): Workerman\Worker::runAll()
#27 {main}
