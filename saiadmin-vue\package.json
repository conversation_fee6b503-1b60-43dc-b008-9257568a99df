{"name": "saiadmin-vue", "admin_name": "<PERSON><PERSON><PERSON><PERSON>", "version": "5.0", "type": "module", "license": "MIT", "scripts": {"dev": "vite serve --mode development", "build": "vite build", "preview": "vite preview", "tailwind": "tailwind-config-viewer -o -c tailwind.config.cjs"}, "dependencies": {"@arco-design/color": "^0.4.0", "@arco-design/web-vue": "^2.57.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "autoprefixer": "^10.4.17", "axios": "^0.27.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "echarts": "^5.4.2", "file2md5": "^1.3.0", "lodash": "^4.17.21", "md-editor-v3": "^4.13.5", "monaco-editor": "^0.33.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.4.1", "postcss-import": "^14.1.0", "qs": "^6.10.3", "resize-observer-polyfill": "^1.5.1", "sortablejs": "^1.15.0", "tailwindcss": "^3.4.1", "vue": "^3.4.19", "vue-clipboard3": "^2.0.0", "vue-color-kit": "^1.0.5", "vue-echarts": "^6.0.2", "vue-i18n": "^9.1.10", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@iconify/vue": "^4.2.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "less": "^4.1.3", "less-loader": "^11.1.4", "rollup-plugin-visualizer": "^5.12.0", "tailwind-config-viewer": "^1.7.3", "typescript": "^4.7.4", "vite": "^5.1.4"}}