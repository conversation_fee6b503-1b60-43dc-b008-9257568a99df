@echo off
echo ========================================
echo    SaiAdmin 开发环境启动脚本
echo ========================================
echo.

echo [1/2] 启动后端服务器 (Webman)...
cd webman
start "SaiAdmin Backend" cmd /k "php start.php start"
echo 后端服务器启动中... (端口: 8787)
echo.

echo [2/2] 启动前端开发服务器 (Vue.js)...
cd ../saiadmin-vue
start "SaiAdmin Frontend" cmd /k "npm run dev"
echo 前端开发服务器启动中... (端口: 5173)
echo.

echo ========================================
echo 服务启动完成！
echo.
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:8787
echo.
echo 按任意键关闭此窗口...
echo ========================================
pause >nul
