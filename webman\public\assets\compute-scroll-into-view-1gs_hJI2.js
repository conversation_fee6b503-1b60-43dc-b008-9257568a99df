function J(e){return typeof e=="object"&&e!=null&&e.nodeType===1}function R(e,r){return(!r||e!=="hidden")&&e!=="visible"&&e!=="clip"}function q(e,r){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var o=getComputedStyle(e,null);return R(o.overflowY,r)||R(o.overflowX,r)||function(u){var i=function(n){if(!n.ownerDocument||!n.ownerDocument.defaultView)return null;try{return n.ownerDocument.defaultView.frameElement}catch{return null}}(u);return!!i&&(i.clientHeight<u.scrollHeight||i.clientWidth<u.scrollWidth)}(e)}return!1}function Q(e,r,o,u,i,n,d,c){return n<e&&d>r||n>e&&d<r?0:n<=e&&c<=o||d>=r&&c>=o?n-e-u:d>r&&c<o||n<e&&c>o?d-r+i:0}var te=function(e,r){var o=window,u=r.scrollMode,i=r.block,n=r.inline,d=r.boundary,c=r.skipOverflowHiddenElements,B=typeof d=="function"?d:function(ee){return ee!==d};if(!J(e))throw new TypeError("Invalid target");for(var z,A,F=document.scrollingElement||document.documentElement,y=[],l=e;J(l)&&B(l);){if((l=(A=(z=l).parentElement)==null?z.getRootNode().host||null:A)===F){y.push(l);break}l!=null&&l===document.body&&q(l)&&!q(document.documentElement)||l!=null&&q(l,c)&&y.push(l)}for(var w=o.visualViewport?o.visualViewport.width:innerWidth,m=o.visualViewport?o.visualViewport.height:innerHeight,v=window.scrollX||pageXOffset,W=window.scrollY||pageYOffset,g=e.getBoundingClientRect(),H=g.height,b=g.width,M=g.top,U=g.right,Z=g.bottom,V=g.left,f=i==="start"||i==="nearest"?M:i==="end"?Z:M+H/2,h=n==="center"?V+b/2:n==="end"?U:V,$=[],j=0;j<y.length;j++){var t=y[j],p=t.getBoundingClientRect(),x=p.height,E=p.width,I=p.top,D=p.right,N=p.bottom,T=p.left;if(u==="if-needed"&&M>=0&&V>=0&&Z<=m&&U<=w&&M>=I&&Z<=N&&V>=T&&U<=D)return $;var k=getComputedStyle(t),L=parseInt(k.borderLeftWidth,10),O=parseInt(k.borderTopWidth,10),S=parseInt(k.borderRightWidth,10),X=parseInt(k.borderBottomWidth,10),s=0,a=0,Y="offsetWidth"in t?t.offsetWidth-t.clientWidth-L-S:0,_="offsetHeight"in t?t.offsetHeight-t.clientHeight-O-X:0,K="offsetWidth"in t?t.offsetWidth===0?0:E/t.offsetWidth:0,P="offsetHeight"in t?t.offsetHeight===0?0:x/t.offsetHeight:0;if(F===t)s=i==="start"?f:i==="end"?f-m:i==="nearest"?Q(W,W+m,m,O,X,W+f,W+f+H,H):f-m/2,a=n==="start"?h:n==="center"?h-w/2:n==="end"?h-w:Q(v,v+w,w,L,S,v+h,v+h+b,b),s=Math.max(0,s+W),a=Math.max(0,a+v);else{s=i==="start"?f-I-O:i==="end"?f-N+X+_:i==="nearest"?Q(I,N,x,O,X+_,f,f+H,H):f-(I+x/2)+_/2,a=n==="start"?h-T-L:n==="center"?h-(T+E/2)+Y/2:n==="end"?h-D+S+Y:Q(T,D,E,L,S+Y,h,h+b,b);var C=t.scrollLeft,G=t.scrollTop;f+=G-(s=Math.max(0,Math.min(G+s/P,t.scrollHeight-x/P+_))),h+=C-(a=Math.max(0,Math.min(C+a/K,t.scrollWidth-E/K+Y)))}$.push({el:t,top:s,left:a})}return $};export{te as i};
