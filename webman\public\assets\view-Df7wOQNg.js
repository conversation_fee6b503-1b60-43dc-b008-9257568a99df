import{t as w}from"./index-ybrmzYq5.js";import{a as h}from"./post-DszuaI2s.js";import{r as m,h as l,j as g,k as D,l as o,t as e,m as n,z as s,a1 as k,O as y}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const B=["textContent"],N=["textContent"],V=["textContent"],j=["textContent"],z=["textContent"],Et={__name:"view",emits:["success"],setup(O,{expose:_,emit:P}){const d=m(),i=m(),u=m(!1),c=m(!1),v=async r=>{d.value=r,i.value={},u.value=!0,await f()},f=async()=>{var p;c.value=!0;const r=await h.read((p=d.value)==null?void 0:p.id);r.code===200&&(i.value=r.data),c.value=!1};return _({open:v}),(r,p)=>{const a=l("a-descriptions-item"),x=l("sa-dict"),C=l("a-descriptions"),b=l("a-spin");return D(),g(y("a-drawer"),{visible:u.value,"onUpdate:visible":p[0]||(p[0]=t=>u.value=t),width:k(w).getDevice()==="mobile"?"100%":"60%",title:"查看详情",footer:!1},{default:o(()=>[e(b,{loading:c.value,class:"w-full"},{default:o(()=>[e(C,{column:1,bordered:""},{default:o(()=>[e(a,{label:"编号"},{default:o(()=>{var t;return[n("span",{textContent:s((t=i.value)==null?void 0:t.id)},null,8,B)]}),_:1}),e(a,{label:"岗位名称"},{default:o(()=>{var t;return[n("span",{textContent:s((t=i.value)==null?void 0:t.name)},null,8,N)]}),_:1}),e(a,{label:"岗位标识"},{default:o(()=>{var t;return[n("span",{textContent:s((t=i.value)==null?void 0:t.code)},null,8,V)]}),_:1}),e(a,{label:"排序"},{default:o(()=>{var t;return[n("span",{textContent:s((t=i.value)==null?void 0:t.sort)},null,8,j)]}),_:1}),e(a,{label:"状态"},{default:o(()=>{var t;return[e(x,{value:(t=i.value)==null?void 0:t.status,dict:"data_status",render:"span"},null,8,["value"])]}),_:1}),e(a,{label:"备注"},{default:o(()=>{var t;return[n("span",{textContent:s((t=i.value)==null?void 0:t.remark)},null,8,z)]}),_:1})]),_:1})]),_:1},8,["loading"])]),_:1},40,["visible","width"])}}};export{Et as default};
