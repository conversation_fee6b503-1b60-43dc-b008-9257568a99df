import{p as u}from"./index-ybrmzYq5.js";import{h as n,n as g,k as f,m as a,t as o,l as i,y as r,z as c,a1 as b}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const y={class:"w-full lg:w-3/12 ma-content-block rounded-sm ml-0 lg:ml-3 p-3 mt-3"},_={class:"block lg:grid lg:grid-cols-2 lg:gap-1 mt-3"},k={class:"w-11/12 mx-auto mt-3"},$t={__name:"st-saiadmin",setup(v){const m=(d="")=>{window.open(d)};return(d,t)=>{const p=n("a-button"),e=n("a-card"),l=n("a-tag");return f(),g("div",y,[t[8]||(t[8]=a("div",{class:"flex"},"SaiAdmin 相关",-1)),a("div",_,[o(e,{class:"rounded-sm text-center","body-style":{padding:0},bordered:!1},{default:i(()=>[o(p,{type:"outline",class:"w-4/5",onClick:t[0]||(t[0]=s=>m("https://saithink.top"))},{default:i(()=>t[4]||(t[4]=[r("官方网站")])),_:1})]),_:1}),o(e,{class:"rounded-sm text-center mt-2 lg:mt-0","body-style":{padding:0},bordered:!1},{default:i(()=>[o(p,{type:"outline",class:"w-4/5",onClick:t[1]||(t[1]=s=>m("https://saithink.top/guide/introduction/"))},{default:i(()=>t[5]||(t[5]=[r(" 开发文档 ")])),_:1})]),_:1}),o(e,{class:"rounded-sm text-center mt-2","body-style":{padding:0},bordered:!1},{default:i(()=>[o(p,{type:"outline",class:"w-4/5",onClick:t[2]||(t[2]=s=>m("https://github.com/saithink/saiadmin"))},{default:i(()=>t[6]||(t[6]=[r(" Github ")])),_:1})]),_:1}),o(e,{class:"rounded-sm text-center mt-2","body-style":{padding:0},bordered:!1},{default:i(()=>[o(p,{type:"outline",class:"w-4/5",onClick:t[3]||(t[3]=s=>m("https://gitee.com/appsai/saiadmin"))},{default:i(()=>t[7]||(t[7]=[r(" Gitee ")])),_:1})]),_:1})]),a("div",k,[o(l,{class:"ml-0.5"},{default:i(()=>[r("SaiAdmin v"+c(b(u).version)+" release",1)]),_:1})])])}}};export{$t as default};
