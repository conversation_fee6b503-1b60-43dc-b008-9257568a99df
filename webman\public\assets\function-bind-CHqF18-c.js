var v,h;function q(){if(h)return v;h=1;var p="Function.prototype.bind called on incompatible ",s=Object.prototype.toString,d=Math.max,m="[object Function]",l=function(e,n){for(var t=[],r=0;r<e.length;r+=1)t[r]=e[r];for(var o=0;o<n.length;o+=1)t[o+e.length]=n[o];return t},b=function(e,n){for(var t=[],r=n,o=0;r<e.length;r+=1,o+=1)t[o]=e[r];return t},F=function(a,e){for(var n="",t=0;t<a.length;t+=1)n+=a[t],t+1<a.length&&(n+=e);return n};return v=function(e){var n=this;if(typeof n!="function"||s.apply(n)!==m)throw new TypeError(p+n);for(var t=b(arguments,1),r,o=function(){if(this instanceof r){var u=n.apply(this,l(t,arguments));return Object(u)===u?u:this}return n.apply(e,l(t,arguments))},R=d(0,n.length-t.length),y=[],i=0;i<R;i++)y[i]="$"+i;if(r=Function("binder","return function ("+F(y,",")+"){ return binder.apply(this,arguments); }")(o),n.prototype){var c=function(){};c.prototype=n.prototype,r.prototype=new c,c.prototype=null}return r},v}var f,g;function S(){if(g)return f;g=1;var p=q();return f=Function.prototype.bind||p,f}export{S as r};
