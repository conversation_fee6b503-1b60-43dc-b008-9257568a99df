import{r as l}from"./function-bind-CHqF18-c.js";import{t as i}from"./es-errors-CFxpeikN.js";var n,a;function p(){return a||(a=1,n=Function.prototype.call),n}var e,t;function c(){return t||(t=1,e=Function.prototype.apply),e}var u=typeof Reflect<"u"&&Reflect&&Reflect.apply,o=l(),f=c(),y=p(),v=u,d=v||o.call(y,f),s=l(),A=i,$=p(),F=d,m=function(r){if(r.length<1||typeof r[0]!="function")throw new A("a function is required");return F(s,$,r)};export{p as a,m as c,c as r};
