import{f as n}from"./function-bind-BbkWVFrm.js";import{t}from"./es-errors-CFxpeikN.js";var p=Function.prototype.call,a,l;function e(){return l||(l=1,a=Function.prototype.apply),a}var c=typeof Reflect<"u"&&Reflect&&Reflect.apply,i=n,o=e(),f=p,u=c,y=u||i.call(f,o),v=n,d=t,A=p,$=y,F=function(r){if(r.length<1||typeof r[0]!="function")throw new d("a function is required");return $(v,A,r)};export{F as c,p as f,e as r};
