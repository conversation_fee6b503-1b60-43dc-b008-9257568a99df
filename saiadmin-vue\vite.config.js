import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { visualizer } from 'rollup-plugin-visualizer'
export default ({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const proxyPrefix = env.VITE_APP_PROXY_PREFIX

  return defineConfig({
    base: env.VITE_APP_BASE,
    plugins: [
      vue(),
      vueJsx(),
      visualizer({
        emitFile: false,
        file: 'stats.html', //分析图生成的文件名
        open: true //如果存在本地服务端口，将在打包后自动展示
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@cps': resolve(__dirname, 'src/components'),
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
      }
    },

    build: {
      chunkSizeWarningLimit: 1500,
      // 优化：启用压缩和代码分割
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // 生产环境移除console
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          // 优化：更精细的代码分割策略
          manualChunks: {
            // 将Vue相关库打包到一个chunk
            vue: ['vue', 'vue-router', 'pinia'],
            // 将Arco Design打包到一个chunk
            arco: ['@arco-design/web-vue'],
            // 将工具库打包到一个chunk
            utils: ['lodash', 'dayjs', 'crypto-js'],
            // 将图表库单独打包
            charts: ['echarts']
          },
          // 优化：资源文件命名
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            const extType = info[info.length - 1]
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              return `media/[name]-[hash].${extType}`
            }
            if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
              return `img/[name]-[hash].${extType}`
            }
            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              return `fonts/[name]-[hash].${extType}`
            }
            return `assets/[name]-[hash].${extType}`
          }
        }
      },
      // 优化：启用gzip压缩
      reportCompressedSize: false,
      // 优化：设置更大的chunk大小限制
      chunkSizeWarningLimit: 2000
    },

    server: {
      host: '0.0.0.0',
      port: env.VITE_APP_PORT || process.env.port,
      proxy: {
        [proxyPrefix]: {
          target: env.VITE_APP_BASE_URL,
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path) => path.replace(new RegExp(`^${proxyPrefix}`), '')
        }
      }
    }
  })
}
