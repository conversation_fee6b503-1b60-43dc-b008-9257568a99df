{"version": 3, "sources": ["../../vue-color-kit/dist/vue-color-kit.esm-bundler.js"], "sourcesContent": ["/*!\n  * vue-color-kit v1.0.6\n  * (c) 2023 \n  * @license MIT\n  */\nimport { defineComponent, openBlock, createBlock, withModifiers, createVNode, createCommentVNode, computed, toDisplayString, withDirectives, vModelText, ref, onUnmounted, Fragment, renderList, resolveComponent, renderSlot } from 'vue';\n\nfunction setColorValue(color) {\r\n    let rgba = { r: 0, g: 0, b: 0, a: 1 };\r\n    if (/#/.test(color)) {\r\n        rgba = hex2rgb(color);\r\n    }\r\n    else if (/rgb/.test(color)) {\r\n        rgba = rgb2rgba(color);\r\n    }\r\n    else if (typeof color === 'string') {\r\n        rgba = rgb2rgba(`rgba(${color})`);\r\n    }\r\n    else if (Object.prototype.toString.call(color) === '[object Object]') {\r\n        rgba = color;\r\n    }\r\n    const { r, g, b, a } = rgba;\r\n    const { h, s, v } = rgb2hsv(rgba);\r\n    return { r, g, b, a: a === undefined ? 1 : a, h, s, v };\r\n}\r\nfunction createAlphaSquare(size) {\r\n    const canvas = document.createElement('canvas');\r\n    const ctx = canvas.getContext('2d');\r\n    const doubleSize = size * 2;\r\n    canvas.width = doubleSize;\r\n    canvas.height = doubleSize;\r\n    ctx.fillStyle = '#ffffff';\r\n    ctx.fillRect(0, 0, doubleSize, doubleSize);\r\n    ctx.fillStyle = '#ccd5db';\r\n    ctx.fillRect(0, 0, size, size);\r\n    ctx.fillRect(size, size, size, size);\r\n    return canvas;\r\n}\r\nfunction createLinearGradient(direction, ctx, width, height, color1, color2) {\r\n    // l horizontal p vertical\r\n    const isL = direction === 'l';\r\n    const gradient = ctx.createLinearGradient(0, 0, isL ? width : 0, isL ? 0 : height);\r\n    gradient.addColorStop(0.01, color1);\r\n    gradient.addColorStop(0.99, color2);\r\n    ctx.fillStyle = gradient;\r\n    ctx.fillRect(0, 0, width, height);\r\n}\r\nfunction rgb2hex({ r, g, b }, toUpper) {\r\n    const change = (val) => ('0' + Number(val).toString(16)).slice(-2);\r\n    const color = `#${change(r)}${change(g)}${change(b)}`;\r\n    return toUpper ? color.toUpperCase() : color;\r\n}\r\nfunction hex2rgb(hex) {\r\n    hex = hex.slice(1);\r\n    const change = (val) => parseInt(val, 16) || 0; // Avoid NaN situations\r\n    return {\r\n        r: change(hex.slice(0, 2)),\r\n        g: change(hex.slice(2, 4)),\r\n        b: change(hex.slice(4, 6)),\r\n    };\r\n}\r\nfunction rgb2rgba(rgba) {\r\n    if (typeof rgba === 'string') {\r\n        rgba = (/rgba?\\((.*?)\\)/.exec(rgba) || ['', '0,0,0,1'])[1].split(',');\r\n        return {\r\n            r: Number(rgba[0]) || 0,\r\n            g: Number(rgba[1]) || 0,\r\n            b: Number(rgba[2]) || 0,\r\n            a: Number(rgba[3] ? rgba[3] : 1),\r\n        };\r\n    }\r\n    else {\r\n        return rgba;\r\n    }\r\n}\r\nfunction rgb2hsv({ r, g, b }) {\r\n    r = r / 255;\r\n    g = g / 255;\r\n    b = b / 255;\r\n    const max = Math.max(r, g, b);\r\n    const min = Math.min(r, g, b);\r\n    const delta = max - min;\r\n    let h = 0;\r\n    if (max === min) {\r\n        h = 0;\r\n    }\r\n    else if (max === r) {\r\n        if (g >= b) {\r\n            h = (60 * (g - b)) / delta;\r\n        }\r\n        else {\r\n            h = (60 * (g - b)) / delta + 360;\r\n        }\r\n    }\r\n    else if (max === g) {\r\n        h = (60 * (b - r)) / delta + 120;\r\n    }\r\n    else if (max === b) {\r\n        h = (60 * (r - g)) / delta + 240;\r\n    }\r\n    h = Math.floor(h);\r\n    let s = parseFloat((max === 0 ? 0 : 1 - min / max).toFixed(2));\r\n    let v = parseFloat(max.toFixed(2));\r\n    return { h, s, v };\r\n}\n\nvar script = defineComponent({\r\n    props: {\r\n        color: {\r\n            type: String,\r\n            default: '#000000',\r\n        },\r\n        hsv: {\r\n            type: Object,\r\n            default: null,\r\n        },\r\n        size: {\r\n            type: Number,\r\n            default: 152,\r\n        },\r\n    },\r\n    emits: ['selectSaturation'],\r\n    data() {\r\n        return {\r\n            slideSaturationStyle: {},\r\n        };\r\n    },\r\n    // Can’t monitor, otherwise the color will change when you change yourself\r\n    // watch: {\r\n    //     color() {\r\n    //         this.renderColor()\r\n    //     }\r\n    // },\r\n    mounted() {\r\n        this.renderColor();\r\n        this.renderSlide();\r\n    },\r\n    methods: {\r\n        renderColor() {\r\n            const canvas = this.$refs.canvasSaturation;\r\n            const size = this.size;\r\n            const ctx = canvas.getContext('2d');\r\n            canvas.width = size;\r\n            canvas.height = size;\r\n            ctx.fillStyle = this.color;\r\n            ctx.fillRect(0, 0, size, size);\r\n            createLinearGradient('l', ctx, size, size, '#FFFFFF', 'rgba(255,255,255,0)');\r\n            createLinearGradient('p', ctx, size, size, 'rgba(0,0,0,0)', '#000000');\r\n        },\r\n        renderSlide() {\r\n            this.slideSaturationStyle = {\r\n                left: this.hsv.s * this.size - 5 + 'px',\r\n                top: (1 - this.hsv.v) * this.size - 5 + 'px',\r\n            };\r\n        },\r\n        selectSaturation(e) {\r\n            const { top: saturationTop, left: saturationLeft, } = this.$el.getBoundingClientRect();\r\n            const ctx = e.target.getContext('2d');\r\n            const mousemove = (e) => {\r\n                let x = e.clientX - saturationLeft;\r\n                let y = e.clientY - saturationTop;\r\n                if (x < 0) {\r\n                    x = 0;\r\n                }\r\n                if (y < 0) {\r\n                    y = 0;\r\n                }\r\n                if (x > this.size) {\r\n                    x = this.size;\r\n                }\r\n                if (y > this.size) {\r\n                    y = this.size;\r\n                }\r\n                // Do not modify the dom by monitoring data changes, otherwise when the color is #ffffff, the slide will go to the lower left corner\r\n                this.slideSaturationStyle = {\r\n                    left: x - 5 + 'px',\r\n                    top: y - 5 + 'px',\r\n                };\r\n                // If you use the maximum value, the selected pixel will be empty, and the empty default is black\r\n                const imgData = ctx.getImageData(Math.min(x, this.size - 1), Math.min(y, this.size - 1), 1, 1);\r\n                const [r, g, b] = imgData.data;\r\n                this.$emit('selectSaturation', { r, g, b });\r\n            };\r\n            mousemove(e);\r\n            const mouseup = () => {\r\n                document.removeEventListener('mousemove', mousemove);\r\n                document.removeEventListener('mouseup', mouseup);\r\n            };\r\n            document.addEventListener('mousemove', mousemove);\r\n            document.addEventListener('mouseup', mouseup);\r\n        },\r\n    },\r\n});\n\nconst _hoisted_1 = { ref: \"canvasSaturation\" };\n\nfunction render(_ctx, _cache, $props, $setup, $data, $options) {\n  return (openBlock(), createBlock(\"div\", {\n    class: \"saturation\",\n    onMousedown: _cache[1] || (_cache[1] = withModifiers((...args) => (_ctx.selectSaturation && _ctx.selectSaturation(...args)), [\"prevent\",\"stop\"]))\n  }, [\n    createVNode(\"canvas\", _hoisted_1, null, 512 /* NEED_PATCH */),\n    createVNode(\"div\", {\n      style: _ctx.slideSaturationStyle,\n      class: \"slide\"\n    }, null, 4 /* STYLE */)\n  ], 32 /* HYDRATE_EVENTS */))\n}\n\nscript.render = render;\nscript.__file = \"src/color/Saturation.vue\";\n\nvar script$1 = defineComponent({\r\n    props: {\r\n        hsv: {\r\n            type: Object,\r\n            default: null,\r\n        },\r\n        width: {\r\n            type: Number,\r\n            default: 15,\r\n        },\r\n        height: {\r\n            type: Number,\r\n            default: 152,\r\n        },\r\n    },\r\n    emits: ['selectHue'],\r\n    data() {\r\n        return {\r\n            slideHueStyle: {},\r\n        };\r\n    },\r\n    mounted() {\r\n        this.renderColor();\r\n        this.renderSlide();\r\n    },\r\n    methods: {\r\n        renderColor() {\r\n            const canvas = this.$refs.canvasHue;\r\n            const width = this.width;\r\n            const height = this.height;\r\n            const ctx = canvas.getContext('2d');\r\n            canvas.width = width;\r\n            canvas.height = height;\r\n            const gradient = ctx.createLinearGradient(0, 0, 0, height);\r\n            gradient.addColorStop(0, '#FF0000'); // red\r\n            gradient.addColorStop(0.17 * 1, '#FF00FF'); // purple\r\n            gradient.addColorStop(0.17 * 2, '#0000FF'); // blue\r\n            gradient.addColorStop(0.17 * 3, '#00FFFF'); // green\r\n            gradient.addColorStop(0.17 * 4, '#00FF00'); // green\r\n            gradient.addColorStop(0.17 * 5, '#FFFF00'); // yellow\r\n            gradient.addColorStop(1, '#FF0000'); // red\r\n            ctx.fillStyle = gradient;\r\n            ctx.fillRect(0, 0, width, height);\r\n        },\r\n        renderSlide() {\r\n            this.slideHueStyle = {\r\n                top: (1 - this.hsv.h / 360) * this.height - 2 + 'px',\r\n            };\r\n        },\r\n        selectHue(e) {\r\n            const { top: hueTop } = this.$el.getBoundingClientRect();\r\n            const ctx = e.target.getContext('2d');\r\n            const mousemove = (e) => {\r\n                let y = e.clientY - hueTop;\r\n                if (y < 0) {\r\n                    y = 0;\r\n                }\r\n                if (y > this.height) {\r\n                    y = this.height;\r\n                }\r\n                this.slideHueStyle = {\r\n                    top: y - 2 + 'px',\r\n                };\r\n                // If you use the maximum value, the selected pixel will be empty, and the empty default is black\r\n                const imgData = ctx.getImageData(0, Math.min(y, this.height - 1), 1, 1);\r\n                const [r, g, b] = imgData.data;\r\n                this.$emit('selectHue', { r, g, b });\r\n            };\r\n            mousemove(e);\r\n            const mouseup = () => {\r\n                document.removeEventListener('mousemove', mousemove);\r\n                document.removeEventListener('mouseup', mouseup);\r\n            };\r\n            document.addEventListener('mousemove', mousemove);\r\n            document.addEventListener('mouseup', mouseup);\r\n        },\r\n    },\r\n});\n\nconst _hoisted_1$1 = { ref: \"canvasHue\" };\n\nfunction render$1(_ctx, _cache, $props, $setup, $data, $options) {\n  return (openBlock(), createBlock(\"div\", {\n    class: \"hue\",\n    onMousedown: _cache[1] || (_cache[1] = withModifiers((...args) => (_ctx.selectHue && _ctx.selectHue(...args)), [\"prevent\",\"stop\"]))\n  }, [\n    createVNode(\"canvas\", _hoisted_1$1, null, 512 /* NEED_PATCH */),\n    createVNode(\"div\", {\n      style: _ctx.slideHueStyle,\n      class: \"slide\"\n    }, null, 4 /* STYLE */)\n  ], 32 /* HYDRATE_EVENTS */))\n}\n\nscript$1.render = render$1;\nscript$1.__file = \"src/color/Hue.vue\";\n\nvar script$2 = defineComponent({\r\n    props: {\r\n        color: {\r\n            type: String,\r\n            default: '#000000',\r\n        },\r\n        rgba: {\r\n            type: Object,\r\n            default: null,\r\n        },\r\n        width: {\r\n            type: Number,\r\n            default: 15,\r\n        },\r\n        height: {\r\n            type: Number,\r\n            default: 152,\r\n        },\r\n    },\r\n    emits: ['selectAlpha'],\r\n    data() {\r\n        return {\r\n            slideAlphaStyle: {},\r\n            alphaSize: 5,\r\n        };\r\n    },\r\n    watch: {\r\n        color() {\r\n            this.renderColor();\r\n        },\r\n        'rgba.a'() {\r\n            this.renderSlide();\r\n        },\r\n    },\r\n    mounted() {\r\n        this.renderColor();\r\n        this.renderSlide();\r\n    },\r\n    methods: {\r\n        renderColor() {\r\n            const canvas = this.$refs.canvasAlpha;\r\n            const width = this.width;\r\n            const height = this.height;\r\n            const size = this.alphaSize;\r\n            const canvasSquare = createAlphaSquare(size);\r\n            const ctx = canvas.getContext('2d');\r\n            canvas.width = width;\r\n            canvas.height = height;\r\n            ctx.fillStyle = ctx.createPattern(canvasSquare, 'repeat');\r\n            ctx.fillRect(0, 0, width, height);\r\n            createLinearGradient('p', ctx, width, height, 'rgba(255,255,255,0)', this.color);\r\n        },\r\n        renderSlide() {\r\n            this.slideAlphaStyle = {\r\n                top: this.rgba.a * this.height - 2 + 'px',\r\n            };\r\n        },\r\n        selectAlpha(e) {\r\n            const { top: hueTop } = this.$el.getBoundingClientRect();\r\n            const mousemove = (e) => {\r\n                let y = e.clientY - hueTop;\r\n                if (y < 0) {\r\n                    y = 0;\r\n                }\r\n                if (y > this.height) {\r\n                    y = this.height;\r\n                }\r\n                let a = parseFloat((y / this.height).toFixed(2));\r\n                this.$emit('selectAlpha', a);\r\n            };\r\n            mousemove(e);\r\n            const mouseup = () => {\r\n                document.removeEventListener('mousemove', mousemove);\r\n                document.removeEventListener('mouseup', mouseup);\r\n            };\r\n            document.addEventListener('mousemove', mousemove);\r\n            document.addEventListener('mouseup', mouseup);\r\n        },\r\n    },\r\n});\n\nconst _hoisted_1$2 = { ref: \"canvasAlpha\" };\n\nfunction render$2(_ctx, _cache, $props, $setup, $data, $options) {\n  return (openBlock(), createBlock(\"div\", {\n    class: \"color-alpha\",\n    onMousedown: _cache[1] || (_cache[1] = withModifiers((...args) => (_ctx.selectAlpha && _ctx.selectAlpha(...args)), [\"prevent\",\"stop\"]))\n  }, [\n    createVNode(\"canvas\", _hoisted_1$2, null, 512 /* NEED_PATCH */),\n    createVNode(\"div\", {\n      style: _ctx.slideAlphaStyle,\n      class: \"slide\"\n    }, null, 4 /* STYLE */)\n  ], 32 /* HYDRATE_EVENTS */))\n}\n\nscript$2.render = render$2;\nscript$2.__file = \"src/color/Alpha.vue\";\n\nvar script$3 = defineComponent({\r\n    props: {\r\n        color: {\r\n            type: String,\r\n            default: '#000000',\r\n        },\r\n        width: {\r\n            type: Number,\r\n            default: 100,\r\n        },\r\n        height: {\r\n            type: Number,\r\n            default: 30,\r\n        },\r\n    },\r\n    data() {\r\n        return {\r\n            alphaSize: 5,\r\n        };\r\n    },\r\n    watch: {\r\n        color() {\r\n            this.renderColor();\r\n        },\r\n    },\r\n    mounted() {\r\n        this.renderColor();\r\n    },\r\n    methods: {\r\n        renderColor() {\r\n            const canvas = this.$el;\r\n            const width = this.width;\r\n            const height = this.height;\r\n            const size = this.alphaSize;\r\n            const canvasSquare = createAlphaSquare(size);\r\n            const ctx = canvas.getContext('2d');\r\n            canvas.width = width;\r\n            canvas.height = height;\r\n            ctx.fillStyle = ctx.createPattern(canvasSquare, 'repeat');\r\n            ctx.fillRect(0, 0, width, height);\r\n            ctx.fillStyle = this.color;\r\n            ctx.fillRect(0, 0, width, height);\r\n        },\r\n    },\r\n});\n\nfunction render$3(_ctx, _cache, $props, $setup, $data, $options) {\n  return (openBlock(), createBlock(\"canvas\"))\n}\n\nscript$3.render = render$3;\nscript$3.__file = \"src/color/Preview.vue\";\n\n// import imgSucker from '../img/sucker.png'\r\nvar script$4 = defineComponent({\r\n    props: {\r\n        suckerCanvas: {\r\n            type: Object,\r\n            default: null,\r\n        },\r\n        suckerArea: {\r\n            type: Array,\r\n            default: () => [],\r\n        },\r\n    },\r\n    data() {\r\n        return {\r\n            isOpenSucker: false,\r\n            suckerPreview: null,\r\n            isSucking: false,\r\n        };\r\n    },\r\n    watch: {\r\n        suckerCanvas(newVal) {\r\n            this.isSucking = false;\r\n            this.suckColor(newVal);\r\n            // newVal.style.cursor = `url('../img/sucker.png') 0 32, default`\r\n            //TODO\r\n        },\r\n    },\r\n    methods: {\r\n        openSucker() {\r\n            if (!this.isOpenSucker) {\r\n                this.isOpenSucker = true;\r\n                this.isSucking = true;\r\n                this.$emit('openSucker', true);\r\n                document.addEventListener('keydown', this.keydownHandler);\r\n            }\r\n            else {\r\n                // The processing logic is the same as pressing the esc key\r\n                this.keydownHandler({ keyCode: 27 });\r\n            }\r\n        },\r\n        keydownHandler(e) {\r\n            // esc\r\n            if (e.keyCode === 27) {\r\n                this.isOpenSucker = false;\r\n                this.isSucking = false;\r\n                this.$emit('openSucker', false);\r\n                document.removeEventListener('keydown', this.keydownHandler);\r\n                document.removeEventListener('mousemove', this.mousemoveHandler);\r\n                document.removeEventListener('mouseup', this.mousemoveHandler);\r\n                if (this.suckerPreview) {\r\n                    // @ts-ignore\r\n                    document.body.removeChild(this.suckerPreview);\r\n                    this.suckerPreview = null;\r\n                }\r\n            }\r\n        },\r\n        mousemoveHandler(e) {\r\n            const { clientX, clientY } = e;\r\n            const { top: domTop, left: domLeft, width, height, } = this.suckerCanvas.getBoundingClientRect();\r\n            const x = clientX - domLeft;\r\n            const y = clientY - domTop;\r\n            const ctx = this.suckerCanvas.getContext('2d');\r\n            const imgData = ctx.getImageData(Math.min(x, width - 1), Math.min(y, height - 1), 1, 1);\r\n            let [r, g, b, a] = imgData.data;\r\n            a = parseFloat((a / 255).toFixed(2));\r\n            // @ts-ignore\r\n            const style = this.suckerPreview.style;\r\n            Object.assign(style, {\r\n                position: 'absolute',\r\n                left: clientX + 20 + 'px',\r\n                top: clientY - 36 + 'px',\r\n                width: '24px',\r\n                height: '24px',\r\n                borderRadius: '50%',\r\n                border: '2px solid #fff',\r\n                boxShadow: '0 0 8px 0 rgba(0, 0, 0, 0.16)',\r\n                background: `rgba(${r}, ${g}, ${b}, ${a})`,\r\n                zIndex: 95,\r\n            });\r\n            if (this.suckerArea.length &&\r\n                // @ts-ignore\r\n                clientX >= this.suckerArea[0] &&\r\n                // @ts-ignore\r\n                clientY >= this.suckerArea[1] &&\r\n                // @ts-ignore\r\n                clientX <= this.suckerArea[2] &&\r\n                // @ts-ignore\r\n                clientY <= this.suckerArea[3]) {\r\n                // @ts-ignore\r\n                style.display = '';\r\n            }\r\n            else {\r\n                // @ts-ignore\r\n                style.display = 'none';\r\n            }\r\n        },\r\n        suckColor(dom) {\r\n            if (dom && dom.tagName !== 'CANVAS') {\r\n                return;\r\n            }\r\n            // @ts-ignore\r\n            this.suckerPreview = document.createElement('div');\r\n            // @ts-ignore\r\n            if (this.suckerPreview)\r\n                document.body.appendChild(this.suckerPreview);\r\n            document.addEventListener('mousemove', this.mousemoveHandler);\r\n            document.addEventListener('mouseup', this.mousemoveHandler);\r\n            dom.addEventListener('click', (e) => {\r\n                const { clientX, clientY } = e;\r\n                const { top, left, width, height } = dom.getBoundingClientRect();\r\n                const x = clientX - left;\r\n                const y = clientY - top;\r\n                const ctx = dom.getContext('2d');\r\n                const imgData = ctx.getImageData(Math.min(x, width - 1), Math.min(y, height - 1), 1, 1);\r\n                let [r, g, b, a] = imgData.data;\r\n                a = parseFloat((a / 255).toFixed(2));\r\n                this.$emit('selectSucker', { r, g, b, a });\r\n            });\r\n        },\r\n    },\r\n});\n\nconst _hoisted_1$3 = /*#__PURE__*/createVNode(\"path\", { d: \"M13.1,8.2l5.6,5.6c0.4,0.4,0.5,1.1,0.1,1.5s-1.1,0.5-1.5,0.1c0,0-0.1,0-0.1-0.1l-1.4-1.4l-7.7,7.7C7.9,21.9,7.6,22,7.3,22H3.1C2.5,22,2,21.5,2,20.9l0,0v-4.2c0-0.3,0.1-0.6,0.3-0.8l5.8-5.8C8.5,9.7,9.2,9.6,9.7,10s0.5,1.1,0.1,1.5c0,0,0,0.1-0.1,0.1l-5.5,5.5v2.7h2.7l7.4-7.4L8.7,6.8c-0.5-0.4-0.5-1-0.1-1.5s1.1-0.5,1.5-0.1c0,0,0.1,0,0.1,0.1l1.4,1.4l3.5-3.5c1.6-1.6,4.1-1.6,5.8-0.1c1.6,1.6,1.6,4.1,0.1,5.8L20.9,9l-3.6,3.6c-0.4,0.4-1.1,0.5-1.5,0.1\" }, null, -1 /* HOISTED */);\nconst _hoisted_2 = {\n  key: 1,\n  class: \"sucker\",\n  viewBox: \"-16 -16 68 68\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  stroke: \"#9099a4\"\n};\nconst _hoisted_3 = /*#__PURE__*/createVNode(\"g\", {\n  fill: \"none\",\n  \"fill-rule\": \"evenodd\"\n}, [\n  /*#__PURE__*/createVNode(\"g\", {\n    transform: \"translate(1 1)\",\n    \"stroke-width\": \"4\"\n  }, [\n    /*#__PURE__*/createVNode(\"circle\", {\n      \"stroke-opacity\": \".5\",\n      cx: \"18\",\n      cy: \"18\",\n      r: \"18\"\n    }),\n    /*#__PURE__*/createVNode(\"path\", { d: \"M36 18c0-9.94-8.06-18-18-18\" }, [\n      /*#__PURE__*/createVNode(\"animateTransform\", {\n        attributeName: \"transform\",\n        type: \"rotate\",\n        from: \"0 18 18\",\n        to: \"360 18 18\",\n        dur: \"1s\",\n        repeatCount: \"indefinite\"\n      })\n    ])\n  ])\n], -1 /* HOISTED */);\n\nfunction render$4(_ctx, _cache, $props, $setup, $data, $options) {\n  return (openBlock(), createBlock(\"div\", null, [\n    (!_ctx.isSucking)\n      ? (openBlock(), createBlock(\"svg\", {\n          key: 0,\n          class: [{ active: _ctx.isOpenSucker }, \"sucker\"],\n          xmlns: \"http://www.w3.org/2000/svg\",\n          viewBox: \"-12 -12 48 48\",\n          onClick: _cache[1] || (_cache[1] = (...args) => (_ctx.openSucker && _ctx.openSucker(...args)))\n        }, [\n          _hoisted_1$3\n        ], 2 /* CLASS */))\n      : createCommentVNode(\"v-if\", true),\n    (_ctx.isSucking)\n      ? (openBlock(), createBlock(\"svg\", _hoisted_2, [\n          _hoisted_3\n        ]))\n      : createCommentVNode(\"v-if\", true)\n  ]))\n}\n\nscript$4.render = render$4;\nscript$4.__file = \"src/color/Sucker.vue\";\n\nvar script$5 = defineComponent({\r\n    props: {\r\n        name: {\r\n            type: String,\r\n            default: '',\r\n        },\r\n        color: {\r\n            type: String,\r\n            default: '',\r\n        },\r\n    },\r\n    emits: ['inputColor', 'inputFocus', 'inputBlur'],\r\n    setup(props, { emit }) {\r\n        const modelColor = computed({\r\n            get() {\r\n                return props.color || '';\r\n            },\r\n            set(val) {\r\n                emit('inputColor', val);\r\n            },\r\n        });\r\n        // Functions for handling focus events\r\n        const handleFocus = (event) => {\r\n            emit('inputFocus', event);\r\n        };\r\n        const handleBlur = (event) => {\r\n            emit('inputBlur', event);\r\n        };\r\n        return {\r\n            modelColor,\r\n            handleFocus,\r\n            handleBlur,\r\n        };\r\n    },\r\n});\n\nconst _hoisted_1$4 = { class: \"color-type\" };\nconst _hoisted_2$1 = { class: \"name\" };\n\nfunction render$5(_ctx, _cache, $props, $setup, $data, $options) {\n  return (openBlock(), createBlock(\"div\", _hoisted_1$4, [\n    createVNode(\"span\", _hoisted_2$1, toDisplayString(_ctx.name), 1 /* TEXT */),\n    withDirectives(createVNode(\"input\", {\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => (_ctx.modelColor = $event)),\n      class: \"value\",\n      onFocus: _cache[2] || (_cache[2] = (...args) => (_ctx.handleFocus && _ctx.handleFocus(...args))),\n      onBlur: _cache[3] || (_cache[3] = (...args) => (_ctx.handleBlur && _ctx.handleBlur(...args)))\n    }, null, 544 /* HYDRATE_EVENTS, NEED_PATCH */), [\n      [vModelText, _ctx.modelColor]\n    ])\n  ]))\n}\n\nscript$5.render = render$5;\nscript$5.__file = \"src/color/Box.vue\";\n\nvar script$6 = defineComponent({\r\n    name: 'ColorPicker',\r\n    props: {\r\n        color: {\r\n            type: String,\r\n            default: '#000000',\r\n        },\r\n        colorsDefault: {\r\n            type: Array,\r\n            default: () => [],\r\n        },\r\n        colorsHistoryKey: {\r\n            type: String,\r\n            default: '',\r\n        },\r\n    },\r\n    emits: ['selectColor'],\r\n    setup(props, { emit }) {\r\n        const color = ref();\r\n        const colorsHistory = ref([]);\r\n        const imgAlphaBase64 = ref();\r\n        if (props.colorsHistoryKey && localStorage) {\r\n            colorsHistory.value =\r\n                JSON.parse(localStorage.getItem(props.colorsHistoryKey)) || [];\r\n        }\r\n        imgAlphaBase64.value = createAlphaSquare(4).toDataURL();\r\n        onUnmounted(() => {\r\n            setColorsHistory(color.value);\r\n        });\r\n        function setColorsHistory(color) {\r\n            if (!color) {\r\n                return;\r\n            }\r\n            const colors = colorsHistory.value || [];\r\n            // @ts-ignore\r\n            const index = colors.indexOf(color);\r\n            if (index >= 0) {\r\n                colors.splice(index, 1);\r\n            }\r\n            if (colors.length >= 8) {\r\n                colors.length = 7;\r\n            }\r\n            // @ts-ignore\r\n            colors.unshift(color);\r\n            colorsHistory.value = colors || [];\r\n            if (localStorage && props.colorsHistoryKey)\r\n                localStorage.setItem(props.colorsHistoryKey, JSON.stringify(colors));\r\n        }\r\n        function selectColor(color) {\r\n            emit('selectColor', color);\r\n        }\r\n        return {\r\n            setColorsHistory,\r\n            colorsHistory,\r\n            color,\r\n            imgAlphaBase64,\r\n            selectColor,\r\n        };\r\n    },\r\n});\n\nconst _hoisted_1$5 = { class: \"colors\" };\nconst _hoisted_2$2 = {\n  key: 0,\n  class: \"colors history\"\n};\n\nfunction render$6(_ctx, _cache, $props, $setup, $data, $options) {\n  return (openBlock(), createBlock(\"div\", null, [\n    createVNode(\"ul\", _hoisted_1$5, [\n      (openBlock(true), createBlock(Fragment, null, renderList(_ctx.colorsDefault, (item) => {\n        return (openBlock(), createBlock(\"li\", {\n          key: item,\n          class: \"item\",\n          onClick: $event => (_ctx.selectColor(item))\n        }, [\n          createVNode(\"div\", {\n            style: { background: `url(${_ctx.imgAlphaBase64})` },\n            class: \"alpha\"\n          }, null, 4 /* STYLE */),\n          createVNode(\"div\", {\n            style: { background: item },\n            class: \"color\"\n          }, null, 4 /* STYLE */)\n        ], 8 /* PROPS */, [\"onClick\"]))\n      }), 128 /* KEYED_FRAGMENT */))\n    ]),\n    (_ctx.colorsHistory.length)\n      ? (openBlock(), createBlock(\"ul\", _hoisted_2$2, [\n          (openBlock(true), createBlock(Fragment, null, renderList(_ctx.colorsHistory, (item) => {\n            return (openBlock(), createBlock(\"li\", {\n              key: item,\n              class: \"item\",\n              onClick: $event => (_ctx.selectColor(item))\n            }, [\n              createVNode(\"div\", {\n                style: { background: `url(${_ctx.imgAlphaBase64})` },\n                class: \"alpha\"\n              }, null, 4 /* STYLE */),\n              createVNode(\"div\", {\n                style: { background: item },\n                class: \"color\"\n              }, null, 4 /* STYLE */)\n            ], 8 /* PROPS */, [\"onClick\"]))\n          }), 128 /* KEYED_FRAGMENT */))\n        ]))\n      : createCommentVNode(\"v-if\", true)\n  ]))\n}\n\nscript$6.render = render$6;\nscript$6.__file = \"src/color/Colors.vue\";\n\nvar script$7 = defineComponent({\r\n    components: {\r\n        Saturation: script,\r\n        Hue: script$1,\r\n        Alpha: script$2,\r\n        Preview: script$3,\r\n        Sucker: script$4,\r\n        Box: script$5,\r\n        Colors: script$6,\r\n    },\r\n    emits: ['changeColor', 'openSucker', 'inputFocus', 'inputBlur'],\r\n    props: {\r\n        color: {\r\n            type: String,\r\n            default: '#000000',\r\n        },\r\n        theme: {\r\n            type: String,\r\n            default: 'dark',\r\n        },\r\n        suckerHide: {\r\n            type: Boolean,\r\n            default: true,\r\n        },\r\n        suckerCanvas: {\r\n            type: null,\r\n            default: null,\r\n        },\r\n        suckerArea: {\r\n            type: Array,\r\n            default: () => [],\r\n        },\r\n        colorsDefault: {\r\n            type: Array,\r\n            default: () => [\r\n                '#000000',\r\n                '#FFFFFF',\r\n                '#FF1900',\r\n                '#F47365',\r\n                '#FFB243',\r\n                '#FFE623',\r\n                '#6EFF2A',\r\n                '#1BC7B1',\r\n                '#00BEFF',\r\n                '#2E81FF',\r\n                '#5D61FF',\r\n                '#FF89CF',\r\n                '#FC3CAD',\r\n                '#BF3DCE',\r\n                '#8E00A7',\r\n                'rgba(0,0,0,0)',\r\n            ],\r\n        },\r\n        colorsHistoryKey: {\r\n            type: String,\r\n            default: 'vue-colorpicker-history',\r\n        },\r\n    },\r\n    data() {\r\n        return {\r\n            hueWidth: 15,\r\n            hueHeight: 152,\r\n            previewHeight: 30,\r\n            modelRgba: '',\r\n            modelHex: '',\r\n            r: 0,\r\n            g: 0,\r\n            b: 0,\r\n            a: 1,\r\n            h: 0,\r\n            s: 0,\r\n            v: 0,\r\n        };\r\n    },\r\n    computed: {\r\n        isLightTheme() {\r\n            return this.theme === 'light';\r\n        },\r\n        totalWidth() {\r\n            return this.hueHeight + (this.hueWidth + 8) * 2;\r\n        },\r\n        previewWidth() {\r\n            return this.totalWidth - (this.suckerHide ? 0 : this.previewHeight);\r\n        },\r\n        rgba() {\r\n            return {\r\n                r: this.r,\r\n                g: this.g,\r\n                b: this.b,\r\n                a: this.a,\r\n            };\r\n        },\r\n        hsv() {\r\n            return {\r\n                h: this.h,\r\n                s: this.s,\r\n                v: this.v,\r\n            };\r\n        },\r\n        rgbString() {\r\n            return `rgb(${this.r}, ${this.g}, ${this.b})`;\r\n        },\r\n        rgbaStringShort() {\r\n            return `${this.r}, ${this.g}, ${this.b}, ${this.a}`;\r\n        },\r\n        rgbaString() {\r\n            return `rgba(${this.rgbaStringShort})`;\r\n        },\r\n        hexString() {\r\n            return rgb2hex(this.rgba, true);\r\n        },\r\n    },\r\n    created() {\r\n        Object.assign(this, setColorValue(this.color));\r\n        this.setText();\r\n        this.$watch('rgba', () => {\r\n            this.$emit('changeColor', {\r\n                rgba: this.rgba,\r\n                hsv: this.hsv,\r\n                hex: this.modelHex,\r\n            });\r\n        });\r\n    },\r\n    methods: {\r\n        selectSaturation(color) {\r\n            const { r, g, b, h, s, v } = setColorValue(color);\r\n            Object.assign(this, { r, g, b, h, s, v });\r\n            this.setText();\r\n        },\r\n        handleFocus(event) {\r\n            this.$emit('inputFocus', event);\r\n        },\r\n        handleBlur(event) {\r\n            this.$emit('inputBlur', event);\r\n        },\r\n        selectHue(color) {\r\n            const { r, g, b, h, s, v } = setColorValue(color);\r\n            Object.assign(this, { r, g, b, h, s, v });\r\n            this.setText();\r\n            this.$nextTick(() => {\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderColor();\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderSlide();\r\n            });\r\n        },\r\n        selectAlpha(a) {\r\n            this.a = a;\r\n            this.setText();\r\n        },\r\n        inputHex(color) {\r\n            const { r, g, b, a, h, s, v } = setColorValue(color);\r\n            Object.assign(this, { r, g, b, a, h, s, v });\r\n            this.modelHex = color;\r\n            this.modelRgba = this.rgbaStringShort;\r\n            this.$nextTick(() => {\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderColor();\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderSlide();\r\n                // @ts-ignore\r\n                this.$refs.hue.renderSlide();\r\n            });\r\n        },\r\n        inputRgba(color) {\r\n            const { r, g, b, a, h, s, v } = setColorValue(color);\r\n            Object.assign(this, { r, g, b, a, h, s, v });\r\n            this.modelHex = this.hexString;\r\n            this.modelRgba = color;\r\n            this.$nextTick(() => {\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderColor();\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderSlide();\r\n                // @ts-ignore\r\n                this.$refs.hue.renderSlide();\r\n            });\r\n        },\r\n        setText() {\r\n            this.modelHex = this.hexString;\r\n            this.modelRgba = this.rgbaStringShort;\r\n        },\r\n        openSucker(isOpen) {\r\n            this.$emit('openSucker', isOpen);\r\n        },\r\n        selectSucker(color) {\r\n            const { r, g, b, a, h, s, v } = setColorValue(color);\r\n            Object.assign(this, { r, g, b, a, h, s, v });\r\n            this.setText();\r\n            this.$nextTick(() => {\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderColor();\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderSlide();\r\n                // @ts-ignore\r\n                this.$refs.hue.renderSlide();\r\n            });\r\n        },\r\n        selectColor(color) {\r\n            const { r, g, b, a, h, s, v } = setColorValue(color);\r\n            Object.assign(this, { r, g, b, a, h, s, v });\r\n            this.setText();\r\n            this.$nextTick(() => {\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderColor();\r\n                // @ts-ignore\r\n                this.$refs.saturation.renderSlide();\r\n                // @ts-ignore\r\n                this.$refs.hue.renderSlide();\r\n            });\r\n        },\r\n    },\r\n});\n\nconst _hoisted_1$6 = { class: \"color-set\" };\n\nfunction render$7(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Saturation = resolveComponent(\"Saturation\");\n  const _component_Hue = resolveComponent(\"Hue\");\n  const _component_Alpha = resolveComponent(\"Alpha\");\n  const _component_Preview = resolveComponent(\"Preview\");\n  const _component_Sucker = resolveComponent(\"Sucker\");\n  const _component_Box = resolveComponent(\"Box\");\n  const _component_Colors = resolveComponent(\"Colors\");\n\n  return (openBlock(), createBlock(\"div\", {\n    class: [\"hu-color-picker\", { light: _ctx.isLightTheme }],\n    style: { width: _ctx.totalWidth + 'px' }\n  }, [\n    createVNode(\"div\", _hoisted_1$6, [\n      createVNode(_component_Saturation, {\n        ref: \"saturation\",\n        color: _ctx.rgbString,\n        hsv: _ctx.hsv,\n        size: _ctx.hueHeight,\n        onSelectSaturation: _ctx.selectSaturation\n      }, null, 8 /* PROPS */, [\"color\", \"hsv\", \"size\", \"onSelectSaturation\"]),\n      createVNode(_component_Hue, {\n        ref: \"hue\",\n        hsv: _ctx.hsv,\n        width: _ctx.hueWidth,\n        height: _ctx.hueHeight,\n        onSelectHue: _ctx.selectHue\n      }, null, 8 /* PROPS */, [\"hsv\", \"width\", \"height\", \"onSelectHue\"]),\n      createVNode(_component_Alpha, {\n        ref: \"alpha\",\n        color: _ctx.rgbString,\n        rgba: _ctx.rgba,\n        width: _ctx.hueWidth,\n        height: _ctx.hueHeight,\n        onSelectAlpha: _ctx.selectAlpha\n      }, null, 8 /* PROPS */, [\"color\", \"rgba\", \"width\", \"height\", \"onSelectAlpha\"])\n    ]),\n    createVNode(\"div\", {\n      style: { height: _ctx.previewHeight + 'px' },\n      class: \"color-show\"\n    }, [\n      createVNode(_component_Preview, {\n        color: _ctx.rgbaString,\n        width: _ctx.previewWidth,\n        height: _ctx.previewHeight\n      }, null, 8 /* PROPS */, [\"color\", \"width\", \"height\"]),\n      (!_ctx.suckerHide)\n        ? (openBlock(), createBlock(_component_Sucker, {\n            key: 0,\n            \"sucker-canvas\": _ctx.suckerCanvas,\n            \"sucker-area\": _ctx.suckerArea,\n            onOpenSucker: _ctx.openSucker,\n            onSelectSucker: _ctx.selectSucker\n          }, null, 8 /* PROPS */, [\"sucker-canvas\", \"sucker-area\", \"onOpenSucker\", \"onSelectSucker\"]))\n        : createCommentVNode(\"v-if\", true)\n    ], 4 /* STYLE */),\n    createVNode(_component_Box, {\n      name: \"HEX\",\n      color: _ctx.modelHex,\n      onInputColor: _ctx.inputHex,\n      onInputFocus: _ctx.handleFocus,\n      onInputBlur: _ctx.handleBlur\n    }, null, 8 /* PROPS */, [\"color\", \"onInputColor\", \"onInputFocus\", \"onInputBlur\"]),\n    createVNode(_component_Box, {\n      name: \"RGBA\",\n      color: _ctx.modelRgba,\n      onInputColor: _ctx.inputRgba,\n      onInputFocus: _ctx.handleFocus,\n      onInputBlur: _ctx.handleBlur\n    }, null, 8 /* PROPS */, [\"color\", \"onInputColor\", \"onInputFocus\", \"onInputBlur\"]),\n    createVNode(_component_Colors, {\n      color: _ctx.rgbaString,\n      \"colors-default\": _ctx.colorsDefault,\n      \"colors-history-key\": _ctx.colorsHistoryKey,\n      onSelectColor: _ctx.selectColor\n    }, null, 8 /* PROPS */, [\"color\", \"colors-default\", \"colors-history-key\", \"onSelectColor\"]),\n    createCommentVNode(\" custom options \"),\n    renderSlot(_ctx.$slots, \"default\")\n  ], 6 /* CLASS, STYLE */))\n}\n\nscript$7.render = render$7;\nscript$7.__file = \"src/color/ColorPicker.vue\";\n\nscript$7.install = (Vue) => {\r\n    Vue.component(script$7.name, script$7);\r\n};\n\nfunction install(Vue) {\r\n    Vue.component(script$7.name, script$7);\r\n}\r\nvar index = { install };\n\nexport default index;\nexport { script$7 as ColorPicker };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,cAAc,OAAO;AAC1B,MAAI,OAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AACpC,MAAI,IAAI,KAAK,KAAK,GAAG;AACjB,WAAO,QAAQ,KAAK;AAAA,EACxB,WACS,MAAM,KAAK,KAAK,GAAG;AACxB,WAAO,SAAS,KAAK;AAAA,EACzB,WACS,OAAO,UAAU,UAAU;AAChC,WAAO,SAAS,QAAQ,KAAK,GAAG;AAAA,EACpC,WACS,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,mBAAmB;AAClE,WAAO;AAAA,EACX;AACA,QAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,QAAM,EAAE,GAAG,GAAG,EAAE,IAAI,QAAQ,IAAI;AAChC,SAAO,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,SAAY,IAAI,GAAG,GAAG,GAAG,EAAE;AAC1D;AACA,SAAS,kBAAkB,MAAM;AAC7B,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,QAAM,aAAa,OAAO;AAC1B,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,MAAI,YAAY;AAChB,MAAI,SAAS,GAAG,GAAG,YAAY,UAAU;AACzC,MAAI,YAAY;AAChB,MAAI,SAAS,GAAG,GAAG,MAAM,IAAI;AAC7B,MAAI,SAAS,MAAM,MAAM,MAAM,IAAI;AACnC,SAAO;AACX;AACA,SAAS,qBAAqB,WAAW,KAAK,OAAO,QAAQ,QAAQ,QAAQ;AAEzE,QAAM,MAAM,cAAc;AAC1B,QAAM,WAAW,IAAI,qBAAqB,GAAG,GAAG,MAAM,QAAQ,GAAG,MAAM,IAAI,MAAM;AACjF,WAAS,aAAa,MAAM,MAAM;AAClC,WAAS,aAAa,MAAM,MAAM;AAClC,MAAI,YAAY;AAChB,MAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AACpC;AACA,SAAS,QAAQ,EAAE,GAAG,GAAG,EAAE,GAAG,SAAS;AACnC,QAAM,SAAS,CAAC,SAAS,MAAM,OAAO,GAAG,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AACjE,QAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AACnD,SAAO,UAAU,MAAM,YAAY,IAAI;AAC3C;AACA,SAAS,QAAQ,KAAK;AAClB,QAAM,IAAI,MAAM,CAAC;AACjB,QAAM,SAAS,CAAC,QAAQ,SAAS,KAAK,EAAE,KAAK;AAC7C,SAAO;AAAA,IACH,GAAG,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,IACzB,GAAG,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,IACzB,GAAG,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,EAC7B;AACJ;AACA,SAAS,SAAS,MAAM;AACpB,MAAI,OAAO,SAAS,UAAU;AAC1B,YAAQ,iBAAiB,KAAK,IAAI,KAAK,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE,MAAM,GAAG;AACpE,WAAO;AAAA,MACH,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK;AAAA,MACtB,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK;AAAA,MACtB,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK;AAAA,MACtB,GAAG,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC;AAAA,IACnC;AAAA,EACJ,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,QAAQ,EAAE,GAAG,GAAG,EAAE,GAAG;AAC1B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,QAAQ,MAAM;AACpB,MAAI,IAAI;AACR,MAAI,QAAQ,KAAK;AACb,QAAI;AAAA,EACR,WACS,QAAQ,GAAG;AAChB,QAAI,KAAK,GAAG;AACR,UAAK,MAAM,IAAI,KAAM;AAAA,IACzB,OACK;AACD,UAAK,MAAM,IAAI,KAAM,QAAQ;AAAA,IACjC;AAAA,EACJ,WACS,QAAQ,GAAG;AAChB,QAAK,MAAM,IAAI,KAAM,QAAQ;AAAA,EACjC,WACS,QAAQ,GAAG;AAChB,QAAK,MAAM,IAAI,KAAM,QAAQ;AAAA,EACjC;AACA,MAAI,KAAK,MAAM,CAAC;AAChB,MAAI,IAAI,YAAY,QAAQ,IAAI,IAAI,IAAI,MAAM,KAAK,QAAQ,CAAC,CAAC;AAC7D,MAAI,IAAI,WAAW,IAAI,QAAQ,CAAC,CAAC;AACjC,SAAO,EAAE,GAAG,GAAG,EAAE;AACrB;AAEA,IAAI,SAAS,gBAAgB;AAAA,EACzB,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO,CAAC,kBAAkB;AAAA,EAC1B,OAAO;AACH,WAAO;AAAA,MACH,sBAAsB,CAAC;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACN,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,SAAS;AAAA,IACL,cAAc;AACV,YAAM,SAAS,KAAK,MAAM;AAC1B,YAAM,OAAO,KAAK;AAClB,YAAM,MAAM,OAAO,WAAW,IAAI;AAClC,aAAO,QAAQ;AACf,aAAO,SAAS;AAChB,UAAI,YAAY,KAAK;AACrB,UAAI,SAAS,GAAG,GAAG,MAAM,IAAI;AAC7B,2BAAqB,KAAK,KAAK,MAAM,MAAM,WAAW,qBAAqB;AAC3E,2BAAqB,KAAK,KAAK,MAAM,MAAM,iBAAiB,SAAS;AAAA,IACzE;AAAA,IACA,cAAc;AACV,WAAK,uBAAuB;AAAA,QACxB,MAAM,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI;AAAA,QACnC,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI;AAAA,MAC5C;AAAA,IACJ;AAAA,IACA,iBAAiB,GAAG;AAChB,YAAM,EAAE,KAAK,eAAe,MAAM,eAAgB,IAAI,KAAK,IAAI,sBAAsB;AACrF,YAAM,MAAM,EAAE,OAAO,WAAW,IAAI;AACpC,YAAM,YAAY,CAACA,OAAM;AACrB,YAAI,IAAIA,GAAE,UAAU;AACpB,YAAI,IAAIA,GAAE,UAAU;AACpB,YAAI,IAAI,GAAG;AACP,cAAI;AAAA,QACR;AACA,YAAI,IAAI,GAAG;AACP,cAAI;AAAA,QACR;AACA,YAAI,IAAI,KAAK,MAAM;AACf,cAAI,KAAK;AAAA,QACb;AACA,YAAI,IAAI,KAAK,MAAM;AACf,cAAI,KAAK;AAAA,QACb;AAEA,aAAK,uBAAuB;AAAA,UACxB,MAAM,IAAI,IAAI;AAAA,UACd,KAAK,IAAI,IAAI;AAAA,QACjB;AAEA,cAAM,UAAU,IAAI,aAAa,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC,GAAG,GAAG,CAAC;AAC7F,cAAM,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ;AAC1B,aAAK,MAAM,oBAAoB,EAAE,GAAG,GAAG,EAAE,CAAC;AAAA,MAC9C;AACA,gBAAU,CAAC;AACX,YAAM,UAAU,MAAM;AAClB,iBAAS,oBAAoB,aAAa,SAAS;AACnD,iBAAS,oBAAoB,WAAW,OAAO;AAAA,MACnD;AACA,eAAS,iBAAiB,aAAa,SAAS;AAChD,eAAS,iBAAiB,WAAW,OAAO;AAAA,IAChD;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,aAAa,EAAE,KAAK,mBAAmB;AAE7C,SAAS,OAAO,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC7D,SAAQ,UAAU,GAAG;AAAA,IAAY;AAAA,IAAO;AAAA,MACtC,OAAO;AAAA,MACP,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAU,KAAK,oBAAoB,KAAK,iBAAiB,GAAG,IAAI,GAAI,CAAC,WAAU,MAAM,CAAC;AAAA,IACjJ;AAAA,IAAG;AAAA,MACD;AAAA,QAAY;AAAA,QAAU;AAAA,QAAY;AAAA,QAAM;AAAA;AAAA,MAAoB;AAAA,MAC5D;AAAA,QAAY;AAAA,QAAO;AAAA,UACjB,OAAO,KAAK;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QAAG;AAAA,QAAM;AAAA;AAAA,MAAa;AAAA,IACxB;AAAA,IAAG;AAAA;AAAA,EAAuB;AAC5B;AAEA,OAAO,SAAS;AAChB,OAAO,SAAS;AAEhB,IAAI,WAAW,gBAAgB;AAAA,EAC3B,OAAO;AAAA,IACH,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO,CAAC,WAAW;AAAA,EACnB,OAAO;AACH,WAAO;AAAA,MACH,eAAe,CAAC;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,SAAS;AAAA,IACL,cAAc;AACV,YAAM,SAAS,KAAK,MAAM;AAC1B,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,KAAK;AACpB,YAAM,MAAM,OAAO,WAAW,IAAI;AAClC,aAAO,QAAQ;AACf,aAAO,SAAS;AAChB,YAAM,WAAW,IAAI,qBAAqB,GAAG,GAAG,GAAG,MAAM;AACzD,eAAS,aAAa,GAAG,SAAS;AAClC,eAAS,aAAa,OAAO,GAAG,SAAS;AACzC,eAAS,aAAa,OAAO,GAAG,SAAS;AACzC,eAAS,aAAa,OAAO,GAAG,SAAS;AACzC,eAAS,aAAa,OAAO,GAAG,SAAS;AACzC,eAAS,aAAa,OAAO,GAAG,SAAS;AACzC,eAAS,aAAa,GAAG,SAAS;AAClC,UAAI,YAAY;AAChB,UAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAAA,IACpC;AAAA,IACA,cAAc;AACV,WAAK,gBAAgB;AAAA,QACjB,MAAM,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,IAAI;AAAA,MACpD;AAAA,IACJ;AAAA,IACA,UAAU,GAAG;AACT,YAAM,EAAE,KAAK,OAAO,IAAI,KAAK,IAAI,sBAAsB;AACvD,YAAM,MAAM,EAAE,OAAO,WAAW,IAAI;AACpC,YAAM,YAAY,CAACA,OAAM;AACrB,YAAI,IAAIA,GAAE,UAAU;AACpB,YAAI,IAAI,GAAG;AACP,cAAI;AAAA,QACR;AACA,YAAI,IAAI,KAAK,QAAQ;AACjB,cAAI,KAAK;AAAA,QACb;AACA,aAAK,gBAAgB;AAAA,UACjB,KAAK,IAAI,IAAI;AAAA,QACjB;AAEA,cAAM,UAAU,IAAI,aAAa,GAAG,KAAK,IAAI,GAAG,KAAK,SAAS,CAAC,GAAG,GAAG,CAAC;AACtE,cAAM,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ;AAC1B,aAAK,MAAM,aAAa,EAAE,GAAG,GAAG,EAAE,CAAC;AAAA,MACvC;AACA,gBAAU,CAAC;AACX,YAAM,UAAU,MAAM;AAClB,iBAAS,oBAAoB,aAAa,SAAS;AACnD,iBAAS,oBAAoB,WAAW,OAAO;AAAA,MACnD;AACA,eAAS,iBAAiB,aAAa,SAAS;AAChD,eAAS,iBAAiB,WAAW,OAAO;AAAA,IAChD;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAAe,EAAE,KAAK,YAAY;AAExC,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAQ,UAAU,GAAG;AAAA,IAAY;AAAA,IAAO;AAAA,MACtC,OAAO;AAAA,MACP,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAU,KAAK,aAAa,KAAK,UAAU,GAAG,IAAI,GAAI,CAAC,WAAU,MAAM,CAAC;AAAA,IACnI;AAAA,IAAG;AAAA,MACD;AAAA,QAAY;AAAA,QAAU;AAAA,QAAc;AAAA,QAAM;AAAA;AAAA,MAAoB;AAAA,MAC9D;AAAA,QAAY;AAAA,QAAO;AAAA,UACjB,OAAO,KAAK;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QAAG;AAAA,QAAM;AAAA;AAAA,MAAa;AAAA,IACxB;AAAA,IAAG;AAAA;AAAA,EAAuB;AAC5B;AAEA,SAAS,SAAS;AAClB,SAAS,SAAS;AAElB,IAAI,WAAW,gBAAgB;AAAA,EAC3B,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO,CAAC,aAAa;AAAA,EACrB,OAAO;AACH,WAAO;AAAA,MACH,iBAAiB,CAAC;AAAA,MAClB,WAAW;AAAA,IACf;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH,QAAQ;AACJ,WAAK,YAAY;AAAA,IACrB;AAAA,IACA,WAAW;AACP,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,SAAS;AAAA,IACL,cAAc;AACV,YAAM,SAAS,KAAK,MAAM;AAC1B,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,KAAK;AACpB,YAAM,OAAO,KAAK;AAClB,YAAM,eAAe,kBAAkB,IAAI;AAC3C,YAAM,MAAM,OAAO,WAAW,IAAI;AAClC,aAAO,QAAQ;AACf,aAAO,SAAS;AAChB,UAAI,YAAY,IAAI,cAAc,cAAc,QAAQ;AACxD,UAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAChC,2BAAqB,KAAK,KAAK,OAAO,QAAQ,uBAAuB,KAAK,KAAK;AAAA,IACnF;AAAA,IACA,cAAc;AACV,WAAK,kBAAkB;AAAA,QACnB,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,YAAY,GAAG;AACX,YAAM,EAAE,KAAK,OAAO,IAAI,KAAK,IAAI,sBAAsB;AACvD,YAAM,YAAY,CAACA,OAAM;AACrB,YAAI,IAAIA,GAAE,UAAU;AACpB,YAAI,IAAI,GAAG;AACP,cAAI;AAAA,QACR;AACA,YAAI,IAAI,KAAK,QAAQ;AACjB,cAAI,KAAK;AAAA,QACb;AACA,YAAI,IAAI,YAAY,IAAI,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAC/C,aAAK,MAAM,eAAe,CAAC;AAAA,MAC/B;AACA,gBAAU,CAAC;AACX,YAAM,UAAU,MAAM;AAClB,iBAAS,oBAAoB,aAAa,SAAS;AACnD,iBAAS,oBAAoB,WAAW,OAAO;AAAA,MACnD;AACA,eAAS,iBAAiB,aAAa,SAAS;AAChD,eAAS,iBAAiB,WAAW,OAAO;AAAA,IAChD;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAAe,EAAE,KAAK,cAAc;AAE1C,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAQ,UAAU,GAAG;AAAA,IAAY;AAAA,IAAO;AAAA,MACtC,OAAO;AAAA,MACP,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAU,KAAK,eAAe,KAAK,YAAY,GAAG,IAAI,GAAI,CAAC,WAAU,MAAM,CAAC;AAAA,IACvI;AAAA,IAAG;AAAA,MACD;AAAA,QAAY;AAAA,QAAU;AAAA,QAAc;AAAA,QAAM;AAAA;AAAA,MAAoB;AAAA,MAC9D;AAAA,QAAY;AAAA,QAAO;AAAA,UACjB,OAAO,KAAK;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QAAG;AAAA,QAAM;AAAA;AAAA,MAAa;AAAA,IACxB;AAAA,IAAG;AAAA;AAAA,EAAuB;AAC5B;AAEA,SAAS,SAAS;AAClB,SAAS,SAAS;AAElB,IAAI,WAAW,gBAAgB;AAAA,EAC3B,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO;AACH,WAAO;AAAA,MACH,WAAW;AAAA,IACf;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH,QAAQ;AACJ,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,SAAS;AAAA,IACL,cAAc;AACV,YAAM,SAAS,KAAK;AACpB,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,KAAK;AACpB,YAAM,OAAO,KAAK;AAClB,YAAM,eAAe,kBAAkB,IAAI;AAC3C,YAAM,MAAM,OAAO,WAAW,IAAI;AAClC,aAAO,QAAQ;AACf,aAAO,SAAS;AAChB,UAAI,YAAY,IAAI,cAAc,cAAc,QAAQ;AACxD,UAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAChC,UAAI,YAAY,KAAK;AACrB,UAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAAA,IACpC;AAAA,EACJ;AACJ,CAAC;AAED,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAQ,UAAU,GAAG,YAAY,QAAQ;AAC3C;AAEA,SAAS,SAAS;AAClB,SAAS,SAAS;AAGlB,IAAI,WAAW,gBAAgB;AAAA,EAC3B,OAAO;AAAA,IACH,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,OAAO;AACH,WAAO;AAAA,MACH,cAAc;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA,IACf;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH,aAAa,QAAQ;AACjB,WAAK,YAAY;AACjB,WAAK,UAAU,MAAM;AAAA,IAGzB;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL,aAAa;AACT,UAAI,CAAC,KAAK,cAAc;AACpB,aAAK,eAAe;AACpB,aAAK,YAAY;AACjB,aAAK,MAAM,cAAc,IAAI;AAC7B,iBAAS,iBAAiB,WAAW,KAAK,cAAc;AAAA,MAC5D,OACK;AAED,aAAK,eAAe,EAAE,SAAS,GAAG,CAAC;AAAA,MACvC;AAAA,IACJ;AAAA,IACA,eAAe,GAAG;AAEd,UAAI,EAAE,YAAY,IAAI;AAClB,aAAK,eAAe;AACpB,aAAK,YAAY;AACjB,aAAK,MAAM,cAAc,KAAK;AAC9B,iBAAS,oBAAoB,WAAW,KAAK,cAAc;AAC3D,iBAAS,oBAAoB,aAAa,KAAK,gBAAgB;AAC/D,iBAAS,oBAAoB,WAAW,KAAK,gBAAgB;AAC7D,YAAI,KAAK,eAAe;AAEpB,mBAAS,KAAK,YAAY,KAAK,aAAa;AAC5C,eAAK,gBAAgB;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,iBAAiB,GAAG;AAChB,YAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,YAAM,EAAE,KAAK,QAAQ,MAAM,SAAS,OAAO,OAAQ,IAAI,KAAK,aAAa,sBAAsB;AAC/F,YAAM,IAAI,UAAU;AACpB,YAAM,IAAI,UAAU;AACpB,YAAM,MAAM,KAAK,aAAa,WAAW,IAAI;AAC7C,YAAM,UAAU,IAAI,aAAa,KAAK,IAAI,GAAG,QAAQ,CAAC,GAAG,KAAK,IAAI,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;AACtF,UAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,QAAQ;AAC3B,UAAI,YAAY,IAAI,KAAK,QAAQ,CAAC,CAAC;AAEnC,YAAM,QAAQ,KAAK,cAAc;AACjC,aAAO,OAAO,OAAO;AAAA,QACjB,UAAU;AAAA,QACV,MAAM,UAAU,KAAK;AAAA,QACrB,KAAK,UAAU,KAAK;AAAA,QACpB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,YAAY,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAAA,QACvC,QAAQ;AAAA,MACZ,CAAC;AACD,UAAI,KAAK,WAAW;AAAA,MAEhB,WAAW,KAAK,WAAW,CAAC;AAAA,MAE5B,WAAW,KAAK,WAAW,CAAC;AAAA,MAE5B,WAAW,KAAK,WAAW,CAAC;AAAA,MAE5B,WAAW,KAAK,WAAW,CAAC,GAAG;AAE/B,cAAM,UAAU;AAAA,MACpB,OACK;AAED,cAAM,UAAU;AAAA,MACpB;AAAA,IACJ;AAAA,IACA,UAAU,KAAK;AACX,UAAI,OAAO,IAAI,YAAY,UAAU;AACjC;AAAA,MACJ;AAEA,WAAK,gBAAgB,SAAS,cAAc,KAAK;AAEjD,UAAI,KAAK;AACL,iBAAS,KAAK,YAAY,KAAK,aAAa;AAChD,eAAS,iBAAiB,aAAa,KAAK,gBAAgB;AAC5D,eAAS,iBAAiB,WAAW,KAAK,gBAAgB;AAC1D,UAAI,iBAAiB,SAAS,CAAC,MAAM;AACjC,cAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,cAAM,EAAE,KAAK,MAAM,OAAO,OAAO,IAAI,IAAI,sBAAsB;AAC/D,cAAM,IAAI,UAAU;AACpB,cAAM,IAAI,UAAU;AACpB,cAAM,MAAM,IAAI,WAAW,IAAI;AAC/B,cAAM,UAAU,IAAI,aAAa,KAAK,IAAI,GAAG,QAAQ,CAAC,GAAG,KAAK,IAAI,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;AACtF,YAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,QAAQ;AAC3B,YAAI,YAAY,IAAI,KAAK,QAAQ,CAAC,CAAC;AACnC,aAAK,MAAM,gBAAgB,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAAA,MAC7C,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAA4B;AAAA,EAAY;AAAA,EAAQ,EAAE,GAAG,obAAob;AAAA,EAAG;AAAA,EAAM;AAAA;AAAgB;AACxgB,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,aAA0B;AAAA,EAAY;AAAA,EAAK;AAAA,IAC/C,MAAM;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EAAG;AAAA,IACY,YAAY,KAAK;AAAA,MAC5B,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,GAAG;AAAA,MACY,YAAY,UAAU;AAAA,QACjC,kBAAkB;AAAA,QAClB,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,GAAG;AAAA,MACL,CAAC;AAAA,MACY,YAAY,QAAQ,EAAE,GAAG,8BAA8B,GAAG;AAAA,QACxD,YAAY,oBAAoB;AAAA,UAC3C,eAAe;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,aAAa;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAAG;AAAA;AAAgB;AAEnB,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAQ,UAAU,GAAG,YAAY,OAAO,MAAM;AAAA,IAC3C,CAAC,KAAK,aACF,UAAU,GAAG;AAAA,MAAY;AAAA,MAAO;AAAA,QAC/B,KAAK;AAAA,QACL,OAAO,CAAC,EAAE,QAAQ,KAAK,aAAa,GAAG,QAAQ;AAAA,QAC/C,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAU,KAAK,cAAc,KAAK,WAAW,GAAG,IAAI;AAAA,MAC7F;AAAA,MAAG;AAAA,QACD;AAAA,MACF;AAAA,MAAG;AAAA;AAAA,IAAa,KAChB,mBAAmB,QAAQ,IAAI;AAAA,IAClC,KAAK,aACD,UAAU,GAAG,YAAY,OAAO,YAAY;AAAA,MAC3C;AAAA,IACF,CAAC,KACD,mBAAmB,QAAQ,IAAI;AAAA,EACrC,CAAC;AACH;AAEA,SAAS,SAAS;AAClB,SAAS,SAAS;AAElB,IAAI,WAAW,gBAAgB;AAAA,EAC3B,OAAO;AAAA,IACH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO,CAAC,cAAc,cAAc,WAAW;AAAA,EAC/C,MAAM,OAAO,EAAE,KAAK,GAAG;AACnB,UAAM,aAAa,SAAS;AAAA,MACxB,MAAM;AACF,eAAO,MAAM,SAAS;AAAA,MAC1B;AAAA,MACA,IAAI,KAAK;AACL,aAAK,cAAc,GAAG;AAAA,MAC1B;AAAA,IACJ,CAAC;AAED,UAAM,cAAc,CAAC,UAAU;AAC3B,WAAK,cAAc,KAAK;AAAA,IAC5B;AACA,UAAM,aAAa,CAAC,UAAU;AAC1B,WAAK,aAAa,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAAe,EAAE,OAAO,aAAa;AAC3C,IAAM,eAAe,EAAE,OAAO,OAAO;AAErC,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAQ,UAAU,GAAG,YAAY,OAAO,cAAc;AAAA,IACpD;AAAA,MAAY;AAAA,MAAQ;AAAA,MAAc,gBAAgB,KAAK,IAAI;AAAA,MAAG;AAAA;AAAA,IAAY;AAAA,IAC1E,eAAe;AAAA,MAAY;AAAA,MAAS;AAAA,QAClC,uBAAuB,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,YAAW,KAAK,aAAa;AAAA,QAC9E,OAAO;AAAA,QACP,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAU,KAAK,eAAe,KAAK,YAAY,GAAG,IAAI;AAAA,QAC7F,QAAQ,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAU,KAAK,cAAc,KAAK,WAAW,GAAG,IAAI;AAAA,MAC5F;AAAA,MAAG;AAAA,MAAM;AAAA;AAAA,IAAoC,GAAG;AAAA,MAC9C,CAAC,YAAY,KAAK,UAAU;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,SAAS;AAClB,SAAS,SAAS;AAElB,IAAI,WAAW,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IACpB;AAAA,IACA,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO,CAAC,aAAa;AAAA,EACrB,MAAM,OAAO,EAAE,KAAK,GAAG;AACnB,UAAM,QAAQ,IAAI;AAClB,UAAM,gBAAgB,IAAI,CAAC,CAAC;AAC5B,UAAM,iBAAiB,IAAI;AAC3B,QAAI,MAAM,oBAAoB,cAAc;AACxC,oBAAc,QACV,KAAK,MAAM,aAAa,QAAQ,MAAM,gBAAgB,CAAC,KAAK,CAAC;AAAA,IACrE;AACA,mBAAe,QAAQ,kBAAkB,CAAC,EAAE,UAAU;AACtD,gBAAY,MAAM;AACd,uBAAiB,MAAM,KAAK;AAAA,IAChC,CAAC;AACD,aAAS,iBAAiBC,QAAO;AAC7B,UAAI,CAACA,QAAO;AACR;AAAA,MACJ;AACA,YAAM,SAAS,cAAc,SAAS,CAAC;AAEvC,YAAMC,SAAQ,OAAO,QAAQD,MAAK;AAClC,UAAIC,UAAS,GAAG;AACZ,eAAO,OAAOA,QAAO,CAAC;AAAA,MAC1B;AACA,UAAI,OAAO,UAAU,GAAG;AACpB,eAAO,SAAS;AAAA,MACpB;AAEA,aAAO,QAAQD,MAAK;AACpB,oBAAc,QAAQ,UAAU,CAAC;AACjC,UAAI,gBAAgB,MAAM;AACtB,qBAAa,QAAQ,MAAM,kBAAkB,KAAK,UAAU,MAAM,CAAC;AAAA,IAC3E;AACA,aAAS,YAAYA,QAAO;AACxB,WAAK,eAAeA,MAAK;AAAA,IAC7B;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAAe,EAAE,OAAO,SAAS;AACvC,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AACT;AAEA,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAQ,UAAU,GAAG,YAAY,OAAO,MAAM;AAAA,IAC5C,YAAY,MAAM,cAAc;AAAA,OAC7B,UAAU,IAAI,GAAG;AAAA,QAAY;AAAA,QAAU;AAAA,QAAM,WAAW,KAAK,eAAe,CAAC,SAAS;AACrF,iBAAQ,UAAU,GAAG,YAAY,MAAM;AAAA,YACrC,KAAK;AAAA,YACL,OAAO;AAAA,YACP,SAAS,YAAW,KAAK,YAAY,IAAI;AAAA,UAC3C,GAAG;AAAA,YACD;AAAA,cAAY;AAAA,cAAO;AAAA,gBACjB,OAAO,EAAE,YAAY,OAAO,KAAK,cAAc,IAAI;AAAA,gBACnD,OAAO;AAAA,cACT;AAAA,cAAG;AAAA,cAAM;AAAA;AAAA,YAAa;AAAA,YACtB;AAAA,cAAY;AAAA,cAAO;AAAA,gBACjB,OAAO,EAAE,YAAY,KAAK;AAAA,gBAC1B,OAAO;AAAA,cACT;AAAA,cAAG;AAAA,cAAM;AAAA;AAAA,YAAa;AAAA,UACxB,GAAG,GAAe,CAAC,SAAS,CAAC;AAAA,QAC/B,CAAC;AAAA,QAAG;AAAA;AAAA,MAAwB;AAAA,IAC9B,CAAC;AAAA,IACA,KAAK,cAAc,UACf,UAAU,GAAG,YAAY,MAAM,cAAc;AAAA,OAC3C,UAAU,IAAI,GAAG;AAAA,QAAY;AAAA,QAAU;AAAA,QAAM,WAAW,KAAK,eAAe,CAAC,SAAS;AACrF,iBAAQ,UAAU,GAAG,YAAY,MAAM;AAAA,YACrC,KAAK;AAAA,YACL,OAAO;AAAA,YACP,SAAS,YAAW,KAAK,YAAY,IAAI;AAAA,UAC3C,GAAG;AAAA,YACD;AAAA,cAAY;AAAA,cAAO;AAAA,gBACjB,OAAO,EAAE,YAAY,OAAO,KAAK,cAAc,IAAI;AAAA,gBACnD,OAAO;AAAA,cACT;AAAA,cAAG;AAAA,cAAM;AAAA;AAAA,YAAa;AAAA,YACtB;AAAA,cAAY;AAAA,cAAO;AAAA,gBACjB,OAAO,EAAE,YAAY,KAAK;AAAA,gBAC1B,OAAO;AAAA,cACT;AAAA,cAAG;AAAA,cAAM;AAAA;AAAA,YAAa;AAAA,UACxB,GAAG,GAAe,CAAC,SAAS,CAAC;AAAA,QAC/B,CAAC;AAAA,QAAG;AAAA;AAAA,MAAwB;AAAA,IAC9B,CAAC,KACD,mBAAmB,QAAQ,IAAI;AAAA,EACrC,CAAC;AACH;AAEA,SAAS,SAAS;AAClB,SAAS,SAAS;AAElB,IAAI,WAAW,gBAAgB;AAAA,EAC3B,YAAY;AAAA,IACR,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ;AAAA,EACA,OAAO,CAAC,eAAe,cAAc,cAAc,WAAW;AAAA,EAC9D,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IACpB;AAAA,IACA,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,OAAO;AACH,WAAO;AAAA,MACH,UAAU;AAAA,MACV,WAAW;AAAA,MACX,eAAe;AAAA,MACf,WAAW;AAAA,MACX,UAAU;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN,eAAe;AACX,aAAO,KAAK,UAAU;AAAA,IAC1B;AAAA,IACA,aAAa;AACT,aAAO,KAAK,aAAa,KAAK,WAAW,KAAK;AAAA,IAClD;AAAA,IACA,eAAe;AACX,aAAO,KAAK,cAAc,KAAK,aAAa,IAAI,KAAK;AAAA,IACzD;AAAA,IACA,OAAO;AACH,aAAO;AAAA,QACH,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,MACZ;AAAA,IACJ;AAAA,IACA,MAAM;AACF,aAAO;AAAA,QACH,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,MACZ;AAAA,IACJ;AAAA,IACA,YAAY;AACR,aAAO,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,IAC9C;AAAA,IACA,kBAAkB;AACd,aAAO,GAAG,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,IACrD;AAAA,IACA,aAAa;AACT,aAAO,QAAQ,KAAK,eAAe;AAAA,IACvC;AAAA,IACA,YAAY;AACR,aAAO,QAAQ,KAAK,MAAM,IAAI;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,UAAU;AACN,WAAO,OAAO,MAAM,cAAc,KAAK,KAAK,CAAC;AAC7C,SAAK,QAAQ;AACb,SAAK,OAAO,QAAQ,MAAM;AACtB,WAAK,MAAM,eAAe;AAAA,QACtB,MAAM,KAAK;AAAA,QACX,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AAAA,MACd,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,SAAS;AAAA,IACL,iBAAiB,OAAO;AACpB,YAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,cAAc,KAAK;AAChD,aAAO,OAAO,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACxC,WAAK,QAAQ;AAAA,IACjB;AAAA,IACA,YAAY,OAAO;AACf,WAAK,MAAM,cAAc,KAAK;AAAA,IAClC;AAAA,IACA,WAAW,OAAO;AACd,WAAK,MAAM,aAAa,KAAK;AAAA,IACjC;AAAA,IACA,UAAU,OAAO;AACb,YAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,cAAc,KAAK;AAChD,aAAO,OAAO,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACxC,WAAK,QAAQ;AACb,WAAK,UAAU,MAAM;AAEjB,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,WAAW,YAAY;AAAA,MACtC,CAAC;AAAA,IACL;AAAA,IACA,YAAY,GAAG;AACX,WAAK,IAAI;AACT,WAAK,QAAQ;AAAA,IACjB;AAAA,IACA,SAAS,OAAO;AACZ,YAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,cAAc,KAAK;AACnD,aAAO,OAAO,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,WAAK,WAAW;AAChB,WAAK,YAAY,KAAK;AACtB,WAAK,UAAU,MAAM;AAEjB,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,IAAI,YAAY;AAAA,MAC/B,CAAC;AAAA,IACL;AAAA,IACA,UAAU,OAAO;AACb,YAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,cAAc,KAAK;AACnD,aAAO,OAAO,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,WAAK,WAAW,KAAK;AACrB,WAAK,YAAY;AACjB,WAAK,UAAU,MAAM;AAEjB,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,IAAI,YAAY;AAAA,MAC/B,CAAC;AAAA,IACL;AAAA,IACA,UAAU;AACN,WAAK,WAAW,KAAK;AACrB,WAAK,YAAY,KAAK;AAAA,IAC1B;AAAA,IACA,WAAW,QAAQ;AACf,WAAK,MAAM,cAAc,MAAM;AAAA,IACnC;AAAA,IACA,aAAa,OAAO;AAChB,YAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,cAAc,KAAK;AACnD,aAAO,OAAO,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,WAAK,QAAQ;AACb,WAAK,UAAU,MAAM;AAEjB,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,IAAI,YAAY;AAAA,MAC/B,CAAC;AAAA,IACL;AAAA,IACA,YAAY,OAAO;AACf,YAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,cAAc,KAAK;AACnD,aAAO,OAAO,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,WAAK,QAAQ;AACb,WAAK,UAAU,MAAM;AAEjB,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,WAAW,YAAY;AAElC,aAAK,MAAM,IAAI,YAAY;AAAA,MAC/B,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;AAED,IAAM,eAAe,EAAE,OAAO,YAAY;AAE1C,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,QAAM,wBAAwB,iBAAiB,YAAY;AAC3D,QAAM,iBAAiB,iBAAiB,KAAK;AAC7C,QAAM,mBAAmB,iBAAiB,OAAO;AACjD,QAAM,qBAAqB,iBAAiB,SAAS;AACrD,QAAM,oBAAoB,iBAAiB,QAAQ;AACnD,QAAM,iBAAiB,iBAAiB,KAAK;AAC7C,QAAM,oBAAoB,iBAAiB,QAAQ;AAEnD,SAAQ,UAAU,GAAG;AAAA,IAAY;AAAA,IAAO;AAAA,MACtC,OAAO,CAAC,mBAAmB,EAAE,OAAO,KAAK,aAAa,CAAC;AAAA,MACvD,OAAO,EAAE,OAAO,KAAK,aAAa,KAAK;AAAA,IACzC;AAAA,IAAG;AAAA,MACD,YAAY,OAAO,cAAc;AAAA,QAC/B,YAAY,uBAAuB;AAAA,UACjC,KAAK;AAAA,UACL,OAAO,KAAK;AAAA,UACZ,KAAK,KAAK;AAAA,UACV,MAAM,KAAK;AAAA,UACX,oBAAoB,KAAK;AAAA,QAC3B,GAAG,MAAM,GAAe,CAAC,SAAS,OAAO,QAAQ,oBAAoB,CAAC;AAAA,QACtE,YAAY,gBAAgB;AAAA,UAC1B,KAAK;AAAA,UACL,KAAK,KAAK;AAAA,UACV,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK;AAAA,UACb,aAAa,KAAK;AAAA,QACpB,GAAG,MAAM,GAAe,CAAC,OAAO,SAAS,UAAU,aAAa,CAAC;AAAA,QACjE,YAAY,kBAAkB;AAAA,UAC5B,KAAK;AAAA,UACL,OAAO,KAAK;AAAA,UACZ,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK;AAAA,UACb,eAAe,KAAK;AAAA,QACtB,GAAG,MAAM,GAAe,CAAC,SAAS,QAAQ,SAAS,UAAU,eAAe,CAAC;AAAA,MAC/E,CAAC;AAAA,MACD;AAAA,QAAY;AAAA,QAAO;AAAA,UACjB,OAAO,EAAE,QAAQ,KAAK,gBAAgB,KAAK;AAAA,UAC3C,OAAO;AAAA,QACT;AAAA,QAAG;AAAA,UACD,YAAY,oBAAoB;AAAA,YAC9B,OAAO,KAAK;AAAA,YACZ,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,UACf,GAAG,MAAM,GAAe,CAAC,SAAS,SAAS,QAAQ,CAAC;AAAA,UACnD,CAAC,KAAK,cACF,UAAU,GAAG,YAAY,mBAAmB;AAAA,YAC3C,KAAK;AAAA,YACL,iBAAiB,KAAK;AAAA,YACtB,eAAe,KAAK;AAAA,YACpB,cAAc,KAAK;AAAA,YACnB,gBAAgB,KAAK;AAAA,UACvB,GAAG,MAAM,GAAe,CAAC,iBAAiB,eAAe,gBAAgB,gBAAgB,CAAC,KAC1F,mBAAmB,QAAQ,IAAI;AAAA,QACrC;AAAA,QAAG;AAAA;AAAA,MAAa;AAAA,MAChB,YAAY,gBAAgB;AAAA,QAC1B,MAAM;AAAA,QACN,OAAO,KAAK;AAAA,QACZ,cAAc,KAAK;AAAA,QACnB,cAAc,KAAK;AAAA,QACnB,aAAa,KAAK;AAAA,MACpB,GAAG,MAAM,GAAe,CAAC,SAAS,gBAAgB,gBAAgB,aAAa,CAAC;AAAA,MAChF,YAAY,gBAAgB;AAAA,QAC1B,MAAM;AAAA,QACN,OAAO,KAAK;AAAA,QACZ,cAAc,KAAK;AAAA,QACnB,cAAc,KAAK;AAAA,QACnB,aAAa,KAAK;AAAA,MACpB,GAAG,MAAM,GAAe,CAAC,SAAS,gBAAgB,gBAAgB,aAAa,CAAC;AAAA,MAChF,YAAY,mBAAmB;AAAA,QAC7B,OAAO,KAAK;AAAA,QACZ,kBAAkB,KAAK;AAAA,QACvB,sBAAsB,KAAK;AAAA,QAC3B,eAAe,KAAK;AAAA,MACtB,GAAG,MAAM,GAAe,CAAC,SAAS,kBAAkB,sBAAsB,eAAe,CAAC;AAAA,MAC1F,mBAAmB,kBAAkB;AAAA,MACrC,WAAW,KAAK,QAAQ,SAAS;AAAA,IACnC;AAAA,IAAG;AAAA;AAAA,EAAoB;AACzB;AAEA,SAAS,SAAS;AAClB,SAAS,SAAS;AAElB,SAAS,UAAU,CAAC,QAAQ;AACxB,MAAI,UAAU,SAAS,MAAM,QAAQ;AACzC;AAEA,SAAS,QAAQ,KAAK;AAClB,MAAI,UAAU,SAAS,MAAM,QAAQ;AACzC;AACA,IAAI,QAAQ,EAAE,QAAQ;AAEtB,IAAO,oCAAQ;", "names": ["e", "color", "index"]}