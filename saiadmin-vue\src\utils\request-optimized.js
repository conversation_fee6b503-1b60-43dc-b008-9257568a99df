import axios from 'axios'
import { Message } from '@arco-design/web-vue'
import tool from '@/utils/tool'
import { get, isEmpty } from 'lodash'
import qs from 'qs'
import { h } from 'vue'
import { IconFaceFrownFill } from '@arco-design/web-vue/dist/arco-vue-icon'
import router from '@/router'

// 优化1：请求缓存机制
const requestCache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

// 优化2：请求去重机制
const pendingRequests = new Map()

// 优化3：请求重试机制
const MAX_RETRY_COUNT = 3
const RETRY_DELAY = 1000

/**
 * 生成请求唯一标识
 */
function generateRequestKey(config) {
  const { method, url, params, data } = config
  return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
}

/**
 * 节流函数优化
 */
function throttle(func, delay = 1000) {
  let timeoutId
  let lastExecTime = 0
  return function (...args) {
    const currentTime = Date.now()
    
    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        func.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

/**
 * 请求重试函数
 */
async function retryRequest(config, retryCount = 0) {
  try {
    return await axios(config)
  } catch (error) {
    if (retryCount < MAX_RETRY_COUNT && shouldRetry(error)) {
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * Math.pow(2, retryCount)))
      return retryRequest(config, retryCount + 1)
    }
    throw error
  }
}

/**
 * 判断是否应该重试
 */
function shouldRetry(error) {
  return error.code === 'NETWORK_ERROR' || 
         (error.response && error.response.status >= 500)
}

function createOptimizedService() {
  const service = axios.create({
    timeout: 10000, // 优化：设置合理的超时时间
    // 优化：启用请求压缩
    headers: {
      'Accept-Encoding': 'gzip, deflate, br'
    }
  })

  // HTTP request 拦截器 - 优化版
  service.interceptors.request.use(
    (config) => {
      const requestKey = generateRequestKey(config)
      
      // 优化：请求去重
      if (pendingRequests.has(requestKey)) {
        const controller = new AbortController()
        config.signal = controller.signal
        controller.abort('Duplicate request cancelled')
        return Promise.reject(new Error('Duplicate request'))
      }
      
      // 优化：GET请求缓存
      if (config.method === 'get' && config.cache !== false) {
        const cached = requestCache.get(requestKey)
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
          return Promise.resolve(cached.data)
        }
      }
      
      // 记录请求
      pendingRequests.set(requestKey, config)
      
      // 添加认证token
      const token = tool.local.get(import.meta.env.VITE_APP_TOKEN_PREFIX)
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      
      // 优化：请求数据压缩
      if (config.data && typeof config.data === 'object') {
        config.headers['Content-Type'] = 'application/json'
      }
      
      return config
    },
    (error) => {
      console.error('Request error:', error)
      return Promise.reject(error)
    }
  )

  // HTTP response 拦截器 - 优化版
  service.interceptors.response.use(
    (response) => {
      const requestKey = generateRequestKey(response.config)
      
      // 清除pending请求记录
      pendingRequests.delete(requestKey)
      
      // 优化：缓存GET请求结果
      if (response.config.method === 'get' && response.config.cache !== false) {
        requestCache.set(requestKey, {
          data: response,
          timestamp: Date.now()
        })
        
        // 优化：定期清理过期缓存
        if (requestCache.size > 100) {
          const now = Date.now()
          for (const [key, value] of requestCache.entries()) {
            if (now - value.timestamp > CACHE_DURATION) {
              requestCache.delete(key)
            }
          }
        }
      }
      
      // 处理文件下载
      if (response.headers['content-disposition'] || 
          !/^application\/json/.test(response.headers['content-type'])) {
        return response
      }
      
      // 处理业务错误
      if (response.data.size) {
        response.data.code = 500
        response.data.message = '服务器内部错误'
        response.data.success = false
      } else if (response.data.code && response.data.code !== 200) {
        handleBusinessError(response.data)
      }
      
      return response.data
    },
    async (error) => {
      const requestKey = generateRequestKey(error.config || {})
      pendingRequests.delete(requestKey)
      
      // 优化：自动重试机制
      if (shouldRetry(error) && !error.config.__retryCount) {
        error.config.__retryCount = 0
        try {
          return await retryRequest(error.config)
        } catch (retryError) {
          return handleRequestError(retryError)
        }
      }
      
      return handleRequestError(error)
    }
  )

  return service
}

/**
 * 处理业务错误
 */
function handleBusinessError(data) {
  if (data.code === 401) {
    throttle(() => {
      Message.error({
        content: data.message || data.msg,
        icon: () => h(IconFaceFrownFill)
      })
      tool.local.clear()
      router.push({ name: 'login' })
    })()
  } else {
    Message.error({
      content: data.message || data.msg,
      icon: () => h(IconFaceFrownFill)
    })
  }
}

/**
 * 处理请求错误
 */
function handleRequestError(error) {
  const errorMessage = (text) => {
    Message.error({
      content: error.response?.data?.message || text,
      icon: () => h(IconFaceFrownFill)
    })
  }

  if (error.response?.data) {
    switch (error.response.status) {
      case 404:
        errorMessage('服务器资源不存在')
        break
      case 500:
        errorMessage('服务器内部错误')
        break
      case 401:
        throttle(() => {
          errorMessage('登录状态已过期，需要重新登录')
          tool.local.clear()
          router.push({ name: 'login' })
        })()
        break
      case 403:
        errorMessage('没有权限访问该资源')
        break
      default:
        errorMessage('网络请求失败')
    }
  } else if (error.code === 'NETWORK_ERROR') {
    errorMessage('网络连接失败，请检查网络')
  } else if (error.code === 'TIMEOUT') {
    errorMessage('请求超时，请稍后重试')
  } else {
    errorMessage('请求失败')
  }

  return Promise.reject(error)
}

// 创建优化的请求实例
const optimizedRequest = createOptimizedService()

// 导出优化的请求方法
export const request = optimizedRequest
export default optimizedRequest

// 导出缓存控制方法
export const requestUtils = {
  clearCache: () => requestCache.clear(),
  getCacheSize: () => requestCache.size,
  clearPendingRequests: () => pendingRequests.clear()
}
