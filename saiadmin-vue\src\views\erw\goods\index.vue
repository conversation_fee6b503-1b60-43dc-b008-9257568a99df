<template>
  <div class="ma-content-block">
    <sa-table ref="crudRef" :options="options" :columns="columns" :searchForm="searchForm">
      <!-- 搜索区 tableSearch -->
      <template #tableSearch>
      </template>

      <!-- Table 自定义渲染 -->
    </sa-table>

    <!-- 编辑表单 -->
    <edit-form ref="editRef" @success="refresh" />

  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import EditForm from './edit.vue'
import api from '../api/goods'

// 引用定义
const crudRef = ref()
const editRef = ref()
const viewRef = ref()

// 搜索表单
const searchForm = ref({
})

// SaTable 基础配置
const options = reactive({
  api: api.getPageList,
  rowSelection: { showCheckedAll: true },
  add: {
    show: true,
    auth: ['/erw/ZjhjBdAdvanceGoods/save'],
    func: async () => {
      editRef.value?.open()
    },
  },
  edit: {
    show: true,
    auth: ['/erw/ZjhjBdAdvanceGoods/update'],
    func: async (record) => {
      editRef.value?.open('edit')
      editRef.value?.setFormData(record)
    },
  },
  delete: {
    show: true,
    auth: ['/erw/ZjhjBdAdvanceGoods/destroy'],
    func: async (params) => {
      const resp = await api.destroy(params)
      if (resp.code === 200) {
        Message.success(`删除成功！`)
        crudRef.value?.refresh()
      }
    },
  },
})

// SaTable 列配置
const columns = reactive([
  { title: '', dataIndex: 'goods_id', width: 180 },
  { title: '', dataIndex: 'mall_id', width: 180 },
  { title: '阶梯规则', dataIndex: 'ladder_rules', width: 180 },
  { title: '', dataIndex: 'deposit', width: 180 },
  { title: '定金膨胀金', dataIndex: 'swell_deposit', width: 180 },
  { title: '预售开始时间', dataIndex: 'start_prepayment_at', width: 180 },
  { title: '预售结束时间', dataIndex: 'end_prepayment_at', width: 180 },
  { title: '尾款支付时间 -1:无限制', dataIndex: 'pay_limit', width: 180 },
])

// 页面数据初始化
const initPage = async () => {}

// SaTable 数据请求
const refresh = async () => {
  crudRef.value?.refresh()
}

// 页面加载完成执行
onMounted(async () => {
  initPage()
  refresh()
})
</script>
