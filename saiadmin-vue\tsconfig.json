{"compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "jsx": "preserve", "allowJs": true, "strictFunctionTypes": false, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true, "removeComments": true, "noImplicitAny": false, "experimentalDecorators": true, "target": "esnext", "module": "esnext", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@cps/*": ["src/components/*"], "vue-i18n": ["vue-i18n/dist/vue-i18n.cjs.js"]}, "lib": ["dom", "esnext"], "types": ["vite/client"]}, "include": ["src", "mock", "vite.config.ts", "node_modules", "dist", "build"]}