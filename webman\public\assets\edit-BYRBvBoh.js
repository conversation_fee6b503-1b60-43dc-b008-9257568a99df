import{t as R,c as A}from"./index-DkGLNqVb.js";import{a as V}from"./role-CkU346eF.js";import{M as G}from"./@arco-design-uttiljWv.js";import{r as n,c as N,a as P,h as m,j as T,k as z,l as i,t as a,a1 as b,O as E}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const Qe={__name:"edit",emits:["success"],setup(H,{expose:k,emit:g}){const w=g,u=n(),c=n(""),p=n(!1),f=n(!1),_=n([]);let x=N(()=>"角色管理"+(c.value=="add"?"-新增":"-编辑"));const v={id:"",parent_id:"",level:"",name:"",code:"",sort:100,status:1,remark:""},t=P({...v}),y={parent_id:[{required:!0,message:"上级角色不能为空"}],name:[{required:!0,message:"角色名称不能为空"}],code:[{required:!0,message:"角色标识不能为空"}]},U=async(l="add")=>{c.value=l,Object.assign(t,v),u.value.clearValidate(),p.value=!0,await D()},D=async()=>{const l=await A.commonGet("/core/role/index?tree=true&filter=false");_.value=l.data},C=async l=>{for(const e in t)l[e]!=null&&l[e]!=null&&(t[e]=l[e])},q=async l=>{var d;if(!await((d=u.value)==null?void 0:d.validate())){f.value=!0;let r={...t},s={};c.value==="add"?(r.id=void 0,s=await V.save(r)):s=await V.update(r.id,r),s.code===200&&(G.success("操作成功"),w("success"),l(!0)),setTimeout(()=>{f.value=!1},500)}l(!1)},B=()=>p.value=!1;return k({open:U,setFormData:C}),(l,e)=>{const d=m("a-tree-select"),r=m("a-form-item"),s=m("a-input"),O=m("a-input-number"),j=m("sa-radio"),F=m("a-textarea"),M=m("a-form");return z(),T(E("a-modal"),{visible:p.value,"onUpdate:visible":e[6]||(e[6]=o=>p.value=o),width:b(R).getDevice()==="mobile"?"100%":"600px",title:b(x),"mask-closable":!1,"ok-loading":f.value,onCancel:B,onBeforeOk:q},{default:i(()=>[a(M,{ref_key:"formRef",ref:u,model:t,rules:y,"auto-label-width":!0},{default:i(()=>[a(r,{field:"parent_id",label:"上级角色"},{default:i(()=>[a(d,{modelValue:t.parent_id,"onUpdate:modelValue":e[0]||(e[0]=o=>t.parent_id=o),data:_.value,"field-names":{key:"value",title:"label"},"allow-clear":"",placeholder:"请选择上级角色"},null,8,["modelValue","data"])]),_:1}),a(r,{label:"角色名称",field:"name"},{default:i(()=>[a(s,{modelValue:t.name,"onUpdate:modelValue":e[1]||(e[1]=o=>t.name=o),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),a(r,{label:"角色标识",field:"code"},{default:i(()=>[a(s,{modelValue:t.code,"onUpdate:modelValue":e[2]||(e[2]=o=>t.code=o),placeholder:"请输入角色标识"},null,8,["modelValue"])]),_:1}),a(r,{label:"排序数字",field:"sort"},{default:i(()=>[a(O,{modelValue:t.sort,"onUpdate:modelValue":e[3]||(e[3]=o=>t.sort=o),placeholder:"请输入排序数字"},null,8,["modelValue"])]),_:1}),a(r,{label:"状态",field:"status"},{default:i(()=>[a(j,{modelValue:t.status,"onUpdate:modelValue":e[4]||(e[4]=o=>t.status=o),dict:"data_status",placeholder:"请选择状态"},null,8,["modelValue"])]),_:1}),a(r,{label:"备注",field:"remark"},{default:i(()=>[a(F,{modelValue:t.remark,"onUpdate:modelValue":e[5]||(e[5]=o=>t.remark=o),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},40,["visible","width","title","ok-loading"])}}};export{Qe as default};
