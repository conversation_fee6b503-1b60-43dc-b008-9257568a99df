<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> Yii 2.0 控制台应用配置
 */

$params = require __DIR__ . "/params.php";
$db = require __DIR__ . "/db.php";

$config = [
    "id" => "saiadmin-console",
    "basePath" => dirname(__DIR__),
    "bootstrap" => ["log"],
    "controllerNamespace" => "app\commands",
    "aliases" => [
        "@bower" => "@vendor/bower-asset",
        "@npm"   => "@vendor/npm-asset",
        "@tests" => "@app/tests",
    ],
    "components" => [
        "cache" => [
            "class" => "yii\caching\FileCache",
        ],
        "log" => [
            "targets" => [
                [
                    "class" => "yii\log\FileTarget",
                    "levels" => ["error", "warning"],
                ],
            ],
        ],
        "db" => $db,
    ],
    "params" => $params,
    "controllerMap" => [
        "migrate" => [
            "class" => "yii\console\controllers\MigrateController",
            "migrationPath" => "@app/migrations",
        ],
    ],
];

return $config;