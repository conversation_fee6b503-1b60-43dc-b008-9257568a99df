// Native JavaScript for Bootstrap 3/4 Polyfill
(function(){var F="Document",i=document,g=this[F]||this.HTMLDocument,l="Window",E=window,p=this.constructor||this[l]||Window,u="HTMLElement",k="documentElement",D=Element,J="className",d="add",c="classList",x="remove",z="contains",s="class",e="setAttribute",A="getAttribute",t="prototype",o="indexOf",r="length",y="split",b="trim",f="Event",I="CustomEvent",C="_events",n="type",a="target",m="currentTarget",B="relatedTarget",v="cancelable",q="bubbles",w="cancelBubble",H="cancelImmediate",K="detail",L="addEventListener",h="removeEventListener",j="dispatchEvent";if(!E[u]){E[u]=E[D]}if(!Array[t][o]){Array[t][o]=function(O){if(this===undefined||this===null){throw new TypeError(this+" is not an object")}var N=this instanceof String?this[y](""):this,P=Math.max(Math.min(N[r],9007199254740991),0)||0,M=Number(arguments[1])||0;M=(M<0?Math.max(P+M,0):M)-1;while(++M<P){if(M in N&&N[M]===O){return M}}return -1}}if(!String[t][b]){String[t][b]=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}}if(!(c in D[t])){var G=function(M){var N=(M[A](s)||"").replace(/^\s+|\s+$/g,"")[y](/\s+/)||[];hasClass=this[z]=function(O){return N[o](O)>-1},addClass=this[d]=function(O){if(!hasClass(O)){N.push(O);M[e](s,N.join(" "))}},removeClass=this[x]=function(O){if(hasClass(O)){N.splice(N[o](O),1);M[e](s,N.join(" "))}},toggleClass=this.toggle=function(O){if(hasClass(O)){removeClass(O)}else{addClass(O)}}};Object.defineProperty(D[t],c,{get:function(){return new G(this)}})}if(!E[f]||!p[t][f]){E[f]=p[t][f]=g[t][f]=D[t][f]=function(O,Q){if(!O){throw new Error("Not enough arguments")}var P,N=Q&&Q[q]!==undefined?Q[q]:false,M=Q&&Q[v]!==undefined?Q[v]:false;if("createEvent" in i){P=i.createEvent(f);P.initEvent(O,N,M)}else{P=i.createEventObject();P[n]=O;P[q]=N;P[v]=M}return P}}if(!(I in E)||!(I in p[t])){E[I]=p[t][I]=g[t][I]=Element[t][I]=function(M,O){if(!M){throw Error("CustomEvent TypeError: An event name must be provided.")}var N=new Event(M,O);N[K]=O&&O[K]||null;return N}}if(!E[L]||!p[t][L]){E[L]=p[t][L]=g[t][L]=D[t][L]=function(){var M=this,N=arguments[0],O=arguments[1];if(!M[C]){M[C]={}}if(!M[C][N]){M[C][N]=function(T){var U=M[C][T[n]].list,R=U.slice(),Q=-1,S=R[r],P;T.preventDefault=function(){if(T[v]!==false){T.returnValue=false}};T.stopPropagation=function(){T[w]=true};T.stopImmediatePropagation=function(){T[w]=true;T[H]=true};T[m]=M;T[B]=T[B]||T.fromElement||null;T[a]=T[a]||T.srcElement||M;T.timeStamp=new Date().getTime();if(T.clientX){T.pageX=T.clientX+i[k].scrollLeft;T.pageY=T.clientY+i[k].scrollTop}while(++Q<S&&!T[H]){if(Q in R){P=R[Q];if(U[o](P)!==-1&&typeof P==="function"){P.call(M,T)}}}};M[C][N].list=[];if(M.attachEvent){M.attachEvent("on"+N,M[C][N])}}M[C][N].list.push(O)};E[h]=p[t][h]=g[t][h]=D[t][h]=function(){var N=this,O=arguments[0],P=arguments[1],M;if(N[C]&&N[C][O]&&N[C][O].list){M=N[C][O].list[o](P);if(M!==-1){N[C][O].list.splice(M,1);if(!N[C][O].list[r]){if(N.detachEvent){N.detachEvent("on"+O,N[C][O])}delete N[C][O]}}}}}if(!E[j]||!p[t][j]||!g[t][j]||!D[t][j]){E[j]=p[t][j]=g[t][j]=D[t][j]=function(Q){if(!arguments[r]){throw new Error("Not enough arguments")}if(!Q||typeof Q[n]!=="string"){throw new Error("DOM Events Exception 0")}var N=this,P=Q[n];try{if(!Q[q]){Q[w]=true;var O=function(R){R[w]=true;(N||E).detachEvent("on"+P,O)};this.attachEvent("on"+P,O)}this.fireEvent("on"+P,Q)}catch(M){Q[a]=N;do{Q[m]=N;if(C in N&&typeof N[C][P]==="function"){N[C][P].call(N,Q)}if(typeof N["on"+P]==="function"){N["on"+P].call(N,Q)}N=N.nodeType===9?N.parentWindow:N.parentNode}while(N&&!Q[w])}return true}}}());