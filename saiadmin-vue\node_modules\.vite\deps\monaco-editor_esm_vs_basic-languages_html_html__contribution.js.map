{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/html/html.contribution.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/html/html.contribution.ts\nimport { registerLanguage } from \"../_.contribution.js\";\nregisterLanguage({\n  id: \"html\",\n  extensions: [\".html\", \".htm\", \".shtml\", \".xhtml\", \".mdoc\", \".jsp\", \".asp\", \".aspx\", \".jshtm\"],\n  aliases: [\"HTML\", \"htm\", \"html\", \"xhtml\"],\n  mimetypes: [\"text/html\", \"text/x-jshtm\", \"text/template\", \"text/ng-template\"],\n  loader: () => {\n    if (false) {\n      return new Promise((resolve, reject) => {\n        __require([\"vs/basic-languages/html/html\"], resolve, reject);\n      });\n    } else {\n      return import(\"./html.js\");\n    }\n  }\n});\n"], "mappings": ";;;;;;;;AASA,iBAAiB;AAAA,EACf,IAAI;AAAA,EACJ,YAAY,CAAC,SAAS,QAAQ,UAAU,UAAU,SAAS,QAAQ,QAAQ,SAAS,QAAQ;AAAA,EAC5F,SAAS,CAAC,QAAQ,OAAO,QAAQ,OAAO;AAAA,EACxC,WAAW,CAAC,aAAa,gBAAgB,iBAAiB,kBAAkB;AAAA,EAC5E,QAAQ,MAAM;AACZ,QAAI,OAAO;AACT,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,kBAAU,CAAC,8BAA8B,GAAG,SAAS,MAAM;AAAA,MAC7D,CAAC;AAAA,IACH,OAAO;AACL,aAAO,OAAO,oBAAW;AAAA,IAC3B;AAAA,EACF;AACF,CAAC;", "names": []}