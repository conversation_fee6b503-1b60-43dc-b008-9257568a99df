import{a as p}from"./user-pcE09jl3.js";import{c as Z}from"./index-DkGLNqVb.js";import ee from"./edit-D3WKGVOx.js";import{a as te}from"./avatar-DvSZjoFF.js";import{M as v,j as ae}from"./@arco-design-uttiljWv.js";import{r as i,a as M,o as oe,h as l,ba as se,n as j,k as c,m as C,t as o,l as s,j as w,p as D,y as f,M as P,a1 as le}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const re={class:"ma-content-block lg:flex justify-between"},ne={class:"lg:w-2/12 pt-4 pl-2 pr-2"},ie={class:"lg:w-10/12 w-full"},de=["src"],me={key:0},wt={__name:"index",setup(ue){const U=i([{label:"所有部门",value:0}]),d=i(),V=i(),g=i(!1),I=i(),b=i(""),h=i([0]),r=i({username:"",phone:"",email:"",status:"",create_time:[],dept_id:""}),N=async()=>{h.value=[0],r.value.dept_id=""},F=a=>{r.value.dept_id=a[0]===0?"":a[0],d.value.refresh(),h.value=a},O=async(a,e)=>{const n=await p.changeStatus({id:e,status:a});n.code===200&&(v.success(n.message),d.value.refresh())},S=a=>{p.clearCache({id:a}).then(e=>e.code===200&&v.success(e.message))},A=async a=>{p.initUserPassword({id:a}).then(e=>e.code===200&&v.success(e.message))},E=async a=>{const e=await p.setHomePage({id:I.value,dashboard:b.value});e.code===200&&(v.success(e.message),d.value.refresh(),a(!0)),a(!1)},$=(a,e)=>{if(a==="resetPassword"){ae.info({title:"提示",content:"确定将该用户密码重置为 sai123456 吗？",simple:!1,onBeforeOk:n=>{A(e),n(!0)}});return}if(a==="updateCache"){S(e);return}if(a==="setHomePage"){g.value=!0,I.value=e;return}},G=M({api:p.getPageList,operationColumnWidth:210,add:{show:!0,auth:["/core/user/save"],func:async()=>{var a;(a=V.value)==null||a.open()}},edit:{show:!0,auth:["/core/user/update"],func:async a=>{var e;(e=V.value)==null||e.open("edit",a.id)}},delete:{show:!0,auth:["/core/user/destroy"],func:async a=>{var n;(await p.destroy(a)).code===200&&(v.success("删除成功！"),(n=d.value)==null||n.refresh())}}}),K=M([{title:"头像",dataIndex:"avatar",width:75},{title:"账户",dataIndex:"username",width:130},{title:"昵称",dataIndex:"nickname",width:120},{title:"手机",dataIndex:"phone",width:150},{title:"邮箱",dataIndex:"email",width:200},{title:"状态",dataIndex:"status",width:100},{title:"工作台",dataIndex:"dashboard",width:100},{title:"注册时间",dataIndex:"create_time",width:180}]),L=async()=>{(await Z.commonGet("/core/dept/accessDept")).data.map(e=>{U.value.push(e)})},B=async()=>{var a;(a=d.value)==null||a.refresh()};return oe(()=>{L(),B()}),(a,e)=>{const n=l("sa-tree-slider"),x=l("a-input"),m=l("a-form-item"),_=l("a-col"),T=l("a-range-picker"),H=l("sa-select"),W=l("sa-switch"),q=l("a-avatar"),z=l("icon-refresh"),R=l("a-link"),J=l("icon-double-right"),y=l("a-doption"),Q=l("a-dropdown"),X=l("sa-table"),Y=l("a-modal"),k=se("auth");return c(),j("div",re,[C("div",ne,[o(n,{data:U.value,"search-placeholder":"搜索部门",onClick:F,modelValue:h.value,"onUpdate:modelValue":e[0]||(e[0]=t=>h.value=t)},null,8,["data","modelValue"])]),C("div",ie,[o(X,{ref_key:"crudRef",ref:d,options:G,columns:K,searchForm:r.value,onResetSearch:N},{tableSearch:s(()=>[o(_,{sm:8,xs:24},{default:s(()=>[o(m,{field:"username",label:"账号名称"},{default:s(()=>[o(x,{modelValue:r.value.username,"onUpdate:modelValue":e[1]||(e[1]=t=>r.value.username=t),placeholder:"请输入账号名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),o(_,{sm:8,xs:24},{default:s(()=>[o(m,{field:"phone",label:"手机"},{default:s(()=>[o(x,{modelValue:r.value.phone,"onUpdate:modelValue":e[2]||(e[2]=t=>r.value.phone=t),placeholder:"请输入手机","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),o(_,{sm:8,xs:24},{default:s(()=>[o(m,{field:"email",label:"邮箱"},{default:s(()=>[o(x,{modelValue:r.value.email,"onUpdate:modelValue":e[3]||(e[3]=t=>r.value.email=t),placeholder:"请输入邮箱","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),o(_,{sm:16,xs:24},{default:s(()=>[o(m,{field:"create_time",label:"注册时间"},{default:s(()=>[o(T,{modelValue:r.value.create_time,"onUpdate:modelValue":e[4]||(e[4]=t=>r.value.create_time=t),"show-time":"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),o(_,{sm:8,xs:24},{default:s(()=>[o(m,{field:"status",label:"状态"},{default:s(()=>[o(H,{modelValue:r.value.status,"onUpdate:modelValue":e[5]||(e[5]=t=>r.value.status=t),dict:"data_status",placeholder:"请选择状态","alow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),status:s(({record:t})=>[o(W,{modelValue:t.status,"onUpdate:modelValue":u=>t.status=u,disabled:t.id==1,onChange:u=>O(u,t.id)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),avatar:s(({record:t})=>[o(q,null,{default:s(()=>[C("img",{src:t.avatar?t.avatar:le(te),style:{"object-fit":"cover"}},null,8,de)]),_:2},1024)]),operationCell:s(({record:t})=>[t.id==1?(c(),j("div",me,[o(R,{onClick:u=>S(t.id)},{default:s(()=>[o(z),e[8]||(e[8]=f(" 更新缓存"))]),_:2},1032,["onClick"])])):D("",!0)]),operationAfterExtend:s(({record:t})=>[t.id!=1?(c(),w(Q,{key:0,trigger:"hover",onSelect:u=>$(u,t.id)},{content:s(()=>[P((c(),w(y,{value:"updateCache"},{default:s(()=>e[10]||(e[10]=[f("更新缓存")])),_:1})),[[k,["/core/user/clearCache"]]]),P((c(),w(y,{value:"setHomePage"},{default:s(()=>e[11]||(e[11]=[f("设置首页")])),_:1})),[[k,["/core/user/setHomePage"]]]),P((c(),w(y,{value:"resetPassword"},{default:s(()=>e[12]||(e[12]=[f("重置密码")])),_:1})),[[k,["/core/user/initUserPassword"]]])]),default:s(()=>[o(R,null,{default:s(()=>[o(J),e[9]||(e[9]=f(" 更多"))]),_:1})]),_:2},1032,["onSelect"])):D("",!0)]),_:1},8,["options","columns","searchForm"])]),o(ee,{ref_key:"editRef",ref:V,onSuccess:B},null,512),o(Y,{visible:g.value,"onUpdate:visible":e[7]||(e[7]=t=>g.value=t),onBeforeOk:E},{title:s(()=>e[13]||(e[13]=[f("设置用户后台首页")])),default:s(()=>[o(m,{label:"用户首页"},{default:s(()=>[o(H,{modelValue:b.value,"onUpdate:modelValue":e[6]||(e[6]=t=>b.value=t),placeholder:"请选择用户首页",dict:"dashboard"},null,8,["modelValue"])]),_:1})]),_:1},8,["visible"])])}}};export{wt as default};
