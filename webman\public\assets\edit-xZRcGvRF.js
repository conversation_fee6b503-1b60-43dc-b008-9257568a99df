import{a as V}from"./user-BW-rYcwt.js";import{t as z,c as b}from"./index-ybrmzYq5.js";import{M as E}from"./@arco-design-uttiljWv.js";import{r as n,c as H,a as I,h as m,j as J,k as K,l as t,t as e,a1 as x,O as Q}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const $e={__name:"edit",emits:["success"],setup(S,{expose:R,emit:C}){const q=C,_=n(),u=n(""),f=n(!1),v=n(!1),w=n([]),k=n([]),g=n([]);let B=H(()=>"用户管理"+(u.value=="add"?"-新增":"-编辑"));const U={id:"",avatar:"",username:"",nickname:"",dept_id:"",password:"",role_ids:[],phone:"",post_ids:[],email:"",status:1,remark:""},l=I({...U}),G={username:[{required:!0,message:"账户不能为空"}],dept_id:[{required:!0,message:"部门不能为空"}],role_ids:[{required:!0,message:"角色不能为空"}]},L=async(r="add",a="")=>{if(u.value=r,Object.assign(l,U),_.value.clearValidate(),f.value=!0,await O(),r=="edit"){const{data:i}=await V.read(a);if(i.postList){const d=i.postList.map(p=>p.id);i.post_ids=d}const s=i.roleList.map(d=>d.id);i.role_ids=s,i.password="",y(i)}},O=async()=>{const r=await b.commonGet("/core/dept/accessDept");w.value=r.data;const a=await b.commonGet("/core/role/accessRole");k.value=a.data;const i=await b.commonGet("/core/post/accessPost");g.value=i.data},y=async r=>{for(const a in l)r[a]!=null&&r[a]!=null&&(l[a]=r[a])},j=async r=>{var i;if(!await((i=_.value)==null?void 0:i.validate())){v.value=!0;let s={...l},d={};u.value==="add"?(s.id=void 0,d=await V.save(s)):d=await V.update(s.id,s),d.code===200&&(E.success("操作成功"),q("success"),r(!0)),setTimeout(()=>{v.value=!1},500)}r(!1)},F=()=>f.value=!1;return R({open:L,setFormData:y}),(r,a)=>{const i=m("sa-upload-image"),s=m("a-form-item"),d=m("a-col"),p=m("a-row"),c=m("a-input"),D=m("a-tree-select"),M=m("a-input-password"),P=m("a-select"),A=m("sa-radio"),N=m("a-textarea"),T=m("a-form");return K(),J(Q("a-modal"),{visible:f.value,"onUpdate:visible":a[11]||(a[11]=o=>f.value=o),width:x(z).getDevice()==="mobile"?"100%":"800px",title:x(B),"mask-closable":!1,"ok-loading":v.value,onCancel:F,onBeforeOk:j},{default:t(()=>[e(T,{ref_key:"formRef",ref:_,model:l,rules:G,"auto-label-width":!0},{default:t(()=>[e(p,{gutter:16},{default:t(()=>[e(d,{span:24},{default:t(()=>[e(s,{label:"头像",field:"avatar"},{default:t(()=>[e(i,{modelValue:l.avatar,"onUpdate:modelValue":a[0]||(a[0]=o=>l.avatar=o),rounded:!0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:16},{default:t(()=>[e(d,{span:12},{default:t(()=>[e(s,{field:"username",label:"账户"},{default:t(()=>[e(c,{modelValue:l.username,"onUpdate:modelValue":a[1]||(a[1]=o=>l.username=o),disabled:u.value==="edit",placeholder:"请输入账户"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(d,{span:12},{default:t(()=>[e(s,{field:"dept_id",label:"所属部门"},{default:t(()=>[e(D,{modelValue:l.dept_id,"onUpdate:modelValue":a[2]||(a[2]=o=>l.dept_id=o),data:w.value,"field-names":{key:"value",title:"label"},"allow-clear":"",placeholder:"请选择所属部门"},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:16},{default:t(()=>[e(d,{span:12},{default:t(()=>[e(s,{field:"password",label:"密码"},{default:t(()=>[e(M,{modelValue:l.password,"onUpdate:modelValue":a[3]||(a[3]=o=>l.password=o),disabled:u.value==="edit",placeholder:"请输入密码"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(d,{span:12},{default:t(()=>[e(s,{field:"nickname",label:"昵称"},{default:t(()=>[e(c,{modelValue:l.nickname,"onUpdate:modelValue":a[4]||(a[4]=o=>l.nickname=o),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:16},{default:t(()=>[e(d,{span:12},{default:t(()=>[e(s,{field:"role_ids",label:"角色"},{default:t(()=>[e(D,{modelValue:l.role_ids,"onUpdate:modelValue":a[5]||(a[5]=o=>l.role_ids=o),data:k.value,"field-names":{key:"value",title:"label"},"tree-check-strictly":!0,"allow-clear":"","tree-checkable":"",placeholder:"请选择角色"},null,8,["modelValue","data"])]),_:1})]),_:1}),e(d,{span:12},{default:t(()=>[e(s,{field:"phone",label:"手机"},{default:t(()=>[e(c,{modelValue:l.phone,"onUpdate:modelValue":a[6]||(a[6]=o=>l.phone=o),placeholder:"请输入手机"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:16},{default:t(()=>[e(d,{span:12},{default:t(()=>[e(s,{field:"post_ids",label:"岗位"},{default:t(()=>[e(P,{modelValue:l.post_ids,"onUpdate:modelValue":a[7]||(a[7]=o=>l.post_ids=o),options:g.value,"field-names":{label:"name",value:"id"},multiple:"","allow-clear":"",placeholder:"请选择岗位"},null,8,["modelValue","options"])]),_:1})]),_:1}),e(d,{span:12},{default:t(()=>[e(s,{field:"email",label:"邮箱"},{default:t(()=>[e(c,{modelValue:l.email,"onUpdate:modelValue":a[8]||(a[8]=o=>l.email=o),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:16},{default:t(()=>[e(d,{span:24},{default:t(()=>[e(s,{label:"状态",field:"status"},{default:t(()=>[e(A,{modelValue:l.status,"onUpdate:modelValue":a[9]||(a[9]=o=>l.status=o),dict:"data_status",placeholder:"请选择状态"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:16},{default:t(()=>[e(d,{span:24},{default:t(()=>[e(s,{label:"备注",field:"remark"},{default:t(()=>[e(N,{modelValue:l.remark,"onUpdate:modelValue":a[10]||(a[10]=o=>l.remark=o),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},40,["visible","width","title","ok-loading"])}}};export{$e as default};
