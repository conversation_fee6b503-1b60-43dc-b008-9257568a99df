#!/bin/bash
# Sai<PERSON><PERSON><PERSON> Yii 2.0 开发助手

case "$1" in
    "server")
        echo "🌐 启动开发服务器..."
        php -S localhost:8080 -t web/ web/index.php
        ;;
    "migrate")
        echo "🗄️ 运行数据库迁移..."
        php yii migrate
        ;;
    "cache")
        echo "🧹 清除缓存..."
        php yii cache/flush-all
        ;;
    "gii")
        echo "⚙️ 打开Gii代码生成器..."
        echo "访问: http://localhost:8080/gii/"
        ;;
    "debug")
        echo "📊 打开调试工具..."
        echo "访问: http://localhost:8080/debug/"
        ;;
    "test")
        echo "🧪 运行测试..."
        php yii help
        ;;
    "log")
        echo "📋 查看日志..."
        tail -f runtime/logs/app.log
        ;;
    *)
        echo "SaiAdmin Yii 2.0 开发助手"
        echo ""
        echo "用法: $0 {server|migrate|cache|gii|debug|test|log}"
        echo ""
        echo "命令说明:"
        echo "  server  - 启动开发服务器"
        echo "  migrate - 运行数据库迁移"
        echo "  cache   - 清除缓存"
        echo "  gii     - 打开Gii代码生成器"
        echo "  debug   - 打开调试工具"
        echo "  test    - 运行测试"
        echo "  log     - 查看日志"
        ;;
esac