<?php
/**
 * Sai<PERSON>d<PERSON> Yii 2.0 Web 应用配置
 */

$params = require __DIR__ . "/params.php";
$db = require __DIR__ . "/db.php";

$config = [
    "id" => "saiadmin-web",
    "name" => "SaiAdmin",
    "basePath" => dirname(__DIR__),
    "bootstrap" => ["log"],
    "defaultRoute" => "site/index",
    "controllerNamespace" => "app\controllers",
    "aliases" => [
        "@bower" => "@vendor/bower-asset",
        "@npm"   => "@vendor/npm-asset",
    ],
    "components" => [
        "request" => [
            "cookieValidationKey" => "your-secret-key-here",
            "parsers" => [
                "application/json" => "yii\web\JsonParser",
            ]
        ],
        "cache" => [
            "class" => "yii\caching\FileCache",
        ],
        "user" => [
            "identityClass" => "app\models\User",
            "enableAutoLogin" => true,
        ],
        "errorHandler" => [
            "errorAction" => "site/error",
        ],
        "mailer" => [
            "class" => "yii\swiftmailer\Mailer",
            "useFileTransport" => true,
        ],
        "log" => [
            "traceLevel" => YII_DEBUG ? 3 : 0,
            "targets" => [
                [
                    "class" => "yii\log\FileTarget",
                    "levels" => ["error", "warning"],
                ],
            ],
        ],
        "db" => $db,
        "urlManager" => [
            "enablePrettyUrl" => true,
            "showScriptName" => false,
            "rules" => [
                "api/<controller:\w+>/<action:\w+>" => "api/<controller>/<action>",
                "admin/<controller:\w+>/<action:\w+>" => "admin/<controller>/<action>",
                "<controller:\w+>/<action:\w+>" => "<controller>/<action>",
            ],
        ],
    ],
    "modules" => [
        "admin" => [
            "class" => "app\modules\admin\Module",
        ],
        "tool" => [
            "class" => "app\modules\tool\Module",
        ],
        "api" => [
            "class" => "app\modules\api\Module",
        ],
    ],
    "params" => $params,
];

if (YII_ENV_DEV) {
    // 开发环境配置调整
    $config["bootstrap"][] = "debug";
    $config["modules"]["debug"] = [
        "class" => "yii\debug\Module",
        "allowedIPs" => ["127.0.0.1", "::1"],
    ];

    $config["bootstrap"][] = "gii";
    $config["modules"]["gii"] = [
        "class" => "yii\gii\Module",
        "allowedIPs" => ["127.0.0.1", "::1"],
    ];
}

return $config;