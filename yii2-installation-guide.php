<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> Yii 2.0 完整安装部署指南
 */

echo "📖 SaiAdmin Yii 2.0 完整安装部署指南\n";
echo "========================================\n\n";

// 检查当前环境
echo "[1/7] 环境检查...\n";

$requirements = [
    'php_version' => ['required' => '7.4.0', 'current' => PHP_VERSION],
    'extensions' => ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'json', 'fileinfo'],
    'functions' => ['curl_init', 'json_encode', 'mb_strlen']
];

// PHP版本检查
if (version_compare(PHP_VERSION, $requirements['php_version']['required'], '>=')) {
    echo "  ✅ PHP版本: " . PHP_VERSION . " (>= {$requirements['php_version']['required']})\n";
} else {
    echo "  ❌ PHP版本过低: " . PHP_VERSION . " (需要 >= {$requirements['php_version']['required']})\n";
}

// 扩展检查
foreach ($requirements['extensions'] as $ext) {
    if (extension_loaded($ext)) {
        echo "  ✅ {$ext} 扩展已加载\n";
    } else {
        echo "  ❌ {$ext} 扩展未加载\n";
    }
}

// 函数检查
foreach ($requirements['functions'] as $func) {
    if (function_exists($func)) {
        echo "  ✅ {$func} 函数可用\n";
    } else {
        echo "  ❌ {$func} 函数不可用\n";
    }
}

echo "\n[2/7] 项目部署步骤...\n";

$deploySteps = [
    "1. 下载项目" => [
        "描述" => "获取 SaiAdmin Yii 2.0 项目文件",
        "命令" => [
            "# 方式1: 直接复制已生成的项目",
            "cp -r yii2-saiadmin /var/www/html/saiadmin",
            "",
            "# 方式2: 从Git仓库克隆",
            "git clone https://github.com/saiadmin/yii2-saiadmin.git",
            "cd yii2-saiadmin"
        ]
    ],

    "2. 安装依赖" => [
        "描述" => "使用Composer安装PHP依赖包",
        "命令" => [
            "cd yii2-saiadmin",
            "composer install --no-dev --optimize-autoloader",
            "",
            "# 如果遇到内存限制问题",
            "php -d memory_limit=-1 /usr/local/bin/composer install"
        ]
    ],

    "3. 配置权限" => [
        "描述" => "设置目录和文件权限",
        "命令" => [
            "chmod 755 yii",
            "chmod -R 777 runtime/",
            "chmod -R 777 web/assets/",
            "",
            "# 如果使用Apache",
            "chown -R www-data:www-data .",
            "",
            "# 如果使用Nginx",
            "chown -R nginx:nginx ."
        ]
    ],

    "4. 配置数据库" => [
        "描述" => "设置数据库连接参数",
        "命令" => [
            "# 编辑数据库配置文件",
            "nano config/db.php",
            "",
            "# 配置示例:",
            "return [",
            "    'class' => 'yii\\db\\Connection',",
            "    'dsn' => 'mysql:host=localhost;dbname=saiadmin',",
            "    'username' => 'root',",
            "    'password' => 'your_password',",
            "    'charset' => 'utf8mb4',",
            "];"
        ]
    ],

    "5. 运行数据库迁移" => [
        "描述" => "创建数据库表结构",
        "命令" => [
            "# 运行迁移",
            "php yii migrate",
            "",
            "# 如果需要创建新迁移",
            "php yii migrate/create create_user_table",
            "",
            "# 查看迁移历史",
            "php yii migrate/history"
        ]
    ],

    "6. 配置Web服务器" => [
        "描述" => "配置Apache或Nginx虚拟主机",
        "命令" => [
            "# Apache配置 (/etc/apache2/sites-available/saiadmin.conf)",
            "<VirtualHost *:80>",
            "    ServerName saiadmin.local",
            "    DocumentRoot /var/www/html/saiadmin/web",
            "    <Directory /var/www/html/saiadmin/web>",
            "        AllowOverride All",
            "        Require all granted",
            "    </Directory>",
            "</VirtualHost>",
            "",
            "# 启用站点",
            "a2ensite saiadmin.conf",
            "systemctl reload apache2"
        ]
    ],

    "7. 测试部署" => [
        "描述" => "验证部署是否成功",
        "命令" => [
            "# 测试控制台",
            "php yii help",
            "",
            "# 测试Web访问",
            "curl http://saiadmin.local",
            "",
            "# 查看日志",
            "tail -f runtime/logs/app.log"
        ]
    ]
];

foreach ($deploySteps as $step => $details) {
    echo "  📋 {$step}\n";
    echo "     {$details['描述']}\n";
    echo "     命令:\n";
    foreach ($details['命令'] as $cmd) {
        if (empty($cmd)) {
            echo "\n";
        } else {
            echo "     {$cmd}\n";
        }
    }
    echo "\n";
}

echo "[3/7] Web服务器配置详解...\n";

// Apache配置
$apacheConfig = '# Apache 虚拟主机配置
<VirtualHost *:80>
    ServerName saiadmin.local
    ServerAlias www.saiadmin.local
    DocumentRoot /var/www/html/saiadmin/web

    <Directory /var/www/html/saiadmin/web>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted

        # URL重写
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . index.php
    </Directory>

    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/saiadmin_error.log
    CustomLog ${APACHE_LOG_DIR}/saiadmin_access.log combined

    # 安全配置
    <Directory /var/www/html/saiadmin>
        <Files "*.php">
            Require all denied
        </Files>
    </Directory>

    <Directory /var/www/html/saiadmin/web>
        <Files "*.php">
            Require all granted
        </Files>
    </Directory>
</VirtualHost>';

file_put_contents('apache-saiadmin.conf', $apacheConfig);
echo "  ✅ 创建Apache配置: apache-saiadmin.conf\n";

// Nginx配置
$nginxConfig = '# Nginx 服务器配置
server {
    listen 80;
    server_name saiadmin.local www.saiadmin.local;
    root /var/www/html/saiadmin/web;
    index index.php index.html;

    # 字符集
    charset utf-8;

    # 访问日志
    access_log /var/log/nginx/saiadmin_access.log;
    error_log /var/log/nginx/saiadmin_error.log;

    # 主要位置配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;

        # 安全配置
        fastcgi_param HTTP_PROXY "";
        fastcgi_read_timeout 300;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }

    location ~ ^/(protected|framework|themes/\w+/views) {
        deny all;
    }

    # 禁止访问敏感文件
    location ~* \.(htaccess|htpasswd|svn|git) {
        deny all;
    }
}';

file_put_contents('nginx-saiadmin.conf', $nginxConfig);
echo "  ✅ 创建Nginx配置: nginx-saiadmin.conf\n";

echo "\n[4/7] 数据库配置示例...\n";

$dbConfigs = [
    'MySQL' => [
        'dsn' => 'mysql:host=localhost;dbname=saiadmin',
        'username' => 'saiadmin_user',
        'password' => 'secure_password',
        'charset' => 'utf8mb4'
    ],
    'PostgreSQL' => [
        'dsn' => 'pgsql:host=localhost;dbname=saiadmin',
        'username' => 'saiadmin_user',
        'password' => 'secure_password',
        'charset' => 'utf8'
    ],
    'SQLite' => [
        'dsn' => 'sqlite:@app/data/saiadmin.db',
        'username' => '',
        'password' => '',
        'charset' => 'utf8'
    ]
];

foreach ($dbConfigs as $type => $config) {
    echo "  📋 {$type} 配置:\n";
    foreach ($config as $key => $value) {
        echo "    '{$key}' => '{$value}',\n";
    }
    echo "\n";
}

echo "[5/7] 环境配置...\n";

$envConfigs = [
    '开发环境' => [
        'YII_DEBUG' => 'true',
        'YII_ENV' => 'dev',
        '特性' => ['调试工具栏', 'Gii代码生成', '详细错误信息']
    ],
    '测试环境' => [
        'YII_DEBUG' => 'true',
        'YII_ENV' => 'test',
        '特性' => ['单元测试', '功能测试', '模拟数据']
    ],
    '生产环境' => [
        'YII_DEBUG' => 'false',
        'YII_ENV' => 'prod',
        '特性' => ['性能优化', '错误日志', '缓存启用']
    ]
];

foreach ($envConfigs as $env => $config) {
    echo "  📋 {$env}:\n";
    foreach ($config as $key => $value) {
        if ($key === '特性') {
            echo "    特性: " . implode(', ', $value) . "\n";
        } else {
            echo "    {$key}: {$value}\n";
        }
    }
    echo "\n";
}

echo "[6/7] 性能优化建议...\n";

$optimizations = [
    'PHP配置' => [
        'opcache.enable=1',
        'opcache.memory_consumption=128',
        'opcache.max_accelerated_files=4000',
        'realpath_cache_size=4096K'
    ],
    '数据库优化' => [
        '启用查询缓存',
        '优化索引设计',
        '使用连接池',
        '定期分析表'
    ],
    '缓存策略' => [
        '启用文件缓存',
        '使用Redis缓存',
        '配置数据缓存',
        '启用页面缓存'
    ],
    'Web服务器' => [
        '启用Gzip压缩',
        '配置静态文件缓存',
        '使用CDN加速',
        '优化Keep-Alive'
    ]
];

foreach ($optimizations as $category => $items) {
    echo "  📋 {$category}:\n";
    foreach ($items as $item) {
        echo "    - {$item}\n";
    }
    echo "\n";
}

echo "[7/7] 故障排除...\n";

$troubleshooting = [
    '权限问题' => [
        '症状' => '无法写入文件或目录',
        '解决' => 'chmod -R 777 runtime/ web/assets/'
    ],
    '数据库连接失败' => [
        '症状' => 'SQLSTATE[HY000] [2002] Connection refused',
        '解决' => '检查数据库服务状态和连接参数'
    ],
    '404错误' => [
        '症状' => '页面无法找到',
        '解决' => '检查URL重写规则和虚拟主机配置'
    ],
    '500内部错误' => [
        '症状' => 'Internal Server Error',
        '解决' => '查看错误日志，检查PHP语法错误'
    ]
];

foreach ($troubleshooting as $problem => $solution) {
    echo "  🔧 {$problem}:\n";
    echo "     症状: {$solution['症状']}\n";
    echo "     解决: {$solution['解决']}\n\n";
}

// 生成安装脚本
$installScript = '#!/bin/bash
# SaiAdmin Yii 2.0 自动安装脚本

echo "🚀 开始安装 SaiAdmin Yii 2.0..."

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 安装依赖
echo "📦 安装系统依赖..."
apt-get update
apt-get install -y php php-mysql php-mbstring php-xml php-curl composer nginx mysql-server

# 创建项目目录
echo "📁 创建项目目录..."
mkdir -p /var/www/html/saiadmin
cd /var/www/html/saiadmin

# 复制项目文件
echo "📋 复制项目文件..."
cp -r /path/to/yii2-saiadmin/* .

# 安装Composer依赖
echo "📦 安装Composer依赖..."
composer install --no-dev --optimize-autoloader

# 设置权限
echo "🔐 设置权限..."
chmod 755 yii
chmod -R 777 runtime/
chmod -R 777 web/assets/
chown -R www-data:www-data .

# 配置Nginx
echo "🌐 配置Nginx..."
cp nginx-saiadmin.conf /etc/nginx/sites-available/saiadmin
ln -s /etc/nginx/sites-available/saiadmin /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

# 创建数据库
echo "🗄️ 创建数据库..."
mysql -e "CREATE DATABASE saiadmin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER \'saiadmin_user\'@\'localhost\' IDENTIFIED BY \'secure_password\';"
mysql -e "GRANT ALL PRIVILEGES ON saiadmin.* TO \'saiadmin_user\'@\'localhost\';"
mysql -e "FLUSH PRIVILEGES;"

echo "✅ 安装完成！"
echo "🌐 访问地址: http://localhost"
echo "🔧 控制台: php yii help"';

file_put_contents('install-saiadmin.sh', $installScript);
chmod('install-saiadmin.sh', 0755);
echo "  ✅ 创建自动安装脚本: install-saiadmin.sh\n";

echo "\n========================================\n";
echo "📖 安装指南生成完成！\n";
echo "========================================\n\n";

echo "📁 生成的文件:\n";
echo "✅ apache-saiadmin.conf - Apache虚拟主机配置\n";
echo "✅ nginx-saiadmin.conf - Nginx服务器配置\n";
echo "✅ install-saiadmin.sh - 自动安装脚本\n\n";

echo "🚀 快速开始:\n";
echo "1. 运行自动安装: sudo ./install-saiadmin.sh\n";
echo "2. 或按照上述步骤手动安装\n";
echo "3. 配置数据库连接\n";
echo "4. 运行数据库迁移\n";
echo "5. 访问应用开始开发\n\n";

echo "📞 技术支持:\n";
echo "- 文档: https://www.yiiframework.com/doc/guide/2.0/zh-cn\n";
echo "- 社区: https://forum.yiiframework.com\n";
echo "- GitHub: https://github.com/saiadmin/yii2-saiadmin\n\n";

echo "🎉 SaiAdmin Yii 2.0 安装指南已准备就绪！\n";
