<?php
/**
 * 缓存演示视图
 */
use yii\helpers\Html;

$this->title = "缓存演示";
?>

<div class="demo-cache">
    <h1><?= Html::encode($this->title) ?></h1>
    
    <div class="row">
        <div class="col-md-6">
            <h4>💾 缓存数据</h4>
            <table class="table table-bordered">
                <?php foreach ($cache_data as $key => $value): ?>
                    <tr>
                        <td><strong><?= Html::encode($key) ?></strong></td>
                        <td><?= Html::encode($value) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="col-md-6">
            <h4>ℹ️ 缓存信息</h4>
            <table class="table table-bordered">
                <?php foreach ($cache_info as $key => $value): ?>
                    <tr>
                        <td><strong><?= Html::encode($key) ?></strong></td>
                        <td><?= Html::encode($value) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    </div>
    
    <div class="alert alert-info">
        <strong>提示:</strong> 刷新页面查看缓存效果，数据在缓存有效期内不会改变。
    </div>
    
    <div class="mt-3">
        <?= Html::a("刷新页面", ["demo/cache"], ["class" => "btn btn-primary"]) ?>
        <?= Html::a("返回", ["demo/index"], ["class" => "btn btn-secondary"]) ?>
    </div>
</div>