[2025-08-02 21:41:13] default.ERROR: GET /core/system/statistics
[request_param]: []
[timestamp]: 2025-08-02 21:41:13
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: TypeError
[exception_info]: 
TypeError: think\db\PDOConnection::getQueryClass(): Return value must be of type string, array returned in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:232
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(166): think\db\PDOConnection->getQueryClass()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(200): think\db\Connection->newQuery()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(409): think\db\Connection->name('SystemUser')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\SoftDelete.php(31): think\Model->db(Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): plugin\saiadmin\basic\BaseModel->db()
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#8 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\system\SystemUser))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\system\SystemUser))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\SystemUserLogic.php(29): think\Model->__construct()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\SystemController.php(257): plugin\saiadmin\app\logic\system\SystemUserLogic->__construct()
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\SystemController->statistics(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#21 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/core/system/st...', 'GET/core/system...', Object(support\Request), 200)
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #346)
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#28 {main} [] []
[2025-08-02 21:41:13] default.ERROR: GET /core/system/loginChart
[request_param]: []
[timestamp]: 2025-08-02 21:41:13
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: TypeError
[exception_info]: 
TypeError: think\db\PDOConnection::getQueryClass(): Return value must be of type string, array returned in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:232
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(166): think\db\PDOConnection->getQueryClass()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(200): think\db\Connection->newQuery()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(409): think\db\Connection->name('SystemLoginLog')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\SoftDelete.php(31): think\Model->db(Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): plugin\saiadmin\basic\BaseModel->db()
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#8 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\system\SystemLoginLog))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\system\SystemLoginLog))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\SystemLoginLogLogic.php(24): think\Model->__construct()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\SystemController.php(279): plugin\saiadmin\app\logic\system\SystemLoginLogLogic->__construct()
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\SystemController->loginChart(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#21 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/core/system/lo...', 'GET/core/system...', Object(support\Request), 200)
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #347)
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#28 {main} [] []
[2025-08-02 21:41:13] default.ERROR: GET /core/system/notice?limit=5&orderBy=id&orderType=desc
[request_param]: {"limit":"5","orderBy":"id","orderType":"desc"}
[timestamp]: 2025-08-02 21:41:13
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: TypeError
[exception_info]: 
TypeError: think\db\PDOConnection::getQueryClass(): Return value must be of type string, array returned in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:232
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(166): think\db\PDOConnection->getQueryClass()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(200): think\db\Connection->newQuery()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(409): think\db\Connection->name('SystemNotice')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\SoftDelete.php(31): think\Model->db(Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): plugin\saiadmin\basic\BaseModel->db()
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#8 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\system\SystemNotice))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\system\SystemNotice))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\SystemNoticeLogic.php(23): think\Model->__construct()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\SystemController.php(296): plugin\saiadmin\app\logic\system\SystemNoticeLogic->__construct()
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\SystemController->systemNotice(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#21 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/core/system/no...', 'GET/core/system...', Object(support\Request), 200)
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #348)
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#28 {main} [] []
[2025-08-02 21:41:16] default.ERROR: GET /core/system/getLoginLogList?limit=5&orderBy=login_time&orderType=desc
[request_param]: {"limit":"5","orderBy":"login_time","orderType":"desc"}
[timestamp]: 2025-08-02 21:41:16
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: TypeError
[exception_info]: 
TypeError: think\db\PDOConnection::getQueryClass(): Return value must be of type string, array returned in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:232
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(166): think\db\PDOConnection->getQueryClass()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(200): think\db\Connection->newQuery()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(409): think\db\Connection->name('SystemLoginLog')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\SoftDelete.php(31): think\Model->db(Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): plugin\saiadmin\basic\BaseModel->db()
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#8 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\system\SystemLoginLog))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\system\SystemLoginLog))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\SystemLoginLogLogic.php(24): think\Model->__construct()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\SystemController.php(217): plugin\saiadmin\app\logic\system\SystemLoginLogLogic->__construct()
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\SystemController->getLoginLogList(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#21 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/core/system/ge...', 'GET/core/system...', Object(support\Request), 200)
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #416)
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#28 {main} [] []
[2025-08-02 21:41:16] default.ERROR: GET /core/system/getOperationLogList?limit=5&orderBy=create_time&orderType=desc
[request_param]: {"limit":"5","orderBy":"create_time","orderType":"desc"}
[timestamp]: 2025-08-02 21:41:16
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: TypeError
[exception_info]: 
TypeError: think\db\PDOConnection::getQueryClass(): Return value must be of type string, array returned in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:232
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(166): think\db\PDOConnection->getQueryClass()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\Connection.php(200): think\db\Connection->newQuery()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(409): think\db\Connection->name('SystemOperLog')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\SoftDelete.php(31): think\Model->db(Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(1178): plugin\saiadmin\basic\BaseModel->db()
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(82): think\Model->__call('getFieldType', Array)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\model\concern\TimeStamp.php(62): think\Model->checkTimeFieldType(true)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\DbManager.php(108): think\Model->isAutoWriteTimestamp(true)
#8 [internal function]: think\DbManager->think\{closure}(Object(plugin\saiadmin\app\model\system\SystemOperLog))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\Model.php(272): call_user_func(Object(Closure), Object(plugin\saiadmin\app\model\system\SystemOperLog))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\SystemOperLogLogic.php(23): think\Model->__construct()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\SystemController.php(230): plugin\saiadmin\app\logic\system\SystemOperLogLogic->__construct()
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\SystemController->getOperationLogList(Object(support\Request))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#20 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(663): Webman\App::Webman\{closure}(Object(support\Request))
#21 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(158): Webman\App::findRoute(Object(Workerman\Connection\TcpConnection), '/core/system/ge...', 'GET/core/system...', Object(support\Request), 200)
#22 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #417)
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#28 {main} [] []
