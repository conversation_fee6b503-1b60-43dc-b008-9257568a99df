[2025-08-02 22:54:03] default.ERROR: GET /core/database/index?page=1&limit=10&name=&source=mysql_read
[request_param]: {"page":"1","limit":"10","name":"","source":"mysql_read"}
[timestamp]: 2025-08-02 22:54:03
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: think\db\exception\PDOException
[exception_info]: 
think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'readonly_user'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(746): think\db\PDOConnection->getPDOStatement('show table stat...', Array, false, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(685): think\db\PDOConnection->pdoQuery(Object(think\db\Query), 'show table stat...', false)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\DatabaseLogic.php(41): think\db\PDOConnection->query('show table stat...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\DatabaseLogic.php(28): plugin\saiadmin\app\logic\system\DatabaseLogic->getTableList(Array, '1', '10')
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\system\DataBaseController.php(39): plugin\saiadmin\app\logic\system\DatabaseLogic->getList(Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\system\DataBaseController->index(Object(support\Request))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(750): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #527)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#20 {main} [] []
[2025-08-02 22:54:08] default.ERROR: GET /core/database/index?page=1&limit=10&name=&source=mysql_read
[request_param]: {"page":"1","limit":"10","name":"","source":"mysql_read"}
[timestamp]: 2025-08-02 22:54:08
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: think\db\exception\PDOException
[exception_info]: 
think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'readonly_user'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(746): think\db\PDOConnection->getPDOStatement('show table stat...', Array, false, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(685): think\db\PDOConnection->pdoQuery(Object(think\db\Query), 'show table stat...', false)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\DatabaseLogic.php(41): think\db\PDOConnection->query('show table stat...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\DatabaseLogic.php(28): plugin\saiadmin\app\logic\system\DatabaseLogic->getTableList(Array, '1', '10')
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\system\DataBaseController.php(39): plugin\saiadmin\app\logic\system\DatabaseLogic->getList(Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\system\DataBaseController->index(Object(support\Request))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #536)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#20 {main} [] []
[2025-08-02 22:54:13] default.ERROR: GET /core/database/index?page=1&limit=10&name=&source=mysql_read
[request_param]: {"page":"1","limit":"10","name":"","source":"mysql_read"}
[timestamp]: 2025-08-02 22:54:13
[client_ip]: 127.0.0.1
[action_user]: array (
  'id' => 1,
  'username' => 'admin',
  'type' => 'pc',
)
[exception_handle]: think\db\exception\PDOException
[exception_info]: 
think\db\exception\PDOException: SQLSTATE[HY000] [1045] Access denied for user 'readonly_user'@'localhost' (using password: YES) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php:836
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(746): think\db\PDOConnection->getPDOStatement('show table stat...', Array, false, false)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\topthink\think-orm\src\db\PDOConnection.php(685): think\db\PDOConnection->pdoQuery(Object(think\db\Query), 'show table stat...', false)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\DatabaseLogic.php(41): think\db\PDOConnection->query('show table stat...')
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\logic\system\DatabaseLogic.php(28): plugin\saiadmin\app\logic\system\DatabaseLogic->getTableList(Array, '1', '10')
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\controller\system\DataBaseController.php(39): plugin\saiadmin\app\logic\system\DatabaseLogic->getList(Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(336): plugin\saiadmin\app\controller\system\DataBaseController->index(Object(support\Request))
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}(Object(support\Request))
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\SystemLog.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\SystemLog->process(Object(support\Request), Object(Closure))
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckAuth.php(40): Webman\App::Webman\{closure}(Object(support\Request))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckAuth->process(Object(support\Request), Object(Closure))
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\plugin\saiadmin\app\middleware\CheckLogin.php(37): Webman\App::Webman\{closure}(Object(support\Request))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(352): plugin\saiadmin\app\middleware\CheckLogin->process(Object(support\Request), Object(Closure))
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}(Object(support\Request))
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(679): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Events\Select.php(406): Workerman\Connection\TcpConnection->baseRead(Resource id #539)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1613): Workerman\Events\Select->run()
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(1528): Workerman\Worker::forkWorkersForWindows()
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\vendor\workerman\workerman\src\Worker.php(594): Workerman\Worker::forkWorkers()
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#20 {main} [] []
