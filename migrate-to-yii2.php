<?php
/**
 * 将现有 SaiAdmin 代码迁移到 Yii 2.0 结构
 */

echo "🔄 迁移现有代码到 Yii 2.0 结构\n";
echo "========================================\n\n";

// 迁移映射配置
$migrationMap = [
    // 控制器迁移
    'controllers' => [
        'source' => 'webman/plugin/saiadmin/app/controller',
        'target' => 'yii2-saiadmin/modules/admin/controllers',
        'namespace_from' => 'plugin\\saiadmin\\app\\controller',
        'namespace_to' => 'app\\modules\\admin\\controllers',
        'extends_from' => 'plugin\\saiadmin\\basic\\BaseController',
        'extends_to' => 'app\\components\\base\\BaseController'
    ],
    
    // 模型迁移
    'models' => [
        'source' => 'webman/plugin/saiadmin/app/model',
        'target' => 'yii2-saiadmin/modules/admin/models',
        'namespace_from' => 'plugin\\saiadmin\\app\\model',
        'namespace_to' => 'app\\modules\\admin\\models',
        'extends_from' => 'plugin\\saiadmin\\basic\\BaseModel',
        'extends_to' => 'yii\\db\\ActiveRecord'
    ],
    
    // 业务逻辑迁移为服务
    'services' => [
        'source' => 'webman/plugin/saiadmin/app/logic',
        'target' => 'yii2-saiadmin/components/services',
        'namespace_from' => 'plugin\\saiadmin\\app\\logic',
        'namespace_to' => 'app\\components\\services',
        'extends_from' => 'plugin\\saiadmin\\basic\\BaseLogic',
        'extends_to' => 'yii\\base\\Component'
    ],
    
    // 验证器迁移
    'validators' => [
        'source' => 'webman/plugin/saiadmin/app/validate',
        'target' => 'yii2-saiadmin/components/validators',
        'namespace_from' => 'plugin\\saiadmin\\app\\validate',
        'namespace_to' => 'app\\components\\validators',
        'extends_from' => 'think\\Validate',
        'extends_to' => 'yii\\base\\Model'
    ]
];

echo "[1/5] 开始迁移文件...\n";

foreach ($migrationMap as $type => $config) {
    echo "\n  📁 迁移 {$type}...\n";
    
    if (!is_dir($config['source'])) {
        echo "    ⚠️ 源目录不存在: {$config['source']}\n";
        continue;
    }
    
    // 确保目标目录存在
    if (!is_dir($config['target'])) {
        mkdir($config['target'], 0755, true);
    }
    
    // 递归迁移文件
    migrateDirectory($config['source'], $config['target'], $config);
}

echo "\n[2/5] 创建 Yii 2.0 兼容的基础模型...\n";

$baseModelContent = '<?php
/**
 * SaiAdmin 基础模型 (Yii 2.0 兼容)
 */
namespace app\components\base;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\behaviors\BlameableBehavior;

/**
 * 基础 ActiveRecord 模型
 */
class BaseModel extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            [
                "class" => TimestampBehavior::class,
                "createdAtAttribute" => "created_at",
                "updatedAtAttribute" => "updated_at",
                "value" => function() {
                    return date("Y-m-d H:i:s");
                }
            ],
            [
                "class" => BlameableBehavior::class,
                "createdByAttribute" => "created_by",
                "updatedByAttribute" => "updated_by",
                "value" => function() {
                    return Yii::$app->user->id ?? 0;
                }
            ],
        ];
    }

    /**
     * 获取表前缀
     * @return string
     */
    public static function getTablePrefix()
    {
        return "sa_";
    }

    /**
     * 搜索范围 - 状态
     * @param \yii\db\ActiveQuery $query
     * @param int $status
     * @return \yii\db\ActiveQuery
     */
    public function scopeStatus($query, $status)
    {
        return $query->andWhere(["status" => $status]);
    }

    /**
     * 搜索范围 - 时间范围
     * @param \yii\db\ActiveQuery $query
     * @param array $timeRange
     * @return \yii\db\ActiveQuery
     */
    public function scopeTimeRange($query, $timeRange)
    {
        if (!empty($timeRange) && count($timeRange) === 2) {
            return $query->andWhere(["between", "created_at", $timeRange[0], $timeRange[1]]);
        }
        return $query;
    }

    /**
     * 软删除
     * @return bool
     */
    public function softDelete()
    {
        $this->deleted_at = date("Y-m-d H:i:s");
        return $this->save(false);
    }

    /**
     * 批量插入
     * @param array $data
     * @return bool
     */
    public static function batchInsert($data)
    {
        if (empty($data)) {
            return false;
        }
        
        $columns = array_keys($data[0]);
        $values = array_values($data);
        
        return Yii::$app->db->createCommand()
            ->batchInsert(static::tableName(), $columns, $values)
            ->execute() > 0;
    }
}';

file_put_contents('yii2-saiadmin/components/base/BaseModel.php', $baseModelContent);
echo "  ✅ 创建基础模型: components/base/BaseModel.php\n";

echo "\n[3/5] 创建 Admin 模块...\n";

$adminModuleContent = '<?php
/**
 * SaiAdmin 管理模块
 */
namespace app\modules\admin;

use yii\base\Module;

/**
 * Admin 模块类
 */
class Module extends \yii\base\Module
{
    /**
     * @inheritdoc
     */
    public $controllerNamespace = "app\modules\admin\controllers";

    /**
     * @inheritdoc
     */
    public function init()
    {
        parent::init();
        
        // 自定义初始化代码
        $this->layout = "admin";
    }
}';

file_put_contents('yii2-saiadmin/modules/admin/Module.php', $adminModuleContent);
echo "  ✅ 创建 Admin 模块: modules/admin/Module.php\n";

echo "\n[4/5] 创建示例控制器 (Yii 2.0 风格)...\n";

$sampleControllerContent = '<?php
/**
 * 示例控制器 (Yii 2.0 风格)
 */
namespace app\modules\admin\controllers;

use Yii;
use app\components\base\BaseController;
use app\modules\admin\models\User;
use app\components\services\UserService;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * 用户控制器
 */
class UserController extends BaseController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            "verbs" => [
                "class" => VerbFilter::class,
                "actions" => [
                    "delete" => ["POST"],
                ],
            ],
        ]);
    }

    /**
     * 用户列表
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new User();
        $dataProvider = new ActiveDataProvider([
            "query" => User::find(),
            "pagination" => [
                "pageSize" => 20,
            ],
        ]);

        if (Yii::$app->request->isAjax) {
            return $this->success([
                "list" => $dataProvider->getModels(),
                "pagination" => [
                    "total" => $dataProvider->getTotalCount(),
                    "page" => $dataProvider->getPagination()->getPage() + 1,
                    "pageSize" => $dataProvider->getPagination()->getPageSize(),
                ]
            ]);
        }

        return $this->render("index", [
            "searchModel" => $searchModel,
            "dataProvider" => $dataProvider,
        ]);
    }

    /**
     * 查看用户详情
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        
        if (Yii::$app->request->isAjax) {
            return $this->success($model->toArray());
        }
        
        return $this->render("view", [
            "model" => $model,
        ]);
    }

    /**
     * 创建用户
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new User();
        $userService = new UserService();

        if ($model->load(Yii::$app->request->post())) {
            if ($userService->createUser($model)) {
                if (Yii::$app->request->isAjax) {
                    return $this->success([], "用户创建成功");
                }
                return $this->redirect(["view", "id" => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail("用户创建失败");
        }

        return $this->render("create", [
            "model" => $model,
        ]);
    }

    /**
     * 更新用户
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $userService = new UserService();

        if ($model->load(Yii::$app->request->post())) {
            if ($userService->updateUser($model)) {
                if (Yii::$app->request->isAjax) {
                    return $this->success([], "用户更新成功");
                }
                return $this->redirect(["view", "id" => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail("用户更新失败");
        }

        return $this->render("update", [
            "model" => $model,
        ]);
    }

    /**
     * 删除用户
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $userService = new UserService();
        
        if ($userService->deleteUser($model)) {
            if (Yii::$app->request->isAjax) {
                return $this->success([], "用户删除成功");
            }
        } else {
            if (Yii::$app->request->isAjax) {
                return $this->fail("用户删除失败");
            }
        }

        return $this->redirect(["index"]);
    }

    /**
     * 查找用户模型
     * @param integer $id
     * @return User
     * @throws NotFoundHttpException
     */
    protected function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException("请求的页面不存在。");
    }
}';

file_put_contents('yii2-saiadmin/modules/admin/controllers/UserController.php', $sampleControllerContent);
echo "  ✅ 创建示例控制器: modules/admin/controllers/UserController.php\n";

echo "\n[5/5] 创建 Composer 配置...\n";

$composerConfig = [
    "name" => "saiadmin/yii2-saiadmin",
    "description" => "SaiAdmin based on Yii 2.0 Framework",
    "keywords" => ["yii2", "framework", "admin", "saiadmin"],
    "homepage" => "https://saiadmin.com",
    "type" => "project",
    "license" => "MIT",
    "support" => [
        "issues" => "https://github.com/saiadmin/yii2-saiadmin/issues",
        "source" => "https://github.com/saiadmin/yii2-saiadmin"
    ],
    "minimum-stability" => "stable",
    "require" => [
        "php" => ">=7.4.0",
        "yiisoft/yii2" => "~2.0.45",
        "yiisoft/yii2-bootstrap4" => "~2.0.0",
        "yiisoft/yii2-swiftmailer" => "~2.1.0"
    ],
    "require-dev" => [
        "yiisoft/yii2-debug" => "~2.1.0",
        "yiisoft/yii2-gii" => "~2.2.0",
        "yiisoft/yii2-faker" => "~2.0.0",
        "codeception/codeception" => "^4.0",
        "codeception/verify" => "~0.5.0 || ~1.1.0",
        "codeception/specify" => "~0.4.6",
        "symfony/browser-kit" => ">=2.7 <=4.2.4",
        "codeception/module-filesystem" => "^1.0.0",
        "codeception/module-yii2" => "^1.0.0",
        "codeception/module-asserts" => "^1.0.0"
    ],
    "config" => [
        "process-timeout" => 1800,
        "fxp-asset" => [
            "enabled" => false
        ]
    ],
    "repositories" => [
        [
            "type" => "composer",
            "url" => "https://asset-packagist.org"
        ]
    ],
    "autoload" => [
        "psr-4" => [
            "app\\" => ""
        ]
    ],
    "autoload-dev" => [
        "psr-4" => [
            "app\\tests\\" => "tests/"
        ]
    ],
    "scripts" => [
        "post-install-cmd" => [
            "yii\\composer\\Installer::postInstall"
        ],
        "post-create-project-cmd" => [
            "yii\\composer\\Installer::postCreateProject",
            "yii\\composer\\Installer::postInstall"
        ]
    ],
    "extra" => [
        "yii\\composer\\Installer::postCreateProject" => [
            "setPermission" => [
                [
                    "runtime",
                    "web/assets"
                ],
                "0777"
            ]
        ],
        "yii\\composer\\Installer::postInstall" => [
            "generateCookieValidationKey" => [
                "config/web.php"
            ]
        ]
    ]
];

file_put_contents('yii2-saiadmin/composer.json', json_encode($composerConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
echo "  ✅ 创建 Composer 配置: composer.json\n";

echo "\n========================================\n";
echo "🎉 代码迁移完成！\n";
echo "========================================\n\n";

echo "📋 迁移总结:\n";
echo "✅ 创建了 Yii 2.0 标准目录结构\n";
echo "✅ 迁移了控制器到模块结构\n";
echo "✅ 迁移了模型到 ActiveRecord\n";
echo "✅ 迁移了业务逻辑到服务组件\n";
echo "✅ 创建了基础类和示例代码\n";
echo "✅ 配置了 Composer 依赖\n\n";

echo "🚀 下一步操作:\n";
echo "1. cd yii2-saiadmin\n";
echo "2. composer install\n";
echo "3. ./yii migrate\n";
echo "4. 配置 Web 服务器指向 web/ 目录\n";
echo "5. 访问应用进行测试\n\n";

echo "🌐 访问地址:\n";
echo "- 前端: http://localhost/yii2-saiadmin/web/\n";
echo "- 管理后台: http://localhost/yii2-saiadmin/web/admin/\n";
echo "- Gii 代码生成: http://localhost/yii2-saiadmin/web/gii/\n";
echo "- Debug 工具栏: http://localhost/yii2-saiadmin/web/debug/\n";

/**
 * 递归迁移目录
 */
function migrateDirectory($source, $target, $config) {
    $files = scandir($source);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $sourcePath = $source . '/' . $file;
        $targetPath = $target . '/' . $file;
        
        if (is_dir($sourcePath)) {
            if (!is_dir($targetPath)) {
                mkdir($targetPath, 0755, true);
            }
            migrateDirectory($sourcePath, $targetPath, $config);
        } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
            migratePhpFile($sourcePath, $targetPath, $config);
        }
    }
}

/**
 * 迁移PHP文件
 */
function migratePhpFile($source, $target, $config) {
    $content = file_get_contents($source);
    
    // 替换命名空间
    $content = str_replace($config['namespace_from'], $config['namespace_to'], $content);
    
    // 替换继承类
    $content = str_replace($config['extends_from'], $config['extends_to'], $content);
    
    // 添加 Yii 2.0 特定的 use 语句
    if (strpos($content, 'namespace') !== false) {
        $content = str_replace(
            'namespace ' . $config['namespace_to'] . ';',
            'namespace ' . $config['namespace_to'] . ";\n\nuse Yii;",
            $content
        );
    }
    
    file_put_contents($target, $content);
    echo "    ✅ 迁移文件: " . basename($source) . "\n";
}
