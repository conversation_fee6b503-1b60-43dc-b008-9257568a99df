<template>
  <component
    is="a-modal"
    :width="tool.getDevice() === 'mobile' ? '100%' : '600px'"
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :ok-loading="loading"
    @cancel="close"
    @before-ok="submit">
    <!-- 表单信息 start -->
    <a-form ref="formRef" :model="formData" :rules="rules" :auto-label-width="true">
      <a-form-item label="" field="mch_id">
        <a-input v-model="formData.mch_id" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="售价" field="price">
        <a-input v-model="formData.price" placeholder="请输入售价" />
      </a-form-item>
      <a-form-item label="是否使用规格：0=不使用，1=使用" field="use_attr">
        <a-input v-model="formData.use_attr" placeholder="请输入是否使用规格：0=不使用，1=使用" />
      </a-form-item>
      <a-form-item label="商品规格组" field="attr_groups">
        <ma-wangEditor v-model="formData.attr_groups" :height="400" />
      </a-form-item>
      <a-form-item label="商品库存" field="goods_stock">
        <a-input v-model="formData.goods_stock" placeholder="请输入商品库存" />
      </a-form-item>
      <a-form-item label="已出售量" field="virtual_sales">
        <a-input v-model="formData.virtual_sales" placeholder="请输入已出售量" />
      </a-form-item>
      <a-form-item label="购物数量限制" field="confine_count">
        <a-input v-model="formData.confine_count" placeholder="请输入购物数量限制" />
      </a-form-item>
      <a-form-item label="单品满件包邮" field="pieces">
        <a-input v-model="formData.pieces" placeholder="请输入单品满件包邮" />
      </a-form-item>
      <a-form-item label="单口满额包邮" field="forehead">
        <a-input v-model="formData.forehead" placeholder="请输入单口满额包邮" />
      </a-form-item>
      <a-form-item label="包邮模板ID" field="shipping_id">
        <a-input v-model="formData.shipping_id" placeholder="请输入包邮模板ID" />
      </a-form-item>
      <a-form-item label="运费模板ID" field="freight_id">
        <a-input v-model="formData.freight_id" placeholder="请输入运费模板ID" />
      </a-form-item>
      <a-form-item label="赠送积分" field="give_integral">
        <a-input v-model="formData.give_integral" placeholder="请输入赠送积分" />
      </a-form-item>
      <a-form-item label="赠送积分类型1.固定值 |2.百分比" field="give_integral_type">
        <a-input v-model="formData.give_integral_type" placeholder="请输入赠送积分类型1.固定值 |2.百分比" />
      </a-form-item>
      <a-form-item label="赠送余额" field="give_balance">
        <a-input v-model="formData.give_balance" placeholder="请输入赠送余额" />
      </a-form-item>
      <a-form-item label="赠送余额类型1.固定值 |2.百分比" field="give_balance_type">
        <a-input v-model="formData.give_balance_type" placeholder="请输入赠送余额类型1.固定值 |2.百分比" />
      </a-form-item>
      <a-form-item label="可抵扣积分" field="forehead_integral">
        <a-input v-model="formData.forehead_integral" placeholder="请输入可抵扣积分" />
      </a-form-item>
      <a-form-item label="可抵扣积分类型1.固定值 |2.百分比" field="forehead_integral_type">
        <a-input v-model="formData.forehead_integral_type" placeholder="请输入可抵扣积分类型1.固定值 |2.百分比" />
      </a-form-item>
      <a-form-item label="允许多件累计折扣" field="accumulative">
        <a-input v-model="formData.accumulative" placeholder="请输入允许多件累计折扣" />
      </a-form-item>
      <a-form-item label="是否单独分销设置：0=否，1=是" field="individual_share">
        <a-input v-model="formData.individual_share" placeholder="请输入是否单独分销设置：0=否，1=是" />
      </a-form-item>
      <a-form-item label="分销设置类型 0.普通设置|1.详细设置" field="attr_setting_type">
        <a-input v-model="formData.attr_setting_type" placeholder="请输入分销设置类型 0.普通设置|1.详细设置" />
      </a-form-item>
      <a-form-item label="是否享受会员价购买" field="is_level">
        <a-input v-model="formData.is_level" placeholder="请输入是否享受会员价购买" />
      </a-form-item>
      <a-form-item label="是否单独设置会员价" field="is_level_alone">
        <a-input v-model="formData.is_level_alone" placeholder="请输入是否单独设置会员价" />
      </a-form-item>
      <a-form-item label="佣金配比 0--固定金额 1--百分比" field="share_type">
        <a-input v-model="formData.share_type" placeholder="请输入佣金配比 0--固定金额 1--百分比" />
      </a-form-item>
      <a-form-item label="自定义分享图片" field="app_share_pic">
        <a-input v-model="formData.app_share_pic" placeholder="请输入自定义分享图片" />
      </a-form-item>
      <a-form-item label="自定义分享标题" field="app_share_title">
        <a-input v-model="formData.app_share_title" placeholder="请输入自定义分享标题" />
      </a-form-item>
      <a-form-item label="默认服务 0.否|1.是" field="is_default_services">
        <a-input v-model="formData.is_default_services" placeholder="请输入默认服务 0.否|1.是" />
      </a-form-item>
      <a-form-item label="" field="updated_at">
        <a-input v-model="formData.updated_at" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="deleted_at">
        <a-input v-model="formData.deleted_at" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="支付人数" field="payment_people">
        <a-input v-model="formData.payment_people" placeholder="请输入支付人数" />
      </a-form-item>
      <a-form-item label="支付件数" field="payment_num">
        <a-input v-model="formData.payment_num" placeholder="请输入支付件数" />
      </a-form-item>
      <a-form-item label="支付金额" field="payment_amount">
        <a-input v-model="formData.payment_amount" placeholder="请输入支付金额" />
      </a-form-item>
      <a-form-item label="支付订单数" field="payment_order">
        <a-input v-model="formData.payment_order" placeholder="请输入支付订单数" />
      </a-form-item>
      <a-form-item label="" field="confine_order_count">
        <a-input v-model="formData.confine_order_count" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="是否单独区域购买" field="is_area_limit">
        <a-input v-model="formData.is_area_limit" placeholder="请输入是否单独区域购买" />
      </a-form-item>
      <a-form-item label="区域限制" field="area_limit">
        <ma-wangEditor v-model="formData.area_limit" :height="400" />
      </a-form-item>
      <a-form-item label="自定义表单id 0--表示默认表单 -1--表示不使用表单" field="form_id">
        <a-input v-model="formData.form_id" placeholder="请输入自定义表单id 0--表示默认表单 -1--表示不使用表单" />
      </a-form-item>
      <a-form-item label="详情浏览量统计" field="detail_count">
        <a-input v-model="formData.detail_count" placeholder="请输入详情浏览量统计" />
      </a-form-item>
      <a-form-item label="商品服务标题" field="guarantee_title">
        <a-input v-model="formData.guarantee_title" placeholder="请输入商品服务标题" />
      </a-form-item>
      <a-form-item label="商品服务标识" field="guarantee_pic">
        <a-input v-model="formData.guarantee_pic" placeholder="请输入商品服务标识" />
      </a-form-item>
    </a-form>
    <!-- 表单信息 end -->
  </component>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import tool from '@/utils/tool'
import { Message, Modal } from '@arco-design/web-vue'
import api from '../../api/goods/goods'

const emit = defineEmits(['success'])
// 引用定义
const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const mode = ref('')

let title = computed(() => {
  return '商品通用信息表' + (mode.value == 'add' ? '-新增' : '-编辑')
})

// 表单初始值
const initialFormData = {
  id: null,
  mch_id: null,
  price: '0.00',
  use_attr: 1,
  attr_groups: '',
  goods_stock: null,
  virtual_sales: null,
  confine_count: -1,
  pieces: null,
  forehead: '0.00',
  shipping_id: null,
  freight_id: null,
  give_integral: null,
  give_integral_type: 1,
  give_balance: '0.00',
  give_balance_type: 1,
  forehead_integral: '0.00',
  forehead_integral_type: 1,
  accumulative: null,
  individual_share: null,
  attr_setting_type: null,
  is_level: null,
  is_level_alone: null,
  share_type: null,
  app_share_pic: '',
  app_share_title: '',
  is_default_services: 1,
  updated_at: '',
  deleted_at: '',
  payment_people: null,
  payment_num: null,
  payment_amount: '0.00',
  payment_order: null,
  confine_order_count: -1,
  is_area_limit: null,
  area_limit: '',
  form_id: null,
  detail_count: null,
  guarantee_title: '',
  guarantee_pic: '',
}

// 表单信息
const formData = reactive({ ...initialFormData })

// 验证规则
const rules = {
  mall_id: [{ required: true, message: '必需填写' }],
  mch_id: [{ required: true, message: '必需填写' }],
  goods_warehouse_id: [{ required: true, message: '必需填写' }],
  status: [{ required: true, message: '上架状态：0=下架，1=上架必需填写' }],
  price: [{ required: true, message: '售价必需填写' }],
  use_attr: [{ required: true, message: '是否使用规格：0=不使用，1=使用必需填写' }],
  attr_groups: [{ required: true, message: '商品规格组必需填写' }],
  goods_stock: [{ required: true, message: '商品库存必需填写' }],
  virtual_sales: [{ required: true, message: '已出售量必需填写' }],
  confine_count: [{ required: true, message: '购物数量限制必需填写' }],
  pieces: [{ required: true, message: '单品满件包邮必需填写' }],
  forehead: [{ required: true, message: '单口满额包邮必需填写' }],
  shipping_id: [{ required: true, message: '包邮模板ID必需填写' }],
  freight_id: [{ required: true, message: '运费模板ID必需填写' }],
  give_integral: [{ required: true, message: '赠送积分必需填写' }],
  give_integral_type: [{ required: true, message: '赠送积分类型1.固定值 |2.百分比必需填写' }],
  give_balance: [{ required: true, message: '赠送余额必需填写' }],
  give_balance_type: [{ required: true, message: '赠送余额类型1.固定值 |2.百分比必需填写' }],
  forehead_integral: [{ required: true, message: '可抵扣积分必需填写' }],
  forehead_integral_type: [{ required: true, message: '可抵扣积分类型1.固定值 |2.百分比必需填写' }],
  accumulative: [{ required: true, message: '允许多件累计折扣必需填写' }],
  individual_share: [{ required: true, message: '是否单独分销设置：0=否，1=是必需填写' }],
  attr_setting_type: [{ required: true, message: '分销设置类型 0.普通设置|1.详细设置必需填写' }],
  is_level: [{ required: true, message: '是否享受会员价购买必需填写' }],
  is_level_alone: [{ required: true, message: '是否单独设置会员价必需填写' }],
  share_type: [{ required: true, message: '佣金配比 0--固定金额 1--百分比必需填写' }],
  sign: [{ required: true, message: '商品标示用于区分商品属于什么模块必需填写' }],
  app_share_pic: [{ required: true, message: '自定义分享图片必需填写' }],
  app_share_title: [{ required: true, message: '自定义分享标题必需填写' }],
  is_default_services: [{ required: true, message: '默认服务 0.否|1.是必需填写' }],
  sort: [{ required: true, message: '排序必需填写' }],
  created_at: [{ required: true, message: '必需填写' }],
  updated_at: [{ required: true, message: '必需填写' }],
  deleted_at: [{ required: true, message: '必需填写' }],
  is_delete: [{ required: true, message: '必需填写' }],
  payment_people: [{ required: true, message: '支付人数必需填写' }],
  payment_num: [{ required: true, message: '支付件数必需填写' }],
  payment_amount: [{ required: true, message: '支付金额必需填写' }],
  payment_order: [{ required: true, message: '支付订单数必需填写' }],
  confine_order_count: [{ required: true, message: '必需填写' }],
  is_area_limit: [{ required: true, message: '是否单独区域购买必需填写' }],
  form_id: [{ required: true, message: '自定义表单id 0--表示默认表单 -1--表示不使用表单必需填写' }],
  sales: [{ required: true, message: '商品实际销量必需填写' }],
  detail_count: [{ required: true, message: '详情浏览量统计必需填写' }],
  guarantee_title: [{ required: true, message: '商品服务标题必需填写' }],
  guarantee_pic: [{ required: true, message: '商品服务标识必需填写' }],
}

// 打开弹框
const open = async (type = 'add') => {
  mode.value = type
  // 重置表单数据
  Object.assign(formData, initialFormData)
  formRef.value.clearValidate()
  visible.value = true
  await initPage()
}

// 初始化页面数据
const initPage = async () => {}

// 设置数据
const setFormData = async (data) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key]
    }
  }
}

// 数据保存
const submit = async (done) => {
  const validate = await formRef.value?.validate()
  if (!validate) {
    loading.value = true
    let data = { ...formData }
    let result = {}
    if (mode.value === 'add') {
      // 添加数据
      data.id = undefined
      result = await api.save(data)
    } else {
      // 修改数据
      result = await api.update(data.id, data)
    }
    if (result.code === 200) {
      Message.success('操作成功')
      emit('success')
      done(true)
    }
    // 防止连续点击提交
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
  done(false)
}

// 关闭弹窗
const close = () => (visible.value = false)

defineExpose({ open, setFormData })
</script>
