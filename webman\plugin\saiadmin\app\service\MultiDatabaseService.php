<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 多数据源服务示例
// +----------------------------------------------------------------------
// | Author: AI Assistant (多数据源实战版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\service;

use plugin\saiadmin\app\database\MultiDatabaseManager;
use plugin\saiadmin\app\model\OperationLog;
use plugin\saiadmin\app\model\LoginLog;
use plugin\saiadmin\app\model\PerformanceLog;
use plugin\saiadmin\app\model\CacheData;

/**
 * 多数据源服务示例
 */
class MultiDatabaseService
{
    /**
     * 用户服务 - 演示读写分离
     */
    public function getUserList(array $params = []): array
    {
        try {
            // 记录性能开始时间
            $startTime = microtime(true);
            
            // 使用读库查询用户列表（读写分离）
            $query = MultiDatabaseManager::read()->table('sa_system_user');
            
            // 添加查询条件
            if (!empty($params['username'])) {
                $query->where('username', 'like', "%{$params['username']}%");
            }
            
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }
            
            if (!empty($params['dept_id'])) {
                $query->where('dept_id', $params['dept_id']);
            }
            
            // 分页查询
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            $offset = ($page - 1) * $limit;
            
            $users = $query->limit($offset, $limit)->select();
            $total = $query->count();
            
            // 记录性能日志
            $responseTime = (microtime(true) - $startTime) * 1000;
            PerformanceLog::record([
                'request_id' => uniqid(),
                'uri' => '/api/users',
                'method' => 'GET',
                'response_time' => $responseTime,
                'memory_usage' => memory_get_usage(),
                'query_count' => 1
            ]);
            
            return [
                'code' => 200,
                'data' => [
                    'list' => $users,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ],
                'message' => '查询成功'
            ];
            
        } catch (\Exception $e) {
            // 记录错误日志
            error_log('用户列表查询失败: ' . $e->getMessage());
            
            return [
                'code' => 500,
                'data' => [],
                'message' => '查询失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 创建用户 - 演示事务和日志记录
     */
    public function createUser(array $userData, int $operatorId = 0): array
    {
        try {
            // 使用事务确保数据一致性
            $result = MultiDatabaseManager::transaction(function() use ($userData, $operatorId) {
                // 插入用户数据到主库
                $userId = MultiDatabaseManager::main()->table('sa_system_user')->insertGetId([
                    'username' => $userData['username'],
                    'nickname' => $userData['nickname'],
                    'password' => password_hash($userData['password'], PASSWORD_DEFAULT),
                    'email' => $userData['email'] ?? '',
                    'phone' => $userData['phone'] ?? '',
                    'status' => $userData['status'] ?? 1,
                    'dept_id' => $userData['dept_id'] ?? 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                
                // 如果有角色信息，插入用户角色关联
                if (!empty($userData['role_ids'])) {
                    $roleData = [];
                    foreach ($userData['role_ids'] as $roleId) {
                        $roleData[] = [
                            'user_id' => $userId,
                            'role_id' => $roleId,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    MultiDatabaseManager::main()->table('sa_system_user_role')->insertAll($roleData);
                }
                
                return $userId;
            });
            
            // 记录操作日志到日志库
            OperationLog::record([
                'user_id' => $operatorId,
                'username' => getCurrentInfo()['username'] ?? 'system',
                'method' => 'POST',
                'router' => '/api/users',
                'service_name' => 'UserService',
                'ip' => request()->getRealIp(),
                'ip_location' => '本地',
                'request_data' => array_merge($userData, ['password' => '***']), // 隐藏密码
                'response_code' => '200',
                'response_data' => ['user_id' => $result]
            ]);
            
            // 清除相关缓存
            CacheData::deleteCache('user_list');
            CacheData::deleteCache('user_count');
            
            return [
                'code' => 200,
                'data' => ['user_id' => $result],
                'message' => '用户创建成功'
            ];
            
        } catch (\Exception $e) {
            // 记录错误日志
            error_log('用户创建失败: ' . $e->getMessage());
            
            return [
                'code' => 500,
                'data' => [],
                'message' => '用户创建失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 用户登录 - 演示多数据源协作
     */
    public function userLogin(string $username, string $password, array $loginInfo = []): array
    {
        try {
            // 从主库查询用户信息
            $user = MultiDatabaseManager::main()
                ->table('sa_system_user')
                ->where('username', $username)
                ->where('status', 1)
                ->find();
            
            if (!$user) {
                // 记录登录失败日志
                LoginLog::record([
                    'username' => $username,
                    'ip' => $loginInfo['ip'] ?? '',
                    'ip_location' => $loginInfo['ip_location'] ?? '',
                    'os' => $loginInfo['os'] ?? '',
                    'browser' => $loginInfo['browser'] ?? '',
                    'status' => 2,
                    'message' => '用户不存在或已禁用'
                ]);
                
                return [
                    'code' => 401,
                    'data' => [],
                    'message' => '用户不存在或已禁用'
                ];
            }
            
            // 验证密码
            if (!password_verify($password, $user['password'])) {
                // 记录登录失败日志
                LoginLog::record([
                    'username' => $username,
                    'ip' => $loginInfo['ip'] ?? '',
                    'ip_location' => $loginInfo['ip_location'] ?? '',
                    'os' => $loginInfo['os'] ?? '',
                    'browser' => $loginInfo['browser'] ?? '',
                    'status' => 2,
                    'message' => '密码错误'
                ]);
                
                return [
                    'code' => 401,
                    'data' => [],
                    'message' => '密码错误'
                ];
            }
            
            // 生成登录token
            $token = bin2hex(random_bytes(32));
            $expireTime = time() + 7200; // 2小时过期
            
            // 将token存储到缓存数据库
            CacheData::setCache("user_token_{$user['id']}", [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'token' => $token,
                'expire_time' => $expireTime,
                'login_time' => time()
            ], 7200);
            
            // 更新用户最后登录时间（写库）
            MultiDatabaseManager::main()
                ->table('sa_system_user')
                ->where('id', $user['id'])
                ->update([
                    'last_login_time' => date('Y-m-d H:i:s'),
                    'last_login_ip' => $loginInfo['ip'] ?? ''
                ]);
            
            // 记录登录成功日志
            LoginLog::record([
                'username' => $username,
                'ip' => $loginInfo['ip'] ?? '',
                'ip_location' => $loginInfo['ip_location'] ?? '',
                'os' => $loginInfo['os'] ?? '',
                'browser' => $loginInfo['browser'] ?? '',
                'status' => 1,
                'message' => '登录成功'
            ]);
            
            return [
                'code' => 200,
                'data' => [
                    'token' => $token,
                    'user_info' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'nickname' => $user['nickname'],
                        'email' => $user['email']
                    ],
                    'expire_time' => $expireTime
                ],
                'message' => '登录成功'
            ];
            
        } catch (\Exception $e) {
            error_log('用户登录失败: ' . $e->getMessage());
            
            return [
                'code' => 500,
                'data' => [],
                'message' => '登录失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取统计数据 - 演示多数据源聚合查询
     */
    public function getDashboardStats(): array
    {
        try {
            // 检查缓存
            $cacheKey = 'dashboard_stats_' . date('Y-m-d-H');
            $cachedStats = CacheData::getCache($cacheKey);
            
            if ($cachedStats) {
                return [
                    'code' => 200,
                    'data' => $cachedStats,
                    'message' => '获取成功（缓存）'
                ];
            }
            
            // 从主库获取用户统计
            $userStats = MultiDatabaseManager::read()
                ->table('sa_system_user')
                ->field([
                    'COUNT(*) as total_users',
                    'SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_users',
                    'SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_new_users'
                ])
                ->find();
            
            // 从日志库获取今日登录统计
            $loginStats = LoginLog::getLoginStats();
            
            // 从日志库获取今日性能统计
            $perfStats = PerformanceLog::getPerformanceStats();
            
            // 从主库获取系统统计
            $systemStats = [
                'total_roles' => MultiDatabaseManager::read()->table('sa_system_role')->count(),
                'total_menus' => MultiDatabaseManager::read()->table('sa_system_menu')->count(),
                'total_depts' => MultiDatabaseManager::read()->table('sa_system_dept')->count(),
            ];
            
            $stats = [
                'user_stats' => $userStats,
                'login_stats' => $loginStats,
                'performance_stats' => $perfStats,
                'system_stats' => $systemStats,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 缓存统计数据1小时
            CacheData::setCache($cacheKey, $stats, 3600);
            
            return [
                'code' => 200,
                'data' => $stats,
                'message' => '获取成功'
            ];
            
        } catch (\Exception $e) {
            error_log('获取统计数据失败: ' . $e->getMessage());
            
            return [
                'code' => 500,
                'data' => [],
                'message' => '获取失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 数据备份 - 演示跨数据库操作
     */
    public function backupData(array $tables = []): array
    {
        try {
            $backupTime = date('Y-m-d H:i:s');
            $backupId = uniqid('backup_');
            
            // 默认备份的表
            if (empty($tables)) {
                $tables = ['sa_system_user', 'sa_system_role', 'sa_system_menu', 'sa_system_dept'];
            }
            
            $backupResults = [];
            
            foreach ($tables as $table) {
                // 从主库读取数据
                $data = MultiDatabaseManager::main()->table($table)->select();
                
                // 将备份数据存储到缓存库
                $backupKey = "backup_{$backupId}_{$table}";
                $result = CacheData::setCache($backupKey, $data, 86400 * 7); // 保存7天
                
                $backupResults[$table] = [
                    'records' => count($data),
                    'backup_key' => $backupKey,
                    'status' => $result ? 'success' : 'failed'
                ];
            }
            
            // 记录备份操作日志
            OperationLog::record([
                'user_id' => getCurrentInfo()['id'] ?? 0,
                'username' => getCurrentInfo()['username'] ?? 'system',
                'method' => 'POST',
                'router' => '/api/backup',
                'service_name' => 'BackupService',
                'ip' => request()->getRealIp(),
                'ip_location' => '本地',
                'request_data' => ['tables' => $tables],
                'response_code' => '200',
                'response_data' => ['backup_id' => $backupId]
            ]);
            
            return [
                'code' => 200,
                'data' => [
                    'backup_id' => $backupId,
                    'backup_time' => $backupTime,
                    'results' => $backupResults
                ],
                'message' => '备份完成'
            ];
            
        } catch (\Exception $e) {
            error_log('数据备份失败: ' . $e->getMessage());
            
            return [
                'code' => 500,
                'data' => [],
                'message' => '备份失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取数据库健康状态
     */
    public function getDatabaseHealth(): array
    {
        try {
            $health = MultiDatabaseManager::healthCheck();
            $stats = MultiDatabaseManager::getStatistics();
            
            return [
                'code' => 200,
                'data' => [
                    'health_check' => $health,
                    'statistics' => $stats,
                    'check_time' => date('Y-m-d H:i:s')
                ],
                'message' => '健康检查完成'
            ];
            
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'data' => [],
                'message' => '健康检查失败: ' . $e->getMessage()
            ];
        }
    }
}
