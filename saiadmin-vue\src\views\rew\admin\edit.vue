<template>
  <component
    is="a-modal"
    :width="tool.getDevice() === 'mobile' ? '100%' : '600px'"
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :ok-loading="loading"
    @cancel="close"
    @before-ok="submit">
    <!-- 表单信息 start -->
    <a-form ref="formRef" :model="formData" :rules="rules" :auto-label-width="true">
      <a-form-item label="" field="createtime">
        <a-input v-model="formData.createtime" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="token">
        <a-input v-model="formData.token" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="chouchengset">
        <a-input v-model="formData.chouchengset" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="chouchengrate">
        <a-input v-model="formData.chouchengrate" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="chouchengmin">
        <a-input v-model="formData.chouchengmin" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="chouchengmoney">
        <a-input v-model="formData.chouchengmoney" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="linkman">
        <a-input v-model="formData.linkman" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="tel">
        <a-input v-model="formData.tel" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="province">
        <a-input v-model="formData.province" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="city">
        <a-input v-model="formData.city" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="area">
        <a-input v-model="formData.area" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="address">
        <a-input v-model="formData.address" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="0公众号 1小程序" field="platform">
        <a-input v-model="formData.platform" placeholder="请输入0公众号 1小程序" />
      </a-form-item>
      <a-form-item label="" field="status">
        <a-input v-model="formData.status" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="endtime">
        <a-input v-model="formData.endtime" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="pid">
        <a-input v-model="formData.pid" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="remote">
        <ma-wangEditor v-model="formData.remote" :height="400" />
      </a-form-item>
      <a-form-item label="" field="copyright">
        <a-input v-model="formData.copyright" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="domain">
        <a-input v-model="formData.domain" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="image_search">
        <sa-upload-image v-model="formData.image_search" :limit="3" :multiple="false" />
      </a-form-item>
      <a-form-item label="" field="agent_card">
        <a-input v-model="formData.agent_card" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="order_show_onlychildren">
        <a-input v-model="formData.order_show_onlychildren" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="choucheng_receivertype">
        <a-input v-model="formData.choucheng_receivertype" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="choucheng_receivertype1_account">
        <a-input v-model="formData.choucheng_receivertype1_account" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="choucheng_receivertype1_name">
        <a-input v-model="formData.choucheng_receivertype1_name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="choucheng_receivertype2_openidtype">
        <a-input v-model="formData.choucheng_receivertype2_openidtype" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="choucheng_receivertype2_account">
        <a-input v-model="formData.choucheng_receivertype2_account" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="choucheng_receivertype2_accountwx">
        <a-input v-model="formData.choucheng_receivertype2_accountwx" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="choucheng_receivertype2_name">
        <a-input v-model="formData.choucheng_receivertype2_name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="commission_frozen">
        <a-input v-model="formData.commission_frozen" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="score">
        <a-input v-model="formData.score" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="商城自定义购买按钮 0：关闭 1：开启" field="buybtn_status">
        <a-input v-model="formData.buybtn_status" placeholder="请输入商城自定义购买按钮 0：关闭 1：开启" />
      </a-form-item>
      <a-form-item label="多账户 0：关闭 1：开启" field="othermoney_status">
        <a-input v-model="formData.othermoney_status" placeholder="请输入多账户 0：关闭 1：开启" />
      </a-form-item>
      <a-form-item label="" field="jushuitan_status">
        <a-input v-model="formData.jushuitan_status" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="外链参数，外部链接是否携带系统aid和mid" field="with_system_param">
        <a-input v-model="formData.with_system_param" placeholder="请输入外链参数，外部链接是否携带系统aid和mid" />
      </a-form-item>
      <a-form-item label="" field="need_school">
        <a-input v-model="formData.need_school" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="贡献开关" field="member_gongxian_status">
        <a-input v-model="formData.member_gongxian_status" placeholder="请输入贡献开关" />
      </a-form-item>
      <a-form-item label="股东投资分红状态" field="shareholder_status">
        <a-input v-model="formData.shareholder_status" placeholder="请输入股东投资分红状态" />
      </a-form-item>
      <a-form-item label="" field="group_id">
        <a-input v-model="formData.group_id" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="file_image_total">
        <sa-upload-file v-model="formData.file_image_total" :limit="3" :multiple="false" />
      </a-form-item>
      <a-form-item label="" field="file_video_total">
        <sa-upload-file v-model="formData.file_video_total" :limit="3" :multiple="false" />
      </a-form-item>
      <a-form-item label="" field="file_other_total">
        <sa-upload-file v-model="formData.file_other_total" :limit="3" :multiple="false" />
      </a-form-item>
      <a-form-item label="" field="file_upload_total">
        <sa-upload-file v-model="formData.file_upload_total" :limit="3" :multiple="false" />
      </a-form-item>
      <a-form-item label="" field="file_upload_limit">
        <sa-upload-file v-model="formData.file_upload_limit" :limit="3" :multiple="false" />
      </a-form-item>
      <a-form-item label="服务商流量主分账比例" field="ad_ratio">
        <a-input v-model="formData.ad_ratio" placeholder="请输入服务商流量主分账比例" />
      </a-form-item>
    </a-form>
    <!-- 表单信息 end -->
  </component>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import tool from '@/utils/tool'
import { Message, Modal } from '@arco-design/web-vue'
import api from '../api/admin'

const emit = defineEmits(['success'])
// 引用定义
const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const mode = ref('')

let title = computed(() => {
  return '授信' + (mode.value == 'add' ? '-新增' : '-编辑')
})

// 表单初始值
const initialFormData = {
  id: null,
  createtime: null,
  token: '',
  chouchengset: null,
  chouchengrate: '',
  chouchengmin: '',
  chouchengmoney: '',
  linkman: '',
  tel: '',
  province: '',
  city: '',
  area: '',
  address: '',
  platform: '0',
  status: 1,
  endtime: '',
  pid: null,
  remote: '',
  copyright: '',
  domain: '',
  image_search: null,
  agent_card: null,
  order_show_onlychildren: null,
  choucheng_receivertype: null,
  choucheng_receivertype1_account: '',
  choucheng_receivertype1_name: '',
  choucheng_receivertype2_openidtype: null,
  choucheng_receivertype2_account: '',
  choucheng_receivertype2_accountwx: '',
  choucheng_receivertype2_name: '',
  commission_frozen: null,
  score: null,
  buybtn_status: null,
  othermoney_status: null,
  jushuitan_status: null,
  with_system_param: null,
  need_school: null,
  member_gongxian_status: null,
  shareholder_status: null,
  group_id: null,
  file_image_total: null,
  file_video_total: null,
  file_other_total: null,
  file_upload_total: null,
  file_upload_limit: null,
  ad_ratio: null,
}

// 表单信息
const formData = reactive({ ...initialFormData })

// 验证规则
const rules = {
  choucheng_receivertype: [{ required: true, message: '必需填写' }],
  choucheng_receivertype1_account: [{ required: true, message: '必需填写' }],
  choucheng_receivertype1_name: [{ required: true, message: '必需填写' }],
  choucheng_receivertype2_openidtype: [{ required: true, message: '必需填写' }],
  choucheng_receivertype2_account: [{ required: true, message: '必需填写' }],
  choucheng_receivertype2_accountwx: [{ required: true, message: '必需填写' }],
  choucheng_receivertype2_name: [{ required: true, message: '必需填写' }],
  buybtn_status: [{ required: true, message: '商城自定义购买按钮 0：关闭 1：开启必需填写' }],
  othermoney_status: [{ required: true, message: '多账户 0：关闭 1：开启必需填写' }],
}

// 打开弹框
const open = async (type = 'add') => {
  mode.value = type
  // 重置表单数据
  Object.assign(formData, initialFormData)
  formRef.value.clearValidate()
  visible.value = true
  await initPage()
}

// 初始化页面数据
const initPage = async () => {}

// 设置数据
const setFormData = async (data) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key]
    }
  }
}

// 数据保存
const submit = async (done) => {
  const validate = await formRef.value?.validate()
  if (!validate) {
    loading.value = true
    let data = { ...formData }
    let result = {}
    if (mode.value === 'add') {
      // 添加数据
      data.id = undefined
      result = await api.save(data)
    } else {
      // 修改数据
      result = await api.update(data.id, data)
    }
    if (result.code === 200) {
      Message.success('操作成功')
      emit('success')
      done(true)
    }
    // 防止连续点击提交
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
  done(false)
}

// 关闭弹窗
const close = () => (visible.value = false)

defineExpose({ open, setFormData })
</script>
