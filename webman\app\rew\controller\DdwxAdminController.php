<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\rew\controller;

use plugin\saiadmin\basic\BaseController;
use app\rew\logic\DdwxAdminLogic;
use app\rew\validate\DdwxAdminValidate;
use support\Request;
use support\Response;

/**
 * 授信控制器
 */
class DdwxAdmin<PERSON>ontroller extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new DdwxAdminLogic();
        $this->validate = new DdwxAdminValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['choucheng_receivertype', ''],
            ['choucheng_receivertype1_account', ''],
            ['choucheng_receivertype1_name', ''],
            ['choucheng_receivertype2_openidtype', ''],
            ['choucheng_receivertype2_account', ''],
            ['choucheng_receivertype2_accountwx', ''],
            ['choucheng_receivertype2_name', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

}
