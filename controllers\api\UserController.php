<?php
/**
 * API控制器基类
 */
namespace app\controllers\api;

use Yii;
use yii\rest\ActiveController;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\ContentNegotiator;
use yii\web\Response;

/**
 * API基础控制器
 */
class BaseApiController extends ActiveController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        
        // 内容协商
        $behaviors["contentNegotiator"] = [
            "class" => ContentNegotiator::class,
            "formats" => [
                "application/json" => Response::FORMAT_JSON,
                "application/xml" => Response::FORMAT_XML,
            ],
        ];
        
        // 认证
        $behaviors["authenticator"] = [
            "class" => HttpBearerAuth::class,
            "except" => ["options"],
        ];
        
        return $behaviors;
    }

    /**
     * 成功响应
     */
    protected function success($data = [], $message = "操作成功")
    {
        return [
            "code" => 200,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }

    /**
     * 失败响应
     */
    protected function fail($message = "操作失败", $code = 400, $data = [])
    {
        Yii::$app->response->statusCode = $code;
        return [
            "code" => $code,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }
}

/**
 * 用户API控制器
 */
class UserController extends BaseApiController
{
    public $modelClass = "app\models\User";

    /**
     * @inheritdoc
     */
    public function actions()
    {
        $actions = parent::actions();
        
        // 自定义actions
        unset($actions["delete"], $actions["create"], $actions["update"]);
        
        return $actions;
    }

    /**
     * 创建用户
     */
    public function actionCreate()
    {
        $model = new $this->modelClass();
        $model->scenario = "create";
        
        if ($model->load(Yii::$app->request->post(), "") && $model->save()) {
            return $this->success($model->toArray(), "用户创建成功");
        }
        
        return $this->fail("用户创建失败", 422, $model->errors);
    }

    /**
     * 更新用户
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->scenario = "update";
        
        if ($model->load(Yii::$app->request->post(), "") && $model->save()) {
            return $this->success($model->toArray(), "用户更新成功");
        }
        
        return $this->fail("用户更新失败", 422, $model->errors);
    }

    /**
     * 删除用户
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            return $this->success([], "用户删除成功");
        }
        
        return $this->fail("用户删除失败");
    }

    /**
     * 用户登录
     */
    public function actionLogin()
    {
        $username = Yii::$app->request->post("username");
        $password = Yii::$app->request->post("password");
        
        if (empty($username) || empty($password)) {
            return $this->fail("用户名和密码不能为空", 422);
        }
        
        $user = User::findByUsername($username);
        if (!$user || !$user->validatePassword($password)) {
            return $this->fail("用户名或密码错误", 401);
        }
        
        // 生成访问令牌
        $token = Yii::$app->security->generateRandomString(32);
        $user->access_token = $token;
        $user->updateLastLogin();
        
        return $this->success([
            "user" => $user->toArray(["id", "username", "email", "nickname"]),
            "token" => $token,
            "expires_in" => 3600 * 24 * 7 // 7天
        ], "登录成功");
    }

    /**
     * 用户信息
     */
    public function actionProfile()
    {
        $user = Yii::$app->user->identity;
        
        return $this->success([
            "user" => $user->toArray(),
            "permissions" => [], // 用户权限
            "roles" => [] // 用户角色
        ]);
    }
}