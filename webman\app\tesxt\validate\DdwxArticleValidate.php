<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\tesxt\validate;

use think\Validate;

/**
 * test验证器
 */
class DdwxArticleValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'name' => 'require',
        'subname' => 'require',
        'showname' => 'require',
        'showsubname' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'name' => '必须填写',
        'subname' => '必须填写',
        'showname' => '必须填写',
        'showsubname' => '必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'name',
            'subname',
            'showname',
            'showsubname',
        ],
        'update' => [
            'name',
            'subname',
            'showname',
            'showsubname',
        ],
    ];

}
