export default {
  pageSetting: 'Page Setting',
  chinese: '简体中文',
  english: 'English',
  search: 'Search',
  store: 'App Store',
  fullScreen: 'Full Screen',
  closeFullScreen: 'Close Full Screen',
  changeSkin: 'Change Skin',
  skin: 'Skin',
  layouts: 'Layout',
  language: 'Language',
  dark: 'Dark Mode',
  tag: 'Open Tags',
  water: 'Watermark',
  waterContent: 'Watermark content',
  menuFold: 'Menu Fold',
  menuWidth: 'Mene Width',
  skinHelp: 'Set up background skins',
  layoutsHelp: 'Set the background display',
  languageHelp: 'Set the page language and the request background language',
  darkHelp: 'Sets the page display mode',
  tagHelp: 'Whether to enable multi-tab mode',
  waterHelp: 'Whether to display the watermark',
  menuFoldHelp: 'Whether the left menu of the system is collapsed',
  menuWidthHelp: 'Sets the display width of the left menu',
  saveToBackend: 'Save to backend',
  backendSettingTitle: 'Backend setting',
  systemPrimaryColor: 'System Primary Color',
  personalizedConfig: 'Personalized configuration',
  layout: {
    classic: 'Classic',
    columns: 'Columns',
    banner: 'Banner',
    mixed: 'Mixed',
  },
  userCenter: 'User Center',
  clearCache: 'Clear Cache',
  logout: 'Logout System',
  logoutAlert: 'Exit prompt',
  logoutMessage: 'Are you sure you want to sign out?',
  operationMessage: {
    message: 'Message',
    notification: 'Notification',
    todo: 'Todo',
  },
  goHome: 'Go Home',
  notFoundPage: 'Exit tip Ah oh, the page visited was hijacked by the Martians...',
  login: {
    slogan: 'High-quality middle and back office management system out of the box',
    title: 'Login System',
    username: 'Username',
    usernameNotice: 'Please enter the username',
    password: 'Passoword',
    passwordNotice: 'Please enter the password',
    verifyCode: 'Please enter the verification code',
    verifyCodeNotice: 'Please enter the correct verification code',
    loginBtn: 'Login in',
    otherLoginType: 'Other ways to sign in'
  },
  verifyCode: {
    switch: 'Click Toggle verification code',
    error: 'The verification code is incorrect',
    notice: 'Please enter the verification code'
  },
  i18n: 'open multi-language',
  i18nHelp: 'Whether to enable the multi-language feature',
  ws: 'open websocket',
  wsHelp: 'Whether to enable the websocket feature',
  round: 'opend round',
  roundHelp: 'Whether to enable the round feature',
  animation: 'Animation',
  animationHelp: 'Page transition animation effect',
  animate: {
    fade: 'The page fades out',
    sliderLeft: 'The page fades to the left',
    sliderRight:'The page fades to the right',
    sliderDown:'The page fades to the down',
    sliderUp:'The page fades to the up',
  },
  tags: {
    refresh: 'Refresh',
    fullscreen: 'Full screen',
    closeRightTag: 'Close right tag',
    closeLeftTag: 'Close left tag',
    closeTag: 'Close current tag',
    closeOtherTag: 'Close other tag',
  },
  noticeTitle: 'System Prompted',
  save: 'Save',
  cancel: 'Cancel',
}
