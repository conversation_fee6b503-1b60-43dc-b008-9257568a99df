{"version": 3, "sources": ["../../echarts/lib/data/DataDiffer.js", "../../echarts/lib/data/helper/dimensionHelper.js", "../../echarts/lib/data/SeriesDimensionDefine.js", "../../echarts/lib/data/helper/SeriesDataSchema.js", "../../echarts/lib/data/SeriesData.js", "../../zrender/lib/contain/polygon.js", "../../echarts/lib/coord/geo/Region.js", "../../echarts/lib/coord/geo/parseGeoJson.js", "../../echarts/lib/scale/Scale.js", "../../echarts/lib/data/OrdinalMeta.js", "../../echarts/lib/scale/helper.js", "../../echarts/lib/scale/Ordinal.js", "../../echarts/lib/scale/Interval.js", "../../echarts/lib/data/helper/dataStackHelper.js", "../../echarts/lib/util/vendor.js", "../../echarts/lib/layout/barGrid.js", "../../echarts/lib/scale/Time.js", "../../echarts/lib/scale/Log.js", "../../echarts/lib/coord/scaleRawExtentInfo.js", "../../echarts/lib/coord/axisHelper.js", "../../echarts/lib/coord/axisTickLabelBuilder.js", "../../echarts/lib/coord/Axis.js", "../../echarts/lib/coord/axisModelCommonMixin.js", "../../echarts/lib/label/labelLayoutHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nfunction dataIndexMapValueLength(valNumOrArrLengthMoreThan2) {\n  return valNumOrArrLengthMoreThan2 == null ? 0 : valNumOrArrLengthMoreThan2.length || 1;\n}\nfunction defaultKeyGetter(item) {\n  return item;\n}\nvar DataDiffer = /** @class */function () {\n  /**\r\n   * @param context Can be visited by this.context in callback.\r\n   */\n  function DataDiffer(oldArr, newArr, oldKeyGetter, newKeyGetter, context,\n  // By default: 'oneToOne'.\n  diffMode) {\n    this._old = oldArr;\n    this._new = newArr;\n    this._oldKeyGetter = oldKeyGetter || defaultKeyGetter;\n    this._newKeyGetter = newKeyGetter || defaultKeyGetter;\n    // Visible in callback via `this.context`;\n    this.context = context;\n    this._diffModeMultiple = diffMode === 'multiple';\n  }\n  /**\r\n   * Callback function when add a data\r\n   */\n  DataDiffer.prototype.add = function (func) {\n    this._add = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data\r\n   */\n  DataDiffer.prototype.update = function (func) {\n    this._update = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\r\n   */\n  DataDiffer.prototype.updateManyToOne = function (func) {\n    this._updateManyToOne = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\r\n   */\n  DataDiffer.prototype.updateOneToMany = function (func) {\n    this._updateOneToMany = func;\n    return this;\n  };\n  /**\r\n   * Callback function when update a data and only work in `cbMode: 'byKey'`.\r\n   */\n  DataDiffer.prototype.updateManyToMany = function (func) {\n    this._updateManyToMany = func;\n    return this;\n  };\n  /**\r\n   * Callback function when remove a data\r\n   */\n  DataDiffer.prototype.remove = function (func) {\n    this._remove = func;\n    return this;\n  };\n  DataDiffer.prototype.execute = function () {\n    this[this._diffModeMultiple ? '_executeMultiple' : '_executeOneToOne']();\n  };\n  DataDiffer.prototype._executeOneToOne = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var newDataIndexMap = {};\n    var oldDataKeyArr = new Array(oldArr.length);\n    var newDataKeyArr = new Array(newArr.length);\n    this._initIndexMap(oldArr, null, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      // idx can never be empty array here. see 'set null' logic below.\n      if (newIdxMapValLen > 1) {\n        // Consider there is duplicate key (for example, use dataItem.name as key).\n        // We should make sure every item in newArr and oldArr can be visited.\n        var newIdx = newIdxMapVal.shift();\n        if (newIdxMapVal.length === 1) {\n          newDataIndexMap[oldKey] = newIdxMapVal[0];\n        }\n        this._update && this._update(newIdx, i);\n      } else if (newIdxMapValLen === 1) {\n        newDataIndexMap[oldKey] = null;\n        this._update && this._update(newIdxMapVal, i);\n      } else {\n        this._remove && this._remove(i);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  /**\r\n   * For example, consider the case:\r\n   * oldData: [o0, o1, o2, o3, o4, o5, o6, o7],\r\n   * newData: [n0, n1, n2, n3, n4, n5, n6, n7, n8],\r\n   * Where:\r\n   *     o0, o1, n0 has key 'a' (many to one)\r\n   *     o5, n4, n5, n6 has key 'b' (one to many)\r\n   *     o2, n1 has key 'c' (one to one)\r\n   *     n2, n3 has key 'd' (add)\r\n   *     o3, o4 has key 'e' (remove)\r\n   *     o6, o7, n7, n8 has key 'f' (many to many, treated as add and remove)\r\n   * Then:\r\n   *     (The order of the following directives are not ensured.)\r\n   *     this._updateManyToOne(n0, [o0, o1]);\r\n   *     this._updateOneToMany([n4, n5, n6], o5);\r\n   *     this._update(n1, o2);\r\n   *     this._remove(o3);\r\n   *     this._remove(o4);\r\n   *     this._remove(o6);\r\n   *     this._remove(o7);\r\n   *     this._add(n2);\r\n   *     this._add(n3);\r\n   *     this._add(n7);\r\n   *     this._add(n8);\r\n   */\n  DataDiffer.prototype._executeMultiple = function () {\n    var oldArr = this._old;\n    var newArr = this._new;\n    var oldDataIndexMap = {};\n    var newDataIndexMap = {};\n    var oldDataKeyArr = [];\n    var newDataKeyArr = [];\n    this._initIndexMap(oldArr, oldDataIndexMap, oldDataKeyArr, '_oldKeyGetter');\n    this._initIndexMap(newArr, newDataIndexMap, newDataKeyArr, '_newKeyGetter');\n    for (var i = 0; i < oldDataKeyArr.length; i++) {\n      var oldKey = oldDataKeyArr[i];\n      var oldIdxMapVal = oldDataIndexMap[oldKey];\n      var newIdxMapVal = newDataIndexMap[oldKey];\n      var oldIdxMapValLen = dataIndexMapValueLength(oldIdxMapVal);\n      var newIdxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (oldIdxMapValLen > 1 && newIdxMapValLen === 1) {\n        this._updateManyToOne && this._updateManyToOne(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen > 1) {\n        this._updateOneToMany && this._updateOneToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen === 1 && newIdxMapValLen === 1) {\n        this._update && this._update(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1 && newIdxMapValLen > 1) {\n        this._updateManyToMany && this._updateManyToMany(newIdxMapVal, oldIdxMapVal);\n        newDataIndexMap[oldKey] = null;\n      } else if (oldIdxMapValLen > 1) {\n        for (var i_1 = 0; i_1 < oldIdxMapValLen; i_1++) {\n          this._remove && this._remove(oldIdxMapVal[i_1]);\n        }\n      } else {\n        this._remove && this._remove(oldIdxMapVal);\n      }\n    }\n    this._performRestAdd(newDataKeyArr, newDataIndexMap);\n  };\n  DataDiffer.prototype._performRestAdd = function (newDataKeyArr, newDataIndexMap) {\n    for (var i = 0; i < newDataKeyArr.length; i++) {\n      var newKey = newDataKeyArr[i];\n      var newIdxMapVal = newDataIndexMap[newKey];\n      var idxMapValLen = dataIndexMapValueLength(newIdxMapVal);\n      if (idxMapValLen > 1) {\n        for (var j = 0; j < idxMapValLen; j++) {\n          this._add && this._add(newIdxMapVal[j]);\n        }\n      } else if (idxMapValLen === 1) {\n        this._add && this._add(newIdxMapVal);\n      }\n      // Support both `newDataKeyArr` are duplication removed or not removed.\n      newDataIndexMap[newKey] = null;\n    }\n  };\n  DataDiffer.prototype._initIndexMap = function (arr,\n  // Can be null.\n  map,\n  // In 'byKey', the output `keyArr` is duplication removed.\n  // In 'byIndex', the output `keyArr` is not duplication removed and\n  //     its indices are accurately corresponding to `arr`.\n  keyArr, keyGetterName) {\n    var cbModeMultiple = this._diffModeMultiple;\n    for (var i = 0; i < arr.length; i++) {\n      // Add prefix to avoid conflict with Object.prototype.\n      var key = '_ec_' + this[keyGetterName](arr[i], i);\n      if (!cbModeMultiple) {\n        keyArr[i] = key;\n      }\n      if (!map) {\n        continue;\n      }\n      var idxMapVal = map[key];\n      var idxMapValLen = dataIndexMapValueLength(idxMapVal);\n      if (idxMapValLen === 0) {\n        // Simple optimize: in most cases, one index has one key,\n        // do not need array.\n        map[key] = i;\n        if (cbModeMultiple) {\n          keyArr.push(key);\n        }\n      } else if (idxMapValLen === 1) {\n        map[key] = [idxMapVal, i];\n      } else {\n        idxMapVal.push(i);\n      }\n    }\n  };\n  return DataDiffer;\n}();\nexport default DataDiffer;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, createHashMap, assert, map } from 'zrender/lib/core/util.js';\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nvar DimensionUserOuput = /** @class */function () {\n  function DimensionUserOuput(encode, dimRequest) {\n    this._encode = encode;\n    this._schema = dimRequest;\n  }\n  DimensionUserOuput.prototype.get = function () {\n    return {\n      // Do not generate full dimension name until fist used.\n      fullDimensions: this._getFullDimensionNames(),\n      encode: this._encode\n    };\n  };\n  /**\r\n   * Get all data store dimension names.\r\n   * Theoretically a series data store is defined both by series and used dataset (if any).\r\n   * If some dimensions are omitted for performance reason in `this.dimensions`,\r\n   * the dimension name may not be auto-generated if user does not specify a dimension name.\r\n   * In this case, the dimension name is `null`/`undefined`.\r\n   */\n  DimensionUserOuput.prototype._getFullDimensionNames = function () {\n    if (!this._cachedDimNames) {\n      this._cachedDimNames = this._schema ? this._schema.makeOutputDimensionNames() : [];\n    }\n    return this._cachedDimNames;\n  };\n  return DimensionUserOuput;\n}();\n;\nexport function summarizeDimensions(data, schema) {\n  var summary = {};\n  var encode = summary.encode = {};\n  var notExtraCoordDimMap = createHashMap();\n  var defaultedLabel = [];\n  var defaultedTooltip = [];\n  var userOutputEncode = {};\n  each(data.dimensions, function (dimName) {\n    var dimItem = data.getDimensionInfo(dimName);\n    var coordDim = dimItem.coordDim;\n    if (coordDim) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(VISUAL_DIMENSIONS.get(coordDim) == null);\n      }\n      var coordDimIndex = dimItem.coordDimIndex;\n      getOrCreateEncodeArr(encode, coordDim)[coordDimIndex] = dimName;\n      if (!dimItem.isExtraCoord) {\n        notExtraCoordDimMap.set(coordDim, 1);\n        // Use the last coord dim (and label friendly) as default label,\n        // because when dataset is used, it is hard to guess which dimension\n        // can be value dimension. If both show x, y on label is not look good,\n        // and conventionally y axis is focused more.\n        if (mayLabelDimType(dimItem.type)) {\n          defaultedLabel[0] = dimName;\n        }\n        // User output encode do not contain generated coords.\n        // And it only has index. User can use index to retrieve value from the raw item array.\n        getOrCreateEncodeArr(userOutputEncode, coordDim)[coordDimIndex] = data.getDimensionIndex(dimItem.name);\n      }\n      if (dimItem.defaultTooltip) {\n        defaultedTooltip.push(dimName);\n      }\n    }\n    VISUAL_DIMENSIONS.each(function (v, otherDim) {\n      var encodeArr = getOrCreateEncodeArr(encode, otherDim);\n      var dimIndex = dimItem.otherDims[otherDim];\n      if (dimIndex != null && dimIndex !== false) {\n        encodeArr[dimIndex] = dimItem.name;\n      }\n    });\n  });\n  var dataDimsOnCoord = [];\n  var encodeFirstDimNotExtra = {};\n  notExtraCoordDimMap.each(function (v, coordDim) {\n    var dimArr = encode[coordDim];\n    encodeFirstDimNotExtra[coordDim] = dimArr[0];\n    // Not necessary to remove duplicate, because a data\n    // dim canot on more than one coordDim.\n    dataDimsOnCoord = dataDimsOnCoord.concat(dimArr);\n  });\n  summary.dataDimsOnCoord = dataDimsOnCoord;\n  summary.dataDimIndicesOnCoord = map(dataDimsOnCoord, function (dimName) {\n    return data.getDimensionInfo(dimName).storeDimIndex;\n  });\n  summary.encodeFirstDimNotExtra = encodeFirstDimNotExtra;\n  var encodeLabel = encode.label;\n  // FIXME `encode.label` is not recommended, because formatter cannot be set\n  // in this way. Use label.formatter instead. Maybe remove this approach someday.\n  if (encodeLabel && encodeLabel.length) {\n    defaultedLabel = encodeLabel.slice();\n  }\n  var encodeTooltip = encode.tooltip;\n  if (encodeTooltip && encodeTooltip.length) {\n    defaultedTooltip = encodeTooltip.slice();\n  } else if (!defaultedTooltip.length) {\n    defaultedTooltip = defaultedLabel.slice();\n  }\n  encode.defaultedLabel = defaultedLabel;\n  encode.defaultedTooltip = defaultedTooltip;\n  summary.userOutput = new DimensionUserOuput(userOutputEncode, schema);\n  return summary;\n}\nfunction getOrCreateEncodeArr(encode, dim) {\n  if (!encode.hasOwnProperty(dim)) {\n    encode[dim] = [];\n  }\n  return encode[dim];\n}\n// FIXME:TS should be type `AxisType`\nexport function getDimensionTypeByAxis(axisType) {\n  return axisType === 'category' ? 'ordinal' : axisType === 'time' ? 'time' : 'float';\n}\nfunction mayLabelDimType(dimType) {\n  // In most cases, ordinal and time do not suitable for label.\n  // Ordinal info can be displayed on axis. Time is too long.\n  return !(dimType === 'ordinal' || dimType === 'time');\n}\n// function findTheLastDimMayLabel(data) {\n//     // Get last value dim\n//     let dimensions = data.dimensions.slice();\n//     let valueType;\n//     let valueDim;\n//     while (dimensions.length && (\n//         valueDim = dimensions.pop(),\n//         valueType = data.getDimensionInfo(valueDim).type,\n//         valueType === 'ordinal' || valueType === 'time'\n//     )) {} // jshint ignore:line\n//     return valueDim;\n// }", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar SeriesDimensionDefine = /** @class */function () {\n  /**\r\n   * @param opt All of the fields will be shallow copied.\r\n   */\n  function SeriesDimensionDefine(opt) {\n    /**\r\n     * The format of `otherDims` is:\r\n     * ```js\r\n     * {\r\n     *     tooltip?: number\r\n     *     label?: number\r\n     *     itemName?: number\r\n     *     seriesName?: number\r\n     * }\r\n     * ```\r\n     *\r\n     * A `series.encode` can specified these fields:\r\n     * ```js\r\n     * encode: {\r\n     *     // \"3, 1, 5\" is the index of data dimension.\r\n     *     tooltip: [3, 1, 5],\r\n     *     label: [0, 3],\r\n     *     ...\r\n     * }\r\n     * ```\r\n     * `otherDims` is the parse result of the `series.encode` above, like:\r\n     * ```js\r\n     * // Suppose the index of this data dimension is `3`.\r\n     * this.otherDims = {\r\n     *     // `3` is at the index `0` of the `encode.tooltip`\r\n     *     tooltip: 0,\r\n     *     // `3` is at the index `1` of the `encode.label`\r\n     *     label: 1\r\n     * };\r\n     * ```\r\n     *\r\n     * This prop should never be `null`/`undefined` after initialized.\r\n     */\n    this.otherDims = {};\n    if (opt != null) {\n      zrUtil.extend(this, opt);\n    }\n  }\n  return SeriesDimensionDefine;\n}();\n;\nexport default SeriesDimensionDefine;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, isObject, retrieve2 } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { shouldRetrieveDataByName } from '../Source.js';\nvar inner = makeInner();\nvar dimTypeShort = {\n  float: 'f',\n  int: 'i',\n  ordinal: 'o',\n  number: 'n',\n  time: 't'\n};\n/**\r\n * Represents the dimension requirement of a series.\r\n *\r\n * NOTICE:\r\n * When there are too many dimensions in dataset and many series, only the used dimensions\r\n * (i.e., used by coord sys and declared in `series.encode`) are add to `dimensionDefineList`.\r\n * But users may query data by other unused dimension names.\r\n * In this case, users can only query data if and only if they have defined dimension names\r\n * via ec option, so we provide `getDimensionIndexFromSource`, which only query them from\r\n * `source` dimensions.\r\n */\nvar SeriesDataSchema = /** @class */function () {\n  function SeriesDataSchema(opt) {\n    this.dimensions = opt.dimensions;\n    this._dimOmitted = opt.dimensionOmitted;\n    this.source = opt.source;\n    this._fullDimCount = opt.fullDimensionCount;\n    this._updateDimOmitted(opt.dimensionOmitted);\n  }\n  SeriesDataSchema.prototype.isDimensionOmitted = function () {\n    return this._dimOmitted;\n  };\n  SeriesDataSchema.prototype._updateDimOmitted = function (dimensionOmitted) {\n    this._dimOmitted = dimensionOmitted;\n    if (!dimensionOmitted) {\n      return;\n    }\n    if (!this._dimNameMap) {\n      this._dimNameMap = ensureSourceDimNameMap(this.source);\n    }\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Get index by user defined dimension name (i.e., not internal generate name).\r\n   * That is, get index from `dimensionsDefine`.\r\n   * If no `dimensionsDefine`, or no name get, return -1.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimensionIndex = function (dimName) {\n    return retrieve2(this._dimNameMap.get(dimName), -1);\n  };\n  /**\r\n   * @caution Can only be used when `dimensionOmitted: true`.\r\n   *\r\n   * Notice: may return `null`/`undefined` if user not specify dimension names.\r\n   */\n  SeriesDataSchema.prototype.getSourceDimension = function (dimIndex) {\n    var dimensionsDefine = this.source.dimensionsDefine;\n    if (dimensionsDefine) {\n      return dimensionsDefine[dimIndex];\n    }\n  };\n  SeriesDataSchema.prototype.makeStoreSchema = function () {\n    var dimCount = this._fullDimCount;\n    var willRetrieveDataByName = shouldRetrieveDataByName(this.source);\n    var makeHashStrict = !shouldOmitUnusedDimensions(dimCount);\n    // If source don't have dimensions or series don't omit unsed dimensions.\n    // Generate from seriesDimList directly\n    var dimHash = '';\n    var dims = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < dimCount; fullDimIdx++) {\n      var property = void 0;\n      var type = void 0;\n      var ordinalMeta = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        property = willRetrieveDataByName ? seriesDimDef.name : null;\n        type = seriesDimDef.type;\n        ordinalMeta = seriesDimDef.ordinalMeta;\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          property = willRetrieveDataByName ? sourceDimDef.name : null;\n          type = sourceDimDef.type;\n        }\n      }\n      dims.push({\n        property: property,\n        type: type,\n        ordinalMeta: ordinalMeta\n      });\n      // If retrieving data by index,\n      //   use <index, type, ordinalMeta> to determine whether data can be shared.\n      //   (Because in this case there might be no dimension name defined in dataset, but indices always exists).\n      //   (Indices are always 0, 1, 2, ..., so we can ignore them to shorten the hash).\n      // Otherwise if retrieving data by property name (like `data: [{aa: 123, bb: 765}, ...]`),\n      //   use <property, type, ordinalMeta> in hash.\n      if (willRetrieveDataByName && property != null\n      // For data stack, we have make sure each series has its own dim on this store.\n      // So we do not add property to hash to make sure they can share this store.\n      && (!seriesDimDef || !seriesDimDef.isCalculationCoord)) {\n        dimHash += makeHashStrict\n        // Use escape character '`' in case that property name contains '$'.\n        ? property.replace(/\\`/g, '`1').replace(/\\$/g, '`2')\n        // For better performance, when there are large dimensions, tolerant this defects that hardly meet.\n        : property;\n      }\n      dimHash += '$';\n      dimHash += dimTypeShort[type] || 'f';\n      if (ordinalMeta) {\n        dimHash += ordinalMeta.uid;\n      }\n      dimHash += '$';\n    }\n    // Source from endpoint(usually series) will be read differently\n    // when seriesLayoutBy or startIndex(which is affected by sourceHeader) are different.\n    // So we use this three props as key.\n    var source = this.source;\n    var hash = [source.seriesLayoutBy, source.startIndex, dimHash].join('$$');\n    return {\n      dimensions: dims,\n      hash: hash\n    };\n  };\n  SeriesDataSchema.prototype.makeOutputDimensionNames = function () {\n    var result = [];\n    for (var fullDimIdx = 0, seriesDimIdx = 0; fullDimIdx < this._fullDimCount; fullDimIdx++) {\n      var name_1 = void 0;\n      var seriesDimDef = this.dimensions[seriesDimIdx];\n      // The list has been sorted by `storeDimIndex` asc.\n      if (seriesDimDef && seriesDimDef.storeDimIndex === fullDimIdx) {\n        if (!seriesDimDef.isCalculationCoord) {\n          name_1 = seriesDimDef.name;\n        }\n        seriesDimIdx++;\n      } else {\n        var sourceDimDef = this.getSourceDimension(fullDimIdx);\n        if (sourceDimDef) {\n          name_1 = sourceDimDef.name;\n        }\n      }\n      result.push(name_1);\n    }\n    return result;\n  };\n  SeriesDataSchema.prototype.appendCalculationDimension = function (dimDef) {\n    this.dimensions.push(dimDef);\n    dimDef.isCalculationCoord = true;\n    this._fullDimCount++;\n    // If append dimension on a data store, consider the store\n    // might be shared by different series, series dimensions not\n    // really map to store dimensions.\n    this._updateDimOmitted(true);\n  };\n  return SeriesDataSchema;\n}();\nexport { SeriesDataSchema };\nexport function isSeriesDataSchema(schema) {\n  return schema instanceof SeriesDataSchema;\n}\nexport function createDimNameMap(dimsDef) {\n  var dataDimNameMap = createHashMap();\n  for (var i = 0; i < (dimsDef || []).length; i++) {\n    var dimDefItemRaw = dimsDef[i];\n    var userDimName = isObject(dimDefItemRaw) ? dimDefItemRaw.name : dimDefItemRaw;\n    if (userDimName != null && dataDimNameMap.get(userDimName) == null) {\n      dataDimNameMap.set(userDimName, i);\n    }\n  }\n  return dataDimNameMap;\n}\nexport function ensureSourceDimNameMap(source) {\n  var innerSource = inner(source);\n  return innerSource.dimNameMap || (innerSource.dimNameMap = createDimNameMap(source.dimensionsDefine));\n}\nexport function shouldOmitUnusedDimensions(dimCount) {\n  return dimCount > 30;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Int32Array */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../model/Model.js';\nimport DataDiffer from './DataDiffer.js';\nimport { DefaultDataProvider } from './helper/dataProvider.js';\nimport { summarizeDimensions } from './helper/dimensionHelper.js';\nimport SeriesDimensionDefine from './SeriesDimensionDefine.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../util/types.js';\nimport { convertOptionIdName, isDataItemOption } from '../util/model.js';\nimport { setCommonECData } from '../util/innerStore.js';\nimport { isSourceInstance } from './Source.js';\nimport DataStore from './DataStore.js';\nimport { isSeriesDataSchema } from './helper/SeriesDataSchema.js';\nvar isObject = zrUtil.isObject;\nvar map = zrUtil.map;\nvar CtorInt32Array = typeof Int32Array === 'undefined' ? Array : Int32Array;\n// Use prefix to avoid index to be the same as otherIdList[idx],\n// which will cause weird update animation.\nvar ID_PREFIX = 'e\\0\\0';\nvar INDEX_NOT_FOUND = -1;\n// type SeriesDimensionIndex = DimensionIndex;\nvar TRANSFERABLE_PROPERTIES = ['hasItemOption', '_nameList', '_idList', '_invertedIndicesMap', '_dimSummary', 'userOutput', '_rawData', '_dimValueGetter', '_nameDimIdx', '_idDimIdx', '_nameRepeatCount'];\nvar CLONE_PROPERTIES = ['_approximateExtent'];\n// -----------------------------\n// Internal method declarations:\n// -----------------------------\nvar prepareInvertedIndex;\nvar getId;\nvar getIdNameFromStore;\nvar normalizeDimensions;\nvar transferProperties;\nvar cloneListForMapAndSample;\nvar makeIdFromName;\nvar SeriesData = /** @class */function () {\n  /**\r\n   * @param dimensionsInput.dimensions\r\n   *        For example, ['someDimName', {name: 'someDimName', type: 'someDimType'}, ...].\r\n   *        Dimensions should be concrete names like x, y, z, lng, lat, angle, radius\r\n   */\n  function SeriesData(dimensionsInput, hostModel) {\n    this.type = 'list';\n    this._dimOmitted = false;\n    this._nameList = [];\n    this._idList = [];\n    // Models of data option is stored sparse for optimizing memory cost\n    // Never used yet (not used yet).\n    // private _optionModels: Model[] = [];\n    // Global visual properties after visual coding\n    this._visual = {};\n    // Global layout properties.\n    this._layout = {};\n    // Item visual properties after visual coding\n    this._itemVisuals = [];\n    // Item layout properties after layout\n    this._itemLayouts = [];\n    // Graphic elements\n    this._graphicEls = [];\n    // key: dim, value: extent\n    this._approximateExtent = {};\n    this._calculationInfo = {};\n    // Having detected that there is data item is non primitive type\n    // (in type `OptionDataItemObject`).\n    // Like `data: [ { value: xx, itemStyle: {...} }, ...]`\n    // At present it only happen in `SOURCE_FORMAT_ORIGINAL`.\n    this.hasItemOption = false;\n    // Methods that create a new list based on this list should be listed here.\n    // Notice that those method should `RETURN` the new list.\n    this.TRANSFERABLE_METHODS = ['cloneShallow', 'downSample', 'minmaxDownSample', 'lttbDownSample', 'map'];\n    // Methods that change indices of this list should be listed here.\n    this.CHANGABLE_METHODS = ['filterSelf', 'selectRange'];\n    this.DOWNSAMPLE_METHODS = ['downSample', 'minmaxDownSample', 'lttbDownSample'];\n    var dimensions;\n    var assignStoreDimIdx = false;\n    if (isSeriesDataSchema(dimensionsInput)) {\n      dimensions = dimensionsInput.dimensions;\n      this._dimOmitted = dimensionsInput.isDimensionOmitted();\n      this._schema = dimensionsInput;\n    } else {\n      assignStoreDimIdx = true;\n      dimensions = dimensionsInput;\n    }\n    dimensions = dimensions || ['x', 'y'];\n    var dimensionInfos = {};\n    var dimensionNames = [];\n    var invertedIndicesMap = {};\n    var needsHasOwn = false;\n    var emptyObj = {};\n    for (var i = 0; i < dimensions.length; i++) {\n      // Use the original dimensions[i], where other flag props may exists.\n      var dimInfoInput = dimensions[i];\n      var dimensionInfo = zrUtil.isString(dimInfoInput) ? new SeriesDimensionDefine({\n        name: dimInfoInput\n      }) : !(dimInfoInput instanceof SeriesDimensionDefine) ? new SeriesDimensionDefine(dimInfoInput) : dimInfoInput;\n      var dimensionName = dimensionInfo.name;\n      dimensionInfo.type = dimensionInfo.type || 'float';\n      if (!dimensionInfo.coordDim) {\n        dimensionInfo.coordDim = dimensionName;\n        dimensionInfo.coordDimIndex = 0;\n      }\n      var otherDims = dimensionInfo.otherDims = dimensionInfo.otherDims || {};\n      dimensionNames.push(dimensionName);\n      dimensionInfos[dimensionName] = dimensionInfo;\n      if (emptyObj[dimensionName] != null) {\n        needsHasOwn = true;\n      }\n      if (dimensionInfo.createInvertedIndices) {\n        invertedIndicesMap[dimensionName] = [];\n      }\n      if (otherDims.itemName === 0) {\n        this._nameDimIdx = i;\n      }\n      if (otherDims.itemId === 0) {\n        this._idDimIdx = i;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(assignStoreDimIdx || dimensionInfo.storeDimIndex >= 0);\n      }\n      if (assignStoreDimIdx) {\n        dimensionInfo.storeDimIndex = i;\n      }\n    }\n    this.dimensions = dimensionNames;\n    this._dimInfos = dimensionInfos;\n    this._initGetDimensionInfo(needsHasOwn);\n    this.hostModel = hostModel;\n    this._invertedIndicesMap = invertedIndicesMap;\n    if (this._dimOmitted) {\n      var dimIdxToName_1 = this._dimIdxToName = zrUtil.createHashMap();\n      zrUtil.each(dimensionNames, function (dimName) {\n        dimIdxToName_1.set(dimensionInfos[dimName].storeDimIndex, dimName);\n      });\n    }\n  }\n  /**\r\n   *\r\n   * Get concrete dimension name by dimension name or dimension index.\r\n   * If input a dimension name, do not validate whether the dimension name exits.\r\n   *\r\n   * @caution\r\n   * @param dim Must make sure the dimension is `SeriesDimensionLoose`.\r\n   * Because only those dimensions will have auto-generated dimension names if not\r\n   * have a user-specified name, and other dimensions will get a return of null/undefined.\r\n   *\r\n   * @notice Because of this reason, should better use `getDimensionIndex` instead, for examples:\r\n   * ```js\r\n   * const val = data.getStore().get(data.getDimensionIndex(dim), dataIdx);\r\n   * ```\r\n   *\r\n   * @return Concrete dim name.\r\n   */\n  SeriesData.prototype.getDimension = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx == null) {\n      return dim;\n    }\n    dimIdx = dim;\n    if (!this._dimOmitted) {\n      return this.dimensions[dimIdx];\n    }\n    // Retrieve from series dimension definition because it probably contains\n    // generated dimension name (like 'x', 'y').\n    var dimName = this._dimIdxToName.get(dimIdx);\n    if (dimName != null) {\n      return dimName;\n    }\n    var sourceDimDef = this._schema.getSourceDimension(dimIdx);\n    if (sourceDimDef) {\n      return sourceDimDef.name;\n    }\n  };\n  /**\r\n   * Get dimension index in data store. Return -1 if not found.\r\n   * Can be used to index value from getRawValue.\r\n   */\n  SeriesData.prototype.getDimensionIndex = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx != null) {\n      return dimIdx;\n    }\n    if (dim == null) {\n      return -1;\n    }\n    var dimInfo = this._getDimInfo(dim);\n    return dimInfo ? dimInfo.storeDimIndex : this._dimOmitted ? this._schema.getSourceDimensionIndex(dim) : -1;\n  };\n  /**\r\n   * The meanings of the input parameter `dim`:\r\n   *\r\n   * + If dim is a number (e.g., `1`), it means the index of the dimension.\r\n   *   For example, `getDimension(0)` will return 'x' or 'lng' or 'radius'.\r\n   * + If dim is a number-like string (e.g., `\"1\"`):\r\n   *     + If there is the same concrete dim name defined in `series.dimensions` or `dataset.dimensions`,\r\n   *        it means that concrete name.\r\n   *     + If not, it will be converted to a number, which means the index of the dimension.\r\n   *        (why? because of the backward compatibility. We have been tolerating number-like string in\r\n   *        dimension setting, although now it seems that it is not a good idea.)\r\n   *     For example, `visualMap[i].dimension: \"1\"` is the same meaning as `visualMap[i].dimension: 1`,\r\n   *     if no dimension name is defined as `\"1\"`.\r\n   * + If dim is a not-number-like string, it means the concrete dim name.\r\n   *   For example, it can be be default name `\"x\"`, `\"y\"`, `\"z\"`, `\"lng\"`, `\"lat\"`, `\"angle\"`, `\"radius\"`,\r\n   *   or customized in `dimensions` property of option like `\"age\"`.\r\n   *\r\n   * @return recognized `DimensionIndex`. Otherwise return null/undefined (means that dim is `DimensionName`).\r\n   */\n  SeriesData.prototype._recognizeDimIndex = function (dim) {\n    if (zrUtil.isNumber(dim)\n    // If being a number-like string but not being defined as a dimension name.\n    || dim != null && !isNaN(dim) && !this._getDimInfo(dim) && (!this._dimOmitted || this._schema.getSourceDimensionIndex(dim) < 0)) {\n      return +dim;\n    }\n  };\n  SeriesData.prototype._getStoreDimIndex = function (dim) {\n    var dimIdx = this.getDimensionIndex(dim);\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimIdx == null) {\n        throw new Error('Unknown dimension ' + dim);\n      }\n    }\n    return dimIdx;\n  };\n  /**\r\n   * Get type and calculation info of particular dimension\r\n   * @param dim\r\n   *        Dimension can be concrete names like x, y, z, lng, lat, angle, radius\r\n   *        Or a ordinal number. For example getDimensionInfo(0) will return 'x' or 'lng' or 'radius'\r\n   */\n  SeriesData.prototype.getDimensionInfo = function (dim) {\n    // Do not clone, because there may be categories in dimInfo.\n    return this._getDimInfo(this.getDimension(dim));\n  };\n  SeriesData.prototype._initGetDimensionInfo = function (needsHasOwn) {\n    var dimensionInfos = this._dimInfos;\n    this._getDimInfo = needsHasOwn ? function (dimName) {\n      return dimensionInfos.hasOwnProperty(dimName) ? dimensionInfos[dimName] : undefined;\n    } : function (dimName) {\n      return dimensionInfos[dimName];\n    };\n  };\n  /**\r\n   * concrete dimension name list on coord.\r\n   */\n  SeriesData.prototype.getDimensionsOnCoord = function () {\n    return this._dimSummary.dataDimsOnCoord.slice();\n  };\n  SeriesData.prototype.mapDimension = function (coordDim, idx) {\n    var dimensionsSummary = this._dimSummary;\n    if (idx == null) {\n      return dimensionsSummary.encodeFirstDimNotExtra[coordDim];\n    }\n    var dims = dimensionsSummary.encode[coordDim];\n    return dims ? dims[idx] : null;\n  };\n  SeriesData.prototype.mapDimensionsAll = function (coordDim) {\n    var dimensionsSummary = this._dimSummary;\n    var dims = dimensionsSummary.encode[coordDim];\n    return (dims || []).slice();\n  };\n  SeriesData.prototype.getStore = function () {\n    return this._store;\n  };\n  /**\r\n   * Initialize from data\r\n   * @param data source or data or data store.\r\n   * @param nameList The name of a datum is used on data diff and\r\n   *        default label/tooltip.\r\n   *        A name can be specified in encode.itemName,\r\n   *        or dataItem.name (only for series option data),\r\n   *        or provided in nameList from outside.\r\n   */\n  SeriesData.prototype.initData = function (data, nameList, dimValueGetter) {\n    var _this = this;\n    var store;\n    if (data instanceof DataStore) {\n      store = data;\n    }\n    if (!store) {\n      var dimensions = this.dimensions;\n      var provider = isSourceInstance(data) || zrUtil.isArrayLike(data) ? new DefaultDataProvider(data, dimensions.length) : data;\n      store = new DataStore();\n      var dimensionInfos = map(dimensions, function (dimName) {\n        return {\n          type: _this._dimInfos[dimName].type,\n          property: dimName\n        };\n      });\n      store.initData(provider, dimensionInfos, dimValueGetter);\n    }\n    this._store = store;\n    // Reset\n    this._nameList = (nameList || []).slice();\n    this._idList = [];\n    this._nameRepeatCount = {};\n    this._doInit(0, store.count());\n    // Cache summary info for fast visit. See \"dimensionHelper\".\n    // Needs to be initialized after store is prepared.\n    this._dimSummary = summarizeDimensions(this, this._schema);\n    this.userOutput = this._dimSummary.userOutput;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  SeriesData.prototype.appendData = function (data) {\n    var range = this._store.appendData(data);\n    this._doInit(range[0], range[1]);\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   * This method does not modify `rawData` (`dataProvider`), but only\r\n   * add values to store.\r\n   *\r\n   * The final count will be increased by `Math.max(values.length, names.length)`.\r\n   *\r\n   * @param values That is the SourceType: 'arrayRows', like\r\n   *        [\r\n   *            [12, 33, 44],\r\n   *            [NaN, 43, 1],\r\n   *            ['-', 'asdf', 0]\r\n   *        ]\r\n   *        Each item is exactly corresponding to a dimension.\r\n   */\n  SeriesData.prototype.appendValues = function (values, names) {\n    var _a = this._store.appendValues(values, names && names.length),\n      start = _a.start,\n      end = _a.end;\n    var shouldMakeIdFromName = this._shouldMakeIdFromName();\n    this._updateOrdinalMeta();\n    if (names) {\n      for (var idx = start; idx < end; idx++) {\n        var sourceIdx = idx - start;\n        this._nameList[idx] = names[sourceIdx];\n        if (shouldMakeIdFromName) {\n          makeIdFromName(this, idx);\n        }\n      }\n    }\n  };\n  SeriesData.prototype._updateOrdinalMeta = function () {\n    var store = this._store;\n    var dimensions = this.dimensions;\n    for (var i = 0; i < dimensions.length; i++) {\n      var dimInfo = this._dimInfos[dimensions[i]];\n      if (dimInfo.ordinalMeta) {\n        store.collectOrdinalMeta(dimInfo.storeDimIndex, dimInfo.ordinalMeta);\n      }\n    }\n  };\n  SeriesData.prototype._shouldMakeIdFromName = function () {\n    var provider = this._store.getProvider();\n    return this._idDimIdx == null && provider.getSource().sourceFormat !== SOURCE_FORMAT_TYPED_ARRAY && !provider.fillStorage;\n  };\n  SeriesData.prototype._doInit = function (start, end) {\n    if (start >= end) {\n      return;\n    }\n    var store = this._store;\n    var provider = store.getProvider();\n    this._updateOrdinalMeta();\n    var nameList = this._nameList;\n    var idList = this._idList;\n    var sourceFormat = provider.getSource().sourceFormat;\n    var isFormatOriginal = sourceFormat === SOURCE_FORMAT_ORIGINAL;\n    // Each data item is value\n    // [1, 2]\n    // 2\n    // Bar chart, line chart which uses category axis\n    // only gives the 'y' value. 'x' value is the indices of category\n    // Use a tempValue to normalize the value to be a (x, y) value\n    // If dataItem is {name: ...} or {id: ...}, it has highest priority.\n    // This kind of ids and names are always stored `_nameList` and `_idList`.\n    if (isFormatOriginal && !provider.pure) {\n      var sharedDataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        var dataItem = provider.getItem(idx, sharedDataItem);\n        if (!this.hasItemOption && isDataItemOption(dataItem)) {\n          this.hasItemOption = true;\n        }\n        if (dataItem) {\n          var itemName = dataItem.name;\n          if (nameList[idx] == null && itemName != null) {\n            nameList[idx] = convertOptionIdName(itemName, null);\n          }\n          var itemId = dataItem.id;\n          if (idList[idx] == null && itemId != null) {\n            idList[idx] = convertOptionIdName(itemId, null);\n          }\n        }\n      }\n    }\n    if (this._shouldMakeIdFromName()) {\n      for (var idx = start; idx < end; idx++) {\n        makeIdFromName(this, idx);\n      }\n    }\n    prepareInvertedIndex(this);\n  };\n  /**\r\n   * PENDING: In fact currently this function is only used to short-circuit\r\n   * the calling of `scale.unionExtentFromData` when data have been filtered by modules\r\n   * like \"dataZoom\". `scale.unionExtentFromData` is used to calculate data extent for series on\r\n   * an axis, but if a \"axis related data filter module\" is used, the extent of the axis have\r\n   * been fixed and no need to calling `scale.unionExtentFromData` actually.\r\n   * But if we add \"custom data filter\" in future, which is not \"axis related\", this method may\r\n   * be still needed.\r\n   *\r\n   * Optimize for the scenario that data is filtered by a given extent.\r\n   * Consider that if data amount is more than hundreds of thousand,\r\n   * extent calculation will cost more than 10ms and the cache will\r\n   * be erased because of the filtering.\r\n   */\n  SeriesData.prototype.getApproximateExtent = function (dim) {\n    return this._approximateExtent[dim] || this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  /**\r\n   * Calculate extent on a filtered data might be time consuming.\r\n   * Approximate extent is only used for: calculate extent of filtered data outside.\r\n   */\n  SeriesData.prototype.setApproximateExtent = function (extent, dim) {\n    dim = this.getDimension(dim);\n    this._approximateExtent[dim] = extent.slice();\n  };\n  SeriesData.prototype.getCalculationInfo = function (key) {\n    return this._calculationInfo[key];\n  };\n  SeriesData.prototype.setCalculationInfo = function (key, value) {\n    isObject(key) ? zrUtil.extend(this._calculationInfo, key) : this._calculationInfo[key] = value;\n  };\n  /**\r\n   * @return Never be null/undefined. `number` will be converted to string. Because:\r\n   * In most cases, name is used in display, where returning a string is more convenient.\r\n   * In other cases, name is used in query (see `indexOfName`), where we can keep the\r\n   * rule that name `2` equals to name `'2'`.\r\n   */\n  SeriesData.prototype.getName = function (idx) {\n    var rawIndex = this.getRawIndex(idx);\n    var name = this._nameList[rawIndex];\n    if (name == null && this._nameDimIdx != null) {\n      name = getIdNameFromStore(this, this._nameDimIdx, rawIndex);\n    }\n    if (name == null) {\n      name = '';\n    }\n    return name;\n  };\n  SeriesData.prototype._getCategory = function (dimIdx, idx) {\n    var ordinal = this._store.get(dimIdx, idx);\n    var ordinalMeta = this._store.getOrdinalMeta(dimIdx);\n    if (ordinalMeta) {\n      return ordinalMeta.categories[ordinal];\n    }\n    return ordinal;\n  };\n  /**\r\n   * @return Never null/undefined. `number` will be converted to string. Because:\r\n   * In all cases having encountered at present, id is used in making diff comparison, which\r\n   * are usually based on hash map. We can keep the rule that the internal id are always string\r\n   * (treat `2` is the same as `'2'`) to make the related logic simple.\r\n   */\n  SeriesData.prototype.getId = function (idx) {\n    return getId(this, this.getRawIndex(idx));\n  };\n  SeriesData.prototype.count = function () {\n    return this._store.count();\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   *\r\n   * @notice Should better to use `data.getStore().get(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.get = function (dim, idx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.get(dimInfo.storeDimIndex, idx);\n    }\n  };\n  /**\r\n   * @notice Should better to use `data.getStore().getByRawIndex(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.getByRawIndex = function (dim, rawIdx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.getByRawIndex(dimInfo.storeDimIndex, rawIdx);\n    }\n  };\n  SeriesData.prototype.getIndices = function () {\n    return this._store.getIndices();\n  };\n  SeriesData.prototype.getDataExtent = function (dim) {\n    return this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getSum = function (dim) {\n    return this._store.getSum(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getMedian = function (dim) {\n    return this._store.getMedian(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getValues = function (dimensions, idx) {\n    var _this = this;\n    var store = this._store;\n    return zrUtil.isArray(dimensions) ? store.getValues(map(dimensions, function (dim) {\n      return _this._getStoreDimIndex(dim);\n    }), idx) : store.getValues(dimensions);\n  };\n  /**\r\n   * If value is NaN. Including '-'\r\n   * Only check the coord dimensions.\r\n   */\n  SeriesData.prototype.hasValue = function (idx) {\n    var dataDimIndicesOnCoord = this._dimSummary.dataDimIndicesOnCoord;\n    for (var i = 0, len = dataDimIndicesOnCoord.length; i < len; i++) {\n      // Ordinal type originally can be string or number.\n      // But when an ordinal type is used on coord, it can\n      // not be string but only number. So we can also use isNaN.\n      if (isNaN(this._store.get(dataDimIndicesOnCoord[i], idx))) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\r\n   * Retrieve the index with given name\r\n   */\n  SeriesData.prototype.indexOfName = function (name) {\n    for (var i = 0, len = this._store.count(); i < len; i++) {\n      if (this.getName(i) === name) {\n        return i;\n      }\n    }\n    return -1;\n  };\n  SeriesData.prototype.getRawIndex = function (idx) {\n    return this._store.getRawIndex(idx);\n  };\n  SeriesData.prototype.indexOfRawIndex = function (rawIndex) {\n    return this._store.indexOfRawIndex(rawIndex);\n  };\n  /**\r\n   * Only support the dimension which inverted index created.\r\n   * Do not support other cases until required.\r\n   * @param dim concrete dim\r\n   * @param value ordinal index\r\n   * @return rawIndex\r\n   */\n  SeriesData.prototype.rawIndexOf = function (dim, value) {\n    var invertedIndices = dim && this._invertedIndicesMap[dim];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!invertedIndices) {\n        throw new Error('Do not supported yet');\n      }\n    }\n    var rawIndex = invertedIndices && invertedIndices[value];\n    if (rawIndex == null || isNaN(rawIndex)) {\n      return INDEX_NOT_FOUND;\n    }\n    return rawIndex;\n  };\n  /**\r\n   * Retrieve the index of nearest value\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices has\r\n   *         the same value, they are put to the result.\r\n   */\n  SeriesData.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    return this._store.indicesOfNearest(this._getStoreDimIndex(dim), value, maxDistance);\n  };\n  SeriesData.prototype.each = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store.each(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  SeriesData.prototype.filterSelf = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store = this._store.filter(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return this;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  SeriesData.prototype.selectRange = function (range) {\n    'use strict';\n\n    var _this = this;\n    var innerRange = {};\n    var dims = zrUtil.keys(range);\n    var dimIndices = [];\n    zrUtil.each(dims, function (dim) {\n      var dimIdx = _this._getStoreDimIndex(dim);\n      innerRange[dimIdx] = range[dim];\n      dimIndices.push(dimIdx);\n    });\n    this._store = this._store.selectRange(innerRange);\n    return this;\n  };\n  /* eslint-enable max-len */\n  SeriesData.prototype.mapArray = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    ctx = ctx || this;\n    var result = [];\n    this.each(dims, function () {\n      result.push(cb && cb.apply(this, arguments));\n    }, ctx);\n    return result;\n  };\n  SeriesData.prototype.map = function (dims, cb, ctx, ctxCompat) {\n    'use strict';\n\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.map(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return list;\n  };\n  SeriesData.prototype.modify = function (dims, cb, ctx, ctxCompat) {\n    var _this = this;\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.each(normalizeDimensions(dims), function (dim) {\n        var dimInfo = _this.getDimensionInfo(dim);\n        if (!dimInfo.isCalculationCoord) {\n          console.error('Danger: only stack dimension can be modified');\n        }\n      });\n    }\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    // If do shallow clone here, if there are too many stacked series,\n    // it still cost lots of memory, because `_store.dimensions` are not shared.\n    // We should consider there probably be shallow clone happen in each series\n    // in consequent filter/map.\n    this._store.modify(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  SeriesData.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.downSample(this._getStoreDimIndex(dimension), rate, sampleValue, sampleIndex);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  SeriesData.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.minmaxDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  SeriesData.prototype.lttbDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.lttbDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  SeriesData.prototype.getRawDataItem = function (idx) {\n    return this._store.getRawDataItem(idx);\n  };\n  /**\r\n   * Get model of one data item.\r\n   */\n  // TODO: Type of data item\n  SeriesData.prototype.getItemModel = function (idx) {\n    var hostModel = this.hostModel;\n    var dataItem = this.getRawDataItem(idx);\n    return new Model(dataItem, hostModel, hostModel && hostModel.ecModel);\n  };\n  /**\r\n   * Create a data differ\r\n   */\n  SeriesData.prototype.diff = function (otherList) {\n    var thisList = this;\n    return new DataDiffer(otherList ? otherList.getStore().getIndices() : [], this.getStore().getIndices(), function (idx) {\n      return getId(otherList, idx);\n    }, function (idx) {\n      return getId(thisList, idx);\n    });\n  };\n  /**\r\n   * Get visual property.\r\n   */\n  SeriesData.prototype.getVisual = function (key) {\n    var visual = this._visual;\n    return visual && visual[key];\n  };\n  SeriesData.prototype.setVisual = function (kvObj, val) {\n    this._visual = this._visual || {};\n    if (isObject(kvObj)) {\n      zrUtil.extend(this._visual, kvObj);\n    } else {\n      this._visual[kvObj] = val;\n    }\n  };\n  /**\r\n   * Get visual property of single data item\r\n   */\n  // eslint-disable-next-line\n  SeriesData.prototype.getItemVisual = function (idx, key) {\n    var itemVisual = this._itemVisuals[idx];\n    var val = itemVisual && itemVisual[key];\n    if (val == null) {\n      // Use global visual property\n      return this.getVisual(key);\n    }\n    return val;\n  };\n  /**\r\n   * If exists visual property of single data item\r\n   */\n  SeriesData.prototype.hasItemVisual = function () {\n    return this._itemVisuals.length > 0;\n  };\n  /**\r\n   * Make sure itemVisual property is unique\r\n   */\n  // TODO: use key to save visual to reduce memory.\n  SeriesData.prototype.ensureUniqueItemVisual = function (idx, key) {\n    var itemVisuals = this._itemVisuals;\n    var itemVisual = itemVisuals[idx];\n    if (!itemVisual) {\n      itemVisual = itemVisuals[idx] = {};\n    }\n    var val = itemVisual[key];\n    if (val == null) {\n      val = this.getVisual(key);\n      // TODO Performance?\n      if (zrUtil.isArray(val)) {\n        val = val.slice();\n      } else if (isObject(val)) {\n        val = zrUtil.extend({}, val);\n      }\n      itemVisual[key] = val;\n    }\n    return val;\n  };\n  // eslint-disable-next-line\n  SeriesData.prototype.setItemVisual = function (idx, key, value) {\n    var itemVisual = this._itemVisuals[idx] || {};\n    this._itemVisuals[idx] = itemVisual;\n    if (isObject(key)) {\n      zrUtil.extend(itemVisual, key);\n    } else {\n      itemVisual[key] = value;\n    }\n  };\n  /**\r\n   * Clear itemVisuals and list visual.\r\n   */\n  SeriesData.prototype.clearAllVisual = function () {\n    this._visual = {};\n    this._itemVisuals = [];\n  };\n  SeriesData.prototype.setLayout = function (key, val) {\n    isObject(key) ? zrUtil.extend(this._layout, key) : this._layout[key] = val;\n  };\n  /**\r\n   * Get layout property.\r\n   */\n  SeriesData.prototype.getLayout = function (key) {\n    return this._layout[key];\n  };\n  /**\r\n   * Get layout of single data item\r\n   */\n  SeriesData.prototype.getItemLayout = function (idx) {\n    return this._itemLayouts[idx];\n  };\n  /**\r\n   * Set layout of single data item\r\n   */\n  SeriesData.prototype.setItemLayout = function (idx, layout, merge) {\n    this._itemLayouts[idx] = merge ? zrUtil.extend(this._itemLayouts[idx] || {}, layout) : layout;\n  };\n  /**\r\n   * Clear all layout of single data item\r\n   */\n  SeriesData.prototype.clearItemLayouts = function () {\n    this._itemLayouts.length = 0;\n  };\n  /**\r\n   * Set graphic element relative to data. It can be set as null\r\n   */\n  SeriesData.prototype.setItemGraphicEl = function (idx, el) {\n    var seriesIndex = this.hostModel && this.hostModel.seriesIndex;\n    setCommonECData(seriesIndex, this.dataType, idx, el);\n    this._graphicEls[idx] = el;\n  };\n  SeriesData.prototype.getItemGraphicEl = function (idx) {\n    return this._graphicEls[idx];\n  };\n  SeriesData.prototype.eachItemGraphicEl = function (cb, context) {\n    zrUtil.each(this._graphicEls, function (el, idx) {\n      if (el) {\n        cb && cb.call(context, el, idx);\n      }\n    });\n  };\n  /**\r\n   * Shallow clone a new list except visual and layout properties, and graph elements.\r\n   * New list only change the indices.\r\n   */\n  SeriesData.prototype.cloneShallow = function (list) {\n    if (!list) {\n      list = new SeriesData(this._schema ? this._schema : map(this.dimensions, this._getDimInfo, this), this.hostModel);\n    }\n    transferProperties(list, this);\n    list._store = this._store;\n    return list;\n  };\n  /**\r\n   * Wrap some method to add more feature\r\n   */\n  SeriesData.prototype.wrapMethod = function (methodName, injectFunction) {\n    var originalMethod = this[methodName];\n    if (!zrUtil.isFunction(originalMethod)) {\n      return;\n    }\n    this.__wrappedMethods = this.__wrappedMethods || [];\n    this.__wrappedMethods.push(methodName);\n    this[methodName] = function () {\n      var res = originalMethod.apply(this, arguments);\n      return injectFunction.apply(this, [res].concat(zrUtil.slice(arguments)));\n    };\n  };\n  // ----------------------------------------------------------\n  // A work around for internal method visiting private member.\n  // ----------------------------------------------------------\n  SeriesData.internalField = function () {\n    prepareInvertedIndex = function (data) {\n      var invertedIndicesMap = data._invertedIndicesMap;\n      zrUtil.each(invertedIndicesMap, function (invertedIndices, dim) {\n        var dimInfo = data._dimInfos[dim];\n        // Currently, only dimensions that has ordinalMeta can create inverted indices.\n        var ordinalMeta = dimInfo.ordinalMeta;\n        var store = data._store;\n        if (ordinalMeta) {\n          invertedIndices = invertedIndicesMap[dim] = new CtorInt32Array(ordinalMeta.categories.length);\n          // The default value of TypedArray is 0. To avoid miss\n          // mapping to 0, we should set it as INDEX_NOT_FOUND.\n          for (var i = 0; i < invertedIndices.length; i++) {\n            invertedIndices[i] = INDEX_NOT_FOUND;\n          }\n          for (var i = 0; i < store.count(); i++) {\n            // Only support the case that all values are distinct.\n            invertedIndices[store.get(dimInfo.storeDimIndex, i)] = i;\n          }\n        }\n      });\n    };\n    getIdNameFromStore = function (data, dimIdx, idx) {\n      return convertOptionIdName(data._getCategory(dimIdx, idx), null);\n    };\n    /**\r\n     * @see the comment of `List['getId']`.\r\n     */\n    getId = function (data, rawIndex) {\n      var id = data._idList[rawIndex];\n      if (id == null && data._idDimIdx != null) {\n        id = getIdNameFromStore(data, data._idDimIdx, rawIndex);\n      }\n      if (id == null) {\n        id = ID_PREFIX + rawIndex;\n      }\n      return id;\n    };\n    normalizeDimensions = function (dimensions) {\n      if (!zrUtil.isArray(dimensions)) {\n        dimensions = dimensions != null ? [dimensions] : [];\n      }\n      return dimensions;\n    };\n    /**\r\n     * Data in excludeDimensions is copied, otherwise transferred.\r\n     */\n    cloneListForMapAndSample = function (original) {\n      var list = new SeriesData(original._schema ? original._schema : map(original.dimensions, original._getDimInfo, original), original.hostModel);\n      // FIXME If needs stackedOn, value may already been stacked\n      transferProperties(list, original);\n      return list;\n    };\n    transferProperties = function (target, source) {\n      zrUtil.each(TRANSFERABLE_PROPERTIES.concat(source.__wrappedMethods || []), function (propName) {\n        if (source.hasOwnProperty(propName)) {\n          target[propName] = source[propName];\n        }\n      });\n      target.__wrappedMethods = source.__wrappedMethods;\n      zrUtil.each(CLONE_PROPERTIES, function (propName) {\n        target[propName] = zrUtil.clone(source[propName]);\n      });\n      target._calculationInfo = zrUtil.extend({}, source._calculationInfo);\n    };\n    makeIdFromName = function (data, idx) {\n      var nameList = data._nameList;\n      var idList = data._idList;\n      var nameDimIdx = data._nameDimIdx;\n      var idDimIdx = data._idDimIdx;\n      var name = nameList[idx];\n      var id = idList[idx];\n      if (name == null && nameDimIdx != null) {\n        nameList[idx] = name = getIdNameFromStore(data, nameDimIdx, idx);\n      }\n      if (id == null && idDimIdx != null) {\n        idList[idx] = id = getIdNameFromStore(data, idDimIdx, idx);\n      }\n      if (id == null && name != null) {\n        var nameRepeatCount = data._nameRepeatCount;\n        var nmCnt = nameRepeatCount[name] = (nameRepeatCount[name] || 0) + 1;\n        id = name;\n        if (nmCnt > 1) {\n          id += '__ec__' + nmCnt;\n        }\n        idList[idx] = id;\n      }\n    };\n  }();\n  return SeriesData;\n}();\nexport default SeriesData;", "import windingLine from './windingLine.js';\nvar EPSILON = 1e-8;\nfunction isAroundEqual(a, b) {\n    return Math.abs(a - b) < EPSILON;\n}\nexport function contain(points, x, y) {\n    var w = 0;\n    var p = points[0];\n    if (!p) {\n        return false;\n    }\n    for (var i = 1; i < points.length; i++) {\n        var p2 = points[i];\n        w += windingLine(p[0], p[1], p2[0], p2[1], x, y);\n        p = p2;\n    }\n    var p0 = points[0];\n    if (!isAroundEqual(p[0], p0[0]) || !isAroundEqual(p[1], p0[1])) {\n        w += windingLine(p[0], p[1], p0[0], p0[1], x, y);\n    }\n    return w !== 0;\n}\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as polygonContain from 'zrender/lib/contain/polygon.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar TMP_TRANSFORM = [];\nfunction transformPoints(points, transform) {\n  for (var p = 0; p < points.length; p++) {\n    vec2.applyTransform(points[p], points[p], transform);\n  }\n}\nfunction updateBBoxFromPoints(points, min, max, projection) {\n  for (var i = 0; i < points.length; i++) {\n    var p = points[i];\n    if (projection) {\n      // projection may return null point.\n      p = projection.project(p);\n    }\n    if (p && isFinite(p[0]) && isFinite(p[1])) {\n      vec2.min(min, min, p);\n      vec2.max(max, max, p);\n    }\n  }\n}\nfunction centroid(points) {\n  var signedArea = 0;\n  var cx = 0;\n  var cy = 0;\n  var len = points.length;\n  var x0 = points[len - 1][0];\n  var y0 = points[len - 1][1];\n  // Polygon should been closed.\n  for (var i = 0; i < len; i++) {\n    var x1 = points[i][0];\n    var y1 = points[i][1];\n    var a = x0 * y1 - x1 * y0;\n    signedArea += a;\n    cx += (x0 + x1) * a;\n    cy += (y0 + y1) * a;\n    x0 = x1;\n    y0 = y1;\n  }\n  return signedArea ? [cx / signedArea / 3, cy / signedArea / 3, signedArea] : [points[0][0] || 0, points[0][1] || 0];\n}\nvar Region = /** @class */function () {\n  function Region(name) {\n    this.name = name;\n  }\n  Region.prototype.setCenter = function (center) {\n    this._center = center;\n  };\n  /**\r\n   * Get center point in data unit. That is,\r\n   * for GeoJSONRegion, the unit is lat/lng,\r\n   * for GeoSVGRegion, the unit is SVG local coord.\r\n   */\n  Region.prototype.getCenter = function () {\n    var center = this._center;\n    if (!center) {\n      // In most cases there are no need to calculate this center.\n      // So calculate only when called.\n      center = this._center = this.calcCenter();\n    }\n    return center;\n  };\n  return Region;\n}();\nexport { Region };\nvar GeoJSONPolygonGeometry = /** @class */function () {\n  function GeoJSONPolygonGeometry(exterior, interiors) {\n    this.type = 'polygon';\n    this.exterior = exterior;\n    this.interiors = interiors;\n  }\n  return GeoJSONPolygonGeometry;\n}();\nexport { GeoJSONPolygonGeometry };\nvar GeoJSONLineStringGeometry = /** @class */function () {\n  function GeoJSONLineStringGeometry(points) {\n    this.type = 'linestring';\n    this.points = points;\n  }\n  return GeoJSONLineStringGeometry;\n}();\nexport { GeoJSONLineStringGeometry };\nvar GeoJSONRegion = /** @class */function (_super) {\n  __extends(GeoJSONRegion, _super);\n  function GeoJSONRegion(name, geometries, cp) {\n    var _this = _super.call(this, name) || this;\n    _this.type = 'geoJSON';\n    _this.geometries = geometries;\n    _this._center = cp && [cp[0], cp[1]];\n    return _this;\n  }\n  GeoJSONRegion.prototype.calcCenter = function () {\n    var geometries = this.geometries;\n    var largestGeo;\n    var largestGeoSize = 0;\n    for (var i = 0; i < geometries.length; i++) {\n      var geo = geometries[i];\n      var exterior = geo.exterior;\n      // Simple trick to use points count instead of polygon area as region size.\n      // Ignore linestring\n      var size = exterior && exterior.length;\n      if (size > largestGeoSize) {\n        largestGeo = geo;\n        largestGeoSize = size;\n      }\n    }\n    if (largestGeo) {\n      return centroid(largestGeo.exterior);\n    }\n    // from bounding rect by default.\n    var rect = this.getBoundingRect();\n    return [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  };\n  GeoJSONRegion.prototype.getBoundingRect = function (projection) {\n    var rect = this._rect;\n    // Always recalculate if using projection.\n    if (rect && !projection) {\n      return rect;\n    }\n    var min = [Infinity, Infinity];\n    var max = [-Infinity, -Infinity];\n    var geometries = this.geometries;\n    each(geometries, function (geo) {\n      if (geo.type === 'polygon') {\n        // Doesn't consider hole\n        updateBBoxFromPoints(geo.exterior, min, max, projection);\n      } else {\n        each(geo.points, function (points) {\n          updateBBoxFromPoints(points, min, max, projection);\n        });\n      }\n    });\n    // Normalie invalid bounding.\n    if (!(isFinite(min[0]) && isFinite(min[1]) && isFinite(max[0]) && isFinite(max[1]))) {\n      min[0] = min[1] = max[0] = max[1] = 0;\n    }\n    rect = new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    if (!projection) {\n      this._rect = rect;\n    }\n    return rect;\n  };\n  GeoJSONRegion.prototype.contain = function (coord) {\n    var rect = this.getBoundingRect();\n    var geometries = this.geometries;\n    if (!rect.contain(coord[0], coord[1])) {\n      return false;\n    }\n    loopGeo: for (var i = 0, len = geometries.length; i < len; i++) {\n      var geo = geometries[i];\n      // Only support polygon.\n      if (geo.type !== 'polygon') {\n        continue;\n      }\n      var exterior = geo.exterior;\n      var interiors = geo.interiors;\n      if (polygonContain.contain(exterior, coord[0], coord[1])) {\n        // Not in the region if point is in the hole.\n        for (var k = 0; k < (interiors ? interiors.length : 0); k++) {\n          if (polygonContain.contain(interiors[k], coord[0], coord[1])) {\n            continue loopGeo;\n          }\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n  /**\r\n   * Transform the raw coords to target bounding.\r\n   * @param x\r\n   * @param y\r\n   * @param width\r\n   * @param height\r\n   */\n  GeoJSONRegion.prototype.transformTo = function (x, y, width, height) {\n    var rect = this.getBoundingRect();\n    var aspect = rect.width / rect.height;\n    if (!width) {\n      width = aspect * height;\n    } else if (!height) {\n      height = width / aspect;\n    }\n    var target = new BoundingRect(x, y, width, height);\n    var transform = rect.calculateTransform(target);\n    var geometries = this.geometries;\n    for (var i = 0; i < geometries.length; i++) {\n      var geo = geometries[i];\n      if (geo.type === 'polygon') {\n        transformPoints(geo.exterior, transform);\n        each(geo.interiors, function (interior) {\n          transformPoints(interior, transform);\n        });\n      } else {\n        each(geo.points, function (points) {\n          transformPoints(points, transform);\n        });\n      }\n    }\n    rect = this._rect;\n    rect.copy(target);\n    // Update center\n    this._center = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  };\n  GeoJSONRegion.prototype.cloneShallow = function (name) {\n    name == null && (name = this.name);\n    var newRegion = new GeoJSONRegion(name, this.geometries, this._center);\n    newRegion._rect = this._rect;\n    newRegion.transformTo = null; // Simply avoid to be called.\n    return newRegion;\n  };\n  return GeoJSONRegion;\n}(Region);\nexport { GeoJSONRegion };\nvar GeoSVGRegion = /** @class */function (_super) {\n  __extends(GeoSVGRegion, _super);\n  function GeoSVGRegion(name, elOnlyForCalculate) {\n    var _this = _super.call(this, name) || this;\n    _this.type = 'geoSVG';\n    _this._elOnlyForCalculate = elOnlyForCalculate;\n    return _this;\n  }\n  GeoSVGRegion.prototype.calcCenter = function () {\n    var el = this._elOnlyForCalculate;\n    var rect = el.getBoundingRect();\n    var center = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n    var mat = matrix.identity(TMP_TRANSFORM);\n    var target = el;\n    while (target && !target.isGeoSVGGraphicRoot) {\n      matrix.mul(mat, target.getLocalTransform(), mat);\n      target = target.parent;\n    }\n    matrix.invert(mat, mat);\n    vec2.applyTransform(center, center, mat);\n    return center;\n  };\n  return GeoSVGRegion;\n}(Region);\nexport { GeoSVGRegion };", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Parse and decode geo json\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { GeoJSONLineStringGeometry, GeoJSONPolygonGeometry, GeoJSONRegion } from './Region.js';\nfunction decode(json) {\n  if (!json.UTF8Encoding) {\n    return json;\n  }\n  var jsonCompressed = json;\n  var encodeScale = jsonCompressed.UTF8Scale;\n  if (encodeScale == null) {\n    encodeScale = 1024;\n  }\n  var features = jsonCompressed.features;\n  zrUtil.each(features, function (feature) {\n    var geometry = feature.geometry;\n    var encodeOffsets = geometry.encodeOffsets;\n    var coordinates = geometry.coordinates;\n    // Geometry may be appeded manually in the script after json loaded.\n    // In this case this geometry is usually not encoded.\n    if (!encodeOffsets) {\n      return;\n    }\n    switch (geometry.type) {\n      case 'LineString':\n        geometry.coordinates = decodeRing(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'Polygon':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiLineString':\n        decodeRings(coordinates, encodeOffsets, encodeScale);\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(coordinates, function (rings, idx) {\n          return decodeRings(rings, encodeOffsets[idx], encodeScale);\n        });\n    }\n  });\n  // Has been decoded\n  jsonCompressed.UTF8Encoding = false;\n  return jsonCompressed;\n}\nfunction decodeRings(rings, encodeOffsets, encodeScale) {\n  for (var c = 0; c < rings.length; c++) {\n    rings[c] = decodeRing(rings[c], encodeOffsets[c], encodeScale);\n  }\n}\nfunction decodeRing(coordinate, encodeOffsets, encodeScale) {\n  var result = [];\n  var prevX = encodeOffsets[0];\n  var prevY = encodeOffsets[1];\n  for (var i = 0; i < coordinate.length; i += 2) {\n    var x = coordinate.charCodeAt(i) - 64;\n    var y = coordinate.charCodeAt(i + 1) - 64;\n    // ZigZag decoding\n    x = x >> 1 ^ -(x & 1);\n    y = y >> 1 ^ -(y & 1);\n    // Delta deocding\n    x += prevX;\n    y += prevY;\n    prevX = x;\n    prevY = y;\n    // Dequantize\n    result.push([x / encodeScale, y / encodeScale]);\n  }\n  return result;\n}\nexport default function parseGeoJSON(geoJson, nameProperty) {\n  geoJson = decode(geoJson);\n  return zrUtil.map(zrUtil.filter(geoJson.features, function (featureObj) {\n    // Output of mapshaper may have geometry null\n    return featureObj.geometry && featureObj.properties && featureObj.geometry.coordinates.length > 0;\n  }), function (featureObj) {\n    var properties = featureObj.properties;\n    var geo = featureObj.geometry;\n    var geometries = [];\n    switch (geo.type) {\n      case 'Polygon':\n        var coordinates = geo.coordinates;\n        // According to the GeoJSON specification.\n        // First must be exterior, and the rest are all interior(holes).\n        geometries.push(new GeoJSONPolygonGeometry(coordinates[0], coordinates.slice(1)));\n        break;\n      case 'MultiPolygon':\n        zrUtil.each(geo.coordinates, function (item) {\n          if (item[0]) {\n            geometries.push(new GeoJSONPolygonGeometry(item[0], item.slice(1)));\n          }\n        });\n        break;\n      case 'LineString':\n        geometries.push(new GeoJSONLineStringGeometry([geo.coordinates]));\n        break;\n      case 'MultiLineString':\n        geometries.push(new GeoJSONLineStringGeometry(geo.coordinates));\n    }\n    var region = new GeoJSONRegion(properties[nameProperty || 'name'], geometries, properties.cp);\n    region.properties = properties;\n    return region;\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as clazzUtil from '../util/clazz.js';\nvar Scale = /** @class */function () {\n  function Scale(setting) {\n    this._setting = setting || {};\n    this._extent = [Infinity, -Infinity];\n  }\n  Scale.prototype.getSetting = function (name) {\n    return this._setting[name];\n  };\n  /**\r\n   * Set extent from data\r\n   */\n  Scale.prototype.unionExtent = function (other) {\n    var extent = this._extent;\n    other[0] < extent[0] && (extent[0] = other[0]);\n    other[1] > extent[1] && (extent[1] = other[1]);\n    // not setExtent because in log axis it may transformed to power\n    // this.setExtent(extent[0], extent[1]);\n  };\n  /**\r\n   * Set extent from data\r\n   */\n  Scale.prototype.unionExtentFromData = function (data, dim) {\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * Get extent\r\n   *\r\n   * Extent is always in increase order.\r\n   */\n  Scale.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  /**\r\n   * Set extent\r\n   */\n  Scale.prototype.setExtent = function (start, end) {\n    var thisExtent = this._extent;\n    if (!isNaN(start)) {\n      thisExtent[0] = start;\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = end;\n    }\n  };\n  /**\r\n   * If value is in extent range\r\n   */\n  Scale.prototype.isInExtentRange = function (value) {\n    return this._extent[0] <= value && this._extent[1] >= value;\n  };\n  /**\r\n   * When axis extent depends on data and no data exists,\r\n   * axis ticks should not be drawn, which is named 'blank'.\r\n   */\n  Scale.prototype.isBlank = function () {\n    return this._isBlank;\n  };\n  /**\r\n   * When axis extent depends on data and no data exists,\r\n   * axis ticks should not be drawn, which is named 'blank'.\r\n   */\n  Scale.prototype.setBlank = function (isBlank) {\n    this._isBlank = isBlank;\n  };\n  return Scale;\n}();\nclazzUtil.enableClassManagement(Scale);\nexport default Scale;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, isObject, map, isString } from 'zrender/lib/core/util.js';\nvar uidBase = 0;\nvar OrdinalMeta = /** @class */function () {\n  function OrdinalMeta(opt) {\n    this.categories = opt.categories || [];\n    this._needCollect = opt.needCollect;\n    this._deduplication = opt.deduplication;\n    this.uid = ++uidBase;\n  }\n  OrdinalMeta.createByAxisModel = function (axisModel) {\n    var option = axisModel.option;\n    var data = option.data;\n    var categories = data && map(data, getName);\n    return new OrdinalMeta({\n      categories: categories,\n      needCollect: !categories,\n      // deduplication is default in axis.\n      deduplication: option.dedplication !== false\n    });\n  };\n  ;\n  OrdinalMeta.prototype.getOrdinal = function (category) {\n    // @ts-ignore\n    return this._getOrCreateMap().get(category);\n  };\n  /**\r\n   * @return The ordinal. If not found, return NaN.\r\n   */\n  OrdinalMeta.prototype.parseAndCollect = function (category) {\n    var index;\n    var needCollect = this._needCollect;\n    // The value of category dim can be the index of the given category set.\n    // This feature is only supported when !needCollect, because we should\n    // consider a common case: a value is 2017, which is a number but is\n    // expected to be tread as a category. This case usually happen in dataset,\n    // where it happent to be no need of the index feature.\n    if (!isString(category) && !needCollect) {\n      return category;\n    }\n    // Optimize for the scenario:\n    // category is ['2012-01-01', '2012-01-02', ...], where the input\n    // data has been ensured not duplicate and is large data.\n    // Notice, if a dataset dimension provide categroies, usually echarts\n    // should remove duplication except user tell echarts dont do that\n    // (set axis.deduplication = false), because echarts do not know whether\n    // the values in the category dimension has duplication (consider the\n    // parallel-aqi example)\n    if (needCollect && !this._deduplication) {\n      index = this.categories.length;\n      this.categories[index] = category;\n      return index;\n    }\n    var map = this._getOrCreateMap();\n    // @ts-ignore\n    index = map.get(category);\n    if (index == null) {\n      if (needCollect) {\n        index = this.categories.length;\n        this.categories[index] = category;\n        // @ts-ignore\n        map.set(category, index);\n      } else {\n        index = NaN;\n      }\n    }\n    return index;\n  };\n  // Consider big data, do not create map until needed.\n  OrdinalMeta.prototype._getOrCreateMap = function () {\n    return this._map || (this._map = createHashMap(this.categories));\n  };\n  return OrdinalMeta;\n}();\nfunction getName(obj) {\n  if (isObject(obj) && obj.value != null) {\n    return obj.value;\n  } else {\n    return obj + '';\n  }\n}\nexport default OrdinalMeta;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getPrecision, round, nice, quantityExponent } from '../util/number.js';\nexport function isValueNice(val) {\n  var exp10 = Math.pow(10, quantityExponent(Math.abs(val)));\n  var f = Math.abs(val / exp10);\n  return f === 0 || f === 1 || f === 2 || f === 3 || f === 5;\n}\nexport function isIntervalOrLogScale(scale) {\n  return scale.type === 'interval' || scale.type === 'log';\n}\n/**\r\n * @param extent Both extent[0] and extent[1] should be valid number.\r\n *               Should be extent[0] < extent[1].\r\n * @param splitNumber splitNumber should be >= 1.\r\n */\nexport function intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval) {\n  var result = {};\n  var span = extent[1] - extent[0];\n  var interval = result.interval = nice(span / splitNumber, true);\n  if (minInterval != null && interval < minInterval) {\n    interval = result.interval = minInterval;\n  }\n  if (maxInterval != null && interval > maxInterval) {\n    interval = result.interval = maxInterval;\n  }\n  // Tow more digital for tick.\n  var precision = result.intervalPrecision = getIntervalPrecision(interval);\n  // Niced extent inside original extent\n  var niceTickExtent = result.niceTickExtent = [round(Math.ceil(extent[0] / interval) * interval, precision), round(Math.floor(extent[1] / interval) * interval, precision)];\n  fixExtent(niceTickExtent, extent);\n  return result;\n}\nexport function increaseInterval(interval) {\n  var exp10 = Math.pow(10, quantityExponent(interval));\n  // Increase interval\n  var f = interval / exp10;\n  if (!f) {\n    f = 1;\n  } else if (f === 2) {\n    f = 3;\n  } else if (f === 3) {\n    f = 5;\n  } else {\n    // f is 1 or 5\n    f *= 2;\n  }\n  return round(f * exp10);\n}\n/**\r\n * @return interval precision\r\n */\nexport function getIntervalPrecision(interval) {\n  // Tow more digital for tick.\n  return getPrecision(interval) + 2;\n}\nfunction clamp(niceTickExtent, idx, extent) {\n  niceTickExtent[idx] = Math.max(Math.min(niceTickExtent[idx], extent[1]), extent[0]);\n}\n// In some cases (e.g., splitNumber is 1), niceTickExtent may be out of extent.\nexport function fixExtent(niceTickExtent, extent) {\n  !isFinite(niceTickExtent[0]) && (niceTickExtent[0] = extent[0]);\n  !isFinite(niceTickExtent[1]) && (niceTickExtent[1] = extent[1]);\n  clamp(niceTickExtent, 0, extent);\n  clamp(niceTickExtent, 1, extent);\n  if (niceTickExtent[0] > niceTickExtent[1]) {\n    niceTickExtent[0] = niceTickExtent[1];\n  }\n}\nexport function contain(val, extent) {\n  return val >= extent[0] && val <= extent[1];\n}\nexport function normalize(val, extent) {\n  if (extent[1] === extent[0]) {\n    return 0.5;\n  }\n  return (val - extent[0]) / (extent[1] - extent[0]);\n}\nexport function scale(val, extent) {\n  return val * (extent[1] - extent[0]) + extent[0];\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Linear continuous scale\r\n * http://en.wikipedia.org/wiki/Level_of_measurement\r\n */\n// FIXME only one data\nimport Scale from './Scale.js';\nimport OrdinalMeta from '../data/OrdinalMeta.js';\nimport * as scaleHelper from './helper.js';\nimport { isArray, map, isObject, isString } from 'zrender/lib/core/util.js';\nvar OrdinalScale = /** @class */function (_super) {\n  __extends(OrdinalScale, _super);\n  function OrdinalScale(setting) {\n    var _this = _super.call(this, setting) || this;\n    _this.type = 'ordinal';\n    var ordinalMeta = _this.getSetting('ordinalMeta');\n    // Caution: Should not use instanceof, consider ec-extensions using\n    // import approach to get OrdinalMeta class.\n    if (!ordinalMeta) {\n      ordinalMeta = new OrdinalMeta({});\n    }\n    if (isArray(ordinalMeta)) {\n      ordinalMeta = new OrdinalMeta({\n        categories: map(ordinalMeta, function (item) {\n          return isObject(item) ? item.value : item;\n        })\n      });\n    }\n    _this._ordinalMeta = ordinalMeta;\n    _this._extent = _this.getSetting('extent') || [0, ordinalMeta.categories.length - 1];\n    return _this;\n  }\n  OrdinalScale.prototype.parse = function (val) {\n    // Caution: Math.round(null) will return `0` rather than `NaN`\n    if (val == null) {\n      return NaN;\n    }\n    return isString(val) ? this._ordinalMeta.getOrdinal(val)\n    // val might be float.\n    : Math.round(val);\n  };\n  OrdinalScale.prototype.contain = function (rank) {\n    rank = this.parse(rank);\n    return scaleHelper.contain(rank, this._extent) && this._ordinalMeta.categories[rank] != null;\n  };\n  /**\r\n   * Normalize given rank or name to linear [0, 1]\r\n   * @param val raw ordinal number.\r\n   * @return normalized value in [0, 1].\r\n   */\n  OrdinalScale.prototype.normalize = function (val) {\n    val = this._getTickNumber(this.parse(val));\n    return scaleHelper.normalize(val, this._extent);\n  };\n  /**\r\n   * @param val normalized value in [0, 1].\r\n   * @return raw ordinal number.\r\n   */\n  OrdinalScale.prototype.scale = function (val) {\n    val = Math.round(scaleHelper.scale(val, this._extent));\n    return this.getRawOrdinalNumber(val);\n  };\n  OrdinalScale.prototype.getTicks = function () {\n    var ticks = [];\n    var extent = this._extent;\n    var rank = extent[0];\n    while (rank <= extent[1]) {\n      ticks.push({\n        value: rank\n      });\n      rank++;\n    }\n    return ticks;\n  };\n  OrdinalScale.prototype.getMinorTicks = function (splitNumber) {\n    // Not support.\n    return;\n  };\n  /**\r\n   * @see `Ordinal['_ordinalNumbersByTick']`\r\n   */\n  OrdinalScale.prototype.setSortInfo = function (info) {\n    if (info == null) {\n      this._ordinalNumbersByTick = this._ticksByOrdinalNumber = null;\n      return;\n    }\n    var infoOrdinalNumbers = info.ordinalNumbers;\n    var ordinalsByTick = this._ordinalNumbersByTick = [];\n    var ticksByOrdinal = this._ticksByOrdinalNumber = [];\n    // Unnecessary support negative tick in `realtimeSort`.\n    var tickNum = 0;\n    var allCategoryLen = this._ordinalMeta.categories.length;\n    for (var len = Math.min(allCategoryLen, infoOrdinalNumbers.length); tickNum < len; ++tickNum) {\n      var ordinalNumber = infoOrdinalNumbers[tickNum];\n      ordinalsByTick[tickNum] = ordinalNumber;\n      ticksByOrdinal[ordinalNumber] = tickNum;\n    }\n    // Handle that `series.data` only covers part of the `axis.category.data`.\n    var unusedOrdinal = 0;\n    for (; tickNum < allCategoryLen; ++tickNum) {\n      while (ticksByOrdinal[unusedOrdinal] != null) {\n        unusedOrdinal++;\n      }\n      ;\n      ordinalsByTick.push(unusedOrdinal);\n      ticksByOrdinal[unusedOrdinal] = tickNum;\n    }\n  };\n  OrdinalScale.prototype._getTickNumber = function (ordinal) {\n    var ticksByOrdinalNumber = this._ticksByOrdinalNumber;\n    // also support ordinal out of range of `ordinalMeta.categories.length`,\n    // where ordinal numbers are used as tick value directly.\n    return ticksByOrdinalNumber && ordinal >= 0 && ordinal < ticksByOrdinalNumber.length ? ticksByOrdinalNumber[ordinal] : ordinal;\n  };\n  /**\r\n   * @usage\r\n   * ```js\r\n   * const ordinalNumber = ordinalScale.getRawOrdinalNumber(tickVal);\r\n   *\r\n   * // case0\r\n   * const rawOrdinalValue = axisModel.getCategories()[ordinalNumber];\r\n   * // case1\r\n   * const rawOrdinalValue = this._ordinalMeta.categories[ordinalNumber];\r\n   * // case2\r\n   * const coord = axis.dataToCoord(ordinalNumber);\r\n   * ```\r\n   *\r\n   * @param {OrdinalNumber} tickNumber index of display\r\n   */\n  OrdinalScale.prototype.getRawOrdinalNumber = function (tickNumber) {\n    var ordinalNumbersByTick = this._ordinalNumbersByTick;\n    // tickNumber may be out of range, e.g., when axis max is larger than `ordinalMeta.categories.length`.,\n    // where ordinal numbers are used as tick value directly.\n    return ordinalNumbersByTick && tickNumber >= 0 && tickNumber < ordinalNumbersByTick.length ? ordinalNumbersByTick[tickNumber] : tickNumber;\n  };\n  /**\r\n   * Get item on tick\r\n   */\n  OrdinalScale.prototype.getLabel = function (tick) {\n    if (!this.isBlank()) {\n      var ordinalNumber = this.getRawOrdinalNumber(tick.value);\n      var cateogry = this._ordinalMeta.categories[ordinalNumber];\n      // Note that if no data, ordinalMeta.categories is an empty array.\n      // Return empty if it's not exist.\n      return cateogry == null ? '' : cateogry + '';\n    }\n  };\n  OrdinalScale.prototype.count = function () {\n    return this._extent[1] - this._extent[0] + 1;\n  };\n  OrdinalScale.prototype.unionExtentFromData = function (data, dim) {\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * @override\r\n   * If value is in extent range\r\n   */\n  OrdinalScale.prototype.isInExtentRange = function (value) {\n    value = this._getTickNumber(value);\n    return this._extent[0] <= value && this._extent[1] >= value;\n  };\n  OrdinalScale.prototype.getOrdinalMeta = function () {\n    return this._ordinalMeta;\n  };\n  OrdinalScale.prototype.calcNiceTicks = function () {};\n  OrdinalScale.prototype.calcNiceExtent = function () {};\n  OrdinalScale.type = 'ordinal';\n  return OrdinalScale;\n}(Scale);\nScale.registerClass(OrdinalScale);\nexport default OrdinalScale;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as numberUtil from '../util/number.js';\nimport * as formatUtil from '../util/format.js';\nimport Scale from './Scale.js';\nimport * as helper from './helper.js';\nvar roundNumber = numberUtil.round;\nvar IntervalScale = /** @class */function (_super) {\n  __extends(IntervalScale, _super);\n  function IntervalScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'interval';\n    // Step is calculated in adjustExtent.\n    _this._interval = 0;\n    _this._intervalPrecision = 2;\n    return _this;\n  }\n  IntervalScale.prototype.parse = function (val) {\n    return val;\n  };\n  IntervalScale.prototype.contain = function (val) {\n    return helper.contain(val, this._extent);\n  };\n  IntervalScale.prototype.normalize = function (val) {\n    return helper.normalize(val, this._extent);\n  };\n  IntervalScale.prototype.scale = function (val) {\n    return helper.scale(val, this._extent);\n  };\n  IntervalScale.prototype.setExtent = function (start, end) {\n    var thisExtent = this._extent;\n    // start,end may be a Number like '25',so...\n    if (!isNaN(start)) {\n      thisExtent[0] = parseFloat(start);\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = parseFloat(end);\n    }\n  };\n  IntervalScale.prototype.unionExtent = function (other) {\n    var extent = this._extent;\n    other[0] < extent[0] && (extent[0] = other[0]);\n    other[1] > extent[1] && (extent[1] = other[1]);\n    // unionExtent may called by it's sub classes\n    this.setExtent(extent[0], extent[1]);\n  };\n  IntervalScale.prototype.getInterval = function () {\n    return this._interval;\n  };\n  IntervalScale.prototype.setInterval = function (interval) {\n    this._interval = interval;\n    // Dropped auto calculated niceExtent and use user-set extent.\n    // We assume user wants to set both interval, min, max to get a better result.\n    this._niceExtent = this._extent.slice();\n    this._intervalPrecision = helper.getIntervalPrecision(interval);\n  };\n  /**\r\n   * @param expandToNicedExtent Whether expand the ticks to niced extent.\r\n   */\n  IntervalScale.prototype.getTicks = function (expandToNicedExtent) {\n    var interval = this._interval;\n    var extent = this._extent;\n    var niceTickExtent = this._niceExtent;\n    var intervalPrecision = this._intervalPrecision;\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    // Consider this case: using dataZoom toolbox, zoom and zoom.\n    var safeLimit = 10000;\n    if (extent[0] < niceTickExtent[0]) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(niceTickExtent[0] - interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[0]\n        });\n      }\n    }\n    var tick = niceTickExtent[0];\n    while (tick <= niceTickExtent[1]) {\n      ticks.push({\n        value: tick\n      });\n      // Avoid rounding error\n      tick = roundNumber(tick + interval, intervalPrecision);\n      if (tick === ticks[ticks.length - 1].value) {\n        // Consider out of safe float point, e.g.,\n        // -3711126.9907707 + 2e-10 === -3711126.9907707\n        break;\n      }\n      if (ticks.length > safeLimit) {\n        return [];\n      }\n    }\n    // Consider this case: the last item of ticks is smaller\n    // than niceTickExtent[1] and niceTickExtent[1] === extent[1].\n    var lastNiceTick = ticks.length ? ticks[ticks.length - 1].value : niceTickExtent[1];\n    if (extent[1] > lastNiceTick) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(lastNiceTick + interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[1]\n        });\n      }\n    }\n    return ticks;\n  };\n  IntervalScale.prototype.getMinorTicks = function (splitNumber) {\n    var ticks = this.getTicks(true);\n    var minorTicks = [];\n    var extent = this.getExtent();\n    for (var i = 1; i < ticks.length; i++) {\n      var nextTick = ticks[i];\n      var prevTick = ticks[i - 1];\n      var count = 0;\n      var minorTicksGroup = [];\n      var interval = nextTick.value - prevTick.value;\n      var minorInterval = interval / splitNumber;\n      while (count < splitNumber - 1) {\n        var minorTick = roundNumber(prevTick.value + (count + 1) * minorInterval);\n        // For the first and last interval. The count may be less than splitNumber.\n        if (minorTick > extent[0] && minorTick < extent[1]) {\n          minorTicksGroup.push(minorTick);\n        }\n        count++;\n      }\n      minorTicks.push(minorTicksGroup);\n    }\n    return minorTicks;\n  };\n  /**\r\n   * @param opt.precision If 'auto', use nice presision.\r\n   * @param opt.pad returns 1.50 but not 1.5 if precision is 2.\r\n   */\n  IntervalScale.prototype.getLabel = function (data, opt) {\n    if (data == null) {\n      return '';\n    }\n    var precision = opt && opt.precision;\n    if (precision == null) {\n      precision = numberUtil.getPrecision(data.value) || 0;\n    } else if (precision === 'auto') {\n      // Should be more precise then tick.\n      precision = this._intervalPrecision;\n    }\n    // (1) If `precision` is set, 12.005 should be display as '12.00500'.\n    // (2) Use roundNumber (toFixed) to avoid scientific notation like '3.5e-7'.\n    var dataNum = roundNumber(data.value, precision, true);\n    return formatUtil.addCommas(dataNum);\n  };\n  /**\r\n   * @param splitNumber By default `5`.\r\n   */\n  IntervalScale.prototype.calcNiceTicks = function (splitNumber, minInterval, maxInterval) {\n    splitNumber = splitNumber || 5;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (!isFinite(span)) {\n      return;\n    }\n    // User may set axis min 0 and data are all negative\n    // FIXME If it needs to reverse ?\n    if (span < 0) {\n      span = -span;\n      extent.reverse();\n    }\n    var result = helper.intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval);\n    this._intervalPrecision = result.intervalPrecision;\n    this._interval = result.interval;\n    this._niceExtent = result.niceTickExtent;\n  };\n  IntervalScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent;\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      if (extent[0] !== 0) {\n        // Expand extent\n        // Note that extents can be both negative. See #13154\n        var expandSize = Math.abs(extent[0]);\n        // In the fowllowing case\n        //      Axis has been fixed max 100\n        //      Plus data are all 100 and axis extent are [100, 100].\n        // Extend to the both side will cause expanded max is larger than fixed max.\n        // So only expand to the smaller side.\n        if (!opt.fixMax) {\n          extent[1] += expandSize / 2;\n          extent[0] -= expandSize / 2;\n        } else {\n          extent[0] -= expandSize / 2;\n        }\n      } else {\n        extent[1] = 1;\n      }\n    }\n    var span = extent[1] - extent[0];\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (!isFinite(span)) {\n      extent[0] = 0;\n      extent[1] = 1;\n    }\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n    // let extent = this._extent;\n    var interval = this._interval;\n    if (!opt.fixMin) {\n      extent[0] = roundNumber(Math.floor(extent[0] / interval) * interval);\n    }\n    if (!opt.fixMax) {\n      extent[1] = roundNumber(Math.ceil(extent[1] / interval) * interval);\n    }\n  };\n  IntervalScale.prototype.setNiceExtent = function (min, max) {\n    this._niceExtent = [min, max];\n  };\n  IntervalScale.type = 'interval';\n  return IntervalScale;\n}(Scale);\nScale.registerClass(IntervalScale);\nexport default IntervalScale;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isString } from 'zrender/lib/core/util.js';\nimport { isSeriesDataSchema } from './SeriesDataSchema.js';\n/**\r\n * Note that it is too complicated to support 3d stack by value\r\n * (have to create two-dimension inverted index), so in 3d case\r\n * we just support that stacked by index.\r\n *\r\n * @param seriesModel\r\n * @param dimensionsInput The same as the input of <module:echarts/data/SeriesData>.\r\n *        The input will be modified.\r\n * @param opt\r\n * @param opt.stackedCoordDimension Specify a coord dimension if needed.\r\n * @param opt.byIndex=false\r\n * @return calculationInfo\r\n * {\r\n *     stackedDimension: string\r\n *     stackedByDimension: string\r\n *     isStackedByIndex: boolean\r\n *     stackedOverDimension: string\r\n *     stackResultDimension: string\r\n * }\r\n */\nexport function enableDataStack(seriesModel, dimensionsInput, opt) {\n  opt = opt || {};\n  var byIndex = opt.byIndex;\n  var stackedCoordDimension = opt.stackedCoordDimension;\n  var dimensionDefineList;\n  var schema;\n  var store;\n  if (isLegacyDimensionsInput(dimensionsInput)) {\n    dimensionDefineList = dimensionsInput;\n  } else {\n    schema = dimensionsInput.schema;\n    dimensionDefineList = schema.dimensions;\n    store = dimensionsInput.store;\n  }\n  // Compatibal: when `stack` is set as '', do not stack.\n  var mayStack = !!(seriesModel && seriesModel.get('stack'));\n  var stackedByDimInfo;\n  var stackedDimInfo;\n  var stackResultDimension;\n  var stackedOverDimension;\n  each(dimensionDefineList, function (dimensionInfo, index) {\n    if (isString(dimensionInfo)) {\n      dimensionDefineList[index] = dimensionInfo = {\n        name: dimensionInfo\n      };\n    }\n    if (mayStack && !dimensionInfo.isExtraCoord) {\n      // Find the first ordinal dimension as the stackedByDimInfo.\n      if (!byIndex && !stackedByDimInfo && dimensionInfo.ordinalMeta) {\n        stackedByDimInfo = dimensionInfo;\n      }\n      // Find the first stackable dimension as the stackedDimInfo.\n      if (!stackedDimInfo && dimensionInfo.type !== 'ordinal' && dimensionInfo.type !== 'time' && (!stackedCoordDimension || stackedCoordDimension === dimensionInfo.coordDim)) {\n        stackedDimInfo = dimensionInfo;\n      }\n    }\n  });\n  if (stackedDimInfo && !byIndex && !stackedByDimInfo) {\n    // Compatible with previous design, value axis (time axis) only stack by index.\n    // It may make sense if the user provides elaborately constructed data.\n    byIndex = true;\n  }\n  // Add stack dimension, they can be both calculated by coordinate system in `unionExtent`.\n  // That put stack logic in List is for using conveniently in echarts extensions, but it\n  // might not be a good way.\n  if (stackedDimInfo) {\n    // Use a weird name that not duplicated with other names.\n    // Also need to use seriesModel.id as postfix because different\n    // series may share same data store. The stack dimension needs to be distinguished.\n    stackResultDimension = '__\\0ecstackresult_' + seriesModel.id;\n    stackedOverDimension = '__\\0ecstackedover_' + seriesModel.id;\n    // Create inverted index to fast query index by value.\n    if (stackedByDimInfo) {\n      stackedByDimInfo.createInvertedIndices = true;\n    }\n    var stackedDimCoordDim_1 = stackedDimInfo.coordDim;\n    var stackedDimType = stackedDimInfo.type;\n    var stackedDimCoordIndex_1 = 0;\n    each(dimensionDefineList, function (dimensionInfo) {\n      if (dimensionInfo.coordDim === stackedDimCoordDim_1) {\n        stackedDimCoordIndex_1++;\n      }\n    });\n    var stackedOverDimensionDefine = {\n      name: stackResultDimension,\n      coordDim: stackedDimCoordDim_1,\n      coordDimIndex: stackedDimCoordIndex_1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length\n    };\n    var stackResultDimensionDefine = {\n      name: stackedOverDimension,\n      // This dimension contains stack base (generally, 0), so do not set it as\n      // `stackedDimCoordDim` to avoid extent calculation, consider log scale.\n      coordDim: stackedOverDimension,\n      coordDimIndex: stackedDimCoordIndex_1 + 1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length + 1\n    };\n    if (schema) {\n      if (store) {\n        stackedOverDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackedOverDimension, stackedDimType);\n        stackResultDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackResultDimension, stackedDimType);\n      }\n      schema.appendCalculationDimension(stackedOverDimensionDefine);\n      schema.appendCalculationDimension(stackResultDimensionDefine);\n    } else {\n      dimensionDefineList.push(stackedOverDimensionDefine);\n      dimensionDefineList.push(stackResultDimensionDefine);\n    }\n  }\n  return {\n    stackedDimension: stackedDimInfo && stackedDimInfo.name,\n    stackedByDimension: stackedByDimInfo && stackedByDimInfo.name,\n    isStackedByIndex: byIndex,\n    stackedOverDimension: stackedOverDimension,\n    stackResultDimension: stackResultDimension\n  };\n}\nfunction isLegacyDimensionsInput(dimensionsInput) {\n  return !isSeriesDataSchema(dimensionsInput.schema);\n}\nexport function isDimensionStacked(data, stackedDim) {\n  // Each single series only maps to one pair of axis. So we do not need to\n  // check stackByDim, whatever stacked by a dimension or stacked by index.\n  return !!stackedDim && stackedDim === data.getCalculationInfo('stackedDimension');\n}\nexport function getStackedDimension(data, targetDim) {\n  return isDimensionStacked(data, targetDim) ? data.getCalculationInfo('stackResultDimension') : targetDim;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\n/* global Float32Array */\nvar supportFloat32Array = typeof Float32Array !== 'undefined';\nvar Float32ArrayCtor = !supportFloat32Array ? Array : Float32Array;\nexport function createFloat32Array(arg) {\n  if (isArray(arg)) {\n    // Return self directly if don't support TypedArray.\n    return supportFloat32Array ? new Float32Array(arg) : arg;\n  }\n  // Else is number\n  return new Float32ArrayCtor(arg);\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, defaults, keys } from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../util/number.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { createFloat32Array } from '../util/vendor.js';\nvar STACK_PREFIX = '__ec_stack_';\nfunction getSeriesStackId(seriesModel) {\n  return seriesModel.get('stack') || STACK_PREFIX + seriesModel.seriesIndex;\n}\nfunction getAxisKey(axis) {\n  return axis.dim + axis.index;\n}\n/**\r\n * @return {Object} {width, offset, offsetCenter} If axis.type is not 'category', return undefined.\r\n */\nexport function getLayoutOnAxis(opt) {\n  var params = [];\n  var baseAxis = opt.axis;\n  var axisKey = 'axis0';\n  if (baseAxis.type !== 'category') {\n    return;\n  }\n  var bandWidth = baseAxis.getBandWidth();\n  for (var i = 0; i < opt.count || 0; i++) {\n    params.push(defaults({\n      bandWidth: bandWidth,\n      axisKey: axisKey,\n      stackId: STACK_PREFIX + i\n    }, opt));\n  }\n  var widthAndOffsets = doCalBarWidthAndOffset(params);\n  var result = [];\n  for (var i = 0; i < opt.count; i++) {\n    var item = widthAndOffsets[axisKey][STACK_PREFIX + i];\n    item.offsetCenter = item.offset + item.width / 2;\n    result.push(item);\n  }\n  return result;\n}\nexport function prepareLayoutBarSeries(seriesType, ecModel) {\n  var seriesModels = [];\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    // Check series coordinate, do layout for cartesian2d only\n    if (isOnCartesian(seriesModel)) {\n      seriesModels.push(seriesModel);\n    }\n  });\n  return seriesModels;\n}\n/**\r\n * Map from (baseAxis.dim + '_' + baseAxis.index) to min gap of two adjacent\r\n * values.\r\n * This works for time axes, value axes, and log axes.\r\n * For a single time axis, return value is in the form like\r\n * {'x_0': [1000000]}.\r\n * The value of 1000000 is in milliseconds.\r\n */\nfunction getValueAxesMinGaps(barSeries) {\n  /**\r\n   * Map from axis.index to values.\r\n   * For a single time axis, axisValues is in the form like\r\n   * {'x_0': [1495555200000, 1495641600000, 1495728000000]}.\r\n   * Items in axisValues[x], e.g. 1495555200000, are time values of all\r\n   * series.\r\n   */\n  var axisValues = {};\n  each(barSeries, function (seriesModel) {\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    if (baseAxis.type !== 'time' && baseAxis.type !== 'value') {\n      return;\n    }\n    var data = seriesModel.getData();\n    var key = baseAxis.dim + '_' + baseAxis.index;\n    var dimIdx = data.getDimensionIndex(data.mapDimension(baseAxis.dim));\n    var store = data.getStore();\n    for (var i = 0, cnt = store.count(); i < cnt; ++i) {\n      var value = store.get(dimIdx, i);\n      if (!axisValues[key]) {\n        // No previous data for the axis\n        axisValues[key] = [value];\n      } else {\n        // No value in previous series\n        axisValues[key].push(value);\n      }\n      // Ignore duplicated time values in the same axis\n    }\n  });\n  var axisMinGaps = {};\n  for (var key in axisValues) {\n    if (axisValues.hasOwnProperty(key)) {\n      var valuesInAxis = axisValues[key];\n      if (valuesInAxis) {\n        // Sort axis values into ascending order to calculate gaps\n        valuesInAxis.sort(function (a, b) {\n          return a - b;\n        });\n        var min = null;\n        for (var j = 1; j < valuesInAxis.length; ++j) {\n          var delta = valuesInAxis[j] - valuesInAxis[j - 1];\n          if (delta > 0) {\n            // Ignore 0 delta because they are of the same axis value\n            min = min === null ? delta : Math.min(min, delta);\n          }\n        }\n        // Set to null if only have one data\n        axisMinGaps[key] = min;\n      }\n    }\n  }\n  return axisMinGaps;\n}\nexport function makeColumnLayout(barSeries) {\n  var axisMinGaps = getValueAxesMinGaps(barSeries);\n  var seriesInfoList = [];\n  each(barSeries, function (seriesModel) {\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var axisExtent = baseAxis.getExtent();\n    var bandWidth;\n    if (baseAxis.type === 'category') {\n      bandWidth = baseAxis.getBandWidth();\n    } else if (baseAxis.type === 'value' || baseAxis.type === 'time') {\n      var key = baseAxis.dim + '_' + baseAxis.index;\n      var minGap = axisMinGaps[key];\n      var extentSpan = Math.abs(axisExtent[1] - axisExtent[0]);\n      var scale = baseAxis.scale.getExtent();\n      var scaleSpan = Math.abs(scale[1] - scale[0]);\n      bandWidth = minGap ? extentSpan / scaleSpan * minGap : extentSpan; // When there is only one data value\n    } else {\n      var data = seriesModel.getData();\n      bandWidth = Math.abs(axisExtent[1] - axisExtent[0]) / data.count();\n    }\n    var barWidth = parsePercent(seriesModel.get('barWidth'), bandWidth);\n    var barMaxWidth = parsePercent(seriesModel.get('barMaxWidth'), bandWidth);\n    var barMinWidth = parsePercent(\n    // barMinWidth by default is 0.5 / 1 in cartesian. Because in value axis,\n    // the auto-calculated bar width might be less than 0.5 / 1.\n    seriesModel.get('barMinWidth') || (isInLargeMode(seriesModel) ? 0.5 : 1), bandWidth);\n    var barGap = seriesModel.get('barGap');\n    var barCategoryGap = seriesModel.get('barCategoryGap');\n    seriesInfoList.push({\n      bandWidth: bandWidth,\n      barWidth: barWidth,\n      barMaxWidth: barMaxWidth,\n      barMinWidth: barMinWidth,\n      barGap: barGap,\n      barCategoryGap: barCategoryGap,\n      axisKey: getAxisKey(baseAxis),\n      stackId: getSeriesStackId(seriesModel)\n    });\n  });\n  return doCalBarWidthAndOffset(seriesInfoList);\n}\nfunction doCalBarWidthAndOffset(seriesInfoList) {\n  // Columns info on each category axis. Key is cartesian name\n  var columnsMap = {};\n  each(seriesInfoList, function (seriesInfo, idx) {\n    var axisKey = seriesInfo.axisKey;\n    var bandWidth = seriesInfo.bandWidth;\n    var columnsOnAxis = columnsMap[axisKey] || {\n      bandWidth: bandWidth,\n      remainedWidth: bandWidth,\n      autoWidthCount: 0,\n      categoryGap: null,\n      gap: '20%',\n      stacks: {}\n    };\n    var stacks = columnsOnAxis.stacks;\n    columnsMap[axisKey] = columnsOnAxis;\n    var stackId = seriesInfo.stackId;\n    if (!stacks[stackId]) {\n      columnsOnAxis.autoWidthCount++;\n    }\n    stacks[stackId] = stacks[stackId] || {\n      width: 0,\n      maxWidth: 0\n    };\n    // Caution: In a single coordinate system, these barGrid attributes\n    // will be shared by series. Consider that they have default values,\n    // only the attributes set on the last series will work.\n    // Do not change this fact unless there will be a break change.\n    var barWidth = seriesInfo.barWidth;\n    if (barWidth && !stacks[stackId].width) {\n      // See #6312, do not restrict width.\n      stacks[stackId].width = barWidth;\n      barWidth = Math.min(columnsOnAxis.remainedWidth, barWidth);\n      columnsOnAxis.remainedWidth -= barWidth;\n    }\n    var barMaxWidth = seriesInfo.barMaxWidth;\n    barMaxWidth && (stacks[stackId].maxWidth = barMaxWidth);\n    var barMinWidth = seriesInfo.barMinWidth;\n    barMinWidth && (stacks[stackId].minWidth = barMinWidth);\n    var barGap = seriesInfo.barGap;\n    barGap != null && (columnsOnAxis.gap = barGap);\n    var barCategoryGap = seriesInfo.barCategoryGap;\n    barCategoryGap != null && (columnsOnAxis.categoryGap = barCategoryGap);\n  });\n  var result = {};\n  each(columnsMap, function (columnsOnAxis, coordSysName) {\n    result[coordSysName] = {};\n    var stacks = columnsOnAxis.stacks;\n    var bandWidth = columnsOnAxis.bandWidth;\n    var categoryGapPercent = columnsOnAxis.categoryGap;\n    if (categoryGapPercent == null) {\n      var columnCount = keys(stacks).length;\n      // More columns in one group\n      // the spaces between group is smaller. Or the column will be too thin.\n      categoryGapPercent = Math.max(35 - columnCount * 4, 15) + '%';\n    }\n    var categoryGap = parsePercent(categoryGapPercent, bandWidth);\n    var barGapPercent = parsePercent(columnsOnAxis.gap, 1);\n    var remainedWidth = columnsOnAxis.remainedWidth;\n    var autoWidthCount = columnsOnAxis.autoWidthCount;\n    var autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    // Find if any auto calculated bar exceeded maxBarWidth\n    each(stacks, function (column) {\n      var maxWidth = column.maxWidth;\n      var minWidth = column.minWidth;\n      if (!column.width) {\n        var finalWidth = autoWidth;\n        if (maxWidth && maxWidth < finalWidth) {\n          finalWidth = Math.min(maxWidth, remainedWidth);\n        }\n        // `minWidth` has higher priority. `minWidth` decide that whether the\n        // bar is able to be visible. So `minWidth` should not be restricted\n        // by `maxWidth` or `remainedWidth` (which is from `bandWidth`). In\n        // the extreme cases for `value` axis, bars are allowed to overlap\n        // with each other if `minWidth` specified.\n        if (minWidth && minWidth > finalWidth) {\n          finalWidth = minWidth;\n        }\n        if (finalWidth !== autoWidth) {\n          column.width = finalWidth;\n          remainedWidth -= finalWidth + barGapPercent * finalWidth;\n          autoWidthCount--;\n        }\n      } else {\n        // `barMinWidth/barMaxWidth` has higher priority than `barWidth`, as\n        // CSS does. Because barWidth can be a percent value, where\n        // `barMaxWidth` can be used to restrict the final width.\n        var finalWidth = column.width;\n        if (maxWidth) {\n          finalWidth = Math.min(finalWidth, maxWidth);\n        }\n        // `minWidth` has higher priority, as described above\n        if (minWidth) {\n          finalWidth = Math.max(finalWidth, minWidth);\n        }\n        column.width = finalWidth;\n        remainedWidth -= finalWidth + barGapPercent * finalWidth;\n        autoWidthCount--;\n      }\n    });\n    // Recalculate width again\n    autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    var widthSum = 0;\n    var lastColumn;\n    each(stacks, function (column, idx) {\n      if (!column.width) {\n        column.width = autoWidth;\n      }\n      lastColumn = column;\n      widthSum += column.width * (1 + barGapPercent);\n    });\n    if (lastColumn) {\n      widthSum -= lastColumn.width * barGapPercent;\n    }\n    var offset = -widthSum / 2;\n    each(stacks, function (column, stackId) {\n      result[coordSysName][stackId] = result[coordSysName][stackId] || {\n        bandWidth: bandWidth,\n        offset: offset,\n        width: column.width\n      };\n      offset += column.width * (1 + barGapPercent);\n    });\n  });\n  return result;\n}\nfunction retrieveColumnLayout(barWidthAndOffset, axis, seriesModel) {\n  if (barWidthAndOffset && axis) {\n    var result = barWidthAndOffset[getAxisKey(axis)];\n    if (result != null && seriesModel != null) {\n      return result[getSeriesStackId(seriesModel)];\n    }\n    return result;\n  }\n}\nexport { retrieveColumnLayout };\nexport function layout(seriesType, ecModel) {\n  var seriesModels = prepareLayoutBarSeries(seriesType, ecModel);\n  var barWidthAndOffset = makeColumnLayout(seriesModels);\n  each(seriesModels, function (seriesModel) {\n    var data = seriesModel.getData();\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var stackId = getSeriesStackId(seriesModel);\n    var columnLayoutInfo = barWidthAndOffset[getAxisKey(baseAxis)][stackId];\n    var columnOffset = columnLayoutInfo.offset;\n    var columnWidth = columnLayoutInfo.width;\n    data.setLayout({\n      bandWidth: columnLayoutInfo.bandWidth,\n      offset: columnOffset,\n      size: columnWidth\n    });\n  });\n}\n// TODO: Do not support stack in large mode yet.\nexport function createProgressiveLayout(seriesType) {\n  return {\n    seriesType: seriesType,\n    plan: createRenderPlanner(),\n    reset: function (seriesModel) {\n      if (!isOnCartesian(seriesModel)) {\n        return;\n      }\n      var data = seriesModel.getData();\n      var cartesian = seriesModel.coordinateSystem;\n      var baseAxis = cartesian.getBaseAxis();\n      var valueAxis = cartesian.getOtherAxis(baseAxis);\n      var valueDimIdx = data.getDimensionIndex(data.mapDimension(valueAxis.dim));\n      var baseDimIdx = data.getDimensionIndex(data.mapDimension(baseAxis.dim));\n      var drawBackground = seriesModel.get('showBackground', true);\n      var valueDim = data.mapDimension(valueAxis.dim);\n      var stackResultDim = data.getCalculationInfo('stackResultDimension');\n      var stacked = isDimensionStacked(data, valueDim) && !!data.getCalculationInfo('stackedOnSeries');\n      var isValueAxisH = valueAxis.isHorizontal();\n      var valueAxisStart = getValueAxisStart(baseAxis, valueAxis);\n      var isLarge = isInLargeMode(seriesModel);\n      var barMinHeight = seriesModel.get('barMinHeight') || 0;\n      var stackedDimIdx = stackResultDim && data.getDimensionIndex(stackResultDim);\n      // Layout info.\n      var columnWidth = data.getLayout('size');\n      var columnOffset = data.getLayout('offset');\n      return {\n        progress: function (params, data) {\n          var count = params.count;\n          var largePoints = isLarge && createFloat32Array(count * 3);\n          var largeBackgroundPoints = isLarge && drawBackground && createFloat32Array(count * 3);\n          var largeDataIndices = isLarge && createFloat32Array(count);\n          var coordLayout = cartesian.master.getRect();\n          var bgSize = isValueAxisH ? coordLayout.width : coordLayout.height;\n          var dataIndex;\n          var store = data.getStore();\n          var idxOffset = 0;\n          while ((dataIndex = params.next()) != null) {\n            var value = store.get(stacked ? stackedDimIdx : valueDimIdx, dataIndex);\n            var baseValue = store.get(baseDimIdx, dataIndex);\n            var baseCoord = valueAxisStart;\n            var stackStartValue = void 0;\n            // Because of the barMinHeight, we can not use the value in\n            // stackResultDimension directly.\n            if (stacked) {\n              stackStartValue = +value - store.get(valueDimIdx, dataIndex);\n            }\n            var x = void 0;\n            var y = void 0;\n            var width = void 0;\n            var height = void 0;\n            if (isValueAxisH) {\n              var coord = cartesian.dataToPoint([value, baseValue]);\n              if (stacked) {\n                var startCoord = cartesian.dataToPoint([stackStartValue, baseValue]);\n                baseCoord = startCoord[0];\n              }\n              x = baseCoord;\n              y = coord[1] + columnOffset;\n              width = coord[0] - baseCoord;\n              height = columnWidth;\n              if (Math.abs(width) < barMinHeight) {\n                width = (width < 0 ? -1 : 1) * barMinHeight;\n              }\n            } else {\n              var coord = cartesian.dataToPoint([baseValue, value]);\n              if (stacked) {\n                var startCoord = cartesian.dataToPoint([baseValue, stackStartValue]);\n                baseCoord = startCoord[1];\n              }\n              x = coord[0] + columnOffset;\n              y = baseCoord;\n              width = columnWidth;\n              height = coord[1] - baseCoord;\n              if (Math.abs(height) < barMinHeight) {\n                // Include zero to has a positive bar\n                height = (height <= 0 ? -1 : 1) * barMinHeight;\n              }\n            }\n            if (!isLarge) {\n              data.setItemLayout(dataIndex, {\n                x: x,\n                y: y,\n                width: width,\n                height: height\n              });\n            } else {\n              largePoints[idxOffset] = x;\n              largePoints[idxOffset + 1] = y;\n              largePoints[idxOffset + 2] = isValueAxisH ? width : height;\n              if (largeBackgroundPoints) {\n                largeBackgroundPoints[idxOffset] = isValueAxisH ? coordLayout.x : x;\n                largeBackgroundPoints[idxOffset + 1] = isValueAxisH ? y : coordLayout.y;\n                largeBackgroundPoints[idxOffset + 2] = bgSize;\n              }\n              largeDataIndices[dataIndex] = dataIndex;\n            }\n            idxOffset += 3;\n          }\n          if (isLarge) {\n            data.setLayout({\n              largePoints: largePoints,\n              largeDataIndices: largeDataIndices,\n              largeBackgroundPoints: largeBackgroundPoints,\n              valueAxisHorizontal: isValueAxisH\n            });\n          }\n        }\n      };\n    }\n  };\n}\nfunction isOnCartesian(seriesModel) {\n  return seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'cartesian2d';\n}\nfunction isInLargeMode(seriesModel) {\n  return seriesModel.pipelineContext && seriesModel.pipelineContext.large;\n}\n// See cases in `test/bar-start.html` and `#7412`, `#8747`.\nfunction getValueAxisStart(baseAxis, valueAxis) {\n  var startValue = valueAxis.model.get('startValue');\n  if (!startValue) {\n    startValue = 0;\n  }\n  return valueAxis.toGlobalCoord(valueAxis.dataToCoord(valueAxis.type === 'log' ? startValue > 0 ? startValue : 1 : startValue));\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The \"scaleLevels\" was originally copied from \"d3.js\" with some\r\n* modifications made for this project.\r\n* (See more details in the comment on the definition of \"scaleLevels\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\n// [About UTC and local time zone]:\n// In most cases, `number.parseDate` will treat input data string as local time\n// (except time zone is specified in time string). And `format.formateTime` returns\n// local time by default. option.useUTC is false by default. This design has\n// considered these common cases:\n// (1) Time that is persistent in server is in UTC, but it is needed to be displayed\n// in local time by default.\n// (2) By default, the input data string (e.g., '2011-01-02') should be displayed\n// as its original time, without any time difference.\nimport * as numberUtil from '../util/number.js';\nimport { ONE_SECOND, ONE_MINUTE, ONE_HOUR, ONE_DAY, ONE_YEAR, format, leveledFormat, getUnitValue, timeUnits, fullLeveledFormatter, getPrimaryTimeUnit, isPrimaryTimeUnit, getDefaultFormatPrecisionOfInterval, fullYearGetterName, monthSetterName, fullYearSetterName, dateSetterName, hoursGetterName, hoursSetterName, minutesSetterName, secondsSetterName, millisecondsSetterName, monthGetterName, dateGetterName, minutesGetterName, secondsGetterName, millisecondsGetterName } from '../util/time.js';\nimport * as scaleHelper from './helper.js';\nimport IntervalScale from './Interval.js';\nimport Scale from './Scale.js';\nimport { warn } from '../util/log.js';\nimport { filter, isNumber, map } from 'zrender/lib/core/util.js';\n// FIXME 公用？\nvar bisect = function (a, x, lo, hi) {\n  while (lo < hi) {\n    var mid = lo + hi >>> 1;\n    if (a[mid][1] < x) {\n      lo = mid + 1;\n    } else {\n      hi = mid;\n    }\n  }\n  return lo;\n};\nvar TimeScale = /** @class */function (_super) {\n  __extends(TimeScale, _super);\n  function TimeScale(settings) {\n    var _this = _super.call(this, settings) || this;\n    _this.type = 'time';\n    return _this;\n  }\n  /**\r\n   * Get label is mainly for other components like dataZoom, tooltip.\r\n   */\n  TimeScale.prototype.getLabel = function (tick) {\n    var useUTC = this.getSetting('useUTC');\n    return format(tick.value, fullLeveledFormatter[getDefaultFormatPrecisionOfInterval(getPrimaryTimeUnit(this._minLevelUnit))] || fullLeveledFormatter.second, useUTC, this.getSetting('locale'));\n  };\n  TimeScale.prototype.getFormattedLabel = function (tick, idx, labelFormatter) {\n    var isUTC = this.getSetting('useUTC');\n    var lang = this.getSetting('locale');\n    return leveledFormat(tick, idx, labelFormatter, lang, isUTC);\n  };\n  /**\r\n   * @override\r\n   */\n  TimeScale.prototype.getTicks = function () {\n    var interval = this._interval;\n    var extent = this._extent;\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    ticks.push({\n      value: extent[0],\n      level: 0\n    });\n    var useUTC = this.getSetting('useUTC');\n    var innerTicks = getIntervalTicks(this._minLevelUnit, this._approxInterval, useUTC, extent);\n    ticks = ticks.concat(innerTicks);\n    ticks.push({\n      value: extent[1],\n      level: 0\n    });\n    return ticks;\n  };\n  TimeScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent;\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      // Expand extent\n      extent[0] -= ONE_DAY;\n      extent[1] += ONE_DAY;\n    }\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (extent[1] === -Infinity && extent[0] === Infinity) {\n      var d = new Date();\n      extent[1] = +new Date(d.getFullYear(), d.getMonth(), d.getDate());\n      extent[0] = extent[1] - ONE_DAY;\n    }\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n  };\n  TimeScale.prototype.calcNiceTicks = function (approxTickNum, minInterval, maxInterval) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    this._approxInterval = span / approxTickNum;\n    if (minInterval != null && this._approxInterval < minInterval) {\n      this._approxInterval = minInterval;\n    }\n    if (maxInterval != null && this._approxInterval > maxInterval) {\n      this._approxInterval = maxInterval;\n    }\n    var scaleIntervalsLen = scaleIntervals.length;\n    var idx = Math.min(bisect(scaleIntervals, this._approxInterval, 0, scaleIntervalsLen), scaleIntervalsLen - 1);\n    // Interval that can be used to calculate ticks\n    this._interval = scaleIntervals[idx][1];\n    // Min level used when picking ticks from top down.\n    // We check one more level to avoid the ticks are to sparse in some case.\n    this._minLevelUnit = scaleIntervals[Math.max(idx - 1, 0)][0];\n  };\n  TimeScale.prototype.parse = function (val) {\n    // val might be float.\n    return isNumber(val) ? val : +numberUtil.parseDate(val);\n  };\n  TimeScale.prototype.contain = function (val) {\n    return scaleHelper.contain(this.parse(val), this._extent);\n  };\n  TimeScale.prototype.normalize = function (val) {\n    return scaleHelper.normalize(this.parse(val), this._extent);\n  };\n  TimeScale.prototype.scale = function (val) {\n    return scaleHelper.scale(val, this._extent);\n  };\n  TimeScale.type = 'time';\n  return TimeScale;\n}(IntervalScale);\n/**\r\n * This implementation was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/time/scale.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nvar scaleIntervals = [\n// Format                           interval\n['second', ONE_SECOND], ['minute', ONE_MINUTE], ['hour', ONE_HOUR], ['quarter-day', ONE_HOUR * 6], ['half-day', ONE_HOUR * 12], ['day', ONE_DAY * 1.2], ['half-week', ONE_DAY * 3.5], ['week', ONE_DAY * 7], ['month', ONE_DAY * 31], ['quarter', ONE_DAY * 95], ['half-year', ONE_YEAR / 2], ['year', ONE_YEAR] // 1Y\n];\nfunction isUnitValueSame(unit, valueA, valueB, isUTC) {\n  var dateA = numberUtil.parseDate(valueA);\n  var dateB = numberUtil.parseDate(valueB);\n  var isSame = function (unit) {\n    return getUnitValue(dateA, unit, isUTC) === getUnitValue(dateB, unit, isUTC);\n  };\n  var isSameYear = function () {\n    return isSame('year');\n  };\n  // const isSameHalfYear = () => isSameYear() && isSame('half-year');\n  // const isSameQuater = () => isSameYear() && isSame('quarter');\n  var isSameMonth = function () {\n    return isSameYear() && isSame('month');\n  };\n  var isSameDay = function () {\n    return isSameMonth() && isSame('day');\n  };\n  // const isSameHalfDay = () => isSameDay() && isSame('half-day');\n  var isSameHour = function () {\n    return isSameDay() && isSame('hour');\n  };\n  var isSameMinute = function () {\n    return isSameHour() && isSame('minute');\n  };\n  var isSameSecond = function () {\n    return isSameMinute() && isSame('second');\n  };\n  var isSameMilliSecond = function () {\n    return isSameSecond() && isSame('millisecond');\n  };\n  switch (unit) {\n    case 'year':\n      return isSameYear();\n    case 'month':\n      return isSameMonth();\n    case 'day':\n      return isSameDay();\n    case 'hour':\n      return isSameHour();\n    case 'minute':\n      return isSameMinute();\n    case 'second':\n      return isSameSecond();\n    case 'millisecond':\n      return isSameMilliSecond();\n  }\n}\n// const primaryUnitGetters = {\n//     year: fullYearGetterName(),\n//     month: monthGetterName(),\n//     day: dateGetterName(),\n//     hour: hoursGetterName(),\n//     minute: minutesGetterName(),\n//     second: secondsGetterName(),\n//     millisecond: millisecondsGetterName()\n// };\n// const primaryUnitUTCGetters = {\n//     year: fullYearGetterName(true),\n//     month: monthGetterName(true),\n//     day: dateGetterName(true),\n//     hour: hoursGetterName(true),\n//     minute: minutesGetterName(true),\n//     second: secondsGetterName(true),\n//     millisecond: millisecondsGetterName(true)\n// };\n// function moveTick(date: Date, unitName: TimeUnit, step: number, isUTC: boolean) {\n//     step = step || 1;\n//     switch (getPrimaryTimeUnit(unitName)) {\n//         case 'year':\n//             date[fullYearSetterName(isUTC)](date[fullYearGetterName(isUTC)]() + step);\n//             break;\n//         case 'month':\n//             date[monthSetterName(isUTC)](date[monthGetterName(isUTC)]() + step);\n//             break;\n//         case 'day':\n//             date[dateSetterName(isUTC)](date[dateGetterName(isUTC)]() + step);\n//             break;\n//         case 'hour':\n//             date[hoursSetterName(isUTC)](date[hoursGetterName(isUTC)]() + step);\n//             break;\n//         case 'minute':\n//             date[minutesSetterName(isUTC)](date[minutesGetterName(isUTC)]() + step);\n//             break;\n//         case 'second':\n//             date[secondsSetterName(isUTC)](date[secondsGetterName(isUTC)]() + step);\n//             break;\n//         case 'millisecond':\n//             date[millisecondsSetterName(isUTC)](date[millisecondsGetterName(isUTC)]() + step);\n//             break;\n//     }\n//     return date.getTime();\n// }\n// const DATE_INTERVALS = [[8, 7.5], [4, 3.5], [2, 1.5]];\n// const MONTH_INTERVALS = [[6, 5.5], [3, 2.5], [2, 1.5]];\n// const MINUTES_SECONDS_INTERVALS = [[30, 30], [20, 20], [15, 15], [10, 10], [5, 5], [2, 2]];\nfunction getDateInterval(approxInterval, daysInMonth) {\n  approxInterval /= ONE_DAY;\n  return approxInterval > 16 ? 16\n  // Math.floor(daysInMonth / 2) + 1  // In this case we only want one tick between two months.\n  : approxInterval > 7.5 ? 7 // TODO week 7 or day 8?\n  : approxInterval > 3.5 ? 4 : approxInterval > 1.5 ? 2 : 1;\n}\nfunction getMonthInterval(approxInterval) {\n  var APPROX_ONE_MONTH = 30 * ONE_DAY;\n  approxInterval /= APPROX_ONE_MONTH;\n  return approxInterval > 6 ? 6 : approxInterval > 3 ? 3 : approxInterval > 2 ? 2 : 1;\n}\nfunction getHourInterval(approxInterval) {\n  approxInterval /= ONE_HOUR;\n  return approxInterval > 12 ? 12 : approxInterval > 6 ? 6 : approxInterval > 3.5 ? 4 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMinutesAndSecondsInterval(approxInterval, isMinutes) {\n  approxInterval /= isMinutes ? ONE_MINUTE : ONE_SECOND;\n  return approxInterval > 30 ? 30 : approxInterval > 20 ? 20 : approxInterval > 15 ? 15 : approxInterval > 10 ? 10 : approxInterval > 5 ? 5 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMillisecondsInterval(approxInterval) {\n  return numberUtil.nice(approxInterval, true);\n}\nfunction getFirstTimestampOfUnit(date, unitName, isUTC) {\n  var outDate = new Date(date);\n  switch (getPrimaryTimeUnit(unitName)) {\n    case 'year':\n    case 'month':\n      outDate[monthSetterName(isUTC)](0);\n    case 'day':\n      outDate[dateSetterName(isUTC)](1);\n    case 'hour':\n      outDate[hoursSetterName(isUTC)](0);\n    case 'minute':\n      outDate[minutesSetterName(isUTC)](0);\n    case 'second':\n      outDate[secondsSetterName(isUTC)](0);\n      outDate[millisecondsSetterName(isUTC)](0);\n  }\n  return outDate.getTime();\n}\nfunction getIntervalTicks(bottomUnitName, approxInterval, isUTC, extent) {\n  var safeLimit = 10000;\n  var unitNames = timeUnits;\n  var iter = 0;\n  function addTicksInSpan(interval, minTimestamp, maxTimestamp, getMethodName, setMethodName, isDate, out) {\n    var date = new Date(minTimestamp);\n    var dateTime = minTimestamp;\n    var d = date[getMethodName]();\n    // if (isDate) {\n    //     d -= 1; // Starts with 0;   PENDING\n    // }\n    while (dateTime < maxTimestamp && dateTime <= extent[1]) {\n      out.push({\n        value: dateTime\n      });\n      d += interval;\n      date[setMethodName](d);\n      dateTime = date.getTime();\n    }\n    // This extra tick is for calcuating ticks of next level. Will not been added to the final result\n    out.push({\n      value: dateTime,\n      notAdd: true\n    });\n  }\n  function addLevelTicks(unitName, lastLevelTicks, levelTicks) {\n    var newAddedTicks = [];\n    var isFirstLevel = !lastLevelTicks.length;\n    if (isUnitValueSame(getPrimaryTimeUnit(unitName), extent[0], extent[1], isUTC)) {\n      return;\n    }\n    if (isFirstLevel) {\n      lastLevelTicks = [{\n        // TODO Optimize. Not include so may ticks.\n        value: getFirstTimestampOfUnit(new Date(extent[0]), unitName, isUTC)\n      }, {\n        value: extent[1]\n      }];\n    }\n    for (var i = 0; i < lastLevelTicks.length - 1; i++) {\n      var startTick = lastLevelTicks[i].value;\n      var endTick = lastLevelTicks[i + 1].value;\n      if (startTick === endTick) {\n        continue;\n      }\n      var interval = void 0;\n      var getterName = void 0;\n      var setterName = void 0;\n      var isDate = false;\n      switch (unitName) {\n        case 'year':\n          interval = Math.max(1, Math.round(approxInterval / ONE_DAY / 365));\n          getterName = fullYearGetterName(isUTC);\n          setterName = fullYearSetterName(isUTC);\n          break;\n        case 'half-year':\n        case 'quarter':\n        case 'month':\n          interval = getMonthInterval(approxInterval);\n          getterName = monthGetterName(isUTC);\n          setterName = monthSetterName(isUTC);\n          break;\n        case 'week': // PENDING If week is added. Ignore day.\n        case 'half-week':\n        case 'day':\n          interval = getDateInterval(approxInterval, 31); // Use 32 days and let interval been 16\n          getterName = dateGetterName(isUTC);\n          setterName = dateSetterName(isUTC);\n          isDate = true;\n          break;\n        case 'half-day':\n        case 'quarter-day':\n        case 'hour':\n          interval = getHourInterval(approxInterval);\n          getterName = hoursGetterName(isUTC);\n          setterName = hoursSetterName(isUTC);\n          break;\n        case 'minute':\n          interval = getMinutesAndSecondsInterval(approxInterval, true);\n          getterName = minutesGetterName(isUTC);\n          setterName = minutesSetterName(isUTC);\n          break;\n        case 'second':\n          interval = getMinutesAndSecondsInterval(approxInterval, false);\n          getterName = secondsGetterName(isUTC);\n          setterName = secondsSetterName(isUTC);\n          break;\n        case 'millisecond':\n          interval = getMillisecondsInterval(approxInterval);\n          getterName = millisecondsGetterName(isUTC);\n          setterName = millisecondsSetterName(isUTC);\n          break;\n      }\n      addTicksInSpan(interval, startTick, endTick, getterName, setterName, isDate, newAddedTicks);\n      if (unitName === 'year' && levelTicks.length > 1 && i === 0) {\n        // Add nearest years to the left extent.\n        levelTicks.unshift({\n          value: levelTicks[0].value - interval\n        });\n      }\n    }\n    for (var i = 0; i < newAddedTicks.length; i++) {\n      levelTicks.push(newAddedTicks[i]);\n    }\n    // newAddedTicks.length && console.log(unitName, newAddedTicks);\n    return newAddedTicks;\n  }\n  var levelsTicks = [];\n  var currentLevelTicks = [];\n  var tickCount = 0;\n  var lastLevelTickCount = 0;\n  for (var i = 0; i < unitNames.length && iter++ < safeLimit; ++i) {\n    var primaryTimeUnit = getPrimaryTimeUnit(unitNames[i]);\n    if (!isPrimaryTimeUnit(unitNames[i])) {\n      // TODO\n      continue;\n    }\n    addLevelTicks(unitNames[i], levelsTicks[levelsTicks.length - 1] || [], currentLevelTicks);\n    var nextPrimaryTimeUnit = unitNames[i + 1] ? getPrimaryTimeUnit(unitNames[i + 1]) : null;\n    if (primaryTimeUnit !== nextPrimaryTimeUnit) {\n      if (currentLevelTicks.length) {\n        lastLevelTickCount = tickCount;\n        // Remove the duplicate so the tick count can be precisely.\n        currentLevelTicks.sort(function (a, b) {\n          return a.value - b.value;\n        });\n        var levelTicksRemoveDuplicated = [];\n        for (var i_1 = 0; i_1 < currentLevelTicks.length; ++i_1) {\n          var tickValue = currentLevelTicks[i_1].value;\n          if (i_1 === 0 || currentLevelTicks[i_1 - 1].value !== tickValue) {\n            levelTicksRemoveDuplicated.push(currentLevelTicks[i_1]);\n            if (tickValue >= extent[0] && tickValue <= extent[1]) {\n              tickCount++;\n            }\n          }\n        }\n        var targetTickNum = (extent[1] - extent[0]) / approxInterval;\n        // Added too much in this level and not too less in last level\n        if (tickCount > targetTickNum * 1.5 && lastLevelTickCount > targetTickNum / 1.5) {\n          break;\n        }\n        // Only treat primary time unit as one level.\n        levelsTicks.push(levelTicksRemoveDuplicated);\n        if (tickCount > targetTickNum || bottomUnitName === unitNames[i]) {\n          break;\n        }\n      }\n      // Reset if next unitName is primary\n      currentLevelTicks = [];\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (iter >= safeLimit) {\n      warn('Exceed safe limit.');\n    }\n  }\n  var levelsTicksInExtent = filter(map(levelsTicks, function (levelTicks) {\n    return filter(levelTicks, function (tick) {\n      return tick.value >= extent[0] && tick.value <= extent[1] && !tick.notAdd;\n    });\n  }), function (levelTicks) {\n    return levelTicks.length > 0;\n  });\n  var ticks = [];\n  var maxLevel = levelsTicksInExtent.length - 1;\n  for (var i = 0; i < levelsTicksInExtent.length; ++i) {\n    var levelTicks = levelsTicksInExtent[i];\n    for (var k = 0; k < levelTicks.length; ++k) {\n      ticks.push({\n        value: levelTicks[k].value,\n        level: maxLevel - i\n      });\n    }\n  }\n  ticks.sort(function (a, b) {\n    return a.value - b.value;\n  });\n  // Remove duplicates\n  var result = [];\n  for (var i = 0; i < ticks.length; ++i) {\n    if (i === 0 || ticks[i].value !== ticks[i - 1].value) {\n      result.push(ticks[i]);\n    }\n  }\n  return result;\n}\nScale.registerClass(TimeScale);\nexport default TimeScale;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Scale from './Scale.js';\nimport * as numberUtil from '../util/number.js';\nimport * as scaleHelper from './helper.js';\n// Use some method of IntervalScale\nimport IntervalScale from './Interval.js';\nvar scaleProto = Scale.prototype;\n// FIXME:TS refactor: not good to call it directly with `this`?\nvar intervalScaleProto = IntervalScale.prototype;\nvar roundingErrorFix = numberUtil.round;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar mathPow = Math.pow;\nvar mathLog = Math.log;\nvar LogScale = /** @class */function (_super) {\n  __extends(LogScale, _super);\n  function LogScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'log';\n    _this.base = 10;\n    _this._originalScale = new IntervalScale();\n    // FIXME:TS actually used by `IntervalScale`\n    _this._interval = 0;\n    return _this;\n  }\n  /**\r\n   * @param Whether expand the ticks to niced extent.\r\n   */\n  LogScale.prototype.getTicks = function (expandToNicedExtent) {\n    var originalScale = this._originalScale;\n    var extent = this._extent;\n    var originalExtent = originalScale.getExtent();\n    var ticks = intervalScaleProto.getTicks.call(this, expandToNicedExtent);\n    return zrUtil.map(ticks, function (tick) {\n      var val = tick.value;\n      var powVal = numberUtil.round(mathPow(this.base, val));\n      // Fix #4158\n      powVal = val === extent[0] && this._fixMin ? fixRoundingError(powVal, originalExtent[0]) : powVal;\n      powVal = val === extent[1] && this._fixMax ? fixRoundingError(powVal, originalExtent[1]) : powVal;\n      return {\n        value: powVal\n      };\n    }, this);\n  };\n  LogScale.prototype.setExtent = function (start, end) {\n    var base = mathLog(this.base);\n    // log(-Infinity) is NaN, so safe guard here\n    start = mathLog(Math.max(0, start)) / base;\n    end = mathLog(Math.max(0, end)) / base;\n    intervalScaleProto.setExtent.call(this, start, end);\n  };\n  /**\r\n   * @return {number} end\r\n   */\n  LogScale.prototype.getExtent = function () {\n    var base = this.base;\n    var extent = scaleProto.getExtent.call(this);\n    extent[0] = mathPow(base, extent[0]);\n    extent[1] = mathPow(base, extent[1]);\n    // Fix #4158\n    var originalScale = this._originalScale;\n    var originalExtent = originalScale.getExtent();\n    this._fixMin && (extent[0] = fixRoundingError(extent[0], originalExtent[0]));\n    this._fixMax && (extent[1] = fixRoundingError(extent[1], originalExtent[1]));\n    return extent;\n  };\n  LogScale.prototype.unionExtent = function (extent) {\n    this._originalScale.unionExtent(extent);\n    var base = this.base;\n    extent[0] = mathLog(extent[0]) / mathLog(base);\n    extent[1] = mathLog(extent[1]) / mathLog(base);\n    scaleProto.unionExtent.call(this, extent);\n  };\n  LogScale.prototype.unionExtentFromData = function (data, dim) {\n    // TODO\n    // filter value that <= 0\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * Update interval and extent of intervals for nice ticks\r\n   * @param approxTickNum default 10 Given approx tick number\r\n   */\n  LogScale.prototype.calcNiceTicks = function (approxTickNum) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (span === Infinity || span <= 0) {\n      return;\n    }\n    var interval = numberUtil.quantity(span);\n    var err = approxTickNum / span * interval;\n    // Filter ticks to get closer to the desired count.\n    if (err <= 0.5) {\n      interval *= 10;\n    }\n    // Interval should be integer\n    while (!isNaN(interval) && Math.abs(interval) < 1 && Math.abs(interval) > 0) {\n      interval *= 10;\n    }\n    var niceExtent = [numberUtil.round(mathCeil(extent[0] / interval) * interval), numberUtil.round(mathFloor(extent[1] / interval) * interval)];\n    this._interval = interval;\n    this._niceExtent = niceExtent;\n  };\n  LogScale.prototype.calcNiceExtent = function (opt) {\n    intervalScaleProto.calcNiceExtent.call(this, opt);\n    this._fixMin = opt.fixMin;\n    this._fixMax = opt.fixMax;\n  };\n  LogScale.prototype.parse = function (val) {\n    return val;\n  };\n  LogScale.prototype.contain = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.contain(val, this._extent);\n  };\n  LogScale.prototype.normalize = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.normalize(val, this._extent);\n  };\n  LogScale.prototype.scale = function (val) {\n    val = scaleHelper.scale(val, this._extent);\n    return mathPow(this.base, val);\n  };\n  LogScale.type = 'log';\n  return LogScale;\n}(Scale);\nvar proto = LogScale.prototype;\nproto.getMinorTicks = intervalScaleProto.getMinorTicks;\nproto.getLabel = intervalScaleProto.getLabel;\nfunction fixRoundingError(val, originalVal) {\n  return roundingErrorFix(val, numberUtil.getPrecision(originalVal));\n}\nScale.registerClass(LogScale);\nexport default LogScale;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, isArray, eqNaN, isFunction } from 'zrender/lib/core/util.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nvar ScaleRawExtentInfo = /** @class */function () {\n  function ScaleRawExtentInfo(scale, model,\n  // Usually: data extent from all series on this axis.\n  originalExtent) {\n    this._prepareParams(scale, model, originalExtent);\n  }\n  /**\r\n   * Parameters depending on outside (like model, user callback)\r\n   * are prepared and fixed here.\r\n   */\n  ScaleRawExtentInfo.prototype._prepareParams = function (scale, model,\n  // Usually: data extent from all series on this axis.\n  dataExtent) {\n    if (dataExtent[1] < dataExtent[0]) {\n      dataExtent = [NaN, NaN];\n    }\n    this._dataMin = dataExtent[0];\n    this._dataMax = dataExtent[1];\n    var isOrdinal = this._isOrdinal = scale.type === 'ordinal';\n    this._needCrossZero = scale.type === 'interval' && model.getNeedCrossZero && model.getNeedCrossZero();\n    var axisMinValue = model.get('min', true);\n    if (axisMinValue == null) {\n      axisMinValue = model.get('startValue', true);\n    }\n    var modelMinRaw = this._modelMinRaw = axisMinValue;\n    if (isFunction(modelMinRaw)) {\n      // This callback always provides users the full data extent (before data is filtered).\n      this._modelMinNum = parseAxisModelMinMax(scale, modelMinRaw({\n        min: dataExtent[0],\n        max: dataExtent[1]\n      }));\n    } else if (modelMinRaw !== 'dataMin') {\n      this._modelMinNum = parseAxisModelMinMax(scale, modelMinRaw);\n    }\n    var modelMaxRaw = this._modelMaxRaw = model.get('max', true);\n    if (isFunction(modelMaxRaw)) {\n      // This callback always provides users the full data extent (before data is filtered).\n      this._modelMaxNum = parseAxisModelMinMax(scale, modelMaxRaw({\n        min: dataExtent[0],\n        max: dataExtent[1]\n      }));\n    } else if (modelMaxRaw !== 'dataMax') {\n      this._modelMaxNum = parseAxisModelMinMax(scale, modelMaxRaw);\n    }\n    if (isOrdinal) {\n      // FIXME: there is a flaw here: if there is no \"block\" data processor like `dataZoom`,\n      // and progressive rendering is using, here the category result might just only contain\n      // the processed chunk rather than the entire result.\n      this._axisDataLen = model.getCategories().length;\n    } else {\n      var boundaryGap = model.get('boundaryGap');\n      var boundaryGapArr = isArray(boundaryGap) ? boundaryGap : [boundaryGap || 0, boundaryGap || 0];\n      if (typeof boundaryGapArr[0] === 'boolean' || typeof boundaryGapArr[1] === 'boolean') {\n        if (process.env.NODE_ENV !== 'production') {\n          console.warn('Boolean type for boundaryGap is only ' + 'allowed for ordinal axis. Please use string in ' + 'percentage instead, e.g., \"20%\". Currently, ' + 'boundaryGap is set to be 0.');\n        }\n        this._boundaryGapInner = [0, 0];\n      } else {\n        this._boundaryGapInner = [parsePercent(boundaryGapArr[0], 1), parsePercent(boundaryGapArr[1], 1)];\n      }\n    }\n  };\n  /**\r\n   * Calculate extent by prepared parameters.\r\n   * This method has no external dependency and can be called duplicatedly,\r\n   * getting the same result.\r\n   * If parameters changed, should call this method to recalcuate.\r\n   */\n  ScaleRawExtentInfo.prototype.calculate = function () {\n    // Notice: When min/max is not set (that is, when there are null/undefined,\n    // which is the most common case), these cases should be ensured:\n    // (1) For 'ordinal', show all axis.data.\n    // (2) For others:\n    //      + `boundaryGap` is applied (if min/max set, boundaryGap is\n    //      disabled).\n    //      + If `needCrossZero`, min/max should be zero, otherwise, min/max should\n    //      be the result that originalExtent enlarged by boundaryGap.\n    // (3) If no data, it should be ensured that `scale.setBlank` is set.\n    var isOrdinal = this._isOrdinal;\n    var dataMin = this._dataMin;\n    var dataMax = this._dataMax;\n    var axisDataLen = this._axisDataLen;\n    var boundaryGapInner = this._boundaryGapInner;\n    var span = !isOrdinal ? dataMax - dataMin || Math.abs(dataMin) : null;\n    // Currently if a `'value'` axis model min is specified as 'dataMin'/'dataMax',\n    // `boundaryGap` will not be used. It's the different from specifying as `null`/`undefined`.\n    var min = this._modelMinRaw === 'dataMin' ? dataMin : this._modelMinNum;\n    var max = this._modelMaxRaw === 'dataMax' ? dataMax : this._modelMaxNum;\n    // If `_modelMinNum`/`_modelMaxNum` is `null`/`undefined`, should not be fixed.\n    var minFixed = min != null;\n    var maxFixed = max != null;\n    if (min == null) {\n      min = isOrdinal ? axisDataLen ? 0 : NaN : dataMin - boundaryGapInner[0] * span;\n    }\n    if (max == null) {\n      max = isOrdinal ? axisDataLen ? axisDataLen - 1 : NaN : dataMax + boundaryGapInner[1] * span;\n    }\n    (min == null || !isFinite(min)) && (min = NaN);\n    (max == null || !isFinite(max)) && (max = NaN);\n    var isBlank = eqNaN(min) || eqNaN(max) || isOrdinal && !axisDataLen;\n    // If data extent modified, need to recalculated to ensure cross zero.\n    if (this._needCrossZero) {\n      // Axis is over zero and min is not set\n      if (min > 0 && max > 0 && !minFixed) {\n        min = 0;\n        // minFixed = true;\n      }\n      // Axis is under zero and max is not set\n      if (min < 0 && max < 0 && !maxFixed) {\n        max = 0;\n        // maxFixed = true;\n      }\n      // PENDING:\n      // When `needCrossZero` and all data is positive/negative, should it be ensured\n      // that the results processed by boundaryGap are positive/negative?\n      // If so, here `minFixed`/`maxFixed` need to be set.\n    }\n    var determinedMin = this._determinedMin;\n    var determinedMax = this._determinedMax;\n    if (determinedMin != null) {\n      min = determinedMin;\n      minFixed = true;\n    }\n    if (determinedMax != null) {\n      max = determinedMax;\n      maxFixed = true;\n    }\n    // Ensure min/max be finite number or NaN here. (not to be null/undefined)\n    // `NaN` means min/max axis is blank.\n    return {\n      min: min,\n      max: max,\n      minFixed: minFixed,\n      maxFixed: maxFixed,\n      isBlank: isBlank\n    };\n  };\n  ScaleRawExtentInfo.prototype.modifyDataMinMax = function (minMaxName, val) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this.frozen);\n    }\n    this[DATA_MIN_MAX_ATTR[minMaxName]] = val;\n  };\n  ScaleRawExtentInfo.prototype.setDeterminedMinMax = function (minMaxName, val) {\n    var attr = DETERMINED_MIN_MAX_ATTR[minMaxName];\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this.frozen\n      // Earse them usually means logic flaw.\n      && this[attr] == null);\n    }\n    this[attr] = val;\n  };\n  ScaleRawExtentInfo.prototype.freeze = function () {\n    // @ts-ignore\n    this.frozen = true;\n  };\n  return ScaleRawExtentInfo;\n}();\nexport { ScaleRawExtentInfo };\nvar DETERMINED_MIN_MAX_ATTR = {\n  min: '_determinedMin',\n  max: '_determinedMax'\n};\nvar DATA_MIN_MAX_ATTR = {\n  min: '_dataMin',\n  max: '_dataMax'\n};\n/**\r\n * Get scale min max and related info only depends on model settings.\r\n * This method can be called after coordinate system created.\r\n * For example, in data processing stage.\r\n *\r\n * Scale extent info probably be required multiple times during a workflow.\r\n * For example:\r\n * (1) `dataZoom` depends it to get the axis extent in \"100%\" state.\r\n * (2) `processor/extentCalculator` depends it to make sure whether axis extent is specified.\r\n * (3) `coordSys.update` use it to finally decide the scale extent.\r\n * But the callback of `min`/`max` should not be called multiple times.\r\n * The code below should not be implemented repeatedly either.\r\n * So we cache the result in the scale instance, which will be recreated at the beginning\r\n * of the workflow (because `scale` instance will be recreated each round of the workflow).\r\n */\nexport function ensureScaleRawExtentInfo(scale, model,\n// Usually: data extent from all series on this axis.\noriginalExtent) {\n  // Do not permit to recreate.\n  var rawExtentInfo = scale.rawExtentInfo;\n  if (rawExtentInfo) {\n    return rawExtentInfo;\n  }\n  rawExtentInfo = new ScaleRawExtentInfo(scale, model, originalExtent);\n  // @ts-ignore\n  scale.rawExtentInfo = rawExtentInfo;\n  return rawExtentInfo;\n}\nexport function parseAxisModelMinMax(scale, minMax) {\n  return minMax == null ? null : eqNaN(minMax) ? NaN : scale.parse(minMax);\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport OrdinalScale from '../scale/Ordinal.js';\nimport IntervalScale from '../scale/Interval.js';\nimport Scale from '../scale/Scale.js';\nimport { prepareLayoutBarSeries, makeColumnLayout, retrieveColumnLayout } from '../layout/barGrid.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport TimeScale from '../scale/Time.js';\nimport LogScale from '../scale/Log.js';\nimport { getStackedDimension } from '../data/helper/dataStackHelper.js';\nimport { ensureScaleRawExtentInfo } from './scaleRawExtentInfo.js';\n/**\r\n * Get axis scale extent before niced.\r\n * Item of returned array can only be number (including Infinity and NaN).\r\n *\r\n * Caution:\r\n * Precondition of calling this method:\r\n * The scale extent has been initialized using series data extent via\r\n * `scale.setExtent` or `scale.unionExtentFromData`;\r\n */\nexport function getScaleExtent(scale, model) {\n  var scaleType = scale.type;\n  var rawExtentResult = ensureScaleRawExtentInfo(scale, model, scale.getExtent()).calculate();\n  scale.setBlank(rawExtentResult.isBlank);\n  var min = rawExtentResult.min;\n  var max = rawExtentResult.max;\n  // If bars are placed on a base axis of type time or interval account for axis boundary overflow and current axis\n  // is base axis\n  // FIXME\n  // (1) Consider support value axis, where below zero and axis `onZero` should be handled properly.\n  // (2) Refactor the logic with `barGrid`. Is it not need to `makeBarWidthAndOffsetInfo` twice with different extent?\n  //     Should not depend on series type `bar`?\n  // (3) Fix that might overlap when using dataZoom.\n  // (4) Consider other chart types using `barGrid`?\n  // See #6728, #4862, `test/bar-overflow-time-plot.html`\n  var ecModel = model.ecModel;\n  if (ecModel && scaleType === 'time' /* || scaleType === 'interval' */) {\n    var barSeriesModels = prepareLayoutBarSeries('bar', ecModel);\n    var isBaseAxisAndHasBarSeries_1 = false;\n    zrUtil.each(barSeriesModels, function (seriesModel) {\n      isBaseAxisAndHasBarSeries_1 = isBaseAxisAndHasBarSeries_1 || seriesModel.getBaseAxis() === model.axis;\n    });\n    if (isBaseAxisAndHasBarSeries_1) {\n      // Calculate placement of bars on axis. TODO should be decoupled\n      // with barLayout\n      var barWidthAndOffset = makeColumnLayout(barSeriesModels);\n      // Adjust axis min and max to account for overflow\n      var adjustedScale = adjustScaleForOverflow(min, max, model, barWidthAndOffset);\n      min = adjustedScale.min;\n      max = adjustedScale.max;\n    }\n  }\n  return {\n    extent: [min, max],\n    // \"fix\" means \"fixed\", the value should not be\n    // changed in the subsequent steps.\n    fixMin: rawExtentResult.minFixed,\n    fixMax: rawExtentResult.maxFixed\n  };\n}\nfunction adjustScaleForOverflow(min, max, model,\n// Only support cartesian coord yet.\nbarWidthAndOffset) {\n  // Get Axis Length\n  var axisExtent = model.axis.getExtent();\n  var axisLength = Math.abs(axisExtent[1] - axisExtent[0]);\n  // Get bars on current base axis and calculate min and max overflow\n  var barsOnCurrentAxis = retrieveColumnLayout(barWidthAndOffset, model.axis);\n  if (barsOnCurrentAxis === undefined) {\n    return {\n      min: min,\n      max: max\n    };\n  }\n  var minOverflow = Infinity;\n  zrUtil.each(barsOnCurrentAxis, function (item) {\n    minOverflow = Math.min(item.offset, minOverflow);\n  });\n  var maxOverflow = -Infinity;\n  zrUtil.each(barsOnCurrentAxis, function (item) {\n    maxOverflow = Math.max(item.offset + item.width, maxOverflow);\n  });\n  minOverflow = Math.abs(minOverflow);\n  maxOverflow = Math.abs(maxOverflow);\n  var totalOverFlow = minOverflow + maxOverflow;\n  // Calculate required buffer based on old range and overflow\n  var oldRange = max - min;\n  var oldRangePercentOfNew = 1 - (minOverflow + maxOverflow) / axisLength;\n  var overflowBuffer = oldRange / oldRangePercentOfNew - oldRange;\n  max += overflowBuffer * (maxOverflow / totalOverFlow);\n  min -= overflowBuffer * (minOverflow / totalOverFlow);\n  return {\n    min: min,\n    max: max\n  };\n}\n// Precondition of calling this method:\n// The scale extent has been initialized using series data extent via\n// `scale.setExtent` or `scale.unionExtentFromData`;\nexport function niceScaleExtent(scale, inModel) {\n  var model = inModel;\n  var extentInfo = getScaleExtent(scale, model);\n  var extent = extentInfo.extent;\n  var splitNumber = model.get('splitNumber');\n  if (scale instanceof LogScale) {\n    scale.base = model.get('logBase');\n  }\n  var scaleType = scale.type;\n  var interval = model.get('interval');\n  var isIntervalOrTime = scaleType === 'interval' || scaleType === 'time';\n  scale.setExtent(extent[0], extent[1]);\n  scale.calcNiceExtent({\n    splitNumber: splitNumber,\n    fixMin: extentInfo.fixMin,\n    fixMax: extentInfo.fixMax,\n    minInterval: isIntervalOrTime ? model.get('minInterval') : null,\n    maxInterval: isIntervalOrTime ? model.get('maxInterval') : null\n  });\n  // If some one specified the min, max. And the default calculated interval\n  // is not good enough. He can specify the interval. It is often appeared\n  // in angle axis with angle 0 - 360. Interval calculated in interval scale is hard\n  // to be 60.\n  // FIXME\n  if (interval != null) {\n    scale.setInterval && scale.setInterval(interval);\n  }\n}\n/**\r\n * @param axisType Default retrieve from model.type\r\n */\nexport function createScaleByModel(model, axisType) {\n  axisType = axisType || model.get('type');\n  if (axisType) {\n    switch (axisType) {\n      // Buildin scale\n      case 'category':\n        return new OrdinalScale({\n          ordinalMeta: model.getOrdinalMeta ? model.getOrdinalMeta() : model.getCategories(),\n          extent: [Infinity, -Infinity]\n        });\n      case 'time':\n        return new TimeScale({\n          locale: model.ecModel.getLocaleModel(),\n          useUTC: model.ecModel.get('useUTC')\n        });\n      default:\n        // case 'value'/'interval', 'log', or others.\n        return new (Scale.getClass(axisType) || IntervalScale)();\n    }\n  }\n}\n/**\r\n * Check if the axis cross 0\r\n */\nexport function ifAxisCrossZero(axis) {\n  var dataExtent = axis.scale.getExtent();\n  var min = dataExtent[0];\n  var max = dataExtent[1];\n  return !(min > 0 && max > 0 || min < 0 && max < 0);\n}\n/**\r\n * @param axis\r\n * @return Label formatter function.\r\n *         param: {number} tickValue,\r\n *         param: {number} idx, the index in all ticks.\r\n *                         If category axis, this param is not required.\r\n *         return: {string} label string.\r\n */\nexport function makeLabelFormatter(axis) {\n  var labelFormatter = axis.getLabelModel().get('formatter');\n  var categoryTickStart = axis.type === 'category' ? axis.scale.getExtent()[0] : null;\n  if (axis.scale.type === 'time') {\n    return function (tpl) {\n      return function (tick, idx) {\n        return axis.scale.getFormattedLabel(tick, idx, tpl);\n      };\n    }(labelFormatter);\n  } else if (zrUtil.isString(labelFormatter)) {\n    return function (tpl) {\n      return function (tick) {\n        // For category axis, get raw value; for numeric axis,\n        // get formatted label like '1,333,444'.\n        var label = axis.scale.getLabel(tick);\n        var text = tpl.replace('{value}', label != null ? label : '');\n        return text;\n      };\n    }(labelFormatter);\n  } else if (zrUtil.isFunction(labelFormatter)) {\n    return function (cb) {\n      return function (tick, idx) {\n        // The original intention of `idx` is \"the index of the tick in all ticks\".\n        // But the previous implementation of category axis do not consider the\n        // `axisLabel.interval`, which cause that, for example, the `interval` is\n        // `1`, then the ticks \"name5\", \"name7\", \"name9\" are displayed, where the\n        // corresponding `idx` are `0`, `2`, `4`, but not `0`, `1`, `2`. So we keep\n        // the definition here for back compatibility.\n        if (categoryTickStart != null) {\n          idx = tick.value - categoryTickStart;\n        }\n        return cb(getAxisRawValue(axis, tick), idx, tick.level != null ? {\n          level: tick.level\n        } : null);\n      };\n    }(labelFormatter);\n  } else {\n    return function (tick) {\n      return axis.scale.getLabel(tick);\n    };\n  }\n}\nexport function getAxisRawValue(axis, tick) {\n  // In category axis with data zoom, tick is not the original\n  // index of axis.data. So tick should not be exposed to user\n  // in category axis.\n  return axis.type === 'category' ? axis.scale.getLabel(tick) : tick.value;\n}\n/**\r\n * @param axis\r\n * @return Be null/undefined if no labels.\r\n */\nexport function estimateLabelUnionRect(axis) {\n  var axisModel = axis.model;\n  var scale = axis.scale;\n  if (!axisModel.get(['axisLabel', 'show']) || scale.isBlank()) {\n    return;\n  }\n  var realNumberScaleTicks;\n  var tickCount;\n  var categoryScaleExtent = scale.getExtent();\n  // Optimize for large category data, avoid call `getTicks()`.\n  if (scale instanceof OrdinalScale) {\n    tickCount = scale.count();\n  } else {\n    realNumberScaleTicks = scale.getTicks();\n    tickCount = realNumberScaleTicks.length;\n  }\n  var axisLabelModel = axis.getLabelModel();\n  var labelFormatter = makeLabelFormatter(axis);\n  var rect;\n  var step = 1;\n  // Simple optimization for large amount of labels\n  if (tickCount > 40) {\n    step = Math.ceil(tickCount / 40);\n  }\n  for (var i = 0; i < tickCount; i += step) {\n    var tick = realNumberScaleTicks ? realNumberScaleTicks[i] : {\n      value: categoryScaleExtent[0] + i\n    };\n    var label = labelFormatter(tick, i);\n    var unrotatedSingleRect = axisLabelModel.getTextRect(label);\n    var singleRect = rotateTextRect(unrotatedSingleRect, axisLabelModel.get('rotate') || 0);\n    rect ? rect.union(singleRect) : rect = singleRect;\n  }\n  return rect;\n}\nfunction rotateTextRect(textRect, rotate) {\n  var rotateRadians = rotate * Math.PI / 180;\n  var beforeWidth = textRect.width;\n  var beforeHeight = textRect.height;\n  var afterWidth = beforeWidth * Math.abs(Math.cos(rotateRadians)) + Math.abs(beforeHeight * Math.sin(rotateRadians));\n  var afterHeight = beforeWidth * Math.abs(Math.sin(rotateRadians)) + Math.abs(beforeHeight * Math.cos(rotateRadians));\n  var rotatedRect = new BoundingRect(textRect.x, textRect.y, afterWidth, afterHeight);\n  return rotatedRect;\n}\n/**\r\n * @param model axisLabelModel or axisTickModel\r\n * @return {number|String} Can be null|'auto'|number|function\r\n */\nexport function getOptionCategoryInterval(model) {\n  var interval = model.get('interval');\n  return interval == null ? 'auto' : interval;\n}\n/**\r\n * Set `categoryInterval` as 0 implicitly indicates that\r\n * show all labels regardless of overlap.\r\n * @param {Object} axis axisModel.axis\r\n */\nexport function shouldShowAllLabels(axis) {\n  return axis.type === 'category' && getOptionCategoryInterval(axis.getLabelModel()) === 0;\n}\nexport function getDataDimensionsOnAxis(data, axisDim) {\n  // Remove duplicated dat dimensions caused by `getStackedDimension`.\n  var dataDimMap = {};\n  // Currently `mapDimensionsAll` will contain stack result dimension ('__\\0ecstackresult').\n  // PENDING: is it reasonable? Do we need to remove the original dim from \"coord dim\" since\n  // there has been stacked result dim?\n  zrUtil.each(data.mapDimensionsAll(axisDim), function (dataDim) {\n    // For example, the extent of the original dimension\n    // is [0.1, 0.5], the extent of the `stackResultDimension`\n    // is [7, 9], the final extent should NOT include [0.1, 0.5],\n    // because there is no graphic corresponding to [0.1, 0.5].\n    // See the case in `test/area-stack.html` `main1`, where area line\n    // stack needs `yAxis` not start from 0.\n    dataDimMap[getStackedDimension(data, dataDim)] = true;\n  });\n  return zrUtil.keys(dataDimMap);\n}\nexport function unionAxisExtentFromData(dataExtent, data, axisDim) {\n  if (data) {\n    zrUtil.each(getDataDimensionsOnAxis(data, axisDim), function (dim) {\n      var seriesExtent = data.getApproximateExtent(dim);\n      seriesExtent[0] < dataExtent[0] && (dataExtent[0] = seriesExtent[0]);\n      seriesExtent[1] > dataExtent[1] && (dataExtent[1] = seriesExtent[1]);\n    });\n  }\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../util/model.js';\nimport { makeLabelFormatter, getOptionCategoryInterval, shouldShowAllLabels } from './axisHelper.js';\nvar inner = makeInner();\nfunction tickValuesToNumbers(axis, values) {\n  var nums = zrUtil.map(values, function (val) {\n    return axis.scale.parse(val);\n  });\n  if (axis.type === 'time' && nums.length > 0) {\n    // Time axis needs duplicate first/last tick (see TimeScale.getTicks())\n    // The first and last tick/label don't get drawn\n    nums.sort();\n    nums.unshift(nums[0]);\n    nums.push(nums[nums.length - 1]);\n  }\n  return nums;\n}\nexport function createAxisLabels(axis) {\n  var custom = axis.getLabelModel().get('customValues');\n  if (custom) {\n    var labelFormatter_1 = makeLabelFormatter(axis);\n    var extent_1 = axis.scale.getExtent();\n    var tickNumbers = tickValuesToNumbers(axis, custom);\n    var ticks = zrUtil.filter(tickNumbers, function (val) {\n      return val >= extent_1[0] && val <= extent_1[1];\n    });\n    return {\n      labels: zrUtil.map(ticks, function (numval) {\n        var tick = {\n          value: numval\n        };\n        return {\n          formattedLabel: labelFormatter_1(tick),\n          rawLabel: axis.scale.getLabel(tick),\n          tickValue: numval\n        };\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryLabels(axis) : makeRealNumberLabels(axis);\n}\n/**\r\n * @param {module:echats/coord/Axis} axis\r\n * @param {module:echarts/model/Model} tickModel For example, can be axisTick, splitLine, splitArea.\r\n * @return {Object} {\r\n *     ticks: Array.<number>\r\n *     tickCategoryInterval: number\r\n * }\r\n */\nexport function createAxisTicks(axis, tickModel) {\n  var custom = axis.getTickModel().get('customValues');\n  if (custom) {\n    var extent_2 = axis.scale.getExtent();\n    var tickNumbers = tickValuesToNumbers(axis, custom);\n    return {\n      ticks: zrUtil.filter(tickNumbers, function (val) {\n        return val >= extent_2[0] && val <= extent_2[1];\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryTicks(axis, tickModel) : {\n    ticks: zrUtil.map(axis.scale.getTicks(), function (tick) {\n      return tick.value;\n    })\n  };\n}\nfunction makeCategoryLabels(axis) {\n  var labelModel = axis.getLabelModel();\n  var result = makeCategoryLabelsActually(axis, labelModel);\n  return !labelModel.get('show') || axis.scale.isBlank() ? {\n    labels: [],\n    labelCategoryInterval: result.labelCategoryInterval\n  } : result;\n}\nfunction makeCategoryLabelsActually(axis, labelModel) {\n  var labelsCache = getListCache(axis, 'labels');\n  var optionLabelInterval = getOptionCategoryInterval(labelModel);\n  var result = listCacheGet(labelsCache, optionLabelInterval);\n  if (result) {\n    return result;\n  }\n  var labels;\n  var numericLabelInterval;\n  if (zrUtil.isFunction(optionLabelInterval)) {\n    labels = makeLabelsByCustomizedCategoryInterval(axis, optionLabelInterval);\n  } else {\n    numericLabelInterval = optionLabelInterval === 'auto' ? makeAutoCategoryInterval(axis) : optionLabelInterval;\n    labels = makeLabelsByNumericCategoryInterval(axis, numericLabelInterval);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(labelsCache, optionLabelInterval, {\n    labels: labels,\n    labelCategoryInterval: numericLabelInterval\n  });\n}\nfunction makeCategoryTicks(axis, tickModel) {\n  var ticksCache = getListCache(axis, 'ticks');\n  var optionTickInterval = getOptionCategoryInterval(tickModel);\n  var result = listCacheGet(ticksCache, optionTickInterval);\n  if (result) {\n    return result;\n  }\n  var ticks;\n  var tickCategoryInterval;\n  // Optimize for the case that large category data and no label displayed,\n  // we should not return all ticks.\n  if (!tickModel.get('show') || axis.scale.isBlank()) {\n    ticks = [];\n  }\n  if (zrUtil.isFunction(optionTickInterval)) {\n    ticks = makeLabelsByCustomizedCategoryInterval(axis, optionTickInterval, true);\n  }\n  // Always use label interval by default despite label show. Consider this\n  // scenario, Use multiple grid with the xAxis sync, and only one xAxis shows\n  // labels. `splitLine` and `axisTick` should be consistent in this case.\n  else if (optionTickInterval === 'auto') {\n    var labelsResult = makeCategoryLabelsActually(axis, axis.getLabelModel());\n    tickCategoryInterval = labelsResult.labelCategoryInterval;\n    ticks = zrUtil.map(labelsResult.labels, function (labelItem) {\n      return labelItem.tickValue;\n    });\n  } else {\n    tickCategoryInterval = optionTickInterval;\n    ticks = makeLabelsByNumericCategoryInterval(axis, tickCategoryInterval, true);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(ticksCache, optionTickInterval, {\n    ticks: ticks,\n    tickCategoryInterval: tickCategoryInterval\n  });\n}\nfunction makeRealNumberLabels(axis) {\n  var ticks = axis.scale.getTicks();\n  var labelFormatter = makeLabelFormatter(axis);\n  return {\n    labels: zrUtil.map(ticks, function (tick, idx) {\n      return {\n        level: tick.level,\n        formattedLabel: labelFormatter(tick, idx),\n        rawLabel: axis.scale.getLabel(tick),\n        tickValue: tick.value\n      };\n    })\n  };\n}\nfunction getListCache(axis, prop) {\n  // Because key can be a function, and cache size always is small, we use array cache.\n  return inner(axis)[prop] || (inner(axis)[prop] = []);\n}\nfunction listCacheGet(cache, key) {\n  for (var i = 0; i < cache.length; i++) {\n    if (cache[i].key === key) {\n      return cache[i].value;\n    }\n  }\n}\nfunction listCacheSet(cache, key, value) {\n  cache.push({\n    key: key,\n    value: value\n  });\n  return value;\n}\nfunction makeAutoCategoryInterval(axis) {\n  var result = inner(axis).autoInterval;\n  return result != null ? result : inner(axis).autoInterval = axis.calculateCategoryInterval();\n}\n/**\r\n * Calculate interval for category axis ticks and labels.\r\n * To get precise result, at least one of `getRotate` and `isHorizontal`\r\n * should be implemented in axis.\r\n */\nexport function calculateCategoryInterval(axis) {\n  var params = fetchAutoCategoryIntervalCalculationParams(axis);\n  var labelFormatter = makeLabelFormatter(axis);\n  var rotation = (params.axisRotate - params.labelRotate) / 180 * Math.PI;\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  // Providing this method is for optimization:\n  // avoid generating a long array by `getTicks`\n  // in large category data case.\n  var tickCount = ordinalScale.count();\n  if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n    return 0;\n  }\n  var step = 1;\n  // Simple optimization. Empirical value: tick count should less than 40.\n  if (tickCount > 40) {\n    step = Math.max(1, Math.floor(tickCount / 40));\n  }\n  var tickValue = ordinalExtent[0];\n  var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n  var unitW = Math.abs(unitSpan * Math.cos(rotation));\n  var unitH = Math.abs(unitSpan * Math.sin(rotation));\n  var maxW = 0;\n  var maxH = 0;\n  // Caution: Performance sensitive for large category data.\n  // Consider dataZoom, we should make appropriate step to avoid O(n) loop.\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    var width = 0;\n    var height = 0;\n    // Not precise, do not consider align and vertical align\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(labelFormatter({\n      value: tickValue\n    }), params.font, 'center', 'top');\n    // Magic number\n    width = rect.width * 1.3;\n    height = rect.height * 1.3;\n    // Min size, void long loop.\n    maxW = Math.max(maxW, width, 7);\n    maxH = Math.max(maxH, height, 7);\n  }\n  var dw = maxW / unitW;\n  var dh = maxH / unitH;\n  // 0/0 is NaN, 1/0 is Infinity.\n  isNaN(dw) && (dw = Infinity);\n  isNaN(dh) && (dh = Infinity);\n  var interval = Math.max(0, Math.floor(Math.min(dw, dh)));\n  var cache = inner(axis.model);\n  var axisExtent = axis.getExtent();\n  var lastAutoInterval = cache.lastAutoInterval;\n  var lastTickCount = cache.lastTickCount;\n  // Use cache to keep interval stable while moving zoom window,\n  // otherwise the calculated interval might jitter when the zoom\n  // window size is close to the interval-changing size.\n  // For example, if all of the axis labels are `a, b, c, d, e, f, g`.\n  // The jitter will cause that sometimes the displayed labels are\n  // `a, d, g` (interval: 2) sometimes `a, c, e`(interval: 1).\n  if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n  // Always choose the bigger one, otherwise the critical\n  // point is not the same when zooming in or zooming out.\n  && lastAutoInterval > interval\n  // If the axis change is caused by chart resize, the cache should not\n  // be used. Otherwise some hidden labels might not be shown again.\n  && cache.axisExtent0 === axisExtent[0] && cache.axisExtent1 === axisExtent[1]) {\n    interval = lastAutoInterval;\n  }\n  // Only update cache if cache not used, otherwise the\n  // changing of interval is too insensitive.\n  else {\n    cache.lastTickCount = tickCount;\n    cache.lastAutoInterval = interval;\n    cache.axisExtent0 = axisExtent[0];\n    cache.axisExtent1 = axisExtent[1];\n  }\n  return interval;\n}\nfunction fetchAutoCategoryIntervalCalculationParams(axis) {\n  var labelModel = axis.getLabelModel();\n  return {\n    axisRotate: axis.getRotate ? axis.getRotate() : axis.isHorizontal && !axis.isHorizontal() ? 90 : 0,\n    labelRotate: labelModel.get('rotate') || 0,\n    font: labelModel.getFont()\n  };\n}\nfunction makeLabelsByNumericCategoryInterval(axis, categoryInterval, onlyTick) {\n  var labelFormatter = makeLabelFormatter(axis);\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  var labelModel = axis.getLabelModel();\n  var result = [];\n  // TODO: axisType: ordinalTime, pick the tick from each month/day/year/...\n  var step = Math.max((categoryInterval || 0) + 1, 1);\n  var startTick = ordinalExtent[0];\n  var tickCount = ordinalScale.count();\n  // Calculate start tick based on zero if possible to keep label consistent\n  // while zooming and moving while interval > 0. Otherwise the selection\n  // of displayable ticks and symbols probably keep changing.\n  // 3 is empirical value.\n  if (startTick !== 0 && step > 1 && tickCount / step > 2) {\n    startTick = Math.round(Math.ceil(startTick / step) * step);\n  }\n  // (1) Only add min max label here but leave overlap checking\n  // to render stage, which also ensure the returned list\n  // suitable for splitLine and splitArea rendering.\n  // (2) Scales except category always contain min max label so\n  // do not need to perform this process.\n  var showAllLabel = shouldShowAllLabels(axis);\n  var includeMinLabel = labelModel.get('showMinLabel') || showAllLabel;\n  var includeMaxLabel = labelModel.get('showMaxLabel') || showAllLabel;\n  if (includeMinLabel && startTick !== ordinalExtent[0]) {\n    addItem(ordinalExtent[0]);\n  }\n  // Optimize: avoid generating large array by `ordinalScale.getTicks()`.\n  var tickValue = startTick;\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    addItem(tickValue);\n  }\n  if (includeMaxLabel && tickValue - step !== ordinalExtent[1]) {\n    addItem(ordinalExtent[1]);\n  }\n  function addItem(tickValue) {\n    var tickObj = {\n      value: tickValue\n    };\n    result.push(onlyTick ? tickValue : {\n      formattedLabel: labelFormatter(tickObj),\n      rawLabel: ordinalScale.getLabel(tickObj),\n      tickValue: tickValue\n    });\n  }\n  return result;\n}\nfunction makeLabelsByCustomizedCategoryInterval(axis, categoryInterval, onlyTick) {\n  var ordinalScale = axis.scale;\n  var labelFormatter = makeLabelFormatter(axis);\n  var result = [];\n  zrUtil.each(ordinalScale.getTicks(), function (tick) {\n    var rawLabel = ordinalScale.getLabel(tick);\n    var tickValue = tick.value;\n    if (categoryInterval(tick.value, rawLabel)) {\n      result.push(onlyTick ? tickValue : {\n        formattedLabel: labelFormatter(tick),\n        rawLabel: rawLabel,\n        tickValue: tickValue\n      });\n    }\n  });\n  return result;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, map } from 'zrender/lib/core/util.js';\nimport { linearMap, getPixelPrecision, round } from '../util/number.js';\nimport { createAxisTicks, createAxisLabels, calculateCategoryInterval } from './axisTickLabelBuilder.js';\nvar NORMALIZED_EXTENT = [0, 1];\n/**\r\n * Base class of Axis.\r\n */\nvar Axis = /** @class */function () {\n  function Axis(dim, scale, extent) {\n    this.onBand = false;\n    this.inverse = false;\n    this.dim = dim;\n    this.scale = scale;\n    this._extent = extent || [0, 0];\n  }\n  /**\r\n   * If axis extent contain given coord\r\n   */\n  Axis.prototype.contain = function (coord) {\n    var extent = this._extent;\n    var min = Math.min(extent[0], extent[1]);\n    var max = Math.max(extent[0], extent[1]);\n    return coord >= min && coord <= max;\n  };\n  /**\r\n   * If axis extent contain given data\r\n   */\n  Axis.prototype.containData = function (data) {\n    return this.scale.contain(data);\n  };\n  /**\r\n   * Get coord extent.\r\n   */\n  Axis.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  /**\r\n   * Get precision used for formatting\r\n   */\n  Axis.prototype.getPixelPrecision = function (dataExtent) {\n    return getPixelPrecision(dataExtent || this.scale.getExtent(), this._extent);\n  };\n  /**\r\n   * Set coord extent\r\n   */\n  Axis.prototype.setExtent = function (start, end) {\n    var extent = this._extent;\n    extent[0] = start;\n    extent[1] = end;\n  };\n  /**\r\n   * Convert data to coord. Data is the rank if it has an ordinal scale\r\n   */\n  Axis.prototype.dataToCoord = function (data, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    data = scale.normalize(data);\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    return linearMap(data, NORMALIZED_EXTENT, extent, clamp);\n  };\n  /**\r\n   * Convert coord to data. Data is the rank if it has an ordinal scale\r\n   */\n  Axis.prototype.coordToData = function (coord, clamp) {\n    var extent = this._extent;\n    var scale = this.scale;\n    if (this.onBand && scale.type === 'ordinal') {\n      extent = extent.slice();\n      fixExtentWithBands(extent, scale.count());\n    }\n    var t = linearMap(coord, extent, NORMALIZED_EXTENT, clamp);\n    return this.scale.scale(t);\n  };\n  /**\r\n   * Convert pixel point to data in axis\r\n   */\n  Axis.prototype.pointToData = function (point, clamp) {\n    // Should be implemented in derived class if necessary.\n    return;\n  };\n  /**\r\n   * Different from `zrUtil.map(axis.getTicks(), axis.dataToCoord, axis)`,\r\n   * `axis.getTicksCoords` considers `onBand`, which is used by\r\n   * `boundaryGap:true` of category axis and splitLine and splitArea.\r\n   * @param opt.tickModel default: axis.model.getModel('axisTick')\r\n   * @param opt.clamp If `true`, the first and the last\r\n   *        tick must be at the axis end points. Otherwise, clip ticks\r\n   *        that outside the axis extent.\r\n   */\n  Axis.prototype.getTicksCoords = function (opt) {\n    opt = opt || {};\n    var tickModel = opt.tickModel || this.getTickModel();\n    var result = createAxisTicks(this, tickModel);\n    var ticks = result.ticks;\n    var ticksCoords = map(ticks, function (tickVal) {\n      return {\n        coord: this.dataToCoord(this.scale.type === 'ordinal' ? this.scale.getRawOrdinalNumber(tickVal) : tickVal),\n        tickValue: tickVal\n      };\n    }, this);\n    var alignWithLabel = tickModel.get('alignWithLabel');\n    fixOnBandTicksCoords(this, ticksCoords, alignWithLabel, opt.clamp);\n    return ticksCoords;\n  };\n  Axis.prototype.getMinorTicksCoords = function () {\n    if (this.scale.type === 'ordinal') {\n      // Category axis doesn't support minor ticks\n      return [];\n    }\n    var minorTickModel = this.model.getModel('minorTick');\n    var splitNumber = minorTickModel.get('splitNumber');\n    // Protection.\n    if (!(splitNumber > 0 && splitNumber < 100)) {\n      splitNumber = 5;\n    }\n    var minorTicks = this.scale.getMinorTicks(splitNumber);\n    var minorTicksCoords = map(minorTicks, function (minorTicksGroup) {\n      return map(minorTicksGroup, function (minorTick) {\n        return {\n          coord: this.dataToCoord(minorTick),\n          tickValue: minorTick\n        };\n      }, this);\n    }, this);\n    return minorTicksCoords;\n  };\n  Axis.prototype.getViewLabels = function () {\n    return createAxisLabels(this).labels;\n  };\n  Axis.prototype.getLabelModel = function () {\n    return this.model.getModel('axisLabel');\n  };\n  /**\r\n   * Notice here we only get the default tick model. For splitLine\r\n   * or splitArea, we should pass the splitLineModel or splitAreaModel\r\n   * manually when calling `getTicksCoords`.\r\n   * In GL, this method may be overridden to:\r\n   * `axisModel.getModel('axisTick', grid3DModel.getModel('axisTick'));`\r\n   */\n  Axis.prototype.getTickModel = function () {\n    return this.model.getModel('axisTick');\n  };\n  /**\r\n   * Get width of band\r\n   */\n  Axis.prototype.getBandWidth = function () {\n    var axisExtent = this._extent;\n    var dataExtent = this.scale.getExtent();\n    var len = dataExtent[1] - dataExtent[0] + (this.onBand ? 1 : 0);\n    // Fix #2728, avoid NaN when only one data.\n    len === 0 && (len = 1);\n    var size = Math.abs(axisExtent[1] - axisExtent[0]);\n    return Math.abs(size) / len;\n  };\n  /**\r\n   * Only be called in category axis.\r\n   * Can be overridden, consider other axes like in 3D.\r\n   * @return Auto interval for cateogry axis tick and label\r\n   */\n  Axis.prototype.calculateCategoryInterval = function () {\n    return calculateCategoryInterval(this);\n  };\n  return Axis;\n}();\nfunction fixExtentWithBands(extent, nTick) {\n  var size = extent[1] - extent[0];\n  var len = nTick;\n  var margin = size / len / 2;\n  extent[0] += margin;\n  extent[1] -= margin;\n}\n// If axis has labels [1, 2, 3, 4]. Bands on the axis are\n// |---1---|---2---|---3---|---4---|.\n// So the displayed ticks and splitLine/splitArea should between\n// each data item, otherwise cause misleading (e.g., split tow bars\n// of a single data item when there are two bar series).\n// Also consider if tickCategoryInterval > 0 and onBand, ticks and\n// splitLine/spliteArea should layout appropriately corresponding\n// to displayed labels. (So we should not use `getBandWidth` in this\n// case).\nfunction fixOnBandTicksCoords(axis, ticksCoords, alignWithLabel, clamp) {\n  var ticksLen = ticksCoords.length;\n  if (!axis.onBand || alignWithLabel || !ticksLen) {\n    return;\n  }\n  var axisExtent = axis.getExtent();\n  var last;\n  var diffSize;\n  if (ticksLen === 1) {\n    ticksCoords[0].coord = axisExtent[0];\n    last = ticksCoords[1] = {\n      coord: axisExtent[1],\n      tickValue: ticksCoords[0].tickValue\n    };\n  } else {\n    var crossLen = ticksCoords[ticksLen - 1].tickValue - ticksCoords[0].tickValue;\n    var shift_1 = (ticksCoords[ticksLen - 1].coord - ticksCoords[0].coord) / crossLen;\n    each(ticksCoords, function (ticksItem) {\n      ticksItem.coord -= shift_1 / 2;\n    });\n    var dataExtent = axis.scale.getExtent();\n    diffSize = 1 + dataExtent[1] - ticksCoords[ticksLen - 1].tickValue;\n    last = {\n      coord: ticksCoords[ticksLen - 1].coord + shift_1 * diffSize,\n      tickValue: dataExtent[1] + 1\n    };\n    ticksCoords.push(last);\n  }\n  var inverse = axisExtent[0] > axisExtent[1];\n  // Handling clamp.\n  if (littleThan(ticksCoords[0].coord, axisExtent[0])) {\n    clamp ? ticksCoords[0].coord = axisExtent[0] : ticksCoords.shift();\n  }\n  if (clamp && littleThan(axisExtent[0], ticksCoords[0].coord)) {\n    ticksCoords.unshift({\n      coord: axisExtent[0]\n    });\n  }\n  if (littleThan(axisExtent[1], last.coord)) {\n    clamp ? last.coord = axisExtent[1] : ticksCoords.pop();\n  }\n  if (clamp && littleThan(last.coord, axisExtent[1])) {\n    ticksCoords.push({\n      coord: axisExtent[1]\n    });\n  }\n  function littleThan(a, b) {\n    // Avoid rounding error cause calculated tick coord different with extent.\n    // It may cause an extra unnecessary tick added.\n    a = round(a);\n    b = round(b);\n    return inverse ? a > b : a < b;\n  }\n}\nexport default Axis;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nvar AxisModelCommonMixin = /** @class */function () {\n  function AxisModelCommonMixin() {}\n  AxisModelCommonMixin.prototype.getNeedCrossZero = function () {\n    var option = this.option;\n    return !option.scale;\n  };\n  /**\r\n   * Should be implemented by each axis model if necessary.\r\n   * @return coordinate system model\r\n   */\n  AxisModelCommonMixin.prototype.getCoordSysModel = function () {\n    return;\n  };\n  return AxisModelCommonMixin;\n}();\nexport { AxisModelCommonMixin };", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { BoundingRect, OrientedBoundingRect } from '../util/graphic.js';\nexport function prepareLayoutList(input) {\n  var list = [];\n  for (var i = 0; i < input.length; i++) {\n    var rawItem = input[i];\n    if (rawItem.defaultAttr.ignore) {\n      continue;\n    }\n    var label = rawItem.label;\n    var transform = label.getComputedTransform();\n    // NOTE: Get bounding rect after getComputedTransform, or label may not been updated by the host el.\n    var localRect = label.getBoundingRect();\n    var isAxisAligned = !transform || transform[1] < 1e-5 && transform[2] < 1e-5;\n    var minMargin = label.style.margin || 0;\n    var globalRect = localRect.clone();\n    globalRect.applyTransform(transform);\n    globalRect.x -= minMargin / 2;\n    globalRect.y -= minMargin / 2;\n    globalRect.width += minMargin;\n    globalRect.height += minMargin;\n    var obb = isAxisAligned ? new OrientedBoundingRect(localRect, transform) : null;\n    list.push({\n      label: label,\n      labelLine: rawItem.labelLine,\n      rect: globalRect,\n      localRect: localRect,\n      obb: obb,\n      priority: rawItem.priority,\n      defaultAttr: rawItem.defaultAttr,\n      layoutOption: rawItem.computedLayoutOption,\n      axisAligned: isAxisAligned,\n      transform: transform\n    });\n  }\n  return list;\n}\nfunction shiftLayout(list, xyDim, sizeDim, minBound, maxBound, balanceShift) {\n  var len = list.length;\n  if (len < 2) {\n    return;\n  }\n  list.sort(function (a, b) {\n    return a.rect[xyDim] - b.rect[xyDim];\n  });\n  var lastPos = 0;\n  var delta;\n  var adjusted = false;\n  var shifts = [];\n  var totalShifts = 0;\n  for (var i = 0; i < len; i++) {\n    var item = list[i];\n    var rect = item.rect;\n    delta = rect[xyDim] - lastPos;\n    if (delta < 0) {\n      // shiftForward(i, len, -delta);\n      rect[xyDim] -= delta;\n      item.label[xyDim] -= delta;\n      adjusted = true;\n    }\n    var shift = Math.max(-delta, 0);\n    shifts.push(shift);\n    totalShifts += shift;\n    lastPos = rect[xyDim] + rect[sizeDim];\n  }\n  if (totalShifts > 0 && balanceShift) {\n    // Shift back to make the distribution more equally.\n    shiftList(-totalShifts / len, 0, len);\n  }\n  // TODO bleedMargin?\n  var first = list[0];\n  var last = list[len - 1];\n  var minGap;\n  var maxGap;\n  updateMinMaxGap();\n  // If ends exceed two bounds, squeeze at most 80%, then take the gap of two bounds.\n  minGap < 0 && squeezeGaps(-minGap, 0.8);\n  maxGap < 0 && squeezeGaps(maxGap, 0.8);\n  updateMinMaxGap();\n  takeBoundsGap(minGap, maxGap, 1);\n  takeBoundsGap(maxGap, minGap, -1);\n  // Handle bailout when there is not enough space.\n  updateMinMaxGap();\n  if (minGap < 0) {\n    squeezeWhenBailout(-minGap);\n  }\n  if (maxGap < 0) {\n    squeezeWhenBailout(maxGap);\n  }\n  function updateMinMaxGap() {\n    minGap = first.rect[xyDim] - minBound;\n    maxGap = maxBound - last.rect[xyDim] - last.rect[sizeDim];\n  }\n  function takeBoundsGap(gapThisBound, gapOtherBound, moveDir) {\n    if (gapThisBound < 0) {\n      // Move from other gap if can.\n      var moveFromMaxGap = Math.min(gapOtherBound, -gapThisBound);\n      if (moveFromMaxGap > 0) {\n        shiftList(moveFromMaxGap * moveDir, 0, len);\n        var remained = moveFromMaxGap + gapThisBound;\n        if (remained < 0) {\n          squeezeGaps(-remained * moveDir, 1);\n        }\n      } else {\n        squeezeGaps(-gapThisBound * moveDir, 1);\n      }\n    }\n  }\n  function shiftList(delta, start, end) {\n    if (delta !== 0) {\n      adjusted = true;\n    }\n    for (var i = start; i < end; i++) {\n      var item = list[i];\n      var rect = item.rect;\n      rect[xyDim] += delta;\n      item.label[xyDim] += delta;\n    }\n  }\n  // Squeeze gaps if the labels exceed margin.\n  function squeezeGaps(delta, maxSqeezePercent) {\n    var gaps = [];\n    var totalGaps = 0;\n    for (var i = 1; i < len; i++) {\n      var prevItemRect = list[i - 1].rect;\n      var gap = Math.max(list[i].rect[xyDim] - prevItemRect[xyDim] - prevItemRect[sizeDim], 0);\n      gaps.push(gap);\n      totalGaps += gap;\n    }\n    if (!totalGaps) {\n      return;\n    }\n    var squeezePercent = Math.min(Math.abs(delta) / totalGaps, maxSqeezePercent);\n    if (delta > 0) {\n      for (var i = 0; i < len - 1; i++) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i] * squeezePercent;\n        // Forward\n        shiftList(movement, 0, i + 1);\n      }\n    } else {\n      // Backward\n      for (var i = len - 1; i > 0; i--) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i - 1] * squeezePercent;\n        shiftList(-movement, i, len);\n      }\n    }\n  }\n  /**\r\n   * Squeeze to allow overlap if there is no more space available.\r\n   * Let other overlapping strategy like hideOverlap do the job instead of keep exceeding the bounds.\r\n   */\n  function squeezeWhenBailout(delta) {\n    var dir = delta < 0 ? -1 : 1;\n    delta = Math.abs(delta);\n    var moveForEachLabel = Math.ceil(delta / (len - 1));\n    for (var i = 0; i < len - 1; i++) {\n      if (dir > 0) {\n        // Forward\n        shiftList(moveForEachLabel, 0, i + 1);\n      } else {\n        // Backward\n        shiftList(-moveForEachLabel, len - i - 1, len);\n      }\n      delta -= moveForEachLabel;\n      if (delta <= 0) {\n        return;\n      }\n    }\n  }\n  return adjusted;\n}\n/**\r\n * Adjust labels on x direction to avoid overlap.\r\n */\nexport function shiftLayoutOnX(list, leftBound, rightBound,\n// If average the shifts on all labels and add them to 0\n// TODO: Not sure if should enable it.\n// Pros: The angle of lines will distribute more equally\n// Cons: In some layout. It may not what user wanted. like in pie. the label of last sector is usually changed unexpectedly.\nbalanceShift) {\n  return shiftLayout(list, 'x', 'width', leftBound, rightBound, balanceShift);\n}\n/**\r\n * Adjust labels on y direction to avoid overlap.\r\n */\nexport function shiftLayoutOnY(list, topBound, bottomBound,\n// If average the shifts on all labels and add them to 0\nbalanceShift) {\n  return shiftLayout(list, 'y', 'height', topBound, bottomBound, balanceShift);\n}\nexport function hideOverlap(labelList) {\n  var displayedLabels = [];\n  // TODO, render overflow visible first, put in the displayedLabels.\n  labelList.sort(function (a, b) {\n    return b.priority - a.priority;\n  });\n  var globalRect = new BoundingRect(0, 0, 0, 0);\n  function hideEl(el) {\n    if (!el.ignore) {\n      // Show on emphasis.\n      var emphasisState = el.ensureState('emphasis');\n      if (emphasisState.ignore == null) {\n        emphasisState.ignore = false;\n      }\n    }\n    el.ignore = true;\n  }\n  for (var i = 0; i < labelList.length; i++) {\n    var labelItem = labelList[i];\n    var isAxisAligned = labelItem.axisAligned;\n    var localRect = labelItem.localRect;\n    var transform = labelItem.transform;\n    var label = labelItem.label;\n    var labelLine = labelItem.labelLine;\n    globalRect.copy(labelItem.rect);\n    // Add a threshold because layout may be aligned precisely.\n    globalRect.width -= 0.1;\n    globalRect.height -= 0.1;\n    globalRect.x += 0.05;\n    globalRect.y += 0.05;\n    var obb = labelItem.obb;\n    var overlapped = false;\n    for (var j = 0; j < displayedLabels.length; j++) {\n      var existsTextCfg = displayedLabels[j];\n      // Fast rejection.\n      if (!globalRect.intersect(existsTextCfg.rect)) {\n        continue;\n      }\n      if (isAxisAligned && existsTextCfg.axisAligned) {\n        // Is overlapped\n        overlapped = true;\n        break;\n      }\n      if (!existsTextCfg.obb) {\n        // If self is not axis aligned. But other is.\n        existsTextCfg.obb = new OrientedBoundingRect(existsTextCfg.localRect, existsTextCfg.transform);\n      }\n      if (!obb) {\n        // If self is axis aligned. But other is not.\n        obb = new OrientedBoundingRect(localRect, transform);\n      }\n      if (obb.intersect(existsTextCfg.obb)) {\n        overlapped = true;\n        break;\n      }\n    }\n    // TODO Callback to determine if this overlap should be handled?\n    if (overlapped) {\n      hideEl(label);\n      labelLine && hideEl(labelLine);\n    } else {\n      label.attr('ignore', labelItem.defaultAttr.ignore);\n      labelLine && labelLine.attr('ignore', labelItem.defaultAttr.labelGuideIgnore);\n      displayedLabels.push(labelItem);\n    }\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA,SAAS,wBAAwB,4BAA4B;AAC3D,SAAO,8BAA8B,OAAO,IAAI,2BAA2B,UAAU;AACvF;AACA,SAAS,iBAAiB,MAAM;AAC9B,SAAO;AACT;AACA,IAAI;AAAA;AAAA,EAA0B,WAAY;AAIxC,aAASA,YAAW,QAAQ,QAAQ,cAAc,cAAc,SAEhE,UAAU;AACR,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,gBAAgB,gBAAgB;AACrC,WAAK,gBAAgB,gBAAgB;AAErC,WAAK,UAAU;AACf,WAAK,oBAAoB,aAAa;AAAA,IACxC;AAIA,IAAAA,YAAW,UAAU,MAAM,SAAU,MAAM;AACzC,WAAK,OAAO;AACZ,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,SAAS,SAAU,MAAM;AAC5C,WAAK,UAAU;AACf,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,MAAM;AACrD,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,MAAM;AACrD,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,MAAM;AACtD,WAAK,oBAAoB;AACzB,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,SAAS,SAAU,MAAM;AAC5C,WAAK,UAAU;AACf,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,UAAU,WAAY;AACzC,WAAK,KAAK,oBAAoB,qBAAqB,kBAAkB,EAAE;AAAA,IACzE;AACA,IAAAA,YAAW,UAAU,mBAAmB,WAAY;AAClD,UAAI,SAAS,KAAK;AAClB,UAAI,SAAS,KAAK;AAClB,UAAI,kBAAkB,CAAC;AACvB,UAAI,gBAAgB,IAAI,MAAM,OAAO,MAAM;AAC3C,UAAI,gBAAgB,IAAI,MAAM,OAAO,MAAM;AAC3C,WAAK,cAAc,QAAQ,MAAM,eAAe,eAAe;AAC/D,WAAK,cAAc,QAAQ,iBAAiB,eAAe,eAAe;AAC1E,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,SAAS,cAAc,CAAC;AAC5B,YAAI,eAAe,gBAAgB,MAAM;AACzC,YAAI,kBAAkB,wBAAwB,YAAY;AAE1D,YAAI,kBAAkB,GAAG;AAGvB,cAAI,SAAS,aAAa,MAAM;AAChC,cAAI,aAAa,WAAW,GAAG;AAC7B,4BAAgB,MAAM,IAAI,aAAa,CAAC;AAAA,UAC1C;AACA,eAAK,WAAW,KAAK,QAAQ,QAAQ,CAAC;AAAA,QACxC,WAAW,oBAAoB,GAAG;AAChC,0BAAgB,MAAM,IAAI;AAC1B,eAAK,WAAW,KAAK,QAAQ,cAAc,CAAC;AAAA,QAC9C,OAAO;AACL,eAAK,WAAW,KAAK,QAAQ,CAAC;AAAA,QAChC;AAAA,MACF;AACA,WAAK,gBAAgB,eAAe,eAAe;AAAA,IACrD;AA0BA,IAAAA,YAAW,UAAU,mBAAmB,WAAY;AAClD,UAAI,SAAS,KAAK;AAClB,UAAI,SAAS,KAAK;AAClB,UAAI,kBAAkB,CAAC;AACvB,UAAI,kBAAkB,CAAC;AACvB,UAAI,gBAAgB,CAAC;AACrB,UAAI,gBAAgB,CAAC;AACrB,WAAK,cAAc,QAAQ,iBAAiB,eAAe,eAAe;AAC1E,WAAK,cAAc,QAAQ,iBAAiB,eAAe,eAAe;AAC1E,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,YAAI,SAAS,cAAc,CAAC;AAC5B,YAAI,eAAe,gBAAgB,MAAM;AACzC,YAAI,eAAe,gBAAgB,MAAM;AACzC,YAAI,kBAAkB,wBAAwB,YAAY;AAC1D,YAAI,kBAAkB,wBAAwB,YAAY;AAC1D,YAAI,kBAAkB,KAAK,oBAAoB,GAAG;AAChD,eAAK,oBAAoB,KAAK,iBAAiB,cAAc,YAAY;AACzE,0BAAgB,MAAM,IAAI;AAAA,QAC5B,WAAW,oBAAoB,KAAK,kBAAkB,GAAG;AACvD,eAAK,oBAAoB,KAAK,iBAAiB,cAAc,YAAY;AACzE,0BAAgB,MAAM,IAAI;AAAA,QAC5B,WAAW,oBAAoB,KAAK,oBAAoB,GAAG;AACzD,eAAK,WAAW,KAAK,QAAQ,cAAc,YAAY;AACvD,0BAAgB,MAAM,IAAI;AAAA,QAC5B,WAAW,kBAAkB,KAAK,kBAAkB,GAAG;AACrD,eAAK,qBAAqB,KAAK,kBAAkB,cAAc,YAAY;AAC3E,0BAAgB,MAAM,IAAI;AAAA,QAC5B,WAAW,kBAAkB,GAAG;AAC9B,mBAAS,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC9C,iBAAK,WAAW,KAAK,QAAQ,aAAa,GAAG,CAAC;AAAA,UAChD;AAAA,QACF,OAAO;AACL,eAAK,WAAW,KAAK,QAAQ,YAAY;AAAA,QAC3C;AAAA,MACF;AACA,WAAK,gBAAgB,eAAe,eAAe;AAAA,IACrD;AACA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,eAAe,iBAAiB;AAC/E,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,YAAI,SAAS,cAAc,CAAC;AAC5B,YAAI,eAAe,gBAAgB,MAAM;AACzC,YAAI,eAAe,wBAAwB,YAAY;AACvD,YAAI,eAAe,GAAG;AACpB,mBAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,iBAAK,QAAQ,KAAK,KAAK,aAAa,CAAC,CAAC;AAAA,UACxC;AAAA,QACF,WAAW,iBAAiB,GAAG;AAC7B,eAAK,QAAQ,KAAK,KAAK,YAAY;AAAA,QACrC;AAEA,wBAAgB,MAAM,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAE/CC,MAIA,QAAQ,eAAe;AACrB,UAAI,iBAAiB,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAEnC,YAAI,MAAM,SAAS,KAAK,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC;AAChD,YAAI,CAAC,gBAAgB;AACnB,iBAAO,CAAC,IAAI;AAAA,QACd;AACA,YAAI,CAACA,MAAK;AACR;AAAA,QACF;AACA,YAAI,YAAYA,KAAI,GAAG;AACvB,YAAI,eAAe,wBAAwB,SAAS;AACpD,YAAI,iBAAiB,GAAG;AAGtB,UAAAA,KAAI,GAAG,IAAI;AACX,cAAI,gBAAgB;AAClB,mBAAO,KAAK,GAAG;AAAA,UACjB;AAAA,QACF,WAAW,iBAAiB,GAAG;AAC7B,UAAAA,KAAI,GAAG,IAAI,CAAC,WAAW,CAAC;AAAA,QAC1B,OAAO;AACL,oBAAU,KAAK,CAAC;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,WAAOD;AAAA,EACT,EAAE;AAAA;AACF,IAAO,qBAAQ;;;AC/Mf,IAAI;AAAA;AAAA,EAAkC,WAAY;AAChD,aAASE,oBAAmB,QAAQ,YAAY;AAC9C,WAAK,UAAU;AACf,WAAK,UAAU;AAAA,IACjB;AACA,IAAAA,oBAAmB,UAAU,MAAM,WAAY;AAC7C,aAAO;AAAA;AAAA,QAEL,gBAAgB,KAAK,uBAAuB;AAAA,QAC5C,QAAQ,KAAK;AAAA,MACf;AAAA,IACF;AAQA,IAAAA,oBAAmB,UAAU,yBAAyB,WAAY;AAChE,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAkB,KAAK,UAAU,KAAK,QAAQ,yBAAyB,IAAI,CAAC;AAAA,MACnF;AACA,aAAO,KAAK;AAAA,IACd;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEK,SAAS,oBAAoB,MAAM,QAAQ;AAChD,MAAI,UAAU,CAAC;AACf,MAAI,SAAS,QAAQ,SAAS,CAAC;AAC/B,MAAI,sBAAsB,cAAc;AACxC,MAAI,iBAAiB,CAAC;AACtB,MAAI,mBAAmB,CAAC;AACxB,MAAI,mBAAmB,CAAC;AACxB,OAAK,KAAK,YAAY,SAAU,SAAS;AACvC,QAAI,UAAU,KAAK,iBAAiB,OAAO;AAC3C,QAAI,WAAW,QAAQ;AACvB,QAAI,UAAU;AACZ,UAAI,MAAuC;AACzC,eAAO,kBAAkB,IAAI,QAAQ,KAAK,IAAI;AAAA,MAChD;AACA,UAAI,gBAAgB,QAAQ;AAC5B,2BAAqB,QAAQ,QAAQ,EAAE,aAAa,IAAI;AACxD,UAAI,CAAC,QAAQ,cAAc;AACzB,4BAAoB,IAAI,UAAU,CAAC;AAKnC,YAAI,gBAAgB,QAAQ,IAAI,GAAG;AACjC,yBAAe,CAAC,IAAI;AAAA,QACtB;AAGA,6BAAqB,kBAAkB,QAAQ,EAAE,aAAa,IAAI,KAAK,kBAAkB,QAAQ,IAAI;AAAA,MACvG;AACA,UAAI,QAAQ,gBAAgB;AAC1B,yBAAiB,KAAK,OAAO;AAAA,MAC/B;AAAA,IACF;AACA,sBAAkB,KAAK,SAAU,GAAG,UAAU;AAC5C,UAAI,YAAY,qBAAqB,QAAQ,QAAQ;AACrD,UAAI,WAAW,QAAQ,UAAU,QAAQ;AACzC,UAAI,YAAY,QAAQ,aAAa,OAAO;AAC1C,kBAAU,QAAQ,IAAI,QAAQ;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,kBAAkB,CAAC;AACvB,MAAI,yBAAyB,CAAC;AAC9B,sBAAoB,KAAK,SAAU,GAAG,UAAU;AAC9C,QAAI,SAAS,OAAO,QAAQ;AAC5B,2BAAuB,QAAQ,IAAI,OAAO,CAAC;AAG3C,sBAAkB,gBAAgB,OAAO,MAAM;AAAA,EACjD,CAAC;AACD,UAAQ,kBAAkB;AAC1B,UAAQ,wBAAwB,IAAI,iBAAiB,SAAU,SAAS;AACtE,WAAO,KAAK,iBAAiB,OAAO,EAAE;AAAA,EACxC,CAAC;AACD,UAAQ,yBAAyB;AACjC,MAAI,cAAc,OAAO;AAGzB,MAAI,eAAe,YAAY,QAAQ;AACrC,qBAAiB,YAAY,MAAM;AAAA,EACrC;AACA,MAAI,gBAAgB,OAAO;AAC3B,MAAI,iBAAiB,cAAc,QAAQ;AACzC,uBAAmB,cAAc,MAAM;AAAA,EACzC,WAAW,CAAC,iBAAiB,QAAQ;AACnC,uBAAmB,eAAe,MAAM;AAAA,EAC1C;AACA,SAAO,iBAAiB;AACxB,SAAO,mBAAmB;AAC1B,UAAQ,aAAa,IAAI,mBAAmB,kBAAkB,MAAM;AACpE,SAAO;AACT;AACA,SAAS,qBAAqB,QAAQ,KAAK;AACzC,MAAI,CAAC,OAAO,eAAe,GAAG,GAAG;AAC/B,WAAO,GAAG,IAAI,CAAC;AAAA,EACjB;AACA,SAAO,OAAO,GAAG;AACnB;AAEO,SAAS,uBAAuB,UAAU;AAC/C,SAAO,aAAa,aAAa,YAAY,aAAa,SAAS,SAAS;AAC9E;AACA,SAAS,gBAAgB,SAAS;AAGhC,SAAO,EAAE,YAAY,aAAa,YAAY;AAChD;;;ACnHA,IAAI;AAAA;AAAA,EAAqC,2BAAY;AAInD,aAASC,uBAAsB,KAAK;AAkClC,WAAK,YAAY,CAAC;AAClB,UAAI,OAAO,MAAM;AACf,QAAO,OAAO,MAAM,GAAG;AAAA,MACzB;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,IAAO,gCAAQ;;;AC5Cf,IAAI,QAAQ,UAAU;AACtB,IAAI,eAAe;AAAA,EACjB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,MAAM;AACR;AAYA,IAAI;AAAA;AAAA,EAAgC,WAAY;AAC9C,aAASC,kBAAiB,KAAK;AAC7B,WAAK,aAAa,IAAI;AACtB,WAAK,cAAc,IAAI;AACvB,WAAK,SAAS,IAAI;AAClB,WAAK,gBAAgB,IAAI;AACzB,WAAK,kBAAkB,IAAI,gBAAgB;AAAA,IAC7C;AACA,IAAAA,kBAAiB,UAAU,qBAAqB,WAAY;AAC1D,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,kBAAiB,UAAU,oBAAoB,SAAU,kBAAkB;AACzE,WAAK,cAAc;AACnB,UAAI,CAAC,kBAAkB;AACrB;AAAA,MACF;AACA,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,cAAc,uBAAuB,KAAK,MAAM;AAAA,MACvD;AAAA,IACF;AAQA,IAAAA,kBAAiB,UAAU,0BAA0B,SAAU,SAAS;AACtE,aAAO,UAAU,KAAK,YAAY,IAAI,OAAO,GAAG,EAAE;AAAA,IACpD;AAMA,IAAAA,kBAAiB,UAAU,qBAAqB,SAAU,UAAU;AAClE,UAAI,mBAAmB,KAAK,OAAO;AACnC,UAAI,kBAAkB;AACpB,eAAO,iBAAiB,QAAQ;AAAA,MAClC;AAAA,IACF;AACA,IAAAA,kBAAiB,UAAU,kBAAkB,WAAY;AACvD,UAAI,WAAW,KAAK;AACpB,UAAI,yBAAyB,yBAAyB,KAAK,MAAM;AACjE,UAAI,iBAAiB,CAAC,2BAA2B,QAAQ;AAGzD,UAAI,UAAU;AACd,UAAI,OAAO,CAAC;AACZ,eAAS,aAAa,GAAG,eAAe,GAAG,aAAa,UAAU,cAAc;AAC9E,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,cAAc;AAClB,YAAI,eAAe,KAAK,WAAW,YAAY;AAE/C,YAAI,gBAAgB,aAAa,kBAAkB,YAAY;AAC7D,qBAAW,yBAAyB,aAAa,OAAO;AACxD,iBAAO,aAAa;AACpB,wBAAc,aAAa;AAC3B;AAAA,QACF,OAAO;AACL,cAAI,eAAe,KAAK,mBAAmB,UAAU;AACrD,cAAI,cAAc;AAChB,uBAAW,yBAAyB,aAAa,OAAO;AACxD,mBAAO,aAAa;AAAA,UACtB;AAAA,QACF;AACA,aAAK,KAAK;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAOD,YAAI,0BAA0B,YAAY,SAGtC,CAAC,gBAAgB,CAAC,aAAa,qBAAqB;AACtD,qBAAW,iBAET,SAAS,QAAQ,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI,IAEjD;AAAA,QACJ;AACA,mBAAW;AACX,mBAAW,aAAa,IAAI,KAAK;AACjC,YAAI,aAAa;AACf,qBAAW,YAAY;AAAA,QACzB;AACA,mBAAW;AAAA,MACb;AAIA,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,CAAC,OAAO,gBAAgB,OAAO,YAAY,OAAO,EAAE,KAAK,IAAI;AACxE,aAAO;AAAA,QACL,YAAY;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,IAAAA,kBAAiB,UAAU,2BAA2B,WAAY;AAChE,UAAI,SAAS,CAAC;AACd,eAAS,aAAa,GAAG,eAAe,GAAG,aAAa,KAAK,eAAe,cAAc;AACxF,YAAI,SAAS;AACb,YAAI,eAAe,KAAK,WAAW,YAAY;AAE/C,YAAI,gBAAgB,aAAa,kBAAkB,YAAY;AAC7D,cAAI,CAAC,aAAa,oBAAoB;AACpC,qBAAS,aAAa;AAAA,UACxB;AACA;AAAA,QACF,OAAO;AACL,cAAI,eAAe,KAAK,mBAAmB,UAAU;AACrD,cAAI,cAAc;AAChB,qBAAS,aAAa;AAAA,UACxB;AAAA,QACF;AACA,eAAO,KAAK,MAAM;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AACA,IAAAA,kBAAiB,UAAU,6BAA6B,SAAU,QAAQ;AACxE,WAAK,WAAW,KAAK,MAAM;AAC3B,aAAO,qBAAqB;AAC5B,WAAK;AAIL,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEK,SAAS,mBAAmB,QAAQ;AACzC,SAAO,kBAAkB;AAC3B;AACO,SAAS,iBAAiB,SAAS;AACxC,MAAI,iBAAiB,cAAc;AACnC,WAAS,IAAI,GAAG,KAAK,WAAW,CAAC,GAAG,QAAQ,KAAK;AAC/C,QAAI,gBAAgB,QAAQ,CAAC;AAC7B,QAAI,cAAc,SAAS,aAAa,IAAI,cAAc,OAAO;AACjE,QAAI,eAAe,QAAQ,eAAe,IAAI,WAAW,KAAK,MAAM;AAClE,qBAAe,IAAI,aAAa,CAAC;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,uBAAuB,QAAQ;AAC7C,MAAI,cAAc,MAAM,MAAM;AAC9B,SAAO,YAAY,eAAe,YAAY,aAAa,iBAAiB,OAAO,gBAAgB;AACrG;AACO,SAAS,2BAA2B,UAAU;AACnD,SAAO,WAAW;AACpB;;;ACvKA,IAAIC,YAAkB;AACtB,IAAIC,OAAa;AACjB,IAAI,iBAAiB,OAAO,eAAe,cAAc,QAAQ;AAGjE,IAAI,YAAY;AAChB,IAAI,kBAAkB;AAEtB,IAAI,0BAA0B,CAAC,iBAAiB,aAAa,WAAW,uBAAuB,eAAe,cAAc,YAAY,mBAAmB,eAAe,aAAa,kBAAkB;AACzM,IAAI,mBAAmB,CAAC,oBAAoB;AAI5C,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAAA;AAAA,EAA0B,WAAY;AAMxC,aAASC,YAAW,iBAAiB,WAAW;AAC9C,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,YAAY,CAAC;AAClB,WAAK,UAAU,CAAC;AAKhB,WAAK,UAAU,CAAC;AAEhB,WAAK,UAAU,CAAC;AAEhB,WAAK,eAAe,CAAC;AAErB,WAAK,eAAe,CAAC;AAErB,WAAK,cAAc,CAAC;AAEpB,WAAK,qBAAqB,CAAC;AAC3B,WAAK,mBAAmB,CAAC;AAKzB,WAAK,gBAAgB;AAGrB,WAAK,uBAAuB,CAAC,gBAAgB,cAAc,oBAAoB,kBAAkB,KAAK;AAEtG,WAAK,oBAAoB,CAAC,cAAc,aAAa;AACrD,WAAK,qBAAqB,CAAC,cAAc,oBAAoB,gBAAgB;AAC7E,UAAI;AACJ,UAAI,oBAAoB;AACxB,UAAI,mBAAmB,eAAe,GAAG;AACvC,qBAAa,gBAAgB;AAC7B,aAAK,cAAc,gBAAgB,mBAAmB;AACtD,aAAK,UAAU;AAAA,MACjB,OAAO;AACL,4BAAoB;AACpB,qBAAa;AAAA,MACf;AACA,mBAAa,cAAc,CAAC,KAAK,GAAG;AACpC,UAAI,iBAAiB,CAAC;AACtB,UAAI,iBAAiB,CAAC;AACtB,UAAI,qBAAqB,CAAC;AAC1B,UAAI,cAAc;AAClB,UAAI,WAAW,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAE1C,YAAI,eAAe,WAAW,CAAC;AAC/B,YAAI,gBAAuB,SAAS,YAAY,IAAI,IAAI,8BAAsB;AAAA,UAC5E,MAAM;AAAA,QACR,CAAC,IAAI,EAAE,wBAAwB,iCAAyB,IAAI,8BAAsB,YAAY,IAAI;AAClG,YAAI,gBAAgB,cAAc;AAClC,sBAAc,OAAO,cAAc,QAAQ;AAC3C,YAAI,CAAC,cAAc,UAAU;AAC3B,wBAAc,WAAW;AACzB,wBAAc,gBAAgB;AAAA,QAChC;AACA,YAAI,YAAY,cAAc,YAAY,cAAc,aAAa,CAAC;AACtE,uBAAe,KAAK,aAAa;AACjC,uBAAe,aAAa,IAAI;AAChC,YAAI,SAAS,aAAa,KAAK,MAAM;AACnC,wBAAc;AAAA,QAChB;AACA,YAAI,cAAc,uBAAuB;AACvC,6BAAmB,aAAa,IAAI,CAAC;AAAA,QACvC;AACA,YAAI,UAAU,aAAa,GAAG;AAC5B,eAAK,cAAc;AAAA,QACrB;AACA,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,YAAI,MAAuC;AACzC,UAAO,OAAO,qBAAqB,cAAc,iBAAiB,CAAC;AAAA,QACrE;AACA,YAAI,mBAAmB;AACrB,wBAAc,gBAAgB;AAAA,QAChC;AAAA,MACF;AACA,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,sBAAsB,WAAW;AACtC,WAAK,YAAY;AACjB,WAAK,sBAAsB;AAC3B,UAAI,KAAK,aAAa;AACpB,YAAI,iBAAiB,KAAK,gBAAuB,cAAc;AAC/D,QAAO,KAAK,gBAAgB,SAAU,SAAS;AAC7C,yBAAe,IAAI,eAAe,OAAO,EAAE,eAAe,OAAO;AAAA,QACnE,CAAC;AAAA,MACH;AAAA,IACF;AAkBA,IAAAA,YAAW,UAAU,eAAe,SAAU,KAAK;AACjD,UAAI,SAAS,KAAK,mBAAmB,GAAG;AACxC,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,eAAS;AACT,UAAI,CAAC,KAAK,aAAa;AACrB,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AAGA,UAAI,UAAU,KAAK,cAAc,IAAI,MAAM;AAC3C,UAAI,WAAW,MAAM;AACnB,eAAO;AAAA,MACT;AACA,UAAI,eAAe,KAAK,QAAQ,mBAAmB,MAAM;AACzD,UAAI,cAAc;AAChB,eAAO,aAAa;AAAA,MACtB;AAAA,IACF;AAKA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,KAAK;AACtD,UAAI,SAAS,KAAK,mBAAmB,GAAG;AACxC,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,UAAI,UAAU,KAAK,YAAY,GAAG;AAClC,aAAO,UAAU,QAAQ,gBAAgB,KAAK,cAAc,KAAK,QAAQ,wBAAwB,GAAG,IAAI;AAAA,IAC1G;AAoBA,IAAAA,YAAW,UAAU,qBAAqB,SAAU,KAAK;AACvD,UAAW,SAAS,GAAG,KAEpB,OAAO,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,YAAY,GAAG,MAAM,CAAC,KAAK,eAAe,KAAK,QAAQ,wBAAwB,GAAG,IAAI,IAAI;AAC/H,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,KAAK;AACtD,UAAI,SAAS,KAAK,kBAAkB,GAAG;AACvC,UAAI,MAAuC;AACzC,YAAI,UAAU,MAAM;AAClB,gBAAM,IAAI,MAAM,uBAAuB,GAAG;AAAA,QAC5C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAOA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK;AAErD,aAAO,KAAK,YAAY,KAAK,aAAa,GAAG,CAAC;AAAA,IAChD;AACA,IAAAA,YAAW,UAAU,wBAAwB,SAAU,aAAa;AAClE,UAAI,iBAAiB,KAAK;AAC1B,WAAK,cAAc,cAAc,SAAU,SAAS;AAClD,eAAO,eAAe,eAAe,OAAO,IAAI,eAAe,OAAO,IAAI;AAAA,MAC5E,IAAI,SAAU,SAAS;AACrB,eAAO,eAAe,OAAO;AAAA,MAC/B;AAAA,IACF;AAIA,IAAAA,YAAW,UAAU,uBAAuB,WAAY;AACtD,aAAO,KAAK,YAAY,gBAAgB,MAAM;AAAA,IAChD;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,UAAU,KAAK;AAC3D,UAAI,oBAAoB,KAAK;AAC7B,UAAI,OAAO,MAAM;AACf,eAAO,kBAAkB,uBAAuB,QAAQ;AAAA,MAC1D;AACA,UAAI,OAAO,kBAAkB,OAAO,QAAQ;AAC5C,aAAO,OAAO,KAAK,GAAG,IAAI;AAAA,IAC5B;AACA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,UAAU;AAC1D,UAAI,oBAAoB,KAAK;AAC7B,UAAI,OAAO,kBAAkB,OAAO,QAAQ;AAC5C,cAAQ,QAAQ,CAAC,GAAG,MAAM;AAAA,IAC5B;AACA,IAAAA,YAAW,UAAU,WAAW,WAAY;AAC1C,aAAO,KAAK;AAAA,IACd;AAUA,IAAAA,YAAW,UAAU,WAAW,SAAU,MAAM,UAAU,gBAAgB;AACxE,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,gBAAgB,mBAAW;AAC7B,gBAAQ;AAAA,MACV;AACA,UAAI,CAAC,OAAO;AACV,YAAI,aAAa,KAAK;AACtB,YAAI,WAAW,iBAAiB,IAAI,KAAY,YAAY,IAAI,IAAI,IAAI,oBAAoB,MAAM,WAAW,MAAM,IAAI;AACvH,gBAAQ,IAAI,kBAAU;AACtB,YAAI,iBAAiBD,KAAI,YAAY,SAAU,SAAS;AACtD,iBAAO;AAAA,YACL,MAAM,MAAM,UAAU,OAAO,EAAE;AAAA,YAC/B,UAAU;AAAA,UACZ;AAAA,QACF,CAAC;AACD,cAAM,SAAS,UAAU,gBAAgB,cAAc;AAAA,MACzD;AACA,WAAK,SAAS;AAEd,WAAK,aAAa,YAAY,CAAC,GAAG,MAAM;AACxC,WAAK,UAAU,CAAC;AAChB,WAAK,mBAAmB,CAAC;AACzB,WAAK,QAAQ,GAAG,MAAM,MAAM,CAAC;AAG7B,WAAK,cAAc,oBAAoB,MAAM,KAAK,OAAO;AACzD,WAAK,aAAa,KAAK,YAAY;AAAA,IACrC;AAIA,IAAAC,YAAW,UAAU,aAAa,SAAU,MAAM;AAChD,UAAI,QAAQ,KAAK,OAAO,WAAW,IAAI;AACvC,WAAK,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IACjC;AAgBA,IAAAA,YAAW,UAAU,eAAe,SAAU,QAAQ,OAAO;AAC3D,UAAI,KAAK,KAAK,OAAO,aAAa,QAAQ,SAAS,MAAM,MAAM,GAC7D,QAAQ,GAAG,OACX,MAAM,GAAG;AACX,UAAI,uBAAuB,KAAK,sBAAsB;AACtD,WAAK,mBAAmB;AACxB,UAAI,OAAO;AACT,iBAAS,MAAM,OAAO,MAAM,KAAK,OAAO;AACtC,cAAI,YAAY,MAAM;AACtB,eAAK,UAAU,GAAG,IAAI,MAAM,SAAS;AACrC,cAAI,sBAAsB;AACxB,2BAAe,MAAM,GAAG;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,qBAAqB,WAAY;AACpD,UAAI,QAAQ,KAAK;AACjB,UAAI,aAAa,KAAK;AACtB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAI,UAAU,KAAK,UAAU,WAAW,CAAC,CAAC;AAC1C,YAAI,QAAQ,aAAa;AACvB,gBAAM,mBAAmB,QAAQ,eAAe,QAAQ,WAAW;AAAA,QACrE;AAAA,MACF;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,wBAAwB,WAAY;AACvD,UAAI,WAAW,KAAK,OAAO,YAAY;AACvC,aAAO,KAAK,aAAa,QAAQ,SAAS,UAAU,EAAE,iBAAiB,6BAA6B,CAAC,SAAS;AAAA,IAChH;AACA,IAAAA,YAAW,UAAU,UAAU,SAAU,OAAO,KAAK;AACnD,UAAI,SAAS,KAAK;AAChB;AAAA,MACF;AACA,UAAI,QAAQ,KAAK;AACjB,UAAI,WAAW,MAAM,YAAY;AACjC,WAAK,mBAAmB;AACxB,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,KAAK;AAClB,UAAI,eAAe,SAAS,UAAU,EAAE;AACxC,UAAI,mBAAmB,iBAAiB;AASxC,UAAI,oBAAoB,CAAC,SAAS,MAAM;AACtC,YAAI,iBAAiB,CAAC;AACtB,iBAAS,MAAM,OAAO,MAAM,KAAK,OAAO;AAEtC,cAAI,WAAW,SAAS,QAAQ,KAAK,cAAc;AACnD,cAAI,CAAC,KAAK,iBAAiB,iBAAiB,QAAQ,GAAG;AACrD,iBAAK,gBAAgB;AAAA,UACvB;AACA,cAAI,UAAU;AACZ,gBAAI,WAAW,SAAS;AACxB,gBAAI,SAAS,GAAG,KAAK,QAAQ,YAAY,MAAM;AAC7C,uBAAS,GAAG,IAAI,oBAAoB,UAAU,IAAI;AAAA,YACpD;AACA,gBAAI,SAAS,SAAS;AACtB,gBAAI,OAAO,GAAG,KAAK,QAAQ,UAAU,MAAM;AACzC,qBAAO,GAAG,IAAI,oBAAoB,QAAQ,IAAI;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,sBAAsB,GAAG;AAChC,iBAAS,MAAM,OAAO,MAAM,KAAK,OAAO;AACtC,yBAAe,MAAM,GAAG;AAAA,QAC1B;AAAA,MACF;AACA,2BAAqB,IAAI;AAAA,IAC3B;AAeA,IAAAA,YAAW,UAAU,uBAAuB,SAAU,KAAK;AACzD,aAAO,KAAK,mBAAmB,GAAG,KAAK,KAAK,OAAO,cAAc,KAAK,kBAAkB,GAAG,CAAC;AAAA,IAC9F;AAKA,IAAAA,YAAW,UAAU,uBAAuB,SAAU,QAAQ,KAAK;AACjE,YAAM,KAAK,aAAa,GAAG;AAC3B,WAAK,mBAAmB,GAAG,IAAI,OAAO,MAAM;AAAA,IAC9C;AACA,IAAAA,YAAW,UAAU,qBAAqB,SAAU,KAAK;AACvD,aAAO,KAAK,iBAAiB,GAAG;AAAA,IAClC;AACA,IAAAA,YAAW,UAAU,qBAAqB,SAAU,KAAK,OAAO;AAC9D,MAAAF,UAAS,GAAG,IAAW,OAAO,KAAK,kBAAkB,GAAG,IAAI,KAAK,iBAAiB,GAAG,IAAI;AAAA,IAC3F;AAOA,IAAAE,YAAW,UAAU,UAAU,SAAU,KAAK;AAC5C,UAAI,WAAW,KAAK,YAAY,GAAG;AACnC,UAAI,OAAO,KAAK,UAAU,QAAQ;AAClC,UAAI,QAAQ,QAAQ,KAAK,eAAe,MAAM;AAC5C,eAAO,mBAAmB,MAAM,KAAK,aAAa,QAAQ;AAAA,MAC5D;AACA,UAAI,QAAQ,MAAM;AAChB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,QAAQ,KAAK;AACzD,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG;AACzC,UAAI,cAAc,KAAK,OAAO,eAAe,MAAM;AACnD,UAAI,aAAa;AACf,eAAO,YAAY,WAAW,OAAO;AAAA,MACvC;AACA,aAAO;AAAA,IACT;AAOA,IAAAA,YAAW,UAAU,QAAQ,SAAU,KAAK;AAC1C,aAAO,MAAM,MAAM,KAAK,YAAY,GAAG,CAAC;AAAA,IAC1C;AACA,IAAAA,YAAW,UAAU,QAAQ,WAAY;AACvC,aAAO,KAAK,OAAO,MAAM;AAAA,IAC3B;AAMA,IAAAA,YAAW,UAAU,MAAM,SAAU,KAAK,KAAK;AAC7C,UAAI,QAAQ,KAAK;AACjB,UAAI,UAAU,KAAK,UAAU,GAAG;AAChC,UAAI,SAAS;AACX,eAAO,MAAM,IAAI,QAAQ,eAAe,GAAG;AAAA,MAC7C;AAAA,IACF;AAIA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAK,QAAQ;AAC1D,UAAI,QAAQ,KAAK;AACjB,UAAI,UAAU,KAAK,UAAU,GAAG;AAChC,UAAI,SAAS;AACX,eAAO,MAAM,cAAc,QAAQ,eAAe,MAAM;AAAA,MAC1D;AAAA,IACF;AACA,IAAAA,YAAW,UAAU,aAAa,WAAY;AAC5C,aAAO,KAAK,OAAO,WAAW;AAAA,IAChC;AACA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAK;AAClD,aAAO,KAAK,OAAO,cAAc,KAAK,kBAAkB,GAAG,CAAC;AAAA,IAC9D;AACA,IAAAA,YAAW,UAAU,SAAS,SAAU,KAAK;AAC3C,aAAO,KAAK,OAAO,OAAO,KAAK,kBAAkB,GAAG,CAAC;AAAA,IACvD;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,KAAK;AAC9C,aAAO,KAAK,OAAO,UAAU,KAAK,kBAAkB,GAAG,CAAC;AAAA,IAC1D;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,YAAY,KAAK;AAC1D,UAAI,QAAQ;AACZ,UAAI,QAAQ,KAAK;AACjB,aAAc,QAAQ,UAAU,IAAI,MAAM,UAAUD,KAAI,YAAY,SAAU,KAAK;AACjF,eAAO,MAAM,kBAAkB,GAAG;AAAA,MACpC,CAAC,GAAG,GAAG,IAAI,MAAM,UAAU,UAAU;AAAA,IACvC;AAKA,IAAAC,YAAW,UAAU,WAAW,SAAU,KAAK;AAC7C,UAAI,wBAAwB,KAAK,YAAY;AAC7C,eAAS,IAAI,GAAG,MAAM,sBAAsB,QAAQ,IAAI,KAAK,KAAK;AAIhE,YAAI,MAAM,KAAK,OAAO,IAAI,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG;AACzD,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,cAAc,SAAU,MAAM;AACjD,eAAS,IAAI,GAAG,MAAM,KAAK,OAAO,MAAM,GAAG,IAAI,KAAK,KAAK;AACvD,YAAI,KAAK,QAAQ,CAAC,MAAM,MAAM;AAC5B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,KAAK;AAChD,aAAO,KAAK,OAAO,YAAY,GAAG;AAAA,IACpC;AACA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,UAAU;AACzD,aAAO,KAAK,OAAO,gBAAgB,QAAQ;AAAA,IAC7C;AAQA,IAAAA,YAAW,UAAU,aAAa,SAAU,KAAK,OAAO;AACtD,UAAI,kBAAkB,OAAO,KAAK,oBAAoB,GAAG;AACzD,UAAI,MAAuC;AACzC,YAAI,CAAC,iBAAiB;AACpB,gBAAM,IAAI,MAAM,sBAAsB;AAAA,QACxC;AAAA,MACF;AACA,UAAI,WAAW,mBAAmB,gBAAgB,KAAK;AACvD,UAAI,YAAY,QAAQ,MAAM,QAAQ,GAAG;AACvC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AASA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK,OAAO,aAAa;AACzE,aAAO,KAAK,OAAO,iBAAiB,KAAK,kBAAkB,GAAG,GAAG,OAAO,WAAW;AAAA,IACrF;AACA,IAAAA,YAAW,UAAU,OAAO,SAAU,MAAM,IAAI,KAAK;AACnD;AAEA,UAAW,WAAW,IAAI,GAAG;AAC3B,cAAM;AACN,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,OAAO,OAAO;AAClB,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAC5E,WAAK,OAAO,KAAK,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,IAChE;AACA,IAAAC,YAAW,UAAU,aAAa,SAAU,MAAM,IAAI,KAAK;AACzD;AAEA,UAAW,WAAW,IAAI,GAAG;AAC3B,cAAM;AACN,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,OAAO,OAAO;AAClB,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAC5E,WAAK,SAAS,KAAK,OAAO,OAAO,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAC9E,aAAO;AAAA,IACT;AAKA,IAAAC,YAAW,UAAU,cAAc,SAAU,OAAO;AAClD;AAEA,UAAI,QAAQ;AACZ,UAAI,aAAa,CAAC;AAClB,UAAI,OAAc,KAAK,KAAK;AAC5B,UAAI,aAAa,CAAC;AAClB,MAAO,KAAK,MAAM,SAAU,KAAK;AAC/B,YAAI,SAAS,MAAM,kBAAkB,GAAG;AACxC,mBAAW,MAAM,IAAI,MAAM,GAAG;AAC9B,mBAAW,KAAK,MAAM;AAAA,MACxB,CAAC;AACD,WAAK,SAAS,KAAK,OAAO,YAAY,UAAU;AAChD,aAAO;AAAA,IACT;AAEA,IAAAA,YAAW,UAAU,WAAW,SAAU,MAAM,IAAI,KAAK;AACvD;AAEA,UAAW,WAAW,IAAI,GAAG;AAC3B,cAAM;AACN,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,OAAO;AACb,UAAI,SAAS,CAAC;AACd,WAAK,KAAK,MAAM,WAAY;AAC1B,eAAO,KAAK,MAAM,GAAG,MAAM,MAAM,SAAS,CAAC;AAAA,MAC7C,GAAG,GAAG;AACN,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,MAAM,SAAU,MAAM,IAAI,KAAK,WAAW;AAC7D;AAGA,UAAI,OAAO,OAAO,aAAa;AAC/B,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAC5E,UAAI,OAAO,yBAAyB,IAAI;AACxC,WAAK,SAAS,KAAK,OAAO,IAAI,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAC3E,aAAO;AAAA,IACT;AACA,IAAAC,YAAW,UAAU,SAAS,SAAU,MAAM,IAAI,KAAK,WAAW;AAChE,UAAI,QAAQ;AAEZ,UAAI,OAAO,OAAO,aAAa;AAC/B,UAAI,MAAuC;AACzC,QAAO,KAAK,oBAAoB,IAAI,GAAG,SAAU,KAAK;AACpD,cAAI,UAAU,MAAM,iBAAiB,GAAG;AACxC,cAAI,CAAC,QAAQ,oBAAoB;AAC/B,oBAAQ,MAAM,8CAA8C;AAAA,UAC9D;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,aAAaD,KAAI,oBAAoB,IAAI,GAAG,KAAK,mBAAmB,IAAI;AAK5E,WAAK,OAAO,OAAO,YAAY,OAAc,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,IAClE;AAKA,IAAAC,YAAW,UAAU,aAAa,SAAU,WAAW,MAAM,aAAa,aAAa;AACrF,UAAI,OAAO,yBAAyB,IAAI;AACxC,WAAK,SAAS,KAAK,OAAO,WAAW,KAAK,kBAAkB,SAAS,GAAG,MAAM,aAAa,WAAW;AACtG,aAAO;AAAA,IACT;AAMA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,gBAAgB,MAAM;AACtE,UAAI,OAAO,yBAAyB,IAAI;AACxC,WAAK,SAAS,KAAK,OAAO,iBAAiB,KAAK,kBAAkB,cAAc,GAAG,IAAI;AACvF,aAAO;AAAA,IACT;AAMA,IAAAA,YAAW,UAAU,iBAAiB,SAAU,gBAAgB,MAAM;AACpE,UAAI,OAAO,yBAAyB,IAAI;AACxC,WAAK,SAAS,KAAK,OAAO,eAAe,KAAK,kBAAkB,cAAc,GAAG,IAAI;AACrF,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,UAAU,iBAAiB,SAAU,KAAK;AACnD,aAAO,KAAK,OAAO,eAAe,GAAG;AAAA,IACvC;AAKA,IAAAA,YAAW,UAAU,eAAe,SAAU,KAAK;AACjD,UAAI,YAAY,KAAK;AACrB,UAAI,WAAW,KAAK,eAAe,GAAG;AACtC,aAAO,IAAI,cAAM,UAAU,WAAW,aAAa,UAAU,OAAO;AAAA,IACtE;AAIA,IAAAA,YAAW,UAAU,OAAO,SAAU,WAAW;AAC/C,UAAI,WAAW;AACf,aAAO,IAAI,mBAAW,YAAY,UAAU,SAAS,EAAE,WAAW,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,WAAW,GAAG,SAAU,KAAK;AACrH,eAAO,MAAM,WAAW,GAAG;AAAA,MAC7B,GAAG,SAAU,KAAK;AAChB,eAAO,MAAM,UAAU,GAAG;AAAA,MAC5B,CAAC;AAAA,IACH;AAIA,IAAAA,YAAW,UAAU,YAAY,SAAU,KAAK;AAC9C,UAAI,SAAS,KAAK;AAClB,aAAO,UAAU,OAAO,GAAG;AAAA,IAC7B;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,OAAO,KAAK;AACrD,WAAK,UAAU,KAAK,WAAW,CAAC;AAChC,UAAIF,UAAS,KAAK,GAAG;AACnB,QAAO,OAAO,KAAK,SAAS,KAAK;AAAA,MACnC,OAAO;AACL,aAAK,QAAQ,KAAK,IAAI;AAAA,MACxB;AAAA,IACF;AAKA,IAAAE,YAAW,UAAU,gBAAgB,SAAU,KAAK,KAAK;AACvD,UAAI,aAAa,KAAK,aAAa,GAAG;AACtC,UAAI,MAAM,cAAc,WAAW,GAAG;AACtC,UAAI,OAAO,MAAM;AAEf,eAAO,KAAK,UAAU,GAAG;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AAIA,IAAAA,YAAW,UAAU,gBAAgB,WAAY;AAC/C,aAAO,KAAK,aAAa,SAAS;AAAA,IACpC;AAKA,IAAAA,YAAW,UAAU,yBAAyB,SAAU,KAAK,KAAK;AAChE,UAAI,cAAc,KAAK;AACvB,UAAI,aAAa,YAAY,GAAG;AAChC,UAAI,CAAC,YAAY;AACf,qBAAa,YAAY,GAAG,IAAI,CAAC;AAAA,MACnC;AACA,UAAI,MAAM,WAAW,GAAG;AACxB,UAAI,OAAO,MAAM;AACf,cAAM,KAAK,UAAU,GAAG;AAExB,YAAW,QAAQ,GAAG,GAAG;AACvB,gBAAM,IAAI,MAAM;AAAA,QAClB,WAAWF,UAAS,GAAG,GAAG;AACxB,gBAAa,OAAO,CAAC,GAAG,GAAG;AAAA,QAC7B;AACA,mBAAW,GAAG,IAAI;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAEA,IAAAE,YAAW,UAAU,gBAAgB,SAAU,KAAK,KAAK,OAAO;AAC9D,UAAI,aAAa,KAAK,aAAa,GAAG,KAAK,CAAC;AAC5C,WAAK,aAAa,GAAG,IAAI;AACzB,UAAIF,UAAS,GAAG,GAAG;AACjB,QAAO,OAAO,YAAY,GAAG;AAAA,MAC/B,OAAO;AACL,mBAAW,GAAG,IAAI;AAAA,MACpB;AAAA,IACF;AAIA,IAAAE,YAAW,UAAU,iBAAiB,WAAY;AAChD,WAAK,UAAU,CAAC;AAChB,WAAK,eAAe,CAAC;AAAA,IACvB;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,KAAK,KAAK;AACnD,MAAAF,UAAS,GAAG,IAAW,OAAO,KAAK,SAAS,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI;AAAA,IACzE;AAIA,IAAAE,YAAW,UAAU,YAAY,SAAU,KAAK;AAC9C,aAAO,KAAK,QAAQ,GAAG;AAAA,IACzB;AAIA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAK;AAClD,aAAO,KAAK,aAAa,GAAG;AAAA,IAC9B;AAIA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,KAAKC,SAAQ,OAAO;AACjE,WAAK,aAAa,GAAG,IAAI,QAAe,OAAO,KAAK,aAAa,GAAG,KAAK,CAAC,GAAGA,OAAM,IAAIA;AAAA,IACzF;AAIA,IAAAD,YAAW,UAAU,mBAAmB,WAAY;AAClD,WAAK,aAAa,SAAS;AAAA,IAC7B;AAIA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK,IAAI;AACzD,UAAI,cAAc,KAAK,aAAa,KAAK,UAAU;AACnD,sBAAgB,aAAa,KAAK,UAAU,KAAK,EAAE;AACnD,WAAK,YAAY,GAAG,IAAI;AAAA,IAC1B;AACA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,KAAK;AACrD,aAAO,KAAK,YAAY,GAAG;AAAA,IAC7B;AACA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,IAAI,SAAS;AAC9D,MAAO,KAAK,KAAK,aAAa,SAAU,IAAI,KAAK;AAC/C,YAAI,IAAI;AACN,gBAAM,GAAG,KAAK,SAAS,IAAI,GAAG;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAKA,IAAAA,YAAW,UAAU,eAAe,SAAU,MAAM;AAClD,UAAI,CAAC,MAAM;AACT,eAAO,IAAIA,YAAW,KAAK,UAAU,KAAK,UAAUD,KAAI,KAAK,YAAY,KAAK,aAAa,IAAI,GAAG,KAAK,SAAS;AAAA,MAClH;AACA,yBAAmB,MAAM,IAAI;AAC7B,WAAK,SAAS,KAAK;AACnB,aAAO;AAAA,IACT;AAIA,IAAAC,YAAW,UAAU,aAAa,SAAU,YAAY,gBAAgB;AACtE,UAAI,iBAAiB,KAAK,UAAU;AACpC,UAAI,CAAQ,WAAW,cAAc,GAAG;AACtC;AAAA,MACF;AACA,WAAK,mBAAmB,KAAK,oBAAoB,CAAC;AAClD,WAAK,iBAAiB,KAAK,UAAU;AACrC,WAAK,UAAU,IAAI,WAAY;AAC7B,YAAI,MAAM,eAAe,MAAM,MAAM,SAAS;AAC9C,eAAO,eAAe,MAAM,MAAM,CAAC,GAAG,EAAE,OAAc,MAAM,SAAS,CAAC,CAAC;AAAA,MACzE;AAAA,IACF;AAIA,IAAAA,YAAW,gBAAgB,WAAY;AACrC,6BAAuB,SAAU,MAAM;AACrC,YAAI,qBAAqB,KAAK;AAC9B,QAAO,KAAK,oBAAoB,SAAU,iBAAiB,KAAK;AAC9D,cAAI,UAAU,KAAK,UAAU,GAAG;AAEhC,cAAI,cAAc,QAAQ;AAC1B,cAAI,QAAQ,KAAK;AACjB,cAAI,aAAa;AACf,8BAAkB,mBAAmB,GAAG,IAAI,IAAI,eAAe,YAAY,WAAW,MAAM;AAG5F,qBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,8BAAgB,CAAC,IAAI;AAAA,YACvB;AACA,qBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,KAAK;AAEtC,8BAAgB,MAAM,IAAI,QAAQ,eAAe,CAAC,CAAC,IAAI;AAAA,YACzD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA,2BAAqB,SAAU,MAAM,QAAQ,KAAK;AAChD,eAAO,oBAAoB,KAAK,aAAa,QAAQ,GAAG,GAAG,IAAI;AAAA,MACjE;AAIA,cAAQ,SAAU,MAAM,UAAU;AAChC,YAAI,KAAK,KAAK,QAAQ,QAAQ;AAC9B,YAAI,MAAM,QAAQ,KAAK,aAAa,MAAM;AACxC,eAAK,mBAAmB,MAAM,KAAK,WAAW,QAAQ;AAAA,QACxD;AACA,YAAI,MAAM,MAAM;AACd,eAAK,YAAY;AAAA,QACnB;AACA,eAAO;AAAA,MACT;AACA,4BAAsB,SAAU,YAAY;AAC1C,YAAI,CAAQ,QAAQ,UAAU,GAAG;AAC/B,uBAAa,cAAc,OAAO,CAAC,UAAU,IAAI,CAAC;AAAA,QACpD;AACA,eAAO;AAAA,MACT;AAIA,iCAA2B,SAAU,UAAU;AAC7C,YAAI,OAAO,IAAIA,YAAW,SAAS,UAAU,SAAS,UAAUD,KAAI,SAAS,YAAY,SAAS,aAAa,QAAQ,GAAG,SAAS,SAAS;AAE5I,2BAAmB,MAAM,QAAQ;AACjC,eAAO;AAAA,MACT;AACA,2BAAqB,SAAU,QAAQ,QAAQ;AAC7C,QAAO,KAAK,wBAAwB,OAAO,OAAO,oBAAoB,CAAC,CAAC,GAAG,SAAU,UAAU;AAC7F,cAAI,OAAO,eAAe,QAAQ,GAAG;AACnC,mBAAO,QAAQ,IAAI,OAAO,QAAQ;AAAA,UACpC;AAAA,QACF,CAAC;AACD,eAAO,mBAAmB,OAAO;AACjC,QAAO,KAAK,kBAAkB,SAAU,UAAU;AAChD,iBAAO,QAAQ,IAAW,MAAM,OAAO,QAAQ,CAAC;AAAA,QAClD,CAAC;AACD,eAAO,mBAA0B,OAAO,CAAC,GAAG,OAAO,gBAAgB;AAAA,MACrE;AACA,uBAAiB,SAAU,MAAM,KAAK;AACpC,YAAI,WAAW,KAAK;AACpB,YAAI,SAAS,KAAK;AAClB,YAAI,aAAa,KAAK;AACtB,YAAI,WAAW,KAAK;AACpB,YAAI,OAAO,SAAS,GAAG;AACvB,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,QAAQ,QAAQ,cAAc,MAAM;AACtC,mBAAS,GAAG,IAAI,OAAO,mBAAmB,MAAM,YAAY,GAAG;AAAA,QACjE;AACA,YAAI,MAAM,QAAQ,YAAY,MAAM;AAClC,iBAAO,GAAG,IAAI,KAAK,mBAAmB,MAAM,UAAU,GAAG;AAAA,QAC3D;AACA,YAAI,MAAM,QAAQ,QAAQ,MAAM;AAC9B,cAAI,kBAAkB,KAAK;AAC3B,cAAI,QAAQ,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,KAAK,KAAK;AACnE,eAAK;AACL,cAAI,QAAQ,GAAG;AACb,kBAAM,WAAW;AAAA,UACnB;AACA,iBAAO,GAAG,IAAI;AAAA,QAChB;AAAA,MACF;AAAA,IACF,EAAE;AACF,WAAOC;AAAA,EACT,EAAE;AAAA;AACF,IAAO,qBAAQ;;;ACn+Bf,IAAI,UAAU;AACd,SAAS,cAAc,GAAG,GAAG;AACzB,SAAO,KAAK,IAAI,IAAI,CAAC,IAAI;AAC7B;AACO,SAAS,QAAQ,QAAQ,GAAG,GAAG;AAClC,MAAI,IAAI;AACR,MAAI,IAAI,OAAO,CAAC;AAChB,MAAI,CAAC,GAAG;AACJ,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,KAAK,OAAO,CAAC;AACjB,SAAK,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AAC/C,QAAI;AAAA,EACR;AACA,MAAI,KAAK,OAAO,CAAC;AACjB,MAAI,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;AAC5D,SAAK,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AAAA,EACnD;AACA,SAAO,MAAM;AACjB;;;AC4BA,IAAI,gBAAgB,CAAC;AACrB,SAAS,gBAAgB,QAAQ,WAAW;AAC1C,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,IAAK,eAAe,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,SAAS;AAAA,EACrD;AACF;AACA,SAAS,qBAAqB,QAAQE,MAAKC,MAAK,YAAY;AAC1D,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,IAAI,OAAO,CAAC;AAChB,QAAI,YAAY;AAEd,UAAI,WAAW,QAAQ,CAAC;AAAA,IAC1B;AACA,QAAI,KAAK,SAAS,EAAE,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC,CAAC,GAAG;AACzC,MAAK,IAAID,MAAKA,MAAK,CAAC;AACpB,MAAK,IAAIC,MAAKA,MAAK,CAAC;AAAA,IACtB;AAAA,EACF;AACF;AACA,SAAS,SAAS,QAAQ;AACxB,MAAI,aAAa;AACjB,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,MAAM,OAAO;AACjB,MAAI,KAAK,OAAO,MAAM,CAAC,EAAE,CAAC;AAC1B,MAAI,KAAK,OAAO,MAAM,CAAC,EAAE,CAAC;AAE1B,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,KAAK,OAAO,CAAC,EAAE,CAAC;AACpB,QAAI,KAAK,OAAO,CAAC,EAAE,CAAC;AACpB,QAAI,IAAI,KAAK,KAAK,KAAK;AACvB,kBAAc;AACd,WAAO,KAAK,MAAM;AAClB,WAAO,KAAK,MAAM;AAClB,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO,aAAa,CAAC,KAAK,aAAa,GAAG,KAAK,aAAa,GAAG,UAAU,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;AACpH;AACA,IAAI;AAAA;AAAA,EAAsB,WAAY;AACpC,aAASC,QAAO,MAAM;AACpB,WAAK,OAAO;AAAA,IACd;AACA,IAAAA,QAAO,UAAU,YAAY,SAAU,QAAQ;AAC7C,WAAK,UAAU;AAAA,IACjB;AAMA,IAAAA,QAAO,UAAU,YAAY,WAAY;AACvC,UAAI,SAAS,KAAK;AAClB,UAAI,CAAC,QAAQ;AAGX,iBAAS,KAAK,UAAU,KAAK,WAAW;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAsC,2BAAY;AACpD,aAASC,wBAAuB,UAAU,WAAW;AACnD,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,YAAY;AAAA,IACnB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAyC,2BAAY;AACvD,aAASC,2BAA0B,QAAQ;AACzC,WAAK,OAAO;AACZ,WAAK,SAAS;AAAA,IAChB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,eAAc,MAAM,YAAY,IAAI;AAC3C,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,OAAO;AACb,YAAM,aAAa;AACnB,YAAM,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,aAAa,WAAY;AAC/C,UAAI,aAAa,KAAK;AACtB,UAAI;AACJ,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAI,MAAM,WAAW,CAAC;AACtB,YAAI,WAAW,IAAI;AAGnB,YAAI,OAAO,YAAY,SAAS;AAChC,YAAI,OAAO,gBAAgB;AACzB,uBAAa;AACb,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,UAAI,YAAY;AACd,eAAO,SAAS,WAAW,QAAQ;AAAA,MACrC;AAEA,UAAI,OAAO,KAAK,gBAAgB;AAChC,aAAO,CAAC,KAAK,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,KAAK,SAAS,CAAC;AAAA,IAC3D;AACA,IAAAA,eAAc,UAAU,kBAAkB,SAAU,YAAY;AAC9D,UAAI,OAAO,KAAK;AAEhB,UAAI,QAAQ,CAAC,YAAY;AACvB,eAAO;AAAA,MACT;AACA,UAAIC,OAAM,CAAC,UAAU,QAAQ;AAC7B,UAAIC,OAAM,CAAC,WAAW,SAAS;AAC/B,UAAI,aAAa,KAAK;AACtB,WAAK,YAAY,SAAU,KAAK;AAC9B,YAAI,IAAI,SAAS,WAAW;AAE1B,+BAAqB,IAAI,UAAUD,MAAKC,MAAK,UAAU;AAAA,QACzD,OAAO;AACL,eAAK,IAAI,QAAQ,SAAU,QAAQ;AACjC,iCAAqB,QAAQD,MAAKC,MAAK,UAAU;AAAA,UACnD,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,EAAE,SAASD,KAAI,CAAC,CAAC,KAAK,SAASA,KAAI,CAAC,CAAC,KAAK,SAASC,KAAI,CAAC,CAAC,KAAK,SAASA,KAAI,CAAC,CAAC,IAAI;AACnF,QAAAD,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAIC,KAAI,CAAC,IAAIA,KAAI,CAAC,IAAI;AAAA,MACtC;AACA,aAAO,IAAI,qBAAaD,KAAI,CAAC,GAAGA,KAAI,CAAC,GAAGC,KAAI,CAAC,IAAID,KAAI,CAAC,GAAGC,KAAI,CAAC,IAAID,KAAI,CAAC,CAAC;AACxE,UAAI,CAAC,YAAY;AACf,aAAK,QAAQ;AAAA,MACf;AACA,aAAO;AAAA,IACT;AACA,IAAAD,eAAc,UAAU,UAAU,SAAU,OAAO;AACjD,UAAI,OAAO,KAAK,gBAAgB;AAChC,UAAI,aAAa,KAAK;AACtB,UAAI,CAAC,KAAK,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG;AACrC,eAAO;AAAA,MACT;AACA,cAAS,UAAS,IAAI,GAAG,MAAM,WAAW,QAAQ,IAAI,KAAK,KAAK;AAC9D,YAAI,MAAM,WAAW,CAAC;AAEtB,YAAI,IAAI,SAAS,WAAW;AAC1B;AAAA,QACF;AACA,YAAI,WAAW,IAAI;AACnB,YAAI,YAAY,IAAI;AACpB,YAAmB,QAAQ,UAAU,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG;AAExD,mBAAS,IAAI,GAAG,KAAK,YAAY,UAAU,SAAS,IAAI,KAAK;AAC3D,gBAAmB,QAAQ,UAAU,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG;AAC5D,uBAAS;AAAA,YACX;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAQA,IAAAA,eAAc,UAAU,cAAc,SAAU,GAAG,GAAG,OAAO,QAAQ;AACnE,UAAI,OAAO,KAAK,gBAAgB;AAChC,UAAI,SAAS,KAAK,QAAQ,KAAK;AAC/B,UAAI,CAAC,OAAO;AACV,gBAAQ,SAAS;AAAA,MACnB,WAAW,CAAC,QAAQ;AAClB,iBAAS,QAAQ;AAAA,MACnB;AACA,UAAI,SAAS,IAAI,qBAAa,GAAG,GAAG,OAAO,MAAM;AACjD,UAAI,YAAY,KAAK,mBAAmB,MAAM;AAC9C,UAAI,aAAa,KAAK;AACtB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAI,MAAM,WAAW,CAAC;AACtB,YAAI,IAAI,SAAS,WAAW;AAC1B,0BAAgB,IAAI,UAAU,SAAS;AACvC,eAAK,IAAI,WAAW,SAAU,UAAU;AACtC,4BAAgB,UAAU,SAAS;AAAA,UACrC,CAAC;AAAA,QACH,OAAO;AACL,eAAK,IAAI,QAAQ,SAAU,QAAQ;AACjC,4BAAgB,QAAQ,SAAS;AAAA,UACnC,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO,KAAK;AACZ,WAAK,KAAK,MAAM;AAEhB,WAAK,UAAU,CAAC,KAAK,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,KAAK,SAAS,CAAC;AAAA,IACnE;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,MAAM;AACrD,cAAQ,SAAS,OAAO,KAAK;AAC7B,UAAI,YAAY,IAAIA,eAAc,MAAM,KAAK,YAAY,KAAK,OAAO;AACrE,gBAAU,QAAQ,KAAK;AACvB,gBAAU,cAAc;AACxB,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE,MAAM;AAAA;AAER,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAChD,cAAUG,eAAc,MAAM;AAC9B,aAASA,cAAa,MAAM,oBAAoB;AAC9C,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,OAAO;AACb,YAAM,sBAAsB;AAC5B,aAAO;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,aAAa,WAAY;AAC9C,UAAI,KAAK,KAAK;AACd,UAAI,OAAO,GAAG,gBAAgB;AAC9B,UAAI,SAAS,CAAC,KAAK,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,KAAK,SAAS,CAAC;AAC/D,UAAI,MAAa,SAAS,aAAa;AACvC,UAAI,SAAS;AACb,aAAO,UAAU,CAAC,OAAO,qBAAqB;AAC5C,QAAO,IAAI,KAAK,OAAO,kBAAkB,GAAG,GAAG;AAC/C,iBAAS,OAAO;AAAA,MAClB;AACA,MAAO,OAAO,KAAK,GAAG;AACtB,MAAK,eAAe,QAAQ,QAAQ,GAAG;AACvC,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE,MAAM;AAAA;;;AC5OR,SAAS,OAAO,MAAM;AACpB,MAAI,CAAC,KAAK,cAAc;AACtB,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB;AACrB,MAAI,cAAc,eAAe;AACjC,MAAI,eAAe,MAAM;AACvB,kBAAc;AAAA,EAChB;AACA,MAAI,WAAW,eAAe;AAC9B,EAAO,KAAK,UAAU,SAAU,SAAS;AACvC,QAAI,WAAW,QAAQ;AACvB,QAAI,gBAAgB,SAAS;AAC7B,QAAI,cAAc,SAAS;AAG3B,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AACA,YAAQ,SAAS,MAAM;AAAA,MACrB,KAAK;AACH,iBAAS,cAAc,WAAW,aAAa,eAAe,WAAW;AACzE;AAAA,MACF,KAAK;AACH,oBAAY,aAAa,eAAe,WAAW;AACnD;AAAA,MACF,KAAK;AACH,oBAAY,aAAa,eAAe,WAAW;AACnD;AAAA,MACF,KAAK;AACH,QAAO,KAAK,aAAa,SAAU,OAAO,KAAK;AAC7C,iBAAO,YAAY,OAAO,cAAc,GAAG,GAAG,WAAW;AAAA,QAC3D,CAAC;AAAA,IACL;AAAA,EACF,CAAC;AAED,iBAAe,eAAe;AAC9B,SAAO;AACT;AACA,SAAS,YAAY,OAAO,eAAe,aAAa;AACtD,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,CAAC,IAAI,WAAW,MAAM,CAAC,GAAG,cAAc,CAAC,GAAG,WAAW;AAAA,EAC/D;AACF;AACA,SAAS,WAAW,YAAY,eAAe,aAAa;AAC1D,MAAI,SAAS,CAAC;AACd,MAAI,QAAQ,cAAc,CAAC;AAC3B,MAAI,QAAQ,cAAc,CAAC;AAC3B,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,QAAI,IAAI,WAAW,WAAW,CAAC,IAAI;AACnC,QAAI,IAAI,WAAW,WAAW,IAAI,CAAC,IAAI;AAEvC,QAAI,KAAK,IAAI,EAAE,IAAI;AACnB,QAAI,KAAK,IAAI,EAAE,IAAI;AAEnB,SAAK;AACL,SAAK;AACL,YAAQ;AACR,YAAQ;AAER,WAAO,KAAK,CAAC,IAAI,aAAa,IAAI,WAAW,CAAC;AAAA,EAChD;AACA,SAAO;AACT;AACe,SAAR,aAA8B,SAAS,cAAc;AAC1D,YAAU,OAAO,OAAO;AACxB,SAAc,IAAW,OAAO,QAAQ,UAAU,SAAU,YAAY;AAEtE,WAAO,WAAW,YAAY,WAAW,cAAc,WAAW,SAAS,YAAY,SAAS;AAAA,EAClG,CAAC,GAAG,SAAU,YAAY;AACxB,QAAI,aAAa,WAAW;AAC5B,QAAI,MAAM,WAAW;AACrB,QAAI,aAAa,CAAC;AAClB,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK;AACH,YAAI,cAAc,IAAI;AAGtB,mBAAW,KAAK,IAAI,uBAAuB,YAAY,CAAC,GAAG,YAAY,MAAM,CAAC,CAAC,CAAC;AAChF;AAAA,MACF,KAAK;AACH,QAAO,KAAK,IAAI,aAAa,SAAU,MAAM;AAC3C,cAAI,KAAK,CAAC,GAAG;AACX,uBAAW,KAAK,IAAI,uBAAuB,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,UACpE;AAAA,QACF,CAAC;AACD;AAAA,MACF,KAAK;AACH,mBAAW,KAAK,IAAI,0BAA0B,CAAC,IAAI,WAAW,CAAC,CAAC;AAChE;AAAA,MACF,KAAK;AACH,mBAAW,KAAK,IAAI,0BAA0B,IAAI,WAAW,CAAC;AAAA,IAClE;AACA,QAAI,SAAS,IAAI,cAAc,WAAW,gBAAgB,MAAM,GAAG,YAAY,WAAW,EAAE;AAC5F,WAAO,aAAa;AACpB,WAAO;AAAA,EACT,CAAC;AACH;;;ACrGA,IAAI;AAAA;AAAA,EAAqB,WAAY;AACnC,aAASC,OAAM,SAAS;AACtB,WAAK,WAAW,WAAW,CAAC;AAC5B,WAAK,UAAU,CAAC,UAAU,SAAS;AAAA,IACrC;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,MAAM;AAC3C,aAAO,KAAK,SAAS,IAAI;AAAA,IAC3B;AAIA,IAAAA,OAAM,UAAU,cAAc,SAAU,OAAO;AAC7C,UAAI,SAAS,KAAK;AAClB,YAAM,CAAC,IAAI,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,MAAM,CAAC;AAC5C,YAAM,CAAC,IAAI,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IAG9C;AAIA,IAAAA,OAAM,UAAU,sBAAsB,SAAU,MAAM,KAAK;AACzD,WAAK,YAAY,KAAK,qBAAqB,GAAG,CAAC;AAAA,IACjD;AAMA,IAAAA,OAAM,UAAU,YAAY,WAAY;AACtC,aAAO,KAAK,QAAQ,MAAM;AAAA,IAC5B;AAIA,IAAAA,OAAM,UAAU,YAAY,SAAU,OAAO,KAAK;AAChD,UAAI,aAAa,KAAK;AACtB,UAAI,CAAC,MAAM,KAAK,GAAG;AACjB,mBAAW,CAAC,IAAI;AAAA,MAClB;AACA,UAAI,CAAC,MAAM,GAAG,GAAG;AACf,mBAAW,CAAC,IAAI;AAAA,MAClB;AAAA,IACF;AAIA,IAAAA,OAAM,UAAU,kBAAkB,SAAU,OAAO;AACjD,aAAO,KAAK,QAAQ,CAAC,KAAK,SAAS,KAAK,QAAQ,CAAC,KAAK;AAAA,IACxD;AAKA,IAAAA,OAAM,UAAU,UAAU,WAAY;AACpC,aAAO,KAAK;AAAA,IACd;AAKA,IAAAA,OAAM,UAAU,WAAW,SAAU,SAAS;AAC5C,WAAK,WAAW;AAAA,IAClB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACQ,sBAAsB,KAAK;AACrC,IAAO,gBAAQ;;;ACnEf,IAAI,UAAU;AACd,IAAI;AAAA;AAAA,EAA2B,WAAY;AACzC,aAASC,aAAY,KAAK;AACxB,WAAK,aAAa,IAAI,cAAc,CAAC;AACrC,WAAK,eAAe,IAAI;AACxB,WAAK,iBAAiB,IAAI;AAC1B,WAAK,MAAM,EAAE;AAAA,IACf;AACA,IAAAA,aAAY,oBAAoB,SAAU,WAAW;AACnD,UAAI,SAAS,UAAU;AACvB,UAAI,OAAO,OAAO;AAClB,UAAI,aAAa,QAAQ,IAAI,MAAM,OAAO;AAC1C,aAAO,IAAIA,aAAY;AAAA,QACrB;AAAA,QACA,aAAa,CAAC;AAAA;AAAA,QAEd,eAAe,OAAO,iBAAiB;AAAA,MACzC,CAAC;AAAA,IACH;AACA;AACA,IAAAA,aAAY,UAAU,aAAa,SAAU,UAAU;AAErD,aAAO,KAAK,gBAAgB,EAAE,IAAI,QAAQ;AAAA,IAC5C;AAIA,IAAAA,aAAY,UAAU,kBAAkB,SAAU,UAAU;AAC1D,UAAI;AACJ,UAAI,cAAc,KAAK;AAMvB,UAAI,CAAC,SAAS,QAAQ,KAAK,CAAC,aAAa;AACvC,eAAO;AAAA,MACT;AASA,UAAI,eAAe,CAAC,KAAK,gBAAgB;AACvC,gBAAQ,KAAK,WAAW;AACxB,aAAK,WAAW,KAAK,IAAI;AACzB,eAAO;AAAA,MACT;AACA,UAAIC,OAAM,KAAK,gBAAgB;AAE/B,cAAQA,KAAI,IAAI,QAAQ;AACxB,UAAI,SAAS,MAAM;AACjB,YAAI,aAAa;AACf,kBAAQ,KAAK,WAAW;AACxB,eAAK,WAAW,KAAK,IAAI;AAEzB,UAAAA,KAAI,IAAI,UAAU,KAAK;AAAA,QACzB,OAAO;AACL,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,IAAAD,aAAY,UAAU,kBAAkB,WAAY;AAClD,aAAO,KAAK,SAAS,KAAK,OAAO,cAAc,KAAK,UAAU;AAAA,IAChE;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,SAAS,QAAQ,KAAK;AACpB,MAAI,SAAS,GAAG,KAAK,IAAI,SAAS,MAAM;AACtC,WAAO,IAAI;AAAA,EACb,OAAO;AACL,WAAO,MAAM;AAAA,EACf;AACF;AACA,IAAO,sBAAQ;;;AC/ER,SAAS,YAAY,KAAK;AAC/B,MAAI,QAAQ,KAAK,IAAI,IAAI,iBAAiB,KAAK,IAAI,GAAG,CAAC,CAAC;AACxD,MAAI,IAAI,KAAK,IAAI,MAAM,KAAK;AAC5B,SAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAC3D;AACO,SAAS,qBAAqBE,QAAO;AAC1C,SAAOA,OAAM,SAAS,cAAcA,OAAM,SAAS;AACrD;AAMO,SAAS,uBAAuB,QAAQ,aAAa,aAAa,aAAa;AACpF,MAAI,SAAS,CAAC;AACd,MAAI,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,MAAI,WAAW,OAAO,WAAW,KAAK,OAAO,aAAa,IAAI;AAC9D,MAAI,eAAe,QAAQ,WAAW,aAAa;AACjD,eAAW,OAAO,WAAW;AAAA,EAC/B;AACA,MAAI,eAAe,QAAQ,WAAW,aAAa;AACjD,eAAW,OAAO,WAAW;AAAA,EAC/B;AAEA,MAAI,YAAY,OAAO,oBAAoB,qBAAqB,QAAQ;AAExE,MAAI,iBAAiB,OAAO,iBAAiB,CAAC,MAAM,KAAK,KAAK,OAAO,CAAC,IAAI,QAAQ,IAAI,UAAU,SAAS,GAAG,MAAM,KAAK,MAAM,OAAO,CAAC,IAAI,QAAQ,IAAI,UAAU,SAAS,CAAC;AACzK,YAAU,gBAAgB,MAAM;AAChC,SAAO;AACT;AACO,SAAS,iBAAiB,UAAU;AACzC,MAAI,QAAQ,KAAK,IAAI,IAAI,iBAAiB,QAAQ,CAAC;AAEnD,MAAI,IAAI,WAAW;AACnB,MAAI,CAAC,GAAG;AACN,QAAI;AAAA,EACN,WAAW,MAAM,GAAG;AAClB,QAAI;AAAA,EACN,WAAW,MAAM,GAAG;AAClB,QAAI;AAAA,EACN,OAAO;AAEL,SAAK;AAAA,EACP;AACA,SAAO,MAAM,IAAI,KAAK;AACxB;AAIO,SAAS,qBAAqB,UAAU;AAE7C,SAAO,aAAa,QAAQ,IAAI;AAClC;AACA,SAAS,MAAM,gBAAgB,KAAK,QAAQ;AAC1C,iBAAe,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,eAAe,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AACpF;AAEO,SAAS,UAAU,gBAAgB,QAAQ;AAChD,GAAC,SAAS,eAAe,CAAC,CAAC,MAAM,eAAe,CAAC,IAAI,OAAO,CAAC;AAC7D,GAAC,SAAS,eAAe,CAAC,CAAC,MAAM,eAAe,CAAC,IAAI,OAAO,CAAC;AAC7D,QAAM,gBAAgB,GAAG,MAAM;AAC/B,QAAM,gBAAgB,GAAG,MAAM;AAC/B,MAAI,eAAe,CAAC,IAAI,eAAe,CAAC,GAAG;AACzC,mBAAe,CAAC,IAAI,eAAe,CAAC;AAAA,EACtC;AACF;AACO,SAASC,SAAQ,KAAK,QAAQ;AACnC,SAAO,OAAO,OAAO,CAAC,KAAK,OAAO,OAAO,CAAC;AAC5C;AACO,SAAS,UAAU,KAAK,QAAQ;AACrC,MAAI,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,UAAQ,MAAM,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC;AAClD;AACO,SAAS,MAAM,KAAK,QAAQ;AACjC,SAAO,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,OAAO,CAAC;AACjD;;;ACpEA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,cAAa,SAAS;AAC7B,UAAI,QAAQ,OAAO,KAAK,MAAM,OAAO,KAAK;AAC1C,YAAM,OAAO;AACb,UAAI,cAAc,MAAM,WAAW,aAAa;AAGhD,UAAI,CAAC,aAAa;AAChB,sBAAc,IAAI,oBAAY,CAAC,CAAC;AAAA,MAClC;AACA,UAAI,QAAQ,WAAW,GAAG;AACxB,sBAAc,IAAI,oBAAY;AAAA,UAC5B,YAAY,IAAI,aAAa,SAAU,MAAM;AAC3C,mBAAO,SAAS,IAAI,IAAI,KAAK,QAAQ;AAAA,UACvC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,eAAe;AACrB,YAAM,UAAU,MAAM,WAAW,QAAQ,KAAK,CAAC,GAAG,YAAY,WAAW,SAAS,CAAC;AACnF,aAAO;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,QAAQ,SAAU,KAAK;AAE5C,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,aAAO,SAAS,GAAG,IAAI,KAAK,aAAa,WAAW,GAAG,IAErD,KAAK,MAAM,GAAG;AAAA,IAClB;AACA,IAAAA,cAAa,UAAU,UAAU,SAAU,MAAM;AAC/C,aAAO,KAAK,MAAM,IAAI;AACtB,aAAmBC,SAAQ,MAAM,KAAK,OAAO,KAAK,KAAK,aAAa,WAAW,IAAI,KAAK;AAAA,IAC1F;AAMA,IAAAD,cAAa,UAAU,YAAY,SAAU,KAAK;AAChD,YAAM,KAAK,eAAe,KAAK,MAAM,GAAG,CAAC;AACzC,aAAmB,UAAU,KAAK,KAAK,OAAO;AAAA,IAChD;AAKA,IAAAA,cAAa,UAAU,QAAQ,SAAU,KAAK;AAC5C,YAAM,KAAK,MAAkB,MAAM,KAAK,KAAK,OAAO,CAAC;AACrD,aAAO,KAAK,oBAAoB,GAAG;AAAA,IACrC;AACA,IAAAA,cAAa,UAAU,WAAW,WAAY;AAC5C,UAAI,QAAQ,CAAC;AACb,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,OAAO,CAAC;AACnB,aAAO,QAAQ,OAAO,CAAC,GAAG;AACxB,cAAM,KAAK;AAAA,UACT,OAAO;AAAA,QACT,CAAC;AACD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,gBAAgB,SAAU,aAAa;AAE5D;AAAA,IACF;AAIA,IAAAA,cAAa,UAAU,cAAc,SAAU,MAAM;AACnD,UAAI,QAAQ,MAAM;AAChB,aAAK,wBAAwB,KAAK,wBAAwB;AAC1D;AAAA,MACF;AACA,UAAI,qBAAqB,KAAK;AAC9B,UAAI,iBAAiB,KAAK,wBAAwB,CAAC;AACnD,UAAI,iBAAiB,KAAK,wBAAwB,CAAC;AAEnD,UAAI,UAAU;AACd,UAAI,iBAAiB,KAAK,aAAa,WAAW;AAClD,eAAS,MAAM,KAAK,IAAI,gBAAgB,mBAAmB,MAAM,GAAG,UAAU,KAAK,EAAE,SAAS;AAC5F,YAAI,gBAAgB,mBAAmB,OAAO;AAC9C,uBAAe,OAAO,IAAI;AAC1B,uBAAe,aAAa,IAAI;AAAA,MAClC;AAEA,UAAI,gBAAgB;AACpB,aAAO,UAAU,gBAAgB,EAAE,SAAS;AAC1C,eAAO,eAAe,aAAa,KAAK,MAAM;AAC5C;AAAA,QACF;AACA;AACA,uBAAe,KAAK,aAAa;AACjC,uBAAe,aAAa,IAAI;AAAA,MAClC;AAAA,IACF;AACA,IAAAA,cAAa,UAAU,iBAAiB,SAAU,SAAS;AACzD,UAAI,uBAAuB,KAAK;AAGhC,aAAO,wBAAwB,WAAW,KAAK,UAAU,qBAAqB,SAAS,qBAAqB,OAAO,IAAI;AAAA,IACzH;AAgBA,IAAAA,cAAa,UAAU,sBAAsB,SAAU,YAAY;AACjE,UAAI,uBAAuB,KAAK;AAGhC,aAAO,wBAAwB,cAAc,KAAK,aAAa,qBAAqB,SAAS,qBAAqB,UAAU,IAAI;AAAA,IAClI;AAIA,IAAAA,cAAa,UAAU,WAAW,SAAU,MAAM;AAChD,UAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,YAAI,gBAAgB,KAAK,oBAAoB,KAAK,KAAK;AACvD,YAAI,WAAW,KAAK,aAAa,WAAW,aAAa;AAGzD,eAAO,YAAY,OAAO,KAAK,WAAW;AAAA,MAC5C;AAAA,IACF;AACA,IAAAA,cAAa,UAAU,QAAQ,WAAY;AACzC,aAAO,KAAK,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;AAAA,IAC7C;AACA,IAAAA,cAAa,UAAU,sBAAsB,SAAU,MAAM,KAAK;AAChE,WAAK,YAAY,KAAK,qBAAqB,GAAG,CAAC;AAAA,IACjD;AAKA,IAAAA,cAAa,UAAU,kBAAkB,SAAU,OAAO;AACxD,cAAQ,KAAK,eAAe,KAAK;AACjC,aAAO,KAAK,QAAQ,CAAC,KAAK,SAAS,KAAK,QAAQ,CAAC,KAAK;AAAA,IACxD;AACA,IAAAA,cAAa,UAAU,iBAAiB,WAAY;AAClD,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,cAAa,UAAU,gBAAgB,WAAY;AAAA,IAAC;AACpD,IAAAA,cAAa,UAAU,iBAAiB,WAAY;AAAA,IAAC;AACrD,IAAAA,cAAa,OAAO;AACpB,WAAOA;AAAA,EACT,EAAE,aAAK;AAAA;AACP,cAAM,cAAc,YAAY;AAChC,IAAO,kBAAQ;;;ACrKf,IAAI,cAAyB;AAC7B,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUE,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACvB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAO;AAEb,YAAM,YAAY;AAClB,YAAM,qBAAqB;AAC3B,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,QAAQ,SAAU,KAAK;AAC7C,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,UAAU,SAAU,KAAK;AAC/C,aAAcC,SAAQ,KAAK,KAAK,OAAO;AAAA,IACzC;AACA,IAAAD,eAAc,UAAU,YAAY,SAAU,KAAK;AACjD,aAAc,UAAU,KAAK,KAAK,OAAO;AAAA,IAC3C;AACA,IAAAA,eAAc,UAAU,QAAQ,SAAU,KAAK;AAC7C,aAAc,MAAM,KAAK,KAAK,OAAO;AAAA,IACvC;AACA,IAAAA,eAAc,UAAU,YAAY,SAAU,OAAO,KAAK;AACxD,UAAI,aAAa,KAAK;AAEtB,UAAI,CAAC,MAAM,KAAK,GAAG;AACjB,mBAAW,CAAC,IAAI,WAAW,KAAK;AAAA,MAClC;AACA,UAAI,CAAC,MAAM,GAAG,GAAG;AACf,mBAAW,CAAC,IAAI,WAAW,GAAG;AAAA,MAChC;AAAA,IACF;AACA,IAAAA,eAAc,UAAU,cAAc,SAAU,OAAO;AACrD,UAAI,SAAS,KAAK;AAClB,YAAM,CAAC,IAAI,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,MAAM,CAAC;AAC5C,YAAM,CAAC,IAAI,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,MAAM,CAAC;AAE5C,WAAK,UAAU,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACrC;AACA,IAAAA,eAAc,UAAU,cAAc,WAAY;AAChD,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,eAAc,UAAU,cAAc,SAAU,UAAU;AACxD,WAAK,YAAY;AAGjB,WAAK,cAAc,KAAK,QAAQ,MAAM;AACtC,WAAK,qBAA4B,qBAAqB,QAAQ;AAAA,IAChE;AAIA,IAAAA,eAAc,UAAU,WAAW,SAAU,qBAAqB;AAChE,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,KAAK;AAClB,UAAI,iBAAiB,KAAK;AAC1B,UAAI,oBAAoB,KAAK;AAC7B,UAAI,QAAQ,CAAC;AAEb,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AAEA,UAAI,YAAY;AAChB,UAAI,OAAO,CAAC,IAAI,eAAe,CAAC,GAAG;AACjC,YAAI,qBAAqB;AACvB,gBAAM,KAAK;AAAA,YACT,OAAO,YAAY,eAAe,CAAC,IAAI,UAAU,iBAAiB;AAAA,UACpE,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,KAAK;AAAA,YACT,OAAO,OAAO,CAAC;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,OAAO,eAAe,CAAC;AAC3B,aAAO,QAAQ,eAAe,CAAC,GAAG;AAChC,cAAM,KAAK;AAAA,UACT,OAAO;AAAA,QACT,CAAC;AAED,eAAO,YAAY,OAAO,UAAU,iBAAiB;AACrD,YAAI,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,OAAO;AAG1C;AAAA,QACF;AACA,YAAI,MAAM,SAAS,WAAW;AAC5B,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAGA,UAAI,eAAe,MAAM,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,QAAQ,eAAe,CAAC;AAClF,UAAI,OAAO,CAAC,IAAI,cAAc;AAC5B,YAAI,qBAAqB;AACvB,gBAAM,KAAK;AAAA,YACT,OAAO,YAAY,eAAe,UAAU,iBAAiB;AAAA,UAC/D,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,KAAK;AAAA,YACT,OAAO,OAAO,CAAC;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,gBAAgB,SAAU,aAAa;AAC7D,UAAI,QAAQ,KAAK,SAAS,IAAI;AAC9B,UAAI,aAAa,CAAC;AAClB,UAAI,SAAS,KAAK,UAAU;AAC5B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,WAAW,MAAM,CAAC;AACtB,YAAI,WAAW,MAAM,IAAI,CAAC;AAC1B,YAAI,QAAQ;AACZ,YAAI,kBAAkB,CAAC;AACvB,YAAI,WAAW,SAAS,QAAQ,SAAS;AACzC,YAAI,gBAAgB,WAAW;AAC/B,eAAO,QAAQ,cAAc,GAAG;AAC9B,cAAI,YAAY,YAAY,SAAS,SAAS,QAAQ,KAAK,aAAa;AAExE,cAAI,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AAClD,4BAAgB,KAAK,SAAS;AAAA,UAChC;AACA;AAAA,QACF;AACA,mBAAW,KAAK,eAAe;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AAKA,IAAAA,eAAc,UAAU,WAAW,SAAU,MAAM,KAAK;AACtD,UAAI,QAAQ,MAAM;AAChB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,OAAO,IAAI;AAC3B,UAAI,aAAa,MAAM;AACrB,oBAAuB,aAAa,KAAK,KAAK,KAAK;AAAA,MACrD,WAAW,cAAc,QAAQ;AAE/B,oBAAY,KAAK;AAAA,MACnB;AAGA,UAAI,UAAU,YAAY,KAAK,OAAO,WAAW,IAAI;AACrD,aAAkB,UAAU,OAAO;AAAA,IACrC;AAIA,IAAAA,eAAc,UAAU,gBAAgB,SAAU,aAAa,aAAa,aAAa;AACvF,oBAAc,eAAe;AAC7B,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,UAAI,CAAC,SAAS,IAAI,GAAG;AACnB;AAAA,MACF;AAGA,UAAI,OAAO,GAAG;AACZ,eAAO,CAAC;AACR,eAAO,QAAQ;AAAA,MACjB;AACA,UAAI,SAAgB,uBAAuB,QAAQ,aAAa,aAAa,WAAW;AACxF,WAAK,qBAAqB,OAAO;AACjC,WAAK,YAAY,OAAO;AACxB,WAAK,cAAc,OAAO;AAAA,IAC5B;AACA,IAAAA,eAAc,UAAU,iBAAiB,SAAU,KAAK;AACtD,UAAI,SAAS,KAAK;AAElB,UAAI,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG;AAC3B,YAAI,OAAO,CAAC,MAAM,GAAG;AAGnB,cAAI,aAAa,KAAK,IAAI,OAAO,CAAC,CAAC;AAMnC,cAAI,CAAC,IAAI,QAAQ;AACf,mBAAO,CAAC,KAAK,aAAa;AAC1B,mBAAO,CAAC,KAAK,aAAa;AAAA,UAC5B,OAAO;AACL,mBAAO,CAAC,KAAK,aAAa;AAAA,UAC5B;AAAA,QACF,OAAO;AACL,iBAAO,CAAC,IAAI;AAAA,QACd;AAAA,MACF;AACA,UAAI,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAE/B,UAAI,CAAC,SAAS,IAAI,GAAG;AACnB,eAAO,CAAC,IAAI;AACZ,eAAO,CAAC,IAAI;AAAA,MACd;AACA,WAAK,cAAc,IAAI,aAAa,IAAI,aAAa,IAAI,WAAW;AAEpE,UAAI,WAAW,KAAK;AACpB,UAAI,CAAC,IAAI,QAAQ;AACf,eAAO,CAAC,IAAI,YAAY,KAAK,MAAM,OAAO,CAAC,IAAI,QAAQ,IAAI,QAAQ;AAAA,MACrE;AACA,UAAI,CAAC,IAAI,QAAQ;AACf,eAAO,CAAC,IAAI,YAAY,KAAK,KAAK,OAAO,CAAC,IAAI,QAAQ,IAAI,QAAQ;AAAA,MACpE;AAAA,IACF;AACA,IAAAA,eAAc,UAAU,gBAAgB,SAAUE,MAAKC,MAAK;AAC1D,WAAK,cAAc,CAACD,MAAKC,IAAG;AAAA,IAC9B;AACA,IAAAH,eAAc,OAAO;AACrB,WAAOA;AAAA,EACT,EAAE,aAAK;AAAA;AACP,cAAM,cAAc,aAAa;AACjC,IAAO,mBAAQ;;;ACzMR,SAAS,gBAAgB,aAAa,iBAAiB,KAAK;AACjE,QAAM,OAAO,CAAC;AACd,MAAI,UAAU,IAAI;AAClB,MAAI,wBAAwB,IAAI;AAChC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,wBAAwB,eAAe,GAAG;AAC5C,0BAAsB;AAAA,EACxB,OAAO;AACL,aAAS,gBAAgB;AACzB,0BAAsB,OAAO;AAC7B,YAAQ,gBAAgB;AAAA,EAC1B;AAEA,MAAI,WAAW,CAAC,EAAE,eAAe,YAAY,IAAI,OAAO;AACxD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,OAAK,qBAAqB,SAAU,eAAe,OAAO;AACxD,QAAI,SAAS,aAAa,GAAG;AAC3B,0BAAoB,KAAK,IAAI,gBAAgB;AAAA,QAC3C,MAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,YAAY,CAAC,cAAc,cAAc;AAE3C,UAAI,CAAC,WAAW,CAAC,oBAAoB,cAAc,aAAa;AAC9D,2BAAmB;AAAA,MACrB;AAEA,UAAI,CAAC,kBAAkB,cAAc,SAAS,aAAa,cAAc,SAAS,WAAW,CAAC,yBAAyB,0BAA0B,cAAc,WAAW;AACxK,yBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB,CAAC,WAAW,CAAC,kBAAkB;AAGnD,cAAU;AAAA,EACZ;AAIA,MAAI,gBAAgB;AAIlB,2BAAuB,uBAAuB,YAAY;AAC1D,2BAAuB,uBAAuB,YAAY;AAE1D,QAAI,kBAAkB;AACpB,uBAAiB,wBAAwB;AAAA,IAC3C;AACA,QAAI,uBAAuB,eAAe;AAC1C,QAAI,iBAAiB,eAAe;AACpC,QAAI,yBAAyB;AAC7B,SAAK,qBAAqB,SAAU,eAAe;AACjD,UAAI,cAAc,aAAa,sBAAsB;AACnD;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,6BAA6B;AAAA,MAC/B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,eAAe;AAAA,MACf,MAAM;AAAA,MACN,cAAc;AAAA,MACd,oBAAoB;AAAA,MACpB,eAAe,oBAAoB;AAAA,IACrC;AACA,QAAI,6BAA6B;AAAA,MAC/B,MAAM;AAAA;AAAA;AAAA,MAGN,UAAU;AAAA,MACV,eAAe,yBAAyB;AAAA,MACxC,MAAM;AAAA,MACN,cAAc;AAAA,MACd,oBAAoB;AAAA,MACpB,eAAe,oBAAoB,SAAS;AAAA,IAC9C;AACA,QAAI,QAAQ;AACV,UAAI,OAAO;AACT,mCAA2B,gBAAgB,MAAM,2BAA2B,sBAAsB,cAAc;AAChH,mCAA2B,gBAAgB,MAAM,2BAA2B,sBAAsB,cAAc;AAAA,MAClH;AACA,aAAO,2BAA2B,0BAA0B;AAC5D,aAAO,2BAA2B,0BAA0B;AAAA,IAC9D,OAAO;AACL,0BAAoB,KAAK,0BAA0B;AACnD,0BAAoB,KAAK,0BAA0B;AAAA,IACrD;AAAA,EACF;AACA,SAAO;AAAA,IACL,kBAAkB,kBAAkB,eAAe;AAAA,IACnD,oBAAoB,oBAAoB,iBAAiB;AAAA,IACzD,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,wBAAwB,iBAAiB;AAChD,SAAO,CAAC,mBAAmB,gBAAgB,MAAM;AACnD;AACO,SAAS,mBAAmB,MAAM,YAAY;AAGnD,SAAO,CAAC,CAAC,cAAc,eAAe,KAAK,mBAAmB,kBAAkB;AAClF;AACO,SAAS,oBAAoB,MAAM,WAAW;AACnD,SAAO,mBAAmB,MAAM,SAAS,IAAI,KAAK,mBAAmB,sBAAsB,IAAI;AACjG;;;ACrIA,IAAI,sBAAsB,OAAO,iBAAiB;AAClD,IAAI,mBAAmB,CAAC,sBAAsB,QAAQ;AAC/C,SAAS,mBAAmB,KAAK;AACtC,MAAI,QAAQ,GAAG,GAAG;AAEhB,WAAO,sBAAsB,IAAI,aAAa,GAAG,IAAI;AAAA,EACvD;AAEA,SAAO,IAAI,iBAAiB,GAAG;AACjC;;;ACNA,IAAI,eAAe;AACnB,SAAS,iBAAiB,aAAa;AACrC,SAAO,YAAY,IAAI,OAAO,KAAK,eAAe,YAAY;AAChE;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,KAAK,MAAM,KAAK;AACzB;AAIO,SAAS,gBAAgB,KAAK;AACnC,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,IAAI;AACnB,MAAI,UAAU;AACd,MAAI,SAAS,SAAS,YAAY;AAChC;AAAA,EACF;AACA,MAAI,YAAY,SAAS,aAAa;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,KAAK;AACvC,WAAO,KAAK,SAAS;AAAA,MACnB;AAAA,MACA;AAAA,MACA,SAAS,eAAe;AAAA,IAC1B,GAAG,GAAG,CAAC;AAAA,EACT;AACA,MAAI,kBAAkB,uBAAuB,MAAM;AACnD,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,IAAI,OAAO,KAAK;AAClC,QAAI,OAAO,gBAAgB,OAAO,EAAE,eAAe,CAAC;AACpD,SAAK,eAAe,KAAK,SAAS,KAAK,QAAQ;AAC/C,WAAO,KAAK,IAAI;AAAA,EAClB;AACA,SAAO;AACT;AACO,SAAS,uBAAuB,YAAY,SAAS;AAC1D,MAAI,eAAe,CAAC;AACpB,UAAQ,iBAAiB,YAAY,SAAU,aAAa;AAE1D,QAAI,cAAc,WAAW,GAAG;AAC9B,mBAAa,KAAK,WAAW;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AASA,SAAS,oBAAoB,WAAW;AAQtC,MAAI,aAAa,CAAC;AAClB,OAAK,WAAW,SAAU,aAAa;AACrC,QAAI,YAAY,YAAY;AAC5B,QAAI,WAAW,UAAU,YAAY;AACrC,QAAI,SAAS,SAAS,UAAU,SAAS,SAAS,SAAS;AACzD;AAAA,IACF;AACA,QAAI,OAAO,YAAY,QAAQ;AAC/B,QAAII,OAAM,SAAS,MAAM,MAAM,SAAS;AACxC,QAAI,SAAS,KAAK,kBAAkB,KAAK,aAAa,SAAS,GAAG,CAAC;AACnE,QAAI,QAAQ,KAAK,SAAS;AAC1B,aAAS,IAAI,GAAG,MAAM,MAAM,MAAM,GAAG,IAAI,KAAK,EAAE,GAAG;AACjD,UAAI,QAAQ,MAAM,IAAI,QAAQ,CAAC;AAC/B,UAAI,CAAC,WAAWA,IAAG,GAAG;AAEpB,mBAAWA,IAAG,IAAI,CAAC,KAAK;AAAA,MAC1B,OAAO;AAEL,mBAAWA,IAAG,EAAE,KAAK,KAAK;AAAA,MAC5B;AAAA,IAEF;AAAA,EACF,CAAC;AACD,MAAI,cAAc,CAAC;AACnB,WAAS,OAAO,YAAY;AAC1B,QAAI,WAAW,eAAe,GAAG,GAAG;AAClC,UAAI,eAAe,WAAW,GAAG;AACjC,UAAI,cAAc;AAEhB,qBAAa,KAAK,SAAU,GAAG,GAAG;AAChC,iBAAO,IAAI;AAAA,QACb,CAAC;AACD,YAAIC,OAAM;AACV,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,cAAI,QAAQ,aAAa,CAAC,IAAI,aAAa,IAAI,CAAC;AAChD,cAAI,QAAQ,GAAG;AAEb,YAAAA,OAAMA,SAAQ,OAAO,QAAQ,KAAK,IAAIA,MAAK,KAAK;AAAA,UAClD;AAAA,QACF;AAEA,oBAAY,GAAG,IAAIA;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,iBAAiB,WAAW;AAC1C,MAAI,cAAc,oBAAoB,SAAS;AAC/C,MAAI,iBAAiB,CAAC;AACtB,OAAK,WAAW,SAAU,aAAa;AACrC,QAAI,YAAY,YAAY;AAC5B,QAAI,WAAW,UAAU,YAAY;AACrC,QAAI,aAAa,SAAS,UAAU;AACpC,QAAI;AACJ,QAAI,SAAS,SAAS,YAAY;AAChC,kBAAY,SAAS,aAAa;AAAA,IACpC,WAAW,SAAS,SAAS,WAAW,SAAS,SAAS,QAAQ;AAChE,UAAI,MAAM,SAAS,MAAM,MAAM,SAAS;AACxC,UAAI,SAAS,YAAY,GAAG;AAC5B,UAAI,aAAa,KAAK,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,CAAC;AACvD,UAAIC,SAAQ,SAAS,MAAM,UAAU;AACrC,UAAI,YAAY,KAAK,IAAIA,OAAM,CAAC,IAAIA,OAAM,CAAC,CAAC;AAC5C,kBAAY,SAAS,aAAa,YAAY,SAAS;AAAA,IACzD,OAAO;AACL,UAAI,OAAO,YAAY,QAAQ;AAC/B,kBAAY,KAAK,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,CAAC,IAAI,KAAK,MAAM;AAAA,IACnE;AACA,QAAI,WAAWC,cAAa,YAAY,IAAI,UAAU,GAAG,SAAS;AAClE,QAAI,cAAcA,cAAa,YAAY,IAAI,aAAa,GAAG,SAAS;AACxE,QAAI,cAAcA;AAAA;AAAA;AAAA,MAGlB,YAAY,IAAI,aAAa,MAAM,cAAc,WAAW,IAAI,MAAM;AAAA,MAAI;AAAA,IAAS;AACnF,QAAI,SAAS,YAAY,IAAI,QAAQ;AACrC,QAAI,iBAAiB,YAAY,IAAI,gBAAgB;AACrD,mBAAe,KAAK;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,WAAW,QAAQ;AAAA,MAC5B,SAAS,iBAAiB,WAAW;AAAA,IACvC,CAAC;AAAA,EACH,CAAC;AACD,SAAO,uBAAuB,cAAc;AAC9C;AACA,SAAS,uBAAuB,gBAAgB;AAE9C,MAAI,aAAa,CAAC;AAClB,OAAK,gBAAgB,SAAU,YAAY,KAAK;AAC9C,QAAI,UAAU,WAAW;AACzB,QAAI,YAAY,WAAW;AAC3B,QAAI,gBAAgB,WAAW,OAAO,KAAK;AAAA,MACzC;AAAA,MACA,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,KAAK;AAAA,MACL,QAAQ,CAAC;AAAA,IACX;AACA,QAAI,SAAS,cAAc;AAC3B,eAAW,OAAO,IAAI;AACtB,QAAI,UAAU,WAAW;AACzB,QAAI,CAAC,OAAO,OAAO,GAAG;AACpB,oBAAc;AAAA,IAChB;AACA,WAAO,OAAO,IAAI,OAAO,OAAO,KAAK;AAAA,MACnC,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAKA,QAAI,WAAW,WAAW;AAC1B,QAAI,YAAY,CAAC,OAAO,OAAO,EAAE,OAAO;AAEtC,aAAO,OAAO,EAAE,QAAQ;AACxB,iBAAW,KAAK,IAAI,cAAc,eAAe,QAAQ;AACzD,oBAAc,iBAAiB;AAAA,IACjC;AACA,QAAI,cAAc,WAAW;AAC7B,oBAAgB,OAAO,OAAO,EAAE,WAAW;AAC3C,QAAI,cAAc,WAAW;AAC7B,oBAAgB,OAAO,OAAO,EAAE,WAAW;AAC3C,QAAI,SAAS,WAAW;AACxB,cAAU,SAAS,cAAc,MAAM;AACvC,QAAI,iBAAiB,WAAW;AAChC,sBAAkB,SAAS,cAAc,cAAc;AAAA,EACzD,CAAC;AACD,MAAI,SAAS,CAAC;AACd,OAAK,YAAY,SAAU,eAAe,cAAc;AACtD,WAAO,YAAY,IAAI,CAAC;AACxB,QAAI,SAAS,cAAc;AAC3B,QAAI,YAAY,cAAc;AAC9B,QAAI,qBAAqB,cAAc;AACvC,QAAI,sBAAsB,MAAM;AAC9B,UAAI,cAAc,KAAK,MAAM,EAAE;AAG/B,2BAAqB,KAAK,IAAI,KAAK,cAAc,GAAG,EAAE,IAAI;AAAA,IAC5D;AACA,QAAI,cAAcA,cAAa,oBAAoB,SAAS;AAC5D,QAAI,gBAAgBA,cAAa,cAAc,KAAK,CAAC;AACrD,QAAI,gBAAgB,cAAc;AAClC,QAAI,iBAAiB,cAAc;AACnC,QAAI,aAAa,gBAAgB,gBAAgB,kBAAkB,iBAAiB,KAAK;AACzF,gBAAY,KAAK,IAAI,WAAW,CAAC;AAEjC,SAAK,QAAQ,SAAU,QAAQ;AAC7B,UAAI,WAAW,OAAO;AACtB,UAAI,WAAW,OAAO;AACtB,UAAI,CAAC,OAAO,OAAO;AACjB,YAAI,aAAa;AACjB,YAAI,YAAY,WAAW,YAAY;AACrC,uBAAa,KAAK,IAAI,UAAU,aAAa;AAAA,QAC/C;AAMA,YAAI,YAAY,WAAW,YAAY;AACrC,uBAAa;AAAA,QACf;AACA,YAAI,eAAe,WAAW;AAC5B,iBAAO,QAAQ;AACf,2BAAiB,aAAa,gBAAgB;AAC9C;AAAA,QACF;AAAA,MACF,OAAO;AAIL,YAAI,aAAa,OAAO;AACxB,YAAI,UAAU;AACZ,uBAAa,KAAK,IAAI,YAAY,QAAQ;AAAA,QAC5C;AAEA,YAAI,UAAU;AACZ,uBAAa,KAAK,IAAI,YAAY,QAAQ;AAAA,QAC5C;AACA,eAAO,QAAQ;AACf,yBAAiB,aAAa,gBAAgB;AAC9C;AAAA,MACF;AAAA,IACF,CAAC;AAED,iBAAa,gBAAgB,gBAAgB,kBAAkB,iBAAiB,KAAK;AACrF,gBAAY,KAAK,IAAI,WAAW,CAAC;AACjC,QAAI,WAAW;AACf,QAAI;AACJ,SAAK,QAAQ,SAAU,QAAQ,KAAK;AAClC,UAAI,CAAC,OAAO,OAAO;AACjB,eAAO,QAAQ;AAAA,MACjB;AACA,mBAAa;AACb,kBAAY,OAAO,SAAS,IAAI;AAAA,IAClC,CAAC;AACD,QAAI,YAAY;AACd,kBAAY,WAAW,QAAQ;AAAA,IACjC;AACA,QAAI,SAAS,CAAC,WAAW;AACzB,SAAK,QAAQ,SAAU,QAAQ,SAAS;AACtC,aAAO,YAAY,EAAE,OAAO,IAAI,OAAO,YAAY,EAAE,OAAO,KAAK;AAAA,QAC/D;AAAA,QACA;AAAA,QACA,OAAO,OAAO;AAAA,MAChB;AACA,gBAAU,OAAO,SAAS,IAAI;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AACA,SAAS,qBAAqB,mBAAmB,MAAM,aAAa;AAClE,MAAI,qBAAqB,MAAM;AAC7B,QAAI,SAAS,kBAAkB,WAAW,IAAI,CAAC;AAC/C,QAAI,UAAU,QAAQ,eAAe,MAAM;AACzC,aAAO,OAAO,iBAAiB,WAAW,CAAC;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,OAAO,YAAY,SAAS;AAC1C,MAAI,eAAe,uBAAuB,YAAY,OAAO;AAC7D,MAAI,oBAAoB,iBAAiB,YAAY;AACrD,OAAK,cAAc,SAAU,aAAa;AACxC,QAAI,OAAO,YAAY,QAAQ;AAC/B,QAAI,YAAY,YAAY;AAC5B,QAAI,WAAW,UAAU,YAAY;AACrC,QAAI,UAAU,iBAAiB,WAAW;AAC1C,QAAI,mBAAmB,kBAAkB,WAAW,QAAQ,CAAC,EAAE,OAAO;AACtE,QAAI,eAAe,iBAAiB;AACpC,QAAI,cAAc,iBAAiB;AACnC,SAAK,UAAU;AAAA,MACb,WAAW,iBAAiB;AAAA,MAC5B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AAEO,SAAS,wBAAwB,YAAY;AAClD,SAAO;AAAA,IACL;AAAA,IACA,MAAM,oBAAoB;AAAA,IAC1B,OAAO,SAAU,aAAa;AAC5B,UAAI,CAAC,cAAc,WAAW,GAAG;AAC/B;AAAA,MACF;AACA,UAAI,OAAO,YAAY,QAAQ;AAC/B,UAAI,YAAY,YAAY;AAC5B,UAAI,WAAW,UAAU,YAAY;AACrC,UAAI,YAAY,UAAU,aAAa,QAAQ;AAC/C,UAAI,cAAc,KAAK,kBAAkB,KAAK,aAAa,UAAU,GAAG,CAAC;AACzE,UAAI,aAAa,KAAK,kBAAkB,KAAK,aAAa,SAAS,GAAG,CAAC;AACvE,UAAI,iBAAiB,YAAY,IAAI,kBAAkB,IAAI;AAC3D,UAAI,WAAW,KAAK,aAAa,UAAU,GAAG;AAC9C,UAAI,iBAAiB,KAAK,mBAAmB,sBAAsB;AACnE,UAAI,UAAU,mBAAmB,MAAM,QAAQ,KAAK,CAAC,CAAC,KAAK,mBAAmB,iBAAiB;AAC/F,UAAI,eAAe,UAAU,aAAa;AAC1C,UAAI,iBAAiB,kBAAkB,UAAU,SAAS;AAC1D,UAAI,UAAU,cAAc,WAAW;AACvC,UAAI,eAAe,YAAY,IAAI,cAAc,KAAK;AACtD,UAAI,gBAAgB,kBAAkB,KAAK,kBAAkB,cAAc;AAE3E,UAAI,cAAc,KAAK,UAAU,MAAM;AACvC,UAAI,eAAe,KAAK,UAAU,QAAQ;AAC1C,aAAO;AAAA,QACL,UAAU,SAAU,QAAQC,OAAM;AAChC,cAAI,QAAQ,OAAO;AACnB,cAAI,cAAc,WAAW,mBAAmB,QAAQ,CAAC;AACzD,cAAI,wBAAwB,WAAW,kBAAkB,mBAAmB,QAAQ,CAAC;AACrF,cAAI,mBAAmB,WAAW,mBAAmB,KAAK;AAC1D,cAAI,cAAc,UAAU,OAAO,QAAQ;AAC3C,cAAI,SAAS,eAAe,YAAY,QAAQ,YAAY;AAC5D,cAAI;AACJ,cAAI,QAAQA,MAAK,SAAS;AAC1B,cAAI,YAAY;AAChB,kBAAQ,YAAY,OAAO,KAAK,MAAM,MAAM;AAC1C,gBAAI,QAAQ,MAAM,IAAI,UAAU,gBAAgB,aAAa,SAAS;AACtE,gBAAI,YAAY,MAAM,IAAI,YAAY,SAAS;AAC/C,gBAAI,YAAY;AAChB,gBAAI,kBAAkB;AAGtB,gBAAI,SAAS;AACX,gCAAkB,CAAC,QAAQ,MAAM,IAAI,aAAa,SAAS;AAAA,YAC7D;AACA,gBAAI,IAAI;AACR,gBAAI,IAAI;AACR,gBAAI,QAAQ;AACZ,gBAAI,SAAS;AACb,gBAAI,cAAc;AAChB,kBAAI,QAAQ,UAAU,YAAY,CAAC,OAAO,SAAS,CAAC;AACpD,kBAAI,SAAS;AACX,oBAAI,aAAa,UAAU,YAAY,CAAC,iBAAiB,SAAS,CAAC;AACnE,4BAAY,WAAW,CAAC;AAAA,cAC1B;AACA,kBAAI;AACJ,kBAAI,MAAM,CAAC,IAAI;AACf,sBAAQ,MAAM,CAAC,IAAI;AACnB,uBAAS;AACT,kBAAI,KAAK,IAAI,KAAK,IAAI,cAAc;AAClC,yBAAS,QAAQ,IAAI,KAAK,KAAK;AAAA,cACjC;AAAA,YACF,OAAO;AACL,kBAAI,QAAQ,UAAU,YAAY,CAAC,WAAW,KAAK,CAAC;AACpD,kBAAI,SAAS;AACX,oBAAI,aAAa,UAAU,YAAY,CAAC,WAAW,eAAe,CAAC;AACnE,4BAAY,WAAW,CAAC;AAAA,cAC1B;AACA,kBAAI,MAAM,CAAC,IAAI;AACf,kBAAI;AACJ,sBAAQ;AACR,uBAAS,MAAM,CAAC,IAAI;AACpB,kBAAI,KAAK,IAAI,MAAM,IAAI,cAAc;AAEnC,0BAAU,UAAU,IAAI,KAAK,KAAK;AAAA,cACpC;AAAA,YACF;AACA,gBAAI,CAAC,SAAS;AACZ,cAAAA,MAAK,cAAc,WAAW;AAAA,gBAC5B;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH,OAAO;AACL,0BAAY,SAAS,IAAI;AACzB,0BAAY,YAAY,CAAC,IAAI;AAC7B,0BAAY,YAAY,CAAC,IAAI,eAAe,QAAQ;AACpD,kBAAI,uBAAuB;AACzB,sCAAsB,SAAS,IAAI,eAAe,YAAY,IAAI;AAClE,sCAAsB,YAAY,CAAC,IAAI,eAAe,IAAI,YAAY;AACtE,sCAAsB,YAAY,CAAC,IAAI;AAAA,cACzC;AACA,+BAAiB,SAAS,IAAI;AAAA,YAChC;AACA,yBAAa;AAAA,UACf;AACA,cAAI,SAAS;AACX,YAAAA,MAAK,UAAU;AAAA,cACb;AAAA,cACA;AAAA,cACA;AAAA,cACA,qBAAqB;AAAA,YACvB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,cAAc,aAAa;AAClC,SAAO,YAAY,oBAAoB,YAAY,iBAAiB,SAAS;AAC/E;AACA,SAAS,cAAc,aAAa;AAClC,SAAO,YAAY,mBAAmB,YAAY,gBAAgB;AACpE;AAEA,SAAS,kBAAkB,UAAU,WAAW;AAC9C,MAAI,aAAa,UAAU,MAAM,IAAI,YAAY;AACjD,MAAI,CAAC,YAAY;AACf,iBAAa;AAAA,EACf;AACA,SAAO,UAAU,cAAc,UAAU,YAAY,UAAU,SAAS,QAAQ,aAAa,IAAI,aAAa,IAAI,UAAU,CAAC;AAC/H;;;ACzZA,IAAI,SAAS,SAAU,GAAG,GAAG,IAAI,IAAI;AACnC,SAAO,KAAK,IAAI;AACd,QAAI,MAAM,KAAK,OAAO;AACtB,QAAI,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG;AACjB,WAAK,MAAM;AAAA,IACb,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI;AAAA;AAAA,EAAyB,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,WAAU,UAAU;AAC3B,UAAI,QAAQ,OAAO,KAAK,MAAM,QAAQ,KAAK;AAC3C,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AAIA,IAAAA,WAAU,UAAU,WAAW,SAAU,MAAM;AAC7C,UAAI,SAAS,KAAK,WAAW,QAAQ;AACrC,aAAO,OAAO,KAAK,OAAO,qBAAqB,oCAAoC,mBAAmB,KAAK,aAAa,CAAC,CAAC,KAAK,qBAAqB,QAAQ,QAAQ,KAAK,WAAW,QAAQ,CAAC;AAAA,IAC/L;AACA,IAAAA,WAAU,UAAU,oBAAoB,SAAU,MAAM,KAAK,gBAAgB;AAC3E,UAAI,QAAQ,KAAK,WAAW,QAAQ;AACpC,UAAI,OAAO,KAAK,WAAW,QAAQ;AACnC,aAAO,cAAc,MAAM,KAAK,gBAAgB,MAAM,KAAK;AAAA,IAC7D;AAIA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACzC,UAAI,WAAW,KAAK;AACpB,UAAI,SAAS,KAAK;AAClB,UAAI,QAAQ,CAAC;AAEb,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AACA,YAAM,KAAK;AAAA,QACT,OAAO,OAAO,CAAC;AAAA,QACf,OAAO;AAAA,MACT,CAAC;AACD,UAAI,SAAS,KAAK,WAAW,QAAQ;AACrC,UAAI,aAAa,iBAAiB,KAAK,eAAe,KAAK,iBAAiB,QAAQ,MAAM;AAC1F,cAAQ,MAAM,OAAO,UAAU;AAC/B,YAAM,KAAK;AAAA,QACT,OAAO,OAAO,CAAC;AAAA,QACf,OAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,iBAAiB,SAAU,KAAK;AAClD,UAAI,SAAS,KAAK;AAElB,UAAI,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG;AAE3B,eAAO,CAAC,KAAK;AACb,eAAO,CAAC,KAAK;AAAA,MACf;AAEA,UAAI,OAAO,CAAC,MAAM,aAAa,OAAO,CAAC,MAAM,UAAU;AACrD,YAAI,IAAI,oBAAI,KAAK;AACjB,eAAO,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,YAAY,GAAG,EAAE,SAAS,GAAG,EAAE,QAAQ,CAAC;AAChE,eAAO,CAAC,IAAI,OAAO,CAAC,IAAI;AAAA,MAC1B;AACA,WAAK,cAAc,IAAI,aAAa,IAAI,aAAa,IAAI,WAAW;AAAA,IACtE;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,eAAe,aAAa,aAAa;AACrF,sBAAgB,iBAAiB;AACjC,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,WAAK,kBAAkB,OAAO;AAC9B,UAAI,eAAe,QAAQ,KAAK,kBAAkB,aAAa;AAC7D,aAAK,kBAAkB;AAAA,MACzB;AACA,UAAI,eAAe,QAAQ,KAAK,kBAAkB,aAAa;AAC7D,aAAK,kBAAkB;AAAA,MACzB;AACA,UAAI,oBAAoB,eAAe;AACvC,UAAI,MAAM,KAAK,IAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG,iBAAiB,GAAG,oBAAoB,CAAC;AAE5G,WAAK,YAAY,eAAe,GAAG,EAAE,CAAC;AAGtC,WAAK,gBAAgB,eAAe,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;AAAA,IAC7D;AACA,IAAAA,WAAU,UAAU,QAAQ,SAAU,KAAK;AAEzC,aAAO,SAAS,GAAG,IAAI,MAAM,CAAY,UAAU,GAAG;AAAA,IACxD;AACA,IAAAA,WAAU,UAAU,UAAU,SAAU,KAAK;AAC3C,aAAmBC,SAAQ,KAAK,MAAM,GAAG,GAAG,KAAK,OAAO;AAAA,IAC1D;AACA,IAAAD,WAAU,UAAU,YAAY,SAAU,KAAK;AAC7C,aAAmB,UAAU,KAAK,MAAM,GAAG,GAAG,KAAK,OAAO;AAAA,IAC5D;AACA,IAAAA,WAAU,UAAU,QAAQ,SAAU,KAAK;AACzC,aAAmB,MAAM,KAAK,KAAK,OAAO;AAAA,IAC5C;AACA,IAAAA,WAAU,OAAO;AACjB,WAAOA;AAAA,EACT,EAAE,gBAAa;AAAA;AAOf,IAAI,iBAAiB;AAAA;AAAA,EAErB,CAAC,UAAU,UAAU;AAAA,EAAG,CAAC,UAAU,UAAU;AAAA,EAAG,CAAC,QAAQ,QAAQ;AAAA,EAAG,CAAC,eAAe,WAAW,CAAC;AAAA,EAAG,CAAC,YAAY,WAAW,EAAE;AAAA,EAAG,CAAC,OAAO,UAAU,GAAG;AAAA,EAAG,CAAC,aAAa,UAAU,GAAG;AAAA,EAAG,CAAC,QAAQ,UAAU,CAAC;AAAA,EAAG,CAAC,SAAS,UAAU,EAAE;AAAA,EAAG,CAAC,WAAW,UAAU,EAAE;AAAA,EAAG,CAAC,aAAa,WAAW,CAAC;AAAA,EAAG,CAAC,QAAQ,QAAQ;AAAA;AAC/S;AACA,SAAS,gBAAgB,MAAM,QAAQ,QAAQ,OAAO;AACpD,MAAI,QAAmB,UAAU,MAAM;AACvC,MAAI,QAAmB,UAAU,MAAM;AACvC,MAAI,SAAS,SAAUE,OAAM;AAC3B,WAAO,aAAa,OAAOA,OAAM,KAAK,MAAM,aAAa,OAAOA,OAAM,KAAK;AAAA,EAC7E;AACA,MAAI,aAAa,WAAY;AAC3B,WAAO,OAAO,MAAM;AAAA,EACtB;AAGA,MAAI,cAAc,WAAY;AAC5B,WAAO,WAAW,KAAK,OAAO,OAAO;AAAA,EACvC;AACA,MAAI,YAAY,WAAY;AAC1B,WAAO,YAAY,KAAK,OAAO,KAAK;AAAA,EACtC;AAEA,MAAI,aAAa,WAAY;AAC3B,WAAO,UAAU,KAAK,OAAO,MAAM;AAAA,EACrC;AACA,MAAI,eAAe,WAAY;AAC7B,WAAO,WAAW,KAAK,OAAO,QAAQ;AAAA,EACxC;AACA,MAAI,eAAe,WAAY;AAC7B,WAAO,aAAa,KAAK,OAAO,QAAQ;AAAA,EAC1C;AACA,MAAI,oBAAoB,WAAY;AAClC,WAAO,aAAa,KAAK,OAAO,aAAa;AAAA,EAC/C;AACA,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,WAAW;AAAA,IACpB,KAAK;AACH,aAAO,YAAY;AAAA,IACrB,KAAK;AACH,aAAO,UAAU;AAAA,IACnB,KAAK;AACH,aAAO,WAAW;AAAA,IACpB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,kBAAkB;AAAA,EAC7B;AACF;AAiDA,SAAS,gBAAgB,gBAAgB,aAAa;AACpD,oBAAkB;AAClB,SAAO,iBAAiB,KAAK,KAE3B,iBAAiB,MAAM,IACvB,iBAAiB,MAAM,IAAI,iBAAiB,MAAM,IAAI;AAC1D;AACA,SAAS,iBAAiB,gBAAgB;AACxC,MAAI,mBAAmB,KAAK;AAC5B,oBAAkB;AAClB,SAAO,iBAAiB,IAAI,IAAI,iBAAiB,IAAI,IAAI,iBAAiB,IAAI,IAAI;AACpF;AACA,SAAS,gBAAgB,gBAAgB;AACvC,oBAAkB;AAClB,SAAO,iBAAiB,KAAK,KAAK,iBAAiB,IAAI,IAAI,iBAAiB,MAAM,IAAI,iBAAiB,IAAI,IAAI;AACjH;AACA,SAAS,6BAA6B,gBAAgB,WAAW;AAC/D,oBAAkB,YAAY,aAAa;AAC3C,SAAO,iBAAiB,KAAK,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,IAAI,IAAI,iBAAiB,IAAI,IAAI;AACvK;AACA,SAAS,wBAAwB,gBAAgB;AAC/C,SAAkB,KAAK,gBAAgB,IAAI;AAC7C;AACA,SAAS,wBAAwB,MAAM,UAAU,OAAO;AACtD,MAAI,UAAU,IAAI,KAAK,IAAI;AAC3B,UAAQ,mBAAmB,QAAQ,GAAG;AAAA,IACpC,KAAK;AAAA,IACL,KAAK;AACH,cAAQ,gBAAgB,KAAK,CAAC,EAAE,CAAC;AAAA,IACnC,KAAK;AACH,cAAQ,eAAe,KAAK,CAAC,EAAE,CAAC;AAAA,IAClC,KAAK;AACH,cAAQ,gBAAgB,KAAK,CAAC,EAAE,CAAC;AAAA,IACnC,KAAK;AACH,cAAQ,kBAAkB,KAAK,CAAC,EAAE,CAAC;AAAA,IACrC,KAAK;AACH,cAAQ,kBAAkB,KAAK,CAAC,EAAE,CAAC;AACnC,cAAQ,uBAAuB,KAAK,CAAC,EAAE,CAAC;AAAA,EAC5C;AACA,SAAO,QAAQ,QAAQ;AACzB;AACA,SAAS,iBAAiB,gBAAgB,gBAAgB,OAAO,QAAQ;AACvE,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI,OAAO;AACX,WAAS,eAAe,UAAU,cAAc,cAAc,eAAe,eAAe,QAAQ,KAAK;AACvG,QAAI,OAAO,IAAI,KAAK,YAAY;AAChC,QAAI,WAAW;AACf,QAAI,IAAI,KAAK,aAAa,EAAE;AAI5B,WAAO,WAAW,gBAAgB,YAAY,OAAO,CAAC,GAAG;AACvD,UAAI,KAAK;AAAA,QACP,OAAO;AAAA,MACT,CAAC;AACD,WAAK;AACL,WAAK,aAAa,EAAE,CAAC;AACrB,iBAAW,KAAK,QAAQ;AAAA,IAC1B;AAEA,QAAI,KAAK;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,WAAS,cAAc,UAAU,gBAAgBC,aAAY;AAC3D,QAAI,gBAAgB,CAAC;AACrB,QAAI,eAAe,CAAC,eAAe;AACnC,QAAI,gBAAgB,mBAAmB,QAAQ,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG;AAC9E;AAAA,IACF;AACA,QAAI,cAAc;AAChB,uBAAiB,CAAC;AAAA;AAAA,QAEhB,OAAO,wBAAwB,IAAI,KAAK,OAAO,CAAC,CAAC,GAAG,UAAU,KAAK;AAAA,MACrE,GAAG;AAAA,QACD,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AACA,aAASC,KAAI,GAAGA,KAAI,eAAe,SAAS,GAAGA,MAAK;AAClD,UAAI,YAAY,eAAeA,EAAC,EAAE;AAClC,UAAI,UAAU,eAAeA,KAAI,CAAC,EAAE;AACpC,UAAI,cAAc,SAAS;AACzB;AAAA,MACF;AACA,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,SAAS;AACb,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,qBAAW,KAAK,IAAI,GAAG,KAAK,MAAM,iBAAiB,UAAU,GAAG,CAAC;AACjE,uBAAa,mBAAmB,KAAK;AACrC,uBAAa,mBAAmB,KAAK;AACrC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,iBAAiB,cAAc;AAC1C,uBAAa,gBAAgB,KAAK;AAClC,uBAAa,gBAAgB,KAAK;AAClC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,gBAAgB,gBAAgB,EAAE;AAC7C,uBAAa,eAAe,KAAK;AACjC,uBAAa,eAAe,KAAK;AACjC,mBAAS;AACT;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,gBAAgB,cAAc;AACzC,uBAAa,gBAAgB,KAAK;AAClC,uBAAa,gBAAgB,KAAK;AAClC;AAAA,QACF,KAAK;AACH,qBAAW,6BAA6B,gBAAgB,IAAI;AAC5D,uBAAa,kBAAkB,KAAK;AACpC,uBAAa,kBAAkB,KAAK;AACpC;AAAA,QACF,KAAK;AACH,qBAAW,6BAA6B,gBAAgB,KAAK;AAC7D,uBAAa,kBAAkB,KAAK;AACpC,uBAAa,kBAAkB,KAAK;AACpC;AAAA,QACF,KAAK;AACH,qBAAW,wBAAwB,cAAc;AACjD,uBAAa,uBAAuB,KAAK;AACzC,uBAAa,uBAAuB,KAAK;AACzC;AAAA,MACJ;AACA,qBAAe,UAAU,WAAW,SAAS,YAAY,YAAY,QAAQ,aAAa;AAC1F,UAAI,aAAa,UAAUD,YAAW,SAAS,KAAKC,OAAM,GAAG;AAE3D,QAAAD,YAAW,QAAQ;AAAA,UACjB,OAAOA,YAAW,CAAC,EAAE,QAAQ;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AACA,aAASC,KAAI,GAAGA,KAAI,cAAc,QAAQA,MAAK;AAC7C,MAAAD,YAAW,KAAK,cAAcC,EAAC,CAAC;AAAA,IAClC;AAEA,WAAO;AAAA,EACT;AACA,MAAI,cAAc,CAAC;AACnB,MAAI,oBAAoB,CAAC;AACzB,MAAI,YAAY;AAChB,MAAI,qBAAqB;AACzB,WAAS,IAAI,GAAG,IAAI,UAAU,UAAU,SAAS,WAAW,EAAE,GAAG;AAC/D,QAAI,kBAAkB,mBAAmB,UAAU,CAAC,CAAC;AACrD,QAAI,CAAC,kBAAkB,UAAU,CAAC,CAAC,GAAG;AAEpC;AAAA,IACF;AACA,kBAAc,UAAU,CAAC,GAAG,YAAY,YAAY,SAAS,CAAC,KAAK,CAAC,GAAG,iBAAiB;AACxF,QAAI,sBAAsB,UAAU,IAAI,CAAC,IAAI,mBAAmB,UAAU,IAAI,CAAC,CAAC,IAAI;AACpF,QAAI,oBAAoB,qBAAqB;AAC3C,UAAI,kBAAkB,QAAQ;AAC5B,6BAAqB;AAErB,0BAAkB,KAAK,SAAU,GAAG,GAAG;AACrC,iBAAO,EAAE,QAAQ,EAAE;AAAA,QACrB,CAAC;AACD,YAAI,6BAA6B,CAAC;AAClC,iBAAS,MAAM,GAAG,MAAM,kBAAkB,QAAQ,EAAE,KAAK;AACvD,cAAI,YAAY,kBAAkB,GAAG,EAAE;AACvC,cAAI,QAAQ,KAAK,kBAAkB,MAAM,CAAC,EAAE,UAAU,WAAW;AAC/D,uCAA2B,KAAK,kBAAkB,GAAG,CAAC;AACtD,gBAAI,aAAa,OAAO,CAAC,KAAK,aAAa,OAAO,CAAC,GAAG;AACpD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,iBAAiB,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK;AAE9C,YAAI,YAAY,gBAAgB,OAAO,qBAAqB,gBAAgB,KAAK;AAC/E;AAAA,QACF;AAEA,oBAAY,KAAK,0BAA0B;AAC3C,YAAI,YAAY,iBAAiB,mBAAmB,UAAU,CAAC,GAAG;AAChE;AAAA,QACF;AAAA,MACF;AAEA,0BAAoB,CAAC;AAAA,IACvB;AAAA,EACF;AACA,MAAI,MAAuC;AACzC,QAAI,QAAQ,WAAW;AACrB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,sBAAsB,OAAO,IAAI,aAAa,SAAUD,aAAY;AACtE,WAAO,OAAOA,aAAY,SAAU,MAAM;AACxC,aAAO,KAAK,SAAS,OAAO,CAAC,KAAK,KAAK,SAAS,OAAO,CAAC,KAAK,CAAC,KAAK;AAAA,IACrE,CAAC;AAAA,EACH,CAAC,GAAG,SAAUA,aAAY;AACxB,WAAOA,YAAW,SAAS;AAAA,EAC7B,CAAC;AACD,MAAI,QAAQ,CAAC;AACb,MAAI,WAAW,oBAAoB,SAAS;AAC5C,WAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,EAAE,GAAG;AACnD,QAAI,aAAa,oBAAoB,CAAC;AACtC,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,YAAM,KAAK;AAAA,QACT,OAAO,WAAW,CAAC,EAAE;AAAA,QACrB,OAAO,WAAW;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,KAAK,SAAU,GAAG,GAAG;AACzB,WAAO,EAAE,QAAQ,EAAE;AAAA,EACrB,CAAC;AAED,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,QAAI,MAAM,KAAK,MAAM,CAAC,EAAE,UAAU,MAAM,IAAI,CAAC,EAAE,OAAO;AACpD,aAAO,KAAK,MAAM,CAAC,CAAC;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,cAAM,cAAc,SAAS;AAC7B,IAAO,eAAQ;;;AC1cf,IAAI,aAAa,cAAM;AAEvB,IAAI,qBAAqB,iBAAc;AACvC,IAAI,mBAA8B;AAClC,IAAI,YAAY,KAAK;AACrB,IAAI,WAAW,KAAK;AACpB,IAAI,UAAU,KAAK;AACnB,IAAI,UAAU,KAAK;AACnB,IAAI;AAAA;AAAA,EAAwB,SAAU,QAAQ;AAC5C,cAAUE,WAAU,MAAM;AAC1B,aAASA,YAAW;AAClB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAO;AACb,YAAM,OAAO;AACb,YAAM,iBAAiB,IAAI,iBAAc;AAEzC,YAAM,YAAY;AAClB,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,UAAU,WAAW,SAAU,qBAAqB;AAC3D,UAAI,gBAAgB,KAAK;AACzB,UAAI,SAAS,KAAK;AAClB,UAAI,iBAAiB,cAAc,UAAU;AAC7C,UAAI,QAAQ,mBAAmB,SAAS,KAAK,MAAM,mBAAmB;AACtE,aAAc,IAAI,OAAO,SAAU,MAAM;AACvC,YAAI,MAAM,KAAK;AACf,YAAI,SAAoB,MAAM,QAAQ,KAAK,MAAM,GAAG,CAAC;AAErD,iBAAS,QAAQ,OAAO,CAAC,KAAK,KAAK,UAAU,iBAAiB,QAAQ,eAAe,CAAC,CAAC,IAAI;AAC3F,iBAAS,QAAQ,OAAO,CAAC,KAAK,KAAK,UAAU,iBAAiB,QAAQ,eAAe,CAAC,CAAC,IAAI;AAC3F,eAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AACA,IAAAA,UAAS,UAAU,YAAY,SAAU,OAAO,KAAK;AACnD,UAAI,OAAO,QAAQ,KAAK,IAAI;AAE5B,cAAQ,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,IAAI;AACtC,YAAM,QAAQ,KAAK,IAAI,GAAG,GAAG,CAAC,IAAI;AAClC,yBAAmB,UAAU,KAAK,MAAM,OAAO,GAAG;AAAA,IACpD;AAIA,IAAAA,UAAS,UAAU,YAAY,WAAY;AACzC,UAAI,OAAO,KAAK;AAChB,UAAI,SAAS,WAAW,UAAU,KAAK,IAAI;AAC3C,aAAO,CAAC,IAAI,QAAQ,MAAM,OAAO,CAAC,CAAC;AACnC,aAAO,CAAC,IAAI,QAAQ,MAAM,OAAO,CAAC,CAAC;AAEnC,UAAI,gBAAgB,KAAK;AACzB,UAAI,iBAAiB,cAAc,UAAU;AAC7C,WAAK,YAAY,OAAO,CAAC,IAAI,iBAAiB,OAAO,CAAC,GAAG,eAAe,CAAC,CAAC;AAC1E,WAAK,YAAY,OAAO,CAAC,IAAI,iBAAiB,OAAO,CAAC,GAAG,eAAe,CAAC,CAAC;AAC1E,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,UAAU,cAAc,SAAU,QAAQ;AACjD,WAAK,eAAe,YAAY,MAAM;AACtC,UAAI,OAAO,KAAK;AAChB,aAAO,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,IAAI,QAAQ,IAAI;AAC7C,aAAO,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,IAAI,QAAQ,IAAI;AAC7C,iBAAW,YAAY,KAAK,MAAM,MAAM;AAAA,IAC1C;AACA,IAAAA,UAAS,UAAU,sBAAsB,SAAU,MAAM,KAAK;AAG5D,WAAK,YAAY,KAAK,qBAAqB,GAAG,CAAC;AAAA,IACjD;AAKA,IAAAA,UAAS,UAAU,gBAAgB,SAAU,eAAe;AAC1D,sBAAgB,iBAAiB;AACjC,UAAI,SAAS,KAAK;AAClB,UAAI,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,UAAI,SAAS,YAAY,QAAQ,GAAG;AAClC;AAAA,MACF;AACA,UAAI,WAAsB,SAAS,IAAI;AACvC,UAAI,MAAM,gBAAgB,OAAO;AAEjC,UAAI,OAAO,KAAK;AACd,oBAAY;AAAA,MACd;AAEA,aAAO,CAAC,MAAM,QAAQ,KAAK,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,QAAQ,IAAI,GAAG;AAC3E,oBAAY;AAAA,MACd;AACA,UAAI,aAAa,CAAY,MAAM,SAAS,OAAO,CAAC,IAAI,QAAQ,IAAI,QAAQ,GAAc,MAAM,UAAU,OAAO,CAAC,IAAI,QAAQ,IAAI,QAAQ,CAAC;AAC3I,WAAK,YAAY;AACjB,WAAK,cAAc;AAAA,IACrB;AACA,IAAAA,UAAS,UAAU,iBAAiB,SAAU,KAAK;AACjD,yBAAmB,eAAe,KAAK,MAAM,GAAG;AAChD,WAAK,UAAU,IAAI;AACnB,WAAK,UAAU,IAAI;AAAA,IACrB;AACA,IAAAA,UAAS,UAAU,QAAQ,SAAU,KAAK;AACxC,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,UAAU,UAAU,SAAU,KAAK;AAC1C,YAAM,QAAQ,GAAG,IAAI,QAAQ,KAAK,IAAI;AACtC,aAAmBC,SAAQ,KAAK,KAAK,OAAO;AAAA,IAC9C;AACA,IAAAD,UAAS,UAAU,YAAY,SAAU,KAAK;AAC5C,YAAM,QAAQ,GAAG,IAAI,QAAQ,KAAK,IAAI;AACtC,aAAmB,UAAU,KAAK,KAAK,OAAO;AAAA,IAChD;AACA,IAAAA,UAAS,UAAU,QAAQ,SAAU,KAAK;AACxC,YAAkB,MAAM,KAAK,KAAK,OAAO;AACzC,aAAO,QAAQ,KAAK,MAAM,GAAG;AAAA,IAC/B;AACA,IAAAA,UAAS,OAAO;AAChB,WAAOA;AAAA,EACT,EAAE,aAAK;AAAA;AACP,IAAI,QAAQ,SAAS;AACrB,MAAM,gBAAgB,mBAAmB;AACzC,MAAM,WAAW,mBAAmB;AACpC,SAAS,iBAAiB,KAAK,aAAa;AAC1C,SAAO,iBAAiB,KAAgB,aAAa,WAAW,CAAC;AACnE;AACA,cAAM,cAAc,QAAQ;AAC5B,IAAO,cAAQ;;;ACpIf,IAAI;AAAA;AAAA,EAAkC,WAAY;AAChD,aAASE,oBAAmBC,QAAO,OAEnC,gBAAgB;AACd,WAAK,eAAeA,QAAO,OAAO,cAAc;AAAA,IAClD;AAKA,IAAAD,oBAAmB,UAAU,iBAAiB,SAAUC,QAAO,OAE/D,YAAY;AACV,UAAI,WAAW,CAAC,IAAI,WAAW,CAAC,GAAG;AACjC,qBAAa,CAAC,KAAK,GAAG;AAAA,MACxB;AACA,WAAK,WAAW,WAAW,CAAC;AAC5B,WAAK,WAAW,WAAW,CAAC;AAC5B,UAAI,YAAY,KAAK,aAAaA,OAAM,SAAS;AACjD,WAAK,iBAAiBA,OAAM,SAAS,cAAc,MAAM,oBAAoB,MAAM,iBAAiB;AACpG,UAAI,eAAe,MAAM,IAAI,OAAO,IAAI;AACxC,UAAI,gBAAgB,MAAM;AACxB,uBAAe,MAAM,IAAI,cAAc,IAAI;AAAA,MAC7C;AACA,UAAI,cAAc,KAAK,eAAe;AACtC,UAAI,WAAW,WAAW,GAAG;AAE3B,aAAK,eAAe,qBAAqBA,QAAO,YAAY;AAAA,UAC1D,KAAK,WAAW,CAAC;AAAA,UACjB,KAAK,WAAW,CAAC;AAAA,QACnB,CAAC,CAAC;AAAA,MACJ,WAAW,gBAAgB,WAAW;AACpC,aAAK,eAAe,qBAAqBA,QAAO,WAAW;AAAA,MAC7D;AACA,UAAI,cAAc,KAAK,eAAe,MAAM,IAAI,OAAO,IAAI;AAC3D,UAAI,WAAW,WAAW,GAAG;AAE3B,aAAK,eAAe,qBAAqBA,QAAO,YAAY;AAAA,UAC1D,KAAK,WAAW,CAAC;AAAA,UACjB,KAAK,WAAW,CAAC;AAAA,QACnB,CAAC,CAAC;AAAA,MACJ,WAAW,gBAAgB,WAAW;AACpC,aAAK,eAAe,qBAAqBA,QAAO,WAAW;AAAA,MAC7D;AACA,UAAI,WAAW;AAIb,aAAK,eAAe,MAAM,cAAc,EAAE;AAAA,MAC5C,OAAO;AACL,YAAI,cAAc,MAAM,IAAI,aAAa;AACzC,YAAI,iBAAiB,QAAQ,WAAW,IAAI,cAAc,CAAC,eAAe,GAAG,eAAe,CAAC;AAC7F,YAAI,OAAO,eAAe,CAAC,MAAM,aAAa,OAAO,eAAe,CAAC,MAAM,WAAW;AACpF,cAAI,MAAuC;AACzC,oBAAQ,KAAK,6JAA4K;AAAA,UAC3L;AACA,eAAK,oBAAoB,CAAC,GAAG,CAAC;AAAA,QAChC,OAAO;AACL,eAAK,oBAAoB,CAAC,aAAa,eAAe,CAAC,GAAG,CAAC,GAAG,aAAa,eAAe,CAAC,GAAG,CAAC,CAAC;AAAA,QAClG;AAAA,MACF;AAAA,IACF;AAOA,IAAAD,oBAAmB,UAAU,YAAY,WAAY;AAUnD,UAAI,YAAY,KAAK;AACrB,UAAI,UAAU,KAAK;AACnB,UAAI,UAAU,KAAK;AACnB,UAAI,cAAc,KAAK;AACvB,UAAI,mBAAmB,KAAK;AAC5B,UAAI,OAAO,CAAC,YAAY,UAAU,WAAW,KAAK,IAAI,OAAO,IAAI;AAGjE,UAAIE,OAAM,KAAK,iBAAiB,YAAY,UAAU,KAAK;AAC3D,UAAIC,OAAM,KAAK,iBAAiB,YAAY,UAAU,KAAK;AAE3D,UAAI,WAAWD,QAAO;AACtB,UAAI,WAAWC,QAAO;AACtB,UAAID,QAAO,MAAM;AACf,QAAAA,OAAM,YAAY,cAAc,IAAI,MAAM,UAAU,iBAAiB,CAAC,IAAI;AAAA,MAC5E;AACA,UAAIC,QAAO,MAAM;AACf,QAAAA,OAAM,YAAY,cAAc,cAAc,IAAI,MAAM,UAAU,iBAAiB,CAAC,IAAI;AAAA,MAC1F;AACA,OAACD,QAAO,QAAQ,CAAC,SAASA,IAAG,OAAOA,OAAM;AAC1C,OAACC,QAAO,QAAQ,CAAC,SAASA,IAAG,OAAOA,OAAM;AAC1C,UAAI,UAAU,MAAMD,IAAG,KAAK,MAAMC,IAAG,KAAK,aAAa,CAAC;AAExD,UAAI,KAAK,gBAAgB;AAEvB,YAAID,OAAM,KAAKC,OAAM,KAAK,CAAC,UAAU;AACnC,UAAAD,OAAM;AAAA,QAER;AAEA,YAAIA,OAAM,KAAKC,OAAM,KAAK,CAAC,UAAU;AACnC,UAAAA,OAAM;AAAA,QAER;AAAA,MAKF;AACA,UAAI,gBAAgB,KAAK;AACzB,UAAI,gBAAgB,KAAK;AACzB,UAAI,iBAAiB,MAAM;AACzB,QAAAD,OAAM;AACN,mBAAW;AAAA,MACb;AACA,UAAI,iBAAiB,MAAM;AACzB,QAAAC,OAAM;AACN,mBAAW;AAAA,MACb;AAGA,aAAO;AAAA,QACL,KAAKD;AAAA,QACL,KAAKC;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAAH,oBAAmB,UAAU,mBAAmB,SAAU,YAAY,KAAK;AACzE,UAAI,MAAuC;AACzC,eAAO,CAAC,KAAK,MAAM;AAAA,MACrB;AACA,WAAK,kBAAkB,UAAU,CAAC,IAAI;AAAA,IACxC;AACA,IAAAA,oBAAmB,UAAU,sBAAsB,SAAU,YAAY,KAAK;AAC5E,UAAI,OAAO,wBAAwB,UAAU;AAC7C,UAAI,MAAuC;AACzC,eAAO,CAAC,KAAK,UAEV,KAAK,IAAI,KAAK,IAAI;AAAA,MACvB;AACA,WAAK,IAAI,IAAI;AAAA,IACf;AACA,IAAAA,oBAAmB,UAAU,SAAS,WAAY;AAEhD,WAAK,SAAS;AAAA,IAChB;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,IAAI,0BAA0B;AAAA,EAC5B,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAI,oBAAoB;AAAA,EACtB,KAAK;AAAA,EACL,KAAK;AACP;AAgBO,SAAS,yBAAyBI,QAAO,OAEhD,gBAAgB;AAEd,MAAI,gBAAgBA,OAAM;AAC1B,MAAI,eAAe;AACjB,WAAO;AAAA,EACT;AACA,kBAAgB,IAAI,mBAAmBA,QAAO,OAAO,cAAc;AAEnE,EAAAA,OAAM,gBAAgB;AACtB,SAAO;AACT;AACO,SAAS,qBAAqBA,QAAO,QAAQ;AAClD,SAAO,UAAU,OAAO,OAAO,MAAM,MAAM,IAAI,MAAMA,OAAM,MAAM,MAAM;AACzE;;;ACpLO,SAAS,eAAeC,QAAO,OAAO;AAC3C,MAAI,YAAYA,OAAM;AACtB,MAAI,kBAAkB,yBAAyBA,QAAO,OAAOA,OAAM,UAAU,CAAC,EAAE,UAAU;AAC1F,EAAAA,OAAM,SAAS,gBAAgB,OAAO;AACtC,MAAIC,OAAM,gBAAgB;AAC1B,MAAIC,OAAM,gBAAgB;AAU1B,MAAI,UAAU,MAAM;AACpB,MAAI,WAAW,cAAc,QAA0C;AACrE,QAAI,kBAAkB,uBAAuB,OAAO,OAAO;AAC3D,QAAI,8BAA8B;AAClC,IAAO,KAAK,iBAAiB,SAAU,aAAa;AAClD,oCAA8B,+BAA+B,YAAY,YAAY,MAAM,MAAM;AAAA,IACnG,CAAC;AACD,QAAI,6BAA6B;AAG/B,UAAI,oBAAoB,iBAAiB,eAAe;AAExD,UAAI,gBAAgB,uBAAuBD,MAAKC,MAAK,OAAO,iBAAiB;AAC7E,MAAAD,OAAM,cAAc;AACpB,MAAAC,OAAM,cAAc;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AAAA,IACL,QAAQ,CAACD,MAAKC,IAAG;AAAA;AAAA;AAAA,IAGjB,QAAQ,gBAAgB;AAAA,IACxB,QAAQ,gBAAgB;AAAA,EAC1B;AACF;AACA,SAAS,uBAAuBD,MAAKC,MAAK,OAE1C,mBAAmB;AAEjB,MAAI,aAAa,MAAM,KAAK,UAAU;AACtC,MAAI,aAAa,KAAK,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,CAAC;AAEvD,MAAI,oBAAoB,qBAAqB,mBAAmB,MAAM,IAAI;AAC1E,MAAI,sBAAsB,QAAW;AACnC,WAAO;AAAA,MACL,KAAKD;AAAA,MACL,KAAKC;AAAA,IACP;AAAA,EACF;AACA,MAAI,cAAc;AAClB,EAAO,KAAK,mBAAmB,SAAU,MAAM;AAC7C,kBAAc,KAAK,IAAI,KAAK,QAAQ,WAAW;AAAA,EACjD,CAAC;AACD,MAAI,cAAc;AAClB,EAAO,KAAK,mBAAmB,SAAU,MAAM;AAC7C,kBAAc,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,WAAW;AAAA,EAC9D,CAAC;AACD,gBAAc,KAAK,IAAI,WAAW;AAClC,gBAAc,KAAK,IAAI,WAAW;AAClC,MAAI,gBAAgB,cAAc;AAElC,MAAI,WAAWA,OAAMD;AACrB,MAAI,uBAAuB,KAAK,cAAc,eAAe;AAC7D,MAAI,iBAAiB,WAAW,uBAAuB;AACvD,EAAAC,QAAO,kBAAkB,cAAc;AACvC,EAAAD,QAAO,kBAAkB,cAAc;AACvC,SAAO;AAAA,IACL,KAAKA;AAAA,IACL,KAAKC;AAAA,EACP;AACF;AAIO,SAAS,gBAAgBF,QAAO,SAAS;AAC9C,MAAI,QAAQ;AACZ,MAAI,aAAa,eAAeA,QAAO,KAAK;AAC5C,MAAI,SAAS,WAAW;AACxB,MAAI,cAAc,MAAM,IAAI,aAAa;AACzC,MAAIA,kBAAiB,aAAU;AAC7B,IAAAA,OAAM,OAAO,MAAM,IAAI,SAAS;AAAA,EAClC;AACA,MAAI,YAAYA,OAAM;AACtB,MAAI,WAAW,MAAM,IAAI,UAAU;AACnC,MAAI,mBAAmB,cAAc,cAAc,cAAc;AACjE,EAAAA,OAAM,UAAU,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACpC,EAAAA,OAAM,eAAe;AAAA,IACnB;AAAA,IACA,QAAQ,WAAW;AAAA,IACnB,QAAQ,WAAW;AAAA,IACnB,aAAa,mBAAmB,MAAM,IAAI,aAAa,IAAI;AAAA,IAC3D,aAAa,mBAAmB,MAAM,IAAI,aAAa,IAAI;AAAA,EAC7D,CAAC;AAMD,MAAI,YAAY,MAAM;AACpB,IAAAA,OAAM,eAAeA,OAAM,YAAY,QAAQ;AAAA,EACjD;AACF;AAIO,SAAS,mBAAmB,OAAO,UAAU;AAClD,aAAW,YAAY,MAAM,IAAI,MAAM;AACvC,MAAI,UAAU;AACZ,YAAQ,UAAU;AAAA,MAEhB,KAAK;AACH,eAAO,IAAI,gBAAa;AAAA,UACtB,aAAa,MAAM,iBAAiB,MAAM,eAAe,IAAI,MAAM,cAAc;AAAA,UACjF,QAAQ,CAAC,UAAU,SAAS;AAAA,QAC9B,CAAC;AAAA,MACH,KAAK;AACH,eAAO,IAAI,aAAU;AAAA,UACnB,QAAQ,MAAM,QAAQ,eAAe;AAAA,UACrC,QAAQ,MAAM,QAAQ,IAAI,QAAQ;AAAA,QACpC,CAAC;AAAA,MACH;AAEE,eAAO,KAAK,cAAM,SAAS,QAAQ,KAAK,kBAAe;AAAA,IAC3D;AAAA,EACF;AACF;AAIO,SAAS,gBAAgB,MAAM;AACpC,MAAI,aAAa,KAAK,MAAM,UAAU;AACtC,MAAIC,OAAM,WAAW,CAAC;AACtB,MAAIC,OAAM,WAAW,CAAC;AACtB,SAAO,EAAED,OAAM,KAAKC,OAAM,KAAKD,OAAM,KAAKC,OAAM;AAClD;AASO,SAAS,mBAAmB,MAAM;AACvC,MAAI,iBAAiB,KAAK,cAAc,EAAE,IAAI,WAAW;AACzD,MAAI,oBAAoB,KAAK,SAAS,aAAa,KAAK,MAAM,UAAU,EAAE,CAAC,IAAI;AAC/E,MAAI,KAAK,MAAM,SAAS,QAAQ;AAC9B,WAAO,yBAAU,KAAK;AACpB,aAAO,SAAU,MAAM,KAAK;AAC1B,eAAO,KAAK,MAAM,kBAAkB,MAAM,KAAK,GAAG;AAAA,MACpD;AAAA,IACF,EAAE,cAAc;AAAA,EAClB,WAAkB,SAAS,cAAc,GAAG;AAC1C,WAAO,yBAAU,KAAK;AACpB,aAAO,SAAU,MAAM;AAGrB,YAAI,QAAQ,KAAK,MAAM,SAAS,IAAI;AACpC,YAAI,OAAO,IAAI,QAAQ,WAAW,SAAS,OAAO,QAAQ,EAAE;AAC5D,eAAO;AAAA,MACT;AAAA,IACF,EAAE,cAAc;AAAA,EAClB,WAAkB,WAAW,cAAc,GAAG;AAC5C,WAAO,yBAAU,IAAI;AACnB,aAAO,SAAU,MAAM,KAAK;AAO1B,YAAI,qBAAqB,MAAM;AAC7B,gBAAM,KAAK,QAAQ;AAAA,QACrB;AACA,eAAO,GAAG,gBAAgB,MAAM,IAAI,GAAG,KAAK,KAAK,SAAS,OAAO;AAAA,UAC/D,OAAO,KAAK;AAAA,QACd,IAAI,IAAI;AAAA,MACV;AAAA,IACF,EAAE,cAAc;AAAA,EAClB,OAAO;AACL,WAAO,SAAU,MAAM;AACrB,aAAO,KAAK,MAAM,SAAS,IAAI;AAAA,IACjC;AAAA,EACF;AACF;AACO,SAAS,gBAAgB,MAAM,MAAM;AAI1C,SAAO,KAAK,SAAS,aAAa,KAAK,MAAM,SAAS,IAAI,IAAI,KAAK;AACrE;AAKO,SAAS,uBAAuB,MAAM;AAC3C,MAAI,YAAY,KAAK;AACrB,MAAIF,SAAQ,KAAK;AACjB,MAAI,CAAC,UAAU,IAAI,CAAC,aAAa,MAAM,CAAC,KAAKA,OAAM,QAAQ,GAAG;AAC5D;AAAA,EACF;AACA,MAAI;AACJ,MAAI;AACJ,MAAI,sBAAsBA,OAAM,UAAU;AAE1C,MAAIA,kBAAiB,iBAAc;AACjC,gBAAYA,OAAM,MAAM;AAAA,EAC1B,OAAO;AACL,2BAAuBA,OAAM,SAAS;AACtC,gBAAY,qBAAqB;AAAA,EACnC;AACA,MAAI,iBAAiB,KAAK,cAAc;AACxC,MAAI,iBAAiB,mBAAmB,IAAI;AAC5C,MAAI;AACJ,MAAI,OAAO;AAEX,MAAI,YAAY,IAAI;AAClB,WAAO,KAAK,KAAK,YAAY,EAAE;AAAA,EACjC;AACA,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK,MAAM;AACxC,QAAI,OAAO,uBAAuB,qBAAqB,CAAC,IAAI;AAAA,MAC1D,OAAO,oBAAoB,CAAC,IAAI;AAAA,IAClC;AACA,QAAI,QAAQ,eAAe,MAAM,CAAC;AAClC,QAAI,sBAAsB,eAAe,YAAY,KAAK;AAC1D,QAAI,aAAa,eAAe,qBAAqB,eAAe,IAAI,QAAQ,KAAK,CAAC;AACtF,WAAO,KAAK,MAAM,UAAU,IAAI,OAAO;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,eAAe,UAAU,QAAQ;AACxC,MAAI,gBAAgB,SAAS,KAAK,KAAK;AACvC,MAAI,cAAc,SAAS;AAC3B,MAAI,eAAe,SAAS;AAC5B,MAAI,aAAa,cAAc,KAAK,IAAI,KAAK,IAAI,aAAa,CAAC,IAAI,KAAK,IAAI,eAAe,KAAK,IAAI,aAAa,CAAC;AAClH,MAAI,cAAc,cAAc,KAAK,IAAI,KAAK,IAAI,aAAa,CAAC,IAAI,KAAK,IAAI,eAAe,KAAK,IAAI,aAAa,CAAC;AACnH,MAAI,cAAc,IAAI,qBAAa,SAAS,GAAG,SAAS,GAAG,YAAY,WAAW;AAClF,SAAO;AACT;AAKO,SAAS,0BAA0B,OAAO;AAC/C,MAAI,WAAW,MAAM,IAAI,UAAU;AACnC,SAAO,YAAY,OAAO,SAAS;AACrC;AAMO,SAAS,oBAAoB,MAAM;AACxC,SAAO,KAAK,SAAS,cAAc,0BAA0B,KAAK,cAAc,CAAC,MAAM;AACzF;AACO,SAAS,wBAAwB,MAAM,SAAS;AAErD,MAAI,aAAa,CAAC;AAIlB,EAAO,KAAK,KAAK,iBAAiB,OAAO,GAAG,SAAU,SAAS;AAO7D,eAAW,oBAAoB,MAAM,OAAO,CAAC,IAAI;AAAA,EACnD,CAAC;AACD,SAAc,KAAK,UAAU;AAC/B;AACO,SAAS,wBAAwB,YAAY,MAAM,SAAS;AACjE,MAAI,MAAM;AACR,IAAO,KAAK,wBAAwB,MAAM,OAAO,GAAG,SAAU,KAAK;AACjE,UAAI,eAAe,KAAK,qBAAqB,GAAG;AAChD,mBAAa,CAAC,IAAI,WAAW,CAAC,MAAM,WAAW,CAAC,IAAI,aAAa,CAAC;AAClE,mBAAa,CAAC,IAAI,WAAW,CAAC,MAAM,WAAW,CAAC,IAAI,aAAa,CAAC;AAAA,IACpE,CAAC;AAAA,EACH;AACF;;;AC5SA,IAAIG,SAAQ,UAAU;AACtB,SAAS,oBAAoB,MAAM,QAAQ;AACzC,MAAI,OAAc,IAAI,QAAQ,SAAU,KAAK;AAC3C,WAAO,KAAK,MAAM,MAAM,GAAG;AAAA,EAC7B,CAAC;AACD,MAAI,KAAK,SAAS,UAAU,KAAK,SAAS,GAAG;AAG3C,SAAK,KAAK;AACV,SAAK,QAAQ,KAAK,CAAC,CAAC;AACpB,SAAK,KAAK,KAAK,KAAK,SAAS,CAAC,CAAC;AAAA,EACjC;AACA,SAAO;AACT;AACO,SAAS,iBAAiB,MAAM;AACrC,MAAI,SAAS,KAAK,cAAc,EAAE,IAAI,cAAc;AACpD,MAAI,QAAQ;AACV,QAAI,mBAAmB,mBAAmB,IAAI;AAC9C,QAAI,WAAW,KAAK,MAAM,UAAU;AACpC,QAAI,cAAc,oBAAoB,MAAM,MAAM;AAClD,QAAI,QAAe,OAAO,aAAa,SAAU,KAAK;AACpD,aAAO,OAAO,SAAS,CAAC,KAAK,OAAO,SAAS,CAAC;AAAA,IAChD,CAAC;AACD,WAAO;AAAA,MACL,QAAe,IAAI,OAAO,SAAU,QAAQ;AAC1C,YAAI,OAAO;AAAA,UACT,OAAO;AAAA,QACT;AACA,eAAO;AAAA,UACL,gBAAgB,iBAAiB,IAAI;AAAA,UACrC,UAAU,KAAK,MAAM,SAAS,IAAI;AAAA,UAClC,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,KAAK,SAAS,aAAa,mBAAmB,IAAI,IAAI,qBAAqB,IAAI;AACxF;AASO,SAAS,gBAAgB,MAAM,WAAW;AAC/C,MAAI,SAAS,KAAK,aAAa,EAAE,IAAI,cAAc;AACnD,MAAI,QAAQ;AACV,QAAI,WAAW,KAAK,MAAM,UAAU;AACpC,QAAI,cAAc,oBAAoB,MAAM,MAAM;AAClD,WAAO;AAAA,MACL,OAAc,OAAO,aAAa,SAAU,KAAK;AAC/C,eAAO,OAAO,SAAS,CAAC,KAAK,OAAO,SAAS,CAAC;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,KAAK,SAAS,aAAa,kBAAkB,MAAM,SAAS,IAAI;AAAA,IACrE,OAAc,IAAI,KAAK,MAAM,SAAS,GAAG,SAAU,MAAM;AACvD,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,aAAa,KAAK,cAAc;AACpC,MAAI,SAAS,2BAA2B,MAAM,UAAU;AACxD,SAAO,CAAC,WAAW,IAAI,MAAM,KAAK,KAAK,MAAM,QAAQ,IAAI;AAAA,IACvD,QAAQ,CAAC;AAAA,IACT,uBAAuB,OAAO;AAAA,EAChC,IAAI;AACN;AACA,SAAS,2BAA2B,MAAM,YAAY;AACpD,MAAI,cAAc,aAAa,MAAM,QAAQ;AAC7C,MAAI,sBAAsB,0BAA0B,UAAU;AAC9D,MAAI,SAAS,aAAa,aAAa,mBAAmB;AAC1D,MAAI,QAAQ;AACV,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACJ,MAAW,WAAW,mBAAmB,GAAG;AAC1C,aAAS,uCAAuC,MAAM,mBAAmB;AAAA,EAC3E,OAAO;AACL,2BAAuB,wBAAwB,SAAS,yBAAyB,IAAI,IAAI;AACzF,aAAS,oCAAoC,MAAM,oBAAoB;AAAA,EACzE;AAEA,SAAO,aAAa,aAAa,qBAAqB;AAAA,IACpD;AAAA,IACA,uBAAuB;AAAA,EACzB,CAAC;AACH;AACA,SAAS,kBAAkB,MAAM,WAAW;AAC1C,MAAI,aAAa,aAAa,MAAM,OAAO;AAC3C,MAAI,qBAAqB,0BAA0B,SAAS;AAC5D,MAAI,SAAS,aAAa,YAAY,kBAAkB;AACxD,MAAI,QAAQ;AACV,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AAGJ,MAAI,CAAC,UAAU,IAAI,MAAM,KAAK,KAAK,MAAM,QAAQ,GAAG;AAClD,YAAQ,CAAC;AAAA,EACX;AACA,MAAW,WAAW,kBAAkB,GAAG;AACzC,YAAQ,uCAAuC,MAAM,oBAAoB,IAAI;AAAA,EAC/E,WAIS,uBAAuB,QAAQ;AACtC,QAAI,eAAe,2BAA2B,MAAM,KAAK,cAAc,CAAC;AACxE,2BAAuB,aAAa;AACpC,YAAe,IAAI,aAAa,QAAQ,SAAU,WAAW;AAC3D,aAAO,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,OAAO;AACL,2BAAuB;AACvB,YAAQ,oCAAoC,MAAM,sBAAsB,IAAI;AAAA,EAC9E;AAEA,SAAO,aAAa,YAAY,oBAAoB;AAAA,IAClD;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,SAAS,qBAAqB,MAAM;AAClC,MAAI,QAAQ,KAAK,MAAM,SAAS;AAChC,MAAI,iBAAiB,mBAAmB,IAAI;AAC5C,SAAO;AAAA,IACL,QAAe,IAAI,OAAO,SAAU,MAAM,KAAK;AAC7C,aAAO;AAAA,QACL,OAAO,KAAK;AAAA,QACZ,gBAAgB,eAAe,MAAM,GAAG;AAAA,QACxC,UAAU,KAAK,MAAM,SAAS,IAAI;AAAA,QAClC,WAAW,KAAK;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,aAAa,MAAM,MAAM;AAEhC,SAAOA,OAAM,IAAI,EAAE,IAAI,MAAMA,OAAM,IAAI,EAAE,IAAI,IAAI,CAAC;AACpD;AACA,SAAS,aAAa,OAAO,KAAK;AAChC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,CAAC,EAAE,QAAQ,KAAK;AACxB,aAAO,MAAM,CAAC,EAAE;AAAA,IAClB;AAAA,EACF;AACF;AACA,SAAS,aAAa,OAAO,KAAK,OAAO;AACvC,QAAM,KAAK;AAAA,IACT;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,yBAAyB,MAAM;AACtC,MAAI,SAASA,OAAM,IAAI,EAAE;AACzB,SAAO,UAAU,OAAO,SAASA,OAAM,IAAI,EAAE,eAAe,KAAK,0BAA0B;AAC7F;AAMO,SAAS,0BAA0B,MAAM;AAC9C,MAAI,SAAS,2CAA2C,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,IAAI;AAC5C,MAAI,YAAY,OAAO,aAAa,OAAO,eAAe,MAAM,KAAK;AACrE,MAAI,eAAe,KAAK;AACxB,MAAI,gBAAgB,aAAa,UAAU;AAI3C,MAAI,YAAY,aAAa,MAAM;AACnC,MAAI,cAAc,CAAC,IAAI,cAAc,CAAC,IAAI,GAAG;AAC3C,WAAO;AAAA,EACT;AACA,MAAI,OAAO;AAEX,MAAI,YAAY,IAAI;AAClB,WAAO,KAAK,IAAI,GAAG,KAAK,MAAM,YAAY,EAAE,CAAC;AAAA,EAC/C;AACA,MAAI,YAAY,cAAc,CAAC;AAC/B,MAAI,WAAW,KAAK,YAAY,YAAY,CAAC,IAAI,KAAK,YAAY,SAAS;AAC3E,MAAI,QAAQ,KAAK,IAAI,WAAW,KAAK,IAAI,QAAQ,CAAC;AAClD,MAAI,QAAQ,KAAK,IAAI,WAAW,KAAK,IAAI,QAAQ,CAAC;AAClD,MAAI,OAAO;AACX,MAAI,OAAO;AAGX,SAAO,aAAa,cAAc,CAAC,GAAG,aAAa,MAAM;AACvD,QAAI,QAAQ;AACZ,QAAI,SAAS;AAGb,QAAI,OAAmB,gBAAgB,eAAe;AAAA,MACpD,OAAO;AAAA,IACT,CAAC,GAAG,OAAO,MAAM,UAAU,KAAK;AAEhC,YAAQ,KAAK,QAAQ;AACrB,aAAS,KAAK,SAAS;AAEvB,WAAO,KAAK,IAAI,MAAM,OAAO,CAAC;AAC9B,WAAO,KAAK,IAAI,MAAM,QAAQ,CAAC;AAAA,EACjC;AACA,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,OAAO;AAEhB,QAAM,EAAE,MAAM,KAAK;AACnB,QAAM,EAAE,MAAM,KAAK;AACnB,MAAI,WAAW,KAAK,IAAI,GAAG,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC,CAAC;AACvD,MAAI,QAAQA,OAAM,KAAK,KAAK;AAC5B,MAAI,aAAa,KAAK,UAAU;AAChC,MAAI,mBAAmB,MAAM;AAC7B,MAAI,gBAAgB,MAAM;AAO1B,MAAI,oBAAoB,QAAQ,iBAAiB,QAAQ,KAAK,IAAI,mBAAmB,QAAQ,KAAK,KAAK,KAAK,IAAI,gBAAgB,SAAS,KAAK,KAG3I,mBAAmB,YAGnB,MAAM,gBAAgB,WAAW,CAAC,KAAK,MAAM,gBAAgB,WAAW,CAAC,GAAG;AAC7E,eAAW;AAAA,EACb,OAGK;AACH,UAAM,gBAAgB;AACtB,UAAM,mBAAmB;AACzB,UAAM,cAAc,WAAW,CAAC;AAChC,UAAM,cAAc,WAAW,CAAC;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,2CAA2C,MAAM;AACxD,MAAI,aAAa,KAAK,cAAc;AACpC,SAAO;AAAA,IACL,YAAY,KAAK,YAAY,KAAK,UAAU,IAAI,KAAK,gBAAgB,CAAC,KAAK,aAAa,IAAI,KAAK;AAAA,IACjG,aAAa,WAAW,IAAI,QAAQ,KAAK;AAAA,IACzC,MAAM,WAAW,QAAQ;AAAA,EAC3B;AACF;AACA,SAAS,oCAAoC,MAAM,kBAAkB,UAAU;AAC7E,MAAI,iBAAiB,mBAAmB,IAAI;AAC5C,MAAI,eAAe,KAAK;AACxB,MAAI,gBAAgB,aAAa,UAAU;AAC3C,MAAI,aAAa,KAAK,cAAc;AACpC,MAAI,SAAS,CAAC;AAEd,MAAI,OAAO,KAAK,KAAK,oBAAoB,KAAK,GAAG,CAAC;AAClD,MAAI,YAAY,cAAc,CAAC;AAC/B,MAAI,YAAY,aAAa,MAAM;AAKnC,MAAI,cAAc,KAAK,OAAO,KAAK,YAAY,OAAO,GAAG;AACvD,gBAAY,KAAK,MAAM,KAAK,KAAK,YAAY,IAAI,IAAI,IAAI;AAAA,EAC3D;AAMA,MAAI,eAAe,oBAAoB,IAAI;AAC3C,MAAI,kBAAkB,WAAW,IAAI,cAAc,KAAK;AACxD,MAAI,kBAAkB,WAAW,IAAI,cAAc,KAAK;AACxD,MAAI,mBAAmB,cAAc,cAAc,CAAC,GAAG;AACrD,YAAQ,cAAc,CAAC,CAAC;AAAA,EAC1B;AAEA,MAAI,YAAY;AAChB,SAAO,aAAa,cAAc,CAAC,GAAG,aAAa,MAAM;AACvD,YAAQ,SAAS;AAAA,EACnB;AACA,MAAI,mBAAmB,YAAY,SAAS,cAAc,CAAC,GAAG;AAC5D,YAAQ,cAAc,CAAC,CAAC;AAAA,EAC1B;AACA,WAAS,QAAQC,YAAW;AAC1B,QAAI,UAAU;AAAA,MACZ,OAAOA;AAAA,IACT;AACA,WAAO,KAAK,WAAWA,aAAY;AAAA,MACjC,gBAAgB,eAAe,OAAO;AAAA,MACtC,UAAU,aAAa,SAAS,OAAO;AAAA,MACvC,WAAWA;AAAA,IACb,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,uCAAuC,MAAM,kBAAkB,UAAU;AAChF,MAAI,eAAe,KAAK;AACxB,MAAI,iBAAiB,mBAAmB,IAAI;AAC5C,MAAI,SAAS,CAAC;AACd,EAAO,KAAK,aAAa,SAAS,GAAG,SAAU,MAAM;AACnD,QAAI,WAAW,aAAa,SAAS,IAAI;AACzC,QAAI,YAAY,KAAK;AACrB,QAAI,iBAAiB,KAAK,OAAO,QAAQ,GAAG;AAC1C,aAAO,KAAK,WAAW,YAAY;AAAA,QACjC,gBAAgB,eAAe,IAAI;AAAA,QACnC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;AChUA,IAAI,oBAAoB,CAAC,GAAG,CAAC;AAI7B,IAAI;AAAA;AAAA,EAAoB,WAAY;AAClC,aAASC,MAAK,KAAKC,QAAO,QAAQ;AAChC,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,MAAM;AACX,WAAK,QAAQA;AACb,WAAK,UAAU,UAAU,CAAC,GAAG,CAAC;AAAA,IAChC;AAIA,IAAAD,MAAK,UAAU,UAAU,SAAU,OAAO;AACxC,UAAI,SAAS,KAAK;AAClB,UAAIE,OAAM,KAAK,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACvC,UAAIC,OAAM,KAAK,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACvC,aAAO,SAASD,QAAO,SAASC;AAAA,IAClC;AAIA,IAAAH,MAAK,UAAU,cAAc,SAAU,MAAM;AAC3C,aAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,IAChC;AAIA,IAAAA,MAAK,UAAU,YAAY,WAAY;AACrC,aAAO,KAAK,QAAQ,MAAM;AAAA,IAC5B;AAIA,IAAAA,MAAK,UAAU,oBAAoB,SAAU,YAAY;AACvD,aAAO,kBAAkB,cAAc,KAAK,MAAM,UAAU,GAAG,KAAK,OAAO;AAAA,IAC7E;AAIA,IAAAA,MAAK,UAAU,YAAY,SAAU,OAAO,KAAK;AAC/C,UAAI,SAAS,KAAK;AAClB,aAAO,CAAC,IAAI;AACZ,aAAO,CAAC,IAAI;AAAA,IACd;AAIA,IAAAA,MAAK,UAAU,cAAc,SAAU,MAAMI,QAAO;AAClD,UAAI,SAAS,KAAK;AAClB,UAAIH,SAAQ,KAAK;AACjB,aAAOA,OAAM,UAAU,IAAI;AAC3B,UAAI,KAAK,UAAUA,OAAM,SAAS,WAAW;AAC3C,iBAAS,OAAO,MAAM;AACtB,2BAAmB,QAAQA,OAAM,MAAM,CAAC;AAAA,MAC1C;AACA,aAAO,UAAU,MAAM,mBAAmB,QAAQG,MAAK;AAAA,IACzD;AAIA,IAAAJ,MAAK,UAAU,cAAc,SAAU,OAAOI,QAAO;AACnD,UAAI,SAAS,KAAK;AAClB,UAAIH,SAAQ,KAAK;AACjB,UAAI,KAAK,UAAUA,OAAM,SAAS,WAAW;AAC3C,iBAAS,OAAO,MAAM;AACtB,2BAAmB,QAAQA,OAAM,MAAM,CAAC;AAAA,MAC1C;AACA,UAAI,IAAI,UAAU,OAAO,QAAQ,mBAAmBG,MAAK;AACzD,aAAO,KAAK,MAAM,MAAM,CAAC;AAAA,IAC3B;AAIA,IAAAJ,MAAK,UAAU,cAAc,SAAU,OAAOI,QAAO;AAEnD;AAAA,IACF;AAUA,IAAAJ,MAAK,UAAU,iBAAiB,SAAU,KAAK;AAC7C,YAAM,OAAO,CAAC;AACd,UAAI,YAAY,IAAI,aAAa,KAAK,aAAa;AACnD,UAAI,SAAS,gBAAgB,MAAM,SAAS;AAC5C,UAAI,QAAQ,OAAO;AACnB,UAAI,cAAc,IAAI,OAAO,SAAU,SAAS;AAC9C,eAAO;AAAA,UACL,OAAO,KAAK,YAAY,KAAK,MAAM,SAAS,YAAY,KAAK,MAAM,oBAAoB,OAAO,IAAI,OAAO;AAAA,UACzG,WAAW;AAAA,QACb;AAAA,MACF,GAAG,IAAI;AACP,UAAI,iBAAiB,UAAU,IAAI,gBAAgB;AACnD,2BAAqB,MAAM,aAAa,gBAAgB,IAAI,KAAK;AACjE,aAAO;AAAA,IACT;AACA,IAAAA,MAAK,UAAU,sBAAsB,WAAY;AAC/C,UAAI,KAAK,MAAM,SAAS,WAAW;AAEjC,eAAO,CAAC;AAAA,MACV;AACA,UAAI,iBAAiB,KAAK,MAAM,SAAS,WAAW;AACpD,UAAI,cAAc,eAAe,IAAI,aAAa;AAElD,UAAI,EAAE,cAAc,KAAK,cAAc,MAAM;AAC3C,sBAAc;AAAA,MAChB;AACA,UAAI,aAAa,KAAK,MAAM,cAAc,WAAW;AACrD,UAAI,mBAAmB,IAAI,YAAY,SAAU,iBAAiB;AAChE,eAAO,IAAI,iBAAiB,SAAU,WAAW;AAC/C,iBAAO;AAAA,YACL,OAAO,KAAK,YAAY,SAAS;AAAA,YACjC,WAAW;AAAA,UACb;AAAA,QACF,GAAG,IAAI;AAAA,MACT,GAAG,IAAI;AACP,aAAO;AAAA,IACT;AACA,IAAAA,MAAK,UAAU,gBAAgB,WAAY;AACzC,aAAO,iBAAiB,IAAI,EAAE;AAAA,IAChC;AACA,IAAAA,MAAK,UAAU,gBAAgB,WAAY;AACzC,aAAO,KAAK,MAAM,SAAS,WAAW;AAAA,IACxC;AAQA,IAAAA,MAAK,UAAU,eAAe,WAAY;AACxC,aAAO,KAAK,MAAM,SAAS,UAAU;AAAA,IACvC;AAIA,IAAAA,MAAK,UAAU,eAAe,WAAY;AACxC,UAAI,aAAa,KAAK;AACtB,UAAI,aAAa,KAAK,MAAM,UAAU;AACtC,UAAI,MAAM,WAAW,CAAC,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,IAAI;AAE7D,cAAQ,MAAM,MAAM;AACpB,UAAI,OAAO,KAAK,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,CAAC;AACjD,aAAO,KAAK,IAAI,IAAI,IAAI;AAAA,IAC1B;AAMA,IAAAA,MAAK,UAAU,4BAA4B,WAAY;AACrD,aAAO,0BAA0B,IAAI;AAAA,IACvC;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,MAAI,MAAM;AACV,MAAI,SAAS,OAAO,MAAM;AAC1B,SAAO,CAAC,KAAK;AACb,SAAO,CAAC,KAAK;AACf;AAUA,SAAS,qBAAqB,MAAM,aAAa,gBAAgBI,QAAO;AACtE,MAAI,WAAW,YAAY;AAC3B,MAAI,CAAC,KAAK,UAAU,kBAAkB,CAAC,UAAU;AAC/C;AAAA,EACF;AACA,MAAI,aAAa,KAAK,UAAU;AAChC,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa,GAAG;AAClB,gBAAY,CAAC,EAAE,QAAQ,WAAW,CAAC;AACnC,WAAO,YAAY,CAAC,IAAI;AAAA,MACtB,OAAO,WAAW,CAAC;AAAA,MACnB,WAAW,YAAY,CAAC,EAAE;AAAA,IAC5B;AAAA,EACF,OAAO;AACL,QAAI,WAAW,YAAY,WAAW,CAAC,EAAE,YAAY,YAAY,CAAC,EAAE;AACpE,QAAI,WAAW,YAAY,WAAW,CAAC,EAAE,QAAQ,YAAY,CAAC,EAAE,SAAS;AACzE,SAAK,aAAa,SAAU,WAAW;AACrC,gBAAU,SAAS,UAAU;AAAA,IAC/B,CAAC;AACD,QAAI,aAAa,KAAK,MAAM,UAAU;AACtC,eAAW,IAAI,WAAW,CAAC,IAAI,YAAY,WAAW,CAAC,EAAE;AACzD,WAAO;AAAA,MACL,OAAO,YAAY,WAAW,CAAC,EAAE,QAAQ,UAAU;AAAA,MACnD,WAAW,WAAW,CAAC,IAAI;AAAA,IAC7B;AACA,gBAAY,KAAK,IAAI;AAAA,EACvB;AACA,MAAI,UAAU,WAAW,CAAC,IAAI,WAAW,CAAC;AAE1C,MAAI,WAAW,YAAY,CAAC,EAAE,OAAO,WAAW,CAAC,CAAC,GAAG;AACnD,IAAAA,SAAQ,YAAY,CAAC,EAAE,QAAQ,WAAW,CAAC,IAAI,YAAY,MAAM;AAAA,EACnE;AACA,MAAIA,UAAS,WAAW,WAAW,CAAC,GAAG,YAAY,CAAC,EAAE,KAAK,GAAG;AAC5D,gBAAY,QAAQ;AAAA,MAClB,OAAO,WAAW,CAAC;AAAA,IACrB,CAAC;AAAA,EACH;AACA,MAAI,WAAW,WAAW,CAAC,GAAG,KAAK,KAAK,GAAG;AACzC,IAAAA,SAAQ,KAAK,QAAQ,WAAW,CAAC,IAAI,YAAY,IAAI;AAAA,EACvD;AACA,MAAIA,UAAS,WAAW,KAAK,OAAO,WAAW,CAAC,CAAC,GAAG;AAClD,gBAAY,KAAK;AAAA,MACf,OAAO,WAAW,CAAC;AAAA,IACrB,CAAC;AAAA,EACH;AACA,WAAS,WAAW,GAAG,GAAG;AAGxB,QAAI,MAAM,CAAC;AACX,QAAI,MAAM,CAAC;AACX,WAAO,UAAU,IAAI,IAAI,IAAI;AAAA,EAC/B;AACF;AACA,IAAO,eAAQ;;;AC5Of,IAAI;AAAA;AAAA,EAAoC,WAAY;AAClD,aAASC,wBAAuB;AAAA,IAAC;AACjC,IAAAA,sBAAqB,UAAU,mBAAmB,WAAY;AAC5D,UAAI,SAAS,KAAK;AAClB,aAAO,CAAC,OAAO;AAAA,IACjB;AAKA,IAAAA,sBAAqB,UAAU,mBAAmB,WAAY;AAC5D;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;;;ACdK,SAAS,kBAAkB,OAAO;AACvC,MAAI,OAAO,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,UAAU,MAAM,CAAC;AACrB,QAAI,QAAQ,YAAY,QAAQ;AAC9B;AAAA,IACF;AACA,QAAI,QAAQ,QAAQ;AACpB,QAAI,YAAY,MAAM,qBAAqB;AAE3C,QAAI,YAAY,MAAM,gBAAgB;AACtC,QAAI,gBAAgB,CAAC,aAAa,UAAU,CAAC,IAAI,QAAQ,UAAU,CAAC,IAAI;AACxE,QAAI,YAAY,MAAM,MAAM,UAAU;AACtC,QAAI,aAAa,UAAU,MAAM;AACjC,eAAW,eAAe,SAAS;AACnC,eAAW,KAAK,YAAY;AAC5B,eAAW,KAAK,YAAY;AAC5B,eAAW,SAAS;AACpB,eAAW,UAAU;AACrB,QAAI,MAAM,gBAAgB,IAAI,6BAAqB,WAAW,SAAS,IAAI;AAC3E,SAAK,KAAK;AAAA,MACR;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,UAAU,QAAQ;AAAA,MAClB,aAAa,QAAQ;AAAA,MACrB,cAAc,QAAQ;AAAA,MACtB,aAAa;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,YAAY,MAAM,OAAO,SAAS,UAAU,UAAU,cAAc;AAC3E,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,GAAG;AACX;AAAA,EACF;AACA,OAAK,KAAK,SAAU,GAAG,GAAG;AACxB,WAAO,EAAE,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK;AAAA,EACrC,CAAC;AACD,MAAI,UAAU;AACd,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,SAAS,CAAC;AACd,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,OAAO,KAAK,CAAC;AACjB,QAAI,OAAO,KAAK;AAChB,YAAQ,KAAK,KAAK,IAAI;AACtB,QAAI,QAAQ,GAAG;AAEb,WAAK,KAAK,KAAK;AACf,WAAK,MAAM,KAAK,KAAK;AACrB,iBAAW;AAAA,IACb;AACA,QAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC;AAC9B,WAAO,KAAK,KAAK;AACjB,mBAAe;AACf,cAAU,KAAK,KAAK,IAAI,KAAK,OAAO;AAAA,EACtC;AACA,MAAI,cAAc,KAAK,cAAc;AAEnC,cAAU,CAAC,cAAc,KAAK,GAAG,GAAG;AAAA,EACtC;AAEA,MAAI,QAAQ,KAAK,CAAC;AAClB,MAAI,OAAO,KAAK,MAAM,CAAC;AACvB,MAAI;AACJ,MAAI;AACJ,kBAAgB;AAEhB,WAAS,KAAK,YAAY,CAAC,QAAQ,GAAG;AACtC,WAAS,KAAK,YAAY,QAAQ,GAAG;AACrC,kBAAgB;AAChB,gBAAc,QAAQ,QAAQ,CAAC;AAC/B,gBAAc,QAAQ,QAAQ,EAAE;AAEhC,kBAAgB;AAChB,MAAI,SAAS,GAAG;AACd,uBAAmB,CAAC,MAAM;AAAA,EAC5B;AACA,MAAI,SAAS,GAAG;AACd,uBAAmB,MAAM;AAAA,EAC3B;AACA,WAAS,kBAAkB;AACzB,aAAS,MAAM,KAAK,KAAK,IAAI;AAC7B,aAAS,WAAW,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,OAAO;AAAA,EAC1D;AACA,WAAS,cAAc,cAAc,eAAe,SAAS;AAC3D,QAAI,eAAe,GAAG;AAEpB,UAAI,iBAAiB,KAAK,IAAI,eAAe,CAAC,YAAY;AAC1D,UAAI,iBAAiB,GAAG;AACtB,kBAAU,iBAAiB,SAAS,GAAG,GAAG;AAC1C,YAAI,WAAW,iBAAiB;AAChC,YAAI,WAAW,GAAG;AAChB,sBAAY,CAAC,WAAW,SAAS,CAAC;AAAA,QACpC;AAAA,MACF,OAAO;AACL,oBAAY,CAAC,eAAe,SAAS,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,WAAS,UAAUC,QAAO,OAAO,KAAK;AACpC,QAAIA,WAAU,GAAG;AACf,iBAAW;AAAA,IACb;AACA,aAASC,KAAI,OAAOA,KAAI,KAAKA,MAAK;AAChC,UAAIC,QAAO,KAAKD,EAAC;AACjB,UAAIE,QAAOD,MAAK;AAChB,MAAAC,MAAK,KAAK,KAAKH;AACf,MAAAE,MAAK,MAAM,KAAK,KAAKF;AAAA,IACvB;AAAA,EACF;AAEA,WAAS,YAAYA,QAAO,kBAAkB;AAC5C,QAAI,OAAO,CAAC;AACZ,QAAI,YAAY;AAChB,aAASC,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC5B,UAAI,eAAe,KAAKA,KAAI,CAAC,EAAE;AAC/B,UAAI,MAAM,KAAK,IAAI,KAAKA,EAAC,EAAE,KAAK,KAAK,IAAI,aAAa,KAAK,IAAI,aAAa,OAAO,GAAG,CAAC;AACvF,WAAK,KAAK,GAAG;AACb,mBAAa;AAAA,IACf;AACA,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,QAAI,iBAAiB,KAAK,IAAI,KAAK,IAAID,MAAK,IAAI,WAAW,gBAAgB;AAC3E,QAAIA,SAAQ,GAAG;AACb,eAASC,KAAI,GAAGA,KAAI,MAAM,GAAGA,MAAK;AAEhC,YAAI,WAAW,KAAKA,EAAC,IAAI;AAEzB,kBAAU,UAAU,GAAGA,KAAI,CAAC;AAAA,MAC9B;AAAA,IACF,OAAO;AAEL,eAASA,KAAI,MAAM,GAAGA,KAAI,GAAGA,MAAK;AAEhC,YAAI,WAAW,KAAKA,KAAI,CAAC,IAAI;AAC7B,kBAAU,CAAC,UAAUA,IAAG,GAAG;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAKA,WAAS,mBAAmBD,QAAO;AACjC,QAAI,MAAMA,SAAQ,IAAI,KAAK;AAC3B,IAAAA,SAAQ,KAAK,IAAIA,MAAK;AACtB,QAAI,mBAAmB,KAAK,KAAKA,UAAS,MAAM,EAAE;AAClD,aAASC,KAAI,GAAGA,KAAI,MAAM,GAAGA,MAAK;AAChC,UAAI,MAAM,GAAG;AAEX,kBAAU,kBAAkB,GAAGA,KAAI,CAAC;AAAA,MACtC,OAAO;AAEL,kBAAU,CAAC,kBAAkB,MAAMA,KAAI,GAAG,GAAG;AAAA,MAC/C;AACA,MAAAD,UAAS;AACT,UAAIA,UAAS,GAAG;AACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAIO,SAAS,eAAe,MAAM,WAAW,YAKhD,cAAc;AACZ,SAAO,YAAY,MAAM,KAAK,SAAS,WAAW,YAAY,YAAY;AAC5E;AAIO,SAAS,eAAe,MAAM,UAAU,aAE/C,cAAc;AACZ,SAAO,YAAY,MAAM,KAAK,UAAU,UAAU,aAAa,YAAY;AAC7E;AACO,SAAS,YAAY,WAAW;AACrC,MAAI,kBAAkB,CAAC;AAEvB,YAAU,KAAK,SAAU,GAAG,GAAG;AAC7B,WAAO,EAAE,WAAW,EAAE;AAAA,EACxB,CAAC;AACD,MAAI,aAAa,IAAI,qBAAa,GAAG,GAAG,GAAG,CAAC;AAC5C,WAAS,OAAO,IAAI;AAClB,QAAI,CAAC,GAAG,QAAQ;AAEd,UAAI,gBAAgB,GAAG,YAAY,UAAU;AAC7C,UAAI,cAAc,UAAU,MAAM;AAChC,sBAAc,SAAS;AAAA,MACzB;AAAA,IACF;AACA,OAAG,SAAS;AAAA,EACd;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,YAAY,UAAU,CAAC;AAC3B,QAAI,gBAAgB,UAAU;AAC9B,QAAI,YAAY,UAAU;AAC1B,QAAI,YAAY,UAAU;AAC1B,QAAI,QAAQ,UAAU;AACtB,QAAI,YAAY,UAAU;AAC1B,eAAW,KAAK,UAAU,IAAI;AAE9B,eAAW,SAAS;AACpB,eAAW,UAAU;AACrB,eAAW,KAAK;AAChB,eAAW,KAAK;AAChB,QAAI,MAAM,UAAU;AACpB,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAI,gBAAgB,gBAAgB,CAAC;AAErC,UAAI,CAAC,WAAW,UAAU,cAAc,IAAI,GAAG;AAC7C;AAAA,MACF;AACA,UAAI,iBAAiB,cAAc,aAAa;AAE9C,qBAAa;AACb;AAAA,MACF;AACA,UAAI,CAAC,cAAc,KAAK;AAEtB,sBAAc,MAAM,IAAI,6BAAqB,cAAc,WAAW,cAAc,SAAS;AAAA,MAC/F;AACA,UAAI,CAAC,KAAK;AAER,cAAM,IAAI,6BAAqB,WAAW,SAAS;AAAA,MACrD;AACA,UAAI,IAAI,UAAU,cAAc,GAAG,GAAG;AACpC,qBAAa;AACb;AAAA,MACF;AAAA,IACF;AAEA,QAAI,YAAY;AACd,aAAO,KAAK;AACZ,mBAAa,OAAO,SAAS;AAAA,IAC/B,OAAO;AACL,YAAM,KAAK,UAAU,UAAU,YAAY,MAAM;AACjD,mBAAa,UAAU,KAAK,UAAU,UAAU,YAAY,gBAAgB;AAC5E,sBAAgB,KAAK,SAAS;AAAA,IAChC;AAAA,EACF;AACF;", "names": ["<PERSON><PERSON><PERSON><PERSON>", "map", "DimensionUserOuput", "SeriesDimensionDefine", "SeriesDataSchema", "isObject", "map", "SeriesData", "layout", "min", "max", "Region", "GeoJSONPolygonGeometry", "GeoJSONLineStringGeometry", "GeoJSONRegion", "min", "max", "GeoSVGRegion", "Scale", "OrdinalMeta", "map", "scale", "contain", "OrdinalScale", "contain", "IntervalScale", "contain", "min", "max", "key", "min", "scale", "parsePercent", "data", "TimeScale", "contain", "unit", "levelTicks", "i", "LogScale", "contain", "ScaleRawExtentInfo", "scale", "min", "max", "scale", "scale", "min", "max", "inner", "tickValue", "Axis", "scale", "min", "max", "clamp", "AxisModelCommonMixin", "delta", "i", "item", "rect"]}