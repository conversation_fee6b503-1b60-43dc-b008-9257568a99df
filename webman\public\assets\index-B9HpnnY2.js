import{g as D,_ as re,h as me,t as C,N as de,c as pe}from"./index-DkGLNqVb.js";import{M as I}from"./@arco-design-uttiljWv.js";import{r as _,a as F,o as _e,h as a,n as B,k as s,m as c,t,l as e,y as f,j as u,a1 as k,z as g,p as M,F as ue,P as fe}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const z={getPageList(h={}){return D({url:"/core/attachment/index",method:"get",params:h})},destroy(h){return D({url:"/core/attachment/destroy",method:"delete",data:h})}},ge={class:"ma-content-block lg:flex justify-between"},he={class:"w-full p-4 pr-4 border-r border-gray-100 lg:w-2/12"},ve={class:"lg:w-10/12 w-full"},ke={class:"actions"},we=["onClick"],ye={class:"action"},be={name:"system:attachment"},xe=Object.assign(be,{setup(h){const S=me().data,r=_(),d=_("list"),w=_(["all"]),L=_([]),p=_([]),P=async n=>{w.value=n;const o=n[0]==="all"?void 0:n[0];m.value.mime_type=o,r.value.refresh()},$=()=>{d.value=d.value==="list"?"window":"list"},N=async n=>{const o=await pe.downloadById(n.id);o?(C.download(o,n.origin_name),I.success("请求成功，文件开始下载")):I.error("文件下载失败")},q=n=>{p.value=n,r.value.setSelecteds(n)},E=()=>{r.value.getTableData().map(n=>p.value.push(n.id)),r.value.setSelecteds(p.value)},R=()=>{p.value=[],r.value.clearSelected()},m=_({origin_name:"",mime_type:"",storage_mode:"",create_time:[]}),T=F({api:z.getPageList,rowSelection:{showCheckedAll:!0},singleLine:!0,delete:{show:!0,auth:["/core/attachment/destroy"],func:async n=>{var v;(await z.destroy(n)).code===200&&(I.success("删除成功！"),(v=r.value)==null||v.refresh())}}}),K=F([{title:"预览",dataIndex:"url",width:80},{title:"存储名称",dataIndex:"object_name",width:220},{title:"原文件名",dataIndex:"origin_name",width:150},{title:"存储模式",dataIndex:"storage_mode",dict:"upload_mode",width:100},{title:"资源类型",dataIndex:"mime_type",width:130},{title:"存储目录",dataIndex:"storage_path",width:130},{title:"文件大小",dataIndex:"size_info",width:130},{title:"上传时间",dataIndex:"create_time",width:180}]);return _e(async()=>{var o;const n=S.attachment_type;L.value=[{label:"所有",value:"all"},...n],(o=r.value)==null||o.refresh()}),(n,o)=>{const v=a("sa-tree-slider"),O=a("a-input"),y=a("a-form-item"),b=a("a-col"),G=a("sa-select"),H=a("a-range-picker"),J=a("icon-select-all"),x=a("a-button"),Q=a("icon-eraser"),W=a("a-input-group"),X=a("icon-apps"),Y=a("icon-list"),V=a("a-tooltip"),Z=a("icon-check"),ee=a("a-tag"),te=a("a-checkbox"),U=a("icon-download"),oe=a("icon-info-circle"),j=a("a-image"),ae=a("a-space"),ne=a("a-image-preview-group"),le=a("a-checkbox-group"),ie=a("a-avatar"),se=a("a-link"),ce=a("sa-table");return s(),B("div",ge,[c("div",he,[t(v,{data:L.value,border:!1,"search-placeholder":"搜索资源类型","field-names":{title:"label",key:"value"},onClick:P,icon:"icon-folder",modelValue:w.value,"onUpdate:modelValue":o[0]||(o[0]=l=>w.value=l)},null,8,["data","modelValue"])]),c("div",ve,[t(ce,{ref_key:"crudRef",ref:r,options:T,columns:K,searchForm:m.value},{tableSearch:e(()=>[t(b,{sm:7,xs:24},{default:e(()=>[t(y,{field:"origin_name",label:"原文件名"},{default:e(()=>[t(O,{modelValue:m.value.origin_name,"onUpdate:modelValue":o[1]||(o[1]=l=>m.value.origin_name=l),placeholder:"请输入原文件名","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),t(b,{sm:7,xs:24},{default:e(()=>[t(y,{field:"storage_mode",label:"存储模式"},{default:e(()=>[t(G,{modelValue:m.value.storage_mode,"onUpdate:modelValue":o[2]||(o[2]=l=>m.value.storage_mode=l),dict:"upload_mode",placeholder:"请选择存储模式"},null,8,["modelValue"])]),_:1})]),_:1}),t(b,{sm:10,xs:24},{default:e(()=>[t(y,{field:"create_time",label:"上传时间"},{default:e(()=>[t(H,{modelValue:m.value.create_time,"onUpdate:modelValue":o[3]||(o[3]=l=>m.value.create_time=l)},null,8,["modelValue"])]),_:1})]),_:1})]),tableAfterButtons:e(()=>[d.value==="window"?(s(),u(W,{key:0},{default:e(()=>[t(x,{onClick:E},{icon:e(()=>[t(J)]),default:e(()=>[o[5]||(o[5]=f("全选 "))]),_:1}),t(x,{onClick:R},{icon:e(()=>[t(Q)]),default:e(()=>[o[6]||(o[6]=f("清除 "))]),_:1})]),_:1})):M("",!0)]),tools:e(()=>[t(V,{content:d.value==="list"?"切换橱窗模式":"切换列表模式"},{default:e(()=>[t(x,{shape:"circle",onClick:$},{default:e(()=>[d.value==="list"?(s(),u(X,{key:0})):(s(),u(Y,{key:1}))]),_:1})]),_:1},8,["content"])]),crudContent:e(l=>[d.value==="window"?(s(),u(le,{key:0,modelValue:p.value,"onUpdate:modelValue":o[4]||(o[4]=i=>p.value=i),onChange:q},{default:e(()=>[t(ne,{infinite:""},{default:e(()=>[t(ae,{class:"window-list"},{default:e(()=>[(s(!0),B(ue,null,fe(l.data,i=>(s(),B("div",{key:i.id,class:"mb-2 image-content"},[t(te,{value:i.id,class:"checkbox"},{checkbox:e(({checked:A})=>[t(ee,{checked:A,color:"arcoblue",checkable:""},{default:e(()=>[t(Z),o[7]||(o[7]=f(" 选择"))]),_:2},1032,["checked"])]),_:2},1032,["value"]),t(j,{width:"190",height:"190","show-loader":"",title:i.origin_name,description:`大小：${i.size_info}`,src:/image/g.test(i.mime_type)?i.url:k(de)},{extra:e(()=>[c("div",ke,[t(V,{content:"下载此文件"},{default:e(()=>[c("span",{class:"action",onClick:A=>N(i)},[t(U)],8,we)]),_:2},1024),t(V,null,{content:e(()=>[c("div",null,"存储名称："+g(i.object_name),1),c("div",null,"存储目录："+g(i.storage_path),1),c("div",null,"上传时间："+g(i.create_time),1),c("div",null,"存储模式："+g(k(C).getLabel(i.storage_mode,k(S).upload_mode)),1)]),default:e(()=>[c("span",ye,[t(oe)])]),_:2},1024)])]),_:2},1032,["title","description","src"])]))),128))]),_:2},1024)]),_:2},1024)]),_:2},1032,["modelValue"])):M("",!0)]),url:e(({record:l})=>[/image/g.test(l.mime_type)?(s(),u(j,{key:0,class:"list-image",width:"40px",height:"40px",src:k(C).attachUrl(l.url)},null,8,["src"])):(s(),u(ie,{key:1,shape:"square",style:{top:"0px"}},{default:e(()=>[f(g(l.suffix),1)]),_:2},1024))]),operationBeforeExtend:e(({record:l})=>[t(se,{onClick:i=>N(l)},{default:e(()=>[t(U),o[8]||(o[8]=f(" 下载"))]),_:2},1032,["onClick"])]),_:1},8,["options","columns","searchForm"])])])}}}),Bt=re(xe,[["__scopeId","data-v-bf267136"]]);export{Bt as default};
