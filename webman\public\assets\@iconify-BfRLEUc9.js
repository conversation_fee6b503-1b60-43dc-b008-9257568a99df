import{e as me,X as G}from"./@vue-9ZIPiVZG.js";const se=/^[a-z0-9]+(-[a-z0-9]+)*$/,M=(e,n,o,i="")=>{const t=e.split(":");if(e.slice(0,1)==="@"){if(t.length<2||t.length>3)return null;i=t.shift().slice(1)}if(t.length>3||!t.length)return null;if(t.length>1){const c=t.pop(),l=t.pop(),f={provider:t.length>0?t[0]:i,prefix:l,name:c};return n&&!L(f)?null:f}const s=t[0],r=s.split("-");if(r.length>1){const c={provider:i,prefix:r.shift(),name:r.join("-")};return n&&!L(c)?null:c}if(o&&i===""){const c={provider:i,prefix:"",name:s};return n&&!L(c,o)?null:c}return null},L=(e,n)=>e?!!((n&&e.prefix===""||e.prefix)&&e.name):!1,re=Object.freeze({left:0,top:0,width:16,height:16}),A=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),N=Object.freeze({...re,...A}),z=Object.freeze({...N,body:"",hidden:!1});function ye(e,n){const o={};!e.hFlip!=!n.hFlip&&(o.hFlip=!0),!e.vFlip!=!n.vFlip&&(o.vFlip=!0);const i=((e.rotate||0)+(n.rotate||0))%4;return i&&(o.rotate=i),o}function B(e,n){const o=ye(e,n);for(const i in z)i in A?i in e&&!(i in o)&&(o[i]=A[i]):i in n?o[i]=n[i]:i in e&&(o[i]=e[i]);return o}function be(e,n){const o=e.icons,i=e.aliases||Object.create(null),t=Object.create(null);function s(r){if(o[r])return t[r]=[];if(!(r in t)){t[r]=null;const c=i[r]&&i[r].parent,l=c&&s(c);l&&(t[r]=[c].concat(l))}return t[r]}return Object.keys(o).concat(Object.keys(i)).forEach(s),t}function xe(e,n,o){const i=e.icons,t=e.aliases||Object.create(null);let s={};function r(c){s=B(i[c]||t[c],s)}return r(n),o.forEach(r),B(e,s)}function ce(e,n){const o=[];if(typeof e!="object"||typeof e.icons!="object")return o;e.not_found instanceof Array&&e.not_found.forEach(t=>{n(t,null),o.push(t)});const i=be(e);for(const t in i){const s=i[t];s&&(n(t,xe(e,t,s)),o.push(t))}return o}const Ie={provider:"",aliases:{},not_found:{},...re};function D(e,n){for(const o in n)if(o in e&&typeof e[o]!=typeof n[o])return!1;return!0}function le(e){if(typeof e!="object"||e===null)return null;const n=e;if(typeof n.prefix!="string"||!e.icons||typeof e.icons!="object"||!D(e,Ie))return null;const o=n.icons;for(const t in o){const s=o[t];if(!t||typeof s.body!="string"||!D(s,z))return null}const i=n.aliases||Object.create(null);for(const t in i){const s=i[t],r=s.parent;if(!t||typeof r!="string"||!o[r]&&!i[r]||!D(s,z))return null}return n}const K=Object.create(null);function we(e,n){return{provider:e,prefix:n,icons:Object.create(null),missing:new Set}}function k(e,n){const o=K[e]||(K[e]=Object.create(null));return o[n]||(o[n]=we(e,n))}function fe(e,n){return le(n)?ce(n,(o,i)=>{i?e.icons[o]=i:e.missing.add(o)}):[]}function ve(e,n,o){try{if(typeof o.body=="string")return e.icons[n]={...o},!0}catch{}return!1}let j=!1;function ue(e){return typeof e=="boolean"&&(j=e),j}function Se(e){const n=typeof e=="string"?M(e,!0,j):e;if(n){const o=k(n.provider,n.prefix),i=n.name;return o.icons[i]||(o.missing.has(i)?null:void 0)}}function ke(e,n){const o=M(e,!0,j);if(!o)return!1;const i=k(o.provider,o.prefix);return n?ve(i,o.name,n):(i.missing.add(o.name),!0)}function Te(e,n){if(typeof e!="object")return!1;if(typeof n!="string"&&(n=e.provider||""),j&&!n&&!e.prefix){let t=!1;return le(e)&&(e.prefix="",ce(e,(s,r)=>{ke(s,r)&&(t=!0)})),t}const o=e.prefix;if(!L({prefix:o,name:"a"}))return!1;const i=k(n,o);return!!fe(i,e)}const ae=Object.freeze({width:null,height:null}),de=Object.freeze({...ae,...A}),Ce=/(-?[0-9.]*[0-9]+[0-9.]*)/g,Pe=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function W(e,n,o){if(n===1)return e;if(o=o||100,typeof e=="number")return Math.ceil(e*n*o)/o;if(typeof e!="string")return e;const i=e.split(Ce);if(i===null||!i.length)return e;const t=[];let s=i.shift(),r=Pe.test(s);for(;;){if(r){const c=parseFloat(s);isNaN(c)?t.push(s):t.push(Math.ceil(c*n*o)/o)}else t.push(s);if(s=i.shift(),s===void 0)return t.join("");r=!r}}function je(e,n="defs"){let o="";const i=e.indexOf("<"+n);for(;i>=0;){const t=e.indexOf(">",i),s=e.indexOf("</"+n);if(t===-1||s===-1)break;const r=e.indexOf(">",s);if(r===-1)break;o+=e.slice(t+1,s).trim(),e=e.slice(0,i).trim()+e.slice(r+1)}return{defs:o,content:e}}function Ee(e,n){return e?"<defs>"+e+"</defs>"+n:n}function Le(e,n,o){const i=je(e);return Ee(i.defs,n+i.content+o)}const Fe=e=>e==="unset"||e==="undefined"||e==="none";function Oe(e,n){const o={...N,...e},i={...de,...n},t={left:o.left,top:o.top,width:o.width,height:o.height};let s=o.body;[o,i].forEach(g=>{const u=[],S=g.hFlip,w=g.vFlip;let x=g.rotate;S?w?x+=2:(u.push("translate("+(t.width+t.left).toString()+" "+(0-t.top).toString()+")"),u.push("scale(-1 1)"),t.top=t.left=0):w&&(u.push("translate("+(0-t.left).toString()+" "+(t.height+t.top).toString()+")"),u.push("scale(1 -1)"),t.top=t.left=0);let y;switch(x<0&&(x-=Math.floor(x/4)*4),x=x%4,x){case 1:y=t.height/2+t.top,u.unshift("rotate(90 "+y.toString()+" "+y.toString()+")");break;case 2:u.unshift("rotate(180 "+(t.width/2+t.left).toString()+" "+(t.height/2+t.top).toString()+")");break;case 3:y=t.width/2+t.left,u.unshift("rotate(-90 "+y.toString()+" "+y.toString()+")");break}x%2===1&&(t.left!==t.top&&(y=t.left,t.left=t.top,t.top=y),t.width!==t.height&&(y=t.width,t.width=t.height,t.height=y)),u.length&&(s=Le(s,'<g transform="'+u.join(" ")+'">',"</g>"))});const r=i.width,c=i.height,l=t.width,f=t.height;let a,d;r===null?(d=c===null?"1em":c==="auto"?f:c,a=W(d,l/f)):(a=r==="auto"?l:r,d=c===null?W(a,f/l):c==="auto"?f:c);const p={},m=(g,u)=>{Fe(u)||(p[g]=u.toString())};m("width",a),m("height",d);const I=[t.left,t.top,l,f];return p.viewBox=I.join(" "),{attributes:p,viewBox:I,body:s}}const Ae=/\sid="(\S+)"/g,Me="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let Ne=0;function _e(e,n=Me){const o=[];let i;for(;i=Ae.exec(e);)o.push(i[1]);if(!o.length)return e;const t="suffix"+(Math.random()*16777216|Date.now()).toString(16);return o.forEach(s=>{const r=typeof n=="function"?n(s):n+(Ne++).toString(),c=s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+c+')([")]|\\.[a-z])',"g"),"$1"+r+t+"$3")}),e=e.replace(new RegExp(t,"g"),""),e}const Q=Object.create(null);function De(e,n){Q[e]=n}function $(e){return Q[e]||Q[""]}function U(e){let n;if(typeof e.resources=="string")n=[e.resources];else if(n=e.resources,!(n instanceof Array)||!n.length)return null;return{resources:n,path:e.path||"/",maxURL:e.maxURL||500,rotate:e.rotate||750,timeout:e.timeout||5e3,random:e.random===!0,index:e.index||0,dataAfterTimeout:e.dataAfterTimeout!==!1}}const H=Object.create(null),C=["https://api.simplesvg.com","https://api.unisvg.com"],F=[];for(;C.length>0;)C.length===1||Math.random()>.5?F.push(C.shift()):F.push(C.pop());H[""]=U({resources:["https://api.iconify.design"].concat(F)});function Re(e,n){const o=U(n);return o===null?!1:(H[e]=o,!0)}function V(e){return H[e]}const ze=()=>{let e;try{if(e=fetch,typeof e=="function")return e}catch{}};let X=ze();function Qe(e,n){const o=V(e);if(!o)return 0;let i;if(!o.maxURL)i=0;else{let t=0;o.resources.forEach(r=>{t=Math.max(t,r.length)});const s=n+".json?icons=";i=o.maxURL-t-o.path.length-s.length}return i}function $e(e){return e===404}const qe=(e,n,o)=>{const i=[],t=Qe(e,n),s="icons";let r={type:s,provider:e,prefix:n,icons:[]},c=0;return o.forEach((l,f)=>{c+=l.length+1,c>=t&&f>0&&(i.push(r),r={type:s,provider:e,prefix:n,icons:[]},c=l.length),r.icons.push(l)}),i.push(r),i};function Ue(e){if(typeof e=="string"){const n=V(e);if(n)return n.path}return"/"}const He=(e,n,o)=>{if(!X){o("abort",424);return}let i=Ue(n.provider);switch(n.type){case"icons":{const s=n.prefix,c=n.icons.join(","),l=new URLSearchParams({icons:c});i+=s+".json?"+l.toString();break}case"custom":{const s=n.uri;i+=s.slice(0,1)==="/"?s.slice(1):s;break}default:o("abort",400);return}let t=503;X(e+i).then(s=>{const r=s.status;if(r!==200){setTimeout(()=>{o($e(r)?"abort":"next",r)});return}return t=501,s.json()}).then(s=>{if(typeof s!="object"||s===null){setTimeout(()=>{s===404?o("abort",s):o("next",t)});return}setTimeout(()=>{o("success",s)})}).catch(()=>{o("next",t)})},Ve={prepare:qe,send:He};function Ge(e){const n={loaded:[],missing:[],pending:[]},o=Object.create(null);e.sort((t,s)=>t.provider!==s.provider?t.provider.localeCompare(s.provider):t.prefix!==s.prefix?t.prefix.localeCompare(s.prefix):t.name.localeCompare(s.name));let i={provider:"",prefix:"",name:""};return e.forEach(t=>{if(i.name===t.name&&i.prefix===t.prefix&&i.provider===t.provider)return;i=t;const s=t.provider,r=t.prefix,c=t.name,l=o[s]||(o[s]=Object.create(null)),f=l[r]||(l[r]=k(s,r));let a;c in f.icons?a=n.loaded:r===""||f.missing.has(c)?a=n.missing:a=n.pending;const d={provider:s,prefix:r,name:c};a.push(d)}),n}function he(e,n){e.forEach(o=>{const i=o.loaderCallbacks;i&&(o.loaderCallbacks=i.filter(t=>t.id!==n))})}function Be(e){e.pendingCallbacksFlag||(e.pendingCallbacksFlag=!0,setTimeout(()=>{e.pendingCallbacksFlag=!1;const n=e.loaderCallbacks?e.loaderCallbacks.slice(0):[];if(!n.length)return;let o=!1;const i=e.provider,t=e.prefix;n.forEach(s=>{const r=s.icons,c=r.pending.length;r.pending=r.pending.filter(l=>{if(l.prefix!==t)return!0;const f=l.name;if(e.icons[f])r.loaded.push({provider:i,prefix:t,name:f});else if(e.missing.has(f))r.missing.push({provider:i,prefix:t,name:f});else return o=!0,!0;return!1}),r.pending.length!==c&&(o||he([e],s.id),s.callback(r.loaded.slice(0),r.missing.slice(0),r.pending.slice(0),s.abort))})}))}let Ke=0;function We(e,n,o){const i=Ke++,t=he.bind(null,o,i);if(!n.pending.length)return t;const s={id:i,icons:n,callback:e,abort:t};return o.forEach(r=>{(r.loaderCallbacks||(r.loaderCallbacks=[])).push(s)}),t}function Xe(e,n=!0,o=!1){const i=[];return e.forEach(t=>{const s=typeof t=="string"?M(t,n,o):t;s&&i.push(s)}),i}var Je={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Ye(e,n,o,i){const t=e.resources.length,s=e.random?Math.floor(Math.random()*t):e.index;let r;if(e.random){let h=e.resources.slice(0);for(r=[];h.length>1;){const b=Math.floor(Math.random()*h.length);r.push(h[b]),h=h.slice(0,b).concat(h.slice(b+1))}r=r.concat(h)}else r=e.resources.slice(s).concat(e.resources.slice(0,s));const c=Date.now();let l="pending",f=0,a,d=null,p=[],m=[];typeof i=="function"&&m.push(i);function I(){d&&(clearTimeout(d),d=null)}function g(){l==="pending"&&(l="aborted"),I(),p.forEach(h=>{h.status==="pending"&&(h.status="aborted")}),p=[]}function u(h,b){b&&(m=[]),typeof h=="function"&&m.push(h)}function S(){return{startTime:c,payload:n,status:l,queriesSent:f,queriesPending:p.length,subscribe:u,abort:g}}function w(){l="failed",m.forEach(h=>{h(void 0,a)})}function x(){p.forEach(h=>{h.status==="pending"&&(h.status="aborted")}),p=[]}function y(h,b,T){const E=b!=="success";switch(p=p.filter(v=>v!==h),l){case"pending":break;case"failed":if(E||!e.dataAfterTimeout)return;break;default:return}if(b==="abort"){a=T,w();return}if(E){a=T,p.length||(r.length?_():w());return}if(I(),x(),!e.random){const v=e.resources.indexOf(h.resource);v!==-1&&v!==e.index&&(e.index=v)}l="completed",m.forEach(v=>{v(T)})}function _(){if(l!=="pending")return;I();const h=r.shift();if(h===void 0){if(p.length){d=setTimeout(()=>{I(),l==="pending"&&(x(),w())},e.timeout);return}w();return}const b={status:"pending",resource:h,callback:(T,E)=>{y(b,T,E)}};p.push(b),f++,d=setTimeout(_,e.rotate),o(h,n,b.callback)}return setTimeout(_),S}function pe(e){const n={...Je,...e};let o=[];function i(){o=o.filter(c=>c().status==="pending")}function t(c,l,f){const a=Ye(n,c,l,(d,p)=>{i(),f&&f(d,p)});return o.push(a),a}function s(c){return o.find(l=>c(l))||null}return{query:t,find:s,setIndex:c=>{n.index=c},getIndex:()=>n.index,cleanup:i}}function J(){}const R=Object.create(null);function Ze(e){if(!R[e]){const n=V(e);if(!n)return;const o=pe(n),i={config:n,redundancy:o};R[e]=i}return R[e]}function et(e,n,o){let i,t;if(typeof e=="string"){const s=$(e);if(!s)return o(void 0,424),J;t=s.send;const r=Ze(e);r&&(i=r.redundancy)}else{const s=U(e);if(s){i=pe(s);const r=e.resources?e.resources[0]:"",c=$(r);c&&(t=c.send)}}return!i||!t?(o(void 0,424),J):i.query(n,t,o)().abort}function Y(){}function tt(e){e.iconsLoaderFlag||(e.iconsLoaderFlag=!0,setTimeout(()=>{e.iconsLoaderFlag=!1,Be(e)}))}function nt(e){const n=[],o=[];return e.forEach(i=>{(i.match(se)?n:o).push(i)}),{valid:n,invalid:o}}function P(e,n,o){function i(){const t=e.pendingIcons;n.forEach(s=>{t&&t.delete(s),e.icons[s]||e.missing.add(s)})}if(o&&typeof o=="object")try{if(!fe(e,o).length){i();return}}catch(t){console.error(t)}i(),tt(e)}function Z(e,n){e instanceof Promise?e.then(o=>{n(o)}).catch(()=>{n(null)}):n(e)}function ot(e,n){e.iconsToLoad?e.iconsToLoad=e.iconsToLoad.concat(n).sort():e.iconsToLoad=n,e.iconsQueueFlag||(e.iconsQueueFlag=!0,setTimeout(()=>{e.iconsQueueFlag=!1;const{provider:o,prefix:i}=e,t=e.iconsToLoad;if(delete e.iconsToLoad,!t||!t.length)return;const s=e.loadIcon;if(e.loadIcons&&(t.length>1||!s)){Z(e.loadIcons(t,i,o),a=>{P(e,t,a)});return}if(s){t.forEach(a=>{const d=s(a,i,o);Z(d,p=>{const m=p?{prefix:i,icons:{[a]:p}}:null;P(e,[a],m)})});return}const{valid:r,invalid:c}=nt(t);if(c.length&&P(e,c,null),!r.length)return;const l=i.match(se)?$(o):null;if(!l){P(e,r,null);return}l.prepare(o,i,r).forEach(a=>{et(o,a,d=>{P(e,a.icons,d)})})}))}const it=(e,n)=>{const o=Xe(e,!0,ue()),i=Ge(o);if(!i.pending.length){let l=!0;return n&&setTimeout(()=>{l&&n(i.loaded,i.missing,i.pending,Y)}),()=>{l=!1}}const t=Object.create(null),s=[];let r,c;return i.pending.forEach(l=>{const{provider:f,prefix:a}=l;if(a===c&&f===r)return;r=f,c=a,s.push(k(f,a));const d=t[f]||(t[f]=Object.create(null));d[a]||(d[a]=[])}),i.pending.forEach(l=>{const{provider:f,prefix:a,name:d}=l,p=k(f,a),m=p.pendingIcons||(p.pendingIcons=new Set);m.has(d)||(m.add(d),t[f][a].push(d))}),s.forEach(l=>{const f=t[l.provider][l.prefix];f.length&&ot(l,f)}),n?We(n,i,s):Y};function st(e,n){const o={...e};for(const i in n){const t=n[i],s=typeof t;i in ae?(t===null||t&&(s==="string"||s==="number"))&&(o[i]=t):s===typeof o[i]&&(o[i]=i==="rotate"?t%4:t)}return o}const rt=/[\s,]+/;function ct(e,n){n.split(rt).forEach(o=>{switch(o.trim()){case"horizontal":e.hFlip=!0;break;case"vertical":e.vFlip=!0;break}})}function lt(e,n=0){const o=e.replace(/^-?[0-9.]*/,"");function i(t){for(;t<0;)t+=4;return t%4}if(o===""){const t=parseInt(e);return isNaN(t)?0:i(t)}else if(o!==e){let t=0;switch(o){case"%":t=25;break;case"deg":t=90}if(t){let s=parseFloat(e.slice(0,e.length-o.length));return isNaN(s)?0:(s=s/t,s%1===0?i(s):0)}}return n}function ft(e,n){let o=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in n)o+=" "+i+'="'+n[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+o+">"+e+"</svg>"}function ut(e){return e.replace(/"/g,"'").replace(/%/g,"%25").replace(/#/g,"%23").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/\s+/g," ")}function at(e){return"data:image/svg+xml,"+ut(e)}function dt(e){return'url("'+at(e)+'")'}const ee={...de,inline:!1},ht={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img"},pt={display:"inline-block"},q={backgroundColor:"currentColor"},ge={backgroundColor:"transparent"},te={Image:"var(--svg)",Repeat:"no-repeat",Size:"100% 100%"},ne={webkitMask:q,mask:q,background:ge};for(const e in ne){const n=ne[e];for(const o in te)n[e+o]=te[o]}const O={};["horizontal","vertical"].forEach(e=>{const n=e.slice(0,1)+"Flip";O[e+"-flip"]=n,O[e.slice(0,1)+"-flip"]=n,O[e+"Flip"]=n});function oe(e){return e+(e.match(/^[-0-9.]+$/)?"px":"")}const ie=(e,n)=>{const o=st(ee,n),i={...ht},t=n.mode||"svg",s={},r=n.style,c=typeof r=="object"&&!(r instanceof Array)?r:{};for(let g in n){const u=n[g];if(u!==void 0)switch(g){case"icon":case"style":case"onLoad":case"mode":case"ssr":break;case"inline":case"hFlip":case"vFlip":o[g]=u===!0||u==="true"||u===1;break;case"flip":typeof u=="string"&&ct(o,u);break;case"color":s.color=u;break;case"rotate":typeof u=="string"?o[g]=lt(u):typeof u=="number"&&(o[g]=u);break;case"ariaHidden":case"aria-hidden":u!==!0&&u!=="true"&&delete i["aria-hidden"];break;default:{const S=O[g];S?(u===!0||u==="true"||u===1)&&(o[S]=!0):ee[g]===void 0&&(i[g]=u)}}}const l=Oe(e,o),f=l.attributes;if(o.inline&&(s.verticalAlign="-0.125em"),t==="svg"){i.style={...s,...c},Object.assign(i,f);let g=0,u=n.id;return typeof u=="string"&&(u=u.replace(/-/g,"_")),i.innerHTML=_e(l.body,u?()=>u+"ID"+g++:"iconifyVue"),G("svg",i)}const{body:a,width:d,height:p}=e,m=t==="mask"||(t==="bg"?!1:a.indexOf("currentColor")!==-1),I=ft(a,{...f,width:d+"",height:p+""});return i.style={...s,"--svg":dt(I),width:oe(f.width),height:oe(f.height),...pt,...m?q:ge,...c},G("span",i)};ue(!0);De("",Ve);if(typeof document<"u"&&typeof window<"u"){const e=window;if(e.IconifyPreload!==void 0){const n=e.IconifyPreload,o="Invalid IconifyPreload syntax.";typeof n=="object"&&n!==null&&(n instanceof Array?n:[n]).forEach(i=>{try{(typeof i!="object"||i===null||i instanceof Array||typeof i.icons!="object"||typeof i.prefix!="string"||!Te(i))&&console.error(o)}catch{console.error(o)}})}if(e.IconifyProviders!==void 0){const n=e.IconifyProviders;if(typeof n=="object"&&n!==null)for(let o in n){const i="IconifyProviders["+o+"] is invalid.";try{const t=n[o];if(typeof t!="object"||!t||t.resources===void 0)continue;Re(o,t)||console.error(i)}catch{console.error(i)}}}}const gt={...N,body:""},yt=me({inheritAttrs:!1,data(){return{_name:"",_loadingIcon:null,iconMounted:!1,counter:0}},mounted(){this.iconMounted=!0},unmounted(){this.abortLoading()},methods:{abortLoading(){this._loadingIcon&&(this._loadingIcon.abort(),this._loadingIcon=null)},getIcon(e,n,o){if(typeof e=="object"&&e!==null&&typeof e.body=="string")return this._name="",this.abortLoading(),{data:e};let i;if(typeof e!="string"||(i=M(e,!1,!0))===null)return this.abortLoading(),null;let t=Se(i);if(!t)return(!this._loadingIcon||this._loadingIcon.name!==e)&&(this.abortLoading(),this._name="",t!==null&&(this._loadingIcon={name:e,abort:it([i],()=>{this.counter++})})),null;if(this.abortLoading(),this._name!==e&&(this._name=e,n&&n(e)),o){t=Object.assign({},t);const r=o(t.body,i.name,i.prefix,i.provider);typeof r=="string"&&(t.body=r)}const s=["iconify"];return i.prefix!==""&&s.push("iconify--"+i.prefix),i.provider!==""&&s.push("iconify--"+i.provider),{data:t,classes:s}}},render(){this.counter;const e=this.$attrs,n=this.iconMounted||e.ssr?this.getIcon(e.icon,e.onLoad,e.customise):null;if(!n)return ie(gt,e);let o=e;return n.classes&&(o={...e,class:(typeof e.class=="string"?e.class+" ":"")+n.classes.join(" ")}),ie({...N,...n.data},o)}});export{yt as I};
