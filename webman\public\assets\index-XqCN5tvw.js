import{t as q}from"./index-ybrmzYq5.js";import{a as s}from"./table-BHRaYvrI.js";import H from"./loadTable-hzlR-U5D.js";import J from"./preview-Bqm1bMFJ.js";import K from"./editInfo-DXBGScbZ.js";import Q from"./formDesign-BDBYukjt.js";import{M as l,j as D}from"./@arco-design-uttiljWv.js";import{r as c,c as X,a as A,o as Y,h as r,n as Z,k as u,t as o,G as ee,l as t,a1 as C,y as a,j as y}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./index-D4ERJytk.js";import"./menu-CgiEA4rB.js";import"./dict-C6FxRZf9.js";import"./settingComponent-BvoGVFoj.js";import"./vars-CZGqfX5Y.js";import"./sortablejs-C83syoBY.js";import"./table-BjY8BMqW.js";const te={class:"ma-content-block"},oe={name:"app/saicode/index"},kt=Object.assign(oe,{setup(ne){const p=c(),R=c(),S=c(),I=c(),h=c(),g=c([]),M=n=>g.value=n,j=async(n,e)=>{if(n==="generateCode"){F(e);return}if(n==="generateSync"){D.info({title:"提示",content:"同步将会自动矫正最新字段结构，确定要同步吗？",simple:!1,onBeforeOk:async m=>{(await s.sync(e)).code===200&&(l.success("操作成功"),m(!0)),m(!1)}});return}if(n==="generateFile"){D.info({title:"提示",content:"生成到项目将会覆盖原有文件，确定要生成吗？",simple:!1,onBeforeOk:m=>{E(e),m(!0)}});return}},F=async n=>{l.info("代码生成下载中，请稍后");const e=await s.generateCode({ids:n.toString().split(",")});e?(q.download(e,"saiadmin.zip"),l.success("代码生成成功，开始下载")):l.error("文件下载失败")},E=async n=>{const e=await s.generateFile({id:n});e.code===200&&l.success(e.message)},O=()=>{if(g.value.length===0){l.error("至少要选择一条数据");return}F(g.value.join(","))},d=c({table_name:"",source:""});let v=X(()=>p.value?p.value.isRecovery:!1);const U=A({pk:"id",api:s.getPageList,recycleApi:s.getRecyclePageList,pageLayout:"fixed",rowSelection:{showCheckedAll:!0},operationColumn:!0,operationColumnWidth:300,edit:{show:!0,func:async n=>{R.value.open(n.id)}},delete:{show:!0,func:async n=>{var e;await s.deletes(n),l.success("删除成功！"),(e=p.value)==null||e.refresh()},realAuth:["/core/menu/realDestroy"],realFunc:async n=>{var e;await s.realDestroy(n),l.success("销毁成功！"),(e=p.value)==null||e.refresh()}},recovery:{show:!0,func:async n=>{var e;await s.recoverys(n),l.success("恢复成功！"),(e=p.value)==null||e.refresh()}}}),L=A([{title:"表名称",dataIndex:"table_name",width:160,align:"left"},{title:"表描述",dataIndex:"table_comment",width:120,align:"left"},{title:"模板类型",dataIndex:"stub",width:100},{title:"应用类型",dataIndex:"template",width:100},{title:"应用名称",dataIndex:"namespace",width:100},{title:"类名称",dataIndex:"class_name",width:150},{title:"生成类型",dataIndex:"tpl_category",width:110},{title:"表单类型",dataIndex:"component_type",width:100},{title:"数据源",dataIndex:"source",width:100},{title:"创建时间",dataIndex:"create_time",width:180}]),f=async()=>{var n;(n=p.value)==null||n.refresh()};return Y(async()=>{f()}),(n,e)=>{const m=r("a-input"),k=r("a-form-item"),V=r("a-col"),P=r("icon-code"),B=r("a-button"),T=r("icon-export"),$=r("icon-eye"),w=r("a-link"),G=r("icon-computer"),N=r("icon-double-right"),b=r("a-doption"),z=r("a-dropdown"),_=r("a-tag"),W=r("sa-table");return u(),Z("div",te,[o(W,{ref_key:"crudRef",ref:p,options:U,columns:L,searchForm:d.value,onSelectionChange:M},ee({tableSearch:t(()=>[o(V,{span:8},{default:t(()=>[o(k,{field:"table_name",label:"表名称"},{default:t(()=>[o(m,{modelValue:d.value.table_name,"onUpdate:modelValue":e[0]||(e[0]=i=>d.value.table_name=i),placeholder:"请输入数据表名称"},null,8,["modelValue"])]),_:1})]),_:1}),o(V,{span:8},{default:t(()=>[o(k,{field:"source",label:"数据源"},{default:t(()=>[o(m,{modelValue:d.value.source,"onUpdate:modelValue":e[1]||(e[1]=i=>d.value.source=i),placeholder:"请输入数据源名称"},null,8,["modelValue"])]),_:1})]),_:1})]),tpl_category:t(({record:i})=>[i.tpl_category=="single"?(u(),y(_,{key:0,color:"green"},{default:t(()=>e[11]||(e[11]=[a("单表CRUD")])),_:1})):(u(),y(_,{key:1,color:"red"},{default:t(()=>e[12]||(e[12]=[a("树表CRUD")])),_:1}))]),component_type:t(({record:i})=>[i.component_type==1?(u(),y(_,{key:0,color:"blue"},{default:t(()=>e[13]||(e[13]=[a("Modal")])),_:1})):(u(),y(_,{key:1,color:"orange"},{default:t(()=>e[14]||(e[14]=[a("Drawer")])),_:1}))]),_:2},[C(v)?void 0:{name:"tableAfterButtons",fn:t(()=>[o(B,{type:"outline",onClick:O},{icon:t(()=>[o(P)]),default:t(()=>[e[3]||(e[3]=a("生成代码 "))]),_:1}),o(B,{onClick:e[2]||(e[2]=()=>h.value.open()),type:"outline",status:"success"},{icon:t(()=>[o(T)]),default:t(()=>[e[4]||(e[4]=a("装载数据表 "))]),_:1})]),key:"0"},C(v)?void 0:{name:"operationBeforeExtend",fn:t(({record:i})=>[o(w,{onClick:x=>S.value.open(i.id)},{default:t(()=>[o($),e[5]||(e[5]=a(" 预览"))]),_:2},1032,["onClick"])]),key:"1"},C(v)?void 0:{name:"operationAfterExtend",fn:t(({record:i})=>[o(w,{onClick:x=>I.value.open(i.id)},{default:t(()=>[o(G),e[6]||(e[6]=a(" 设计"))]),_:2},1032,["onClick"]),o(z,{trigger:"hover",onSelect:x=>j(x,i.id)},{content:t(()=>[o(b,{value:"generateFile"},{default:t(()=>e[8]||(e[8]=[a("生成到项目")])),_:1}),o(b,{value:"generateCode"},{default:t(()=>e[9]||(e[9]=[a("代码下载")])),_:1}),o(b,{value:"generateSync"},{default:t(()=>e[10]||(e[10]=[a("字段同步")])),_:1})]),default:t(()=>[o(w,null,{default:t(()=>[o(N),e[7]||(e[7]=a(" 操作"))]),_:1})]),_:2},1032,["onSelect"])]),key:"2"}]),1032,["options","columns","searchForm"]),o(H,{ref_key:"loadTableRef",ref:h,onSuccess:f},null,512),o(J,{ref_key:"previewRef",ref:S},null,512),o(K,{ref_key:"editRef",ref:R,onSuccess:f},null,512),o(Q,{ref_key:"designRef",ref:I,onSuccess:f},null,512)])}}});export{kt as default};
