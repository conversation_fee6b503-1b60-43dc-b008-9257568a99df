<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 多数据源管理器
// +----------------------------------------------------------------------
// | Author: AI Assistant (多数据源版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\database;

use think\facade\Db;
use think\Model;

/**
 * 多数据源管理器
 */
class MultiDatabaseManager
{
    /**
     * 数据库连接映射
     */
    private static $connectionMap = [
        'main' => 'mysql',           // 主数据库
        'read' => 'mysql_read',      // 读库
        'log' => 'mysql_log',        // 日志库
        'cache' => 'mysql_cache',    // 缓存库
        'pgsql' => 'pgsql',          // PostgreSQL
        'sqlite' => 'sqlite',        // SQLite
    ];

    /**
     * 连接状态缓存
     */
    private static $connectionStatus = [];

    /**
     * 获取指定数据库连接
     * @param string $name 连接名称
     * @return \think\db\Query
     */
    public static function connection($name = 'main')
    {
        $connectionName = isset(self::$connectionMap[$name]) ? self::$connectionMap[$name] : $name;
        return Db::connect($connectionName);
    }

    /**
     * 获取主数据库连接
     * @return \think\db\Query
     */
    public static function main()
    {
        return self::connection('main');
    }

    /**
     * 获取读库连接
     * @return \think\db\Query
     */
    public static function read()
    {
        return self::connection('read');
    }

    /**
     * 获取日志数据库连接
     * @return \think\db\Query
     */
    public static function log()
    {
        return self::connection('log');
    }

    /**
     * 获取缓存数据库连接
     * @return \think\db\Query
     */
    public static function cache()
    {
        return self::connection('cache');
    }

    /**
     * 获取PostgreSQL连接
     * @return \think\db\Query
     */
    public static function pgsql()
    {
        return self::connection('pgsql');
    }

    /**
     * 获取SQLite连接
     * @return \think\db\Query
     */
    public static function sqlite()
    {
        return self::connection('sqlite');
    }

    /**
     * 读写分离查询
     * @param string $sql SQL语句
     * @param array $bind 绑定参数
     * @param bool $forceWrite 强制使用写库
     * @return mixed
     */
    public static function query($sql, $bind = array(), $forceWrite = false)
    {
        // 判断是否为写操作
        $isWrite = $forceWrite || self::isWriteOperation($sql);

        if ($isWrite || !env('DB_RW_SEPARATE', false)) {
            return self::main()->query($sql, $bind);
        } else {
            return self::read()->query($sql, $bind);
        }
    }

    /**
     * 读写分离执行
     * @param string $sql SQL语句
     * @param array $bind 绑定参数
     * @return mixed
     */
    public static function execute($sql, $bind = array())
    {
        return self::main()->execute($sql, $bind);
    }

    /**
     * 判断是否为写操作
     * @param string $sql SQL语句
     * @return bool
     */
    private static function isWriteOperation($sql)
    {
        $writeKeywords = ['INSERT', 'UPDATE', 'DELETE', 'REPLACE', 'CREATE', 'DROP', 'ALTER', 'TRUNCATE'];
        $sql = strtoupper(trim($sql));

        foreach ($writeKeywords as $keyword) {
            if (strpos($sql, $keyword) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 事务处理（使用主库）
     * @param callable $callback 回调函数
     * @return mixed
     * @throws \Exception
     */
    public static function transaction(callable $callback)
    {
        return self::main()->transaction($callback);
    }

    /**
     * 检查数据库连接状态
     * @param string $name 连接名称
     * @return bool
     */
    public static function checkConnection($name = 'main')
    {
        try {
            $connection = self::connection($name);
            $connection->query('SELECT 1');
            self::$connectionStatus[$name] = true;
            return true;
        } catch (\Exception $e) {
            self::$connectionStatus[$name] = false;
            return false;
        }
    }

    /**
     * 获取所有连接状态
     * @return array
     */
    public static function getAllConnectionStatus()
    {
        $status = [];

        foreach (self::$connectionMap as $alias => $connection) {
            $status[$alias] = [
                'connection' => $connection,
                'status' => self::checkConnection($alias),
                'last_check' => date('Y-m-d H:i:s')
            ];
        }

        return $status;
    }

    /**
     * 创建日志数据库表
     */
    public static function createLogTables()
    {
        $logDb = self::log();

        // 创建操作日志表
        $logDb->execute("
            CREATE TABLE IF NOT EXISTS log_operation (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL DEFAULT 0,
                username VARCHAR(50) NOT NULL DEFAULT '',
                method VARCHAR(10) NOT NULL DEFAULT '',
                router VARCHAR(500) NOT NULL DEFAULT '',
                service_name VARCHAR(30) NOT NULL DEFAULT '',
                ip VARCHAR(45) NOT NULL DEFAULT '',
                ip_location VARCHAR(255) NOT NULL DEFAULT '',
                request_data TEXT,
                response_code VARCHAR(5) NOT NULL DEFAULT '',
                response_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_username (username),
                INDEX idx_method (method),
                INDEX idx_created_at (created_at),
                INDEX idx_ip (ip)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表'
        ");

        // 创建登录日志表
        $logDb->execute("
            CREATE TABLE IF NOT EXISTS log_login (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL DEFAULT '',
                ip VARCHAR(45) NOT NULL DEFAULT '',
                ip_location VARCHAR(255) NOT NULL DEFAULT '',
                os VARCHAR(50) NOT NULL DEFAULT '',
                browser VARCHAR(50) NOT NULL DEFAULT '',
                status TINYINT NOT NULL DEFAULT 1 COMMENT '1成功 2失败',
                message VARCHAR(50) NOT NULL DEFAULT '',
                login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_ip (ip),
                INDEX idx_status (status),
                INDEX idx_login_time (login_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表'
        ");

        // 创建性能日志表
        $logDb->execute("
            CREATE TABLE IF NOT EXISTS log_performance (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                request_id VARCHAR(32) NOT NULL DEFAULT '',
                uri VARCHAR(500) NOT NULL DEFAULT '',
                method VARCHAR(10) NOT NULL DEFAULT '',
                response_time DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                memory_usage BIGINT NOT NULL DEFAULT 0,
                query_count INT NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_request_id (request_id),
                INDEX idx_uri (uri),
                INDEX idx_response_time (response_time),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='性能日志表'
        ");
    }

    /**
     * 创建缓存数据库表
     */
    public static function createCacheTables()
    {
        $cacheDb = self::cache();

        // 创建缓存表
        $cacheDb->execute("
            CREATE TABLE IF NOT EXISTS cache_data (
                cache_key VARCHAR(255) PRIMARY KEY,
                cache_value LONGTEXT NOT NULL,
                expire_time TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_expire_time (expire_time),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存数据表'
        ");

        // 创建会话表
        $cacheDb->execute("
            CREATE TABLE IF NOT EXISTS cache_session (
                session_id VARCHAR(128) PRIMARY KEY,
                session_data TEXT NOT NULL,
                expire_time TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_expire_time (expire_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话数据表'
        ");
    }

    /**
     * 数据库健康检查
     * @return array
     */
    public static function healthCheck()
    {
        $health = [
            'overall_status' => 'healthy',
            'connections' => [],
            'issues' => []
        ];

        foreach (self::$connectionMap as $alias => $connection) {
            try {
                $startTime = microtime(true);
                $db = self::connection($alias);
                $db->query('SELECT 1');
                $responseTime = (microtime(true) - $startTime) * 1000;

                $health['connections'][$alias] = [
                    'status' => 'healthy',
                    'response_time' => round($responseTime, 2) . 'ms',
                    'connection_name' => $connection
                ];

                if ($responseTime > 1000) {
                    $health['issues'][] = "{$alias} 连接响应时间过长: {$responseTime}ms";
                }

            } catch (\Exception $e) {
                $health['connections'][$alias] = [
                    'status' => 'unhealthy',
                    'error' => $e->getMessage(),
                    'connection_name' => $connection
                ];
                $health['issues'][] = "{$alias} 连接失败: " . $e->getMessage();
                $health['overall_status'] = 'unhealthy';
            }
        }

        return $health;
    }

    /**
     * 获取数据库统计信息
     * @return array
     */
    public static function getStatistics()
    {
        $stats = [];

        foreach (self::$connectionMap as $alias => $connection) {
            try {
                $db = self::connection($alias);

                if (strpos($connection, 'mysql') !== false) {
                    // MySQL统计
                    $result = $db->query('SHOW STATUS LIKE "Threads_connected"');
                    $connections = $result[0]['Value'] ?? 0;

                    $result = $db->query('SHOW STATUS LIKE "Queries"');
                    $queries = $result[0]['Value'] ?? 0;

                    $stats[$alias] = [
                        'type' => 'mysql',
                        'connections' => $connections,
                        'total_queries' => $queries,
                        'status' => 'online'
                    ];
                } else {
                    $stats[$alias] = [
                        'type' => $connection,
                        'status' => 'online'
                    ];
                }

            } catch (\Exception $e) {
                $stats[$alias] = [
                    'type' => $connection,
                    'status' => 'offline',
                    'error' => $e->getMessage()
                ];
            }
        }

        return $stats;
    }
}
