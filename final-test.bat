@echo off
chcp 65001 >nul
echo ========================================
echo    SaiAdmin 项目完整测试脚本
echo ========================================
echo.

echo [1/6] 测试前端服务器...
curl -s -o nul -w "前端服务器状态: %%{http_code} - %%{url_effective}\n" http://localhost:8889/ || echo "❌ 前端服务器连接失败"
echo.

echo [2/6] 测试后端服务器...
curl -s -o nul -w "后端服务器状态: %%{http_code} - %%{url_effective}\n" http://localhost:8787/ || echo "❌ 后端服务器连接失败"
echo.

echo [3/6] 测试数据库连接...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "SELECT 'Database connection successful' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 数据库连接成功
) else (
    echo ❌ 数据库连接失败
)
echo.

echo [4/6] 检查数据库表...
"D:\BtSoft\mysql\MySQL5.7\bin\mysql.exe" -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W saiadmin -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='saiadmin';" 2>nul
if %errorlevel% == 0 (
    echo ✅ 数据库表检查完成
) else (
    echo ❌ 数据库表检查失败
)
echo.

echo [5/6] 测试WebSocket连接...
echo WebSocket服务运行在: ws://localhost:3131/
echo.

echo [6/6] 生成访问链接...
echo ========================================
echo 🎉 SaiAdmin 项目激活完成！
echo ========================================
echo.
echo 📱 前端访问地址:
echo    本地: http://localhost:8889/
echo    网络: http://**************:8889/
echo.
echo 🔧 后端API地址:
echo    本地: http://localhost:8787/
echo    WebSocket: ws://localhost:3131/
echo.
echo 💾 数据库信息:
echo    主机: 127.0.0.1:3306
echo    数据库: saiadmin
echo    用户: root
echo.
echo 📁 管理脚本:
echo    启动: start-dev.bat
echo    测试: final-test.bat
echo    数据库检查: check-mysql.ps1
echo.
echo ========================================
echo 测试完成！按任意键退出...
echo ========================================
pause >nul
