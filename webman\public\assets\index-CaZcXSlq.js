import{h as B,_ as I}from"./index-ybrmzYq5.js";import{r as n,o as N,h as p,j as h,k as s,l as t,n as d,p as w,t as e,m as P,y as a,z as l}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const V={getServerInfo(){return B({url:"/core/system/monitor",method:"get"})}},G={key:0,class:"ma-content-block lg:flex p-4"},H={key:1,class:"flex justify-between w-full redis-info mt-3"},S={class:"echarts hidden lg:block"},U={key:1,class:"ma-content-block lg:flex p-4 mt-3"},A={key:1,class:"flex justify-between w-full redis-info mt-3"},M={class:"echarts hidden lg:block"},O={key:2,class:"ma-content-block lg:flex p-4 mt-3"},q={key:1,class:"flex justify-between w-full redis-info mt-3"},D={__name:"index",setup(E){const k=n({}),y=n({}),i=n({}),r=n({}),_=n({}),z=n({}),f=n(!0),C=async()=>{var g,m,c,u;f.value=!0;const v=await V.getServerInfo();i.value=(g=v.data)==null?void 0:g.cpu,r.value=(m=v.data)==null?void 0:m.memory,_.value=(c=v.data)==null?void 0:c.phpenv,z.value=(u=v.data)==null?void 0:u.disk,k.value={tooltip:{formatter:"{b} : {c}%"},series:[{name:"CPU使用率",type:"gauge",progress:{show:!0},detail:{valueAnimation:!0,formatter:"{value}"},data:[{value:i.value.usage,name:"CPU使用率"}]}]},y.value={tooltip:{formatter:"{b} : {c}%"},series:[{name:"内存使用率",type:"gauge",progress:{show:!0},detail:{valueAnimation:!0,formatter:"{value}"},data:[{value:r.value.rate,name:"内存使用率"}]}]},f.value=!1};return N(async()=>{C()}),(v,g)=>{const m=p("a-skeleton-line"),c=p("a-space"),u=p("a-skeleton"),o=p("a-descriptions-item"),b=p("a-descriptions"),x=p("sa-chart"),j=p("a-layout-content");return s(),h(j,null,{default:t(()=>[i.value?(s(),d("div",G,[f.value?(s(),h(u,{key:0,animation:"",class:"w-full"},{default:t(()=>[e(c,{direction:"vertical",class:"w-full",size:"large"},{default:t(()=>[e(m,{rows:5})]),_:1})]),_:1})):(s(),d("div",H,[e(b,{column:1,size:"large",bordered:"",title:"CPU信息",class:"lg:w-9/12 w-full"},{default:t(()=>[e(o,{label:"型号"},{default:t(()=>[a(l(i.value.name),1)]),_:1}),e(o,{label:"核心数"},{default:t(()=>[a(l(i.value.cores),1)]),_:1}),e(o,{label:"缓存"},{default:t(()=>[a(l(i.value.cache),1)]),_:1}),e(o,{label:"使用率"},{default:t(()=>[a(l(i.value.usage)+"%",1)]),_:1}),e(o,{label:"空闲率"},{default:t(()=>[a(l(i.value.free)+"%",1)]),_:1})]),_:1}),P("div",S,[e(x,{options:k.value,width:"350px",height:"350px"},null,8,["options"])])]))])):w("",!0),r.value?(s(),d("div",U,[f.value?(s(),h(u,{key:0,animation:"",class:"w-full"},{default:t(()=>[e(c,{direction:"vertical",class:"w-full",size:"large"},{default:t(()=>[e(m,{rows:5})]),_:1})]),_:1})):(s(),d("div",A,[e(b,{column:1,size:"large",bordered:"",title:"内存信息",class:"lg:w-9/12 w-full"},{default:t(()=>[e(o,{label:"总内存"},{default:t(()=>[a(l(r.value.total)+"G",1)]),_:1}),e(o,{label:"已使用内存"},{default:t(()=>[a(l(r.value.usage)+"G",1)]),_:1}),e(o,{label:"PHP使用内存"},{default:t(()=>[a(l(r.value.php)+"M",1)]),_:1}),e(o,{label:"空闲内存"},{default:t(()=>[a(l(r.value.free)+"G",1)]),_:1}),e(o,{label:"使用率"},{default:t(()=>[a(l(r.value.rate)+"%",1)]),_:1})]),_:1}),P("div",M,[e(x,{options:y.value,width:"350px",height:"350px"},null,8,["options"])])]))])):w("",!0),_.value?(s(),d("div",O,[f.value?(s(),h(u,{key:0,animation:"",class:"w-full"},{default:t(()=>[e(c,{direction:"vertical",class:"w-full",size:"large"},{default:t(()=>[e(m,{rows:5})]),_:1})]),_:1})):(s(),d("div",q,[e(b,{column:2,size:"large",bordered:"",title:"PHP及环境信息",class:"w-full"},{default:t(()=>[e(o,{label:"操作系统"},{default:t(()=>[a(l(_.value.os),1)]),_:1}),e(o,{label:"PHP版本"},{default:t(()=>[a(l(_.value.php_version),1)]),_:1}),e(o,{label:"系统物理路径"},{default:t(()=>[a(l(_.value.project_path),1)]),_:1})]),_:1})]))])):w("",!0)]),_:1})}}},Ke=I(D,[["__scopeId","data-v-099080da"]]);export{Ke as default};
