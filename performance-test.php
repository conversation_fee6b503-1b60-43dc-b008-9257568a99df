<?php
/**
 * SaiAdmin 性能测试脚本
 * 用于测试系统各个模块的性能表现
 */

class PerformanceTester
{
    private $results = [];
    private $baseUrl = 'http://localhost:8787';
    
    public function __construct()
    {
        echo "🚀 SaiAdmin 性能测试工具\n";
        echo "================================\n\n";
    }
    
    /**
     * 执行完整性能测试
     */
    public function runFullTest()
    {
        $this->testDatabasePerformance();
        $this->testApiPerformance();
        $this->testMemoryUsage();
        $this->generateReport();
    }
    
    /**
     * 测试数据库性能
     */
    private function testDatabasePerformance()
    {
        echo "📊 测试数据库性能...\n";
        
        // 测试连接时间
        $start = microtime(true);
        try {
            $pdo = new PDO(
                'mysql:host=127.0.0.1;port=3306;dbname=saiadmin',
                'root',
                '5GeNi1v7P7Xcur5W',
                [PDO::ATTR_TIMEOUT => 3]
            );
            $connectionTime = (microtime(true) - $start) * 1000;
            $this->results['database']['connection_time'] = round($connectionTime, 2);
            echo "  ✅ 数据库连接时间: {$this->results['database']['connection_time']}ms\n";
        } catch (Exception $e) {
            $this->results['database']['connection_error'] = $e->getMessage();
            echo "  ❌ 数据库连接失败: {$e->getMessage()}\n";
            return;
        }
        
        // 测试简单查询性能
        $start = microtime(true);
        $stmt = $pdo->query('SELECT COUNT(*) FROM sa_system_user');
        $userCount = $stmt->fetchColumn();
        $queryTime = (microtime(true) - $start) * 1000;
        $this->results['database']['simple_query_time'] = round($queryTime, 2);
        echo "  ✅ 简单查询时间: {$this->results['database']['simple_query_time']}ms (用户数: {$userCount})\n";
        
        // 测试复杂查询性能
        $start = microtime(true);
        $stmt = $pdo->query('
            SELECT u.*, d.name as dept_name 
            FROM sa_system_user u 
            LEFT JOIN sa_system_dept d ON u.dept_id = d.id 
            LIMIT 10
        ');
        $complexResults = $stmt->fetchAll();
        $complexQueryTime = (microtime(true) - $start) * 1000;
        $this->results['database']['complex_query_time'] = round($complexQueryTime, 2);
        echo "  ✅ 复杂查询时间: {$this->results['database']['complex_query_time']}ms\n";
        
        // 测试索引效果
        $this->testIndexPerformance($pdo);
    }
    
    /**
     * 测试索引性能
     */
    private function testIndexPerformance($pdo)
    {
        // 测试有索引的查询
        $start = microtime(true);
        $stmt = $pdo->prepare('SELECT * FROM sa_system_user WHERE id = ?');
        $stmt->execute([1]);
        $indexedQueryTime = (microtime(true) - $start) * 1000;
        $this->results['database']['indexed_query_time'] = round($indexedQueryTime, 2);
        echo "  ✅ 索引查询时间: {$this->results['database']['indexed_query_time']}ms\n";
        
        // 测试可能没有索引的查询
        $start = microtime(true);
        $stmt = $pdo->prepare('SELECT * FROM sa_system_user WHERE nickname LIKE ?');
        $stmt->execute(['%admin%']);
        $nonIndexedQueryTime = (microtime(true) - $start) * 1000;
        $this->results['database']['non_indexed_query_time'] = round($nonIndexedQueryTime, 2);
        echo "  ⚠️  模糊查询时间: {$this->results['database']['non_indexed_query_time']}ms\n";
    }
    
    /**
     * 测试API性能
     */
    private function testApiPerformance()
    {
        echo "\n🌐 测试API性能...\n";
        
        $endpoints = [
            '/core/captcha' => 'GET',
            '/' => 'GET'
        ];
        
        foreach ($endpoints as $endpoint => $method) {
            $this->testApiEndpoint($endpoint, $method);
        }
    }
    
    /**
     * 测试单个API端点
     */
    private function testApiEndpoint($endpoint, $method = 'GET')
    {
        $url = $this->baseUrl . $endpoint;
        $times = [];
        
        // 进行5次测试取平均值
        for ($i = 0; $i < 5; $i++) {
            $start = microtime(true);
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_CUSTOMREQUEST => $method,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $responseTime = (microtime(true) - $start) * 1000;
            
            if ($error) {
                echo "  ❌ {$endpoint}: 请求失败 - {$error}\n";
                return;
            }
            
            $times[] = $responseTime;
        }
        
        $avgTime = round(array_sum($times) / count($times), 2);
        $minTime = round(min($times), 2);
        $maxTime = round(max($times), 2);
        
        $this->results['api'][$endpoint] = [
            'avg_time' => $avgTime,
            'min_time' => $minTime,
            'max_time' => $maxTime,
            'http_code' => $httpCode
        ];
        
        $status = $httpCode == 200 ? '✅' : '⚠️';
        echo "  {$status} {$endpoint}: 平均{$avgTime}ms (最小{$minTime}ms, 最大{$maxTime}ms) [HTTP {$httpCode}]\n";
    }
    
    /**
     * 测试内存使用情况
     */
    private function testMemoryUsage()
    {
        echo "\n💾 测试内存使用情况...\n";
        
        $memoryUsage = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        $this->results['memory'] = [
            'current_usage' => $this->formatBytes($memoryUsage),
            'peak_usage' => $this->formatBytes($peakMemory),
            'current_usage_bytes' => $memoryUsage,
            'peak_usage_bytes' => $peakMemory
        ];
        
        echo "  📊 当前内存使用: {$this->results['memory']['current_usage']}\n";
        echo "  📈 峰值内存使用: {$this->results['memory']['peak_usage']}\n";
        
        // 测试大数据处理的内存使用
        $this->testLargeDataMemory();
    }
    
    /**
     * 测试大数据处理内存使用
     */
    private function testLargeDataMemory()
    {
        $beforeMemory = memory_get_usage(true);
        
        // 模拟处理大量数据
        $largeArray = [];
        for ($i = 0; $i < 10000; $i++) {
            $largeArray[] = [
                'id' => $i,
                'name' => 'User ' . $i,
                'email' => 'user' . $i . '@example.com',
                'data' => str_repeat('x', 100)
            ];
        }
        
        $afterMemory = memory_get_usage(true);
        $memoryIncrease = $afterMemory - $beforeMemory;
        
        $this->results['memory']['large_data_test'] = $this->formatBytes($memoryIncrease);
        echo "  🔍 大数据处理内存增长: {$this->results['memory']['large_data_test']}\n";
        
        // 清理内存
        unset($largeArray);
        
        $cleanupMemory = memory_get_usage(true);
        $memoryRecovered = $afterMemory - $cleanupMemory;
        $this->results['memory']['memory_recovered'] = $this->formatBytes($memoryRecovered);
        echo "  🧹 内存回收: {$this->results['memory']['memory_recovered']}\n";
    }
    
    /**
     * 生成性能测试报告
     */
    private function generateReport()
    {
        echo "\n📋 性能测试报告\n";
        echo "================================\n\n";
        
        // 数据库性能评估
        if (isset($this->results['database'])) {
            echo "📊 数据库性能评估:\n";
            $db = $this->results['database'];
            
            if (isset($db['connection_time'])) {
                $connStatus = $db['connection_time'] < 100 ? '✅ 优秀' : ($db['connection_time'] < 500 ? '⚠️ 一般' : '❌ 需优化');
                echo "  连接性能: {$connStatus} ({$db['connection_time']}ms)\n";
            }
            
            if (isset($db['simple_query_time'])) {
                $queryStatus = $db['simple_query_time'] < 10 ? '✅ 优秀' : ($db['simple_query_time'] < 50 ? '⚠️ 一般' : '❌ 需优化');
                echo "  查询性能: {$queryStatus} ({$db['simple_query_time']}ms)\n";
            }
        }
        
        // API性能评估
        if (isset($this->results['api'])) {
            echo "\n🌐 API性能评估:\n";
            foreach ($this->results['api'] as $endpoint => $data) {
                $status = $data['avg_time'] < 200 ? '✅ 优秀' : ($data['avg_time'] < 1000 ? '⚠️ 一般' : '❌ 需优化');
                echo "  {$endpoint}: {$status} (平均{$data['avg_time']}ms)\n";
            }
        }
        
        // 内存使用评估
        if (isset($this->results['memory'])) {
            echo "\n💾 内存使用评估:\n";
            $memory = $this->results['memory'];
            $memoryStatus = $memory['peak_usage_bytes'] < 50*1024*1024 ? '✅ 优秀' : '⚠️ 需关注';
            echo "  内存使用: {$memoryStatus} (峰值: {$memory['peak_usage']})\n";
        }
        
        // 生成JSON报告
        file_put_contents('performance-report.json', json_encode($this->results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n📄 详细报告已保存: performance-report.json\n";
        
        // 提供优化建议
        $this->provideOptimizationSuggestions();
    }
    
    /**
     * 提供优化建议
     */
    private function provideOptimizationSuggestions()
    {
        echo "\n💡 优化建议:\n";
        
        // 数据库优化建议
        if (isset($this->results['database']['connection_time']) && $this->results['database']['connection_time'] > 100) {
            echo "  🔧 数据库连接较慢，建议检查网络延迟或使用连接池\n";
        }
        
        if (isset($this->results['database']['non_indexed_query_time']) && $this->results['database']['non_indexed_query_time'] > 50) {
            echo "  🔧 模糊查询较慢，建议添加全文索引或使用搜索引擎\n";
        }
        
        // API优化建议
        if (isset($this->results['api'])) {
            foreach ($this->results['api'] as $endpoint => $data) {
                if ($data['avg_time'] > 1000) {
                    echo "  🔧 {$endpoint} 响应较慢，建议添加缓存或优化业务逻辑\n";
                }
            }
        }
        
        // 内存优化建议
        if (isset($this->results['memory']['peak_usage_bytes']) && $this->results['memory']['peak_usage_bytes'] > 100*1024*1024) {
            echo "  🔧 内存使用较高，建议优化数据结构或分批处理大数据\n";
        }
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// 执行性能测试
if (php_sapi_name() === 'cli') {
    $tester = new PerformanceTester();
    $tester->runFullTest();
}
