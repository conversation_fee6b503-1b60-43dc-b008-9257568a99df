import {
  CSSIcon,
  Codicon,
  Color,
  ContextKeyExpr,
  Delayer,
  Disposable,
  DisposableStore,
  EditorAction,
  EditorCommand,
  EditorContextKeys,
  Emitter,
  EventHelper,
  HistoryInputBox,
  IClipboardService,
  IContextKeyService,
  IContextViewService,
  IKeybindingService,
  INotificationService,
  IStorageService,
  IThemeService,
  KeybindingsRegistry,
  MenuId,
  MinimapPosition,
  ModelDecorationOptions,
  MultiEditorAction,
  OverviewRulerLane,
  Position,
  Range,
  RawContextKey,
  ReplaceCommand,
  ReplaceCommandThatPreservesSelection,
  RunOnceScheduler,
  Sash,
  SearchParams,
  Selection,
  ThemeIcon,
  TimeoutTimer,
  Widget,
  addDisposableListener,
  addDisposableNonBubblingMouseOutListener,
  alert,
  containsUppercaseCharacter,
  contrastBorder,
  dispose,
  editor<PERSON><PERSON><PERSON><PERSON>,
  editor<PERSON>ind<PERSON>atch<PERSON>order,
  editor<PERSON><PERSON>MatchHighlight,
  editorFindMatchHighlightBorder,
  editor<PERSON><PERSON>RangeHighlight,
  editor<PERSON><PERSON>RangeHighlightBorder,
  editor<PERSON><PERSON><PERSON>Background,
  editor<PERSON><PERSON><PERSON><PERSON>ord<PERSON>,
  editor<PERSON><PERSON>tForeground,
  editor<PERSON><PERSON>tR<PERSON>zeBorder,
  errorForeground,
  escapeRegExpCharacters,
  findFirstInSorted,
  focusBorder,
  format,
  getComputedStyle,
  getDomNodePagePosition,
  getTopLeftOffset,
  getTotalWidth,
  inputActiveOptionBackground,
  inputActiveOptionBorder,
  inputActiveOptionForeground,
  inputBackground,
  inputBorder,
  inputForeground,
  inputValidationErrorBackground,
  inputValidationErrorBorder,
  inputValidationErrorForeground,
  inputValidationInfoBackground,
  inputValidationInfoBorder,
  inputValidationInfoForeground,
  inputValidationWarningBackground,
  inputValidationWarningBorder,
  inputValidationWarningForeground,
  isInDOM,
  isLinux,
  isMacintosh,
  isNative,
  isWindows,
  localize,
  minimapFindMatch,
  onUnexpectedError,
  overviewRulerFindMatchForeground,
  registerEditorAction,
  registerEditorCommand,
  registerEditorContribution,
  registerIcon,
  registerMultiEditorAction,
  registerThemingParticipant,
  themeColorFromId,
  toDisposable,
  toolbarHoverBackground,
  trackFocus,
  widgetClose,
  widgetShadow
} from "./chunk-WBIRFOMM.js";
import "./chunk-LK32TJAX.js";

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findDecorations.js
var FindDecorations = class _FindDecorations {
  constructor(editor) {
    this._editor = editor;
    this._decorations = [];
    this._overviewRulerApproximateDecorations = [];
    this._findScopeDecorationIds = [];
    this._rangeHighlightDecorationId = null;
    this._highlightedDecorationId = null;
    this._startPosition = this._editor.getPosition();
  }
  dispose() {
    this._editor.deltaDecorations(this._allDecorations(), []);
    this._decorations = [];
    this._overviewRulerApproximateDecorations = [];
    this._findScopeDecorationIds = [];
    this._rangeHighlightDecorationId = null;
    this._highlightedDecorationId = null;
  }
  reset() {
    this._decorations = [];
    this._overviewRulerApproximateDecorations = [];
    this._findScopeDecorationIds = [];
    this._rangeHighlightDecorationId = null;
    this._highlightedDecorationId = null;
  }
  getCount() {
    return this._decorations.length;
  }
  /** @deprecated use getFindScopes to support multiple selections */
  getFindScope() {
    if (this._findScopeDecorationIds[0]) {
      return this._editor.getModel().getDecorationRange(this._findScopeDecorationIds[0]);
    }
    return null;
  }
  getFindScopes() {
    if (this._findScopeDecorationIds.length) {
      const scopes = this._findScopeDecorationIds.map((findScopeDecorationId) => this._editor.getModel().getDecorationRange(findScopeDecorationId)).filter((element) => !!element);
      if (scopes.length) {
        return scopes;
      }
    }
    return null;
  }
  getStartPosition() {
    return this._startPosition;
  }
  setStartPosition(newStartPosition) {
    this._startPosition = newStartPosition;
    this.setCurrentFindMatch(null);
  }
  _getDecorationIndex(decorationId) {
    const index = this._decorations.indexOf(decorationId);
    if (index >= 0) {
      return index + 1;
    }
    return 1;
  }
  getCurrentMatchesPosition(desiredRange) {
    let candidates = this._editor.getModel().getDecorationsInRange(desiredRange);
    for (const candidate of candidates) {
      const candidateOpts = candidate.options;
      if (candidateOpts === _FindDecorations._FIND_MATCH_DECORATION || candidateOpts === _FindDecorations._CURRENT_FIND_MATCH_DECORATION) {
        return this._getDecorationIndex(candidate.id);
      }
    }
    return 0;
  }
  setCurrentFindMatch(nextMatch) {
    let newCurrentDecorationId = null;
    let matchPosition = 0;
    if (nextMatch) {
      for (let i = 0, len = this._decorations.length; i < len; i++) {
        let range = this._editor.getModel().getDecorationRange(this._decorations[i]);
        if (nextMatch.equalsRange(range)) {
          newCurrentDecorationId = this._decorations[i];
          matchPosition = i + 1;
          break;
        }
      }
    }
    if (this._highlightedDecorationId !== null || newCurrentDecorationId !== null) {
      this._editor.changeDecorations((changeAccessor) => {
        if (this._highlightedDecorationId !== null) {
          changeAccessor.changeDecorationOptions(this._highlightedDecorationId, _FindDecorations._FIND_MATCH_DECORATION);
          this._highlightedDecorationId = null;
        }
        if (newCurrentDecorationId !== null) {
          this._highlightedDecorationId = newCurrentDecorationId;
          changeAccessor.changeDecorationOptions(this._highlightedDecorationId, _FindDecorations._CURRENT_FIND_MATCH_DECORATION);
        }
        if (this._rangeHighlightDecorationId !== null) {
          changeAccessor.removeDecoration(this._rangeHighlightDecorationId);
          this._rangeHighlightDecorationId = null;
        }
        if (newCurrentDecorationId !== null) {
          let rng = this._editor.getModel().getDecorationRange(newCurrentDecorationId);
          if (rng.startLineNumber !== rng.endLineNumber && rng.endColumn === 1) {
            let lineBeforeEnd = rng.endLineNumber - 1;
            let lineBeforeEndMaxColumn = this._editor.getModel().getLineMaxColumn(lineBeforeEnd);
            rng = new Range(rng.startLineNumber, rng.startColumn, lineBeforeEnd, lineBeforeEndMaxColumn);
          }
          this._rangeHighlightDecorationId = changeAccessor.addDecoration(rng, _FindDecorations._RANGE_HIGHLIGHT_DECORATION);
        }
      });
    }
    return matchPosition;
  }
  set(findMatches, findScopes) {
    this._editor.changeDecorations((accessor) => {
      let findMatchesOptions = _FindDecorations._FIND_MATCH_DECORATION;
      let newOverviewRulerApproximateDecorations = [];
      if (findMatches.length > 1e3) {
        findMatchesOptions = _FindDecorations._FIND_MATCH_NO_OVERVIEW_DECORATION;
        const lineCount = this._editor.getModel().getLineCount();
        const height = this._editor.getLayoutInfo().height;
        const approxPixelsPerLine = height / lineCount;
        const mergeLinesDelta = Math.max(2, Math.ceil(3 / approxPixelsPerLine));
        let prevStartLineNumber = findMatches[0].range.startLineNumber;
        let prevEndLineNumber = findMatches[0].range.endLineNumber;
        for (let i = 1, len = findMatches.length; i < len; i++) {
          const range = findMatches[i].range;
          if (prevEndLineNumber + mergeLinesDelta >= range.startLineNumber) {
            if (range.endLineNumber > prevEndLineNumber) {
              prevEndLineNumber = range.endLineNumber;
            }
          } else {
            newOverviewRulerApproximateDecorations.push({
              range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),
              options: _FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION
            });
            prevStartLineNumber = range.startLineNumber;
            prevEndLineNumber = range.endLineNumber;
          }
        }
        newOverviewRulerApproximateDecorations.push({
          range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),
          options: _FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION
        });
      }
      let newFindMatchesDecorations = new Array(findMatches.length);
      for (let i = 0, len = findMatches.length; i < len; i++) {
        newFindMatchesDecorations[i] = {
          range: findMatches[i].range,
          options: findMatchesOptions
        };
      }
      this._decorations = accessor.deltaDecorations(this._decorations, newFindMatchesDecorations);
      this._overviewRulerApproximateDecorations = accessor.deltaDecorations(this._overviewRulerApproximateDecorations, newOverviewRulerApproximateDecorations);
      if (this._rangeHighlightDecorationId) {
        accessor.removeDecoration(this._rangeHighlightDecorationId);
        this._rangeHighlightDecorationId = null;
      }
      if (this._findScopeDecorationIds.length) {
        this._findScopeDecorationIds.forEach((findScopeDecorationId) => accessor.removeDecoration(findScopeDecorationId));
        this._findScopeDecorationIds = [];
      }
      if (findScopes === null || findScopes === void 0 ? void 0 : findScopes.length) {
        this._findScopeDecorationIds = findScopes.map((findScope) => accessor.addDecoration(findScope, _FindDecorations._FIND_SCOPE_DECORATION));
      }
    });
  }
  matchBeforePosition(position) {
    if (this._decorations.length === 0) {
      return null;
    }
    for (let i = this._decorations.length - 1; i >= 0; i--) {
      let decorationId = this._decorations[i];
      let r = this._editor.getModel().getDecorationRange(decorationId);
      if (!r || r.endLineNumber > position.lineNumber) {
        continue;
      }
      if (r.endLineNumber < position.lineNumber) {
        return r;
      }
      if (r.endColumn > position.column) {
        continue;
      }
      return r;
    }
    return this._editor.getModel().getDecorationRange(this._decorations[this._decorations.length - 1]);
  }
  matchAfterPosition(position) {
    if (this._decorations.length === 0) {
      return null;
    }
    for (let i = 0, len = this._decorations.length; i < len; i++) {
      let decorationId = this._decorations[i];
      let r = this._editor.getModel().getDecorationRange(decorationId);
      if (!r || r.startLineNumber < position.lineNumber) {
        continue;
      }
      if (r.startLineNumber > position.lineNumber) {
        return r;
      }
      if (r.startColumn < position.column) {
        continue;
      }
      return r;
    }
    return this._editor.getModel().getDecorationRange(this._decorations[0]);
  }
  _allDecorations() {
    let result = [];
    result = result.concat(this._decorations);
    result = result.concat(this._overviewRulerApproximateDecorations);
    if (this._findScopeDecorationIds.length) {
      result.push(...this._findScopeDecorationIds);
    }
    if (this._rangeHighlightDecorationId) {
      result.push(this._rangeHighlightDecorationId);
    }
    return result;
  }
};
FindDecorations._CURRENT_FIND_MATCH_DECORATION = ModelDecorationOptions.register({
  description: "current-find-match",
  stickiness: 1,
  zIndex: 13,
  className: "currentFindMatch",
  showIfCollapsed: true,
  overviewRuler: {
    color: themeColorFromId(overviewRulerFindMatchForeground),
    position: OverviewRulerLane.Center
  },
  minimap: {
    color: themeColorFromId(minimapFindMatch),
    position: MinimapPosition.Inline
  }
});
FindDecorations._FIND_MATCH_DECORATION = ModelDecorationOptions.register({
  description: "find-match",
  stickiness: 1,
  zIndex: 10,
  className: "findMatch",
  showIfCollapsed: true,
  overviewRuler: {
    color: themeColorFromId(overviewRulerFindMatchForeground),
    position: OverviewRulerLane.Center
  },
  minimap: {
    color: themeColorFromId(minimapFindMatch),
    position: MinimapPosition.Inline
  }
});
FindDecorations._FIND_MATCH_NO_OVERVIEW_DECORATION = ModelDecorationOptions.register({
  description: "find-match-no-overview",
  stickiness: 1,
  className: "findMatch",
  showIfCollapsed: true
});
FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION = ModelDecorationOptions.register({
  description: "find-match-only-overview",
  stickiness: 1,
  overviewRuler: {
    color: themeColorFromId(overviewRulerFindMatchForeground),
    position: OverviewRulerLane.Center
  }
});
FindDecorations._RANGE_HIGHLIGHT_DECORATION = ModelDecorationOptions.register({
  description: "find-range-highlight",
  stickiness: 1,
  className: "rangeHighlight",
  isWholeLine: true
});
FindDecorations._FIND_SCOPE_DECORATION = ModelDecorationOptions.register({
  description: "find-scope",
  className: "findScope",
  isWholeLine: true
});

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/replaceAllCommand.js
var ReplaceAllCommand = class {
  constructor(editorSelection, ranges, replaceStrings) {
    this._editorSelection = editorSelection;
    this._ranges = ranges;
    this._replaceStrings = replaceStrings;
    this._trackedEditorSelectionId = null;
  }
  getEditOperations(model, builder) {
    if (this._ranges.length > 0) {
      let ops = [];
      for (let i = 0; i < this._ranges.length; i++) {
        ops.push({
          range: this._ranges[i],
          text: this._replaceStrings[i]
        });
      }
      ops.sort((o1, o2) => {
        return Range.compareRangesUsingStarts(o1.range, o2.range);
      });
      let resultOps = [];
      let previousOp = ops[0];
      for (let i = 1; i < ops.length; i++) {
        if (previousOp.range.endLineNumber === ops[i].range.startLineNumber && previousOp.range.endColumn === ops[i].range.startColumn) {
          previousOp.range = previousOp.range.plusRange(ops[i].range);
          previousOp.text = previousOp.text + ops[i].text;
        } else {
          resultOps.push(previousOp);
          previousOp = ops[i];
        }
      }
      resultOps.push(previousOp);
      for (const op of resultOps) {
        builder.addEditOperation(op.range, op.text);
      }
    }
    this._trackedEditorSelectionId = builder.trackSelection(this._editorSelection);
  }
  computeCursorState(model, helper) {
    return helper.getTrackedSelection(this._trackedEditorSelectionId);
  }
};

// node_modules/monaco-editor/esm/vs/base/common/search.js
function buildReplaceStringWithCasePreserved(matches, pattern) {
  if (matches && matches[0] !== "") {
    const containsHyphens = validateSpecificSpecialCharacter(matches, pattern, "-");
    const containsUnderscores = validateSpecificSpecialCharacter(matches, pattern, "_");
    if (containsHyphens && !containsUnderscores) {
      return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, "-");
    } else if (!containsHyphens && containsUnderscores) {
      return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, "_");
    }
    if (matches[0].toUpperCase() === matches[0]) {
      return pattern.toUpperCase();
    } else if (matches[0].toLowerCase() === matches[0]) {
      return pattern.toLowerCase();
    } else if (containsUppercaseCharacter(matches[0][0]) && pattern.length > 0) {
      return pattern[0].toUpperCase() + pattern.substr(1);
    } else if (matches[0][0].toUpperCase() !== matches[0][0] && pattern.length > 0) {
      return pattern[0].toLowerCase() + pattern.substr(1);
    } else {
      return pattern;
    }
  } else {
    return pattern;
  }
}
function validateSpecificSpecialCharacter(matches, pattern, specialCharacter) {
  const doesContainSpecialCharacter = matches[0].indexOf(specialCharacter) !== -1 && pattern.indexOf(specialCharacter) !== -1;
  return doesContainSpecialCharacter && matches[0].split(specialCharacter).length === pattern.split(specialCharacter).length;
}
function buildReplaceStringForSpecificSpecialCharacter(matches, pattern, specialCharacter) {
  const splitPatternAtSpecialCharacter = pattern.split(specialCharacter);
  const splitMatchAtSpecialCharacter = matches[0].split(specialCharacter);
  let replaceString = "";
  splitPatternAtSpecialCharacter.forEach((splitValue, index) => {
    replaceString += buildReplaceStringWithCasePreserved([splitMatchAtSpecialCharacter[index]], splitValue) + specialCharacter;
  });
  return replaceString.slice(0, -1);
}

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/replacePattern.js
var StaticValueReplacePattern = class {
  constructor(staticValue) {
    this.staticValue = staticValue;
    this.kind = 0;
  }
};
var DynamicPiecesReplacePattern = class {
  constructor(pieces) {
    this.pieces = pieces;
    this.kind = 1;
  }
};
var ReplacePattern = class _ReplacePattern {
  constructor(pieces) {
    if (!pieces || pieces.length === 0) {
      this._state = new StaticValueReplacePattern("");
    } else if (pieces.length === 1 && pieces[0].staticValue !== null) {
      this._state = new StaticValueReplacePattern(pieces[0].staticValue);
    } else {
      this._state = new DynamicPiecesReplacePattern(pieces);
    }
  }
  static fromStaticValue(value) {
    return new _ReplacePattern([ReplacePiece.staticValue(value)]);
  }
  get hasReplacementPatterns() {
    return this._state.kind === 1;
  }
  buildReplaceString(matches, preserveCase) {
    if (this._state.kind === 0) {
      if (preserveCase) {
        return buildReplaceStringWithCasePreserved(matches, this._state.staticValue);
      } else {
        return this._state.staticValue;
      }
    }
    let result = "";
    for (let i = 0, len = this._state.pieces.length; i < len; i++) {
      let piece = this._state.pieces[i];
      if (piece.staticValue !== null) {
        result += piece.staticValue;
        continue;
      }
      let match = _ReplacePattern._substitute(piece.matchIndex, matches);
      if (piece.caseOps !== null && piece.caseOps.length > 0) {
        let repl = [];
        let lenOps = piece.caseOps.length;
        let opIdx = 0;
        for (let idx = 0, len2 = match.length; idx < len2; idx++) {
          if (opIdx >= lenOps) {
            repl.push(match.slice(idx));
            break;
          }
          switch (piece.caseOps[opIdx]) {
            case "U":
              repl.push(match[idx].toUpperCase());
              break;
            case "u":
              repl.push(match[idx].toUpperCase());
              opIdx++;
              break;
            case "L":
              repl.push(match[idx].toLowerCase());
              break;
            case "l":
              repl.push(match[idx].toLowerCase());
              opIdx++;
              break;
            default:
              repl.push(match[idx]);
          }
        }
        match = repl.join("");
      }
      result += match;
    }
    return result;
  }
  static _substitute(matchIndex, matches) {
    if (matches === null) {
      return "";
    }
    if (matchIndex === 0) {
      return matches[0];
    }
    let remainder = "";
    while (matchIndex > 0) {
      if (matchIndex < matches.length) {
        let match = matches[matchIndex] || "";
        return match + remainder;
      }
      remainder = String(matchIndex % 10) + remainder;
      matchIndex = Math.floor(matchIndex / 10);
    }
    return "$" + remainder;
  }
};
var ReplacePiece = class _ReplacePiece {
  constructor(staticValue, matchIndex, caseOps) {
    this.staticValue = staticValue;
    this.matchIndex = matchIndex;
    if (!caseOps || caseOps.length === 0) {
      this.caseOps = null;
    } else {
      this.caseOps = caseOps.slice(0);
    }
  }
  static staticValue(value) {
    return new _ReplacePiece(value, -1, null);
  }
  static caseOps(index, caseOps) {
    return new _ReplacePiece(null, index, caseOps);
  }
};
var ReplacePieceBuilder = class {
  constructor(source) {
    this._source = source;
    this._lastCharIndex = 0;
    this._result = [];
    this._resultLen = 0;
    this._currentStaticPiece = "";
  }
  emitUnchanged(toCharIndex) {
    this._emitStatic(this._source.substring(this._lastCharIndex, toCharIndex));
    this._lastCharIndex = toCharIndex;
  }
  emitStatic(value, toCharIndex) {
    this._emitStatic(value);
    this._lastCharIndex = toCharIndex;
  }
  _emitStatic(value) {
    if (value.length === 0) {
      return;
    }
    this._currentStaticPiece += value;
  }
  emitMatchIndex(index, toCharIndex, caseOps) {
    if (this._currentStaticPiece.length !== 0) {
      this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);
      this._currentStaticPiece = "";
    }
    this._result[this._resultLen++] = ReplacePiece.caseOps(index, caseOps);
    this._lastCharIndex = toCharIndex;
  }
  finalize() {
    this.emitUnchanged(this._source.length);
    if (this._currentStaticPiece.length !== 0) {
      this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);
      this._currentStaticPiece = "";
    }
    return new ReplacePattern(this._result);
  }
};
function parseReplaceString(replaceString) {
  if (!replaceString || replaceString.length === 0) {
    return new ReplacePattern(null);
  }
  let caseOps = [];
  let result = new ReplacePieceBuilder(replaceString);
  for (let i = 0, len = replaceString.length; i < len; i++) {
    let chCode = replaceString.charCodeAt(i);
    if (chCode === 92) {
      i++;
      if (i >= len) {
        break;
      }
      let nextChCode = replaceString.charCodeAt(i);
      switch (nextChCode) {
        case 92:
          result.emitUnchanged(i - 1);
          result.emitStatic("\\", i + 1);
          break;
        case 110:
          result.emitUnchanged(i - 1);
          result.emitStatic("\n", i + 1);
          break;
        case 116:
          result.emitUnchanged(i - 1);
          result.emitStatic("	", i + 1);
          break;
        case 117:
        case 85:
        case 108:
        case 76:
          result.emitUnchanged(i - 1);
          result.emitStatic("", i + 1);
          caseOps.push(String.fromCharCode(nextChCode));
          break;
      }
      continue;
    }
    if (chCode === 36) {
      i++;
      if (i >= len) {
        break;
      }
      let nextChCode = replaceString.charCodeAt(i);
      if (nextChCode === 36) {
        result.emitUnchanged(i - 1);
        result.emitStatic("$", i + 1);
        continue;
      }
      if (nextChCode === 48 || nextChCode === 38) {
        result.emitUnchanged(i - 1);
        result.emitMatchIndex(0, i + 1, caseOps);
        caseOps.length = 0;
        continue;
      }
      if (49 <= nextChCode && nextChCode <= 57) {
        let matchIndex = nextChCode - 48;
        if (i + 1 < len) {
          let nextNextChCode = replaceString.charCodeAt(i + 1);
          if (48 <= nextNextChCode && nextNextChCode <= 57) {
            i++;
            matchIndex = matchIndex * 10 + (nextNextChCode - 48);
            result.emitUnchanged(i - 2);
            result.emitMatchIndex(matchIndex, i + 1, caseOps);
            caseOps.length = 0;
            continue;
          }
        }
        result.emitUnchanged(i - 1);
        result.emitMatchIndex(matchIndex, i + 1, caseOps);
        caseOps.length = 0;
        continue;
      }
    }
  }
  return result.finalize();
}

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findModel.js
var CONTEXT_FIND_WIDGET_VISIBLE = new RawContextKey("findWidgetVisible", false);
var CONTEXT_FIND_WIDGET_NOT_VISIBLE = CONTEXT_FIND_WIDGET_VISIBLE.toNegated();
var CONTEXT_FIND_INPUT_FOCUSED = new RawContextKey("findInputFocussed", false);
var CONTEXT_REPLACE_INPUT_FOCUSED = new RawContextKey("replaceInputFocussed", false);
var ToggleCaseSensitiveKeybinding = {
  primary: 512 | 33,
  mac: {
    primary: 2048 | 512 | 33
    /* KeyC */
  }
};
var ToggleWholeWordKeybinding = {
  primary: 512 | 53,
  mac: {
    primary: 2048 | 512 | 53
    /* KeyW */
  }
};
var ToggleRegexKeybinding = {
  primary: 512 | 48,
  mac: {
    primary: 2048 | 512 | 48
    /* KeyR */
  }
};
var ToggleSearchScopeKeybinding = {
  primary: 512 | 42,
  mac: {
    primary: 2048 | 512 | 42
    /* KeyL */
  }
};
var TogglePreserveCaseKeybinding = {
  primary: 512 | 46,
  mac: {
    primary: 2048 | 512 | 46
    /* KeyP */
  }
};
var FIND_IDS = {
  StartFindAction: "actions.find",
  StartFindWithSelection: "actions.findWithSelection",
  StartFindWithArgs: "editor.actions.findWithArgs",
  NextMatchFindAction: "editor.action.nextMatchFindAction",
  PreviousMatchFindAction: "editor.action.previousMatchFindAction",
  NextSelectionMatchFindAction: "editor.action.nextSelectionMatchFindAction",
  PreviousSelectionMatchFindAction: "editor.action.previousSelectionMatchFindAction",
  StartFindReplaceAction: "editor.action.startFindReplaceAction",
  CloseFindWidgetCommand: "closeFindWidget",
  ToggleCaseSensitiveCommand: "toggleFindCaseSensitive",
  ToggleWholeWordCommand: "toggleFindWholeWord",
  ToggleRegexCommand: "toggleFindRegex",
  ToggleSearchScopeCommand: "toggleFindInSelection",
  TogglePreserveCaseCommand: "togglePreserveCase",
  ReplaceOneAction: "editor.action.replaceOne",
  ReplaceAllAction: "editor.action.replaceAll",
  SelectAllMatchesAction: "editor.action.selectAllMatches"
};
var MATCHES_LIMIT = 19999;
var RESEARCH_DELAY = 240;
var FindModelBoundToEditorModel = class _FindModelBoundToEditorModel {
  constructor(editor, state) {
    this._toDispose = new DisposableStore();
    this._editor = editor;
    this._state = state;
    this._isDisposed = false;
    this._startSearchingTimer = new TimeoutTimer();
    this._decorations = new FindDecorations(editor);
    this._toDispose.add(this._decorations);
    this._updateDecorationsScheduler = new RunOnceScheduler(() => this.research(false), 100);
    this._toDispose.add(this._updateDecorationsScheduler);
    this._toDispose.add(this._editor.onDidChangeCursorPosition((e) => {
      if (e.reason === 3 || e.reason === 5 || e.reason === 6) {
        this._decorations.setStartPosition(this._editor.getPosition());
      }
    }));
    this._ignoreModelContentChanged = false;
    this._toDispose.add(this._editor.onDidChangeModelContent((e) => {
      if (this._ignoreModelContentChanged) {
        return;
      }
      if (e.isFlush) {
        this._decorations.reset();
      }
      this._decorations.setStartPosition(this._editor.getPosition());
      this._updateDecorationsScheduler.schedule();
    }));
    this._toDispose.add(this._state.onFindReplaceStateChange((e) => this._onStateChanged(e)));
    this.research(false, this._state.searchScope);
  }
  dispose() {
    this._isDisposed = true;
    dispose(this._startSearchingTimer);
    this._toDispose.dispose();
  }
  _onStateChanged(e) {
    if (this._isDisposed) {
      return;
    }
    if (!this._editor.hasModel()) {
      return;
    }
    if (e.searchString || e.isReplaceRevealed || e.isRegex || e.wholeWord || e.matchCase || e.searchScope) {
      let model = this._editor.getModel();
      if (model.isTooLargeForSyncing()) {
        this._startSearchingTimer.cancel();
        this._startSearchingTimer.setIfNotSet(() => {
          if (e.searchScope) {
            this.research(e.moveCursor, this._state.searchScope);
          } else {
            this.research(e.moveCursor);
          }
        }, RESEARCH_DELAY);
      } else {
        if (e.searchScope) {
          this.research(e.moveCursor, this._state.searchScope);
        } else {
          this.research(e.moveCursor);
        }
      }
    }
  }
  static _getSearchRange(model, findScope) {
    if (findScope) {
      return findScope;
    }
    return model.getFullModelRange();
  }
  research(moveCursor, newFindScope) {
    let findScopes = null;
    if (typeof newFindScope !== "undefined") {
      if (newFindScope !== null) {
        if (!Array.isArray(newFindScope)) {
          findScopes = [newFindScope];
        } else {
          findScopes = newFindScope;
        }
      }
    } else {
      findScopes = this._decorations.getFindScopes();
    }
    if (findScopes !== null) {
      findScopes = findScopes.map((findScope) => {
        if (findScope.startLineNumber !== findScope.endLineNumber) {
          let endLineNumber = findScope.endLineNumber;
          if (findScope.endColumn === 1) {
            endLineNumber = endLineNumber - 1;
          }
          return new Range(findScope.startLineNumber, 1, endLineNumber, this._editor.getModel().getLineMaxColumn(endLineNumber));
        }
        return findScope;
      });
    }
    let findMatches = this._findMatches(findScopes, false, MATCHES_LIMIT);
    this._decorations.set(findMatches, findScopes);
    const editorSelection = this._editor.getSelection();
    let currentMatchesPosition = this._decorations.getCurrentMatchesPosition(editorSelection);
    if (currentMatchesPosition === 0 && findMatches.length > 0) {
      const matchAfterSelection = findFirstInSorted(findMatches.map((match) => match.range), (range) => Range.compareRangesUsingStarts(range, editorSelection) >= 0);
      currentMatchesPosition = matchAfterSelection > 0 ? matchAfterSelection - 1 + 1 : currentMatchesPosition;
    }
    this._state.changeMatchInfo(currentMatchesPosition, this._decorations.getCount(), void 0);
    if (moveCursor && this._editor.getOption(
      35
      /* find */
    ).cursorMoveOnType) {
      this._moveToNextMatch(this._decorations.getStartPosition());
    }
  }
  _hasMatches() {
    return this._state.matchesCount > 0;
  }
  _cannotFind() {
    if (!this._hasMatches()) {
      let findScope = this._decorations.getFindScope();
      if (findScope) {
        this._editor.revealRangeInCenterIfOutsideViewport(
          findScope,
          0
          /* Smooth */
        );
      }
      return true;
    }
    return false;
  }
  _setCurrentFindMatch(match) {
    let matchesPosition = this._decorations.setCurrentFindMatch(match);
    this._state.changeMatchInfo(matchesPosition, this._decorations.getCount(), match);
    this._editor.setSelection(match);
    this._editor.revealRangeInCenterIfOutsideViewport(
      match,
      0
      /* Smooth */
    );
  }
  _prevSearchPosition(before) {
    let isUsingLineStops = this._state.isRegex && (this._state.searchString.indexOf("^") >= 0 || this._state.searchString.indexOf("$") >= 0);
    let { lineNumber, column } = before;
    let model = this._editor.getModel();
    if (isUsingLineStops || column === 1) {
      if (lineNumber === 1) {
        lineNumber = model.getLineCount();
      } else {
        lineNumber--;
      }
      column = model.getLineMaxColumn(lineNumber);
    } else {
      column--;
    }
    return new Position(lineNumber, column);
  }
  _moveToPrevMatch(before, isRecursed = false) {
    if (!this._state.canNavigateBack()) {
      const nextMatchRange = this._decorations.matchAfterPosition(before);
      if (nextMatchRange) {
        this._setCurrentFindMatch(nextMatchRange);
      }
      return;
    }
    if (this._decorations.getCount() < MATCHES_LIMIT) {
      let prevMatchRange = this._decorations.matchBeforePosition(before);
      if (prevMatchRange && prevMatchRange.isEmpty() && prevMatchRange.getStartPosition().equals(before)) {
        before = this._prevSearchPosition(before);
        prevMatchRange = this._decorations.matchBeforePosition(before);
      }
      if (prevMatchRange) {
        this._setCurrentFindMatch(prevMatchRange);
      }
      return;
    }
    if (this._cannotFind()) {
      return;
    }
    let findScope = this._decorations.getFindScope();
    let searchRange = _FindModelBoundToEditorModel._getSearchRange(this._editor.getModel(), findScope);
    if (searchRange.getEndPosition().isBefore(before)) {
      before = searchRange.getEndPosition();
    }
    if (before.isBefore(searchRange.getStartPosition())) {
      before = searchRange.getEndPosition();
    }
    let { lineNumber, column } = before;
    let model = this._editor.getModel();
    let position = new Position(lineNumber, column);
    let prevMatch = model.findPreviousMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(
      117
      /* wordSeparators */
    ) : null, false);
    if (prevMatch && prevMatch.range.isEmpty() && prevMatch.range.getStartPosition().equals(position)) {
      position = this._prevSearchPosition(position);
      prevMatch = model.findPreviousMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(
        117
        /* wordSeparators */
      ) : null, false);
    }
    if (!prevMatch) {
      return;
    }
    if (!isRecursed && !searchRange.containsRange(prevMatch.range)) {
      return this._moveToPrevMatch(prevMatch.range.getStartPosition(), true);
    }
    this._setCurrentFindMatch(prevMatch.range);
  }
  moveToPrevMatch() {
    this._moveToPrevMatch(this._editor.getSelection().getStartPosition());
  }
  _nextSearchPosition(after) {
    let isUsingLineStops = this._state.isRegex && (this._state.searchString.indexOf("^") >= 0 || this._state.searchString.indexOf("$") >= 0);
    let { lineNumber, column } = after;
    let model = this._editor.getModel();
    if (isUsingLineStops || column === model.getLineMaxColumn(lineNumber)) {
      if (lineNumber === model.getLineCount()) {
        lineNumber = 1;
      } else {
        lineNumber++;
      }
      column = 1;
    } else {
      column++;
    }
    return new Position(lineNumber, column);
  }
  _moveToNextMatch(after) {
    if (!this._state.canNavigateForward()) {
      const prevMatchRange = this._decorations.matchBeforePosition(after);
      if (prevMatchRange) {
        this._setCurrentFindMatch(prevMatchRange);
      }
      return;
    }
    if (this._decorations.getCount() < MATCHES_LIMIT) {
      let nextMatchRange = this._decorations.matchAfterPosition(after);
      if (nextMatchRange && nextMatchRange.isEmpty() && nextMatchRange.getStartPosition().equals(after)) {
        after = this._nextSearchPosition(after);
        nextMatchRange = this._decorations.matchAfterPosition(after);
      }
      if (nextMatchRange) {
        this._setCurrentFindMatch(nextMatchRange);
      }
      return;
    }
    let nextMatch = this._getNextMatch(after, false, true);
    if (nextMatch) {
      this._setCurrentFindMatch(nextMatch.range);
    }
  }
  _getNextMatch(after, captureMatches, forceMove, isRecursed = false) {
    if (this._cannotFind()) {
      return null;
    }
    let findScope = this._decorations.getFindScope();
    let searchRange = _FindModelBoundToEditorModel._getSearchRange(this._editor.getModel(), findScope);
    if (searchRange.getEndPosition().isBefore(after)) {
      after = searchRange.getStartPosition();
    }
    if (after.isBefore(searchRange.getStartPosition())) {
      after = searchRange.getStartPosition();
    }
    let { lineNumber, column } = after;
    let model = this._editor.getModel();
    let position = new Position(lineNumber, column);
    let nextMatch = model.findNextMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(
      117
      /* wordSeparators */
    ) : null, captureMatches);
    if (forceMove && nextMatch && nextMatch.range.isEmpty() && nextMatch.range.getStartPosition().equals(position)) {
      position = this._nextSearchPosition(position);
      nextMatch = model.findNextMatch(this._state.searchString, position, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(
        117
        /* wordSeparators */
      ) : null, captureMatches);
    }
    if (!nextMatch) {
      return null;
    }
    if (!isRecursed && !searchRange.containsRange(nextMatch.range)) {
      return this._getNextMatch(nextMatch.range.getEndPosition(), captureMatches, forceMove, true);
    }
    return nextMatch;
  }
  moveToNextMatch() {
    this._moveToNextMatch(this._editor.getSelection().getEndPosition());
  }
  _getReplacePattern() {
    if (this._state.isRegex) {
      return parseReplaceString(this._state.replaceString);
    }
    return ReplacePattern.fromStaticValue(this._state.replaceString);
  }
  replace() {
    if (!this._hasMatches()) {
      return;
    }
    let replacePattern = this._getReplacePattern();
    let selection = this._editor.getSelection();
    let nextMatch = this._getNextMatch(selection.getStartPosition(), true, false);
    if (nextMatch) {
      if (selection.equalsRange(nextMatch.range)) {
        let replaceString = replacePattern.buildReplaceString(nextMatch.matches, this._state.preserveCase);
        let command = new ReplaceCommand(selection, replaceString);
        this._executeEditorCommand("replace", command);
        this._decorations.setStartPosition(new Position(selection.startLineNumber, selection.startColumn + replaceString.length));
        this.research(true);
      } else {
        this._decorations.setStartPosition(this._editor.getPosition());
        this._setCurrentFindMatch(nextMatch.range);
      }
    }
  }
  _findMatches(findScopes, captureMatches, limitResultCount) {
    const searchRanges = (findScopes || [null]).map((scope) => _FindModelBoundToEditorModel._getSearchRange(this._editor.getModel(), scope));
    return this._editor.getModel().findMatches(this._state.searchString, searchRanges, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(
      117
      /* wordSeparators */
    ) : null, captureMatches, limitResultCount);
  }
  replaceAll() {
    if (!this._hasMatches()) {
      return;
    }
    const findScopes = this._decorations.getFindScopes();
    if (findScopes === null && this._state.matchesCount >= MATCHES_LIMIT) {
      this._largeReplaceAll();
    } else {
      this._regularReplaceAll(findScopes);
    }
    this.research(false);
  }
  _largeReplaceAll() {
    const searchParams = new SearchParams(this._state.searchString, this._state.isRegex, this._state.matchCase, this._state.wholeWord ? this._editor.getOption(
      117
      /* wordSeparators */
    ) : null);
    const searchData = searchParams.parseSearchRequest();
    if (!searchData) {
      return;
    }
    let searchRegex = searchData.regex;
    if (!searchRegex.multiline) {
      let mod = "mu";
      if (searchRegex.ignoreCase) {
        mod += "i";
      }
      if (searchRegex.global) {
        mod += "g";
      }
      searchRegex = new RegExp(searchRegex.source, mod);
    }
    const model = this._editor.getModel();
    const modelText = model.getValue(
      1
      /* LF */
    );
    const fullModelRange = model.getFullModelRange();
    const replacePattern = this._getReplacePattern();
    let resultText;
    const preserveCase = this._state.preserveCase;
    if (replacePattern.hasReplacementPatterns || preserveCase) {
      resultText = modelText.replace(searchRegex, function() {
        return replacePattern.buildReplaceString(arguments, preserveCase);
      });
    } else {
      resultText = modelText.replace(searchRegex, replacePattern.buildReplaceString(null, preserveCase));
    }
    let command = new ReplaceCommandThatPreservesSelection(fullModelRange, resultText, this._editor.getSelection());
    this._executeEditorCommand("replaceAll", command);
  }
  _regularReplaceAll(findScopes) {
    const replacePattern = this._getReplacePattern();
    let matches = this._findMatches(
      findScopes,
      replacePattern.hasReplacementPatterns || this._state.preserveCase,
      1073741824
      /* MAX_SAFE_SMALL_INTEGER */
    );
    let replaceStrings = [];
    for (let i = 0, len = matches.length; i < len; i++) {
      replaceStrings[i] = replacePattern.buildReplaceString(matches[i].matches, this._state.preserveCase);
    }
    let command = new ReplaceAllCommand(this._editor.getSelection(), matches.map((m) => m.range), replaceStrings);
    this._executeEditorCommand("replaceAll", command);
  }
  selectAllMatches() {
    if (!this._hasMatches()) {
      return;
    }
    let findScopes = this._decorations.getFindScopes();
    let matches = this._findMatches(
      findScopes,
      false,
      1073741824
      /* MAX_SAFE_SMALL_INTEGER */
    );
    let selections = matches.map((m) => new Selection(m.range.startLineNumber, m.range.startColumn, m.range.endLineNumber, m.range.endColumn));
    let editorSelection = this._editor.getSelection();
    for (let i = 0, len = selections.length; i < len; i++) {
      let sel = selections[i];
      if (sel.equalsRange(editorSelection)) {
        selections = [editorSelection].concat(selections.slice(0, i)).concat(selections.slice(i + 1));
        break;
      }
    }
    this._editor.setSelections(selections);
  }
  _executeEditorCommand(source, command) {
    try {
      this._ignoreModelContentChanged = true;
      this._editor.pushUndoStop();
      this._editor.executeCommand(source, command);
      this._editor.pushUndoStop();
    } finally {
      this._ignoreModelContentChanged = false;
    }
  }
};

// node_modules/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.js
import "C:/Users/<USER>/Desktop/sai/saiadmin-boot/saiadmin-vue/node_modules/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.css";
var defaultOpts = {
  inputActiveOptionBorder: Color.fromHex("#007ACC00"),
  inputActiveOptionForeground: Color.fromHex("#FFFFFF"),
  inputActiveOptionBackground: Color.fromHex("#0E639C50")
};
var Checkbox = class extends Widget {
  constructor(opts) {
    super();
    this._onChange = this._register(new Emitter());
    this.onChange = this._onChange.event;
    this._onKeyDown = this._register(new Emitter());
    this.onKeyDown = this._onKeyDown.event;
    this._opts = Object.assign(Object.assign({}, defaultOpts), opts);
    this._checked = this._opts.isChecked;
    const classes = ["monaco-custom-checkbox"];
    if (this._opts.icon) {
      classes.push(...CSSIcon.asClassNameArray(this._opts.icon));
    }
    if (this._opts.actionClassName) {
      classes.push(...this._opts.actionClassName.split(" "));
    }
    if (this._checked) {
      classes.push("checked");
    }
    this.domNode = document.createElement("div");
    this.domNode.title = this._opts.title;
    this.domNode.classList.add(...classes);
    if (!this._opts.notFocusable) {
      this.domNode.tabIndex = 0;
    }
    this.domNode.setAttribute("role", "checkbox");
    this.domNode.setAttribute("aria-checked", String(this._checked));
    this.domNode.setAttribute("aria-label", this._opts.title);
    this.applyStyles();
    this.onclick(this.domNode, (ev) => {
      if (this.enabled) {
        this.checked = !this._checked;
        this._onChange.fire(false);
        ev.preventDefault();
      }
    });
    this.ignoreGesture(this.domNode);
    this.onkeydown(this.domNode, (keyboardEvent) => {
      if (keyboardEvent.keyCode === 10 || keyboardEvent.keyCode === 3) {
        this.checked = !this._checked;
        this._onChange.fire(true);
        keyboardEvent.preventDefault();
        return;
      }
      this._onKeyDown.fire(keyboardEvent);
    });
  }
  get enabled() {
    return this.domNode.getAttribute("aria-disabled") !== "true";
  }
  focus() {
    this.domNode.focus();
  }
  get checked() {
    return this._checked;
  }
  set checked(newIsChecked) {
    this._checked = newIsChecked;
    this.domNode.setAttribute("aria-checked", String(this._checked));
    this.domNode.classList.toggle("checked", this._checked);
    this.applyStyles();
  }
  width() {
    return 2 + 2 + 2 + 16;
  }
  style(styles) {
    if (styles.inputActiveOptionBorder) {
      this._opts.inputActiveOptionBorder = styles.inputActiveOptionBorder;
    }
    if (styles.inputActiveOptionForeground) {
      this._opts.inputActiveOptionForeground = styles.inputActiveOptionForeground;
    }
    if (styles.inputActiveOptionBackground) {
      this._opts.inputActiveOptionBackground = styles.inputActiveOptionBackground;
    }
    this.applyStyles();
  }
  applyStyles() {
    if (this.domNode) {
      this.domNode.style.borderColor = this._checked && this._opts.inputActiveOptionBorder ? this._opts.inputActiveOptionBorder.toString() : "";
      this.domNode.style.color = this._checked && this._opts.inputActiveOptionForeground ? this._opts.inputActiveOptionForeground.toString() : "inherit";
      this.domNode.style.backgroundColor = this._checked && this._opts.inputActiveOptionBackground ? this._opts.inputActiveOptionBackground.toString() : "";
    }
  }
  enable() {
    this.domNode.setAttribute("aria-disabled", String(false));
  }
  disable() {
    this.domNode.setAttribute("aria-disabled", String(true));
  }
};

// node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/findInputCheckboxes.js
var NLS_CASE_SENSITIVE_CHECKBOX_LABEL = localize("caseDescription", "Match Case");
var NLS_WHOLE_WORD_CHECKBOX_LABEL = localize("wordsDescription", "Match Whole Word");
var NLS_REGEX_CHECKBOX_LABEL = localize("regexDescription", "Use Regular Expression");
var CaseSensitiveCheckbox = class extends Checkbox {
  constructor(opts) {
    super({
      icon: Codicon.caseSensitive,
      title: NLS_CASE_SENSITIVE_CHECKBOX_LABEL + opts.appendTitle,
      isChecked: opts.isChecked,
      inputActiveOptionBorder: opts.inputActiveOptionBorder,
      inputActiveOptionForeground: opts.inputActiveOptionForeground,
      inputActiveOptionBackground: opts.inputActiveOptionBackground
    });
  }
};
var WholeWordsCheckbox = class extends Checkbox {
  constructor(opts) {
    super({
      icon: Codicon.wholeWord,
      title: NLS_WHOLE_WORD_CHECKBOX_LABEL + opts.appendTitle,
      isChecked: opts.isChecked,
      inputActiveOptionBorder: opts.inputActiveOptionBorder,
      inputActiveOptionForeground: opts.inputActiveOptionForeground,
      inputActiveOptionBackground: opts.inputActiveOptionBackground
    });
  }
};
var RegexCheckbox = class extends Checkbox {
  constructor(opts) {
    super({
      icon: Codicon.regex,
      title: NLS_REGEX_CHECKBOX_LABEL + opts.appendTitle,
      isChecked: opts.isChecked,
      inputActiveOptionBorder: opts.inputActiveOptionBorder,
      inputActiveOptionForeground: opts.inputActiveOptionForeground,
      inputActiveOptionBackground: opts.inputActiveOptionBackground
    });
  }
};

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findOptionsWidget.js
var FindOptionsWidget = class _FindOptionsWidget extends Widget {
  constructor(editor, state, keybindingService, themeService) {
    super();
    this._hideSoon = this._register(new RunOnceScheduler(() => this._hide(), 2e3));
    this._isVisible = false;
    this._editor = editor;
    this._state = state;
    this._keybindingService = keybindingService;
    this._domNode = document.createElement("div");
    this._domNode.className = "findOptionsWidget";
    this._domNode.style.display = "none";
    this._domNode.style.top = "10px";
    this._domNode.setAttribute("role", "presentation");
    this._domNode.setAttribute("aria-hidden", "true");
    const inputActiveOptionBorderColor = themeService.getColorTheme().getColor(inputActiveOptionBorder);
    const inputActiveOptionForegroundColor = themeService.getColorTheme().getColor(inputActiveOptionForeground);
    const inputActiveOptionBackgroundColor = themeService.getColorTheme().getColor(inputActiveOptionBackground);
    this.caseSensitive = this._register(new CaseSensitiveCheckbox({
      appendTitle: this._keybindingLabelFor(FIND_IDS.ToggleCaseSensitiveCommand),
      isChecked: this._state.matchCase,
      inputActiveOptionBorder: inputActiveOptionBorderColor,
      inputActiveOptionForeground: inputActiveOptionForegroundColor,
      inputActiveOptionBackground: inputActiveOptionBackgroundColor
    }));
    this._domNode.appendChild(this.caseSensitive.domNode);
    this._register(this.caseSensitive.onChange(() => {
      this._state.change({
        matchCase: this.caseSensitive.checked
      }, false);
    }));
    this.wholeWords = this._register(new WholeWordsCheckbox({
      appendTitle: this._keybindingLabelFor(FIND_IDS.ToggleWholeWordCommand),
      isChecked: this._state.wholeWord,
      inputActiveOptionBorder: inputActiveOptionBorderColor,
      inputActiveOptionForeground: inputActiveOptionForegroundColor,
      inputActiveOptionBackground: inputActiveOptionBackgroundColor
    }));
    this._domNode.appendChild(this.wholeWords.domNode);
    this._register(this.wholeWords.onChange(() => {
      this._state.change({
        wholeWord: this.wholeWords.checked
      }, false);
    }));
    this.regex = this._register(new RegexCheckbox({
      appendTitle: this._keybindingLabelFor(FIND_IDS.ToggleRegexCommand),
      isChecked: this._state.isRegex,
      inputActiveOptionBorder: inputActiveOptionBorderColor,
      inputActiveOptionForeground: inputActiveOptionForegroundColor,
      inputActiveOptionBackground: inputActiveOptionBackgroundColor
    }));
    this._domNode.appendChild(this.regex.domNode);
    this._register(this.regex.onChange(() => {
      this._state.change({
        isRegex: this.regex.checked
      }, false);
    }));
    this._editor.addOverlayWidget(this);
    this._register(this._state.onFindReplaceStateChange((e) => {
      let somethingChanged = false;
      if (e.isRegex) {
        this.regex.checked = this._state.isRegex;
        somethingChanged = true;
      }
      if (e.wholeWord) {
        this.wholeWords.checked = this._state.wholeWord;
        somethingChanged = true;
      }
      if (e.matchCase) {
        this.caseSensitive.checked = this._state.matchCase;
        somethingChanged = true;
      }
      if (!this._state.isRevealed && somethingChanged) {
        this._revealTemporarily();
      }
    }));
    this._register(addDisposableNonBubblingMouseOutListener(this._domNode, (e) => this._onMouseOut()));
    this._register(addDisposableListener(this._domNode, "mouseover", (e) => this._onMouseOver()));
    this._applyTheme(themeService.getColorTheme());
    this._register(themeService.onDidColorThemeChange(this._applyTheme.bind(this)));
  }
  _keybindingLabelFor(actionId) {
    let kb = this._keybindingService.lookupKeybinding(actionId);
    if (!kb) {
      return "";
    }
    return ` (${kb.getLabel()})`;
  }
  dispose() {
    this._editor.removeOverlayWidget(this);
    super.dispose();
  }
  // ----- IOverlayWidget API
  getId() {
    return _FindOptionsWidget.ID;
  }
  getDomNode() {
    return this._domNode;
  }
  getPosition() {
    return {
      preference: 0
      /* TOP_RIGHT_CORNER */
    };
  }
  highlightFindOptions() {
    this._revealTemporarily();
  }
  _revealTemporarily() {
    this._show();
    this._hideSoon.schedule();
  }
  _onMouseOut() {
    this._hideSoon.schedule();
  }
  _onMouseOver() {
    this._hideSoon.cancel();
  }
  _show() {
    if (this._isVisible) {
      return;
    }
    this._isVisible = true;
    this._domNode.style.display = "block";
  }
  _hide() {
    if (!this._isVisible) {
      return;
    }
    this._isVisible = false;
    this._domNode.style.display = "none";
  }
  _applyTheme(theme) {
    let inputStyles = {
      inputActiveOptionBorder: theme.getColor(inputActiveOptionBorder),
      inputActiveOptionForeground: theme.getColor(inputActiveOptionForeground),
      inputActiveOptionBackground: theme.getColor(inputActiveOptionBackground)
    };
    this.caseSensitive.style(inputStyles);
    this.wholeWords.style(inputStyles);
    this.regex.style(inputStyles);
  }
};
FindOptionsWidget.ID = "editor.contrib.findOptionsWidget";
registerThemingParticipant((theme, collector) => {
  const widgetBackground = theme.getColor(editorWidgetBackground);
  if (widgetBackground) {
    collector.addRule(`.monaco-editor .findOptionsWidget { background-color: ${widgetBackground}; }`);
  }
  const widgetForeground = theme.getColor(editorWidgetForeground);
  if (widgetForeground) {
    collector.addRule(`.monaco-editor .findOptionsWidget { color: ${widgetForeground}; }`);
  }
  const widgetShadowColor = theme.getColor(widgetShadow);
  if (widgetShadowColor) {
    collector.addRule(`.monaco-editor .findOptionsWidget { box-shadow: 0 0 8px 2px ${widgetShadowColor}; }`);
  }
  const hcBorder = theme.getColor(contrastBorder);
  if (hcBorder) {
    collector.addRule(`.monaco-editor .findOptionsWidget { border: 2px solid ${hcBorder}; }`);
  }
});

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findState.js
function effectiveOptionValue(override, value) {
  if (override === 1) {
    return true;
  }
  if (override === 2) {
    return false;
  }
  return value;
}
var FindReplaceState = class extends Disposable {
  constructor() {
    super();
    this._onFindReplaceStateChange = this._register(new Emitter());
    this.onFindReplaceStateChange = this._onFindReplaceStateChange.event;
    this._searchString = "";
    this._replaceString = "";
    this._isRevealed = false;
    this._isReplaceRevealed = false;
    this._isRegex = false;
    this._isRegexOverride = 0;
    this._wholeWord = false;
    this._wholeWordOverride = 0;
    this._matchCase = false;
    this._matchCaseOverride = 0;
    this._preserveCase = false;
    this._preserveCaseOverride = 0;
    this._searchScope = null;
    this._matchesPosition = 0;
    this._matchesCount = 0;
    this._currentMatch = null;
    this._loop = true;
    this._isSearching = false;
    this._filters = null;
  }
  get searchString() {
    return this._searchString;
  }
  get replaceString() {
    return this._replaceString;
  }
  get isRevealed() {
    return this._isRevealed;
  }
  get isReplaceRevealed() {
    return this._isReplaceRevealed;
  }
  get isRegex() {
    return effectiveOptionValue(this._isRegexOverride, this._isRegex);
  }
  get wholeWord() {
    return effectiveOptionValue(this._wholeWordOverride, this._wholeWord);
  }
  get matchCase() {
    return effectiveOptionValue(this._matchCaseOverride, this._matchCase);
  }
  get preserveCase() {
    return effectiveOptionValue(this._preserveCaseOverride, this._preserveCase);
  }
  get actualIsRegex() {
    return this._isRegex;
  }
  get actualWholeWord() {
    return this._wholeWord;
  }
  get actualMatchCase() {
    return this._matchCase;
  }
  get actualPreserveCase() {
    return this._preserveCase;
  }
  get searchScope() {
    return this._searchScope;
  }
  get matchesPosition() {
    return this._matchesPosition;
  }
  get matchesCount() {
    return this._matchesCount;
  }
  get currentMatch() {
    return this._currentMatch;
  }
  changeMatchInfo(matchesPosition, matchesCount, currentMatch) {
    let changeEvent = {
      moveCursor: false,
      updateHistory: false,
      searchString: false,
      replaceString: false,
      isRevealed: false,
      isReplaceRevealed: false,
      isRegex: false,
      wholeWord: false,
      matchCase: false,
      preserveCase: false,
      searchScope: false,
      matchesPosition: false,
      matchesCount: false,
      currentMatch: false,
      loop: false,
      isSearching: false,
      filters: false
    };
    let somethingChanged = false;
    if (matchesCount === 0) {
      matchesPosition = 0;
    }
    if (matchesPosition > matchesCount) {
      matchesPosition = matchesCount;
    }
    if (this._matchesPosition !== matchesPosition) {
      this._matchesPosition = matchesPosition;
      changeEvent.matchesPosition = true;
      somethingChanged = true;
    }
    if (this._matchesCount !== matchesCount) {
      this._matchesCount = matchesCount;
      changeEvent.matchesCount = true;
      somethingChanged = true;
    }
    if (typeof currentMatch !== "undefined") {
      if (!Range.equalsRange(this._currentMatch, currentMatch)) {
        this._currentMatch = currentMatch;
        changeEvent.currentMatch = true;
        somethingChanged = true;
      }
    }
    if (somethingChanged) {
      this._onFindReplaceStateChange.fire(changeEvent);
    }
  }
  change(newState, moveCursor, updateHistory = true) {
    var _a;
    let changeEvent = {
      moveCursor,
      updateHistory,
      searchString: false,
      replaceString: false,
      isRevealed: false,
      isReplaceRevealed: false,
      isRegex: false,
      wholeWord: false,
      matchCase: false,
      preserveCase: false,
      searchScope: false,
      matchesPosition: false,
      matchesCount: false,
      currentMatch: false,
      loop: false,
      isSearching: false,
      filters: false
    };
    let somethingChanged = false;
    const oldEffectiveIsRegex = this.isRegex;
    const oldEffectiveWholeWords = this.wholeWord;
    const oldEffectiveMatchCase = this.matchCase;
    const oldEffectivePreserveCase = this.preserveCase;
    if (typeof newState.searchString !== "undefined") {
      if (this._searchString !== newState.searchString) {
        this._searchString = newState.searchString;
        changeEvent.searchString = true;
        somethingChanged = true;
      }
    }
    if (typeof newState.replaceString !== "undefined") {
      if (this._replaceString !== newState.replaceString) {
        this._replaceString = newState.replaceString;
        changeEvent.replaceString = true;
        somethingChanged = true;
      }
    }
    if (typeof newState.isRevealed !== "undefined") {
      if (this._isRevealed !== newState.isRevealed) {
        this._isRevealed = newState.isRevealed;
        changeEvent.isRevealed = true;
        somethingChanged = true;
      }
    }
    if (typeof newState.isReplaceRevealed !== "undefined") {
      if (this._isReplaceRevealed !== newState.isReplaceRevealed) {
        this._isReplaceRevealed = newState.isReplaceRevealed;
        changeEvent.isReplaceRevealed = true;
        somethingChanged = true;
      }
    }
    if (typeof newState.isRegex !== "undefined") {
      this._isRegex = newState.isRegex;
    }
    if (typeof newState.wholeWord !== "undefined") {
      this._wholeWord = newState.wholeWord;
    }
    if (typeof newState.matchCase !== "undefined") {
      this._matchCase = newState.matchCase;
    }
    if (typeof newState.preserveCase !== "undefined") {
      this._preserveCase = newState.preserveCase;
    }
    if (typeof newState.searchScope !== "undefined") {
      if (!((_a = newState.searchScope) === null || _a === void 0 ? void 0 : _a.every((newSearchScope) => {
        var _a2;
        return (_a2 = this._searchScope) === null || _a2 === void 0 ? void 0 : _a2.some((existingSearchScope) => {
          return !Range.equalsRange(existingSearchScope, newSearchScope);
        });
      }))) {
        this._searchScope = newState.searchScope;
        changeEvent.searchScope = true;
        somethingChanged = true;
      }
    }
    if (typeof newState.loop !== "undefined") {
      if (this._loop !== newState.loop) {
        this._loop = newState.loop;
        changeEvent.loop = true;
        somethingChanged = true;
      }
    }
    if (typeof newState.isSearching !== "undefined") {
      if (this._isSearching !== newState.isSearching) {
        this._isSearching = newState.isSearching;
        changeEvent.isSearching = true;
        somethingChanged = true;
      }
    }
    if (typeof newState.filters !== "undefined") {
      if (this._filters) {
        this._filters.update(newState.filters);
      } else {
        this._filters = newState.filters;
      }
      changeEvent.filters = true;
      somethingChanged = true;
    }
    this._isRegexOverride = typeof newState.isRegexOverride !== "undefined" ? newState.isRegexOverride : 0;
    this._wholeWordOverride = typeof newState.wholeWordOverride !== "undefined" ? newState.wholeWordOverride : 0;
    this._matchCaseOverride = typeof newState.matchCaseOverride !== "undefined" ? newState.matchCaseOverride : 0;
    this._preserveCaseOverride = typeof newState.preserveCaseOverride !== "undefined" ? newState.preserveCaseOverride : 0;
    if (oldEffectiveIsRegex !== this.isRegex) {
      somethingChanged = true;
      changeEvent.isRegex = true;
    }
    if (oldEffectiveWholeWords !== this.wholeWord) {
      somethingChanged = true;
      changeEvent.wholeWord = true;
    }
    if (oldEffectiveMatchCase !== this.matchCase) {
      somethingChanged = true;
      changeEvent.matchCase = true;
    }
    if (oldEffectivePreserveCase !== this.preserveCase) {
      somethingChanged = true;
      changeEvent.preserveCase = true;
    }
    if (somethingChanged) {
      this._onFindReplaceStateChange.fire(changeEvent);
    }
  }
  canNavigateBack() {
    return this.canNavigateInLoop() || this.matchesPosition !== 1;
  }
  canNavigateForward() {
    return this.canNavigateInLoop() || this.matchesPosition < this.matchesCount;
  }
  canNavigateInLoop() {
    return this._loop || this.matchesCount >= MATCHES_LIMIT;
  }
};

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findWidget.js
import "C:/Users/<USER>/Desktop/sai/saiadmin-boot/saiadmin-vue/node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findWidget.css";

// node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.js
import "C:/Users/<USER>/Desktop/sai/saiadmin-boot/saiadmin-vue/node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.css";
var NLS_DEFAULT_LABEL = localize("defaultLabel", "input");
var FindInput = class extends Widget {
  constructor(parent, contextViewProvider, _showOptionButtons, options) {
    super();
    this._showOptionButtons = _showOptionButtons;
    this.fixFocusOnOptionClickEnabled = true;
    this.imeSessionInProgress = false;
    this._onDidOptionChange = this._register(new Emitter());
    this.onDidOptionChange = this._onDidOptionChange.event;
    this._onKeyDown = this._register(new Emitter());
    this.onKeyDown = this._onKeyDown.event;
    this._onMouseDown = this._register(new Emitter());
    this.onMouseDown = this._onMouseDown.event;
    this._onInput = this._register(new Emitter());
    this._onKeyUp = this._register(new Emitter());
    this._onCaseSensitiveKeyDown = this._register(new Emitter());
    this.onCaseSensitiveKeyDown = this._onCaseSensitiveKeyDown.event;
    this._onRegexKeyDown = this._register(new Emitter());
    this.onRegexKeyDown = this._onRegexKeyDown.event;
    this._lastHighlightFindOptions = 0;
    this.contextViewProvider = contextViewProvider;
    this.placeholder = options.placeholder || "";
    this.validation = options.validation;
    this.label = options.label || NLS_DEFAULT_LABEL;
    this.inputActiveOptionBorder = options.inputActiveOptionBorder;
    this.inputActiveOptionForeground = options.inputActiveOptionForeground;
    this.inputActiveOptionBackground = options.inputActiveOptionBackground;
    this.inputBackground = options.inputBackground;
    this.inputForeground = options.inputForeground;
    this.inputBorder = options.inputBorder;
    this.inputValidationInfoBorder = options.inputValidationInfoBorder;
    this.inputValidationInfoBackground = options.inputValidationInfoBackground;
    this.inputValidationInfoForeground = options.inputValidationInfoForeground;
    this.inputValidationWarningBorder = options.inputValidationWarningBorder;
    this.inputValidationWarningBackground = options.inputValidationWarningBackground;
    this.inputValidationWarningForeground = options.inputValidationWarningForeground;
    this.inputValidationErrorBorder = options.inputValidationErrorBorder;
    this.inputValidationErrorBackground = options.inputValidationErrorBackground;
    this.inputValidationErrorForeground = options.inputValidationErrorForeground;
    const appendCaseSensitiveLabel = options.appendCaseSensitiveLabel || "";
    const appendWholeWordsLabel = options.appendWholeWordsLabel || "";
    const appendRegexLabel = options.appendRegexLabel || "";
    const history = options.history || [];
    const flexibleHeight = !!options.flexibleHeight;
    const flexibleWidth = !!options.flexibleWidth;
    const flexibleMaxHeight = options.flexibleMaxHeight;
    this.domNode = document.createElement("div");
    this.domNode.classList.add("monaco-findInput");
    this.inputBox = this._register(new HistoryInputBox(this.domNode, this.contextViewProvider, {
      placeholder: this.placeholder || "",
      ariaLabel: this.label || "",
      validationOptions: {
        validation: this.validation
      },
      inputBackground: this.inputBackground,
      inputForeground: this.inputForeground,
      inputBorder: this.inputBorder,
      inputValidationInfoBackground: this.inputValidationInfoBackground,
      inputValidationInfoForeground: this.inputValidationInfoForeground,
      inputValidationInfoBorder: this.inputValidationInfoBorder,
      inputValidationWarningBackground: this.inputValidationWarningBackground,
      inputValidationWarningForeground: this.inputValidationWarningForeground,
      inputValidationWarningBorder: this.inputValidationWarningBorder,
      inputValidationErrorBackground: this.inputValidationErrorBackground,
      inputValidationErrorForeground: this.inputValidationErrorForeground,
      inputValidationErrorBorder: this.inputValidationErrorBorder,
      history,
      showHistoryHint: options.showHistoryHint,
      flexibleHeight,
      flexibleWidth,
      flexibleMaxHeight
    }));
    this.regex = this._register(new RegexCheckbox({
      appendTitle: appendRegexLabel,
      isChecked: false,
      inputActiveOptionBorder: this.inputActiveOptionBorder,
      inputActiveOptionForeground: this.inputActiveOptionForeground,
      inputActiveOptionBackground: this.inputActiveOptionBackground
    }));
    this._register(this.regex.onChange((viaKeyboard) => {
      this._onDidOptionChange.fire(viaKeyboard);
      if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {
        this.inputBox.focus();
      }
      this.validate();
    }));
    this._register(this.regex.onKeyDown((e) => {
      this._onRegexKeyDown.fire(e);
    }));
    this.wholeWords = this._register(new WholeWordsCheckbox({
      appendTitle: appendWholeWordsLabel,
      isChecked: false,
      inputActiveOptionBorder: this.inputActiveOptionBorder,
      inputActiveOptionForeground: this.inputActiveOptionForeground,
      inputActiveOptionBackground: this.inputActiveOptionBackground
    }));
    this._register(this.wholeWords.onChange((viaKeyboard) => {
      this._onDidOptionChange.fire(viaKeyboard);
      if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {
        this.inputBox.focus();
      }
      this.validate();
    }));
    this.caseSensitive = this._register(new CaseSensitiveCheckbox({
      appendTitle: appendCaseSensitiveLabel,
      isChecked: false,
      inputActiveOptionBorder: this.inputActiveOptionBorder,
      inputActiveOptionForeground: this.inputActiveOptionForeground,
      inputActiveOptionBackground: this.inputActiveOptionBackground
    }));
    this._register(this.caseSensitive.onChange((viaKeyboard) => {
      this._onDidOptionChange.fire(viaKeyboard);
      if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {
        this.inputBox.focus();
      }
      this.validate();
    }));
    this._register(this.caseSensitive.onKeyDown((e) => {
      this._onCaseSensitiveKeyDown.fire(e);
    }));
    if (this._showOptionButtons) {
      this.inputBox.paddingRight = this.caseSensitive.width() + this.wholeWords.width() + this.regex.width();
    }
    let indexes = [this.caseSensitive.domNode, this.wholeWords.domNode, this.regex.domNode];
    this.onkeydown(this.domNode, (event) => {
      if (event.equals(
        15
        /* LeftArrow */
      ) || event.equals(
        17
        /* RightArrow */
      ) || event.equals(
        9
        /* Escape */
      )) {
        let index = indexes.indexOf(document.activeElement);
        if (index >= 0) {
          let newIndex = -1;
          if (event.equals(
            17
            /* RightArrow */
          )) {
            newIndex = (index + 1) % indexes.length;
          } else if (event.equals(
            15
            /* LeftArrow */
          )) {
            if (index === 0) {
              newIndex = indexes.length - 1;
            } else {
              newIndex = index - 1;
            }
          }
          if (event.equals(
            9
            /* Escape */
          )) {
            indexes[index].blur();
            this.inputBox.focus();
          } else if (newIndex >= 0) {
            indexes[newIndex].focus();
          }
          EventHelper.stop(event, true);
        }
      }
    });
    this.controls = document.createElement("div");
    this.controls.className = "controls";
    this.controls.style.display = this._showOptionButtons ? "block" : "none";
    this.controls.appendChild(this.caseSensitive.domNode);
    this.controls.appendChild(this.wholeWords.domNode);
    this.controls.appendChild(this.regex.domNode);
    this.domNode.appendChild(this.controls);
    if (parent) {
      parent.appendChild(this.domNode);
    }
    this._register(addDisposableListener(this.inputBox.inputElement, "compositionstart", (e) => {
      this.imeSessionInProgress = true;
    }));
    this._register(addDisposableListener(this.inputBox.inputElement, "compositionend", (e) => {
      this.imeSessionInProgress = false;
      this._onInput.fire();
    }));
    this.onkeydown(this.inputBox.inputElement, (e) => this._onKeyDown.fire(e));
    this.onkeyup(this.inputBox.inputElement, (e) => this._onKeyUp.fire(e));
    this.oninput(this.inputBox.inputElement, (e) => this._onInput.fire());
    this.onmousedown(this.inputBox.inputElement, (e) => this._onMouseDown.fire(e));
  }
  enable() {
    this.domNode.classList.remove("disabled");
    this.inputBox.enable();
    this.regex.enable();
    this.wholeWords.enable();
    this.caseSensitive.enable();
  }
  disable() {
    this.domNode.classList.add("disabled");
    this.inputBox.disable();
    this.regex.disable();
    this.wholeWords.disable();
    this.caseSensitive.disable();
  }
  setFocusInputOnOptionClick(value) {
    this.fixFocusOnOptionClickEnabled = value;
  }
  setEnabled(enabled) {
    if (enabled) {
      this.enable();
    } else {
      this.disable();
    }
  }
  getValue() {
    return this.inputBox.value;
  }
  setValue(value) {
    if (this.inputBox.value !== value) {
      this.inputBox.value = value;
    }
  }
  style(styles) {
    this.inputActiveOptionBorder = styles.inputActiveOptionBorder;
    this.inputActiveOptionForeground = styles.inputActiveOptionForeground;
    this.inputActiveOptionBackground = styles.inputActiveOptionBackground;
    this.inputBackground = styles.inputBackground;
    this.inputForeground = styles.inputForeground;
    this.inputBorder = styles.inputBorder;
    this.inputValidationInfoBackground = styles.inputValidationInfoBackground;
    this.inputValidationInfoForeground = styles.inputValidationInfoForeground;
    this.inputValidationInfoBorder = styles.inputValidationInfoBorder;
    this.inputValidationWarningBackground = styles.inputValidationWarningBackground;
    this.inputValidationWarningForeground = styles.inputValidationWarningForeground;
    this.inputValidationWarningBorder = styles.inputValidationWarningBorder;
    this.inputValidationErrorBackground = styles.inputValidationErrorBackground;
    this.inputValidationErrorForeground = styles.inputValidationErrorForeground;
    this.inputValidationErrorBorder = styles.inputValidationErrorBorder;
    this.applyStyles();
  }
  applyStyles() {
    if (this.domNode) {
      const checkBoxStyles = {
        inputActiveOptionBorder: this.inputActiveOptionBorder,
        inputActiveOptionForeground: this.inputActiveOptionForeground,
        inputActiveOptionBackground: this.inputActiveOptionBackground
      };
      this.regex.style(checkBoxStyles);
      this.wholeWords.style(checkBoxStyles);
      this.caseSensitive.style(checkBoxStyles);
      const inputBoxStyles = {
        inputBackground: this.inputBackground,
        inputForeground: this.inputForeground,
        inputBorder: this.inputBorder,
        inputValidationInfoBackground: this.inputValidationInfoBackground,
        inputValidationInfoForeground: this.inputValidationInfoForeground,
        inputValidationInfoBorder: this.inputValidationInfoBorder,
        inputValidationWarningBackground: this.inputValidationWarningBackground,
        inputValidationWarningForeground: this.inputValidationWarningForeground,
        inputValidationWarningBorder: this.inputValidationWarningBorder,
        inputValidationErrorBackground: this.inputValidationErrorBackground,
        inputValidationErrorForeground: this.inputValidationErrorForeground,
        inputValidationErrorBorder: this.inputValidationErrorBorder
      };
      this.inputBox.style(inputBoxStyles);
    }
  }
  select() {
    this.inputBox.select();
  }
  focus() {
    this.inputBox.focus();
  }
  getCaseSensitive() {
    return this.caseSensitive.checked;
  }
  setCaseSensitive(value) {
    this.caseSensitive.checked = value;
  }
  getWholeWords() {
    return this.wholeWords.checked;
  }
  setWholeWords(value) {
    this.wholeWords.checked = value;
  }
  getRegex() {
    return this.regex.checked;
  }
  setRegex(value) {
    this.regex.checked = value;
    this.validate();
  }
  focusOnCaseSensitive() {
    this.caseSensitive.focus();
  }
  highlightFindOptions() {
    this.domNode.classList.remove("highlight-" + this._lastHighlightFindOptions);
    this._lastHighlightFindOptions = 1 - this._lastHighlightFindOptions;
    this.domNode.classList.add("highlight-" + this._lastHighlightFindOptions);
  }
  validate() {
    this.inputBox.validate();
  }
  clearMessage() {
    this.inputBox.hideMessage();
  }
};

// node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/replaceInput.js
import "C:/Users/<USER>/Desktop/sai/saiadmin-boot/saiadmin-vue/node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.css";
var NLS_DEFAULT_LABEL2 = localize("defaultLabel", "input");
var NLS_PRESERVE_CASE_LABEL = localize("label.preserveCaseCheckbox", "Preserve Case");
var PreserveCaseCheckbox = class extends Checkbox {
  constructor(opts) {
    super({
      // TODO: does this need its own icon?
      icon: Codicon.preserveCase,
      title: NLS_PRESERVE_CASE_LABEL + opts.appendTitle,
      isChecked: opts.isChecked,
      inputActiveOptionBorder: opts.inputActiveOptionBorder,
      inputActiveOptionForeground: opts.inputActiveOptionForeground,
      inputActiveOptionBackground: opts.inputActiveOptionBackground
    });
  }
};
var ReplaceInput = class extends Widget {
  constructor(parent, contextViewProvider, _showOptionButtons, options) {
    super();
    this._showOptionButtons = _showOptionButtons;
    this.fixFocusOnOptionClickEnabled = true;
    this.cachedOptionsWidth = 0;
    this._onDidOptionChange = this._register(new Emitter());
    this.onDidOptionChange = this._onDidOptionChange.event;
    this._onKeyDown = this._register(new Emitter());
    this.onKeyDown = this._onKeyDown.event;
    this._onMouseDown = this._register(new Emitter());
    this._onInput = this._register(new Emitter());
    this._onKeyUp = this._register(new Emitter());
    this._onPreserveCaseKeyDown = this._register(new Emitter());
    this.onPreserveCaseKeyDown = this._onPreserveCaseKeyDown.event;
    this.contextViewProvider = contextViewProvider;
    this.placeholder = options.placeholder || "";
    this.validation = options.validation;
    this.label = options.label || NLS_DEFAULT_LABEL2;
    this.inputActiveOptionBorder = options.inputActiveOptionBorder;
    this.inputActiveOptionForeground = options.inputActiveOptionForeground;
    this.inputActiveOptionBackground = options.inputActiveOptionBackground;
    this.inputBackground = options.inputBackground;
    this.inputForeground = options.inputForeground;
    this.inputBorder = options.inputBorder;
    this.inputValidationInfoBorder = options.inputValidationInfoBorder;
    this.inputValidationInfoBackground = options.inputValidationInfoBackground;
    this.inputValidationInfoForeground = options.inputValidationInfoForeground;
    this.inputValidationWarningBorder = options.inputValidationWarningBorder;
    this.inputValidationWarningBackground = options.inputValidationWarningBackground;
    this.inputValidationWarningForeground = options.inputValidationWarningForeground;
    this.inputValidationErrorBorder = options.inputValidationErrorBorder;
    this.inputValidationErrorBackground = options.inputValidationErrorBackground;
    this.inputValidationErrorForeground = options.inputValidationErrorForeground;
    const appendPreserveCaseLabel = options.appendPreserveCaseLabel || "";
    const history = options.history || [];
    const flexibleHeight = !!options.flexibleHeight;
    const flexibleWidth = !!options.flexibleWidth;
    const flexibleMaxHeight = options.flexibleMaxHeight;
    this.domNode = document.createElement("div");
    this.domNode.classList.add("monaco-findInput");
    this.inputBox = this._register(new HistoryInputBox(this.domNode, this.contextViewProvider, {
      ariaLabel: this.label || "",
      placeholder: this.placeholder || "",
      validationOptions: {
        validation: this.validation
      },
      inputBackground: this.inputBackground,
      inputForeground: this.inputForeground,
      inputBorder: this.inputBorder,
      inputValidationInfoBackground: this.inputValidationInfoBackground,
      inputValidationInfoForeground: this.inputValidationInfoForeground,
      inputValidationInfoBorder: this.inputValidationInfoBorder,
      inputValidationWarningBackground: this.inputValidationWarningBackground,
      inputValidationWarningForeground: this.inputValidationWarningForeground,
      inputValidationWarningBorder: this.inputValidationWarningBorder,
      inputValidationErrorBackground: this.inputValidationErrorBackground,
      inputValidationErrorForeground: this.inputValidationErrorForeground,
      inputValidationErrorBorder: this.inputValidationErrorBorder,
      history,
      showHistoryHint: options.showHistoryHint,
      flexibleHeight,
      flexibleWidth,
      flexibleMaxHeight
    }));
    this.preserveCase = this._register(new PreserveCaseCheckbox({
      appendTitle: appendPreserveCaseLabel,
      isChecked: false,
      inputActiveOptionBorder: this.inputActiveOptionBorder,
      inputActiveOptionForeground: this.inputActiveOptionForeground,
      inputActiveOptionBackground: this.inputActiveOptionBackground
    }));
    this._register(this.preserveCase.onChange((viaKeyboard) => {
      this._onDidOptionChange.fire(viaKeyboard);
      if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {
        this.inputBox.focus();
      }
      this.validate();
    }));
    this._register(this.preserveCase.onKeyDown((e) => {
      this._onPreserveCaseKeyDown.fire(e);
    }));
    if (this._showOptionButtons) {
      this.cachedOptionsWidth = this.preserveCase.width();
    } else {
      this.cachedOptionsWidth = 0;
    }
    let indexes = [this.preserveCase.domNode];
    this.onkeydown(this.domNode, (event) => {
      if (event.equals(
        15
        /* LeftArrow */
      ) || event.equals(
        17
        /* RightArrow */
      ) || event.equals(
        9
        /* Escape */
      )) {
        let index = indexes.indexOf(document.activeElement);
        if (index >= 0) {
          let newIndex = -1;
          if (event.equals(
            17
            /* RightArrow */
          )) {
            newIndex = (index + 1) % indexes.length;
          } else if (event.equals(
            15
            /* LeftArrow */
          )) {
            if (index === 0) {
              newIndex = indexes.length - 1;
            } else {
              newIndex = index - 1;
            }
          }
          if (event.equals(
            9
            /* Escape */
          )) {
            indexes[index].blur();
            this.inputBox.focus();
          } else if (newIndex >= 0) {
            indexes[newIndex].focus();
          }
          EventHelper.stop(event, true);
        }
      }
    });
    let controls = document.createElement("div");
    controls.className = "controls";
    controls.style.display = this._showOptionButtons ? "block" : "none";
    controls.appendChild(this.preserveCase.domNode);
    this.domNode.appendChild(controls);
    if (parent) {
      parent.appendChild(this.domNode);
    }
    this.onkeydown(this.inputBox.inputElement, (e) => this._onKeyDown.fire(e));
    this.onkeyup(this.inputBox.inputElement, (e) => this._onKeyUp.fire(e));
    this.oninput(this.inputBox.inputElement, (e) => this._onInput.fire());
    this.onmousedown(this.inputBox.inputElement, (e) => this._onMouseDown.fire(e));
  }
  enable() {
    this.domNode.classList.remove("disabled");
    this.inputBox.enable();
    this.preserveCase.enable();
  }
  disable() {
    this.domNode.classList.add("disabled");
    this.inputBox.disable();
    this.preserveCase.disable();
  }
  setEnabled(enabled) {
    if (enabled) {
      this.enable();
    } else {
      this.disable();
    }
  }
  style(styles) {
    this.inputActiveOptionBorder = styles.inputActiveOptionBorder;
    this.inputActiveOptionForeground = styles.inputActiveOptionForeground;
    this.inputActiveOptionBackground = styles.inputActiveOptionBackground;
    this.inputBackground = styles.inputBackground;
    this.inputForeground = styles.inputForeground;
    this.inputBorder = styles.inputBorder;
    this.inputValidationInfoBackground = styles.inputValidationInfoBackground;
    this.inputValidationInfoForeground = styles.inputValidationInfoForeground;
    this.inputValidationInfoBorder = styles.inputValidationInfoBorder;
    this.inputValidationWarningBackground = styles.inputValidationWarningBackground;
    this.inputValidationWarningForeground = styles.inputValidationWarningForeground;
    this.inputValidationWarningBorder = styles.inputValidationWarningBorder;
    this.inputValidationErrorBackground = styles.inputValidationErrorBackground;
    this.inputValidationErrorForeground = styles.inputValidationErrorForeground;
    this.inputValidationErrorBorder = styles.inputValidationErrorBorder;
    this.applyStyles();
  }
  applyStyles() {
    if (this.domNode) {
      const checkBoxStyles = {
        inputActiveOptionBorder: this.inputActiveOptionBorder,
        inputActiveOptionForeground: this.inputActiveOptionForeground,
        inputActiveOptionBackground: this.inputActiveOptionBackground
      };
      this.preserveCase.style(checkBoxStyles);
      const inputBoxStyles = {
        inputBackground: this.inputBackground,
        inputForeground: this.inputForeground,
        inputBorder: this.inputBorder,
        inputValidationInfoBackground: this.inputValidationInfoBackground,
        inputValidationInfoForeground: this.inputValidationInfoForeground,
        inputValidationInfoBorder: this.inputValidationInfoBorder,
        inputValidationWarningBackground: this.inputValidationWarningBackground,
        inputValidationWarningForeground: this.inputValidationWarningForeground,
        inputValidationWarningBorder: this.inputValidationWarningBorder,
        inputValidationErrorBackground: this.inputValidationErrorBackground,
        inputValidationErrorForeground: this.inputValidationErrorForeground,
        inputValidationErrorBorder: this.inputValidationErrorBorder
      };
      this.inputBox.style(inputBoxStyles);
    }
  }
  select() {
    this.inputBox.select();
  }
  focus() {
    this.inputBox.focus();
  }
  getPreserveCase() {
    return this.preserveCase.checked;
  }
  setPreserveCase(value) {
    this.preserveCase.checked = value;
  }
  focusOnPreserve() {
    this.preserveCase.focus();
  }
  validate() {
    if (this.inputBox) {
      this.inputBox.validate();
    }
  }
  set width(newWidth) {
    this.inputBox.paddingRight = this.cachedOptionsWidth;
    this.inputBox.width = newWidth;
    this.domNode.style.width = newWidth + "px";
  }
  dispose() {
    super.dispose();
  }
};

// node_modules/monaco-editor/esm/vs/platform/history/browser/contextScopedHistoryWidget.js
var __decorate = function(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = function(paramIndex, decorator) {
  return function(target, key) {
    decorator(target, key, paramIndex);
  };
};
var historyNavigationVisible = new RawContextKey("suggestWidgetVisible", false, localize("suggestWidgetVisible", "Whether suggestion are visible"));
var HistoryNavigationWidgetContext = "historyNavigationWidget";
var HistoryNavigationForwardsEnablementContext = "historyNavigationForwardsEnabled";
var HistoryNavigationBackwardsEnablementContext = "historyNavigationBackwardsEnabled";
function bindContextScopedWidget(contextKeyService, widget, contextKey) {
  new RawContextKey(contextKey, widget).bindTo(contextKeyService);
}
function createWidgetScopedContextKeyService(contextKeyService, widget) {
  return contextKeyService.createScoped(widget.target);
}
function getContextScopedWidget(contextKeyService, contextKey) {
  return contextKeyService.getContext(document.activeElement).getValue(contextKey);
}
function createAndBindHistoryNavigationWidgetScopedContextKeyService(contextKeyService, widget) {
  const scopedContextKeyService = createWidgetScopedContextKeyService(contextKeyService, widget);
  bindContextScopedWidget(scopedContextKeyService, widget, HistoryNavigationWidgetContext);
  const historyNavigationForwardsEnablement = new RawContextKey(HistoryNavigationForwardsEnablementContext, true).bindTo(scopedContextKeyService);
  const historyNavigationBackwardsEnablement = new RawContextKey(HistoryNavigationBackwardsEnablementContext, true).bindTo(scopedContextKeyService);
  return {
    scopedContextKeyService,
    historyNavigationForwardsEnablement,
    historyNavigationBackwardsEnablement
  };
}
var ContextScopedFindInput = class ContextScopedFindInput2 extends FindInput {
  constructor(container, contextViewProvider, options, contextKeyService, showFindOptions = false) {
    super(container, contextViewProvider, showFindOptions, options);
    this._register(createAndBindHistoryNavigationWidgetScopedContextKeyService(contextKeyService, { target: this.inputBox.element, historyNavigator: this.inputBox }).scopedContextKeyService);
  }
};
ContextScopedFindInput = __decorate([
  __param(3, IContextKeyService)
], ContextScopedFindInput);
var ContextScopedReplaceInput = class ContextScopedReplaceInput2 extends ReplaceInput {
  constructor(container, contextViewProvider, options, contextKeyService, showReplaceOptions = false) {
    super(container, contextViewProvider, showReplaceOptions, options);
    this._register(createAndBindHistoryNavigationWidgetScopedContextKeyService(contextKeyService, { target: this.inputBox.element, historyNavigator: this.inputBox }).scopedContextKeyService);
  }
};
ContextScopedReplaceInput = __decorate([
  __param(3, IContextKeyService)
], ContextScopedReplaceInput);
KeybindingsRegistry.registerCommandAndKeybindingRule({
  id: "history.showPrevious",
  weight: 200,
  when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetContext), ContextKeyExpr.equals(HistoryNavigationBackwardsEnablementContext, true), historyNavigationVisible.isEqualTo(false)),
  primary: 16,
  secondary: [
    512 | 16
    /* UpArrow */
  ],
  handler: (accessor) => {
    const widget = getContextScopedWidget(accessor.get(IContextKeyService), HistoryNavigationWidgetContext);
    if (widget) {
      const historyInputBox = widget.historyNavigator;
      historyInputBox.showPreviousValue();
    }
  }
});
KeybindingsRegistry.registerCommandAndKeybindingRule({
  id: "history.showNext",
  weight: 200,
  when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetContext), ContextKeyExpr.equals(HistoryNavigationForwardsEnablementContext, true), historyNavigationVisible.isEqualTo(false)),
  primary: 18,
  secondary: [
    512 | 18
    /* DownArrow */
  ],
  handler: (accessor) => {
    const widget = getContextScopedWidget(accessor.get(IContextKeyService), HistoryNavigationWidgetContext);
    if (widget) {
      const historyInputBox = widget.historyNavigator;
      historyInputBox.showNextValue();
    }
  }
});

// node_modules/monaco-editor/esm/vs/platform/history/browser/historyWidgetKeybindingHint.js
function showHistoryKeybindingHint(keybindingService) {
  var _a, _b;
  return ((_a = keybindingService.lookupKeybinding("history.showPrevious")) === null || _a === void 0 ? void 0 : _a.getElectronAccelerator()) === "Up" && ((_b = keybindingService.lookupKeybinding("history.showNext")) === null || _b === void 0 ? void 0 : _b.getElectronAccelerator()) === "Down";
}

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findWidget.js
var __awaiter = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var findSelectionIcon = registerIcon("find-selection", Codicon.selection, localize("findSelectionIcon", "Icon for 'Find in Selection' in the editor find widget."));
var findCollapsedIcon = registerIcon("find-collapsed", Codicon.chevronRight, localize("findCollapsedIcon", "Icon to indicate that the editor find widget is collapsed."));
var findExpandedIcon = registerIcon("find-expanded", Codicon.chevronDown, localize("findExpandedIcon", "Icon to indicate that the editor find widget is expanded."));
var findReplaceIcon = registerIcon("find-replace", Codicon.replace, localize("findReplaceIcon", "Icon for 'Replace' in the editor find widget."));
var findReplaceAllIcon = registerIcon("find-replace-all", Codicon.replaceAll, localize("findReplaceAllIcon", "Icon for 'Replace All' in the editor find widget."));
var findPreviousMatchIcon = registerIcon("find-previous-match", Codicon.arrowUp, localize("findPreviousMatchIcon", "Icon for 'Find Previous' in the editor find widget."));
var findNextMatchIcon = registerIcon("find-next-match", Codicon.arrowDown, localize("findNextMatchIcon", "Icon for 'Find Next' in the editor find widget."));
var NLS_FIND_INPUT_LABEL = localize("label.find", "Find");
var NLS_FIND_INPUT_PLACEHOLDER = localize("placeholder.find", "Find");
var NLS_PREVIOUS_MATCH_BTN_LABEL = localize("label.previousMatchButton", "Previous Match");
var NLS_NEXT_MATCH_BTN_LABEL = localize("label.nextMatchButton", "Next Match");
var NLS_TOGGLE_SELECTION_FIND_TITLE = localize("label.toggleSelectionFind", "Find in Selection");
var NLS_CLOSE_BTN_LABEL = localize("label.closeButton", "Close");
var NLS_REPLACE_INPUT_LABEL = localize("label.replace", "Replace");
var NLS_REPLACE_INPUT_PLACEHOLDER = localize("placeholder.replace", "Replace");
var NLS_REPLACE_BTN_LABEL = localize("label.replaceButton", "Replace");
var NLS_REPLACE_ALL_BTN_LABEL = localize("label.replaceAllButton", "Replace All");
var NLS_TOGGLE_REPLACE_MODE_BTN_LABEL = localize("label.toggleReplaceButton", "Toggle Replace");
var NLS_MATCHES_COUNT_LIMIT_TITLE = localize("title.matchesCountLimit", "Only the first {0} results are highlighted, but all find operations work on the entire text.", MATCHES_LIMIT);
var NLS_MATCHES_LOCATION = localize("label.matchesLocation", "{0} of {1}");
var NLS_NO_RESULTS = localize("label.noResults", "No results");
var FIND_WIDGET_INITIAL_WIDTH = 419;
var PART_WIDTH = 275;
var FIND_INPUT_AREA_WIDTH = PART_WIDTH - 54;
var MAX_MATCHES_COUNT_WIDTH = 69;
var FIND_INPUT_AREA_HEIGHT = 33;
var ctrlEnterReplaceAllWarningPromptedKey = "ctrlEnterReplaceAll.windows.donotask";
var ctrlKeyMod = isMacintosh ? 256 : 2048;
var FindWidgetViewZone = class {
  constructor(afterLineNumber) {
    this.afterLineNumber = afterLineNumber;
    this.heightInPx = FIND_INPUT_AREA_HEIGHT;
    this.suppressMouseDown = false;
    this.domNode = document.createElement("div");
    this.domNode.className = "dock-find-viewzone";
  }
};
function stopPropagationForMultiLineUpwards(event, value, textarea) {
  const isMultiline = !!value.match(/\n/);
  if (textarea && isMultiline && textarea.selectionStart > 0) {
    event.stopPropagation();
    return;
  }
}
function stopPropagationForMultiLineDownwards(event, value, textarea) {
  const isMultiline = !!value.match(/\n/);
  if (textarea && isMultiline && textarea.selectionEnd < textarea.value.length) {
    event.stopPropagation();
    return;
  }
}
var FindWidget = class _FindWidget extends Widget {
  constructor(codeEditor, controller, state, contextViewProvider, keybindingService, contextKeyService, themeService, storageService, notificationService) {
    super();
    this._cachedHeight = null;
    this._revealTimeouts = [];
    this._codeEditor = codeEditor;
    this._controller = controller;
    this._state = state;
    this._contextViewProvider = contextViewProvider;
    this._keybindingService = keybindingService;
    this._contextKeyService = contextKeyService;
    this._storageService = storageService;
    this._notificationService = notificationService;
    this._ctrlEnterReplaceAllWarningPrompted = !!storageService.getBoolean(
      ctrlEnterReplaceAllWarningPromptedKey,
      0
      /* GLOBAL */
    );
    this._isVisible = false;
    this._isReplaceVisible = false;
    this._ignoreChangeEvent = false;
    this._updateHistoryDelayer = new Delayer(500);
    this._register(toDisposable(() => this._updateHistoryDelayer.cancel()));
    this._register(this._state.onFindReplaceStateChange((e) => this._onStateChanged(e)));
    this._buildDomNode();
    this._updateButtons();
    this._tryUpdateWidgetWidth();
    this._findInput.inputBox.layout();
    this._register(this._codeEditor.onDidChangeConfiguration((e) => {
      if (e.hasChanged(
        81
        /* readOnly */
      )) {
        if (this._codeEditor.getOption(
          81
          /* readOnly */
        )) {
          this._state.change({ isReplaceRevealed: false }, false);
        }
        this._updateButtons();
      }
      if (e.hasChanged(
        131
        /* layoutInfo */
      )) {
        this._tryUpdateWidgetWidth();
      }
      if (e.hasChanged(
        2
        /* accessibilitySupport */
      )) {
        this.updateAccessibilitySupport();
      }
      if (e.hasChanged(
        35
        /* find */
      )) {
        const addExtraSpaceOnTop = this._codeEditor.getOption(
          35
          /* find */
        ).addExtraSpaceOnTop;
        if (addExtraSpaceOnTop && !this._viewZone) {
          this._viewZone = new FindWidgetViewZone(0);
          this._showViewZone();
        }
        if (!addExtraSpaceOnTop && this._viewZone) {
          this._removeViewZone();
        }
      }
    }));
    this.updateAccessibilitySupport();
    this._register(this._codeEditor.onDidChangeCursorSelection(() => {
      if (this._isVisible) {
        this._updateToggleSelectionFindButton();
      }
    }));
    this._register(this._codeEditor.onDidFocusEditorWidget(() => __awaiter(this, void 0, void 0, function* () {
      if (this._isVisible) {
        let globalBufferTerm = yield this._controller.getGlobalBufferTerm();
        if (globalBufferTerm && globalBufferTerm !== this._state.searchString) {
          this._state.change({ searchString: globalBufferTerm }, false);
          this._findInput.select();
        }
      }
    })));
    this._findInputFocused = CONTEXT_FIND_INPUT_FOCUSED.bindTo(contextKeyService);
    this._findFocusTracker = this._register(trackFocus(this._findInput.inputBox.inputElement));
    this._register(this._findFocusTracker.onDidFocus(() => {
      this._findInputFocused.set(true);
      this._updateSearchScope();
    }));
    this._register(this._findFocusTracker.onDidBlur(() => {
      this._findInputFocused.set(false);
    }));
    this._replaceInputFocused = CONTEXT_REPLACE_INPUT_FOCUSED.bindTo(contextKeyService);
    this._replaceFocusTracker = this._register(trackFocus(this._replaceInput.inputBox.inputElement));
    this._register(this._replaceFocusTracker.onDidFocus(() => {
      this._replaceInputFocused.set(true);
      this._updateSearchScope();
    }));
    this._register(this._replaceFocusTracker.onDidBlur(() => {
      this._replaceInputFocused.set(false);
    }));
    this._codeEditor.addOverlayWidget(this);
    if (this._codeEditor.getOption(
      35
      /* find */
    ).addExtraSpaceOnTop) {
      this._viewZone = new FindWidgetViewZone(0);
    }
    this._applyTheme(themeService.getColorTheme());
    this._register(themeService.onDidColorThemeChange(this._applyTheme.bind(this)));
    this._register(this._codeEditor.onDidChangeModel(() => {
      if (!this._isVisible) {
        return;
      }
      this._viewZoneId = void 0;
    }));
    this._register(this._codeEditor.onDidScrollChange((e) => {
      if (e.scrollTopChanged) {
        this._layoutViewZone();
        return;
      }
      setTimeout(() => {
        this._layoutViewZone();
      }, 0);
    }));
  }
  // ----- IOverlayWidget API
  getId() {
    return _FindWidget.ID;
  }
  getDomNode() {
    return this._domNode;
  }
  getPosition() {
    if (this._isVisible) {
      return {
        preference: 0
        /* TOP_RIGHT_CORNER */
      };
    }
    return null;
  }
  // ----- React to state changes
  _onStateChanged(e) {
    if (e.searchString) {
      try {
        this._ignoreChangeEvent = true;
        this._findInput.setValue(this._state.searchString);
      } finally {
        this._ignoreChangeEvent = false;
      }
      this._updateButtons();
    }
    if (e.replaceString) {
      this._replaceInput.inputBox.value = this._state.replaceString;
    }
    if (e.isRevealed) {
      if (this._state.isRevealed) {
        this._reveal();
      } else {
        this._hide(true);
      }
    }
    if (e.isReplaceRevealed) {
      if (this._state.isReplaceRevealed) {
        if (!this._codeEditor.getOption(
          81
          /* readOnly */
        ) && !this._isReplaceVisible) {
          this._isReplaceVisible = true;
          this._replaceInput.width = getTotalWidth(this._findInput.domNode);
          this._updateButtons();
          this._replaceInput.inputBox.layout();
        }
      } else {
        if (this._isReplaceVisible) {
          this._isReplaceVisible = false;
          this._updateButtons();
        }
      }
    }
    if ((e.isRevealed || e.isReplaceRevealed) && (this._state.isRevealed || this._state.isReplaceRevealed)) {
      if (this._tryUpdateHeight()) {
        this._showViewZone();
      }
    }
    if (e.isRegex) {
      this._findInput.setRegex(this._state.isRegex);
    }
    if (e.wholeWord) {
      this._findInput.setWholeWords(this._state.wholeWord);
    }
    if (e.matchCase) {
      this._findInput.setCaseSensitive(this._state.matchCase);
    }
    if (e.preserveCase) {
      this._replaceInput.setPreserveCase(this._state.preserveCase);
    }
    if (e.searchScope) {
      if (this._state.searchScope) {
        this._toggleSelectionFind.checked = true;
      } else {
        this._toggleSelectionFind.checked = false;
      }
      this._updateToggleSelectionFindButton();
    }
    if (e.searchString || e.matchesCount || e.matchesPosition) {
      let showRedOutline = this._state.searchString.length > 0 && this._state.matchesCount === 0;
      this._domNode.classList.toggle("no-results", showRedOutline);
      this._updateMatchesCount();
      this._updateButtons();
    }
    if (e.searchString || e.currentMatch) {
      this._layoutViewZone();
    }
    if (e.updateHistory) {
      this._delayedUpdateHistory();
    }
    if (e.loop) {
      this._updateButtons();
    }
  }
  _delayedUpdateHistory() {
    this._updateHistoryDelayer.trigger(this._updateHistory.bind(this)).then(void 0, onUnexpectedError);
  }
  _updateHistory() {
    if (this._state.searchString) {
      this._findInput.inputBox.addToHistory();
    }
    if (this._state.replaceString) {
      this._replaceInput.inputBox.addToHistory();
    }
  }
  _updateMatchesCount() {
    this._matchesCount.style.minWidth = MAX_MATCHES_COUNT_WIDTH + "px";
    if (this._state.matchesCount >= MATCHES_LIMIT) {
      this._matchesCount.title = NLS_MATCHES_COUNT_LIMIT_TITLE;
    } else {
      this._matchesCount.title = "";
    }
    if (this._matchesCount.firstChild) {
      this._matchesCount.removeChild(this._matchesCount.firstChild);
    }
    let label;
    if (this._state.matchesCount > 0) {
      let matchesCount = String(this._state.matchesCount);
      if (this._state.matchesCount >= MATCHES_LIMIT) {
        matchesCount += "+";
      }
      let matchesPosition = String(this._state.matchesPosition);
      if (matchesPosition === "0") {
        matchesPosition = "?";
      }
      label = format(NLS_MATCHES_LOCATION, matchesPosition, matchesCount);
    } else {
      label = NLS_NO_RESULTS;
    }
    this._matchesCount.appendChild(document.createTextNode(label));
    alert(this._getAriaLabel(label, this._state.currentMatch, this._state.searchString));
    MAX_MATCHES_COUNT_WIDTH = Math.max(MAX_MATCHES_COUNT_WIDTH, this._matchesCount.clientWidth);
  }
  // ----- actions
  _getAriaLabel(label, currentMatch, searchString) {
    if (label === NLS_NO_RESULTS) {
      return searchString === "" ? localize("ariaSearchNoResultEmpty", "{0} found", label) : localize("ariaSearchNoResult", "{0} found for '{1}'", label, searchString);
    }
    if (currentMatch) {
      const ariaLabel = localize("ariaSearchNoResultWithLineNum", "{0} found for '{1}', at {2}", label, searchString, currentMatch.startLineNumber + ":" + currentMatch.startColumn);
      const model = this._codeEditor.getModel();
      if (model && currentMatch.startLineNumber <= model.getLineCount() && currentMatch.startLineNumber >= 1) {
        const lineContent = model.getLineContent(currentMatch.startLineNumber);
        return `${lineContent}, ${ariaLabel}`;
      }
      return ariaLabel;
    }
    return localize("ariaSearchNoResultWithLineNumNoCurrentMatch", "{0} found for '{1}'", label, searchString);
  }
  /**
   * If 'selection find' is ON we should not disable the button (its function is to cancel 'selection find').
   * If 'selection find' is OFF we enable the button only if there is a selection.
   */
  _updateToggleSelectionFindButton() {
    let selection = this._codeEditor.getSelection();
    let isSelection = selection ? selection.startLineNumber !== selection.endLineNumber || selection.startColumn !== selection.endColumn : false;
    let isChecked = this._toggleSelectionFind.checked;
    if (this._isVisible && (isChecked || isSelection)) {
      this._toggleSelectionFind.enable();
    } else {
      this._toggleSelectionFind.disable();
    }
  }
  _updateButtons() {
    this._findInput.setEnabled(this._isVisible);
    this._replaceInput.setEnabled(this._isVisible && this._isReplaceVisible);
    this._updateToggleSelectionFindButton();
    this._closeBtn.setEnabled(this._isVisible);
    let findInputIsNonEmpty = this._state.searchString.length > 0;
    let matchesCount = this._state.matchesCount ? true : false;
    this._prevBtn.setEnabled(this._isVisible && findInputIsNonEmpty && matchesCount && this._state.canNavigateBack());
    this._nextBtn.setEnabled(this._isVisible && findInputIsNonEmpty && matchesCount && this._state.canNavigateForward());
    this._replaceBtn.setEnabled(this._isVisible && this._isReplaceVisible && findInputIsNonEmpty);
    this._replaceAllBtn.setEnabled(this._isVisible && this._isReplaceVisible && findInputIsNonEmpty);
    this._domNode.classList.toggle("replaceToggled", this._isReplaceVisible);
    this._toggleReplaceBtn.setExpanded(this._isReplaceVisible);
    let canReplace = !this._codeEditor.getOption(
      81
      /* readOnly */
    );
    this._toggleReplaceBtn.setEnabled(this._isVisible && canReplace);
  }
  _reveal() {
    this._revealTimeouts.forEach((e) => {
      clearTimeout(e);
    });
    this._revealTimeouts = [];
    if (!this._isVisible) {
      this._isVisible = true;
      const selection = this._codeEditor.getSelection();
      switch (this._codeEditor.getOption(
        35
        /* find */
      ).autoFindInSelection) {
        case "always":
          this._toggleSelectionFind.checked = true;
          break;
        case "never":
          this._toggleSelectionFind.checked = false;
          break;
        case "multiline": {
          const isSelectionMultipleLine = !!selection && selection.startLineNumber !== selection.endLineNumber;
          this._toggleSelectionFind.checked = isSelectionMultipleLine;
          break;
        }
        default:
          break;
      }
      this._tryUpdateWidgetWidth();
      this._updateButtons();
      this._revealTimeouts.push(setTimeout(() => {
        this._domNode.classList.add("visible");
        this._domNode.setAttribute("aria-hidden", "false");
      }, 0));
      this._revealTimeouts.push(setTimeout(() => {
        this._findInput.validate();
      }, 200));
      this._codeEditor.layoutOverlayWidget(this);
      let adjustEditorScrollTop = true;
      if (this._codeEditor.getOption(
        35
        /* find */
      ).seedSearchStringFromSelection && selection) {
        const domNode = this._codeEditor.getDomNode();
        if (domNode) {
          const editorCoords = getDomNodePagePosition(domNode);
          const startCoords = this._codeEditor.getScrolledVisiblePosition(selection.getStartPosition());
          const startLeft = editorCoords.left + (startCoords ? startCoords.left : 0);
          const startTop = startCoords ? startCoords.top : 0;
          if (this._viewZone && startTop < this._viewZone.heightInPx) {
            if (selection.endLineNumber > selection.startLineNumber) {
              adjustEditorScrollTop = false;
            }
            const leftOfFindWidget = getTopLeftOffset(this._domNode).left;
            if (startLeft > leftOfFindWidget) {
              adjustEditorScrollTop = false;
            }
            const endCoords = this._codeEditor.getScrolledVisiblePosition(selection.getEndPosition());
            const endLeft = editorCoords.left + (endCoords ? endCoords.left : 0);
            if (endLeft > leftOfFindWidget) {
              adjustEditorScrollTop = false;
            }
          }
        }
      }
      this._showViewZone(adjustEditorScrollTop);
    }
  }
  _hide(focusTheEditor) {
    this._revealTimeouts.forEach((e) => {
      clearTimeout(e);
    });
    this._revealTimeouts = [];
    if (this._isVisible) {
      this._isVisible = false;
      this._updateButtons();
      this._domNode.classList.remove("visible");
      this._domNode.setAttribute("aria-hidden", "true");
      this._findInput.clearMessage();
      if (focusTheEditor) {
        this._codeEditor.focus();
      }
      this._codeEditor.layoutOverlayWidget(this);
      this._removeViewZone();
    }
  }
  _layoutViewZone(targetScrollTop) {
    const addExtraSpaceOnTop = this._codeEditor.getOption(
      35
      /* find */
    ).addExtraSpaceOnTop;
    if (!addExtraSpaceOnTop) {
      this._removeViewZone();
      return;
    }
    if (!this._isVisible) {
      return;
    }
    const viewZone = this._viewZone;
    if (this._viewZoneId !== void 0 || !viewZone) {
      return;
    }
    this._codeEditor.changeViewZones((accessor) => {
      viewZone.heightInPx = this._getHeight();
      this._viewZoneId = accessor.addZone(viewZone);
      this._codeEditor.setScrollTop(targetScrollTop || this._codeEditor.getScrollTop() + viewZone.heightInPx);
    });
  }
  _showViewZone(adjustScroll = true) {
    if (!this._isVisible) {
      return;
    }
    const addExtraSpaceOnTop = this._codeEditor.getOption(
      35
      /* find */
    ).addExtraSpaceOnTop;
    if (!addExtraSpaceOnTop) {
      return;
    }
    if (this._viewZone === void 0) {
      this._viewZone = new FindWidgetViewZone(0);
    }
    const viewZone = this._viewZone;
    this._codeEditor.changeViewZones((accessor) => {
      if (this._viewZoneId !== void 0) {
        const newHeight = this._getHeight();
        if (newHeight === viewZone.heightInPx) {
          return;
        }
        let scrollAdjustment = newHeight - viewZone.heightInPx;
        viewZone.heightInPx = newHeight;
        accessor.layoutZone(this._viewZoneId);
        if (adjustScroll) {
          this._codeEditor.setScrollTop(this._codeEditor.getScrollTop() + scrollAdjustment);
        }
        return;
      } else {
        let scrollAdjustment = this._getHeight();
        scrollAdjustment -= this._codeEditor.getOption(
          75
          /* padding */
        ).top;
        if (scrollAdjustment <= 0) {
          return;
        }
        viewZone.heightInPx = scrollAdjustment;
        this._viewZoneId = accessor.addZone(viewZone);
        if (adjustScroll) {
          this._codeEditor.setScrollTop(this._codeEditor.getScrollTop() + scrollAdjustment);
        }
      }
    });
  }
  _removeViewZone() {
    this._codeEditor.changeViewZones((accessor) => {
      if (this._viewZoneId !== void 0) {
        accessor.removeZone(this._viewZoneId);
        this._viewZoneId = void 0;
        if (this._viewZone) {
          this._codeEditor.setScrollTop(this._codeEditor.getScrollTop() - this._viewZone.heightInPx);
          this._viewZone = void 0;
        }
      }
    });
  }
  _applyTheme(theme) {
    let inputStyles = {
      inputActiveOptionBorder: theme.getColor(inputActiveOptionBorder),
      inputActiveOptionBackground: theme.getColor(inputActiveOptionBackground),
      inputActiveOptionForeground: theme.getColor(inputActiveOptionForeground),
      inputBackground: theme.getColor(inputBackground),
      inputForeground: theme.getColor(inputForeground),
      inputBorder: theme.getColor(inputBorder),
      inputValidationInfoBackground: theme.getColor(inputValidationInfoBackground),
      inputValidationInfoForeground: theme.getColor(inputValidationInfoForeground),
      inputValidationInfoBorder: theme.getColor(inputValidationInfoBorder),
      inputValidationWarningBackground: theme.getColor(inputValidationWarningBackground),
      inputValidationWarningForeground: theme.getColor(inputValidationWarningForeground),
      inputValidationWarningBorder: theme.getColor(inputValidationWarningBorder),
      inputValidationErrorBackground: theme.getColor(inputValidationErrorBackground),
      inputValidationErrorForeground: theme.getColor(inputValidationErrorForeground),
      inputValidationErrorBorder: theme.getColor(inputValidationErrorBorder)
    };
    this._findInput.style(inputStyles);
    this._replaceInput.style(inputStyles);
    this._toggleSelectionFind.style(inputStyles);
  }
  _tryUpdateWidgetWidth() {
    if (!this._isVisible) {
      return;
    }
    if (!isInDOM(this._domNode)) {
      return;
    }
    const layoutInfo = this._codeEditor.getLayoutInfo();
    const editorContentWidth = layoutInfo.contentWidth;
    if (editorContentWidth <= 0) {
      this._domNode.classList.add("hiddenEditor");
      return;
    } else if (this._domNode.classList.contains("hiddenEditor")) {
      this._domNode.classList.remove("hiddenEditor");
    }
    const editorWidth = layoutInfo.width;
    const minimapWidth = layoutInfo.minimap.minimapWidth;
    let collapsedFindWidget = false;
    let reducedFindWidget = false;
    let narrowFindWidget = false;
    if (this._resized) {
      let widgetWidth = getTotalWidth(this._domNode);
      if (widgetWidth > FIND_WIDGET_INITIAL_WIDTH) {
        this._domNode.style.maxWidth = `${editorWidth - 28 - minimapWidth - 15}px`;
        this._replaceInput.width = getTotalWidth(this._findInput.domNode);
        return;
      }
    }
    if (FIND_WIDGET_INITIAL_WIDTH + 28 + minimapWidth >= editorWidth) {
      reducedFindWidget = true;
    }
    if (FIND_WIDGET_INITIAL_WIDTH + 28 + minimapWidth - MAX_MATCHES_COUNT_WIDTH >= editorWidth) {
      narrowFindWidget = true;
    }
    if (FIND_WIDGET_INITIAL_WIDTH + 28 + minimapWidth - MAX_MATCHES_COUNT_WIDTH >= editorWidth + 50) {
      collapsedFindWidget = true;
    }
    this._domNode.classList.toggle("collapsed-find-widget", collapsedFindWidget);
    this._domNode.classList.toggle("narrow-find-widget", narrowFindWidget);
    this._domNode.classList.toggle("reduced-find-widget", reducedFindWidget);
    if (!narrowFindWidget && !collapsedFindWidget) {
      this._domNode.style.maxWidth = `${editorWidth - 28 - minimapWidth - 15}px`;
    }
    if (this._resized) {
      this._findInput.inputBox.layout();
      let findInputWidth = this._findInput.inputBox.element.clientWidth;
      if (findInputWidth > 0) {
        this._replaceInput.width = findInputWidth;
      }
    } else if (this._isReplaceVisible) {
      this._replaceInput.width = getTotalWidth(this._findInput.domNode);
    }
  }
  _getHeight() {
    let totalheight = 0;
    totalheight += 4;
    totalheight += this._findInput.inputBox.height + 2;
    if (this._isReplaceVisible) {
      totalheight += 4;
      totalheight += this._replaceInput.inputBox.height + 2;
    }
    totalheight += 4;
    return totalheight;
  }
  _tryUpdateHeight() {
    const totalHeight = this._getHeight();
    if (this._cachedHeight !== null && this._cachedHeight === totalHeight) {
      return false;
    }
    this._cachedHeight = totalHeight;
    this._domNode.style.height = `${totalHeight}px`;
    return true;
  }
  // ----- Public
  focusFindInput() {
    this._findInput.select();
    this._findInput.focus();
  }
  focusReplaceInput() {
    this._replaceInput.select();
    this._replaceInput.focus();
  }
  highlightFindOptions() {
    this._findInput.highlightFindOptions();
  }
  _updateSearchScope() {
    if (!this._codeEditor.hasModel()) {
      return;
    }
    if (this._toggleSelectionFind.checked) {
      let selections = this._codeEditor.getSelections();
      selections.map((selection) => {
        if (selection.endColumn === 1 && selection.endLineNumber > selection.startLineNumber) {
          selection = selection.setEndPosition(selection.endLineNumber - 1, this._codeEditor.getModel().getLineMaxColumn(selection.endLineNumber - 1));
        }
        const currentMatch = this._state.currentMatch;
        if (selection.startLineNumber !== selection.endLineNumber) {
          if (!Range.equalsRange(selection, currentMatch)) {
            return selection;
          }
        }
        return null;
      }).filter((element) => !!element);
      if (selections.length) {
        this._state.change({ searchScope: selections }, true);
      }
    }
  }
  _onFindInputMouseDown(e) {
    if (e.middleButton) {
      e.stopPropagation();
    }
  }
  _onFindInputKeyDown(e) {
    if (e.equals(
      ctrlKeyMod | 3
      /* Enter */
    )) {
      if (this._keybindingService.dispatchEvent(e, e.target)) {
        e.preventDefault();
        return;
      } else {
        this._findInput.inputBox.insertAtCursor("\n");
        e.preventDefault();
        return;
      }
    }
    if (e.equals(
      2
      /* Tab */
    )) {
      if (this._isReplaceVisible) {
        this._replaceInput.focus();
      } else {
        this._findInput.focusOnCaseSensitive();
      }
      e.preventDefault();
      return;
    }
    if (e.equals(
      2048 | 18
      /* DownArrow */
    )) {
      this._codeEditor.focus();
      e.preventDefault();
      return;
    }
    if (e.equals(
      16
      /* UpArrow */
    )) {
      return stopPropagationForMultiLineUpwards(e, this._findInput.getValue(), this._findInput.domNode.querySelector("textarea"));
    }
    if (e.equals(
      18
      /* DownArrow */
    )) {
      return stopPropagationForMultiLineDownwards(e, this._findInput.getValue(), this._findInput.domNode.querySelector("textarea"));
    }
  }
  _onReplaceInputKeyDown(e) {
    if (e.equals(
      ctrlKeyMod | 3
      /* Enter */
    )) {
      if (this._keybindingService.dispatchEvent(e, e.target)) {
        e.preventDefault();
        return;
      } else {
        if (isWindows && isNative && !this._ctrlEnterReplaceAllWarningPrompted) {
          this._notificationService.info(localize("ctrlEnter.keybindingChanged", "Ctrl+Enter now inserts line break instead of replacing all. You can modify the keybinding for editor.action.replaceAll to override this behavior."));
          this._ctrlEnterReplaceAllWarningPrompted = true;
          this._storageService.store(
            ctrlEnterReplaceAllWarningPromptedKey,
            true,
            0,
            0
            /* USER */
          );
        }
        this._replaceInput.inputBox.insertAtCursor("\n");
        e.preventDefault();
        return;
      }
    }
    if (e.equals(
      2
      /* Tab */
    )) {
      this._findInput.focusOnCaseSensitive();
      e.preventDefault();
      return;
    }
    if (e.equals(
      1024 | 2
      /* Tab */
    )) {
      this._findInput.focus();
      e.preventDefault();
      return;
    }
    if (e.equals(
      2048 | 18
      /* DownArrow */
    )) {
      this._codeEditor.focus();
      e.preventDefault();
      return;
    }
    if (e.equals(
      16
      /* UpArrow */
    )) {
      return stopPropagationForMultiLineUpwards(e, this._replaceInput.inputBox.value, this._replaceInput.inputBox.element.querySelector("textarea"));
    }
    if (e.equals(
      18
      /* DownArrow */
    )) {
      return stopPropagationForMultiLineDownwards(e, this._replaceInput.inputBox.value, this._replaceInput.inputBox.element.querySelector("textarea"));
    }
  }
  // ----- sash
  getVerticalSashLeft(_sash) {
    return 0;
  }
  // ----- initialization
  _keybindingLabelFor(actionId) {
    let kb = this._keybindingService.lookupKeybinding(actionId);
    if (!kb) {
      return "";
    }
    return ` (${kb.getLabel()})`;
  }
  _buildDomNode() {
    const flexibleHeight = true;
    const flexibleWidth = true;
    this._findInput = this._register(new ContextScopedFindInput(null, this._contextViewProvider, {
      width: FIND_INPUT_AREA_WIDTH,
      label: NLS_FIND_INPUT_LABEL,
      placeholder: NLS_FIND_INPUT_PLACEHOLDER,
      appendCaseSensitiveLabel: this._keybindingLabelFor(FIND_IDS.ToggleCaseSensitiveCommand),
      appendWholeWordsLabel: this._keybindingLabelFor(FIND_IDS.ToggleWholeWordCommand),
      appendRegexLabel: this._keybindingLabelFor(FIND_IDS.ToggleRegexCommand),
      validation: (value) => {
        if (value.length === 0 || !this._findInput.getRegex()) {
          return null;
        }
        try {
          new RegExp(value, "gu");
          return null;
        } catch (e) {
          return { content: e.message };
        }
      },
      flexibleHeight,
      flexibleWidth,
      flexibleMaxHeight: 118,
      showHistoryHint: () => showHistoryKeybindingHint(this._keybindingService)
    }, this._contextKeyService, true));
    this._findInput.setRegex(!!this._state.isRegex);
    this._findInput.setCaseSensitive(!!this._state.matchCase);
    this._findInput.setWholeWords(!!this._state.wholeWord);
    this._register(this._findInput.onKeyDown((e) => this._onFindInputKeyDown(e)));
    this._register(this._findInput.inputBox.onDidChange(() => {
      if (this._ignoreChangeEvent) {
        return;
      }
      this._state.change({ searchString: this._findInput.getValue() }, true);
    }));
    this._register(this._findInput.onDidOptionChange(() => {
      this._state.change({
        isRegex: this._findInput.getRegex(),
        wholeWord: this._findInput.getWholeWords(),
        matchCase: this._findInput.getCaseSensitive()
      }, true);
    }));
    this._register(this._findInput.onCaseSensitiveKeyDown((e) => {
      if (e.equals(
        1024 | 2
        /* Tab */
      )) {
        if (this._isReplaceVisible) {
          this._replaceInput.focus();
          e.preventDefault();
        }
      }
    }));
    this._register(this._findInput.onRegexKeyDown((e) => {
      if (e.equals(
        2
        /* Tab */
      )) {
        if (this._isReplaceVisible) {
          this._replaceInput.focusOnPreserve();
          e.preventDefault();
        }
      }
    }));
    this._register(this._findInput.inputBox.onDidHeightChange((e) => {
      if (this._tryUpdateHeight()) {
        this._showViewZone();
      }
    }));
    if (isLinux) {
      this._register(this._findInput.onMouseDown((e) => this._onFindInputMouseDown(e)));
    }
    this._matchesCount = document.createElement("div");
    this._matchesCount.className = "matchesCount";
    this._updateMatchesCount();
    this._prevBtn = this._register(new SimpleButton({
      label: NLS_PREVIOUS_MATCH_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.PreviousMatchFindAction),
      icon: findPreviousMatchIcon,
      onTrigger: () => {
        this._codeEditor.getAction(FIND_IDS.PreviousMatchFindAction).run().then(void 0, onUnexpectedError);
      }
    }));
    this._nextBtn = this._register(new SimpleButton({
      label: NLS_NEXT_MATCH_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.NextMatchFindAction),
      icon: findNextMatchIcon,
      onTrigger: () => {
        this._codeEditor.getAction(FIND_IDS.NextMatchFindAction).run().then(void 0, onUnexpectedError);
      }
    }));
    let findPart = document.createElement("div");
    findPart.className = "find-part";
    findPart.appendChild(this._findInput.domNode);
    const actionsContainer = document.createElement("div");
    actionsContainer.className = "find-actions";
    findPart.appendChild(actionsContainer);
    actionsContainer.appendChild(this._matchesCount);
    actionsContainer.appendChild(this._prevBtn.domNode);
    actionsContainer.appendChild(this._nextBtn.domNode);
    this._toggleSelectionFind = this._register(new Checkbox({
      icon: findSelectionIcon,
      title: NLS_TOGGLE_SELECTION_FIND_TITLE + this._keybindingLabelFor(FIND_IDS.ToggleSearchScopeCommand),
      isChecked: false
    }));
    this._register(this._toggleSelectionFind.onChange(() => {
      if (this._toggleSelectionFind.checked) {
        if (this._codeEditor.hasModel()) {
          let selections = this._codeEditor.getSelections();
          selections.map((selection) => {
            if (selection.endColumn === 1 && selection.endLineNumber > selection.startLineNumber) {
              selection = selection.setEndPosition(selection.endLineNumber - 1, this._codeEditor.getModel().getLineMaxColumn(selection.endLineNumber - 1));
            }
            if (!selection.isEmpty()) {
              return selection;
            }
            return null;
          }).filter((element) => !!element);
          if (selections.length) {
            this._state.change({ searchScope: selections }, true);
          }
        }
      } else {
        this._state.change({ searchScope: null }, true);
      }
    }));
    actionsContainer.appendChild(this._toggleSelectionFind.domNode);
    this._closeBtn = this._register(new SimpleButton({
      label: NLS_CLOSE_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.CloseFindWidgetCommand),
      icon: widgetClose,
      onTrigger: () => {
        this._state.change({ isRevealed: false, searchScope: null }, false);
      },
      onKeyDown: (e) => {
        if (e.equals(
          2
          /* Tab */
        )) {
          if (this._isReplaceVisible) {
            if (this._replaceBtn.isEnabled()) {
              this._replaceBtn.focus();
            } else {
              this._codeEditor.focus();
            }
            e.preventDefault();
          }
        }
      }
    }));
    actionsContainer.appendChild(this._closeBtn.domNode);
    this._replaceInput = this._register(new ContextScopedReplaceInput(null, void 0, {
      label: NLS_REPLACE_INPUT_LABEL,
      placeholder: NLS_REPLACE_INPUT_PLACEHOLDER,
      appendPreserveCaseLabel: this._keybindingLabelFor(FIND_IDS.TogglePreserveCaseCommand),
      history: [],
      flexibleHeight,
      flexibleWidth,
      flexibleMaxHeight: 118,
      showHistoryHint: () => showHistoryKeybindingHint(this._keybindingService)
    }, this._contextKeyService, true));
    this._replaceInput.setPreserveCase(!!this._state.preserveCase);
    this._register(this._replaceInput.onKeyDown((e) => this._onReplaceInputKeyDown(e)));
    this._register(this._replaceInput.inputBox.onDidChange(() => {
      this._state.change({ replaceString: this._replaceInput.inputBox.value }, false);
    }));
    this._register(this._replaceInput.inputBox.onDidHeightChange((e) => {
      if (this._isReplaceVisible && this._tryUpdateHeight()) {
        this._showViewZone();
      }
    }));
    this._register(this._replaceInput.onDidOptionChange(() => {
      this._state.change({
        preserveCase: this._replaceInput.getPreserveCase()
      }, true);
    }));
    this._register(this._replaceInput.onPreserveCaseKeyDown((e) => {
      if (e.equals(
        2
        /* Tab */
      )) {
        if (this._prevBtn.isEnabled()) {
          this._prevBtn.focus();
        } else if (this._nextBtn.isEnabled()) {
          this._nextBtn.focus();
        } else if (this._toggleSelectionFind.enabled) {
          this._toggleSelectionFind.focus();
        } else if (this._closeBtn.isEnabled()) {
          this._closeBtn.focus();
        }
        e.preventDefault();
      }
    }));
    this._replaceBtn = this._register(new SimpleButton({
      label: NLS_REPLACE_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.ReplaceOneAction),
      icon: findReplaceIcon,
      onTrigger: () => {
        this._controller.replace();
      },
      onKeyDown: (e) => {
        if (e.equals(
          1024 | 2
          /* Tab */
        )) {
          this._closeBtn.focus();
          e.preventDefault();
        }
      }
    }));
    this._replaceAllBtn = this._register(new SimpleButton({
      label: NLS_REPLACE_ALL_BTN_LABEL + this._keybindingLabelFor(FIND_IDS.ReplaceAllAction),
      icon: findReplaceAllIcon,
      onTrigger: () => {
        this._controller.replaceAll();
      }
    }));
    let replacePart = document.createElement("div");
    replacePart.className = "replace-part";
    replacePart.appendChild(this._replaceInput.domNode);
    const replaceActionsContainer = document.createElement("div");
    replaceActionsContainer.className = "replace-actions";
    replacePart.appendChild(replaceActionsContainer);
    replaceActionsContainer.appendChild(this._replaceBtn.domNode);
    replaceActionsContainer.appendChild(this._replaceAllBtn.domNode);
    this._toggleReplaceBtn = this._register(new SimpleButton({
      label: NLS_TOGGLE_REPLACE_MODE_BTN_LABEL,
      className: "codicon toggle left",
      onTrigger: () => {
        this._state.change({ isReplaceRevealed: !this._isReplaceVisible }, false);
        if (this._isReplaceVisible) {
          this._replaceInput.width = getTotalWidth(this._findInput.domNode);
          this._replaceInput.inputBox.layout();
        }
        this._showViewZone();
      }
    }));
    this._toggleReplaceBtn.setExpanded(this._isReplaceVisible);
    this._domNode = document.createElement("div");
    this._domNode.className = "editor-widget find-widget";
    this._domNode.setAttribute("aria-hidden", "true");
    this._domNode.style.width = `${FIND_WIDGET_INITIAL_WIDTH}px`;
    this._domNode.appendChild(this._toggleReplaceBtn.domNode);
    this._domNode.appendChild(findPart);
    this._domNode.appendChild(replacePart);
    this._resizeSash = new Sash(this._domNode, this, { orientation: 0, size: 2 });
    this._resized = false;
    let originalWidth = FIND_WIDGET_INITIAL_WIDTH;
    this._register(this._resizeSash.onDidStart(() => {
      originalWidth = getTotalWidth(this._domNode);
    }));
    this._register(this._resizeSash.onDidChange((evt) => {
      this._resized = true;
      let width = originalWidth + evt.startX - evt.currentX;
      if (width < FIND_WIDGET_INITIAL_WIDTH) {
        return;
      }
      const maxWidth = parseFloat(getComputedStyle(this._domNode).maxWidth) || 0;
      if (width > maxWidth) {
        return;
      }
      this._domNode.style.width = `${width}px`;
      if (this._isReplaceVisible) {
        this._replaceInput.width = getTotalWidth(this._findInput.domNode);
      }
      this._findInput.inputBox.layout();
      this._tryUpdateHeight();
    }));
    this._register(this._resizeSash.onDidReset(() => {
      const currentWidth = getTotalWidth(this._domNode);
      if (currentWidth < FIND_WIDGET_INITIAL_WIDTH) {
        return;
      }
      let width = FIND_WIDGET_INITIAL_WIDTH;
      if (!this._resized || currentWidth === FIND_WIDGET_INITIAL_WIDTH) {
        const layoutInfo = this._codeEditor.getLayoutInfo();
        width = layoutInfo.width - 28 - layoutInfo.minimap.minimapWidth - 15;
        this._resized = true;
      } else {
      }
      this._domNode.style.width = `${width}px`;
      if (this._isReplaceVisible) {
        this._replaceInput.width = getTotalWidth(this._findInput.domNode);
      }
      this._findInput.inputBox.layout();
    }));
  }
  updateAccessibilitySupport() {
    const value = this._codeEditor.getOption(
      2
      /* accessibilitySupport */
    );
    this._findInput.setFocusInputOnOptionClick(
      value !== 2
      /* Enabled */
    );
  }
};
FindWidget.ID = "editor.contrib.findWidget";
var SimpleButton = class extends Widget {
  constructor(opts) {
    super();
    this._opts = opts;
    let className = "button";
    if (this._opts.className) {
      className = className + " " + this._opts.className;
    }
    if (this._opts.icon) {
      className = className + " " + ThemeIcon.asClassName(this._opts.icon);
    }
    this._domNode = document.createElement("div");
    this._domNode.title = this._opts.label;
    this._domNode.tabIndex = 0;
    this._domNode.className = className;
    this._domNode.setAttribute("role", "button");
    this._domNode.setAttribute("aria-label", this._opts.label);
    this.onclick(this._domNode, (e) => {
      this._opts.onTrigger();
      e.preventDefault();
    });
    this.onkeydown(this._domNode, (e) => {
      if (e.equals(
        10
        /* Space */
      ) || e.equals(
        3
        /* Enter */
      )) {
        this._opts.onTrigger();
        e.preventDefault();
        return;
      }
      if (this._opts.onKeyDown) {
        this._opts.onKeyDown(e);
      }
    });
  }
  get domNode() {
    return this._domNode;
  }
  isEnabled() {
    return this._domNode.tabIndex >= 0;
  }
  focus() {
    this._domNode.focus();
  }
  setEnabled(enabled) {
    this._domNode.classList.toggle("disabled", !enabled);
    this._domNode.setAttribute("aria-disabled", String(!enabled));
    this._domNode.tabIndex = enabled ? 0 : -1;
  }
  setExpanded(expanded) {
    this._domNode.setAttribute("aria-expanded", String(!!expanded));
    if (expanded) {
      this._domNode.classList.remove(...ThemeIcon.asClassNameArray(findCollapsedIcon));
      this._domNode.classList.add(...ThemeIcon.asClassNameArray(findExpandedIcon));
    } else {
      this._domNode.classList.remove(...ThemeIcon.asClassNameArray(findExpandedIcon));
      this._domNode.classList.add(...ThemeIcon.asClassNameArray(findCollapsedIcon));
    }
  }
};
registerThemingParticipant((theme, collector) => {
  const addBackgroundColorRule = (selector, color) => {
    if (color) {
      collector.addRule(`.monaco-editor ${selector} { background-color: ${color}; }`);
    }
  };
  addBackgroundColorRule(".findMatch", theme.getColor(editorFindMatchHighlight));
  addBackgroundColorRule(".currentFindMatch", theme.getColor(editorFindMatch));
  addBackgroundColorRule(".findScope", theme.getColor(editorFindRangeHighlight));
  const widgetBackground = theme.getColor(editorWidgetBackground);
  addBackgroundColorRule(".find-widget", widgetBackground);
  const widgetShadowColor = theme.getColor(widgetShadow);
  if (widgetShadowColor) {
    collector.addRule(`.monaco-editor .find-widget { box-shadow: 0 0 8px 2px ${widgetShadowColor}; }`);
  }
  const findMatchHighlightBorder = theme.getColor(editorFindMatchHighlightBorder);
  if (findMatchHighlightBorder) {
    collector.addRule(`.monaco-editor .findMatch { border: 1px ${theme.type === "hc" ? "dotted" : "solid"} ${findMatchHighlightBorder}; box-sizing: border-box; }`);
  }
  const findMatchBorder = theme.getColor(editorFindMatchBorder);
  if (findMatchBorder) {
    collector.addRule(`.monaco-editor .currentFindMatch { border: 2px solid ${findMatchBorder}; padding: 1px; box-sizing: border-box; }`);
  }
  const findRangeHighlightBorder = theme.getColor(editorFindRangeHighlightBorder);
  if (findRangeHighlightBorder) {
    collector.addRule(`.monaco-editor .findScope { border: 1px ${theme.type === "hc" ? "dashed" : "solid"} ${findRangeHighlightBorder}; }`);
  }
  const hcBorder = theme.getColor(contrastBorder);
  if (hcBorder) {
    collector.addRule(`.monaco-editor .find-widget { border: 1px solid ${hcBorder}; }`);
  }
  const foreground = theme.getColor(editorWidgetForeground);
  if (foreground) {
    collector.addRule(`.monaco-editor .find-widget { color: ${foreground}; }`);
  }
  const error = theme.getColor(errorForeground);
  if (error) {
    collector.addRule(`.monaco-editor .find-widget.no-results .matchesCount { color: ${error}; }`);
  }
  const resizeBorderBackground = theme.getColor(editorWidgetResizeBorder);
  if (resizeBorderBackground) {
    collector.addRule(`.monaco-editor .find-widget .monaco-sash { background-color: ${resizeBorderBackground}; }`);
  } else {
    const border = theme.getColor(editorWidgetBorder);
    if (border) {
      collector.addRule(`.monaco-editor .find-widget .monaco-sash { background-color: ${border}; }`);
    }
  }
  const toolbarHoverBackgroundColor = theme.getColor(toolbarHoverBackground);
  if (toolbarHoverBackgroundColor) {
    collector.addRule(`
		.monaco-editor .find-widget .button:not(.disabled):hover,
		.monaco-editor .find-widget .codicon-find-selection:hover {
			background-color: ${toolbarHoverBackgroundColor} !important;
		}
	`);
  }
  const focusOutline = theme.getColor(focusBorder);
  if (focusOutline) {
    collector.addRule(`.monaco-editor .find-widget .monaco-inputbox.synthetic-focus { outline-color: ${focusOutline}; }`);
  }
});

// node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findController.js
var __decorate2 = function(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param2 = function(paramIndex, decorator) {
  return function(target, key) {
    decorator(target, key, paramIndex);
  };
};
var __awaiter2 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var SEARCH_STRING_MAX_LENGTH = 524288;
function getSelectionSearchString(editor, seedSearchStringFromSelection = "single", seedSearchStringFromNonEmptySelection = false) {
  if (!editor.hasModel()) {
    return null;
  }
  const selection = editor.getSelection();
  if (seedSearchStringFromSelection === "single" && selection.startLineNumber === selection.endLineNumber || seedSearchStringFromSelection === "multiple") {
    if (selection.isEmpty()) {
      const wordAtPosition = editor.getConfiguredWordAtPosition(selection.getStartPosition());
      if (wordAtPosition && false === seedSearchStringFromNonEmptySelection) {
        return wordAtPosition.word;
      }
    } else {
      if (editor.getModel().getValueLengthInRange(selection) < SEARCH_STRING_MAX_LENGTH) {
        return editor.getModel().getValueInRange(selection);
      }
    }
  }
  return null;
}
var CommonFindController = class CommonFindController2 extends Disposable {
  constructor(editor, contextKeyService, storageService, clipboardService) {
    super();
    this._editor = editor;
    this._findWidgetVisible = CONTEXT_FIND_WIDGET_VISIBLE.bindTo(contextKeyService);
    this._contextKeyService = contextKeyService;
    this._storageService = storageService;
    this._clipboardService = clipboardService;
    this._updateHistoryDelayer = new Delayer(500);
    this._state = this._register(new FindReplaceState());
    this.loadQueryState();
    this._register(this._state.onFindReplaceStateChange((e) => this._onStateChanged(e)));
    this._model = null;
    this._register(this._editor.onDidChangeModel(() => {
      let shouldRestartFind = this._editor.getModel() && this._state.isRevealed;
      this.disposeModel();
      this._state.change({
        searchScope: null,
        matchCase: this._storageService.getBoolean("editor.matchCase", 1, false),
        wholeWord: this._storageService.getBoolean("editor.wholeWord", 1, false),
        isRegex: this._storageService.getBoolean("editor.isRegex", 1, false),
        preserveCase: this._storageService.getBoolean("editor.preserveCase", 1, false)
      }, false);
      if (shouldRestartFind) {
        this._start({
          forceRevealReplace: false,
          seedSearchStringFromSelection: "none",
          seedSearchStringFromNonEmptySelection: false,
          seedSearchStringFromGlobalClipboard: false,
          shouldFocus: 0,
          shouldAnimate: false,
          updateSearchScope: false,
          loop: this._editor.getOption(
            35
            /* find */
          ).loop
        });
      }
    }));
  }
  get editor() {
    return this._editor;
  }
  static get(editor) {
    return editor.getContribution(CommonFindController2.ID);
  }
  dispose() {
    this.disposeModel();
    super.dispose();
  }
  disposeModel() {
    if (this._model) {
      this._model.dispose();
      this._model = null;
    }
  }
  _onStateChanged(e) {
    this.saveQueryState(e);
    if (e.isRevealed) {
      if (this._state.isRevealed) {
        this._findWidgetVisible.set(true);
      } else {
        this._findWidgetVisible.reset();
        this.disposeModel();
      }
    }
    if (e.searchString) {
      this.setGlobalBufferTerm(this._state.searchString);
    }
  }
  saveQueryState(e) {
    if (e.isRegex) {
      this._storageService.store(
        "editor.isRegex",
        this._state.actualIsRegex,
        1,
        0
        /* USER */
      );
    }
    if (e.wholeWord) {
      this._storageService.store(
        "editor.wholeWord",
        this._state.actualWholeWord,
        1,
        0
        /* USER */
      );
    }
    if (e.matchCase) {
      this._storageService.store(
        "editor.matchCase",
        this._state.actualMatchCase,
        1,
        0
        /* USER */
      );
    }
    if (e.preserveCase) {
      this._storageService.store(
        "editor.preserveCase",
        this._state.actualPreserveCase,
        1,
        0
        /* USER */
      );
    }
  }
  loadQueryState() {
    this._state.change({
      matchCase: this._storageService.getBoolean("editor.matchCase", 1, this._state.matchCase),
      wholeWord: this._storageService.getBoolean("editor.wholeWord", 1, this._state.wholeWord),
      isRegex: this._storageService.getBoolean("editor.isRegex", 1, this._state.isRegex),
      preserveCase: this._storageService.getBoolean("editor.preserveCase", 1, this._state.preserveCase)
    }, false);
  }
  isFindInputFocused() {
    return !!CONTEXT_FIND_INPUT_FOCUSED.getValue(this._contextKeyService);
  }
  getState() {
    return this._state;
  }
  closeFindWidget() {
    this._state.change({
      isRevealed: false,
      searchScope: null
    }, false);
    this._editor.focus();
  }
  toggleCaseSensitive() {
    this._state.change({ matchCase: !this._state.matchCase }, false);
    if (!this._state.isRevealed) {
      this.highlightFindOptions();
    }
  }
  toggleWholeWords() {
    this._state.change({ wholeWord: !this._state.wholeWord }, false);
    if (!this._state.isRevealed) {
      this.highlightFindOptions();
    }
  }
  toggleRegex() {
    this._state.change({ isRegex: !this._state.isRegex }, false);
    if (!this._state.isRevealed) {
      this.highlightFindOptions();
    }
  }
  togglePreserveCase() {
    this._state.change({ preserveCase: !this._state.preserveCase }, false);
    if (!this._state.isRevealed) {
      this.highlightFindOptions();
    }
  }
  toggleSearchScope() {
    if (this._state.searchScope) {
      this._state.change({ searchScope: null }, true);
    } else {
      if (this._editor.hasModel()) {
        let selections = this._editor.getSelections();
        selections.map((selection) => {
          if (selection.endColumn === 1 && selection.endLineNumber > selection.startLineNumber) {
            selection = selection.setEndPosition(selection.endLineNumber - 1, this._editor.getModel().getLineMaxColumn(selection.endLineNumber - 1));
          }
          if (!selection.isEmpty()) {
            return selection;
          }
          return null;
        }).filter((element) => !!element);
        if (selections.length) {
          this._state.change({ searchScope: selections }, true);
        }
      }
    }
  }
  setSearchString(searchString) {
    if (this._state.isRegex) {
      searchString = escapeRegExpCharacters(searchString);
    }
    this._state.change({ searchString }, false);
  }
  highlightFindOptions(ignoreWhenVisible = false) {
  }
  _start(opts, newState) {
    return __awaiter2(this, void 0, void 0, function* () {
      this.disposeModel();
      if (!this._editor.hasModel()) {
        return;
      }
      let stateChanges = Object.assign(Object.assign({}, newState), { isRevealed: true });
      if (opts.seedSearchStringFromSelection === "single") {
        let selectionSearchString = getSelectionSearchString(this._editor, opts.seedSearchStringFromSelection, opts.seedSearchStringFromNonEmptySelection);
        if (selectionSearchString) {
          if (this._state.isRegex) {
            stateChanges.searchString = escapeRegExpCharacters(selectionSearchString);
          } else {
            stateChanges.searchString = selectionSearchString;
          }
        }
      } else if (opts.seedSearchStringFromSelection === "multiple" && !opts.updateSearchScope) {
        let selectionSearchString = getSelectionSearchString(this._editor, opts.seedSearchStringFromSelection);
        if (selectionSearchString) {
          stateChanges.searchString = selectionSearchString;
        }
      }
      if (!stateChanges.searchString && opts.seedSearchStringFromGlobalClipboard) {
        let selectionSearchString = yield this.getGlobalBufferTerm();
        if (!this._editor.hasModel()) {
          return;
        }
        if (selectionSearchString) {
          stateChanges.searchString = selectionSearchString;
        }
      }
      if (opts.forceRevealReplace || stateChanges.isReplaceRevealed) {
        stateChanges.isReplaceRevealed = true;
      } else if (!this._findWidgetVisible.get()) {
        stateChanges.isReplaceRevealed = false;
      }
      if (opts.updateSearchScope) {
        let currentSelections = this._editor.getSelections();
        if (currentSelections.some((selection) => !selection.isEmpty())) {
          stateChanges.searchScope = currentSelections;
        }
      }
      stateChanges.loop = opts.loop;
      this._state.change(stateChanges, false);
      if (!this._model) {
        this._model = new FindModelBoundToEditorModel(this._editor, this._state);
      }
    });
  }
  start(opts, newState) {
    return this._start(opts, newState);
  }
  moveToNextMatch() {
    if (this._model) {
      this._model.moveToNextMatch();
      return true;
    }
    return false;
  }
  moveToPrevMatch() {
    if (this._model) {
      this._model.moveToPrevMatch();
      return true;
    }
    return false;
  }
  replace() {
    if (this._model) {
      this._model.replace();
      return true;
    }
    return false;
  }
  replaceAll() {
    if (this._model) {
      this._model.replaceAll();
      return true;
    }
    return false;
  }
  selectAllMatches() {
    if (this._model) {
      this._model.selectAllMatches();
      this._editor.focus();
      return true;
    }
    return false;
  }
  getGlobalBufferTerm() {
    return __awaiter2(this, void 0, void 0, function* () {
      if (this._editor.getOption(
        35
        /* find */
      ).globalFindClipboard && this._editor.hasModel() && !this._editor.getModel().isTooLargeForSyncing()) {
        return this._clipboardService.readFindText();
      }
      return "";
    });
  }
  setGlobalBufferTerm(text) {
    if (this._editor.getOption(
      35
      /* find */
    ).globalFindClipboard && this._editor.hasModel() && !this._editor.getModel().isTooLargeForSyncing()) {
      this._clipboardService.writeFindText(text);
    }
  }
};
CommonFindController.ID = "editor.contrib.findController";
CommonFindController = __decorate2([
  __param2(1, IContextKeyService),
  __param2(2, IStorageService),
  __param2(3, IClipboardService)
], CommonFindController);
var FindController = class FindController2 extends CommonFindController {
  constructor(editor, _contextViewService, _contextKeyService, _keybindingService, _themeService, _notificationService, _storageService, clipboardService) {
    super(editor, _contextKeyService, _storageService, clipboardService);
    this._contextViewService = _contextViewService;
    this._keybindingService = _keybindingService;
    this._themeService = _themeService;
    this._notificationService = _notificationService;
    this._widget = null;
    this._findOptionsWidget = null;
  }
  _start(opts, newState) {
    const _super = Object.create(null, {
      _start: { get: () => super._start }
    });
    return __awaiter2(this, void 0, void 0, function* () {
      if (!this._widget) {
        this._createFindWidget();
      }
      const selection = this._editor.getSelection();
      let updateSearchScope = false;
      switch (this._editor.getOption(
        35
        /* find */
      ).autoFindInSelection) {
        case "always":
          updateSearchScope = true;
          break;
        case "never":
          updateSearchScope = false;
          break;
        case "multiline": {
          const isSelectionMultipleLine = !!selection && selection.startLineNumber !== selection.endLineNumber;
          updateSearchScope = isSelectionMultipleLine;
          break;
        }
        default:
          break;
      }
      opts.updateSearchScope = opts.updateSearchScope || updateSearchScope;
      yield _super._start.call(this, opts, newState);
      if (this._widget) {
        if (opts.shouldFocus === 2) {
          this._widget.focusReplaceInput();
        } else if (opts.shouldFocus === 1) {
          this._widget.focusFindInput();
        }
      }
    });
  }
  highlightFindOptions(ignoreWhenVisible = false) {
    if (!this._widget) {
      this._createFindWidget();
    }
    if (this._state.isRevealed && !ignoreWhenVisible) {
      this._widget.highlightFindOptions();
    } else {
      this._findOptionsWidget.highlightFindOptions();
    }
  }
  _createFindWidget() {
    this._widget = this._register(new FindWidget(this._editor, this, this._state, this._contextViewService, this._keybindingService, this._contextKeyService, this._themeService, this._storageService, this._notificationService));
    this._findOptionsWidget = this._register(new FindOptionsWidget(this._editor, this._state, this._keybindingService, this._themeService));
  }
};
FindController = __decorate2([
  __param2(1, IContextViewService),
  __param2(2, IContextKeyService),
  __param2(3, IKeybindingService),
  __param2(4, IThemeService),
  __param2(5, INotificationService),
  __param2(6, IStorageService),
  __param2(7, IClipboardService)
], FindController);
var StartFindAction = registerMultiEditorAction(new MultiEditorAction({
  id: FIND_IDS.StartFindAction,
  label: localize("startFindAction", "Find"),
  alias: "Find",
  precondition: ContextKeyExpr.or(EditorContextKeys.focus, ContextKeyExpr.has("editorIsOpen")),
  kbOpts: {
    kbExpr: null,
    primary: 2048 | 36,
    weight: 100
    /* EditorContrib */
  },
  menuOpts: {
    menuId: MenuId.MenubarEditMenu,
    group: "3_find",
    title: localize({ key: "miFind", comment: ["&& denotes a mnemonic"] }, "&&Find"),
    order: 1
  }
}));
StartFindAction.addImplementation(0, (accessor, editor, args) => {
  const controller = CommonFindController.get(editor);
  if (!controller) {
    return false;
  }
  return controller.start({
    forceRevealReplace: false,
    seedSearchStringFromSelection: editor.getOption(
      35
      /* find */
    ).seedSearchStringFromSelection !== "never" ? "single" : "none",
    seedSearchStringFromNonEmptySelection: editor.getOption(
      35
      /* find */
    ).seedSearchStringFromSelection === "selection",
    seedSearchStringFromGlobalClipboard: editor.getOption(
      35
      /* find */
    ).globalFindClipboard,
    shouldFocus: 1,
    shouldAnimate: true,
    updateSearchScope: false,
    loop: editor.getOption(
      35
      /* find */
    ).loop
  });
});
var findArgDescription = {
  description: "Open a new In-Editor Find Widget.",
  args: [{
    name: "Open a new In-Editor Find Widget args",
    schema: {
      properties: {
        searchString: { type: "string" },
        replaceString: { type: "string" },
        regex: { type: "boolean" },
        regexOverride: {
          type: "number",
          description: localize("actions.find.isRegexOverride", 'Overrides "Use Regular Expression" flag.\nThe flag will not be saved for the future.\n0: Do Nothing\n1: True\n2: False')
        },
        wholeWord: { type: "boolean" },
        wholeWordOverride: {
          type: "number",
          description: localize("actions.find.wholeWordOverride", 'Overrides "Match Whole Word" flag.\nThe flag will not be saved for the future.\n0: Do Nothing\n1: True\n2: False')
        },
        matchCase: { type: "boolean" },
        matchCaseOverride: {
          type: "number",
          description: localize("actions.find.matchCaseOverride", 'Overrides "Math Case" flag.\nThe flag will not be saved for the future.\n0: Do Nothing\n1: True\n2: False')
        },
        preserveCase: { type: "boolean" },
        preserveCaseOverride: {
          type: "number",
          description: localize("actions.find.preserveCaseOverride", 'Overrides "Preserve Case" flag.\nThe flag will not be saved for the future.\n0: Do Nothing\n1: True\n2: False')
        },
        findInSelection: { type: "boolean" }
      }
    }
  }]
};
var StartFindWithArgsAction = class extends EditorAction {
  constructor() {
    super({
      id: FIND_IDS.StartFindWithArgs,
      label: localize("startFindWithArgsAction", "Find With Arguments"),
      alias: "Find With Arguments",
      precondition: void 0,
      kbOpts: {
        kbExpr: null,
        primary: 0,
        weight: 100
        /* EditorContrib */
      },
      description: findArgDescription
    });
  }
  run(accessor, editor, args) {
    return __awaiter2(this, void 0, void 0, function* () {
      const controller = CommonFindController.get(editor);
      if (controller) {
        const newState = args ? {
          searchString: args.searchString,
          replaceString: args.replaceString,
          isReplaceRevealed: args.replaceString !== void 0,
          isRegex: args.isRegex,
          // isRegexOverride: args.regexOverride,
          wholeWord: args.matchWholeWord,
          // wholeWordOverride: args.wholeWordOverride,
          matchCase: args.isCaseSensitive,
          // matchCaseOverride: args.matchCaseOverride,
          preserveCase: args.preserveCase
          // preserveCaseOverride: args.preserveCaseOverride,
        } : {};
        yield controller.start({
          forceRevealReplace: false,
          seedSearchStringFromSelection: controller.getState().searchString.length === 0 && editor.getOption(
            35
            /* find */
          ).seedSearchStringFromSelection !== "never" ? "single" : "none",
          seedSearchStringFromNonEmptySelection: editor.getOption(
            35
            /* find */
          ).seedSearchStringFromSelection === "selection",
          seedSearchStringFromGlobalClipboard: true,
          shouldFocus: 1,
          shouldAnimate: true,
          updateSearchScope: (args === null || args === void 0 ? void 0 : args.findInSelection) || false,
          loop: editor.getOption(
            35
            /* find */
          ).loop
        }, newState);
        controller.setGlobalBufferTerm(controller.getState().searchString);
      }
    });
  }
};
var StartFindWithSelectionAction = class extends EditorAction {
  constructor() {
    super({
      id: FIND_IDS.StartFindWithSelection,
      label: localize("startFindWithSelectionAction", "Find With Selection"),
      alias: "Find With Selection",
      precondition: void 0,
      kbOpts: {
        kbExpr: null,
        primary: 0,
        mac: {
          primary: 2048 | 35
        },
        weight: 100
        /* EditorContrib */
      }
    });
  }
  run(accessor, editor) {
    return __awaiter2(this, void 0, void 0, function* () {
      const controller = CommonFindController.get(editor);
      if (controller) {
        yield controller.start({
          forceRevealReplace: false,
          seedSearchStringFromSelection: "multiple",
          seedSearchStringFromNonEmptySelection: false,
          seedSearchStringFromGlobalClipboard: false,
          shouldFocus: 0,
          shouldAnimate: true,
          updateSearchScope: false,
          loop: editor.getOption(
            35
            /* find */
          ).loop
        });
        controller.setGlobalBufferTerm(controller.getState().searchString);
      }
    });
  }
};
var MatchFindAction = class extends EditorAction {
  run(accessor, editor) {
    return __awaiter2(this, void 0, void 0, function* () {
      const controller = CommonFindController.get(editor);
      if (controller && !this._run(controller)) {
        yield controller.start({
          forceRevealReplace: false,
          seedSearchStringFromSelection: controller.getState().searchString.length === 0 && editor.getOption(
            35
            /* find */
          ).seedSearchStringFromSelection !== "never" ? "single" : "none",
          seedSearchStringFromNonEmptySelection: editor.getOption(
            35
            /* find */
          ).seedSearchStringFromSelection === "selection",
          seedSearchStringFromGlobalClipboard: true,
          shouldFocus: 0,
          shouldAnimate: true,
          updateSearchScope: false,
          loop: editor.getOption(
            35
            /* find */
          ).loop
        });
        this._run(controller);
      }
    });
  }
};
var NextMatchFindAction = class extends MatchFindAction {
  constructor() {
    super({
      id: FIND_IDS.NextMatchFindAction,
      label: localize("findNextMatchAction", "Find Next"),
      alias: "Find Next",
      precondition: void 0,
      kbOpts: [{
        kbExpr: EditorContextKeys.focus,
        primary: 61,
        mac: { primary: 2048 | 37, secondary: [
          61
          /* F3 */
        ] },
        weight: 100
        /* EditorContrib */
      }, {
        kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_FIND_INPUT_FOCUSED),
        primary: 3,
        weight: 100
        /* EditorContrib */
      }]
    });
  }
  _run(controller) {
    const result = controller.moveToNextMatch();
    if (result) {
      controller.editor.pushUndoStop();
      return true;
    }
    return false;
  }
};
var PreviousMatchFindAction = class extends MatchFindAction {
  constructor() {
    super({
      id: FIND_IDS.PreviousMatchFindAction,
      label: localize("findPreviousMatchAction", "Find Previous"),
      alias: "Find Previous",
      precondition: void 0,
      kbOpts: [
        {
          kbExpr: EditorContextKeys.focus,
          primary: 1024 | 61,
          mac: { primary: 2048 | 1024 | 37, secondary: [
            1024 | 61
            /* F3 */
          ] },
          weight: 100
          /* EditorContrib */
        },
        {
          kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_FIND_INPUT_FOCUSED),
          primary: 1024 | 3,
          weight: 100
          /* EditorContrib */
        }
      ]
    });
  }
  _run(controller) {
    return controller.moveToPrevMatch();
  }
};
var SelectionMatchFindAction = class extends EditorAction {
  run(accessor, editor) {
    return __awaiter2(this, void 0, void 0, function* () {
      const controller = CommonFindController.get(editor);
      if (!controller) {
        return;
      }
      const seedSearchStringFromNonEmptySelection = editor.getOption(
        35
        /* find */
      ).seedSearchStringFromSelection === "selection";
      let selectionSearchString = null;
      if (editor.getOption(
        35
        /* find */
      ).seedSearchStringFromSelection !== "never") {
        selectionSearchString = getSelectionSearchString(editor, "single", seedSearchStringFromNonEmptySelection);
      }
      if (selectionSearchString) {
        controller.setSearchString(selectionSearchString);
      }
      if (!this._run(controller)) {
        yield controller.start({
          forceRevealReplace: false,
          seedSearchStringFromSelection: editor.getOption(
            35
            /* find */
          ).seedSearchStringFromSelection !== "never" ? "single" : "none",
          seedSearchStringFromNonEmptySelection,
          seedSearchStringFromGlobalClipboard: false,
          shouldFocus: 0,
          shouldAnimate: true,
          updateSearchScope: false,
          loop: editor.getOption(
            35
            /* find */
          ).loop
        });
        this._run(controller);
      }
    });
  }
};
var NextSelectionMatchFindAction = class extends SelectionMatchFindAction {
  constructor() {
    super({
      id: FIND_IDS.NextSelectionMatchFindAction,
      label: localize("nextSelectionMatchFindAction", "Find Next Selection"),
      alias: "Find Next Selection",
      precondition: void 0,
      kbOpts: {
        kbExpr: EditorContextKeys.focus,
        primary: 2048 | 61,
        weight: 100
        /* EditorContrib */
      }
    });
  }
  _run(controller) {
    return controller.moveToNextMatch();
  }
};
var PreviousSelectionMatchFindAction = class extends SelectionMatchFindAction {
  constructor() {
    super({
      id: FIND_IDS.PreviousSelectionMatchFindAction,
      label: localize("previousSelectionMatchFindAction", "Find Previous Selection"),
      alias: "Find Previous Selection",
      precondition: void 0,
      kbOpts: {
        kbExpr: EditorContextKeys.focus,
        primary: 2048 | 1024 | 61,
        weight: 100
        /* EditorContrib */
      }
    });
  }
  _run(controller) {
    return controller.moveToPrevMatch();
  }
};
var StartFindReplaceAction = registerMultiEditorAction(new MultiEditorAction({
  id: FIND_IDS.StartFindReplaceAction,
  label: localize("startReplace", "Replace"),
  alias: "Replace",
  precondition: ContextKeyExpr.or(EditorContextKeys.focus, ContextKeyExpr.has("editorIsOpen")),
  kbOpts: {
    kbExpr: null,
    primary: 2048 | 38,
    mac: {
      primary: 2048 | 512 | 36
      /* KeyF */
    },
    weight: 100
    /* EditorContrib */
  },
  menuOpts: {
    menuId: MenuId.MenubarEditMenu,
    group: "3_find",
    title: localize({ key: "miReplace", comment: ["&& denotes a mnemonic"] }, "&&Replace"),
    order: 2
  }
}));
StartFindReplaceAction.addImplementation(0, (accessor, editor, args) => {
  if (!editor.hasModel() || editor.getOption(
    81
    /* readOnly */
  )) {
    return false;
  }
  const controller = CommonFindController.get(editor);
  if (!controller) {
    return false;
  }
  const currentSelection = editor.getSelection();
  const findInputFocused = controller.isFindInputFocused();
  const seedSearchStringFromSelection = !currentSelection.isEmpty() && currentSelection.startLineNumber === currentSelection.endLineNumber && editor.getOption(
    35
    /* find */
  ).seedSearchStringFromSelection !== "never" && !findInputFocused;
  const shouldFocus = findInputFocused || seedSearchStringFromSelection ? 2 : 1;
  return controller.start({
    forceRevealReplace: true,
    seedSearchStringFromSelection: seedSearchStringFromSelection ? "single" : "none",
    seedSearchStringFromNonEmptySelection: editor.getOption(
      35
      /* find */
    ).seedSearchStringFromSelection === "selection",
    seedSearchStringFromGlobalClipboard: editor.getOption(
      35
      /* find */
    ).seedSearchStringFromSelection !== "never",
    shouldFocus,
    shouldAnimate: true,
    updateSearchScope: false,
    loop: editor.getOption(
      35
      /* find */
    ).loop
  });
});
registerEditorContribution(CommonFindController.ID, FindController);
registerEditorAction(StartFindWithArgsAction);
registerEditorAction(StartFindWithSelectionAction);
registerEditorAction(NextMatchFindAction);
registerEditorAction(PreviousMatchFindAction);
registerEditorAction(NextSelectionMatchFindAction);
registerEditorAction(PreviousSelectionMatchFindAction);
var FindCommand = EditorCommand.bindToContribution(CommonFindController.get);
registerEditorCommand(new FindCommand({
  id: FIND_IDS.CloseFindWidgetCommand,
  precondition: CONTEXT_FIND_WIDGET_VISIBLE,
  handler: (x) => x.closeFindWidget(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, ContextKeyExpr.not("isComposing")),
    primary: 9,
    secondary: [
      1024 | 9
      /* Escape */
    ]
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ToggleCaseSensitiveCommand,
  precondition: void 0,
  handler: (x) => x.toggleCaseSensitive(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: ToggleCaseSensitiveKeybinding.primary,
    mac: ToggleCaseSensitiveKeybinding.mac,
    win: ToggleCaseSensitiveKeybinding.win,
    linux: ToggleCaseSensitiveKeybinding.linux
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ToggleWholeWordCommand,
  precondition: void 0,
  handler: (x) => x.toggleWholeWords(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: ToggleWholeWordKeybinding.primary,
    mac: ToggleWholeWordKeybinding.mac,
    win: ToggleWholeWordKeybinding.win,
    linux: ToggleWholeWordKeybinding.linux
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ToggleRegexCommand,
  precondition: void 0,
  handler: (x) => x.toggleRegex(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: ToggleRegexKeybinding.primary,
    mac: ToggleRegexKeybinding.mac,
    win: ToggleRegexKeybinding.win,
    linux: ToggleRegexKeybinding.linux
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ToggleSearchScopeCommand,
  precondition: void 0,
  handler: (x) => x.toggleSearchScope(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: ToggleSearchScopeKeybinding.primary,
    mac: ToggleSearchScopeKeybinding.mac,
    win: ToggleSearchScopeKeybinding.win,
    linux: ToggleSearchScopeKeybinding.linux
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.TogglePreserveCaseCommand,
  precondition: void 0,
  handler: (x) => x.togglePreserveCase(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: TogglePreserveCaseKeybinding.primary,
    mac: TogglePreserveCaseKeybinding.mac,
    win: TogglePreserveCaseKeybinding.win,
    linux: TogglePreserveCaseKeybinding.linux
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ReplaceOneAction,
  precondition: CONTEXT_FIND_WIDGET_VISIBLE,
  handler: (x) => x.replace(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: 2048 | 1024 | 22
    /* Digit1 */
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ReplaceOneAction,
  precondition: CONTEXT_FIND_WIDGET_VISIBLE,
  handler: (x) => x.replace(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_REPLACE_INPUT_FOCUSED),
    primary: 3
    /* Enter */
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ReplaceAllAction,
  precondition: CONTEXT_FIND_WIDGET_VISIBLE,
  handler: (x) => x.replaceAll(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: 2048 | 512 | 3
    /* Enter */
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.ReplaceAllAction,
  precondition: CONTEXT_FIND_WIDGET_VISIBLE,
  handler: (x) => x.replaceAll(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: ContextKeyExpr.and(EditorContextKeys.focus, CONTEXT_REPLACE_INPUT_FOCUSED),
    primary: void 0,
    mac: {
      primary: 2048 | 3
    }
  }
}));
registerEditorCommand(new FindCommand({
  id: FIND_IDS.SelectAllMatchesAction,
  precondition: CONTEXT_FIND_WIDGET_VISIBLE,
  handler: (x) => x.selectAllMatches(),
  kbOpts: {
    weight: 100 + 5,
    kbExpr: EditorContextKeys.focus,
    primary: 512 | 3
    /* Enter */
  }
}));
export {
  CommonFindController,
  FindController,
  MatchFindAction,
  NextMatchFindAction,
  NextSelectionMatchFindAction,
  PreviousMatchFindAction,
  PreviousSelectionMatchFindAction,
  SelectionMatchFindAction,
  StartFindAction,
  StartFindReplaceAction,
  StartFindWithArgsAction,
  StartFindWithSelectionAction,
  getSelectionSearchString
};
//# sourceMappingURL=monaco-editor_esm_vs_editor_contrib_find_browser_findController.js.map
