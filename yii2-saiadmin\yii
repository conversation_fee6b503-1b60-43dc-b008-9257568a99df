#!/usr/bin/env php
<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> Yii 2.0 兼容版本 - 控制台入口文件
 */

defined("YII_DEBUG") or define("YII_DEBUG", true);
defined("YII_ENV") or define("YII_ENV", "dev");

require __DIR__ . "/vendor/autoload.php";
require __DIR__ . "/vendor/yiisoft/yii2/Yii.php";

$config = require __DIR__ . "/config/console.php";

$application = new yii\console\Application($config);
$exitCode = $application->run();
exit($exitCode);