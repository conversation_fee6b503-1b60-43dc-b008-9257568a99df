{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/php/php.contribution.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/php/php.contribution.ts\nimport { registerLanguage } from \"../_.contribution.js\";\nregisterLanguage({\n  id: \"php\",\n  extensions: [\".php\", \".php4\", \".php5\", \".phtml\", \".ctp\"],\n  aliases: [\"PHP\", \"php\"],\n  mimetypes: [\"application/x-php\"],\n  loader: () => {\n    if (false) {\n      return new Promise((resolve, reject) => {\n        __require([\"vs/basic-languages/php/php\"], resolve, reject);\n      });\n    } else {\n      return import(\"./php.js\");\n    }\n  }\n});\n"], "mappings": ";;;;;;;;AASA,iBAAiB;AAAA,EACf,IAAI;AAAA,EACJ,YAAY,CAAC,QAAQ,SAAS,SAAS,UAAU,MAAM;AAAA,EACvD,SAAS,CAAC,OAAO,KAAK;AAAA,EACtB,WAAW,CAAC,mBAAmB;AAAA,EAC/B,QAAQ,MAAM;AACZ,QAAI,OAAO;AACT,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,kBAAU,CAAC,4BAA4B,GAAG,SAAS,MAAM;AAAA,MAC3D,CAAC;AAAA,IACH,OAAO;AACL,aAAO,OAAO,mBAAU;AAAA,IAC1B;AAAA,EACF;AACF,CAAC;", "names": []}