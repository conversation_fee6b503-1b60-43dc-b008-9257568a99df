import{_ as T}from"./logo-B7uA2Tfd.js";import{_ as D,u as E,a as I,p as J,l as L}from"./index-DkGLNqVb.js";import{b as Q,u as Z}from"./vue-router-DXldG2q0.js";import{r as v,a as F,h as i,n as G,k as H,m as s,Q as K,z as r,a1 as y,t as o,l as t,y as h,q as M}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const O={class:"login-logo"},P={class:"login-width md:w-10/12 w-11/12 mx-auto flex justify-between h-full items-center"},W={class:"w-6/12 mx-auto left-panel rounded-l pl-5 pr-5 hidden md:block"},X={class:"logo"},Y={class:"slogan flex justify-end"},oo={class:"md:w-6/12 w-11/12 md:rounded-r mx-auto pl-5 pr-5 pb-10"},eo={class:"mt-10 text-3xl pb-0 mb-10 login-title"},to=["src"],io={class:"flex w-3/4 pt-2 mx-auto items-stretch justify-around"},so={__name:"login",setup(ao){const b=E(),w=Q(),u=Z(),_=v(null),n=v(!1);var V={username:"",password:"",code:""};const l=F(V),c=()=>{l.code="",l.uuid="",L.getCaptch().then(e=>{e.code===200&&(_.value=e.data.image,l.uuid=e.data.uuid)})};c();const $=I(),q=u.query.redirect?u.query.redirect:"/",k=async({values:e,errors:a})=>{if(!n.value){if(n.value=!0,!a){if(!await $.login(l)){n.value=!1,c();return}w.push(q)}n.value=!1}};return(e,a)=>{const f=i("icon-user"),g=i("a-input"),m=i("a-form-item"),S=i("icon-lock"),z=i("a-input-password"),C=i("icon-safe"),N=i("a-button"),x=i("a-divider"),B=i("icon-wechat"),d=i("a-avatar"),U=i("icon-alipay-circle"),j=i("icon-qq"),A=i("icon-weibo"),R=i("a-form");return H(),G("div",{class:"login-container",style:M({background:y(b).mode==="dark"?"#2e2e30e3":""})},[s("h3",O,[a[3]||(a[3]=s("img",{src:T,alt:"logo"},null,-1)),s("span",null,r(e.$title),1)]),s("div",P,[s("div",W,[s("div",X,[s("span",null,r(e.$title)+" v"+r(y(J).version),1)]),s("div",Y,[s("span",null,"---- "+r(e.$t("sys.login.slogan")),1)])]),s("div",oo,[s("h2",eo,r(e.$t("sys.login.title")),1),o(R,{model:l,onSubmit:k},{default:t(()=>[o(m,{field:"username","hide-label":!0,rules:[{required:!0,message:e.$t("sys.login.usernameNotice")}]},{default:t(()=>[o(g,{modelValue:l.username,"onUpdate:modelValue":a[0]||(a[0]=p=>l.username=p),class:"w-full",size:"large",placeholder:e.$t("sys.login.username"),"allow-clear":""},{prefix:t(()=>[o(f)]),_:1},8,["modelValue","placeholder"])]),_:1},8,["rules"]),o(m,{field:"password","hide-label":!0,rules:[{required:!0,message:e.$t("sys.login.passwordNotice")}]},{default:t(()=>[o(z,{modelValue:l.password,"onUpdate:modelValue":a[1]||(a[1]=p=>l.password=p),placeholder:e.$t("sys.login.password"),size:"large","allow-clear":""},{prefix:t(()=>[o(S)]),_:1},8,["modelValue","placeholder"])]),_:1},8,["rules"]),o(m,{field:"code","hide-label":!0,rules:[{required:!0,match:/^[a-zA-Z0-9]{4}$/,message:e.$t("sys.login.verifyCodeNotice")}]},{default:t(()=>[o(g,{modelValue:l.code,"onUpdate:modelValue":a[2]||(a[2]=p=>l.code=p),placeholder:e.$t("sys.login.verifyCode"),size:"large","allow-clear":""},{prefix:t(()=>[o(C)]),append:t(()=>[s("img",{src:_.value,style:{height:"36px",cursor:"pointer"},onClick:c},null,8,to)]),_:1},8,["modelValue","placeholder"])]),_:1},8,["rules"]),o(m,{"hide-label":!0,class:"mt-5"},{default:t(()=>[o(N,{"html-type":"submit",type:"primary",long:"",size:"large",loading:n.value},{default:t(()=>[h(r(e.$t("sys.login.loginBtn")),1)]),_:1},8,["loading"])]),_:1}),o(x,{orientation:"center"},{default:t(()=>[h(r(e.$t("sys.login.otherLoginType")),1)]),_:1}),s("div",io,[o(d,{class:"other-login wechat"},{default:t(()=>[o(B)]),_:1}),o(d,{class:"other-login alipay"},{default:t(()=>[o(U)]),_:1}),o(d,{class:"other-login qq"},{default:t(()=>[o(j)]),_:1}),o(d,{class:"other-login weibo"},{default:t(()=>[o(A)]),_:1})])]),_:1},8,["model"])])]),a[4]||(a[4]=K('<div class="login-bg" data-v-189b6b10><div class="fly bg-fly-circle1" data-v-189b6b10></div><div class="fly bg-fly-circle2" data-v-189b6b10></div><div class="fly bg-fly-circle3" data-v-189b6b10></div><div class="fly bg-fly-circle4" data-v-189b6b10></div></div>',1))],4)}}},de=D(so,[["__scopeId","data-v-189b6b10"]]);export{de as default};
