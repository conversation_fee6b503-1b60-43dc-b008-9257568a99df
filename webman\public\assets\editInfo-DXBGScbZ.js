import{r as x,h as n,j as q,k as c,l as t,t as e,n as k,p as J,y as s,F as D,P as h,m as w,z as U,M as be,N as Ve}from"./@vue-9ZIPiVZG.js";import{a as S}from"./table-BHRaYvrI.js";import{m as xe}from"./menu-CgiEA4rB.js";import{d as ye}from"./dict-C6FxRZf9.js";import ge from"./settingComponent-BvoGVFoj.js";import{q as we,v as Ue,m as ke,r as qe}from"./vars-CZGqfX5Y.js";import{_ as Ce}from"./index-ybrmzYq5.js";import{M as L}from"./@arco-design-uttiljWv.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const Te={key:0},Ie={class:"flex justify-between w-full"},De={class:"flex justify-between w-full"},he={class:"flex justify-between w-full"},Ke={__name:"editInfo",emits:["success"],setup(Re,{expose:Q,emit:W}){const v=x({}),j=x(!0),M=x(!1),F=x("base_config"),A=x(),X=x(["uploadFile","uploadImage","editor","codeEditor","wangEditor","cityLinkage","date","userInfo"]),o=x({generate_menus:["save","update","read","delete","recycle","recovery","realDestroy","changeStatus"],columns:[]}),O=x(),Y=W,_=x({relations:[]}),N=x([]);x([]);const P=x([]),$=x([]),Z=async d=>{M.value=!0;const a=await S.getDataSource();$.value=a.data.map(r=>({label:r,value:r}));const f=await S.readTable(d);v.value=f.data,ue(),j.value=!1},ee=(d,a)=>{o.value.columns.find((f,r)=>{f.column_name==d&&(o.value.columns[r].options=a)}),L.success("组件设置成功")},le=d=>{console.log(d),d.forEach((a,f)=>{a.list_sort=f}),o.value.columns=d},ae=async d=>{const a=await O.value.validate();if(a){for(let r in a)L.error(a[r].message);return!1}if(o.value.namespace=="saiadmin")return L.error("请修改应用名称，禁止使用saiadmin"),!1;o.value.options=_.value;const f=await S.update(o.value.id,o.value);if(f.code==200)L.success(f.message),Y("success",!0),d(!0);else return!1},C=(d,a)=>o.value.columns.map(f=>{f["is_"+a]=d}),te=()=>{_.value.relations.push({name:"",type:"hasOne",model:"",foreignKey:"",localKey:"",table:""})},oe=d=>_.value.relations.splice(d,1),ue=()=>{for(let d in v.value)d==="generate_menus"?o.value[d]=v.value[d]?v.value[d].split(","):[]:o.value[d]=v.value[d];v.value.options&&v.value.options.relations?_.value.relations=v.value.options.relations:_.value.relations=[],v.value.tpl_category==="tree"&&(_.value.tree_id=v.value.options.tree_id,_.value.tree_name=v.value.options.tree_name,_.value.tree_parent_id=v.value.options.tree_parent_id),S.getTableColumns({table_id:v.value.id,orderBy:"list_sort",orderType:"asc"}).then(d=>{o.value.columns=[],d.data.map(a=>{a.is_required=a.is_required===2,a.is_insert=a.is_insert===2,a.is_edit=a.is_edit===2,a.is_list=a.is_list===2,a.is_query=a.is_query===2,a.is_sort=a.is_sort===2,o.value.columns.push(a)})}),xe.getList({tree:!0}).then(d=>{N.value=d.data,N.value.unshift({id:0,value:0,label:"顶级菜单"})}),ye.getPageList({saiType:"all"}).then(d=>P.value=d.data)};return Q({open:Z}),(d,a)=>{const f=n("a-divider"),r=n("a-input"),m=n("a-form-item"),i=n("a-col"),y=n("a-row"),g=n("a-select"),T=n("a-radio"),B=n("a-radio-group"),ne=n("a-cascader"),z=n("a-input-number"),K=n("a-option"),R=n("a-tab-pane"),G=n("a-tag"),E=n("a-alert"),b=n("a-table-column"),V=n("a-checkbox"),I=n("a-tooltip"),H=n("a-link"),de=n("a-space"),se=n("a-table"),me=n("a-checkbox-group"),ie=n("icon-plus"),re=n("a-button"),pe=n("icon-delete"),_e=n("a-tabs"),fe=n("a-form"),ce=n("a-spin"),ve=n("a-modal");return c(),q(ve,{visible:M.value,"onUpdate:visible":a[28]||(a[28]=l=>M.value=l),"on-before-ok":ae,fullscreen:"","unmount-on-close":""},{title:t(()=>{var l;return[s("编辑生成信息 - "+U((l=v.value)==null?void 0:l.table_comment),1)]}),default:t(()=>[e(ce,{loading:j.value,tip:"加载数据中...",class:"w-full"},{default:t(()=>[e(fe,{model:o.value,ref_key:"formRef",ref:O},{default:t(()=>[e(_e,{"active-key":F.value,"onUpdate:activeKey":a[27]||(a[27]=l=>F.value=l)},{default:t(()=>[e(R,{title:"配置信息",key:"base_config"},{default:t(()=>[e(f,{orientation:"left"},{default:t(()=>a[29]||(a[29]=[s("基础信息")])),_:1}),e(y,{gutter:24},{default:t(()=>[e(i,{span:8},{default:t(()=>[e(m,{label:"表名称",field:"table_name","label-col-flex":"auto","label-col-style":{width:"100px"}},{default:t(()=>[e(r,{modelValue:o.value.table_name,"onUpdate:modelValue":a[0]||(a[0]=l=>o.value.table_name=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(m,{label:"表描述",field:"table_comment","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"表描述必填"}]},{default:t(()=>[e(r,{modelValue:o.value.table_comment,"onUpdate:modelValue":a[1]||(a[1]=l=>o.value.table_comment=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(m,{label:"实体类",field:"class_name","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"实体类必填"}]},{default:t(()=>[e(r,{modelValue:o.value.class_name,"onUpdate:modelValue":a[2]||(a[2]=l=>o.value.class_name=l)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{gutter:24},{default:t(()=>[e(i,{span:8},{default:t(()=>[e(m,{label:"业务名称",field:"business_name","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"实体别名必填"}]},{default:t(()=>[e(r,{modelValue:o.value.business_name,"onUpdate:modelValue":a[3]||(a[3]=l=>o.value.business_name=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(m,{label:"数据源",field:"source","label-col-flex":"auto","label-col-style":{width:"94px"}},{default:t(()=>[e(g,{placeholder:"请选择数据源",modelValue:o.value.source,"onUpdate:modelValue":a[4]||(a[4]=l=>o.value.source=l),options:$.value},null,8,["modelValue","options"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(m,{label:"备注信息",field:"remark","label-col-flex":"auto","label-col-style":{width:"94px"}},{default:t(()=>[e(r,{modelValue:o.value.remark,"onUpdate:modelValue":a[5]||(a[5]=l=>o.value.remark=l)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{orientation:"left"},{default:t(()=>a[30]||(a[30]=[s("生成信息")])),_:1}),e(y,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"应用类型",field:"template","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"应用类型必选"}],extra:"默认app模板,生成文件放app目录下"},{default:t(()=>[e(g,{style:{width:"100%"},modelValue:o.value.template,"onUpdate:modelValue":a[6]||(a[6]=l=>o.value.template=l),options:[{label:"webman应用[app]",value:"app"},{label:"webman插件[plugin]",value:"plugin"}],"allow-clear":"","allow-search":"",placeholder:"请选择生成模板"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"应用名称",field:"namespace","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"应用名称必填"}],extra:"plugin插件名称, 或者app下应用名称, 禁止使用saiadmin"},{default:t(()=>[e(r,{modelValue:o.value.namespace,"onUpdate:modelValue":a[7]||(a[7]=l=>o.value.namespace=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"包名",field:"package_name","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定控制器文件所在控制器目录的二级目录名，如：system"},{default:t(()=>[e(r,{"allow-clear":"",modelValue:o.value.package_name,"onUpdate:modelValue":a[8]||(a[8]=l=>o.value.package_name=l),placeholder:"请输入包名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"生成类型",field:"tpl_category","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"生成类型必填"}],extra:"单表须有主键，树表须指定id、parent_id、name等字段"},{default:t(()=>[e(g,{style:{width:"100%"},modelValue:o.value.tpl_category,"onUpdate:modelValue":a[9]||(a[9]=l=>o.value.tpl_category=l),options:[{label:"单表CRUD",value:"single"},{label:"树表CRUD",value:"tree"}],"allow-clear":"","allow-search":"",placeholder:"请选择所属模块"},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"生成路径",field:"generate_path","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"生成路径必填"}],extra:"前端根目录文件夹名称，必须与后端根目录同级"},{default:t(()=>[e(r,{modelValue:o.value.generate_path,"onUpdate:modelValue":a[10]||(a[10]=l=>o.value.generate_path=l)},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"模型类型",field:"generate_model","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"根据不同选择生成不同的模型"},{default:t(()=>[e(B,{modelValue:o.value.generate_model,"onUpdate:modelValue":a[11]||(a[11]=l=>o.value.generate_model=l)},{default:t(()=>[e(T,{value:1},{default:t(()=>a[31]||(a[31]=[s("软删除")])),_:1}),e(T,{value:2},{default:t(()=>a[32]||(a[32]=[s("非软删除")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"所属菜单",field:"belong_menu_id","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"默认为工具菜单栏目下的子菜单。不选择则为顶级菜单栏目"},{default:t(()=>[e(ne,{modelValue:o.value.belong_menu_id,"onUpdate:modelValue":a[12]||(a[12]=l=>o.value.belong_menu_id=l),options:N.value,"expand-trigger":"hover",style:{width:"100%"},placeholder:"生成功能所属菜单","allow-search":"","allow-clear":"","check-strictly":""},null,8,["modelValue","options"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"菜单名称",field:"menu_name","label-col-flex":"auto","label-col-style":{width:"100px"},rules:[{required:!0,message:"菜单名称必选"}],extra:"显示在菜单栏目上的菜单名称、以及代码中的业务功能名称"},{default:t(()=>[e(r,{"allow-clear":"",modelValue:o.value.menu_name,"onUpdate:modelValue":a[13]||(a[13]=l=>o.value.menu_name=l),placeholder:"请输入菜单名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{gutter:24},{default:t(()=>[e(i,{span:8},{default:t(()=>[e(m,{label:"表单样式",field:"component_type","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"设置新增和修改组件显示方式"},{default:t(()=>[e(B,{modelValue:o.value.component_type,"onUpdate:modelValue":a[14]||(a[14]=l=>o.value.component_type=l),type:"button"},{default:t(()=>[e(T,{value:1},{default:t(()=>a[33]||(a[33]=[s("模态框")])),_:1}),e(T,{value:2},{default:t(()=>a[34]||(a[34]=[s("抽屉")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(m,{label:"表单宽度",field:"form_width","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"表单组件的宽度，单位为px"},{default:t(()=>[e(z,{modelValue:o.value.form_width,"onUpdate:modelValue":a[15]||(a[15]=l=>o.value.form_width=l),min:200,max:1e4},null,8,["modelValue"])]),_:1})]),_:1}),e(i,{span:8},{default:t(()=>[e(m,{label:"表单全屏",field:"is_full","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"编辑表单是否全屏"},{default:t(()=>[e(B,{modelValue:o.value.is_full,"onUpdate:modelValue":a[16]||(a[16]=l=>o.value.is_full=l)},{default:t(()=>[e(T,{value:1},{default:t(()=>a[35]||(a[35]=[s("否")])),_:1}),e(T,{value:2},{default:t(()=>a[36]||(a[36]=[s("是")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o.value.tpl_category==="tree"?(c(),k("div",Te,[e(f,{orientation:"left"},{default:t(()=>a[37]||(a[37]=[s("树表配置")])),_:1}),e(y,{gutter:24},{default:t(()=>[e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"树主ID",field:"tree_id","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定树表的主要ID，一般为主键"},{default:t(()=>[e(g,{style:{width:"100%"},modelValue:_.value.tree_id,"onUpdate:modelValue":a[17]||(a[17]=l=>_.value.tree_id=l),"allow-clear":"","allow-search":"",placeholder:"请选择树表的主ID"},{default:t(()=>[(c(!0),k(D,null,h(o.value.columns,(l,u)=>(c(),q(K,{class:"w-full",label:l.column_name+" - "+l.column_comment,value:l.column_name,key:u},{default:t(()=>[w("div",Ie,[w("span",null,U(l.column_name),1),w("span",null,U(l.column_comment),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"树父ID",field:"tree_parent_id","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定树表的父ID，比如：parent_id"},{default:t(()=>[e(g,{style:{width:"100%"},modelValue:_.value.tree_parent_id,"onUpdate:modelValue":a[18]||(a[18]=l=>_.value.tree_parent_id=l),"allow-clear":"","allow-search":"",placeholder:"请选择树表的父ID"},{default:t(()=>[(c(!0),k(D,null,h(o.value.columns,(l,u)=>(c(),q(K,{class:"w-full",label:l.column_name+" - "+l.column_comment,value:l.column_name,key:u},{default:t(()=>[w("div",De,[w("span",null,U(l.column_name),1),w("span",null,U(l.column_comment),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(i,{xs:24,md:8,xl:8},{default:t(()=>[e(m,{label:"树名称",field:"tree_name","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定树显示的名称字段，比如：name"},{default:t(()=>[e(g,{style:{width:"100%"},modelValue:_.value.tree_name,"onUpdate:modelValue":a[19]||(a[19]=l=>_.value.tree_name=l),"allow-clear":"","allow-search":"",placeholder:"请选择树表的主ID"},{default:t(()=>[(c(!0),k(D,null,h(o.value.columns,(l,u)=>(c(),q(K,{class:"w-full",label:l.column_name+" - "+l.column_comment,value:l.column_name,key:u},{default:t(()=>[w("div",he,[w("span",null,U(l.column_name),1),w("span",null,U(l.column_comment),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})])):J("",!0)]),_:1}),e(R,{title:"字段配置",key:"field_config"},{default:t(()=>[e(E,{title:"提示"},{default:t(()=>[a[40]||(a[40]=s(" 使用数组形式字段的组件，请在模型设置 ")),e(G,{class:"tag-primary"},{default:t(()=>a[38]||(a[38]=[s("获取器")])),_:1}),a[41]||(a[41]=s(" 和 ")),e(G,{class:"tag-primary"},{default:t(()=>a[39]||(a[39]=[s("修改器")])),_:1})]),_:1}),e(se,{data:o.value.columns,pagination:!1,class:"mt-3",onChange:le,draggable:{type:"handle",width:40}},{columns:t(()=>[e(b,{dataIndex:"column_name",title:"字段名称",width:150,tooltip:""}),e(b,{dataIndex:"column_comment",title:"字段描述",width:160},{cell:t(({rowIndex:l})=>[e(r,{modelValue:o.value.columns[l].column_comment,"onUpdate:modelValue":u=>o.value.columns[l].column_comment=u,"allow-clear":""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"column_width",title:"列表宽度",width:100},{cell:t(({rowIndex:l})=>[e(z,{modelValue:o.value.columns[l].column_width,"onUpdate:modelValue":u=>o.value.columns[l].column_width=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"column_type",title:"物理类型",width:100}),e(b,{dataIndex:"is_required",title:"必填",width:60},{title:t(()=>[a[42]||(a[42]=s("必填 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(V,{onChange:a[20]||(a[20]=l=>C(l,"required"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(V,{modelValue:o.value.columns[l].is_required,"onUpdate:modelValue":u=>o.value.columns[l].is_required=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"is_insert",title:"表单",width:60},{title:t(()=>[a[43]||(a[43]=s("表单 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(V,{onChange:a[21]||(a[21]=l=>C(l,"insert"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(V,{modelValue:o.value.columns[l].is_insert,"onUpdate:modelValue":u=>o.value.columns[l].is_insert=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"is_edit",title:"查看",width:60},{title:t(()=>[a[44]||(a[44]=s("查看 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(V,{onChange:a[22]||(a[22]=l=>C(l,"edit"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(V,{modelValue:o.value.columns[l].is_edit,"onUpdate:modelValue":u=>o.value.columns[l].is_edit=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"is_list",title:"列表",width:60},{title:t(()=>[a[45]||(a[45]=s("列表 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(V,{onChange:a[23]||(a[23]=l=>C(l,"list"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(V,{modelValue:o.value.columns[l].is_list,"onUpdate:modelValue":u=>o.value.columns[l].is_list=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"is_query",title:"查询",width:60},{title:t(()=>[a[46]||(a[46]=s("查询 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(V,{onChange:a[24]||(a[24]=l=>C(l,"query"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(V,{modelValue:o.value.columns[l].is_query,"onUpdate:modelValue":u=>o.value.columns[l].is_query=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"is_sort",title:"排序",width:60},{title:t(()=>[a[47]||(a[47]=s("排序 ")),e(I,{content:"全选 / 全不选",position:"bottom"},{default:t(()=>[e(V,{onChange:a[25]||(a[25]=l=>C(l,"sort"))})]),_:1})]),cell:t(({rowIndex:l})=>[e(V,{modelValue:o.value.columns[l].is_sort,"onUpdate:modelValue":u=>o.value.columns[l].is_sort=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(b,{dataIndex:"query_type",title:"查询方式",width:150},{cell:t(({rowIndex:l})=>[e(g,{modelValue:o.value.columns[l].query_type,"onUpdate:modelValue":u=>o.value.columns[l].query_type=u,options:we,"allow-clear":""},null,8,["modelValue","onUpdate:modelValue","options"])]),_:1}),e(b,{dataIndex:"view_type",title:"页面控件",width:210},{cell:t(({record:l,rowIndex:u})=>[e(de,null,{default:t(()=>[e(g,{modelValue:o.value.columns[u].view_type,"onUpdate:modelValue":p=>o.value.columns[u].view_type=p,style:{width:"140px"},options:Ue,"allow-clear":""},null,8,["modelValue","onUpdate:modelValue","options"]),X.value.includes(l.view_type)?(c(),q(H,{key:0,onClick:p=>A.value.open(l)},{default:t(()=>a[48]||(a[48]=[s("设置")])),_:2},1032,["onClick"])):J("",!0)]),_:2},1024)]),_:1}),e(b,{dataIndex:"dict_type",title:"数据字典",width:160},{cell:t(({record:l,rowIndex:u})=>[e(g,{modelValue:o.value.columns[u].dict_type,"onUpdate:modelValue":p=>o.value.columns[u].dict_type=p,options:P.value,"allow-clear":"","field-names":{label:"name",value:"code"},placeholder:"选择数据字典",disabled:!["saSelect","radio","checkbox"].includes(l.view_type)},null,8,["modelValue","onUpdate:modelValue","options","disabled"])]),_:1})]),_:1},8,["data"])]),_:1}),e(R,{title:"菜单功能",key:"menu_config"},{default:t(()=>[e(E,{title:"提示"},{default:t(()=>a[49]||(a[49]=[s(" 选择不同的功能，将会生成对应功能的代码 ")])),_:1}),e(me,{direction:"vertical",modelValue:o.value.generate_menus,"onUpdate:modelValue":a[26]||(a[26]=l=>o.value.generate_menus=l),class:"mt-3","default-value":o.value.generate_menus},{default:t(()=>[(c(!0),k(D,null,h(ke,(l,u)=>(c(),q(V,{value:l.value,key:u},{default:t(()=>[s(U(l.name+"　-　"+l.comment),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","default-value"])]),_:1}),e(R,{title:"关联配置",key:"relation_config"},{default:t(()=>[e(E,{title:"提示"},{default:t(()=>a[50]||(a[50]=[s("模型关联支持：一对一、一对多、一对一（反向）、多对多。")])),_:1}),e(re,{onClick:te,type:"primary",class:"mt-3"},{default:t(()=>[e(ie),a[51]||(a[51]=s(" 新增关联"))]),_:1}),(c(!0),k(D,null,h(_.value.relations,(l,u)=>(c(),k("div",{key:u},[e(f,{orientation:"left"},{default:t(()=>[s(U(l.name?l.name:"定义新关联")+" ",1),e(H,{onClick:p=>oe(u),class:"ml-5"},{default:t(()=>[e(pe),a[52]||(a[52]=s(" 删除定义"))]),_:2},1032,["onClick"])]),_:2},1024),e(y,{gutter:24},{default:t(()=>[e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(m,{label:"关联类型",field:"type","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"指定关联类型"},{default:t(()=>[e(g,{modelValue:l.type,"onUpdate:modelValue":p=>l.type=p,"allow-clear":"","allow-search":"",placeholder:"请选择关联类型"},{default:t(()=>[(c(!0),k(D,null,h(qe,p=>(c(),q(K,{key:p.value,value:p.value,label:p.name},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(m,{label:"关联名称",field:"name","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"设置关联名称，且是代码中调用的名称"},{default:t(()=>[e(r,{modelValue:l.name,"onUpdate:modelValue":p=>l.name=p,"allow-clear":"",placeholder:"设置关联名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(y,{gutter:24},{default:t(()=>[e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(m,{label:"关联模型",field:"model","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"选择要关联的模型"},{default:t(()=>[e(r,{modelValue:l.model,"onUpdate:modelValue":p=>l.model=p,"allow-clear":"",placeholder:"设置关联模型"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(m,{label:l.type==="belongsTo"||l.type==="belongsToMany"?"外键":"当前模型主键",field:"localKey","label-col-flex":"auto","label-col-style":{width:"100px"},extra:l.type==="belongsTo"||l.type==="belongsToMany"?"关联模型_id":"当前模型主键"},{default:t(()=>[e(r,{modelValue:l.localKey,"onUpdate:modelValue":p=>l.localKey=p,"allow-clear":"",placeholder:"设置键名"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","extra"])]),_:2},1024)]),_:2},1024),e(y,{gutter:24},{default:t(()=>[be(e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(m,{label:"中间模型",field:"model","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"多对多关联的中间模型"},{default:t(()=>[e(r,{modelValue:l.table,"onUpdate:modelValue":p=>l.table=p,"allow-clear":"",placeholder:"请输入中间模型"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1536),[[Ve,l.type==="belongsToMany"]]),e(i,{xs:24,md:12,xl:12},{default:t(()=>[e(m,{label:l.type==="belongsTo"?"关联主键":"外键",field:"foreignKey","label-col-flex":"auto","label-col-style":{width:"100px"},extra:l.type==="belongsTo"?"关联模型主键":"当前模型_id"},{default:t(()=>[e(r,{style:{width:"100%"},modelValue:l.foreignKey,"onUpdate:modelValue":p=>l.foreignKey=p,"allow-clear":"",placeholder:"设置键名"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","extra"])]),_:2},1024)]),_:2},1024)]))),128))]),_:1})]),_:1},8,["active-key"])]),_:1},8,["model"])]),_:1},8,["loading"]),e(ge,{ref_key:"settingComponentRef",ref:A,onConfrim:ee},null,512)]),_:1},8,["visible"])}}},Al=Ce(Ke,[["__scopeId","data-v-c97774b4"]]);export{Al as default};
