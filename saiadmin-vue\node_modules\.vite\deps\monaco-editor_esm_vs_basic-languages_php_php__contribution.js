import {
  registerLanguage
} from "./chunk-TIAC434R.js";
import "./chunk-OX74CKBW.js";
import "./chunk-WBIRFOMM.js";
import "./chunk-LK32TJAX.js";

// node_modules/monaco-editor/esm/vs/basic-languages/php/php.contribution.js
registerLanguage({
  id: "php",
  extensions: [".php", ".php4", ".php5", ".phtml", ".ctp"],
  aliases: ["PHP", "php"],
  mimetypes: ["application/x-php"],
  loader: () => {
    if (false) {
      return new Promise((resolve, reject) => {
        __require(["vs/basic-languages/php/php"], resolve, reject);
      });
    } else {
      return import("./php-GDJ6JDJS.js");
    }
  }
});
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/php/php.contribution.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=monaco-editor_esm_vs_basic-languages_php_php__contribution.js.map
