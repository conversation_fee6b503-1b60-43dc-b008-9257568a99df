import{a as f}from"./database-CTMAb1z4.js";import{a as R}from"./generate-CUmP6rhy.js";import{M as x}from"./@arco-design-uttiljWv.js";import{r,a as y,h as t,j,k as q,l as a,t as n,y as _,C as A}from"./@vue-9ZIPiVZG.js";import"./index-DkGLNqVb.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const Ke={__name:"loadTable",emits:["success"],setup($,{expose:k,emit:C}){const c=r(),s=r([]),d=r(!1),m=r("mysql"),v=r({}),b=r({}),S=C,p=r({name:"",source:""}),u=r([]),g=()=>{p.value.source=m.value,h.api=f.getPageList,c.value.refresh()},V=async l=>{if(s.value.length<1){x.info("至少要选择一条数据"),l(!1);return}let e=[];c.value.getTableData().filter(o=>{s.value.includes(o.name)&&e.push({name:o.name,comment:o.comment,sourceName:o.name})}),e.map(o=>{b.value[o.sourceName]&&(o.comment=b.value[o.sourceName]),v.value[o.name]&&(o.name=v.value[o.name])}),(await R.loadTable({source:m.value,names:e})).code===200&&(x.success("装载成功"),S("success"),s.value=[],l(!0))},N=l=>{s.value=l},I=async()=>{d.value=!0;const l=await f.getDataSource();u.value=l.data.map(e=>({label:e,value:e})),m.value=u.value[0]?u.value[0].value:"",A(()=>{g()})},h=y({pk:"name",api:f.getPageList,height:670,showIndex:!0,showSort:!1,operationColumn:!1,rowSelection:{showCheckedAll:!0,key:"name",onlyCurrent:!0}}),T=y([{title:"表名称",dataIndex:"name",align:"left",width:200},{title:"表注释",dataIndex:"comment",align:"left",width:180},{title:"引擎",dataIndex:"engine",width:150},{title:"编码",dataIndex:"collation",width:180},{title:"创建时间",dataIndex:"create_time"}]);return k({open:I}),(l,e)=>{const w=t("a-alert"),o=t("a-input"),B=t("a-form-item"),F=t("a-col"),L=t("a-select"),U=t("a-button"),D=t("a-input-group"),M=t("sa-table"),P=t("a-modal");return q(),j(P,{fullscreen:"",visible:d.value,"onUpdate:visible":e[2]||(e[2]=i=>d.value=i),"on-before-ok":V,"align-center":!1,"unmount-on-close":""},{title:a(()=>e[3]||(e[3]=[_("装载数据表")])),default:a(()=>[n(w,{class:"mb-3",closable:""},{default:a(()=>e[4]||(e[4]=[_(" 1、支持thinkorm配置多数据源； 2、载入表[sa_shop_category]会自动处理为[SaShopCategory]类，可以在载入后对类名进行修改[ShopCategory] ")])),_:1}),n(M,{ref_key:"crudRef",ref:c,options:h,columns:T,searchForm:p.value,onSelectionChange:N},{tableSearch:a(()=>[n(F,{span:8},{default:a(()=>[n(B,{field:"name",label:"表名称"},{default:a(()=>[n(o,{modelValue:p.value.name,"onUpdate:modelValue":e[0]||(e[0]=i=>p.value.name=i),placeholder:"请输入数据表名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),tableBeforeButtons:a(()=>[n(D,null,{default:a(()=>[n(L,{placeholder:"切换数据源",modelValue:m.value,"onUpdate:modelValue":e[1]||(e[1]=i=>m.value=i),options:u.value,style:{width:"300px"}},null,8,["modelValue","options"]),n(U,{type:"primary",onClick:g},{default:a(()=>e[5]||(e[5]=[_("确定切换")])),_:1})]),_:1})]),_:1},8,["options","columns","searchForm"])]),_:1},8,["visible"])}}};export{Ke as default};
