2025-08-03 06:46:43 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 06:46:42 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '54457'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PHP_SELF' => '/index.php'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754174802.9376
    'REQUEST_TIME' => 1754174802
]
2025-08-03 06:49:34 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 06:49:34 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '54658'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/demo'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/demo'
    'PHP_SELF' => '/index.php/demo'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754174974.4235
    'REQUEST_TIME' => 1754174974
]
2025-08-03 06:49:52 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 06:49:52 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '54692'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/demo'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/demo'
    'PHP_SELF' => '/index.php/demo'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_USER_AGENT' => 'curl/8.14.1'
    'HTTP_ACCEPT' => '*/*'
    'HTTP_ACCEPT_ENCODING' => 'gzip'
    'REQUEST_TIME_FLOAT' => 1754174992.1937
    'REQUEST_TIME' => 1754174992
]
2025-08-03 07:01:33 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 07:01:33 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '55432'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/user'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/user'
    'PHP_SELF' => '/index.php/user'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754175693.6554
    'REQUEST_TIME' => 1754175693
]
2025-08-03 07:07:20 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 07:07:20 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '55709'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/demo'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/demo'
    'PHP_SELF' => '/index.php/demo'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_USER_AGENT' => 'curl/8.14.1'
    'HTTP_ACCEPT' => '*/*'
    'HTTP_ACCEPT_ENCODING' => 'gzip'
    'REQUEST_TIME_FLOAT' => 1754176040.8264
    'REQUEST_TIME' => 1754176040
]
2025-08-03 08:32:42 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 08:32:42 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '59074'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/user'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/user'
    'PHP_SELF' => '/index.php/user'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_CACHE_CONTROL' => 'max-age=0'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754181162.8189
    'REQUEST_TIME' => 1754181162
]
2025-08-03 08:32:55 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 08:32:55 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '59075'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PHP_SELF' => '/index.php'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754181175.9417
    'REQUEST_TIME' => 1754181175
]
2025-08-03 08:48:12 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('', Array)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#2 {main}
2025-08-03 08:48:12 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60188'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PHP_SELF' => '/index.php'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754182092.1296
    'REQUEST_TIME' => 1754182092
]
2025-08-03 08:48:43 [127.0.0.1][-][-][error][yii\db\Exception] PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php:722
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(722): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), NULL)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(637): yii\db\Connection->createPdoInstance()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo(true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(460): yii\db\Command->queryInternal('fetchAll', 7)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\mysql\Schema.php(123): yii\db\Command->queryColumn()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Schema.php(235): yii\db\mysql\Schema->findTableNames('')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\Generator.php(229): yii\db\Schema->getTableNames()
#10 [internal function]: yii\gii\generators\model\Generator->yii\gii\generators\model\{closure}()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\components\ActiveField.php(47): call_user_func(Object(Closure))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\gii\components\ActiveField->init()
#13 [internal function]: yii\base\BaseObject->__construct(Array)
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(425): ReflectionClass->newInstanceArgs(Array)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\gii\\compone...', Array, Array)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\gii\\compone...', Array, Array)
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\widgets\ActiveForm.php(331): yii\BaseYii::createObject(Array)
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\form.php(13): yii\widgets\ActiveForm->field(Object(yii\gii\generators\model\Generator), 'tableName')
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\views\default\view.php(41): yii\base\View->renderFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile('C:\\Users\\<USER>\gii\controllers\DefaultController))
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render('view', Array, Object(yii\gii\controllers\DefaultController))
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\controllers\DefaultController.php(72): yii\base\Controller->render('view', Array)
#27 [internal function]: yii\gii\controllers\DefaultController->actionView('model')
#28 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array(Array, Array)
#29 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams(Array)
#30 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction('view', Array)
#31 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('gii/default/vie...', Array)
#32 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#34 {main}

Next yii\db\Exception: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php:648
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo(true)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(460): yii\db\Command->queryInternal('fetchAll', 7)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\mysql\Schema.php(123): yii\db\Command->queryColumn()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Schema.php(235): yii\db\mysql\Schema->findTableNames('')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\Generator.php(229): yii\db\Schema->getTableNames()
#8 [internal function]: yii\gii\generators\model\Generator->yii\gii\generators\model\{closure}()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\components\ActiveField.php(47): call_user_func(Object(Closure))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\gii\components\ActiveField->init()
#11 [internal function]: yii\base\BaseObject->__construct(Array)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(425): ReflectionClass->newInstanceArgs(Array)
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\gii\\compone...', Array, Array)
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\gii\\compone...', Array, Array)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\widgets\ActiveForm.php(331): yii\BaseYii::createObject(Array)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\form.php(13): yii\widgets\ActiveForm->field(Object(yii\gii\generators\model\Generator), 'tableName')
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\views\default\view.php(41): yii\base\View->renderFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile('C:\\Users\\<USER>\gii\controllers\DefaultController))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render('view', Array, Object(yii\gii\controllers\DefaultController))
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\controllers\DefaultController.php(72): yii\base\Controller->render('view', Array)
#25 [internal function]: yii\gii\controllers\DefaultController->actionView('model')
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array(Array, Array)
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams(Array)
#28 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction('view', Array)
#29 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('gii/default/vie...', Array)
#30 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#31 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#32 {main}
Additional Information:
Array
(
    [0] => HY000
    [1] => 1045
    [2] => Access denied for user 'root'@'localhost' (using password: NO)
)

2025-08-03 08:48:43 [127.0.0.1][-][-][info][application] $_GET = [
    'id' => 'model'
]

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60249'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/gii/model'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/gii/model'
    'PHP_SELF' => '/index.php/gii/model'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'same-origin'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_REFERER' => 'http://localhost:8080/gii'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D'
    'REQUEST_TIME_FLOAT' => 1754182123.7599
    'REQUEST_TIME' => 1754182123
]
2025-08-03 08:49:02 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "site/login". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('site/login', Array)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#2 {main}
2025-08-03 08:49:02 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60326'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/site/login'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/site/login'
    'PHP_SELF' => '/index.php/site/login'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754182142.0829
    'REQUEST_TIME' => 1754182142
]
2025-08-03 08:54:17 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('', Array)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#2 {main}
2025-08-03 08:54:17 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60699'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PHP_SELF' => '/index.php'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754182457.1611
    'REQUEST_TIME' => 1754182457
]
2025-08-03 08:54:36 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "site/about". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('site/about', Array)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#2 {main}
2025-08-03 08:54:36 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60700'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/site/about'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/site/about'
    'PHP_SELF' => '/index.php/site/about'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754182476.2015
    'REQUEST_TIME' => 1754182476
]
2025-08-03 08:54:50 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "site/info". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('site/info', Array)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#2 {main}
2025-08-03 08:54:50 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60701'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/site/info'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/site/info'
    'PHP_SELF' => '/index.php/site/info'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754182490.2118
    'REQUEST_TIME' => 1754182490
]
2025-08-03 09:07:41 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "site/info". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('site/info', Array)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#2 {main}
2025-08-03 09:07:41 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61430'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/site/info'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/site/info'
    'PHP_SELF' => '/index.php/site/info'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_CACHE_CONTROL' => 'max-age=0'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754183261.3436
    'REQUEST_TIME' => 1754183261
]
2025-08-03 09:08:08 [127.0.0.1][-][cpq9h07s1fvb9aotleov2pm4j0][error][yii\db\Exception] PDOException: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php:722
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(722): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), NULL)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(637): yii\db\Connection->createPdoInstance()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo(true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(460): yii\db\Command->queryInternal('fetchAll', 7)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\mysql\Schema.php(123): yii\db\Command->queryColumn()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Schema.php(235): yii\db\mysql\Schema->findTableNames('')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\Generator.php(229): yii\db\Schema->getTableNames()
#10 [internal function]: yii\gii\generators\model\Generator->yii\gii\generators\model\{closure}()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\components\ActiveField.php(47): call_user_func(Object(Closure))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\gii\components\ActiveField->init()
#13 [internal function]: yii\base\BaseObject->__construct(Array)
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(425): ReflectionClass->newInstanceArgs(Array)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\gii\\compone...', Array, Array)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\gii\\compone...', Array, Array)
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\widgets\ActiveForm.php(331): yii\BaseYii::createObject(Array)
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\form.php(13): yii\widgets\ActiveForm->field(Object(yii\gii\generators\model\Generator), 'tableName')
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\views\default\view.php(41): yii\base\View->renderFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile('C:\\Users\\<USER>\gii\controllers\DefaultController))
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render('view', Array, Object(yii\gii\controllers\DefaultController))
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\controllers\DefaultController.php(72): yii\base\Controller->render('view', Array)
#27 [internal function]: yii\gii\controllers\DefaultController->actionView('model')
#28 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array(Array, Array)
#29 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams(Array)
#30 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction('view', Array)
#31 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('gii/default/vie...', Array)
#32 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#34 {main}

Next yii\db\Exception: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php:648
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo(true)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(460): yii\db\Command->queryInternal('fetchAll', 7)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\mysql\Schema.php(123): yii\db\Command->queryColumn()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Schema.php(235): yii\db\mysql\Schema->findTableNames('')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\Generator.php(229): yii\db\Schema->getTableNames()
#8 [internal function]: yii\gii\generators\model\Generator->yii\gii\generators\model\{closure}()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\components\ActiveField.php(47): call_user_func(Object(Closure))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\gii\components\ActiveField->init()
#11 [internal function]: yii\base\BaseObject->__construct(Array)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(425): ReflectionClass->newInstanceArgs(Array)
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\gii\\compone...', Array, Array)
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\gii\\compone...', Array, Array)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\widgets\ActiveForm.php(331): yii\BaseYii::createObject(Array)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\form.php(13): yii\widgets\ActiveForm->field(Object(yii\gii\generators\model\Generator), 'tableName')
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\views\default\view.php(41): yii\base\View->renderFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile('C:\\Users\\<USER>\gii\controllers\DefaultController))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render('view', Array, Object(yii\gii\controllers\DefaultController))
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\controllers\DefaultController.php(72): yii\base\Controller->render('view', Array)
#25 [internal function]: yii\gii\controllers\DefaultController->actionView('model')
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array(Array, Array)
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams(Array)
#28 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction('view', Array)
#29 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('gii/default/vie...', Array)
#30 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#31 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#32 {main}
Additional Information:
Array
(
    [0] => HY000
    [1] => 1045
    [2] => Access denied for user 'root'@'localhost' (using password: NO)
)

2025-08-03 09:08:08 [127.0.0.1][-][cpq9h07s1fvb9aotleov2pm4j0][info][application] $_GET = [
    'id' => 'model'
]

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61613'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/gii/model'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/gii/model'
    'PHP_SELF' => '/index.php/gii/model'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'same-origin'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_REFERER' => 'http://localhost:8080/gii/module'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754183288.7522
    'REQUEST_TIME' => 1754183288
]
2025-08-03 09:08:59 [127.0.0.1][-][cpq9h07s1fvb9aotleov2pm4j0][error][yii\db\Exception] PDOException: SQLSTATE[HY000] [1049] Unknown database 'saiadmin_dev' in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php:722
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(722): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), NULL)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(637): yii\db\Connection->createPdoInstance()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo(true)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(460): yii\db\Command->queryInternal('fetchAll', 7)
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\mysql\Schema.php(123): yii\db\Command->queryColumn()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Schema.php(235): yii\db\mysql\Schema->findTableNames('')
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\Generator.php(229): yii\db\Schema->getTableNames()
#10 [internal function]: yii\gii\generators\model\Generator->yii\gii\generators\model\{closure}()
#11 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\components\ActiveField.php(47): call_user_func(Object(Closure))
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\gii\components\ActiveField->init()
#13 [internal function]: yii\base\BaseObject->__construct(Array)
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(425): ReflectionClass->newInstanceArgs(Array)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\gii\\compone...', Array, Array)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\gii\\compone...', Array, Array)
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\widgets\ActiveForm.php(331): yii\BaseYii::createObject(Array)
#18 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\form.php(13): yii\widgets\ActiveForm->field(Object(yii\gii\generators\model\Generator), 'tableName')
#19 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\views\default\view.php(41): yii\base\View->renderFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile('C:\\Users\\<USER>\gii\controllers\DefaultController))
#25 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render('view', Array, Object(yii\gii\controllers\DefaultController))
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\controllers\DefaultController.php(72): yii\base\Controller->render('view', Array)
#27 [internal function]: yii\gii\controllers\DefaultController->actionView('model')
#28 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array(Array, Array)
#29 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams(Array)
#30 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction('view', Array)
#31 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('gii/default/vie...', Array)
#32 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#33 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#34 {main}

Next yii\db\Exception: SQLSTATE[HY000] [1049] Unknown database 'saiadmin_dev' in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php:648
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo(true)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Command.php(460): yii\db\Command->queryInternal('fetchAll', 7)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\mysql\Schema.php(123): yii\db\Command->queryColumn()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\db\Schema.php(235): yii\db\mysql\Schema->findTableNames('')
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\Generator.php(229): yii\db\Schema->getTableNames()
#8 [internal function]: yii\gii\generators\model\Generator->yii\gii\generators\model\{closure}()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\components\ActiveField.php(47): call_user_func(Object(Closure))
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\gii\components\ActiveField->init()
#11 [internal function]: yii\base\BaseObject->__construct(Array)
#12 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(425): ReflectionClass->newInstanceArgs(Array)
#13 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\gii\\compone...', Array, Array)
#14 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\gii\\compone...', Array, Array)
#15 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\widgets\ActiveForm.php(331): yii\BaseYii::createObject(Array)
#16 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\generators\model\form.php(13): yii\widgets\ActiveForm->field(Object(yii\gii\generators\model\Generator), 'tableName')
#17 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\views\default\view.php(41): yii\base\View->renderFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(348): require('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile('C:\\Users\\<USER>\Users\Administrator\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile('C:\\Users\\<USER>\gii\controllers\DefaultController))
#23 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render('view', Array, Object(yii\gii\controllers\DefaultController))
#24 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2-gii\src\controllers\DefaultController.php(72): yii\base\Controller->render('view', Array)
#25 [internal function]: yii\gii\controllers\DefaultController->actionView('model')
#26 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array(Array, Array)
#27 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams(Array)
#28 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction('view', Array)
#29 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('gii/default/vie...', Array)
#30 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#31 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#32 {main}
Additional Information:
Array
(
    [0] => HY000
    [1] => 1049
    [2] => Unknown database 'saiadmin_dev'
)

2025-08-03 09:08:59 [127.0.0.1][-][cpq9h07s1fvb9aotleov2pm4j0][info][application] $_GET = [
    'id' => 'model'
]

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61618'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/gii/model'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/gii/model'
    'PHP_SELF' => '/index.php/gii/model'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_CACHE_CONTROL' => 'max-age=0'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'same-origin'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_REFERER' => 'http://localhost:8080/gii/module'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754183339.0513
    'REQUEST_TIME' => 1754183339
]
2025-08-03 09:09:48 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('', Array)
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->run()
#2 {main}
2025-08-03 09:09:48 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
    '_csrf' => '6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"SVPutlWgCS0eDYnRDWkCAj0CystDWY8x\";}'
    'PHPSESSID' => 'cpq9h07s1fvb9aotleov2pm4j0'
]

$_SESSION = [
    '__flash' => []
    '__returnUrl' => 'http://localhost:8080/demo'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61867'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PHP_SELF' => '/index.php'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d; _csrf=6c54bc550fc1b1e4ca43b26650414ad5e4043314075ff30950036efd415e1897a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22SVPutlWgCS0eDYnRDWkCAj0CystDWY8x%22%3B%7D; PHPSESSID=cpq9h07s1fvb9aotleov2pm4j0'
    'REQUEST_TIME_FLOAT' => 1754183388.8335
    'REQUEST_TIME' => 1754183388
]
