2025-08-03 06:46:43 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 06:46:42 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '54457'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PHP_SELF' => '/index.php'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754174802.9376
    'REQUEST_TIME' => 1754174802
]
2025-08-03 06:49:34 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 06:49:34 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSID' => 'af4260ac6522da41585c4707d086ec4d'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '54658'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/demo'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/demo'
    'PHP_SELF' => '/index.php/demo'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'HTTP_COOKIE' => 'PHPSID=af4260ac6522da41585c4707d086ec4d'
    'REQUEST_TIME_FLOAT' => 1754174974.4235
    'REQUEST_TIME' => 1754174974
]
2025-08-03 06:49:52 [127.0.0.1][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "yii\debug\Module" does not exist in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:513
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(513): ReflectionClass->__construct('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#10 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#11 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "yii\debug\Module". in C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php:515
Stack trace:
#0 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(391): yii\di\Container->getDependencies('yii\\debug\\Modul...')
#1 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\di\Container.php(176): yii\di\Container->build('yii\\debug\\Modul...', Array, Array)
#2 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\BaseYii.php(374): yii\di\Container->get('yii\\debug\\Modul...', Array, Array)
#3 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Module.php(448): yii\BaseYii::createObject(Array, Array)
#4 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(316): yii\base\Module->getModule('debug')
#5 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\web\Application.php(69): yii\base\Application->bootstrap()
#6 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(274): yii\web\Application->bootstrap()
#7 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\BaseObject.php(112): yii\base\Application->init()
#8 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\vendor\yiisoft\yii2\base\Application.php(207): yii\base\BaseObject->__construct(Array)
#9 C:\Users\<USER>\Desktop\sai\saiadmin-boot\yii2-saiadmin\web\index.php(20): yii\base\Application->__construct(Array)
#10 {main}
2025-08-03 06:49:52 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '54692'
    'SERVER_SOFTWARE' => 'PHP 8.2.20 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/demo'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'C:\\Users\\<USER>\\Desktop\\sai\\saiadmin-boot\\yii2-saiadmin\\web\\index.php'
    'PATH_INFO' => '/demo'
    'PHP_SELF' => '/index.php/demo'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_USER_AGENT' => 'curl/8.14.1'
    'HTTP_ACCEPT' => '*/*'
    'HTTP_ACCEPT_ENCODING' => 'gzip'
    'REQUEST_TIME_FLOAT' => 1754174992.1937
    'REQUEST_TIME' => 1754174992
]
