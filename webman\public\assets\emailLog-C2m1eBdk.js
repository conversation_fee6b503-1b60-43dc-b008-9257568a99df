import{h as _}from"./index-ybrmzYq5.js";import{M as F}from"./@arco-design-uttiljWv.js";import{r as u,a as v,o as L,h as r,n as M,k as c,t as e,l as o,j as w,p as h,y as V}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const x={getPageList(d={}){return _({url:"/core/email/index",method:"get",params:d})},destroy(d){return _({url:"/core/email/destroy",method:"delete",data:d})}},N={class:"ma-content-block"},Se={__name:"emailLog",setup(d){const n=u(),y=u([{label:"成功",value:"success"},{label:"失败",value:"failure"}]),l=u({from:"",email:"",code:"",status:"",create_time:[],orderBy:"create_time",orderType:"desc"}),g=v({api:x.getPageList,rowSelection:{showCheckedAll:!0},operationColumnWidth:100,delete:{show:!0,auth:["/core/email/destroy"],func:async i=>{var m;(await x.destroy(i)).code===200&&(F.success("删除成功！"),(m=n.value)==null||m.refresh())}}}),b=v([{title:"服务Host",dataIndex:"gateway",width:120},{title:"发件人",dataIndex:"from",width:180},{title:"收件人",dataIndex:"email",width:180},{title:"验证码",dataIndex:"code",width:100},{title:"状态",dataIndex:"status",width:100},{title:"发送结果",dataIndex:"response",width:180},{title:"发送时间",dataIndex:"create_time",width:180}]),k=async()=>{},I=async()=>{var i;(i=n.value)==null||i.refresh()};return L(async()=>{k(),I()}),(i,t)=>{const m=r("a-input"),s=r("a-form-item"),p=r("a-col"),C=r("a-select"),U=r("a-range-picker"),f=r("a-tag"),B=r("sa-table");return c(),M("div",N,[e(B,{ref_key:"crudRef",ref:n,options:g,columns:b,searchForm:l.value},{tableSearch:o(()=>[e(p,{sm:8,xs:24},{default:o(()=>[e(s,{field:"from",label:"发件人"},{default:o(()=>[e(m,{modelValue:l.value.from,"onUpdate:modelValue":t[0]||(t[0]=a=>l.value.from=a),placeholder:"请输入发件人","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{sm:8,xs:24},{default:o(()=>[e(s,{field:"email",label:"收件人"},{default:o(()=>[e(m,{modelValue:l.value.email,"onUpdate:modelValue":t[1]||(t[1]=a=>l.value.email=a),placeholder:"请输入收件人","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{sm:8,xs:24},{default:o(()=>[e(s,{field:"code",label:"验证码"},{default:o(()=>[e(m,{modelValue:l.value.code,"onUpdate:modelValue":t[2]||(t[2]=a=>l.value.code=a),placeholder:"请输入验证码","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),e(p,{sm:8,xs:24},{default:o(()=>[e(s,{label:"发送状态",field:"status"},{default:o(()=>[e(C,{modelValue:l.value.status,"onUpdate:modelValue":t[3]||(t[3]=a=>l.value.status=a),"field-names":{label:"label",value:"value"},options:y.value,placeholder:"请选择发送状态","allow-clear":""},null,8,["modelValue","options"])]),_:1})]),_:1}),e(p,{sm:16,xs:24},{default:o(()=>[e(s,{field:"create_time",label:"操作时间"},{default:o(()=>[e(U,{modelValue:l.value.create_time,"onUpdate:modelValue":t[4]||(t[4]=a=>l.value.create_time=a),showTime:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),status:o(({record:a})=>[a.status==="success"?(c(),w(f,{key:0,color:"green"},{default:o(()=>t[5]||(t[5]=[V("成功")])),_:1})):h("",!0),a.status==="failure"?(c(),w(f,{key:1,color:"red"},{default:o(()=>t[6]||(t[6]=[V("失败")])),_:1})):h("",!0)]),_:1},8,["options","columns","searchForm"])])}}};export{Se as default};
