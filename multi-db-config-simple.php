<?php
/**
 * 简化的多数据源配置文件
 * 用于测试和演示，不依赖框架函数
 */

return [
    // 默认数据库连接标识
    'default' => 'mysql',
    
    // 多数据库连接配置
    'connections' => [
        // 主数据库 - SaiAdmin核心数据
        'mysql' => [
            'type' => 'mysql',
            'hostname' => '127.0.0.1',
            'database' => 'saiadmin',
            'username' => 'root',
            'password' => '5GeNi1v7P7Xcur5W',
            'hostport' => 3306,
            'params' => [
                PDO::ATTR_TIMEOUT => 3,
                PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                PDO::ATTR_PERSISTENT => true,
            ],
            'charset' => 'utf8mb4',
            'prefix' => '',
            'break_reconnect' => true,
            'bootstrap' => '',
        ],
        
        // 从数据库 - 读写分离（读库）
        'mysql_read' => [
            'type' => 'mysql',
            'hostname' => '127.0.0.1',
            'database' => 'saiadmin_read',
            'username' => 'readonly_user',
            'password' => '5GeNi1v7P7Xcur5W',
            'hostport' => 3306,
            'params' => [
                PDO::ATTR_TIMEOUT => 3,
                PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                PDO::ATTR_PERSISTENT => true,
            ],
            'charset' => 'utf8mb4',
            'prefix' => '',
            'break_reconnect' => true,
            'bootstrap' => '',
        ],
        
        // 日志数据库 - 专门存储日志数据
        'mysql_log' => [
            'type' => 'mysql',
            'hostname' => '127.0.0.1',
            'database' => 'saiadmin_logs',
            'username' => 'log_user',
            'password' => '5GeNi1v7P7Xcur5W',
            'hostport' => 3306,
            'params' => [
                PDO::ATTR_TIMEOUT => 3,
                PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                PDO::ATTR_PERSISTENT => false,
            ],
            'charset' => 'utf8mb4',
            'prefix' => 'log_',
            'break_reconnect' => true,
            'bootstrap' => '',
        ],
        
        // 缓存数据库 - 使用MySQL作为缓存
        'mysql_cache' => [
            'type' => 'mysql',
            'hostname' => '127.0.0.1',
            'database' => 'saiadmin_cache',
            'username' => 'cache_user',
            'password' => '5GeNi1v7P7Xcur5W',
            'hostport' => 3306,
            'params' => [
                PDO::ATTR_TIMEOUT => 2,
                PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
                PDO::ATTR_PERSISTENT => false,
            ],
            'charset' => 'utf8mb4',
            'prefix' => 'cache_',
            'break_reconnect' => true,
            'bootstrap' => '',
        ],
        
        // PostgreSQL数据库示例
        'pgsql' => [
            'type' => 'pgsql',
            'hostname' => '127.0.0.1',
            'database' => 'saiadmin_pg',
            'username' => 'postgres',
            'password' => '',
            'hostport' => 5432,
            'params' => [
                PDO::ATTR_TIMEOUT => 3,
            ],
            'charset' => 'utf8',
            'prefix' => '',
            'break_reconnect' => true,
            'bootstrap' => '',
        ],
        
        // SQLite数据库示例
        'sqlite' => [
            'type' => 'sqlite',
            'database' => __DIR__ . '/runtime/database/saiadmin.db',
            'prefix' => '',
            'break_reconnect' => false,
            'bootstrap' => '',
        ],
    ],
    
    // 读写分离配置
    'read_write_separation' => [
        'enable' => false,  // 设置为true启用读写分离
        'write' => 'mysql',      // 写库
        'read' => 'mysql_read',  // 读库
    ],
    
    // 分库分表配置
    'sharding' => [
        'enable' => false,
        'rules' => [
            // 用户表分表规则示例
            'sa_system_user' => [
                'type' => 'mod',
                'field' => 'id',
                'mod' => 4,
                'tables' => [
                    'sa_system_user_0',
                    'sa_system_user_1', 
                    'sa_system_user_2',
                    'sa_system_user_3',
                ]
            ]
        ]
    ],
];
