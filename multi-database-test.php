<?php
/**
 * SaiAdmin 多数据源测试脚本
 * 演示如何使用多数据源功能
 */

require_once __DIR__ . '/webman/vendor/autoload.php';

use plugin\saiadmin\app\database\MultiDatabaseManager;
use plugin\saiadmin\app\model\OperationLog;
use plugin\saiadmin\app\model\LoginLog;
use plugin\saiadmin\app\model\PerformanceLog;
use plugin\saiadmin\app\model\CacheData;

echo "🗄️ SaiAdmin 多数据源功能测试\n";
echo "========================================\n\n";

// 1. 测试数据库连接状态
echo "[1/8] 测试数据库连接状态...\n";
$connectionStatus = MultiDatabaseManager::getAllConnectionStatus();

foreach ($connectionStatus as $alias => $status) {
    $icon = $status['status'] ? '✅' : '❌';
    echo "  {$icon} {$alias} ({$status['connection']}): " . 
         ($status['status'] ? '连接正常' : '连接失败') . "\n";
}
echo "\n";

// 2. 数据库健康检查
echo "[2/8] 数据库健康检查...\n";
$healthCheck = MultiDatabaseManager::healthCheck();
echo "  整体状态: " . ($healthCheck['overall_status'] === 'healthy' ? '✅ 健康' : '❌ 异常') . "\n";

foreach ($healthCheck['connections'] as $alias => $conn) {
    if ($conn['status'] === 'healthy') {
        echo "  ✅ {$alias}: 响应时间 {$conn['response_time']}\n";
    } else {
        echo "  ❌ {$alias}: {$conn['error']}\n";
    }
}

if (!empty($healthCheck['issues'])) {
    echo "  ⚠️ 发现问题:\n";
    foreach ($healthCheck['issues'] as $issue) {
        echo "    - {$issue}\n";
    }
}
echo "\n";

// 3. 创建日志数据库表
echo "[3/8] 创建日志数据库表...\n";
try {
    MultiDatabaseManager::createLogTables();
    echo "  ✅ 日志表创建成功\n";
} catch (Exception $e) {
    echo "  ❌ 日志表创建失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. 创建缓存数据库表
echo "[4/8] 创建缓存数据库表...\n";
try {
    MultiDatabaseManager::createCacheTables();
    echo "  ✅ 缓存表创建成功\n";
} catch (Exception $e) {
    echo "  ❌ 缓存表创建失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. 测试操作日志记录
echo "[5/8] 测试操作日志记录...\n";
try {
    $logData = [
        'user_id' => 1,
        'username' => 'admin',
        'method' => 'POST',
        'router' => '/api/test',
        'service_name' => 'TestService',
        'ip' => '127.0.0.1',
        'ip_location' => '本地',
        'request_data' => ['test' => 'data'],
        'response_code' => '200',
        'response_data' => ['status' => 'success']
    ];
    
    $result = OperationLog::record($logData);
    echo "  " . ($result ? '✅' : '❌') . " 操作日志记录" . ($result ? '成功' : '失败') . "\n";
} catch (Exception $e) {
    echo "  ❌ 操作日志记录异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. 测试登录日志记录
echo "[6/8] 测试登录日志记录...\n";
try {
    $loginData = [
        'username' => 'admin',
        'ip' => '127.0.0.1',
        'ip_location' => '本地',
        'os' => 'Windows 10',
        'browser' => 'Chrome',
        'status' => 1,
        'message' => '登录成功'
    ];
    
    $result = LoginLog::record($loginData);
    echo "  " . ($result ? '✅' : '❌') . " 登录日志记录" . ($result ? '成功' : '失败') . "\n";
    
    // 获取登录统计
    $stats = LoginLog::getLoginStats();
    echo "  📊 今日登录统计: 总计{$stats['total']}次, 成功{$stats['success']}次, 失败{$stats['failed']}次\n";
} catch (Exception $e) {
    echo "  ❌ 登录日志记录异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. 测试性能日志记录
echo "[7/8] 测试性能日志记录...\n";
try {
    $perfData = [
        'request_id' => uniqid(),
        'uri' => '/api/test',
        'method' => 'GET',
        'response_time' => 125.50,
        'memory_usage' => 2048000,
        'query_count' => 3
    ];
    
    $result = PerformanceLog::record($perfData);
    echo "  " . ($result ? '✅' : '❌') . " 性能日志记录" . ($result ? '成功' : '失败') . "\n";
    
    // 获取性能统计
    $stats = PerformanceLog::getPerformanceStats();
    if (!empty($stats)) {
        echo "  📊 今日性能统计: 平均响应时间{$stats['avg_response_time']}ms, 最大内存使用" . 
             formatBytes($stats['max_memory_usage']) . "\n";
    }
} catch (Exception $e) {
    echo "  ❌ 性能日志记录异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 8. 测试缓存功能
echo "[8/8] 测试缓存功能...\n";
try {
    // 设置缓存
    $cacheKey = 'test_cache_' . time();
    $cacheValue = ['test' => 'data', 'timestamp' => time()];
    $result = CacheData::setCache($cacheKey, $cacheValue, 3600);
    echo "  " . ($result ? '✅' : '❌') . " 缓存设置" . ($result ? '成功' : '失败') . "\n";
    
    // 获取缓存
    $cachedValue = CacheData::getCache($cacheKey);
    $getResult = $cachedValue !== null && $cachedValue['test'] === 'data';
    echo "  " . ($getResult ? '✅' : '❌') . " 缓存获取" . ($getResult ? '成功' : '失败') . "\n";
    
    // 删除缓存
    $deleteResult = CacheData::deleteCache($cacheKey);
    echo "  " . ($deleteResult ? '✅' : '❌') . " 缓存删除" . ($deleteResult ? '成功' : '失败') . "\n";
    
    // 清理过期缓存
    $cleanCount = CacheData::cleanExpired();
    echo "  🧹 清理过期缓存: {$cleanCount}条\n";
} catch (Exception $e) {
    echo "  ❌ 缓存功能测试异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 9. 数据库统计信息
echo "📊 数据库统计信息:\n";
$statistics = MultiDatabaseManager::getStatistics();
foreach ($statistics as $alias => $stat) {
    echo "  📈 {$alias} ({$stat['type']}): {$stat['status']}\n";
    if (isset($stat['connections'])) {
        echo "    - 活跃连接: {$stat['connections']}\n";
        echo "    - 总查询数: {$stat['total_queries']}\n";
    }
    if (isset($stat['error'])) {
        echo "    - 错误: {$stat['error']}\n";
    }
}
echo "\n";

// 10. 读写分离测试
echo "🔄 读写分离测试:\n";
if (env('DB_RW_SEPARATE', false)) {
    echo "  ✅ 读写分离已启用\n";
    
    try {
        // 测试读操作
        $readResult = MultiDatabaseManager::query('SELECT COUNT(*) as count FROM sa_system_user');
        echo "  📖 读操作测试: " . ($readResult ? '✅ 成功' : '❌ 失败') . "\n";
        
        // 测试写操作
        $writeResult = MultiDatabaseManager::execute('UPDATE sa_system_user SET updated_at = NOW() WHERE id = 1');
        echo "  ✏️ 写操作测试: " . ($writeResult !== false ? '✅ 成功' : '❌ 失败') . "\n";
    } catch (Exception $e) {
        echo "  ❌ 读写分离测试异常: " . $e->getMessage() . "\n";
    }
} else {
    echo "  ⚠️ 读写分离未启用\n";
    echo "  💡 要启用读写分离，请在.env文件中设置 DB_RW_SEPARATE=true\n";
}
echo "\n";

echo "========================================\n";
echo "🎉 多数据源功能测试完成！\n";
echo "========================================\n\n";

echo "📋 使用指南:\n";
echo "1. 基本连接使用:\n";
echo "   \$db = MultiDatabaseManager::main();  // 主数据库\n";
echo "   \$db = MultiDatabaseManager::log();   // 日志数据库\n";
echo "   \$db = MultiDatabaseManager::cache(); // 缓存数据库\n\n";

echo "2. 模型使用:\n";
echo "   OperationLog::record(\$data);  // 记录操作日志\n";
echo "   LoginLog::record(\$data);      // 记录登录日志\n";
echo "   CacheData::setCache(\$key, \$value); // 设置缓存\n\n";

echo "3. 读写分离:\n";
echo "   \$result = MultiDatabaseManager::query(\$sql);   // 自动选择读/写库\n";
echo "   \$result = MultiDatabaseManager::execute(\$sql); // 强制使用写库\n\n";

echo "4. 事务处理:\n";
echo "   MultiDatabaseManager::transaction(function() {\n";
echo "       // 事务操作\n";
echo "   });\n\n";

echo "💡 配置说明:\n";
echo "- 在 .env 文件中配置各数据库连接参数\n";
echo "- 在 think-orm.php 中定义连接配置\n";
echo "- 设置 DB_RW_SEPARATE=true 启用读写分离\n";

function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
