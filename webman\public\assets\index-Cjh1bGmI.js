import E from"./edit-l4j53F48.js";import L from"./leader-hmQzSqrT.js";import{a as k}from"./dept-BazEhZT0.js";import{M as N}from"./@arco-design-uttiljWv.js";import{r as c,a as y,o as P,h as i,ba as $,n as p,k as s,t as r,l as a,M as U,p as x,j as w,y as g,F as j,P as z,z as A}from"./@vue-9ZIPiVZG.js";import"./index-DkGLNqVb.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const T={class:"ma-content-block"},W={key:0},q={key:1},G={key:0},Ze={__name:"index",setup(H){const d=c(),l=c(),f=c(),b=e=>{f.value.open(e)},m=c({name:"",create_time:[]}),V=y({api:k.getPageList,rowSelection:{showCheckedAll:!0},isExpand:!0,operationColumnWidth:220,add:{show:!0,auth:["/core/dept/save"],func:async()=>{var e;(e=l.value)==null||e.open()}},edit:{show:!0,auth:["/core/dept/update"],func:async e=>{var o,n;(o=l.value)==null||o.open("edit"),(n=l.value)==null||n.setFormData(e)}},delete:{show:!0,auth:["/core/dept/destroy"],func:async e=>{var n;(await k.destroy(e)).code===200&&(N.success("删除成功！"),(n=d.value)==null||n.refresh())}}}),C=y([{title:"部门名称",dataIndex:"name",width:180},{title:"领导列表",dataIndex:"leader"},{title:"排序",dataIndex:"sort",width:100},{title:"状态",dataIndex:"status",type:"dict",dict:"data_status",width:120},{title:"创建时间",dataIndex:"create_time",width:180}]),F=async()=>{},u=async()=>{var e;(e=d.value)==null||e.refresh()};return P(async()=>{F(),u()}),(e,o)=>{const n=i("a-input"),h=i("a-form-item"),v=i("a-col"),R=i("a-range-picker"),I=i("a-tag"),M=i("icon-user"),S=i("a-link"),B=i("sa-table"),D=$("auth");return s(),p("div",T,[r(B,{ref_key:"crudRef",ref:d,options:V,columns:C,searchForm:m.value},{tableSearch:a(()=>[r(v,{sm:8,xs:24},{default:a(()=>[r(h,{field:"name",label:"部门名称"},{default:a(()=>[r(n,{modelValue:m.value.name,"onUpdate:modelValue":o[0]||(o[0]=t=>m.value.name=t),placeholder:"请输入部门名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),r(v,{sm:8,xs:24},{default:a(()=>[r(h,{field:"create_time",label:"时间范围"},{default:a(()=>[r(R,{modelValue:m.value.create_time,"onUpdate:modelValue":o[1]||(o[1]=t=>m.value.create_time=t)},null,8,["modelValue"])]),_:1})]),_:1})]),leader:a(({record:t})=>[t.leader.length>0?(s(),p("div",W,[(s(!0),p(j,null,z(t.leader,_=>(s(),w(I,{key:_.id,class:"ml-2"},{default:a(()=>[g(A(_.username),1)]),_:2},1024))),128))])):(s(),p("div",q))]),operationCell:a(({record:t})=>[t.disabled?(s(),p("div",G)):x("",!0)]),operationBeforeExtend:a(({record:t})=>[t.disabled?x("",!0):U((s(),w(S,{key:0,onClick:_=>b(t)},{default:a(()=>[r(M),o[2]||(o[2]=g(" 领导列表 "))]),_:2},1032,["onClick"])),[[D,["/core/dept/leaders"]]])]),_:1},8,["options","columns","searchForm"]),r(E,{ref_key:"editRef",ref:l,onSuccess:u},null,512),r(L,{ref_key:"leaderRef",ref:f,onSuccess:u},null,512)])}}};export{Ze as default};
