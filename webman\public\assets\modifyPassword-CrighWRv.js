import{a as V}from"./user-BW-rYcwt.js";import{t as y}from"./index-ybrmzYq5.js";import{b as k}from"./vue-router-DXldG2q0.js";import{M as d}from"./@arco-design-uttiljWv.js";import{a as U,r as f,h as s,n as q,k as C,t,l,y as u,F as S}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const Eo={__name:"modifyPassword",setup(B){const w=k(),m=U({oldPassword:"",newPassword:"",newPassword_confirmation:""}),p=f(!1),r=f(0),c=()=>{w.push({name:"login"})},_=async e=>{if(!e.errors){if(e.values.newPassword!==e.values.newPassword_confirmation){d.error("确认密码与新密码不一致");return}const o=await V.modifyPassword(e.values);o.code===200?(y.local.clear(),p.value=!0):d.error(o.message)}},v=e=>{if(e.length<1){r.value=0;return}if(!(e.length>=6)){r.value=0;return}r.value=.1,/\d/.test(e)&&(r.value+=.1),/[a-z]/.test(e)&&(r.value+=.1),/[A-Z]/.test(e)&&(r.value+=.3),/[`~!@#$%^&*()_+<>?:"{},./;'[\]]/.test(e)&&(r.value+=.4)};return(e,o)=>{const n=s("a-input-password"),i=s("a-form-item"),b=s("a-progress"),P=s("a-button"),x=s("a-form"),g=s("a-modal");return C(),q(S,null,[t(x,{class:"w-full md:w-full mt-3",model:m,onSubmit:_},{default:l(()=>[t(i,{label:"旧密码",field:"oldPassword","label-col-flex":"80px",rules:[{required:!0,message:"旧密码必填"}]},{default:l(()=>[t(n,{modelValue:m.oldPassword,"onUpdate:modelValue":o[0]||(o[0]=a=>m.oldPassword=a),"allow-clear":"",autocomplete:"off"},null,8,["modelValue"])]),_:1}),t(i,{label:"新密码",field:"newPassword","label-col-flex":"80px",rules:[{required:!0,message:"新密码必填"}]},{default:l(()=>[t(n,{modelValue:m.newPassword,"onUpdate:modelValue":o[1]||(o[1]=a=>m.newPassword=a),onInput:v,onClear:o[2]||(o[2]=()=>r.value=0),autocomplete:"off","allow-clear":""},null,8,["modelValue"])]),_:1}),t(i,{label:"密码安全","label-col-flex":"80px"},{default:l(()=>[t(b,{steps:3,status:"success",percent:r.value,animation:"","show-text":!1},null,8,["percent"])]),_:1}),t(i,{label:"确认密码",field:"newPassword_confirmation","label-col-flex":"80px",rules:[{required:!0,message:"确认密码必填"}]},{default:l(()=>[t(n,{"allow-clear":"",modelValue:m.newPassword_confirmation,"onUpdate:modelValue":o[3]||(o[3]=a=>m.newPassword_confirmation=a),autocomplete:"off"},null,8,["modelValue"])]),_:1}),t(i,{"label-col-flex":"80px"},{default:l(()=>[t(P,{"html-type":"submit",type:"primary"},{default:l(()=>o[5]||(o[5]=[u("保存")])),_:1})]),_:1})]),_:1},8,["model"]),t(g,{visible:p.value,"onUpdate:visible":o[4]||(o[4]=a=>p.value=a),onOk:c},{title:l(()=>o[6]||(o[6]=[u("提示")])),default:l(()=>[o[7]||(o[7]=u(" 密码已经修改成功，需要重新登录系统，点击确定跳转登录页面。 "))]),_:1},8,["visible"])],64)}}};export{Eo as default};
