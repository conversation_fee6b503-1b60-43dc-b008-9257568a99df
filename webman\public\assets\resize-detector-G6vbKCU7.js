var h=null;function y(e){return h||(h=(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(r){return setTimeout(r,16)}).bind(window)),h(e)}var v=null;function C(e){v||(v=(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(r){clearTimeout(r)}).bind(window)),v(e)}function E(e){var r=document.createElement("style");return r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e)),(document.querySelector("head")||document.body).appendChild(r),r}function o(e,r){r===void 0&&(r={});var t=document.createElement(e);return Object.keys(r).forEach(function(i){t[i]=r[i]}),t}function b(e,r,t){var i=window.getComputedStyle(e,null)||{display:"none"};return i[r]}function g(e){if(!document.documentElement.contains(e))return{detached:!0,rendered:!1};for(var r=e;r!==document;){if(b(r,"display")==="none")return{detached:!1,rendered:!1};r=r.parentNode}return{detached:!1,rendered:!0}}var x='.resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',l=0,d=null;function F(e,r){e.__resize_mutation_handler__||(e.__resize_mutation_handler__=S.bind(e));var t=e.__resize_listeners__;if(!t){if(e.__resize_listeners__=[],window.ResizeObserver){var i=e.offsetWidth,n=e.offsetHeight,_=new ResizeObserver(function(){!e.__resize_observer_triggered__&&(e.__resize_observer_triggered__=!0,e.offsetWidth===i&&e.offsetHeight===n)||c(e)}),s=g(e),f=s.detached,u=s.rendered;e.__resize_observer_triggered__=f===!1&&u===!1,e.__resize_observer__=_,_.observe(e)}else if(e.attachEvent&&e.addEventListener)e.__resize_legacy_resize_handler__=function(){c(e)},e.attachEvent("onresize",e.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);else if(l||(d=E(x)),A(e),e.__resize_rendered__=g(e).rendered,window.MutationObserver){var a=new MutationObserver(e.__resize_mutation_handler__);a.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),e.__resize_mutation_observer__=a}}e.__resize_listeners__.push(r),l++}function H(e,r){var t=e.__resize_listeners__;if(t){if(r&&t.splice(t.indexOf(r),1),!t.length||!r){if(e.detachEvent&&e.removeEventListener){e.detachEvent("onresize",e.__resize_legacy_resize_handler__),document.removeEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);return}e.__resize_observer__?(e.__resize_observer__.unobserve(e),e.__resize_observer__.disconnect(),e.__resize_observer__=null):(e.__resize_mutation_observer__&&(e.__resize_mutation_observer__.disconnect(),e.__resize_mutation_observer__=null),e.removeEventListener("scroll",z),e.removeChild(e.__resize_triggers__.triggers),e.__resize_triggers__=null),e.__resize_listeners__=null}!--l&&d&&d.parentNode.removeChild(d)}}function L(e){var r=e.__resize_last__,t=r.width,i=r.height,n=e.offsetWidth,_=e.offsetHeight;return n!==t||_!==i?{width:n,height:_}:null}function S(){var e=g(this),r=e.rendered,t=e.detached;r!==this.__resize_rendered__&&(!t&&this.__resize_triggers__&&(p(this),this.addEventListener("scroll",z,!0)),this.__resize_rendered__=r,c(this))}function z(){var e=this;p(this),this.__resize_raf__&&C(this.__resize_raf__),this.__resize_raf__=y(function(){var r=L(e);r&&(e.__resize_last__=r,c(e))})}function c(e){!e||!e.__resize_listeners__||e.__resize_listeners__.forEach(function(r){r.call(e,e)})}function A(e){var r=b(e,"position");(!r||r==="static")&&(e.style.position="relative"),e.__resize_old_position__=r,e.__resize_last__={};var t=o("div",{className:"resize-triggers"}),i=o("div",{className:"resize-expand-trigger"}),n=o("div"),_=o("div",{className:"resize-contract-trigger"});i.appendChild(n),t.appendChild(i),t.appendChild(_),e.appendChild(t),e.__resize_triggers__={triggers:t,expand:i,expandChild:n,contract:_},p(e),e.addEventListener("scroll",z,!0),e.__resize_last__={width:e.offsetWidth,height:e.offsetHeight}}function p(e){var r=e.__resize_triggers__,t=r.expand,i=r.expandChild,n=r.contract,_=n.scrollWidth,s=n.scrollHeight,f=t.offsetWidth,u=t.offsetHeight,a=t.scrollWidth,w=t.scrollHeight;n.scrollLeft=_,n.scrollTop=s,i.style.width=f+1+"px",i.style.height=u+1+"px",t.scrollLeft=a,t.scrollTop=w}export{F as a,H as r};
