import {
  registerLanguage
} from "./chunk-TIAC434R.js";
import "./chunk-OX74CKBW.js";
import "./chunk-WBIRFOMM.js";
import "./chunk-LK32TJAX.js";

// node_modules/monaco-editor/esm/vs/basic-languages/html/html.contribution.js
registerLanguage({
  id: "html",
  extensions: [".html", ".htm", ".shtml", ".xhtml", ".mdoc", ".jsp", ".asp", ".aspx", ".jshtm"],
  aliases: ["HTML", "htm", "html", "xhtml"],
  mimetypes: ["text/html", "text/x-jshtm", "text/template", "text/ng-template"],
  loader: () => {
    if (false) {
      return new Promise((resolve, reject) => {
        __require(["vs/basic-languages/html/html"], resolve, reject);
      });
    } else {
      return import("./html-QL2ZKQUE.js");
    }
  }
});
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/html/html.contribution.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=monaco-editor_esm_vs_basic-languages_html_html__contribution.js.map
