<?php
/**
 * SaiAdmin 应用参数配置
 */

return [
    "adminEmail" => "<EMAIL>",
    "senderEmail" => "<EMAIL>",
    "senderName" => "SaiAdmin",
    "supportEmail" => "<EMAIL>",
    "user.passwordResetTokenExpire" => 3600,
    "user.passwordMinLength" => 8,
    
    // SaiAdmin 特定参数
    "saiadmin.version" => "2.0.0",
    "saiadmin.name" => "SaiAdmin Yii2",
    "saiadmin.description" => "基于 Yii 2.0 的快速开发框架",
];