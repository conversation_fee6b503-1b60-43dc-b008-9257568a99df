/**
 * 前端性能优化工具
 * 提供页面性能监控、资源优化、用户体验提升等功能
 */

class PerformanceOptimizer {
  constructor() {
    this.metrics = {
      pageLoad: {},
      apiCalls: [],
      userInteractions: [],
      resourceLoading: [],
      memoryUsage: []
    }
    
    this.observers = {
      performance: null,
      intersection: null,
      mutation: null
    }
    
    this.config = {
      enableMonitoring: true,
      enableLazyLoading: true,
      enableResourceOptimization: true,
      reportInterval: 30000, // 30秒上报一次
      maxApiCacheSize: 100,
      maxMetricsHistory: 1000
    }
    
    this.init()
  }
  
  /**
   * 初始化性能优化器
   */
  init() {
    if (!this.config.enableMonitoring) return
    
    this.initPerformanceObserver()
    this.initIntersectionObserver()
    this.initResourceOptimization()
    this.initMemoryMonitoring()
    this.initUserInteractionTracking()
    this.startPerformanceReporting()
    
    console.log('🚀 性能优化器已启动')
  }
  
  /**
   * 初始化性能观察器
   */
  initPerformanceObserver() {
    if (!window.PerformanceObserver) return
    
    try {
      // 监控导航性能
      this.observers.performance = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.handlePerformanceEntry(entry)
        }
      })
      
      this.observers.performance.observe({
        entryTypes: ['navigation', 'resource', 'measure', 'paint']
      })
      
    } catch (error) {
      console.warn('性能观察器初始化失败:', error)
    }
  }
  
  /**
   * 处理性能条目
   */
  handlePerformanceEntry(entry) {
    switch (entry.entryType) {
      case 'navigation':
        this.metrics.pageLoad = {
          domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
          loadComplete: entry.loadEventEnd - entry.loadEventStart,
          firstByte: entry.responseStart - entry.requestStart,
          domInteractive: entry.domInteractive - entry.navigationStart,
          timestamp: Date.now()
        }
        break
        
      case 'resource':
        this.metrics.resourceLoading.push({
          name: entry.name,
          duration: entry.duration,
          size: entry.transferSize,
          type: this.getResourceType(entry.name),
          timestamp: Date.now()
        })
        break
        
      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          this.metrics.pageLoad.firstContentfulPaint = entry.startTime
        }
        break
    }
  }
  
  /**
   * 初始化交叉观察器（懒加载）
   */
  initIntersectionObserver() {
    if (!window.IntersectionObserver || !this.config.enableLazyLoading) return
    
    this.observers.intersection = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.handleLazyLoad(entry.target)
        }
      })
    }, {
      rootMargin: '50px'
    })
  }
  
  /**
   * 处理懒加载
   */
  handleLazyLoad(element) {
    if (element.dataset.src) {
      element.src = element.dataset.src
      element.removeAttribute('data-src')
      this.observers.intersection.unobserve(element)
    }
  }
  
  /**
   * 启用图片懒加载
   */
  enableImageLazyLoading() {
    const images = document.querySelectorAll('img[data-src]')
    images.forEach(img => {
      this.observers.intersection.observe(img)
    })
  }
  
  /**
   * 初始化资源优化
   */
  initResourceOptimization() {
    if (!this.config.enableResourceOptimization) return
    
    // 预加载关键资源
    this.preloadCriticalResources()
    
    // 预连接到重要域名
    this.preconnectToDomains([
      'https://api.example.com',
      'https://cdn.example.com'
    ])
    
    // 优化字体加载
    this.optimizeFontLoading()
  }
  
  /**
   * 预加载关键资源
   */
  preloadCriticalResources() {
    const criticalResources = [
      { href: '/api/user/info', as: 'fetch', crossorigin: 'anonymous' },
      { href: '/static/css/critical.css', as: 'style' }
    ]
    
    criticalResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      Object.assign(link, resource)
      document.head.appendChild(link)
    })
  }
  
  /**
   * 预连接到域名
   */
  preconnectToDomains(domains) {
    domains.forEach(domain => {
      const link = document.createElement('link')
      link.rel = 'preconnect'
      link.href = domain
      document.head.appendChild(link)
    })
  }
  
  /**
   * 优化字体加载
   */
  optimizeFontLoading() {
    // 使用font-display: swap优化字体显示
    const style = document.createElement('style')
    style.textContent = `
      @font-face {
        font-family: 'OptimizedFont';
        src: url('/fonts/font.woff2') format('woff2');
        font-display: swap;
      }
    `
    document.head.appendChild(style)
  }
  
  /**
   * 初始化内存监控
   */
  initMemoryMonitoring() {
    if (!performance.memory) return
    
    setInterval(() => {
      this.metrics.memoryUsage.push({
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        timestamp: Date.now()
      })
      
      // 保持历史记录在限制范围内
      if (this.metrics.memoryUsage.length > this.config.maxMetricsHistory) {
        this.metrics.memoryUsage.shift()
      }
    }, 5000)
  }
  
  /**
   * 初始化用户交互跟踪
   */
  initUserInteractionTracking() {
    const events = ['click', 'scroll', 'keydown', 'touchstart']
    
    events.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        this.trackUserInteraction(eventType, event)
      }, { passive: true })
    })
  }
  
  /**
   * 跟踪用户交互
   */
  trackUserInteraction(type, event) {
    const interaction = {
      type,
      timestamp: Date.now(),
      target: event.target.tagName,
      x: event.clientX || 0,
      y: event.clientY || 0
    }
    
    this.metrics.userInteractions.push(interaction)
    
    // 保持历史记录在限制范围内
    if (this.metrics.userInteractions.length > this.config.maxMetricsHistory) {
      this.metrics.userInteractions.shift()
    }
  }
  
  /**
   * API调用性能监控
   */
  monitorApiCall(url, method, startTime, endTime, success, error = null) {
    const apiCall = {
      url,
      method,
      duration: endTime - startTime,
      success,
      error,
      timestamp: Date.now()
    }
    
    this.metrics.apiCalls.push(apiCall)
    
    // 保持历史记录在限制范围内
    if (this.metrics.apiCalls.length > this.config.maxMetricsHistory) {
      this.metrics.apiCalls.shift()
    }
    
    // 检查慢API
    if (apiCall.duration > 3000) {
      console.warn(`慢API检测: ${url} 耗时 ${apiCall.duration}ms`)
    }
  }
  
  /**
   * 开始性能报告
   */
  startPerformanceReporting() {
    setInterval(() => {
      this.reportPerformanceMetrics()
    }, this.config.reportInterval)
  }
  
  /**
   * 上报性能指标
   */
  reportPerformanceMetrics() {
    const report = {
      pageLoad: this.metrics.pageLoad,
      apiPerformance: this.getApiPerformanceStats(),
      memoryUsage: this.getMemoryStats(),
      userActivity: this.getUserActivityStats(),
      timestamp: Date.now()
    }
    
    // 发送到后端
    this.sendMetricsToBackend(report)
  }
  
  /**
   * 获取API性能统计
   */
  getApiPerformanceStats() {
    const recentCalls = this.metrics.apiCalls.slice(-50)
    
    if (recentCalls.length === 0) return null
    
    const totalDuration = recentCalls.reduce((sum, call) => sum + call.duration, 0)
    const successCount = recentCalls.filter(call => call.success).length
    
    return {
      averageResponseTime: totalDuration / recentCalls.length,
      successRate: (successCount / recentCalls.length) * 100,
      totalCalls: recentCalls.length,
      slowCalls: recentCalls.filter(call => call.duration > 3000).length
    }
  }
  
  /**
   * 获取内存统计
   */
  getMemoryStats() {
    const recentMemory = this.metrics.memoryUsage.slice(-10)
    
    if (recentMemory.length === 0) return null
    
    const latest = recentMemory[recentMemory.length - 1]
    const peak = Math.max(...recentMemory.map(m => m.used))
    
    return {
      current: latest.used,
      peak: peak,
      utilization: (latest.used / latest.limit) * 100
    }
  }
  
  /**
   * 获取用户活动统计
   */
  getUserActivityStats() {
    const recentInteractions = this.metrics.userInteractions.slice(-100)
    
    if (recentInteractions.length === 0) return null
    
    const interactionTypes = {}
    recentInteractions.forEach(interaction => {
      interactionTypes[interaction.type] = (interactionTypes[interaction.type] || 0) + 1
    })
    
    return {
      totalInteractions: recentInteractions.length,
      interactionTypes,
      lastActivity: recentInteractions[recentInteractions.length - 1]?.timestamp
    }
  }
  
  /**
   * 发送指标到后端
   */
  async sendMetricsToBackend(metrics) {
    try {
      await fetch('/api/performance/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metrics)
      })
    } catch (error) {
      console.warn('性能指标上报失败:', error)
    }
  }
  
  /**
   * 获取资源类型
   */
  getResourceType(url) {
    if (url.includes('.css')) return 'css'
    if (url.includes('.js')) return 'js'
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image'
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font'
    return 'other'
  }
  
  /**
   * 获取当前性能指标
   */
  getCurrentMetrics() {
    return {
      pageLoad: this.metrics.pageLoad,
      apiCalls: this.metrics.apiCalls.slice(-10),
      memoryUsage: this.metrics.memoryUsage.slice(-5),
      userInteractions: this.metrics.userInteractions.slice(-20)
    }
  }
  
  /**
   * 清理历史数据
   */
  cleanup() {
    const maxAge = 5 * 60 * 1000 // 5分钟
    const now = Date.now()
    
    this.metrics.apiCalls = this.metrics.apiCalls.filter(
      call => now - call.timestamp < maxAge
    )
    
    this.metrics.userInteractions = this.metrics.userInteractions.filter(
      interaction => now - interaction.timestamp < maxAge
    )
    
    this.metrics.memoryUsage = this.metrics.memoryUsage.filter(
      memory => now - memory.timestamp < maxAge
    )
  }
}

// 创建全局实例
const performanceOptimizer = new PerformanceOptimizer()

// 导出
export default performanceOptimizer
export { PerformanceOptimizer }
