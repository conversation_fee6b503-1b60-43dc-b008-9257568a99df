<?php
/**
 * SaiAdmin Yii 2.0 一键部署脚本
 */

echo "🚀 SaiAdmin Yii 2.0 一键部署\n";
echo "========================================\n\n";

// 部署配置
$deployConfig = [
    'project_name' => 'saiadmin',
    'web_root' => '/var/www/html',
    'domain' => 'saiadmin.local',
    'db_name' => 'saiadmin',
    'db_user' => 'saiadmin_user',
    'db_pass' => 'secure_password_' . rand(1000, 9999),
    'admin_email' => '<EMAIL>'
];

echo "[1/8] 环境检查...\n";

// 检查操作系统
$os = PHP_OS_FAMILY;
echo "  📋 操作系统: {$os}\n";

// 检查权限
if (posix_getuid() !== 0 && $os === 'Linux') {
    echo "  ⚠️ 建议使用root权限运行部署脚本\n";
}

// 检查必要命令
$requiredCommands = ['php', 'composer', 'mysql'];
foreach ($requiredCommands as $cmd) {
    $check = shell_exec("which {$cmd} 2>/dev/null");
    if ($check) {
        echo "  ✅ {$cmd} 命令可用\n";
    } else {
        echo "  ❌ {$cmd} 命令不可用\n";
    }
}

echo "\n[2/8] 创建项目目录...\n";

$projectPath = $deployConfig['web_root'] . '/' . $deployConfig['project_name'];

if (!is_dir($deployConfig['web_root'])) {
    mkdir($deployConfig['web_root'], 0755, true);
    echo "  ✅ 创建Web根目录: {$deployConfig['web_root']}\n";
}

if (!is_dir($projectPath)) {
    mkdir($projectPath, 0755, true);
    echo "  ✅ 创建项目目录: {$projectPath}\n";
} else {
    echo "  ⚠️ 项目目录已存在: {$projectPath}\n";
}

echo "\n[3/8] 复制项目文件...\n";

$sourceDir = 'yii2-saiadmin';
if (is_dir($sourceDir)) {
    // 复制文件
    $copyCommand = "cp -r {$sourceDir}/* {$projectPath}/";
    $result = shell_exec($copyCommand . ' 2>&1');
    
    if (is_dir($projectPath . '/vendor')) {
        echo "  ✅ 项目文件复制成功\n";
    } else {
        echo "  ⚠️ 项目文件复制可能有问题\n";
    }
} else {
    echo "  ❌ 源项目目录不存在: {$sourceDir}\n";
    exit(1);
}

echo "\n[4/8] 安装依赖包...\n";

// 切换到项目目录
$originalDir = getcwd();
chdir($projectPath);

// 安装Composer依赖
echo "  📦 安装Composer依赖...\n";
$composerInstall = shell_exec('composer install --no-dev --optimize-autoloader 2>&1');

if (strpos($composerInstall, 'error') === false) {
    echo "  ✅ Composer依赖安装成功\n";
} else {
    echo "  ⚠️ Composer安装可能有问题\n";
}

echo "\n[5/8] 配置权限...\n";

// 设置文件权限
$permissions = [
    'yii' => 0755,
    'runtime' => 0777,
    'web/assets' => 0777
];

foreach ($permissions as $path => $perm) {
    if (file_exists($path)) {
        chmod($path, $perm);
        echo "  ✅ 设置权限: {$path} ({$perm})\n";
    }
}

// 设置所有者
if ($os === 'Linux') {
    $chownResult = shell_exec('chown -R www-data:www-data . 2>&1');
    echo "  ✅ 设置文件所有者: www-data\n";
}

echo "\n[6/8] 配置数据库...\n";

// 生成数据库配置
$dbConfig = "<?php
/**
 * 数据库配置 (自动生成)
 */
return [
    'class' => 'yii\\db\\Connection',
    'dsn' => 'mysql:host=localhost;dbname={$deployConfig['db_name']}',
    'username' => '{$deployConfig['db_user']}',
    'password' => '{$deployConfig['db_pass']}',
    'charset' => 'utf8mb4',
    
    // Schema cache options (for production environment)
    'enableSchemaCache' => true,
    'schemaCacheDuration' => 60,
    'schemaCache' => 'cache',
];";

file_put_contents('config/db.php', $dbConfig);
echo "  ✅ 生成数据库配置文件\n";

// 创建数据库和用户
$mysqlCommands = [
    "CREATE DATABASE IF NOT EXISTS {$deployConfig['db_name']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;",
    "CREATE USER IF NOT EXISTS '{$deployConfig['db_user']}'@'localhost' IDENTIFIED BY '{$deployConfig['db_pass']}';",
    "GRANT ALL PRIVILEGES ON {$deployConfig['db_name']}.* TO '{$deployConfig['db_user']}'@'localhost';",
    "FLUSH PRIVILEGES;"
];

foreach ($mysqlCommands as $sql) {
    $result = shell_exec("mysql -e \"{$sql}\" 2>&1");
    if (strpos($result, 'ERROR') === false) {
        echo "  ✅ 执行SQL: " . substr($sql, 0, 50) . "...\n";
    } else {
        echo "  ⚠️ SQL执行可能有问题: " . substr($sql, 0, 30) . "...\n";
    }
}

echo "\n[7/8] 配置Web服务器...\n";

// 检测Web服务器
$webServer = 'unknown';
if (shell_exec('which apache2 2>/dev/null')) {
    $webServer = 'apache';
} elseif (shell_exec('which nginx 2>/dev/null')) {
    $webServer = 'nginx';
}

echo "  📋 检测到Web服务器: {$webServer}\n";

if ($webServer === 'apache') {
    // Apache配置
    $apacheConfig = "<VirtualHost *:80>
    ServerName {$deployConfig['domain']}
    DocumentRoot {$projectPath}/web
    
    <Directory {$projectPath}/web>
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . index.php
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/{$deployConfig['project_name']}_error.log
    CustomLog \${APACHE_LOG_DIR}/{$deployConfig['project_name']}_access.log combined
</VirtualHost>";

    $configFile = "/etc/apache2/sites-available/{$deployConfig['project_name']}.conf";
    file_put_contents($configFile, $apacheConfig);
    
    // 启用站点
    shell_exec("a2ensite {$deployConfig['project_name']}.conf 2>&1");
    shell_exec("systemctl reload apache2 2>&1");
    
    echo "  ✅ Apache虚拟主机配置完成\n";
    
} elseif ($webServer === 'nginx') {
    // Nginx配置
    $nginxConfig = "server {
    listen 80;
    server_name {$deployConfig['domain']};
    root {$projectPath}/web;
    index index.php;
    
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }
    
    access_log /var/log/nginx/{$deployConfig['project_name']}_access.log;
    error_log /var/log/nginx/{$deployConfig['project_name']}_error.log;
}";

    $configFile = "/etc/nginx/sites-available/{$deployConfig['project_name']}";
    file_put_contents($configFile, $nginxConfig);
    
    // 启用站点
    $symlinkTarget = "/etc/nginx/sites-enabled/{$deployConfig['project_name']}";
    if (!file_exists($symlinkTarget)) {
        symlink($configFile, $symlinkTarget);
    }
    shell_exec("nginx -t && systemctl reload nginx 2>&1");
    
    echo "  ✅ Nginx服务器配置完成\n";
}

echo "\n[8/8] 运行数据库迁移...\n";

// 运行迁移
$migrateOutput = shell_exec('php yii migrate --interactive=0 2>&1');
if (strpos($migrateOutput, 'Migrated up successfully') !== false || strpos($migrateOutput, 'No new migrations found') !== false) {
    echo "  ✅ 数据库迁移完成\n";
} else {
    echo "  ⚠️ 数据库迁移可能有问题\n";
    echo "  输出: " . substr($migrateOutput, 0, 200) . "...\n";
}

// 恢复原目录
chdir($originalDir);

echo "\n========================================\n";
echo "🎉 部署完成！\n";
echo "========================================\n\n";

// 生成部署报告
$deployReport = [
    'deploy_time' => date('Y-m-d H:i:s'),
    'project_path' => $projectPath,
    'domain' => $deployConfig['domain'],
    'database' => [
        'name' => $deployConfig['db_name'],
        'user' => $deployConfig['db_user'],
        'password' => $deployConfig['db_pass']
    ],
    'web_server' => $webServer,
    'status' => '✅ 部署成功'
];

file_put_contents('deploy-report.json', json_encode($deployReport, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "📊 部署信息:\n";
echo "✅ 项目路径: {$projectPath}\n";
echo "✅ 访问域名: http://{$deployConfig['domain']}\n";
echo "✅ 数据库名: {$deployConfig['db_name']}\n";
echo "✅ 数据库用户: {$deployConfig['db_user']}\n";
echo "✅ 数据库密码: {$deployConfig['db_pass']}\n";
echo "✅ Web服务器: {$webServer}\n\n";

echo "🌐 访问地址:\n";
echo "- 前端首页: http://{$deployConfig['domain']}\n";
echo "- 管理后台: http://{$deployConfig['domain']}/admin\n";
echo "- API接口: http://{$deployConfig['domain']}/api\n\n";

echo "🔧 管理命令:\n";
echo "- 控制台: cd {$projectPath} && php yii help\n";
echo "- 清除缓存: cd {$projectPath} && php yii cache/flush-all\n";
echo "- 查看日志: tail -f {$projectPath}/runtime/logs/app.log\n\n";

echo "📝 重要提醒:\n";
echo "1. 请将域名 {$deployConfig['domain']} 添加到 /etc/hosts 或DNS解析\n";
echo "2. 数据库密码已保存到 deploy-report.json\n";
echo "3. 建议修改默认密码和安全配置\n";
echo "4. 生产环境请关闭调试模式\n\n";

echo "🎯 SaiAdmin Yii 2.0 部署成功！\n";
