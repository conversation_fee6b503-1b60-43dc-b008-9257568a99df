import{c as v}from"./config-ZWMRzLkl.js";import{M as B}from"./@arco-design-uttiljWv.js";import{r as d,a as q,h as p,j as D,k as F,l as s,t as a}from"./@vue-9ZIPiVZG.js";import"./index-ybrmzYq5.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const Oe={__name:"add-group",emits:["success"],setup(G,{expose:_,emit:g}){const n=d(!1),u=d(!1),f=d(""),c=d(),k=g,b=(t="add")=>{f.value=t,n.value=!0,c.value.resetFields()},o=q({id:"",name:"",code:"",remark:""}),V={name:[{required:!0,message:"组名称不能为空"}],code:[{required:!0,message:"组标识不能为空"}]},x=async t=>{for(const e in o)t[e]!=null&&t[e]!=null&&(o[e]=t[e])},w=async t=>{var m;if(!await((m=c.value)==null?void 0:m.validate())){u.value=!0;let r={...o},l={};f.value==="add"?(r.id=void 0,l=await v.saveConfigGroup(r)):l=await v.updateConfigGroup(r.id,r),l.code===200&&(B.success("操作成功"),k("success"),t(!0)),setTimeout(()=>{u.value=!1},500)}t(!1)},C=()=>n.value=!1;return _({open:b,setFormData:x}),(t,e)=>{const m=p("a-input"),r=p("a-form-item"),l=p("a-textarea"),y=p("a-form"),U=p("a-modal");return F(),D(U,{visible:n.value,"onUpdate:visible":e[3]||(e[3]=i=>n.value=i),title:"配置组"+(f.value=="add"?"-新增":"-编辑"),draggable:"",width:"600px","ok-loading":u.value,onCancel:C,onBeforeOk:w},{default:s(()=>[a(y,{ref_key:"formRef",ref:c,model:o,rules:V,"auto-label-width":!0},{default:s(()=>[a(r,{label:"组名称（中文）",field:"name"},{default:s(()=>[a(m,{modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=i=>o.name=i),placeholder:"请输入组名称"},null,8,["modelValue"])]),_:1}),a(r,{label:"组标识（英文）",field:"code"},{default:s(()=>[a(m,{modelValue:o.code,"onUpdate:modelValue":e[1]||(e[1]=i=>o.code=i),placeholder:"请输入组标识"},null,8,["modelValue"])]),_:1}),a(r,{label:"备注",field:"remark"},{default:s(()=>[a(l,{modelValue:o.remark,"onUpdate:modelValue":e[2]||(e[2]=i=>o.remark=i),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","title","ok-loading"])}}};export{Oe as default};
