<?php
/**
 * 用户管理控制器 - Yii 2.0 实战示例
 */
namespace app\controllers;

use Yii;
use app\models\User;
use app\components\base\BaseController;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;

class UserController extends BaseController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return array_merge(parent::behaviors(), [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ]);
    }

    /**
     * 用户列表
     */
    public function actionIndex()
    {
        $dataProvider = new ActiveDataProvider([
            'query' => User::find(),
            'pagination' => [
                'pageSize' => 20,
            ],
            'sort' => [
                'defaultOrder' => [
                    'id' => SORT_DESC,
                ]
            ],
        ]);

        if (Yii::$app->request->isAjax) {
            return $this->success([
                'list' => $dataProvider->getModels(),
                'pagination' => [
                    'total' => $dataProvider->getTotalCount(),
                    'page' => $dataProvider->getPagination()->getPage() + 1,
                    'pageSize' => $dataProvider->getPagination()->getPageSize(),
                ]
            ]);
        }

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * 创建用户
     */
    public function actionCreate()
    {
        $model = new User();
        $model->scenario = 'create';

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash('success', '用户创建成功');
                
                if (Yii::$app->request->isAjax) {
                    return $this->success($model->toArray(), '用户创建成功');
                }
                
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail('用户创建失败', 400, $model->errors);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * 查看用户详情
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        
        if (Yii::$app->request->isAjax) {
            return $this->success($model->toArray());
        }
        
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * 更新用户
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->scenario = 'update';

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                Yii::$app->session->setFlash('success', '用户更新成功');
                
                if (Yii::$app->request->isAjax) {
                    return $this->success($model->toArray(), '用户更新成功');
                }
                
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        if (Yii::$app->request->isAjax) {
            return $this->fail('用户更新失败', 400, $model->errors);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * 删除用户
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            Yii::$app->session->setFlash('success', '用户删除成功');
            
            if (Yii::$app->request->isAjax) {
                return $this->success([], '用户删除成功');
            }
        } else {
            if (Yii::$app->request->isAjax) {
                return $this->fail('用户删除失败');
            }
        }

        return $this->redirect(['index']);
    }

    /**
     * 批量删除
     */
    public function actionBatchDelete()
    {
        $ids = Yii::$app->request->post('ids', []);
        
        if (empty($ids)) {
            return $this->fail('请选择要删除的用户');
        }
        
        $count = User::deleteAll(['id' => $ids]);
        
        return $this->success([], "成功删除 {$count} 个用户");
    }

    /**
     * 查找用户模型
     */
    protected function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('请求的用户不存在');
    }
}
