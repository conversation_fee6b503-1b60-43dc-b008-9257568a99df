import{g as r}from"./index-DkGLNqVb.js";const s={getPageList(e={}){return r({url:"/core/user/index",method:"get",params:e})},read(e){return r({url:"/core/user/read?id="+e,method:"get"})},save(e={}){return r({url:"/core/user/save",method:"post",data:e})},destroy(e){return r({url:"/core/user/destroy",method:"delete",data:e})},update(e,t={}){return r({url:"/core/user/update?id="+e,method:"put",data:t})},changeStatus(e={}){return r({url:"/core/user/changeStatus",method:"post",data:e})},clearCache(e={}){return r({url:"/core/user/clearCache",method:"post",data:e})},setHomePage(e={}){return r({url:"/core/user/setHomePage",method:"post",data:e})},initUserPassword(e){return r({url:"/core/user/initUserPassword",method:"post",data:e})},updateInfo(e={}){return r({url:"/core/user/updateInfo",method:"post",data:e})},modifyPassword(e={}){return r({url:"/core/user/modifyPassword",method:"post",data:e})}};export{s as a};
