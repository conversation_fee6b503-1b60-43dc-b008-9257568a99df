<?php
/**
 * 用户服务类 - 业务逻辑层
 */
namespace app\components\services;

use Yii;
use app\models\User;
use yii\base\Component;

/**
 * 用户服务
 */
class UserService extends Component
{
    /**
     * 创建用户
     */
    public function createUser($userData)
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            $user = new User();
            $user->scenario = 'create';
            $user->load($userData, '');
            
            if (!$user->save()) {
                throw new \Exception('用户创建失败: ' . implode(', ', $user->getFirstErrors()));
            }
            
            // 生成认证密钥
            $user->generateAuthKey();
            $user->save(false);
            
            // 记录日志
            Yii::info('用户创建成功: ' . $user->username, 'user');
            
            $transaction->commit();
            return $user;
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 更新用户
     */
    public function updateUser($id, $userData)
    {
        $user = User::findOne($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }
        
        $user->scenario = 'update';
        $user->load($userData, '');
        
        if (!$user->save()) {
            throw new \Exception('用户更新失败: ' . implode(', ', $user->getFirstErrors()));
        }
        
        Yii::info('用户更新成功: ' . $user->username, 'user');
        return $user;
    }

    /**
     * 删除用户
     */
    public function deleteUser($id)
    {
        $user = User::findOne($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }
        
        // 软删除
        $user->status = User::STATUS_DELETED;
        if (!$user->save(false)) {
            throw new \Exception('用户删除失败');
        }
        
        Yii::info('用户删除成功: ' . $user->username, 'user');
        return true;
    }

    /**
     * 批量操作
     */
    public function batchOperation($ids, $operation, $data = [])
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            $count = 0;
            
            foreach ($ids as $id) {
                switch ($operation) {
                    case 'delete':
                        $this->deleteUser($id);
                        $count++;
                        break;
                        
                    case 'activate':
                        $user = User::findOne($id);
                        if ($user) {
                            $user->status = User::STATUS_ACTIVE;
                            $user->save(false);
                            $count++;
                        }
                        break;
                        
                    case 'deactivate':
                        $user = User::findOne($id);
                        if ($user) {
                            $user->status = User::STATUS_INACTIVE;
                            $user->save(false);
                            $count++;
                        }
                        break;
                }
            }
            
            $transaction->commit();
            return $count;
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 用户统计
     */
    public function getUserStats()
    {
        return [
            'total' => User::find()->count(),
            'active' => User::find()->where(['status' => User::STATUS_ACTIVE])->count(),
            'inactive' => User::find()->where(['status' => User::STATUS_INACTIVE])->count(),
            'recent_login' => User::find()->where(['>=', 'last_login_at', date('Y-m-d', strtotime('-7 days'))])->count(),
        ];
    }

    /**
     * 搜索用户
     */
    public function searchUsers($params)
    {
        $query = User::find();
        
        if (!empty($params['username'])) {
            $query->andWhere(['like', 'username', $params['username']]);
        }
        
        if (!empty($params['email'])) {
            $query->andWhere(['like', 'email', $params['email']]);
        }
        
        if (isset($params['status'])) {
            $query->andWhere(['status' => $params['status']]);
        }
        
        if (!empty($params['date_from'])) {
            $query->andWhere(['>=', 'created_at', $params['date_from']]);
        }
        
        if (!empty($params['date_to'])) {
            $query->andWhere(['<=', 'created_at', $params['date_to']]);
        }
        
        return $query;
    }
}
