<?php
/**
 * 主布局文件
 */
use yii\helpers\Html;
use yii\bootstrap4\Nav;
use yii\bootstrap4\NavBar;
use yii\widgets\Breadcrumbs;
use app\assets\AppAsset;

AppAsset::register($this);
?>
<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">
<head>
    <meta charset="<?= Yii::$app->charset ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php $this->registerCsrfMetaTags() ?>
    <title><?= Html::encode($this->title) ?></title>
    <?php $this->head() ?>
</head>
<body>
<?php $this->beginBody() ?>

<div class="wrap">
    <?php
    NavBar::begin([
        "brandLabel" => Yii::$app->name,
        "brandUrl" => Yii::$app->homeUrl,
        "options" => [
            "class" => "navbar-expand-md navbar-dark bg-dark fixed-top",
        ],
    ]);
    echo Nav::widget([
        "options" => ["class" => "navbar-nav"],
        "items" => [
            ["label" => "首页", "url" => ["/site/index"]],
            ["label" => "关于", "url" => ["/site/about"]],
            ["label" => "联系", "url" => ["/site/contact"]],
            Yii::$app->user->isGuest ? (
                ["label" => "登录", "url" => ["/site/login"]]
            ) : (
                "<li>"
                . Html::beginForm(["/site/logout"], "post")
                . Html::submitButton(
                    "退出 (" . Yii::$app->user->identity->username . ")",
                    ["class" => "btn btn-link logout"]
                )
                . Html::endForm()
                . "</li>"
            )
        ],
    ]);
    NavBar::end();
    ?>

    <div class="container">
        <?= Breadcrumbs::widget([
            "links" => isset($this->params["breadcrumbs"]) ? $this->params["breadcrumbs"] : [],
        ]) ?>
        <?= $content ?>
    </div>
</div>

<footer class="footer">
    <div class="container">
        <p class="pull-left">&copy; SaiAdmin <?= date("Y") ?></p>
        <p class="pull-right"><?= Yii::powered() ?></p>
    </div>
</footer>

<?php $this->endBody() ?>
</body>
</html>
<?php $this->endPage() ?>