<?php
/**
 * SaiAdmin Yii 2.0 开发入门脚本
 */

echo "👨‍💻 SaiAdmin Yii 2.0 开发入门\n";
echo "========================================\n\n";

$projectPath = 'yii2-saiadmin';

echo "[1/6] 创建第一个控制器...\n";

// 切换到项目目录
$originalDir = getcwd();
chdir($projectPath);

// 创建示例控制器
$demoControllerContent = '<?php
/**
 * 演示控制器 - Yii 2.0 风格
 */
namespace app\controllers;

use Yii;
use app\components\base\BaseController;
use yii\web\Response;

/**
 * 演示控制器
 */
class DemoController extends BaseController
{
    /**
     * 演示首页
     */
    public function actionIndex()
    {
        return $this->render("index", [
            "title" => "SaiAdmin Yii 2.0 演示",
            "message" => "欢迎使用 SaiAdmin Yii 2.0 框架！",
            "features" => [
                "完全兼容 Yii 2.0",
                "模块化架构设计", 
                "丰富的组件系统",
                "强大的代码生成器",
                "完善的调试工具"
            ]
        ]);
    }
    
    /**
     * API演示
     */
    public function actionApi()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        return $this->success([
            "framework" => "SaiAdmin Yii 2.0",
            "version" => Yii::getVersion(),
            "php_version" => PHP_VERSION,
            "timestamp" => time(),
            "features" => [
                "RESTful API",
                "JSON响应格式",
                "统一错误处理",
                "请求验证",
                "响应缓存"
            ]
        ]);
    }
    
    /**
     * 表单演示
     */
    public function actionForm()
    {
        $model = new \yii\base\DynamicModel(["name", "email", "message"]);
        $model->addRule(["name", "email"], "required")
              ->addRule("email", "email")
              ->addRule("message", "string", ["max" => 500]);
        
        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            Yii::$app->session->setFlash("success", "表单提交成功！");
            return $this->refresh();
        }
        
        return $this->render("form", [
            "model" => $model
        ]);
    }
    
    /**
     * 数据库演示
     */
    public function actionDatabase()
    {
        try {
            $db = Yii::$app->db;
            $tables = $db->createCommand("SHOW TABLES")->queryAll();
            
            return $this->render("database", [
                "connection" => [
                    "driver" => $db->driverName,
                    "dsn" => $db->dsn,
                    "status" => "连接成功"
                ],
                "tables" => $tables
            ]);
        } catch (Exception $e) {
            return $this->render("database", [
                "connection" => [
                    "status" => "连接失败",
                    "error" => $e->getMessage()
                ],
                "tables" => []
            ]);
        }
    }
    
    /**
     * 缓存演示
     */
    public function actionCache()
    {
        $cache = Yii::$app->cache;
        $key = "demo_cache_" . date("Y-m-d");
        
        $data = $cache->get($key);
        if ($data === false) {
            $data = [
                "generated_at" => date("Y-m-d H:i:s"),
                "random_number" => rand(1000, 9999),
                "cache_key" => $key
            ];
            $cache->set($key, $data, 3600); // 缓存1小时
        }
        
        return $this->render("cache", [
            "cache_data" => $data,
            "cache_info" => [
                "class" => get_class($cache),
                "key" => $key,
                "ttl" => "3600秒"
            ]
        ]);
    }
}';

file_put_contents('controllers/DemoController.php', $demoControllerContent);
echo "  ✅ 创建演示控制器: controllers/DemoController.php\n";

echo "\n[2/6] 创建视图文件...\n";

// 创建视图目录
if (!is_dir('views/demo')) {
    mkdir('views/demo', 0755, true);
}

// 创建首页视图
$indexView = '<?php
/**
 * 演示首页视图
 */
use yii\helpers\Html;

$this->title = $title;
?>

<div class="demo-index">
    <div class="jumbotron">
        <h1 class="display-4"><?= Html::encode($title) ?></h1>
        <p class="lead"><?= Html::encode($message) ?></p>
        <hr class="my-4">
        <p>这是一个基于 Yii 2.0 框架的 SaiAdmin 演示页面。</p>
        <?= Html::a("开始探索", ["demo/api"], ["class" => "btn btn-primary btn-lg"]) ?>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <h3>🚀 框架特性</h3>
            <ul class="list-group">
                <?php foreach ($features as $feature): ?>
                    <li class="list-group-item"><?= Html::encode($feature) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <div class="col-md-6">
            <h3>🔗 快速链接</h3>
            <div class="list-group">
                <?= Html::a("API演示", ["demo/api"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("表单演示", ["demo/form"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("数据库演示", ["demo/database"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("缓存演示", ["demo/cache"], ["class" => "list-group-item list-group-item-action"]) ?>
                <?= Html::a("Gii代码生成", ["/gii"], ["class" => "list-group-item list-group-item-action", "target" => "_blank"]) ?>
                <?= Html::a("调试工具", ["/debug"], ["class" => "list-group-item list-group-item-action", "target" => "_blank"]) ?>
            </div>
        </div>
    </div>
</div>';

file_put_contents('views/demo/index.php', $indexView);
echo "  ✅ 创建首页视图: views/demo/index.php\n";

// 创建表单视图
$formView = '<?php
/**
 * 表单演示视图
 */
use yii\helpers\Html;
use yii\widgets\ActiveForm;

$this->title = "表单演示";
?>

<div class="demo-form">
    <h1><?= Html::encode($this->title) ?></h1>
    
    <?php if (Yii::$app->session->hasFlash("success")): ?>
        <div class="alert alert-success">
            <?= Yii::$app->session->getFlash("success") ?>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-6">
            <?php $form = ActiveForm::begin(); ?>
            
            <?= $form->field($model, "name")->textInput(["placeholder" => "请输入姓名"]) ?>
            
            <?= $form->field($model, "email")->textInput(["placeholder" => "请输入邮箱"]) ?>
            
            <?= $form->field($model, "message")->textarea(["rows" => 4, "placeholder" => "请输入消息内容"]) ?>
            
            <div class="form-group">
                <?= Html::submitButton("提交", ["class" => "btn btn-primary"]) ?>
                <?= Html::a("返回", ["demo/index"], ["class" => "btn btn-secondary"]) ?>
            </div>
            
            <?php ActiveForm::end(); ?>
        </div>
        
        <div class="col-md-6">
            <h4>📝 表单特性</h4>
            <ul>
                <li>客户端验证</li>
                <li>服务端验证</li>
                <li>CSRF保护</li>
                <li>Flash消息</li>
                <li>Bootstrap样式</li>
            </ul>
        </div>
    </div>
</div>';

file_put_contents('views/demo/form.php', $formView);
echo "  ✅ 创建表单视图: views/demo/form.php\n";

// 创建数据库视图
$databaseView = '<?php
/**
 * 数据库演示视图
 */
use yii\helpers\Html;

$this->title = "数据库演示";
?>

<div class="demo-database">
    <h1><?= Html::encode($this->title) ?></h1>
    
    <div class="row">
        <div class="col-md-6">
            <h4>🗄️ 连接信息</h4>
            <table class="table table-bordered">
                <?php foreach ($connection as $key => $value): ?>
                    <tr>
                        <td><strong><?= Html::encode($key) ?></strong></td>
                        <td><?= Html::encode($value) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="col-md-6">
            <h4>📋 数据库表</h4>
            <?php if (!empty($tables)): ?>
                <ul class="list-group">
                    <?php foreach ($tables as $table): ?>
                        <li class="list-group-item"><?= Html::encode(array_values($table)[0]) ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p class="text-muted">暂无数据表或连接失败</p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="mt-3">
        <?= Html::a("返回", ["demo/index"], ["class" => "btn btn-secondary"]) ?>
    </div>
</div>';

file_put_contents('views/demo/database.php', $databaseView);
echo "  ✅ 创建数据库视图: views/demo/database.php\n";

// 创建缓存视图
$cacheView = '<?php
/**
 * 缓存演示视图
 */
use yii\helpers\Html;

$this->title = "缓存演示";
?>

<div class="demo-cache">
    <h1><?= Html::encode($this->title) ?></h1>
    
    <div class="row">
        <div class="col-md-6">
            <h4>💾 缓存数据</h4>
            <table class="table table-bordered">
                <?php foreach ($cache_data as $key => $value): ?>
                    <tr>
                        <td><strong><?= Html::encode($key) ?></strong></td>
                        <td><?= Html::encode($value) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="col-md-6">
            <h4>ℹ️ 缓存信息</h4>
            <table class="table table-bordered">
                <?php foreach ($cache_info as $key => $value): ?>
                    <tr>
                        <td><strong><?= Html::encode($key) ?></strong></td>
                        <td><?= Html::encode($value) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    </div>
    
    <div class="alert alert-info">
        <strong>提示:</strong> 刷新页面查看缓存效果，数据在缓存有效期内不会改变。
    </div>
    
    <div class="mt-3">
        <?= Html::a("刷新页面", ["demo/cache"], ["class" => "btn btn-primary"]) ?>
        <?= Html::a("返回", ["demo/index"], ["class" => "btn btn-secondary"]) ?>
    </div>
</div>';

file_put_contents('views/demo/cache.php', $cacheView);
echo "  ✅ 创建缓存视图: views/demo/cache.php\n";

echo "\n[3/6] 创建示例模型...\n";

// 创建示例模型
$demoModelContent = '<?php
/**
 * 演示模型
 */
namespace app\models;

use app\components\base\BaseModel;

/**
 * Demo 模型
 */
class Demo extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return "{{%demo}}";
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [["title"], "required"],
            [["content"], "string"],
            [["status"], "integer"],
            [["title"], "string", "max" => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            "id" => "ID",
            "title" => "标题",
            "content" => "内容",
            "status" => "状态",
            "created_at" => "创建时间",
            "updated_at" => "更新时间",
        ];
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusText()
    {
        $statusMap = [
            0 => "禁用",
            1 => "启用"
        ];
        
        return isset($statusMap[$this->status]) ? $statusMap[$this->status] : "未知";
    }
}';

file_put_contents('models/Demo.php', $demoModelContent);
echo "  ✅ 创建演示模型: models/Demo.php\n";

echo "\n[4/6] 创建数据库迁移...\n";

// 创建迁移文件
$migrationContent = '<?php
/**
 * 演示表迁移
 */
use yii\db\Migration;

class m' . date('ymd_His') . '_create_demo_table extends Migration
{
    public function safeUp()
    {
        $this->createTable("{{%demo}}", [
            "id" => $this->primaryKey(),
            "title" => $this->string()->notNull()->comment("标题"),
            "content" => $this->text()->comment("内容"),
            "status" => $this->smallInteger()->defaultValue(1)->comment("状态"),
            "created_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP")->comment("创建时间"),
            "updated_at" => $this->timestamp()->defaultExpression("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")->comment("更新时间"),
        ], "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\"演示表\"");
        
        // 插入示例数据
        $this->insert("{{%demo}}", [
            "title" => "欢迎使用 SaiAdmin Yii 2.0",
            "content" => "这是一个演示数据，展示了 Yii 2.0 的数据库操作功能。",
            "status" => 1
        ]);
        
        $this->insert("{{%demo}}", [
            "title" => "强大的 ActiveRecord",
            "content" => "Yii 2.0 提供了强大的 ActiveRecord ORM，让数据库操作变得简单。",
            "status" => 1
        ]);
    }

    public function safeDown()
    {
        $this->dropTable("{{%demo}}");
    }
}';

$migrationFile = 'migrations/m' . date('ymd_His') . '_create_demo_table.php';
file_put_contents($migrationFile, $migrationContent);
echo "  ✅ 创建数据库迁移: {$migrationFile}\n";

echo "\n[5/6] 创建开发指南...\n";

$devGuide = '# SaiAdmin Yii 2.0 开发指南

## 🎯 快速开始

### 访问演示页面
- 演示首页: http://localhost:8080/demo
- API演示: http://localhost:8080/demo/api
- 表单演示: http://localhost:8080/demo/form
- 数据库演示: http://localhost:8080/demo/database
- 缓存演示: http://localhost:8080/demo/cache

### 开发工具
- Gii代码生成器: http://localhost:8080/gii
- 调试工具栏: http://localhost:8080/debug

## 🔧 常用命令

### 数据库迁移
```bash
# 运行迁移
php yii migrate

# 创建新迁移
php yii migrate/create create_table_name

# 查看迁移历史
php yii migrate/history
```

### 代码生成
```bash
# 生成模型
php yii gii/model --tableName=demo --modelClass=Demo

# 生成CRUD
php yii gii/crud --modelClass="app\\models\\Demo" --controllerClass="app\\controllers\\DemoController"

# 生成控制器
php yii gii/controller --controller=demo
```

### 缓存管理
```bash
# 清除所有缓存
php yii cache/flush-all

# 清除指定缓存
php yii cache/flush cache1 cache2
```

## 📁 项目结构

```
controllers/     # 控制器
├── DemoController.php

models/         # 模型
├── Demo.php

views/          # 视图
├── demo/
│   ├── index.php
│   ├── form.php
│   ├── database.php
│   └── cache.php

migrations/     # 数据库迁移
├── m*_create_demo_table.php
```

## 🎨 开发模式

### MVC模式
- **Model**: 数据模型和业务逻辑
- **View**: 用户界面和展示逻辑
- **Controller**: 请求处理和流程控制

### 组件系统
- **Application**: 应用主体
- **Component**: 可配置的功能组件
- **Behavior**: 行为扩展
- **Event**: 事件系统

## 📖 学习资源

- [Yii 2.0 权威指南](https://www.yiiframework.com/doc/guide/2.0/zh-cn)
- [Yii 2.0 API文档](https://www.yiiframework.com/doc/api/2.0)
- [SaiAdmin文档](https://saiadmin.com/docs)

## 🚀 下一步

1. 运行数据库迁移: `php yii migrate`
2. 访问演示页面: http://localhost:8080/demo
3. 使用Gii生成代码: http://localhost:8080/gii
4. 开始您的项目开发！
';

file_put_contents('DEVELOPMENT-GUIDE.md', $devGuide);
echo "  ✅ 创建开发指南: DEVELOPMENT-GUIDE.md\n";

echo "\n[6/6] 生成快速测试脚本...\n";

$testScript = '#!/bin/bash
# SaiAdmin Yii 2.0 快速测试脚本

echo "🧪 SaiAdmin Yii 2.0 快速测试"
echo "================================"

# 测试控制台
echo "📋 测试控制台命令..."
php yii help > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 控制台命令正常"
else
    echo "❌ 控制台命令异常"
fi

# 测试Web访问
echo "🌐 测试Web访问..."
if command -v curl &> /dev/null; then
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/demo)
    if [ "$response" = "200" ]; then
        echo "✅ Web访问正常"
    else
        echo "⚠️ Web访问异常 (HTTP $response)"
    fi
else
    echo "⚠️ curl命令不可用，跳过Web测试"
fi

# 测试API
echo "🔌 测试API接口..."
if command -v curl &> /dev/null; then
    api_response=$(curl -s http://localhost:8080/demo/api | head -1)
    if [[ "$api_response" == *"code"* ]]; then
        echo "✅ API接口正常"
    else
        echo "⚠️ API接口异常"
    fi
fi

echo ""
echo "🎯 测试完成！"
echo "📖 查看开发指南: cat DEVELOPMENT-GUIDE.md"
echo "🌐 访问演示页面: http://localhost:8080/demo"';

file_put_contents('quick-test.sh', $testScript);
chmod('quick-test.sh', 0755);
echo "  ✅ 创建快速测试脚本: quick-test.sh\n";

// 恢复原目录
chdir($originalDir);

echo "\n========================================\n";
echo "👨‍💻 开发入门完成！\n";
echo "========================================\n\n";

echo "🎯 立即体验:\n";
echo "1. 访问演示首页: http://localhost:8080/demo\n";
echo "2. 查看API演示: http://localhost:8080/demo/api\n";
echo "3. 体验表单功能: http://localhost:8080/demo/form\n";
echo "4. 使用Gii生成器: http://localhost:8080/gii\n\n";

echo "📁 创建的文件:\n";
echo "✅ controllers/DemoController.php - 演示控制器\n";
echo "✅ views/demo/*.php - 演示视图文件\n";
echo "✅ models/Demo.php - 演示模型\n";
echo "✅ migrations/m*_create_demo_table.php - 数据库迁移\n";
echo "✅ DEVELOPMENT-GUIDE.md - 开发指南\n";
echo "✅ quick-test.sh - 快速测试脚本\n\n";

echo "🔧 下一步操作:\n";
echo "1. cd {$projectPath}\n";
echo "2. php yii migrate (运行数据库迁移)\n";
echo "3. 访问 http://localhost:8080/demo\n";
echo "4. 开始您的 Yii 2.0 开发之旅！\n\n";

echo "🎉 SaiAdmin Yii 2.0 开发环境完全就绪！\n";
