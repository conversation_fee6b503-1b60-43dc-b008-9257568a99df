<?php
/**
 * SaiAdmin 项目结构优化为 Yii 2.0 兼容结构
 * 参考: https://www.yiiframework.com/doc/api/2.0
 */

echo "🔄 SaiAdmin 项目结构优化为 Yii 2.0 兼容\n";
echo "========================================\n\n";

// Yii 2.0 标准目录结构
$yii2Structure = [
    'assets' => '资源文件目录',
    'commands' => '控制台命令',
    'components' => '应用组件',
    'config' => '配置文件',
    'controllers' => '控制器',
    'models' => '模型',
    'modules' => '模块',
    'runtime' => '运行时文件',
    'views' => '视图文件',
    'web' => 'Web入口目录',
    'widgets' => '小部件',
    'vendor' => '第三方包',
    'tests' => '测试文件',
    'migrations' => '数据库迁移',
    'mail' => '邮件模板',
    'rbac' => 'RBAC权限文件'
];

echo "[1/6] 分析当前项目结构...\n";

// 当前项目结构映射
$currentStructure = [
    'webman/app/controller' => 'controllers',
    'webman/plugin/saiadmin/app/model' => 'models',
    'webman/plugin/saiadmin/app/logic' => 'components/services',
    'webman/plugin/saiadmin/app/validate' => 'components/validators',
    'webman/config' => 'config',
    'webman/runtime' => 'runtime',
    'webman/public' => 'web',
    'webman/vendor' => 'vendor',
    'webman/plugin/saiadmin/app/controller' => 'modules/admin/controllers',
    'webman/plugin/saiadmin/app/middleware' => 'components/filters',
    'webman/plugin/saiadmin/basic' => 'components/base'
];

foreach ($currentStructure as $current => $target) {
    if (is_dir($current)) {
        echo "  ✅ 发现: {$current} -> {$target}\n";
    } else {
        echo "  ⚠️ 缺失: {$current}\n";
    }
}

echo "\n[2/6] 创建 Yii 2.0 兼容目录结构...\n";

// 创建 Yii 2.0 标准目录
$yii2Dirs = [
    'yii2-saiadmin/assets',
    'yii2-saiadmin/commands',
    'yii2-saiadmin/components/base',
    'yii2-saiadmin/components/services',
    'yii2-saiadmin/components/validators',
    'yii2-saiadmin/components/filters',
    'yii2-saiadmin/components/widgets',
    'yii2-saiadmin/config',
    'yii2-saiadmin/controllers',
    'yii2-saiadmin/models',
    'yii2-saiadmin/modules/admin/controllers',
    'yii2-saiadmin/modules/admin/models',
    'yii2-saiadmin/modules/admin/views',
    'yii2-saiadmin/modules/tool/controllers',
    'yii2-saiadmin/modules/tool/models',
    'yii2-saiadmin/modules/tool/views',
    'yii2-saiadmin/runtime/cache',
    'yii2-saiadmin/runtime/logs',
    'yii2-saiadmin/runtime/sessions',
    'yii2-saiadmin/views/layouts',
    'yii2-saiadmin/views/site',
    'yii2-saiadmin/web/assets',
    'yii2-saiadmin/web/css',
    'yii2-saiadmin/web/js',
    'yii2-saiadmin/web/images',
    'yii2-saiadmin/widgets',
    'yii2-saiadmin/migrations',
    'yii2-saiadmin/mail/layouts',
    'yii2-saiadmin/mail/templates',
    'yii2-saiadmin/rbac',
    'yii2-saiadmin/tests/unit',
    'yii2-saiadmin/tests/functional'
];

foreach ($yii2Dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "  ✅ 创建目录: {$dir}\n";
    }
}

echo "\n[3/6] 创建 Yii 2.0 应用入口文件...\n";

// 创建 index.php (Web入口)
$indexContent = '<?php
/**
 * SaiAdmin Yii 2.0 兼容版本 - Web入口文件
 */

// 定义应用环境
defined("YII_DEBUG") or define("YII_DEBUG", true);
defined("YII_ENV") or define("YII_ENV", "dev");

// 加载 Composer 自动加载器
require __DIR__ . "/../vendor/autoload.php";

// 加载 Yii 类文件
require __DIR__ . "/../vendor/yiisoft/yii2/Yii.php";

// 加载应用配置
$config = require __DIR__ . "/../config/web.php";

// 创建并运行应用
(new yii\web\Application($config))->run();';

file_put_contents('yii2-saiadmin/web/index.php', $indexContent);
echo "  ✅ 创建 Web 入口文件: web/index.php\n";

// 创建控制台入口文件
$yiiContent = '#!/usr/bin/env php
<?php
/**
 * SaiAdmin Yii 2.0 兼容版本 - 控制台入口文件
 */

defined("YII_DEBUG") or define("YII_DEBUG", true);
defined("YII_ENV") or define("YII_ENV", "dev");

require __DIR__ . "/vendor/autoload.php";
require __DIR__ . "/vendor/yiisoft/yii2/Yii.php";

$config = require __DIR__ . "/config/console.php";

$application = new yii\console\Application($config);
$exitCode = $application->run();
exit($exitCode);';

file_put_contents('yii2-saiadmin/yii', $yiiContent);
chmod('yii2-saiadmin/yii', 0755);
echo "  ✅ 创建控制台入口文件: yii\n";

echo "\n[4/6] 创建 Yii 2.0 配置文件...\n";

// Web 应用配置
$webConfig = '<?php
/**
 * SaiAdmin Yii 2.0 Web 应用配置
 */

$params = require __DIR__ . "/params.php";
$db = require __DIR__ . "/db.php";

$config = [
    "id" => "saiadmin-web",
    "name" => "SaiAdmin",
    "basePath" => dirname(__DIR__),
    "bootstrap" => ["log"],
    "aliases" => [
        "@bower" => "@vendor/bower-asset",
        "@npm"   => "@vendor/npm-asset",
    ],
    "components" => [
        "request" => [
            "cookieValidationKey" => "your-secret-key-here",
            "parsers" => [
                "application/json" => "yii\web\JsonParser",
            ]
        ],
        "cache" => [
            "class" => "yii\caching\FileCache",
        ],
        "user" => [
            "identityClass" => "app\models\User",
            "enableAutoLogin" => true,
        ],
        "errorHandler" => [
            "errorAction" => "site/error",
        ],
        "mailer" => [
            "class" => "yii\swiftmailer\Mailer",
            "useFileTransport" => true,
        ],
        "log" => [
            "traceLevel" => YII_DEBUG ? 3 : 0,
            "targets" => [
                [
                    "class" => "yii\log\FileTarget",
                    "levels" => ["error", "warning"],
                ],
            ],
        ],
        "db" => $db,
        "urlManager" => [
            "enablePrettyUrl" => true,
            "showScriptName" => false,
            "rules" => [
                "api/<controller:\w+>/<action:\w+>" => "api/<controller>/<action>",
                "admin/<controller:\w+>/<action:\w+>" => "admin/<controller>/<action>",
                "<controller:\w+>/<action:\w+>" => "<controller>/<action>",
            ],
        ],
    ],
    "modules" => [
        "admin" => [
            "class" => "app\modules\admin\Module",
        ],
        "tool" => [
            "class" => "app\modules\tool\Module",
        ],
        "api" => [
            "class" => "app\modules\api\Module",
        ],
    ],
    "params" => $params,
];

if (YII_ENV_DEV) {
    // 开发环境配置调整
    $config["bootstrap"][] = "debug";
    $config["modules"]["debug"] = [
        "class" => "yii\debug\Module",
        "allowedIPs" => ["127.0.0.1", "::1"],
    ];

    $config["bootstrap"][] = "gii";
    $config["modules"]["gii"] = [
        "class" => "yii\gii\Module",
        "allowedIPs" => ["127.0.0.1", "::1"],
    ];
}

return $config;';

file_put_contents('yii2-saiadmin/config/web.php', $webConfig);
echo "  ✅ 创建 Web 配置: config/web.php\n";

// 控制台应用配置
$consoleConfig = '<?php
/**
 * SaiAdmin Yii 2.0 控制台应用配置
 */

$params = require __DIR__ . "/params.php";
$db = require __DIR__ . "/db.php";

$config = [
    "id" => "saiadmin-console",
    "basePath" => dirname(__DIR__),
    "bootstrap" => ["log"],
    "controllerNamespace" => "app\commands",
    "aliases" => [
        "@bower" => "@vendor/bower-asset",
        "@npm"   => "@vendor/npm-asset",
        "@tests" => "@app/tests",
    ],
    "components" => [
        "cache" => [
            "class" => "yii\caching\FileCache",
        ],
        "log" => [
            "targets" => [
                [
                    "class" => "yii\log\FileTarget",
                    "levels" => ["error", "warning"],
                ],
            ],
        ],
        "db" => $db,
    ],
    "params" => $params,
    "controllerMap" => [
        "migrate" => [
            "class" => "yii\console\controllers\MigrateController",
            "migrationPath" => "@app/migrations",
        ],
    ],
];

return $config;';

file_put_contents('yii2-saiadmin/config/console.php', $consoleConfig);
echo "  ✅ 创建控制台配置: config/console.php\n";

// 数据库配置
$dbConfig = '<?php
/**
 * SaiAdmin 数据库配置
 */

return [
    "class" => "yii\db\Connection",
    "dsn" => "mysql:host=127.0.0.1;dbname=saiadmin",
    "username" => "root",
    "password" => "5GeNi1v7P7Xcur5W",
    "charset" => "utf8mb4",
    
    // Schema cache options (for production environment)
    "enableSchemaCache" => true,
    "schemaCacheDuration" => 60,
    "schemaCache" => "cache",
];';

file_put_contents('yii2-saiadmin/config/db.php', $dbConfig);
echo "  ✅ 创建数据库配置: config/db.php\n";

// 参数配置
$paramsConfig = '<?php
/**
 * SaiAdmin 应用参数配置
 */

return [
    "adminEmail" => "<EMAIL>",
    "senderEmail" => "<EMAIL>",
    "senderName" => "SaiAdmin",
    "supportEmail" => "<EMAIL>",
    "user.passwordResetTokenExpire" => 3600,
    "user.passwordMinLength" => 8,
    
    // SaiAdmin 特定参数
    "saiadmin.version" => "2.0.0",
    "saiadmin.name" => "SaiAdmin Yii2",
    "saiadmin.description" => "基于 Yii 2.0 的快速开发框架",
];';

file_put_contents('yii2-saiadmin/config/params.php', $paramsConfig);
echo "  ✅ 创建参数配置: config/params.php\n";

echo "\n[5/6] 创建 Yii 2.0 兼容的基础类...\n";

// 创建基础控制器
$baseControllerContent = '<?php
/**
 * SaiAdmin 基础控制器 (Yii 2.0 兼容)
 */
namespace app\components\base;

use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;

/**
 * 基础控制器类
 */
class BaseController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            "access" => [
                "class" => AccessControl::class,
                "rules" => [
                    [
                        "allow" => true,
                        "roles" => ["@"],
                    ],
                ],
            ],
            "verbs" => [
                "class" => VerbFilter::class,
                "actions" => [
                    "delete" => ["POST"],
                ],
            ],
        ];
    }

    /**
     * 返回成功响应
     * @param mixed $data 数据
     * @param string $message 消息
     * @param int $code 状态码
     * @return array
     */
    protected function success($data = [], $message = "操作成功", $code = 200)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            "code" => $code,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }

    /**
     * 返回失败响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 数据
     * @return array
     */
    protected function fail($message = "操作失败", $code = 400, $data = [])
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            "code" => $code,
            "message" => $message,
            "data" => $data,
            "timestamp" => time()
        ];
    }

    /**
     * 获取分页参数
     * @return array
     */
    protected function getPaginationParams()
    {
        $request = Yii::$app->request;
        return [
            "page" => (int) $request->get("page", 1),
            "pageSize" => (int) $request->get("pageSize", 20),
        ];
    }

    /**
     * 获取排序参数
     * @return array
     */
    protected function getSortParams()
    {
        $request = Yii::$app->request;
        return [
            "sort" => $request->get("sort", "id"),
            "order" => $request->get("order", "desc"),
        ];
    }
}';

file_put_contents('yii2-saiadmin/components/base/BaseController.php', $baseControllerContent);
echo "  ✅ 创建基础控制器: components/base/BaseController.php\n";

echo "\n[6/6] 生成项目结构对比报告...\n";

$report = [
    "migration_time" => date("Y-m-d H:i:s"),
    "source_structure" => "Webman + SaiAdmin",
    "target_structure" => "Yii 2.0 Compatible",
    "created_directories" => count($yii2Dirs),
    "created_files" => [
        "web/index.php" => "Web应用入口",
        "yii" => "控制台应用入口", 
        "config/web.php" => "Web应用配置",
        "config/console.php" => "控制台应用配置",
        "config/db.php" => "数据库配置",
        "config/params.php" => "应用参数",
        "components/base/BaseController.php" => "基础控制器"
    ],
    "directory_mapping" => $currentStructure,
    "yii2_features" => [
        "MVC架构" => "✅ 已实现",
        "模块化" => "✅ 已配置",
        "组件系统" => "✅ 已配置", 
        "缓存系统" => "✅ 已配置",
        "日志系统" => "✅ 已配置",
        "数据库抽象层" => "✅ 已配置",
        "URL路由" => "✅ 已配置",
        "表单验证" => "🔄 需要迁移",
        "权限控制" => "🔄 需要迁移",
        "国际化" => "🔄 待实现"
    ]
];

file_put_contents("yii2-migration-report.json", json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "  ✅ 生成迁移报告: yii2-migration-report.json\n";

echo "\n========================================\n";
echo "🎉 Yii 2.0 兼容结构创建完成！\n";
echo "========================================\n\n";

echo "📁 新建目录结构:\n";
echo "yii2-saiadmin/\n";
echo "├── assets/           # 资源包\n";
echo "├── commands/         # 控制台命令\n";
echo "├── components/       # 应用组件\n";
echo "│   ├── base/        # 基础类\n";
echo "│   ├── services/    # 业务服务\n";
echo "│   ├── validators/  # 验证器\n";
echo "│   └── filters/     # 过滤器\n";
echo "├── config/          # 配置文件\n";
echo "├── controllers/     # 控制器\n";
echo "├── models/          # 模型\n";
echo "├── modules/         # 模块\n";
echo "│   ├── admin/      # 管理模块\n";
echo "│   └── tool/       # 工具模块\n";
echo "├── runtime/         # 运行时文件\n";
echo "├── views/           # 视图文件\n";
echo "├── web/             # Web根目录\n";
echo "├── widgets/         # 小部件\n";
echo "├── migrations/      # 数据库迁移\n";
echo "├── mail/            # 邮件模板\n";
echo "├── rbac/            # RBAC权限\n";
echo "└── tests/           # 测试文件\n\n";

echo "🚀 下一步操作:\n";
echo "1. 安装 Yii 2.0: composer require yiisoft/yii2\n";
echo "2. 迁移现有控制器到新结构\n";
echo "3. 迁移现有模型到新结构\n";
echo "4. 配置数据库连接\n";
echo "5. 测试应用功能\n\n";

echo "💡 Yii 2.0 兼容特性:\n";
echo "- ✅ 标准MVC架构\n";
echo "- ✅ 模块化设计\n";
echo "- ✅ 组件系统\n";
echo "- ✅ 依赖注入\n";
echo "- ✅ 事件系统\n";
echo "- ✅ 缓存支持\n";
echo "- ✅ 日志系统\n";
echo "- ✅ 数据库抽象\n";
echo "- ✅ URL美化\n";
echo "- ✅ 表单处理\n";
