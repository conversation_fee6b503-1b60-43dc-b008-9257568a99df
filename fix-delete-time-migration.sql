-- 修复delete_time字段缺失问题
-- 为现有表添加软删除相关字段

-- 如果要使用软删除，需要为表添加delete_time字段
-- ALTER TABLE sa_article_category ADD COLUMN delete_time timestamp NULL DEFAULT NULL COMMENT "删除时间";
-- ALTER TABLE sa_article ADD COLUMN delete_time timestamp NULL DEFAULT NULL COMMENT "删除时间";

-- 或者创建标准的时间字段结构
-- ALTER TABLE sa_article_category 
-- ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
-- ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
-- ADD COLUMN deleted_at timestamp NULL DEFAULT NULL COMMENT "删除时间";

-- 检查现有表结构
SHOW COLUMNS FROM sa_article_category;
-- SHOW COLUMNS FROM sa_article;

-- 如果表不存在，创建标准的文章分类表
CREATE TABLE IF NOT EXISTS sa_article_category (
    id int(11) NOT NULL AUTO_INCREMENT COMMENT "主键ID",
    parent_id int(11) NOT NULL DEFAULT 0 COMMENT "父级ID", 
    category_name varchar(50) NOT NULL DEFAULT "" COMMENT "分类名称",
    category_desc varchar(200) DEFAULT "" COMMENT "分类描述",
    image varchar(255) DEFAULT "" COMMENT "分类图片",
    sort int(11) NOT NULL DEFAULT 0 COMMENT "排序",
    status tinyint(1) NOT NULL DEFAULT 1 COMMENT "状态 0禁用 1启用",
    created_by int(11) DEFAULT 0 COMMENT "创建者",
    updated_by int(11) DEFAULT 0 COMMENT "更新者", 
    created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间",
    PRIMARY KEY (id),
    KEY idx_parent_id (parent_id),
    KEY idx_status (status),
    KEY idx_sort (sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="文章分类表";