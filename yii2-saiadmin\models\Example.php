<?php
/**
 * 示例模型
 */
namespace app\models;

use app\components\base\BaseModel;

/**
 * Example 模型
 */
class Example extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return "{{%example}}";
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [["name"], "required"],
            [["description"], "string"],
            [["status"], "integer"],
            [["name"], "string", "max" => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            "id" => "ID",
            "name" => "名称",
            "description" => "描述",
            "status" => "状态",
            "created_at" => "创建时间",
            "updated_at" => "更新时间",
        ];
    }
}