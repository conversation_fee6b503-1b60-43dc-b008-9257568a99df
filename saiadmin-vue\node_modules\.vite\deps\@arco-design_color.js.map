{"version": 3, "sources": ["../../color-name/index.js", "../../is-arrayish/index.js", "../../simple-swizzle/index.js", "../../color-string/index.js", "../../color-convert/conversions.js", "../../color-convert/route.js", "../../color-convert/index.js", "../../color/index.js", "../../@arco-design/color/src/utils.js", "../../@arco-design/color/src/palette.js", "../../@arco-design/color/src/palette-dark.js", "../../@arco-design/color/src/generate.js", "../../@arco-design/color/src/index.js"], "sourcesContent": ["'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "module.exports = function isArrayish(obj) {\n\tif (!obj || typeof obj === 'string') {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && (obj.splice instanceof Function ||\n\t\t\t(Object.getOwnPropertyDescriptor(obj, (obj.length - 1)) && obj.constructor.name !== 'String')));\n};\n", "'use strict';\n\nvar isArrayish = require('is-arrayish');\n\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\n\nvar swizzle = module.exports = function swizzle(args) {\n\tvar results = [];\n\n\tfor (var i = 0, len = args.length; i < len; i++) {\n\t\tvar arg = args[i];\n\n\t\tif (isArrayish(arg)) {\n\t\t\t// http://jsperf.com/javascript-array-concat-vs-push/98\n\t\t\tresults = concat.call(results, slice.call(arg));\n\t\t} else {\n\t\t\tresults.push(arg);\n\t\t}\n\t}\n\n\treturn results;\n};\n\nswizzle.wrap = function (fn) {\n\treturn function () {\n\t\treturn fn(swizzle(arguments));\n\t};\n};\n", "/* MIT license */\nvar colorNames = require('color-name');\nvar swizzle = require('simple-swizzle');\nvar hasOwnProperty = Object.hasOwnProperty;\n\nvar reverseNames = Object.create(null);\n\n// create a list of reverse color names\nfor (var name in colorNames) {\n\tif (hasOwnProperty.call(colorNames, name)) {\n\t\treverseNames[colorNames[name]] = name;\n\t}\n}\n\nvar cs = module.exports = {\n\tto: {},\n\tget: {}\n};\n\ncs.get = function (string) {\n\tvar prefix = string.substring(0, 3).toLowerCase();\n\tvar val;\n\tvar model;\n\tswitch (prefix) {\n\t\tcase 'hsl':\n\t\t\tval = cs.get.hsl(string);\n\t\t\tmodel = 'hsl';\n\t\t\tbreak;\n\t\tcase 'hwb':\n\t\t\tval = cs.get.hwb(string);\n\t\t\tmodel = 'hwb';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tval = cs.get.rgb(string);\n\t\t\tmodel = 'rgb';\n\t\t\tbreak;\n\t}\n\n\tif (!val) {\n\t\treturn null;\n\t}\n\n\treturn {model: model, value: val};\n};\n\ncs.get.rgb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar abbr = /^#([a-f0-9]{3,4})$/i;\n\tvar hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;\n\tvar rgba = /^rgba?\\(\\s*([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)(?=[\\s,])\\s*(?:,\\s*)?([+-]?\\d+)\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*,?\\s*([+-]?[\\d\\.]+)\\%\\s*(?:[,|\\/]\\s*([+-]?[\\d\\.]+)(%?)\\s*)?\\)$/;\n\tvar keyword = /^(\\w+)$/;\n\n\tvar rgb = [0, 0, 0, 1];\n\tvar match;\n\tvar i;\n\tvar hexAlpha;\n\n\tif (match = string.match(hex)) {\n\t\thexAlpha = match[2];\n\t\tmatch = match[1];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\t// https://jsperf.com/slice-vs-substr-vs-substring-methods-long-string/19\n\t\t\tvar i2 = i * 2;\n\t\t\trgb[i] = parseInt(match.slice(i2, i2 + 2), 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(abbr)) {\n\t\tmatch = match[1];\n\t\thexAlpha = match[3];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i] + match[i], 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = parseInt(hexAlpha + hexAlpha, 16) / 255;\n\t\t}\n\t} else if (match = string.match(rgba)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i + 1], 0);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(per)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\tif (match[5]) {\n\t\t\t\trgb[3] = parseFloat(match[4]) * 0.01;\n\t\t\t} else {\n\t\t\t\trgb[3] = parseFloat(match[4]);\n\t\t\t}\n\t\t}\n\t} else if (match = string.match(keyword)) {\n\t\tif (match[1] === 'transparent') {\n\t\t\treturn [0, 0, 0, 0];\n\t\t}\n\n\t\tif (!hasOwnProperty.call(colorNames, match[1])) {\n\t\t\treturn null;\n\t\t}\n\n\t\trgb = colorNames[match[1]];\n\t\trgb[3] = 1;\n\n\t\treturn rgb;\n\t} else {\n\t\treturn null;\n\t}\n\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = clamp(rgb[i], 0, 255);\n\t}\n\trgb[3] = clamp(rgb[3], 0, 1);\n\n\treturn rgb;\n};\n\ncs.get.hsl = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hsl = /^hsla?\\(\\s*([+-]?(?:\\d{0,3}\\.)?\\d+)(?:deg)?\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*,?\\s*([+-]?[\\d\\.]+)%\\s*(?:[,|\\/]\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hsl);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar s = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar l = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\n\t\treturn [h, s, l, a];\n\t}\n\n\treturn null;\n};\n\ncs.get.hwb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hwb = /^hwb\\(\\s*([+-]?\\d{0,3}(?:\\.\\d+)?)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?(?=\\.\\d|\\d)(?:0|[1-9]\\d*)?(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\s*)?\\)$/;\n\tvar match = string.match(hwb);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar w = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar b = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\t\treturn [h, w, b, a];\n\t}\n\n\treturn null;\n};\n\ncs.to.hex = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn (\n\t\t'#' +\n\t\thexDouble(rgba[0]) +\n\t\thexDouble(rgba[1]) +\n\t\thexDouble(rgba[2]) +\n\t\t(rgba[3] < 1\n\t\t\t? (hexDouble(Math.round(rgba[3] * 255)))\n\t\t\t: '')\n\t);\n};\n\ncs.to.rgb = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ')'\n\t\t: 'rgba(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ', ' + rgba[3] + ')';\n};\n\ncs.to.rgb.percent = function () {\n\tvar rgba = swizzle(arguments);\n\n\tvar r = Math.round(rgba[0] / 255 * 100);\n\tvar g = Math.round(rgba[1] / 255 * 100);\n\tvar b = Math.round(rgba[2] / 255 * 100);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + r + '%, ' + g + '%, ' + b + '%)'\n\t\t: 'rgba(' + r + '%, ' + g + '%, ' + b + '%, ' + rgba[3] + ')';\n};\n\ncs.to.hsl = function () {\n\tvar hsla = swizzle(arguments);\n\treturn hsla.length < 4 || hsla[3] === 1\n\t\t? 'hsl(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%)'\n\t\t: 'hsla(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%, ' + hsla[3] + ')';\n};\n\n// hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n// (hwb have alpha optional & 1 is default value)\ncs.to.hwb = function () {\n\tvar hwba = swizzle(arguments);\n\n\tvar a = '';\n\tif (hwba.length >= 4 && hwba[3] !== 1) {\n\t\ta = ', ' + hwba[3];\n\t}\n\n\treturn 'hwb(' + hwba[0] + ', ' + hwba[1] + '%, ' + hwba[2] + '%' + a + ')';\n};\n\ncs.to.keyword = function (rgb) {\n\treturn reverseNames[rgb.slice(0, 3)];\n};\n\n// helpers\nfunction clamp(num, min, max) {\n\treturn Math.min(Math.max(min, num), max);\n}\n\nfunction hexDouble(num) {\n\tvar str = Math.round(num).toString(16).toUpperCase();\n\treturn (str.length < 2) ? '0' + str : str;\n}\n", "/* MIT license */\nvar cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n", "var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n", "var conversions = require('./conversions');\nvar route = require('./route');\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n", "'use strict';\n\nvar colorString = require('color-string');\nvar convert = require('color-convert');\n\nvar _slice = [].slice;\n\nvar skippedModels = [\n\t// to be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// shouldn't really be in color-convert either...\n\t'hex'\n];\n\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n\thashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\n\nvar limiters = {};\n\nfunction Color(obj, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(obj, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tvar i;\n\tvar channels;\n\n\tif (obj == null) { // eslint-disable-line no-eq-null,eqeqeq\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (obj instanceof Color) {\n\t\tthis.model = obj.model;\n\t\tthis.color = obj.color.slice();\n\t\tthis.valpha = obj.valpha;\n\t} else if (typeof obj === 'string') {\n\t\tvar result = colorString.get(obj);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + obj);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (obj.length) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tvar newArr = _slice.call(obj, 0, channels);\n\t\tthis.color = zeroArray(newArr, channels);\n\t\tthis.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n\t} else if (typeof obj === 'number') {\n\t\t// this is always RGB - can be converted later on.\n\t\tobj &= 0xFFFFFF;\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(obj >> 16) & 0xFF,\n\t\t\t(obj >> 8) & 0xFF,\n\t\t\tobj & 0xFF\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tvar keys = Object.keys(obj);\n\t\tif ('alpha' in obj) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n\t\t}\n\n\t\tvar hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tvar labels = convert[this.model].labels;\n\t\tvar color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(obj[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tvar limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString: function () {\n\t\treturn this.string();\n\t},\n\n\ttoJSON: function () {\n\t\treturn this[this.model]();\n\t},\n\n\tstring: function (places) {\n\t\tvar self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString: function (places) {\n\t\tvar self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray: function () {\n\t\treturn this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n\t},\n\n\tobject: function () {\n\t\tvar result = {};\n\t\tvar channels = convert[this.model].channels;\n\t\tvar labels = convert[this.model].labels;\n\n\t\tfor (var i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray: function () {\n\t\tvar rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject: function () {\n\t\tvar rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround: function (places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n\t},\n\n\talpha: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) { return ((val % 360) + 360) % 360; }), // eslint-disable-line brace-style\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(100)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(100)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\trgbNumber: function () {\n\t\tvar rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity: function () {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tvar rgb = this.rgb().color;\n\n\t\tvar lum = [];\n\t\tfor (var i = 0; i < rgb.length; i++) {\n\t\t\tvar chan = rgb[i] / 255;\n\t\t\tlum[i] = (chan <= 0.03928) ? chan / 12.92 : Math.pow(((chan + 0.055) / 1.055), 2.4);\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast: function (color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tvar lum1 = this.luminosity();\n\t\tvar lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel: function (color2) {\n\t\tvar contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7.1) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark: function () {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tvar rgb = this.rgb().color;\n\t\tvar yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight: function () {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate: function () {\n\t\tvar rgb = this.rgb();\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\t\treturn rgb;\n\t},\n\n\tlighten: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale: function () {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tvar rgb = this.rgb().color;\n\t\tvar val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(val, val, val);\n\t},\n\n\tfade: function (ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer: function (ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate: function (degrees) {\n\t\tvar hsl = this.hsl();\n\t\tvar hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix: function (mixinColor, weight) {\n\t\t// ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\t\tvar color1 = mixinColor.rgb();\n\t\tvar color2 = this.rgb();\n\t\tvar p = weight === undefined ? 0.5 : weight;\n\n\t\tvar w = 2 * p - 1;\n\t\tvar a = color1.alpha() - color2.alpha();\n\n\t\tvar w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\tvar w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t}\n};\n\n// model conversion methods and static constructors\nObject.keys(convert).forEach(function (model) {\n\tif (skippedModels.indexOf(model) !== -1) {\n\t\treturn;\n\t}\n\n\tvar channels = convert[model].channels;\n\n\t// conversion methods\n\tColor.prototype[model] = function () {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (arguments.length) {\n\t\t\treturn new Color(arguments, model);\n\t\t}\n\n\t\tvar newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n\t\treturn new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (color) {\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(_slice.call(arguments), channels);\n\t\t}\n\t\treturn new Color(color, model);\n\t};\n});\n\nfunction roundTo(num, places) {\n\treturn Number(num.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (num) {\n\t\treturn roundTo(num, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tmodel.forEach(function (m) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t});\n\n\tmodel = model[0];\n\n\treturn function (val) {\n\t\tvar result;\n\n\t\tif (arguments.length) {\n\t\t\tif (modifier) {\n\t\t\t\tval = modifier(val);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = val;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(val) {\n\treturn Array.isArray(val) ? val : [val];\n}\n\nfunction zeroArray(arr, length) {\n\tfor (var i = 0; i < length; i++) {\n\t\tif (typeof arr[i] !== 'number') {\n\t\t\tarr[i] = 0;\n\t\t}\n\t}\n\n\treturn arr;\n}\n\nmodule.exports = Color;\n", "const Color = require('color');\n\nexports.getRgbStr = function(color) {\n  return Color(color)\n    .rgb()\n    .round()\n    .color\n    .join(',');\n}\n\nconst formats = ['hex', 'rgb', 'hsl'];\n\nfunction getFormat(format) {\n  if (!format || formats.indexOf(format) < 0) {\n    return 'hex';\n  }\n  return format;\n}\n\nexports.getColorString = function(color, format) {\n  const innerFormat = getFormat(format);\n\n  if (innerFormat === 'hex') {\n    return color[innerFormat]();\n  }\n  return color[innerFormat]().round().string();\n}\n", "const Color = require('color');\nconst { getColorString } = require('./utils');\n\n// 色板\n\n// 动态梯度算法\nfunction colorPalette(originColor, i, format) {\n  const color = Color(originColor);\n  const h = color.hue();\n  const s = color.saturationv();\n  const v = color.value();\n\n  const hueStep = 2;\n  const maxSaturationStep = 100;\n  const minSaturationStep = 9;\n\n  const maxValue = 100;\n  const minValue = 30;\n\n  function getNewHue(isLight, i) {\n    let hue;\n    if (h >= 60 && h <= 240) {\n      hue = isLight ? h - hueStep * i : h + hueStep * i;\n    } else {\n      hue = isLight ? h + hueStep * i : h - hueStep * i;\n    }\n    if (hue < 0) {\n      hue += 360;\n    } else if (hue >= 360) {\n      hue -= 360;\n    }\n    return Math.round(hue);\n  }\n\n  function getNewSaturation(isLight, i) {\n    let newSaturation;\n\n    if (isLight) {\n      newSaturation = s <= minSaturationStep ? s : s - ((s - minSaturationStep) / 5) * i;\n    } else {\n      newSaturation = s + ((maxSaturationStep - s) / 4) * i;\n    }\n    return newSaturation;\n  }\n\n  function getNewValue(isLight, i) {\n    return isLight ? v + ((maxValue - v) / 5) * i : (v <= minValue ? v : v - ((v - minValue) / 4) * i);\n  }\n\n  const isLight = i < 6;\n  const index = isLight ? 6 - i : i - 6;\n\n  const retColor = i === 6\n    ? color\n    : Color({\n        h: getNewHue(isLight, index),\n        s: getNewSaturation(isLight, index),\n        v: getNewValue(isLight, index),\n      });\n  \n  return getColorString(retColor, format);\n}\n\nmodule.exports = colorPalette;\n", "const Color = require('color');\nconst colorPalette = require('./palette');\nconst { getColorString } = require('./utils');\n\n// 暗色色板\n\n// 动态梯度算法\nfunction colorPaletteDark(originColor, i, format) {\n  const lightColor = Color(colorPalette(originColor, 10 - i + 1));\n  const originBaseColor = Color(originColor);\n\n  const originBaseHue = originBaseColor.hue();\n  const originBaseSaturation = originBaseColor.saturationv();\n  const baseColor = Color({\n    h: originBaseColor.hue(),\n    s: getNewSaturation(6),\n    v: originBaseColor.value(),\n  });\n\n  const baseSaturation = baseColor.saturationv();\n  const step = Math.ceil((baseSaturation - 9) / 4);\n  const step1to5 = Math.ceil((100 - baseSaturation) / 5);\n\n  function getNewSaturation(_index) {\n    if (_index < 6) {\n      return baseSaturation + (6 - _index) * step1to5;\n    }\n    if (_index === 6) {\n      if (originBaseHue >= 0 && originBaseHue < 50) {\n        return originBaseSaturation - 15;\n      }\n      if (originBaseHue >= 50 && originBaseHue < 191) {\n        return originBaseSaturation - 20;\n      }\n      if (originBaseHue >= 191 && originBaseHue <= 360) {\n        return originBaseSaturation - 15;\n      }\n    }\n\n    return baseSaturation - step * (_index - 6);\n  }\n\n  const retColor = Color({\n    h: lightColor.hue(),\n    s: getNewSaturation(i),\n    v: lightColor.value(),\n  });\n\n  return getColorString(retColor, format);\n}\n\nmodule.exports = colorPaletteDark;", "const colorPalette = require('./palette');\nconst colorPaletteDark = require('./palette-dark');\n\n/**\n * @param {string} color\n * @param {Object} options\n * @param {number} options.index 1 - 10 (default: 6)\n * @param {boolean} options.dark\n * @param {boolean} options.list\n * @param {string} options.format 'hex' | 'rgb' | 'hsl'\n * \n * @return string | string[]\n */\nfunction generate(color, options = {}) {\n  const { dark, list, index = 6, format = 'hex' } = options;\n\n  if (list) {\n    const list = [];\n    const func = dark ? colorPaletteDark : colorPalette;\n    for(let i = 1; i <= 10; i++) {\n      list.push(func(color, i, format));\n    }\n    return list;\n  }\n  return dark ? colorPaletteDark(color, index, format) : colorPalette(color, index, format);\n}\n\nmodule.exports = generate;\n", "const generate = require('./generate');\nconst { getRgbStr } = require('./utils');\n\nexports.generate = generate;\nexports.getRgbStr = getRgbStr;\n\nconst colorList = {\n  red: '#F53F3F',\n  orangered: '#F77234',\n  orange: '#FF7D00',\n  gold: '#F7BA1E',\n  yellow: '#FADC19',\n  lime: '#9FDB1D',\n  green: '#00B42A',\n  cyan: '#14C9C9',\n  blue: '#3491FA',\n  arcoblue: '#165DFF',\n  purple: '#722ED1',\n  pinkpurple: '#D91AD9',\n  magenta: '#F5319D',\n};\n\nfunction getPresetColors() {\n  const presetColors = {};\n  Object.keys(colorList).forEach((key) => {\n    presetColors[key] = {};\n    presetColors[key].light = generate(colorList[key], { list: true });\n    presetColors[key].dark = generate(colorList[key], { list: true, dark: true });\n    presetColors[key].primary = colorList[key];\n  });\n\n  presetColors.gray = {};\n  presetColors.gray.light = [\n    '#f7f8fa',\n    '#f2f3f5',\n    '#e5e6eb',\n    '#c9cdd4',\n    '#a9aeb8',\n    '#86909c',\n    '#6b7785',\n    '#4e5969',\n    '#272e3b',\n    '#1d2129',\n  ];\n  presetColors.gray.dark = [\n    '#17171a',\n    '#2e2e30',\n    '#484849',\n    '#5f5f60',\n    '#78787a',\n    '#929293',\n    '#ababac',\n    '#c5c5c5',\n    '#dfdfdf',\n    '#f6f6f6',\n  ];\n  presetColors.gray.primary = presetColors.gray.light[6];\n\n  return presetColors;\n}\n\nexports.getPresetColors = getPresetColors;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MAChB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,SAAS,CAAC,KAAK,IAAI,EAAE;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,SAAS,CAAC,KAAK,KAAK,EAAE;AAAA,MACtB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,IAAI,EAAE;AAAA,MACvB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,YAAY,CAAC,GAAG,GAAG,GAAG;AAAA,MACtB,YAAY,CAAC,GAAG,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,EAAE;AAAA,MAC9B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,GAAG,KAAK,CAAC;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,GAAG,GAAG;AAAA,MAC3B,kBAAkB,CAAC,IAAI,KAAK,EAAE;AAAA,MAC9B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,CAAC;AAAA,MACrB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,iBAAiB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,GAAG,KAAK,GAAG;AAAA,MAC7B,cAAc,CAAC,KAAK,GAAG,GAAG;AAAA,MAC1B,YAAY,CAAC,KAAK,IAAI,GAAG;AAAA,MACzB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,cAAc,CAAC,IAAI,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,eAAe,CAAC,IAAI,KAAK,EAAE;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,CAAC;AAAA,MACpB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,MACnB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,UAAU,CAAC,IAAI,GAAG,GAAG;AAAA,MACrB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,wBAAwB,CAAC,KAAK,KAAK,GAAG;AAAA,MACtC,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC9B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,KAAK,CAAC;AAAA,MAClB,aAAa,CAAC,IAAI,KAAK,EAAE;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,GAAG,CAAC;AAAA,MACpB,oBAAoB,CAAC,KAAK,KAAK,GAAG;AAAA,MAClC,cAAc,CAAC,GAAG,GAAG,GAAG;AAAA,MACxB,gBAAgB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC7B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC/B,mBAAmB,CAAC,KAAK,KAAK,GAAG;AAAA,MACjC,qBAAqB,CAAC,GAAG,KAAK,GAAG;AAAA,MACjC,mBAAmB,CAAC,IAAI,KAAK,GAAG;AAAA,MAChC,mBAAmB,CAAC,KAAK,IAAI,GAAG;AAAA,MAChC,gBAAgB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,CAAC;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,aAAa,CAAC,KAAK,IAAI,CAAC;AAAA,MACxB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,EAAE;AAAA,MACrB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,GAAG,GAAG;AAAA,MACtB,iBAAiB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC9B,OAAO,CAAC,KAAK,GAAG,CAAC;AAAA,MACjB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,IAAI,EAAE;AAAA,MAC3B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,cAAc,CAAC,KAAK,KAAK,EAAE;AAAA,MAC3B,YAAY,CAAC,IAAI,KAAK,EAAE;AAAA,MACxB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,OAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACrB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,IAC7B;AAAA;AAAA;;;ACvJA;AAAA;AAAA,WAAO,UAAU,SAAS,WAAW,KAAK;AACzC,UAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACpC,eAAO;AAAA,MACR;AAEA,aAAO,eAAe,SAAS,MAAM,QAAQ,GAAG,KAC9C,IAAI,UAAU,MAAM,IAAI,kBAAkB,YACzC,OAAO,yBAAyB,KAAM,IAAI,SAAS,CAAE,KAAK,IAAI,YAAY,SAAS;AAAA,IACvF;AAAA;AAAA;;;ACRA;AAAA;AAAA;AAEA,QAAI,aAAa;AAEjB,QAAI,SAAS,MAAM,UAAU;AAC7B,QAAI,QAAQ,MAAM,UAAU;AAE5B,QAAI,UAAU,OAAO,UAAU,SAASA,SAAQ,MAAM;AACrD,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAI,MAAM,KAAK,CAAC;AAEhB,YAAI,WAAW,GAAG,GAAG;AAEpB,oBAAU,OAAO,KAAK,SAAS,MAAM,KAAK,GAAG,CAAC;AAAA,QAC/C,OAAO;AACN,kBAAQ,KAAK,GAAG;AAAA,QACjB;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,OAAO,SAAU,IAAI;AAC5B,aAAO,WAAY;AAClB,eAAO,GAAG,QAAQ,SAAS,CAAC;AAAA,MAC7B;AAAA,IACD;AAAA;AAAA;;;AC5BA;AAAA;AACA,QAAI,aAAa;AACjB,QAAI,UAAU;AACd,QAAI,iBAAiB,OAAO;AAE5B,QAAI,eAAe,uBAAO,OAAO,IAAI;AAGrC,SAAS,QAAQ,YAAY;AAC5B,UAAI,eAAe,KAAK,YAAY,IAAI,GAAG;AAC1C,qBAAa,WAAW,IAAI,CAAC,IAAI;AAAA,MAClC;AAAA,IACD;AAJS;AAMT,QAAI,KAAK,OAAO,UAAU;AAAA,MACzB,IAAI,CAAC;AAAA,MACL,KAAK,CAAC;AAAA,IACP;AAEA,OAAG,MAAM,SAAU,QAAQ;AAC1B,UAAI,SAAS,OAAO,UAAU,GAAG,CAAC,EAAE,YAAY;AAChD,UAAI;AACJ,UAAI;AACJ,cAAQ,QAAQ;AAAA,QACf,KAAK;AACJ,gBAAM,GAAG,IAAI,IAAI,MAAM;AACvB,kBAAQ;AACR;AAAA,QACD,KAAK;AACJ,gBAAM,GAAG,IAAI,IAAI,MAAM;AACvB,kBAAQ;AACR;AAAA,QACD;AACC,gBAAM,GAAG,IAAI,IAAI,MAAM;AACvB,kBAAQ;AACR;AAAA,MACF;AAEA,UAAI,CAAC,KAAK;AACT,eAAO;AAAA,MACR;AAEA,aAAO,EAAC,OAAc,OAAO,IAAG;AAAA,IACjC;AAEA,OAAG,IAAI,MAAM,SAAU,QAAQ;AAC9B,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,UAAI,OAAO;AACX,UAAI,MAAM;AACV,UAAI,OAAO;AACX,UAAI,MAAM;AACV,UAAI,UAAU;AAEd,UAAI,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,QAAQ,OAAO,MAAM,GAAG,GAAG;AAC9B,mBAAW,MAAM,CAAC;AAClB,gBAAQ,MAAM,CAAC;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAEvB,cAAI,KAAK,IAAI;AACb,cAAI,CAAC,IAAI,SAAS,MAAM,MAAM,IAAI,KAAK,CAAC,GAAG,EAAE;AAAA,QAC9C;AAEA,YAAI,UAAU;AACb,cAAI,CAAC,IAAI,SAAS,UAAU,EAAE,IAAI;AAAA,QACnC;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,IAAI,GAAG;AACtC,gBAAQ,MAAM,CAAC;AACf,mBAAW,MAAM,CAAC;AAElB,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,cAAI,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE;AAAA,QAC1C;AAEA,YAAI,UAAU;AACb,cAAI,CAAC,IAAI,SAAS,WAAW,UAAU,EAAE,IAAI;AAAA,QAC9C;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,IAAI,GAAG;AACtC,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,cAAI,CAAC,IAAI,SAAS,MAAM,IAAI,CAAC,GAAG,CAAC;AAAA,QAClC;AAEA,YAAI,MAAM,CAAC,GAAG;AACb,cAAI,MAAM,CAAC,GAAG;AACb,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAAA,UACjC,OAAO;AACN,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,GAAG,GAAG;AACrC,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,cAAI,CAAC,IAAI,KAAK,MAAM,WAAW,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI;AAAA,QACpD;AAEA,YAAI,MAAM,CAAC,GAAG;AACb,cAAI,MAAM,CAAC,GAAG;AACb,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAAA,UACjC,OAAO;AACN,gBAAI,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,WAAW,QAAQ,OAAO,MAAM,OAAO,GAAG;AACzC,YAAI,MAAM,CAAC,MAAM,eAAe;AAC/B,iBAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,QACnB;AAEA,YAAI,CAAC,eAAe,KAAK,YAAY,MAAM,CAAC,CAAC,GAAG;AAC/C,iBAAO;AAAA,QACR;AAEA,cAAM,WAAW,MAAM,CAAC,CAAC;AACzB,YAAI,CAAC,IAAI;AAET,eAAO;AAAA,MACR,OAAO;AACN,eAAO;AAAA,MACR;AAEA,WAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,YAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,MAC9B;AACA,UAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC;AAE3B,aAAO;AAAA,IACR;AAEA,OAAG,IAAI,MAAM,SAAU,QAAQ;AAC9B,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,UAAI,MAAM;AACV,UAAI,QAAQ,OAAO,MAAM,GAAG;AAE5B,UAAI,OAAO;AACV,YAAI,QAAQ,WAAW,MAAM,CAAC,CAAC;AAC/B,YAAI,KAAM,WAAW,MAAM,CAAC,CAAC,IAAI,MAAO,OAAO;AAC/C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO,GAAG,CAAC;AAE5C,eAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACnB;AAEA,aAAO;AAAA,IACR;AAEA,OAAG,IAAI,MAAM,SAAU,QAAQ;AAC9B,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,UAAI,MAAM;AACV,UAAI,QAAQ,OAAO,MAAM,GAAG;AAE5B,UAAI,OAAO;AACV,YAAI,QAAQ,WAAW,MAAM,CAAC,CAAC;AAC/B,YAAI,KAAM,WAAW,MAAM,CAAC,CAAC,IAAI,MAAO,OAAO;AAC/C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;AAC1C,YAAI,IAAI,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO,GAAG,CAAC;AAC5C,eAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACnB;AAEA,aAAO;AAAA,IACR;AAEA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAE5B,aACC,MACA,UAAU,KAAK,CAAC,CAAC,IACjB,UAAU,KAAK,CAAC,CAAC,IACjB,UAAU,KAAK,CAAC,CAAC,KAChB,KAAK,CAAC,IAAI,IACP,UAAU,KAAK,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,IACpC;AAAA,IAEL;AAEA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAE5B,aAAO,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,IACnC,SAAS,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,MACzF,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI;AAAA,IAC/G;AAEA,OAAG,GAAG,IAAI,UAAU,WAAY;AAC/B,UAAI,OAAO,QAAQ,SAAS;AAE5B,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG;AACtC,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG;AACtC,UAAI,IAAI,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG;AAEtC,aAAO,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,IACnC,SAAS,IAAI,QAAQ,IAAI,QAAQ,IAAI,OACrC,UAAU,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,KAAK,CAAC,IAAI;AAAA,IAC5D;AAEA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAC5B,aAAO,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,IACnC,SAAS,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,OACtD,UAAU,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI;AAAA,IAC7E;AAIA,OAAG,GAAG,MAAM,WAAY;AACvB,UAAI,OAAO,QAAQ,SAAS;AAE5B,UAAI,IAAI;AACR,UAAI,KAAK,UAAU,KAAK,KAAK,CAAC,MAAM,GAAG;AACtC,YAAI,OAAO,KAAK,CAAC;AAAA,MAClB;AAEA,aAAO,SAAS,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,MAAM,IAAI;AAAA,IACxE;AAEA,OAAG,GAAG,UAAU,SAAU,KAAK;AAC9B,aAAO,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,IACpC;AAGA,aAAS,MAAM,KAAK,KAAK,KAAK;AAC7B,aAAO,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;AAAA,IACxC;AAEA,aAAS,UAAU,KAAK;AACvB,UAAI,MAAM,KAAK,MAAM,GAAG,EAAE,SAAS,EAAE,EAAE,YAAY;AACnD,aAAQ,IAAI,SAAS,IAAK,MAAM,MAAM;AAAA,IACvC;AAAA;AAAA;;;ACjPA;AAAA;AACA,QAAI,cAAc;AAMlB,QAAI,kBAAkB,CAAC;AACvB,SAAS,OAAO,aAAa;AAC5B,UAAI,YAAY,eAAe,GAAG,GAAG;AACpC,wBAAgB,YAAY,GAAG,CAAC,IAAI;AAAA,MACrC;AAAA,IACD;AAJS;AAMT,QAAI,UAAU,OAAO,UAAU;AAAA,MAC9B,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,MAAM,EAAC,UAAU,GAAG,QAAQ,OAAM;AAAA,MAClC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAC;AAAA,MAClC,SAAS,EAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAC;AAAA,MAC1C,QAAQ,EAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,EAAC;AAAA,MACxC,SAAS,EAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAC;AAAA,MAC1C,KAAK,EAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,KAAK,GAAG,EAAC;AAAA,MAC1C,OAAO,EAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,OAAO,KAAK,EAAC;AAAA,MAClD,MAAM,EAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAC;AAAA,IACrC;AAGA,SAAS,SAAS,SAAS;AAC1B,UAAI,QAAQ,eAAe,KAAK,GAAG;AAClC,YAAI,EAAE,cAAc,QAAQ,KAAK,IAAI;AACpC,gBAAM,IAAI,MAAM,gCAAgC,KAAK;AAAA,QACtD;AAEA,YAAI,EAAE,YAAY,QAAQ,KAAK,IAAI;AAClC,gBAAM,IAAI,MAAM,sCAAsC,KAAK;AAAA,QAC5D;AAEA,YAAI,QAAQ,KAAK,EAAE,OAAO,WAAW,QAAQ,KAAK,EAAE,UAAU;AAC7D,gBAAM,IAAI,MAAM,wCAAwC,KAAK;AAAA,QAC9D;AAEI,mBAAW,QAAQ,KAAK,EAAE;AAC1B,iBAAS,QAAQ,KAAK,EAAE;AAC5B,eAAO,QAAQ,KAAK,EAAE;AACtB,eAAO,QAAQ,KAAK,EAAE;AACtB,eAAO,eAAe,QAAQ,KAAK,GAAG,YAAY,EAAC,OAAO,SAAQ,CAAC;AACnE,eAAO,eAAe,QAAQ,KAAK,GAAG,UAAU,EAAC,OAAO,OAAM,CAAC;AAAA,MAChE;AAAA,IACD;AAPM;AACA;AAfG;AAuBT,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,UAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,UAAI,QAAQ,MAAM;AAClB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WAAW,MAAM,KAAK;AACrB,aAAK,IAAI,KAAK;AAAA,MACf,WAAW,MAAM,KAAK;AACrB,YAAI,KAAK,IAAI,KAAK;AAAA,MACnB,WAAW,MAAM,KAAK;AACrB,YAAI,KAAK,IAAI,KAAK;AAAA,MACnB;AAEA,UAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AAExB,UAAI,IAAI,GAAG;AACV,aAAK;AAAA,MACN;AAEA,WAAK,MAAM,OAAO;AAElB,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WAAW,KAAK,KAAK;AACpB,YAAI,SAAS,MAAM;AAAA,MACpB,OAAO;AACN,YAAI,SAAS,IAAI,MAAM;AAAA,MACxB;AAEA,aAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IAC5B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AACxB,UAAI,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AAC/B,UAAI,QAAQ,SAAU,GAAG;AACxB,gBAAQ,IAAI,KAAK,IAAI,OAAO,IAAI;AAAA,MACjC;AAEA,UAAI,SAAS,GAAG;AACf,YAAI,IAAI;AAAA,MACT,OAAO;AACN,YAAI,OAAO;AACX,eAAO,MAAM,CAAC;AACd,eAAO,MAAM,CAAC;AACd,eAAO,MAAM,CAAC;AAEd,YAAI,MAAM,GAAG;AACZ,cAAI,OAAO;AAAA,QACZ,WAAW,MAAM,GAAG;AACnB,cAAK,IAAI,IAAK,OAAO;AAAA,QACtB,WAAW,MAAM,GAAG;AACnB,cAAK,IAAI,IAAK,OAAO;AAAA,QACtB;AACA,YAAI,IAAI,GAAG;AACV,eAAK;AAAA,QACN,WAAW,IAAI,GAAG;AACjB,eAAK;AAAA,QACN;AAAA,MACD;AAEA,aAAO;AAAA,QACN,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACL;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AAC9B,UAAI,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAE5C,UAAI,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAE5C,aAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IAC5B;AAEA,YAAQ,IAAI,OAAO,SAAU,KAAK;AACjC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAChC,WAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AAC7B,WAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AAC7B,WAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AAE7B,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAC3C;AAKA,aAAS,oBAAoB,GAAG,GAAG;AAClC,aACC,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IACvB,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IACvB,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC;AAAA,IAEzB;AAEA,YAAQ,IAAI,UAAU,SAAU,KAAK;AACpC,UAAI,WAAW,gBAAgB,GAAG;AAClC,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAEA,UAAI,yBAAyB;AAC7B,UAAI;AAEJ,eAAS,WAAW,aAAa;AAChC,YAAI,YAAY,eAAe,OAAO,GAAG;AACxC,cAAI,QAAQ,YAAY,OAAO;AAG/B,cAAI,WAAW,oBAAoB,KAAK,KAAK;AAG7C,cAAI,WAAW,wBAAwB;AACtC,qCAAyB;AACzB,oCAAwB;AAAA,UACzB;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,QAAQ,MAAM,SAAU,SAAS;AACxC,aAAO,YAAY,OAAO;AAAA,IAC3B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AAGjB,UAAI,IAAI,UAAU,KAAK,KAAM,IAAI,SAAS,OAAQ,GAAG,IAAK,IAAI;AAC9D,UAAI,IAAI,UAAU,KAAK,KAAM,IAAI,SAAS,OAAQ,GAAG,IAAK,IAAI;AAC9D,UAAI,IAAI,UAAU,KAAK,KAAM,IAAI,SAAS,OAAQ,GAAG,IAAK,IAAI;AAE9D,UAAI,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAC3C,UAAI,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAC3C,UAAI,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAE3C,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC7B,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK;AACL,WAAK;AACL,WAAK;AAEL,UAAI,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC,IAAK,QAAQ,IAAM,KAAK;AAC5D,UAAI,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC,IAAK,QAAQ,IAAM,KAAK;AAC5D,UAAI,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC,IAAK,QAAQ,IAAM,KAAK;AAE5D,UAAK,MAAM,IAAK;AAChB,UAAI,OAAO,IAAI;AACf,UAAI,OAAO,IAAI;AAEf,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,MAAM,GAAG;AACZ,cAAM,IAAI;AACV,eAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB;AAEA,UAAI,IAAI,KAAK;AACZ,aAAK,KAAK,IAAI;AAAA,MACf,OAAO;AACN,aAAK,IAAI,IAAI,IAAI;AAAA,MAClB;AAEA,WAAK,IAAI,IAAI;AAEb,YAAM,CAAC,GAAG,GAAG,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,aAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AACvB,YAAI,KAAK,GAAG;AACX;AAAA,QACD;AACA,YAAI,KAAK,GAAG;AACX;AAAA,QACD;AAEA,YAAI,IAAI,KAAK,GAAG;AACf,gBAAM,MAAM,KAAK,MAAM,IAAI;AAAA,QAC5B,WAAW,IAAI,KAAK,GAAG;AACtB,gBAAM;AAAA,QACP,WAAW,IAAI,KAAK,GAAG;AACtB,gBAAM,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM;AAAA,QACvC,OAAO;AACN,gBAAM;AAAA,QACP;AAEA,YAAI,CAAC,IAAI,MAAM;AAAA,MAChB;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,OAAO;AACX,UAAI,OAAO,KAAK,IAAI,GAAG,IAAI;AAC3B,UAAI;AACJ,UAAI;AAEJ,WAAK;AACL,WAAM,KAAK,IAAK,IAAI,IAAI;AACxB,cAAQ,QAAQ,IAAI,OAAO,IAAI;AAC/B,WAAK,IAAI,KAAK;AACd,WAAK,MAAM,IAAK,IAAI,QAAS,OAAO,QAAS,IAAI,KAAM,IAAI;AAE3D,aAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,IAC7B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,KAAK,KAAK,MAAM,CAAC,IAAI;AAEzB,UAAI,IAAI,IAAI,KAAK,MAAM,CAAC;AACxB,UAAI,IAAI,MAAM,KAAK,IAAI;AACvB,UAAI,IAAI,MAAM,KAAK,IAAK,IAAI;AAC5B,UAAI,IAAI,MAAM,KAAK,IAAK,KAAK,IAAI;AACjC,WAAK;AAEL,cAAQ,IAAI;AAAA,QACX,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,OAAO,KAAK,IAAI,GAAG,IAAI;AAC3B,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK,IAAI,KAAK;AACd,cAAQ,IAAI,KAAK;AACjB,WAAK,IAAI;AACT,YAAO,QAAQ,IAAK,OAAO,IAAI;AAC/B,WAAK,MAAM;AACX,WAAK;AAEL,aAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,IAC7B;AAGA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,KAAK,IAAI,CAAC,IAAI;AAClB,UAAI,KAAK,IAAI,CAAC,IAAI;AAClB,UAAI,QAAQ,KAAK;AACjB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAGJ,UAAI,QAAQ,GAAG;AACd,cAAM;AACN,cAAM;AAAA,MACP;AAEA,UAAI,KAAK,MAAM,IAAI,CAAC;AACpB,UAAI,IAAI;AACR,UAAI,IAAI,IAAI;AAEZ,WAAK,IAAI,OAAU,GAAG;AACrB,YAAI,IAAI;AAAA,MACT;AAEA,UAAI,KAAK,KAAK,IAAI;AAElB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,cAAQ,GAAG;AAAA,QACV;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAG,cAAI;AAAG,cAAI;AAAG,cAAI;AAAI;AAAA,QAC9B,KAAK;AAAG,cAAI;AAAG,cAAI;AAAG,cAAI;AAAI;AAAA,QAC9B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAG,cAAI;AAAG;AAAA,QAC9B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAG,cAAI;AAAG;AAAA,QAC9B,KAAK;AAAG,cAAI;AAAG,cAAI;AAAI,cAAI;AAAG;AAAA,QAC9B,KAAK;AAAG,cAAI;AAAG,cAAI;AAAI,cAAI;AAAG;AAAA,MAC/B;AAEA,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,UAAI,IAAI,KAAK,CAAC,IAAI;AAClB,UAAI,IAAI,KAAK,CAAC,IAAI;AAClB,UAAI,IAAI,KAAK,CAAC,IAAI;AAClB,UAAI,IAAI,KAAK,CAAC,IAAI;AAClB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AACnC,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AACnC,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AAEnC,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAK,IAAI,SAAW,IAAI,UAAY,IAAI;AACxC,UAAK,IAAI,UAAY,IAAI,SAAW,IAAI;AACxC,UAAK,IAAI,SAAW,IAAI,SAAY,IAAI;AAGxC,UAAI,IAAI,WACH,QAAQ,KAAK,IAAI,GAAG,IAAM,GAAG,IAAK,QACpC,IAAI;AAEP,UAAI,IAAI,WACH,QAAQ,KAAK,IAAI,GAAG,IAAM,GAAG,IAAK,QACpC,IAAI;AAEP,UAAI,IAAI,WACH,QAAQ,KAAK,IAAI,GAAG,IAAM,GAAG,IAAK,QACpC,IAAI;AAEP,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAE9B,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK;AACL,WAAK;AACL,WAAK;AAEL,UAAI,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC,IAAK,QAAQ,IAAM,KAAK;AAC5D,UAAI,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC,IAAK,QAAQ,IAAM,KAAK;AAC5D,UAAI,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC,IAAK,QAAQ,IAAM,KAAK;AAE5D,UAAK,MAAM,IAAK;AAChB,UAAI,OAAO,IAAI;AACf,UAAI,OAAO,IAAI;AAEf,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK,IAAI,MAAM;AACf,UAAI,IAAI,MAAM;AACd,UAAI,IAAI,IAAI;AAEZ,UAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,UAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,UAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAC1C,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAC1C,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAE1C,WAAK;AACL,WAAK;AACL,WAAK;AAEL,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK,KAAK,MAAM,GAAG,CAAC;AACpB,UAAI,KAAK,MAAM,IAAI,KAAK;AAExB,UAAI,IAAI,GAAG;AACV,aAAK;AAAA,MACN;AAEA,UAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAE3B,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK,IAAI,MAAM,IAAI,KAAK;AACxB,UAAI,IAAI,KAAK,IAAI,EAAE;AACnB,UAAI,IAAI,KAAK,IAAI,EAAE;AAEnB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,SAAS,SAAU,MAAM;AACpC,UAAI,IAAI,KAAK,CAAC;AACd,UAAI,IAAI,KAAK,CAAC;AACd,UAAI,IAAI,KAAK,CAAC;AACd,UAAI,QAAQ,KAAK,YAAY,UAAU,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,EAAE,CAAC;AAEnE,cAAQ,KAAK,MAAM,QAAQ,EAAE;AAE7B,UAAI,UAAU,GAAG;AAChB,eAAO;AAAA,MACR;AAEA,UAAI,OAAO,MACN,KAAK,MAAM,IAAI,GAAG,KAAK,IACxB,KAAK,MAAM,IAAI,GAAG,KAAK,IACxB,KAAK,MAAM,IAAI,GAAG;AAErB,UAAI,UAAU,GAAG;AAChB,gBAAQ;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,SAAS,SAAU,MAAM;AAGpC,aAAO,QAAQ,IAAI,OAAO,QAAQ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,IACzD;AAEA,YAAQ,IAAI,UAAU,SAAU,MAAM;AACrC,UAAI,IAAI,KAAK,CAAC;AACd,UAAI,IAAI,KAAK,CAAC;AACd,UAAI,IAAI,KAAK,CAAC;AAId,UAAI,MAAM,KAAK,MAAM,GAAG;AACvB,YAAI,IAAI,GAAG;AACV,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,KAAK;AACZ,iBAAO;AAAA,QACR;AAEA,eAAO,KAAK,OAAQ,IAAI,KAAK,MAAO,EAAE,IAAI;AAAA,MAC3C;AAEA,UAAI,OAAO,KACP,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,IAC3B,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAC3B,KAAK,MAAM,IAAI,MAAM,CAAC;AAEzB,aAAO;AAAA,IACR;AAEA,YAAQ,OAAO,MAAM,SAAU,MAAM;AACpC,UAAI,QAAQ,OAAO;AAGnB,UAAI,UAAU,KAAK,UAAU,GAAG;AAC/B,YAAI,OAAO,IAAI;AACd,mBAAS;AAAA,QACV;AAEA,gBAAQ,QAAQ,OAAO;AAEvB,eAAO,CAAC,OAAO,OAAO,KAAK;AAAA,MAC5B;AAEA,UAAI,QAAQ,CAAC,EAAE,OAAO,MAAM,KAAK;AACjC,UAAI,KAAM,QAAQ,KAAK,OAAQ;AAC/B,UAAI,KAAO,SAAS,IAAK,KAAK,OAAQ;AACtC,UAAI,KAAO,SAAS,IAAK,KAAK,OAAQ;AAEtC,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,QAAQ,MAAM,SAAU,MAAM;AAErC,UAAI,QAAQ,KAAK;AAChB,YAAI,KAAK,OAAO,OAAO,KAAK;AAC5B,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MAChB;AAEA,cAAQ;AAER,UAAI;AACJ,UAAI,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI,IAAI;AACpC,UAAI,IAAI,KAAK,OAAO,MAAM,OAAO,MAAM,CAAC,IAAI,IAAI;AAChD,UAAI,IAAK,MAAM,IAAK,IAAI;AAExB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,MAAM;AACjC,UAAI,YAAY,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,QAAS,QAC1C,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,QAAS,MAChC,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI;AAE1B,UAAI,SAAS,QAAQ,SAAS,EAAE,EAAE,YAAY;AAC9C,aAAO,SAAS,UAAU,OAAO,MAAM,IAAI;AAAA,IAC5C;AAEA,YAAQ,IAAI,MAAM,SAAU,MAAM;AACjC,UAAI,QAAQ,KAAK,SAAS,EAAE,EAAE,MAAM,0BAA0B;AAC9D,UAAI,CAAC,OAAO;AACX,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MAChB;AAEA,UAAI,cAAc,MAAM,CAAC;AAEzB,UAAI,MAAM,CAAC,EAAE,WAAW,GAAG;AAC1B,sBAAc,YAAY,MAAM,EAAE,EAAE,IAAI,SAAU,MAAM;AACvD,iBAAO,OAAO;AAAA,QACf,CAAC,EAAE,KAAK,EAAE;AAAA,MACX;AAEA,UAAI,UAAU,SAAS,aAAa,EAAE;AACtC,UAAI,IAAK,WAAW,KAAM;AAC1B,UAAI,IAAK,WAAW,IAAK;AACzB,UAAI,IAAI,UAAU;AAElB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACpC,UAAI,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACpC,UAAI,SAAU,MAAM;AACpB,UAAI;AACJ,UAAI;AAEJ,UAAI,SAAS,GAAG;AACf,oBAAY,OAAO,IAAI;AAAA,MACxB,OAAO;AACN,oBAAY;AAAA,MACb;AAEA,UAAI,UAAU,GAAG;AAChB,cAAM;AAAA,MACP,WACI,QAAQ,GAAG;AACd,eAAQ,IAAI,KAAK,SAAU;AAAA,MAC5B,WACI,QAAQ,GAAG;AACd,cAAM,KAAK,IAAI,KAAK;AAAA,MACrB,OAAO;AACN,cAAM,KAAK,IAAI,KAAK,SAAS;AAAA,MAC9B;AAEA,aAAO;AACP,aAAO;AAEP,aAAO,CAAC,MAAM,KAAK,SAAS,KAAK,YAAY,GAAG;AAAA,IACjD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI;AACR,UAAI,IAAI;AAER,UAAI,IAAI,KAAK;AACZ,YAAI,IAAM,IAAI;AAAA,MACf,OAAO;AACN,YAAI,IAAM,KAAK,IAAM;AAAA,MACtB;AAEA,UAAI,IAAI,GAAK;AACZ,aAAK,IAAI,MAAM,MAAM,IAAM;AAAA,MAC5B;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AAEjB,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI;AAER,UAAI,IAAI,GAAK;AACZ,aAAK,IAAI,MAAM,IAAI;AAAA,MACpB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AAEjB,UAAI,MAAM,GAAK;AACd,eAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,MAClC;AAEA,UAAI,OAAO,CAAC,GAAG,GAAG,CAAC;AACnB,UAAI,KAAM,IAAI,IAAK;AACnB,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,IAAI;AACZ,UAAI,KAAK;AAET,cAAQ,KAAK,MAAM,EAAE,GAAG;AAAA,QACvB,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC;AACC,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAA,MACtC;AAEA,YAAM,IAAM,KAAK;AAEjB,aAAO;AAAA,SACL,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,SACpB,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,SACpB,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,MACtB;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AAEjB,UAAI,IAAI,IAAI,KAAK,IAAM;AACvB,UAAI,IAAI;AAER,UAAI,IAAI,GAAK;AACZ,YAAI,IAAI;AAAA,MACT;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AAEjB,UAAI,IAAI,KAAK,IAAM,KAAK,MAAM;AAC9B,UAAI,IAAI;AAER,UAAI,IAAI,KAAO,IAAI,KAAK;AACvB,YAAI,KAAK,IAAI;AAAA,MACd,WACI,KAAK,OAAO,IAAI,GAAK;AACxB,YAAI,KAAK,KAAK,IAAI;AAAA,MACnB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,KAAK,IAAM;AACvB,aAAO,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG;AAAA,IAC7C;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI;AAER,UAAI,IAAI,GAAG;AACV,aAAK,IAAI,MAAM,IAAI;AAAA,MACpB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,MAAM,MAAM,SAAU,OAAO;AACpC,aAAO,CAAE,MAAM,CAAC,IAAI,QAAS,KAAM,MAAM,CAAC,IAAI,QAAS,KAAM,MAAM,CAAC,IAAI,QAAS,GAAG;AAAA,IACrF;AAEA,YAAQ,IAAI,QAAQ,SAAU,KAAK;AAClC,aAAO,CAAE,IAAI,CAAC,IAAI,MAAO,OAAQ,IAAI,CAAC,IAAI,MAAO,OAAQ,IAAI,CAAC,IAAI,MAAO,KAAK;AAAA,IAC/E;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,KAAK,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,MAAM,GAAG;AAAA,IACtE;AAEA,YAAQ,KAAK,MAAM,QAAQ,KAAK,MAAM,SAAU,MAAM;AACrD,aAAO,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,IACtB;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,IACxB;AAEA,YAAQ,KAAK,OAAO,SAAU,MAAM;AACnC,aAAO,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,IACzB;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AAAA,IACtB;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,UAAI,MAAM,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI;AAC5C,UAAI,WAAW,OAAO,OAAO,OAAO,KAAK;AAEzC,UAAI,SAAS,QAAQ,SAAS,EAAE,EAAE,YAAY;AAC9C,aAAO,SAAS,UAAU,OAAO,MAAM,IAAI;AAAA,IAC5C;AAEA,YAAQ,IAAI,OAAO,SAAU,KAAK;AACjC,UAAI,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AACvC,aAAO,CAAC,MAAM,MAAM,GAAG;AAAA,IACxB;AAAA;AAAA;;;ACn2BA;AAAA;AAAA,QAAI,cAAc;AAalB,aAAS,aAAa;AACrB,UAAI,QAAQ,CAAC;AAEb,UAAI,SAAS,OAAO,KAAK,WAAW;AAEpC,eAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,cAAM,OAAO,CAAC,CAAC,IAAI;AAAA;AAAA;AAAA,UAGlB,UAAU;AAAA,UACV,QAAQ;AAAA,QACT;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAGA,aAAS,UAAU,WAAW;AAC7B,UAAI,QAAQ,WAAW;AACvB,UAAI,QAAQ,CAAC,SAAS;AAEtB,YAAM,SAAS,EAAE,WAAW;AAE5B,aAAO,MAAM,QAAQ;AACpB,YAAI,UAAU,MAAM,IAAI;AACxB,YAAI,YAAY,OAAO,KAAK,YAAY,OAAO,CAAC;AAEhD,iBAAS,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AACrD,cAAI,WAAW,UAAU,CAAC;AAC1B,cAAI,OAAO,MAAM,QAAQ;AAEzB,cAAI,KAAK,aAAa,IAAI;AACzB,iBAAK,WAAW,MAAM,OAAO,EAAE,WAAW;AAC1C,iBAAK,SAAS;AACd,kBAAM,QAAQ,QAAQ;AAAA,UACvB;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,KAAK,MAAM,IAAI;AACvB,aAAO,SAAU,MAAM;AACtB,eAAO,GAAG,KAAK,IAAI,CAAC;AAAA,MACrB;AAAA,IACD;AAEA,aAAS,eAAe,SAAS,OAAO;AACvC,UAAI,OAAO,CAAC,MAAM,OAAO,EAAE,QAAQ,OAAO;AAC1C,UAAI,KAAK,YAAY,MAAM,OAAO,EAAE,MAAM,EAAE,OAAO;AAEnD,UAAI,MAAM,MAAM,OAAO,EAAE;AACzB,aAAO,MAAM,GAAG,EAAE,QAAQ;AACzB,aAAK,QAAQ,MAAM,GAAG,EAAE,MAAM;AAC9B,aAAK,KAAK,YAAY,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG,GAAG,EAAE;AACjD,cAAM,MAAM,GAAG,EAAE;AAAA,MAClB;AAEA,SAAG,aAAa;AAChB,aAAO;AAAA,IACR;AAEA,WAAO,UAAU,SAAU,WAAW;AACrC,UAAI,QAAQ,UAAU,SAAS;AAC/B,UAAI,aAAa,CAAC;AAElB,UAAI,SAAS,OAAO,KAAK,KAAK;AAC9B,eAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,YAAI,UAAU,OAAO,CAAC;AACtB,YAAI,OAAO,MAAM,OAAO;AAExB,YAAI,KAAK,WAAW,MAAM;AAEzB;AAAA,QACD;AAEA,mBAAW,OAAO,IAAI,eAAe,SAAS,KAAK;AAAA,MACpD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC/FA;AAAA;AAAA,QAAI,cAAc;AAClB,QAAI,QAAQ;AAEZ,QAAI,UAAU,CAAC;AAEf,QAAI,SAAS,OAAO,KAAK,WAAW;AAEpC,aAAS,QAAQ,IAAI;AACpB,UAAI,YAAY,SAAU,MAAM;AAC/B,YAAI,SAAS,UAAa,SAAS,MAAM;AACxC,iBAAO;AAAA,QACR;AAEA,YAAI,UAAU,SAAS,GAAG;AACzB,iBAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAAA,QAC5C;AAEA,eAAO,GAAG,IAAI;AAAA,MACf;AAGA,UAAI,gBAAgB,IAAI;AACvB,kBAAU,aAAa,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,YAAY,IAAI;AACxB,UAAI,YAAY,SAAU,MAAM;AAC/B,YAAI,SAAS,UAAa,SAAS,MAAM;AACxC,iBAAO;AAAA,QACR;AAEA,YAAI,UAAU,SAAS,GAAG;AACzB,iBAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAAA,QAC5C;AAEA,YAAI,SAAS,GAAG,IAAI;AAKpB,YAAI,OAAO,WAAW,UAAU;AAC/B,mBAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,mBAAO,CAAC,IAAI,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,UACjC;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,gBAAgB,IAAI;AACvB,kBAAU,aAAa,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,QAAQ,SAAU,WAAW;AACnC,cAAQ,SAAS,IAAI,CAAC;AAEtB,aAAO,eAAe,QAAQ,SAAS,GAAG,YAAY,EAAC,OAAO,YAAY,SAAS,EAAE,SAAQ,CAAC;AAC9F,aAAO,eAAe,QAAQ,SAAS,GAAG,UAAU,EAAC,OAAO,YAAY,SAAS,EAAE,OAAM,CAAC;AAE1F,UAAI,SAAS,MAAM,SAAS;AAC5B,UAAI,cAAc,OAAO,KAAK,MAAM;AAEpC,kBAAY,QAAQ,SAAU,SAAS;AACtC,YAAI,KAAK,OAAO,OAAO;AAEvB,gBAAQ,SAAS,EAAE,OAAO,IAAI,YAAY,EAAE;AAC5C,gBAAQ,SAAS,EAAE,OAAO,EAAE,MAAM,QAAQ,EAAE;AAAA,MAC7C,CAAC;AAAA,IACF,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;AC7EjB;AAAA;AAAA;AAEA,QAAI,cAAc;AAClB,QAAI,UAAU;AAEd,QAAI,SAAS,CAAC,EAAE;AAEhB,QAAI,gBAAgB;AAAA;AAAA,MAEnB;AAAA;AAAA,MAGA;AAAA;AAAA,MAGA;AAAA,IACD;AAEA,QAAI,kBAAkB,CAAC;AACvB,WAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,OAAO;AAC7C,sBAAgB,OAAO,KAAK,QAAQ,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI;AAAA,IACvE,CAAC;AAED,QAAI,WAAW,CAAC;AAEhB,aAAS,MAAM,KAAK,OAAO;AAC1B,UAAI,EAAE,gBAAgB,QAAQ;AAC7B,eAAO,IAAI,MAAM,KAAK,KAAK;AAAA,MAC5B;AAEA,UAAI,SAAS,SAAS,eAAe;AACpC,gBAAQ;AAAA,MACT;AAEA,UAAI,SAAS,EAAE,SAAS,UAAU;AACjC,cAAM,IAAI,MAAM,oBAAoB,KAAK;AAAA,MAC1C;AAEA,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,MAAM;AAChB,aAAK,QAAQ;AACb,aAAK,QAAQ,CAAC,GAAG,GAAG,CAAC;AACrB,aAAK,SAAS;AAAA,MACf,WAAW,eAAe,OAAO;AAChC,aAAK,QAAQ,IAAI;AACjB,aAAK,QAAQ,IAAI,MAAM,MAAM;AAC7B,aAAK,SAAS,IAAI;AAAA,MACnB,WAAW,OAAO,QAAQ,UAAU;AACnC,YAAI,SAAS,YAAY,IAAI,GAAG;AAChC,YAAI,WAAW,MAAM;AACpB,gBAAM,IAAI,MAAM,wCAAwC,GAAG;AAAA,QAC5D;AAEA,aAAK,QAAQ,OAAO;AACpB,mBAAW,QAAQ,KAAK,KAAK,EAAE;AAC/B,aAAK,QAAQ,OAAO,MAAM,MAAM,GAAG,QAAQ;AAC3C,aAAK,SAAS,OAAO,OAAO,MAAM,QAAQ,MAAM,WAAW,OAAO,MAAM,QAAQ,IAAI;AAAA,MACrF,WAAW,IAAI,QAAQ;AACtB,aAAK,QAAQ,SAAS;AACtB,mBAAW,QAAQ,KAAK,KAAK,EAAE;AAC/B,YAAI,SAAS,OAAO,KAAK,KAAK,GAAG,QAAQ;AACzC,aAAK,QAAQ,UAAU,QAAQ,QAAQ;AACvC,aAAK,SAAS,OAAO,IAAI,QAAQ,MAAM,WAAW,IAAI,QAAQ,IAAI;AAAA,MACnE,WAAW,OAAO,QAAQ,UAAU;AAEnC,eAAO;AACP,aAAK,QAAQ;AACb,aAAK,QAAQ;AAAA,UACX,OAAO,KAAM;AAAA,UACb,OAAO,IAAK;AAAA,UACb,MAAM;AAAA,QACP;AACA,aAAK,SAAS;AAAA,MACf,OAAO;AACN,aAAK,SAAS;AAEd,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,YAAI,WAAW,KAAK;AACnB,eAAK,OAAO,KAAK,QAAQ,OAAO,GAAG,CAAC;AACpC,eAAK,SAAS,OAAO,IAAI,UAAU,WAAW,IAAI,QAAQ;AAAA,QAC3D;AAEA,YAAI,aAAa,KAAK,KAAK,EAAE,KAAK,EAAE;AACpC,YAAI,EAAE,cAAc,kBAAkB;AACrC,gBAAM,IAAI,MAAM,wCAAwC,KAAK,UAAU,GAAG,CAAC;AAAA,QAC5E;AAEA,aAAK,QAAQ,gBAAgB,UAAU;AAEvC,YAAI,SAAS,QAAQ,KAAK,KAAK,EAAE;AACjC,YAAI,QAAQ,CAAC;AACb,aAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACnC,gBAAM,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC;AAAA,QAC1B;AAEA,aAAK,QAAQ,UAAU,KAAK;AAAA,MAC7B;AAGA,UAAI,SAAS,KAAK,KAAK,GAAG;AACzB,mBAAW,QAAQ,KAAK,KAAK,EAAE;AAC/B,aAAK,IAAI,GAAG,IAAI,UAAU,KAAK;AAC9B,cAAI,QAAQ,SAAS,KAAK,KAAK,EAAE,CAAC;AAClC,cAAI,OAAO;AACV,iBAAK,MAAM,CAAC,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,UACpC;AAAA,QACD;AAAA,MACD;AAEA,WAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,MAAM,CAAC;AAElD,UAAI,OAAO,QAAQ;AAClB,eAAO,OAAO,IAAI;AAAA,MACnB;AAAA,IACD;AAEA,UAAM,YAAY;AAAA,MACjB,UAAU,WAAY;AACrB,eAAO,KAAK,OAAO;AAAA,MACpB;AAAA,MAEA,QAAQ,WAAY;AACnB,eAAO,KAAK,KAAK,KAAK,EAAE;AAAA,MACzB;AAAA,MAEA,QAAQ,SAAU,QAAQ;AACzB,YAAI,OAAO,KAAK,SAAS,YAAY,KAAK,OAAO,KAAK,IAAI;AAC1D,eAAO,KAAK,MAAM,OAAO,WAAW,WAAW,SAAS,CAAC;AACzD,YAAI,OAAO,KAAK,WAAW,IAAI,KAAK,QAAQ,KAAK,MAAM,OAAO,KAAK,MAAM;AACzE,eAAO,YAAY,GAAG,KAAK,KAAK,EAAE,IAAI;AAAA,MACvC;AAAA,MAEA,eAAe,SAAU,QAAQ;AAChC,YAAI,OAAO,KAAK,IAAI,EAAE,MAAM,OAAO,WAAW,WAAW,SAAS,CAAC;AACnE,YAAI,OAAO,KAAK,WAAW,IAAI,KAAK,QAAQ,KAAK,MAAM,OAAO,KAAK,MAAM;AACzE,eAAO,YAAY,GAAG,IAAI,QAAQ,IAAI;AAAA,MACvC;AAAA,MAEA,OAAO,WAAY;AAClB,eAAO,KAAK,WAAW,IAAI,KAAK,MAAM,MAAM,IAAI,KAAK,MAAM,OAAO,KAAK,MAAM;AAAA,MAC9E;AAAA,MAEA,QAAQ,WAAY;AACnB,YAAI,SAAS,CAAC;AACd,YAAI,WAAW,QAAQ,KAAK,KAAK,EAAE;AACnC,YAAI,SAAS,QAAQ,KAAK,KAAK,EAAE;AAEjC,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAClC,iBAAO,OAAO,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,QACjC;AAEA,YAAI,KAAK,WAAW,GAAG;AACtB,iBAAO,QAAQ,KAAK;AAAA,QACrB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,WAAW,WAAY;AACtB,YAAI,MAAM,KAAK,IAAI,EAAE;AACrB,YAAI,CAAC,KAAK;AACV,YAAI,CAAC,KAAK;AACV,YAAI,CAAC,KAAK;AAEV,YAAI,KAAK,WAAW,GAAG;AACtB,cAAI,KAAK,KAAK,MAAM;AAAA,QACrB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,YAAY,WAAY;AACvB,YAAI,MAAM,KAAK,IAAI,EAAE,OAAO;AAC5B,YAAI,KAAK;AACT,YAAI,KAAK;AACT,YAAI,KAAK;AAET,YAAI,KAAK,WAAW,GAAG;AACtB,cAAI,QAAQ,KAAK;AAAA,QAClB;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,OAAO,SAAU,QAAQ;AACxB,iBAAS,KAAK,IAAI,UAAU,GAAG,CAAC;AAChC,eAAO,IAAI,MAAM,KAAK,MAAM,IAAI,aAAa,MAAM,CAAC,EAAE,OAAO,KAAK,MAAM,GAAG,KAAK,KAAK;AAAA,MACtF;AAAA,MAEA,OAAO,SAAU,KAAK;AACrB,YAAI,UAAU,QAAQ;AACrB,iBAAO,IAAI,MAAM,KAAK,MAAM,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,KAAK;AAAA,QAC9E;AAEA,eAAO,KAAK;AAAA,MACb;AAAA;AAAA,MAGA,KAAK,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAChC,OAAO,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAClC,MAAM,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEjC,KAAK,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,GAAG,SAAU,KAAK;AAAE,gBAAS,MAAM,MAAO,OAAO;AAAA,MAAK,CAAC;AAAA;AAAA,MAExG,aAAa,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MACxC,WAAW,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEtC,aAAa,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MACxC,OAAO,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAElC,QAAQ,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MACnC,MAAM,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEjC,OAAO,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAClC,QAAQ,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAEnC,MAAM,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MAClC,SAAS,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MACrC,QAAQ,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MACpC,OAAO,OAAO,QAAQ,GAAG,MAAM,GAAG,CAAC;AAAA,MAEnC,GAAG,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAC9B,GAAG,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAC9B,GAAG,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAE9B,GAAG,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,MAC9B,GAAG,OAAO,OAAO,CAAC;AAAA,MAClB,GAAG,OAAO,OAAO,CAAC;AAAA,MAElB,SAAS,SAAU,KAAK;AACvB,YAAI,UAAU,QAAQ;AACrB,iBAAO,IAAI,MAAM,GAAG;AAAA,QACrB;AAEA,eAAO,QAAQ,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK;AAAA,MAC9C;AAAA,MAEA,KAAK,SAAU,KAAK;AACnB,YAAI,UAAU,QAAQ;AACrB,iBAAO,IAAI,MAAM,GAAG;AAAA,QACrB;AAEA,eAAO,YAAY,GAAG,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,KAAK;AAAA,MACnD;AAAA,MAEA,WAAW,WAAY;AACtB,YAAI,MAAM,KAAK,IAAI,EAAE;AACrB,gBAAS,IAAI,CAAC,IAAI,QAAS,MAAQ,IAAI,CAAC,IAAI,QAAS,IAAM,IAAI,CAAC,IAAI;AAAA,MACrE;AAAA,MAEA,YAAY,WAAY;AAEvB,YAAI,MAAM,KAAK,IAAI,EAAE;AAErB,YAAI,MAAM,CAAC;AACX,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,cAAI,OAAO,IAAI,CAAC,IAAI;AACpB,cAAI,CAAC,IAAK,QAAQ,UAAW,OAAO,QAAQ,KAAK,KAAM,OAAO,SAAS,OAAQ,GAAG;AAAA,QACnF;AAEA,eAAO,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC;AAAA,MAC1D;AAAA,MAEA,UAAU,SAAU,QAAQ;AAE3B,YAAI,OAAO,KAAK,WAAW;AAC3B,YAAI,OAAO,OAAO,WAAW;AAE7B,YAAI,OAAO,MAAM;AAChB,kBAAQ,OAAO,SAAS,OAAO;AAAA,QAChC;AAEA,gBAAQ,OAAO,SAAS,OAAO;AAAA,MAChC;AAAA,MAEA,OAAO,SAAU,QAAQ;AACxB,YAAI,gBAAgB,KAAK,SAAS,MAAM;AACxC,YAAI,iBAAiB,KAAK;AACzB,iBAAO;AAAA,QACR;AAEA,eAAQ,iBAAiB,MAAO,OAAO;AAAA,MACxC;AAAA,MAEA,QAAQ,WAAY;AAEnB,YAAI,MAAM,KAAK,IAAI,EAAE;AACrB,YAAI,OAAO,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,OAAO;AACzD,eAAO,MAAM;AAAA,MACd;AAAA,MAEA,SAAS,WAAY;AACpB,eAAO,CAAC,KAAK,OAAO;AAAA,MACrB;AAAA,MAEA,QAAQ,WAAY;AACnB,YAAI,MAAM,KAAK,IAAI;AACnB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,cAAI,MAAM,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,QACjC;AACA,eAAO;AAAA,MACR;AAAA,MAEA,SAAS,SAAU,OAAO;AACzB,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,SAAU,OAAO;AACxB,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,UAAU,SAAU,OAAO;AAC1B,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,YAAY,SAAU,OAAO;AAC5B,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,QAAQ,SAAU,OAAO;AACxB,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,SAAS,SAAU,OAAO;AACzB,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI;AAC/B,eAAO;AAAA,MACR;AAAA,MAEA,WAAW,WAAY;AAEtB,YAAI,MAAM,KAAK,IAAI,EAAE;AACrB,YAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI;AAClD,eAAO,MAAM,IAAI,KAAK,KAAK,GAAG;AAAA,MAC/B;AAAA,MAEA,MAAM,SAAU,OAAO;AACtB,eAAO,KAAK,MAAM,KAAK,SAAU,KAAK,SAAS,KAAM;AAAA,MACtD;AAAA,MAEA,SAAS,SAAU,OAAO;AACzB,eAAO,KAAK,MAAM,KAAK,SAAU,KAAK,SAAS,KAAM;AAAA,MACtD;AAAA,MAEA,QAAQ,SAAU,SAAS;AAC1B,YAAI,MAAM,KAAK,IAAI;AACnB,YAAI,MAAM,IAAI,MAAM,CAAC;AACrB,eAAO,MAAM,WAAW;AACxB,cAAM,MAAM,IAAI,MAAM,MAAM;AAC5B,YAAI,MAAM,CAAC,IAAI;AACf,eAAO;AAAA,MACR;AAAA,MAEA,KAAK,SAAU,YAAY,QAAQ;AAGlC,YAAI,CAAC,cAAc,CAAC,WAAW,KAAK;AACnC,gBAAM,IAAI,MAAM,2EAA2E,OAAO,UAAU;AAAA,QAC7G;AACA,YAAI,SAAS,WAAW,IAAI;AAC5B,YAAI,SAAS,KAAK,IAAI;AACtB,YAAI,IAAI,WAAW,SAAY,MAAM;AAErC,YAAI,IAAI,IAAI,IAAI;AAChB,YAAI,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;AAEtC,YAAI,OAAQ,IAAI,MAAM,KAAM,KAAK,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK;AAC9D,YAAI,KAAK,IAAI;AAEb,eAAO,MAAM;AAAA,UACX,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI;AAAA,UACpC,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM;AAAA,UACxC,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,KAAK;AAAA,UACtC,OAAO,MAAM,IAAI,IAAI,OAAO,MAAM,KAAK,IAAI;AAAA,QAAE;AAAA,MAChD;AAAA,IACD;AAGA,WAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,OAAO;AAC7C,UAAI,cAAc,QAAQ,KAAK,MAAM,IAAI;AACxC;AAAA,MACD;AAEA,UAAI,WAAW,QAAQ,KAAK,EAAE;AAG9B,YAAM,UAAU,KAAK,IAAI,WAAY;AACpC,YAAI,KAAK,UAAU,OAAO;AACzB,iBAAO,IAAI,MAAM,IAAI;AAAA,QACtB;AAEA,YAAI,UAAU,QAAQ;AACrB,iBAAO,IAAI,MAAM,WAAW,KAAK;AAAA,QAClC;AAEA,YAAI,WAAW,OAAO,UAAU,QAAQ,MAAM,WAAW,WAAW,KAAK;AACzE,eAAO,IAAI,MAAM,YAAY,QAAQ,KAAK,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ,GAAG,KAAK;AAAA,MACjG;AAGA,YAAM,KAAK,IAAI,SAAU,OAAO;AAC/B,YAAI,OAAO,UAAU,UAAU;AAC9B,kBAAQ,UAAU,OAAO,KAAK,SAAS,GAAG,QAAQ;AAAA,QACnD;AACA,eAAO,IAAI,MAAM,OAAO,KAAK;AAAA,MAC9B;AAAA,IACD,CAAC;AAED,aAAS,QAAQ,KAAK,QAAQ;AAC7B,aAAO,OAAO,IAAI,QAAQ,MAAM,CAAC;AAAA,IAClC;AAEA,aAAS,aAAa,QAAQ;AAC7B,aAAO,SAAU,KAAK;AACrB,eAAO,QAAQ,KAAK,MAAM;AAAA,MAC3B;AAAA,IACD;AAEA,aAAS,OAAO,OAAO,SAAS,UAAU;AACzC,cAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAE7C,YAAM,QAAQ,SAAU,GAAG;AAC1B,SAAC,SAAS,CAAC,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI;AAAA,MAChD,CAAC;AAED,cAAQ,MAAM,CAAC;AAEf,aAAO,SAAU,KAAK;AACrB,YAAI;AAEJ,YAAI,UAAU,QAAQ;AACrB,cAAI,UAAU;AACb,kBAAM,SAAS,GAAG;AAAA,UACnB;AAEA,mBAAS,KAAK,KAAK,EAAE;AACrB,iBAAO,MAAM,OAAO,IAAI;AACxB,iBAAO;AAAA,QACR;AAEA,iBAAS,KAAK,KAAK,EAAE,EAAE,MAAM,OAAO;AACpC,YAAI,UAAU;AACb,mBAAS,SAAS,MAAM;AAAA,QACzB;AAEA,eAAO;AAAA,MACR;AAAA,IACD;AAEA,aAAS,MAAM,KAAK;AACnB,aAAO,SAAU,GAAG;AACnB,eAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC;AAAA,MACpC;AAAA,IACD;AAEA,aAAS,YAAY,KAAK;AACzB,aAAO,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG;AAAA,IACvC;AAEA,aAAS,UAAU,KAAK,QAAQ;AAC/B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,YAAI,OAAO,IAAI,CAAC,MAAM,UAAU;AAC/B,cAAI,CAAC,IAAI;AAAA,QACV;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjejB;AAAA;AAAA,QAAM,QAAQ;AAEd,YAAQ,YAAY,SAAS,OAAO;AAClC,aAAO,MAAM,KAAK,EACf,IAAI,EACJ,MAAM,EACN,MACA,KAAK,GAAG;AAAA,IACb;AAEA,QAAM,UAAU,CAAC,OAAO,OAAO,KAAK;AAEpC,aAAS,UAAU,QAAQ;AACzB,UAAI,CAAC,UAAU,QAAQ,QAAQ,MAAM,IAAI,GAAG;AAC1C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,iBAAiB,SAAS,OAAO,QAAQ;AAC/C,YAAM,cAAc,UAAU,MAAM;AAEpC,UAAI,gBAAgB,OAAO;AACzB,eAAO,MAAM,WAAW,EAAE;AAAA,MAC5B;AACA,aAAO,MAAM,WAAW,EAAE,EAAE,MAAM,EAAE,OAAO;AAAA,IAC7C;AAAA;AAAA;;;AC1BA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,EAAE,eAAe,IAAI;AAK3B,aAAS,aAAa,aAAa,GAAG,QAAQ;AAC5C,YAAM,QAAQ,MAAM,WAAW;AAC/B,YAAM,IAAI,MAAM,IAAI;AACpB,YAAM,IAAI,MAAM,YAAY;AAC5B,YAAM,IAAI,MAAM,MAAM;AAEtB,YAAM,UAAU;AAChB,YAAM,oBAAoB;AAC1B,YAAM,oBAAoB;AAE1B,YAAM,WAAW;AACjB,YAAM,WAAW;AAEjB,eAAS,UAAUC,UAASC,IAAG;AAC7B,YAAI;AACJ,YAAI,KAAK,MAAM,KAAK,KAAK;AACvB,gBAAMD,WAAU,IAAI,UAAUC,KAAI,IAAI,UAAUA;AAAA,QAClD,OAAO;AACL,gBAAMD,WAAU,IAAI,UAAUC,KAAI,IAAI,UAAUA;AAAA,QAClD;AACA,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT,WAAW,OAAO,KAAK;AACrB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,MAAM,GAAG;AAAA,MACvB;AAEA,eAAS,iBAAiBD,UAASC,IAAG;AACpC,YAAI;AAEJ,YAAID,UAAS;AACX,0BAAgB,KAAK,oBAAoB,IAAI,KAAM,IAAI,qBAAqB,IAAKC;AAAA,QACnF,OAAO;AACL,0BAAgB,KAAM,oBAAoB,KAAK,IAAKA;AAAA,QACtD;AACA,eAAO;AAAA,MACT;AAEA,eAAS,YAAYD,UAASC,IAAG;AAC/B,eAAOD,WAAU,KAAM,WAAW,KAAK,IAAKC,KAAK,KAAK,WAAW,IAAI,KAAM,IAAI,YAAY,IAAKA;AAAA,MAClG;AAEA,YAAM,UAAU,IAAI;AACpB,YAAM,QAAQ,UAAU,IAAI,IAAI,IAAI;AAEpC,YAAM,WAAW,MAAM,IACnB,QACA,MAAM;AAAA,QACJ,GAAG,UAAU,SAAS,KAAK;AAAA,QAC3B,GAAG,iBAAiB,SAAS,KAAK;AAAA,QAClC,GAAG,YAAY,SAAS,KAAK;AAAA,MAC/B,CAAC;AAEL,aAAO,eAAe,UAAU,MAAM;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/DjB;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,eAAe;AACrB,QAAM,EAAE,eAAe,IAAI;AAK3B,aAAS,iBAAiB,aAAa,GAAG,QAAQ;AAChD,YAAM,aAAa,MAAM,aAAa,aAAa,KAAK,IAAI,CAAC,CAAC;AAC9D,YAAM,kBAAkB,MAAM,WAAW;AAEzC,YAAM,gBAAgB,gBAAgB,IAAI;AAC1C,YAAM,uBAAuB,gBAAgB,YAAY;AACzD,YAAM,YAAY,MAAM;AAAA,QACtB,GAAG,gBAAgB,IAAI;AAAA,QACvB,GAAG,iBAAiB,CAAC;AAAA,QACrB,GAAG,gBAAgB,MAAM;AAAA,MAC3B,CAAC;AAED,YAAM,iBAAiB,UAAU,YAAY;AAC7C,YAAM,OAAO,KAAK,MAAM,iBAAiB,KAAK,CAAC;AAC/C,YAAM,WAAW,KAAK,MAAM,MAAM,kBAAkB,CAAC;AAErD,eAAS,iBAAiB,QAAQ;AAChC,YAAI,SAAS,GAAG;AACd,iBAAO,kBAAkB,IAAI,UAAU;AAAA,QACzC;AACA,YAAI,WAAW,GAAG;AAChB,cAAI,iBAAiB,KAAK,gBAAgB,IAAI;AAC5C,mBAAO,uBAAuB;AAAA,UAChC;AACA,cAAI,iBAAiB,MAAM,gBAAgB,KAAK;AAC9C,mBAAO,uBAAuB;AAAA,UAChC;AACA,cAAI,iBAAiB,OAAO,iBAAiB,KAAK;AAChD,mBAAO,uBAAuB;AAAA,UAChC;AAAA,QACF;AAEA,eAAO,iBAAiB,QAAQ,SAAS;AAAA,MAC3C;AAEA,YAAM,WAAW,MAAM;AAAA,QACrB,GAAG,WAAW,IAAI;AAAA,QAClB,GAAG,iBAAiB,CAAC;AAAA,QACrB,GAAG,WAAW,MAAM;AAAA,MACtB,CAAC;AAED,aAAO,eAAe,UAAU,MAAM;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnDjB;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,mBAAmB;AAYzB,aAAS,SAAS,OAAO,UAAU,CAAC,GAAG;AACrC,YAAM,EAAE,MAAM,MAAM,QAAQ,GAAG,SAAS,MAAM,IAAI;AAElD,UAAI,MAAM;AACR,cAAMC,QAAO,CAAC;AACd,cAAM,OAAO,OAAO,mBAAmB;AACvC,iBAAQ,IAAI,GAAG,KAAK,IAAI,KAAK;AAC3B,UAAAA,MAAK,KAAK,KAAK,OAAO,GAAG,MAAM,CAAC;AAAA,QAClC;AACA,eAAOA;AAAA,MACT;AACA,aAAO,OAAO,iBAAiB,OAAO,OAAO,MAAM,IAAI,aAAa,OAAO,OAAO,MAAM;AAAA,IAC1F;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3BjB;AAAA;AAAA,QAAM,WAAW;AACjB,QAAM,EAAE,UAAU,IAAI;AAEtB,YAAQ,WAAW;AACnB,YAAQ,YAAY;AAEpB,QAAM,YAAY;AAAA,MAChB,KAAK;AAAA,MACL,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAEA,aAAS,kBAAkB;AACzB,YAAM,eAAe,CAAC;AACtB,aAAO,KAAK,SAAS,EAAE,QAAQ,CAAC,QAAQ;AACtC,qBAAa,GAAG,IAAI,CAAC;AACrB,qBAAa,GAAG,EAAE,QAAQ,SAAS,UAAU,GAAG,GAAG,EAAE,MAAM,KAAK,CAAC;AACjE,qBAAa,GAAG,EAAE,OAAO,SAAS,UAAU,GAAG,GAAG,EAAE,MAAM,MAAM,MAAM,KAAK,CAAC;AAC5E,qBAAa,GAAG,EAAE,UAAU,UAAU,GAAG;AAAA,MAC3C,CAAC;AAED,mBAAa,OAAO,CAAC;AACrB,mBAAa,KAAK,QAAQ;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,mBAAa,KAAK,OAAO;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,mBAAa,KAAK,UAAU,aAAa,KAAK,MAAM,CAAC;AAErD,aAAO;AAAA,IACT;AAEA,YAAQ,kBAAkB;AAAA;AAAA;", "names": ["swizzle", "isLight", "i", "list"]}