import{c as N}from"./color-string-Ckj7g19G.js";var q={exports:{}},U={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanche<PERSON>mond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},m=U,E={};for(var x in m)m.hasOwnProperty(x)&&(E[m[x]]=x);var s=q.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var b in s)if(s.hasOwnProperty(b)){if(!("channels"in s[b]))throw new Error("missing channels property: "+b);if(!("labels"in s[b]))throw new Error("missing channel labels property: "+b);if(s[b].labels.length!==s[b].channels)throw new Error("channel and label counts mismatch: "+b);var $=s[b].channels,D=s[b].labels;delete s[b].channels,delete s[b].labels,Object.defineProperty(s[b],"channels",{value:$}),Object.defineProperty(s[b],"labels",{value:D})}s.rgb.hsl=function(r){var a=r[0]/255,n=r[1]/255,e=r[2]/255,t=Math.min(a,n,e),i=Math.max(a,n,e),o=i-t,l,h,v;return i===t?l=0:a===i?l=(n-e)/o:n===i?l=2+(e-a)/o:e===i&&(l=4+(a-n)/o),l=Math.min(l*60,360),l<0&&(l+=360),v=(t+i)/2,i===t?h=0:v<=.5?h=o/(i+t):h=o/(2-i-t),[l,h*100,v*100]};s.rgb.hsv=function(r){var a,n,e,t,i,o=r[0]/255,l=r[1]/255,h=r[2]/255,v=Math.max(o,l,h),g=v-Math.min(o,l,h),y=function(C){return(v-C)/6/g+1/2};return g===0?t=i=0:(i=g/v,a=y(o),n=y(l),e=y(h),o===v?t=e-n:l===v?t=1/3+a-e:h===v&&(t=2/3+n-a),t<0?t+=1:t>1&&(t-=1)),[t*360,i*100,v*100]};s.rgb.hwb=function(r){var a=r[0],n=r[1],e=r[2],t=s.rgb.hsl(r)[0],i=1/255*Math.min(a,Math.min(n,e));return e=1-1/255*Math.max(a,Math.max(n,e)),[t,i*100,e*100]};s.rgb.cmyk=function(r){var a=r[0]/255,n=r[1]/255,e=r[2]/255,t,i,o,l;return l=Math.min(1-a,1-n,1-e),t=(1-a-l)/(1-l)||0,i=(1-n-l)/(1-l)||0,o=(1-e-l)/(1-l)||0,[t*100,i*100,o*100,l*100]};function I(r,a){return Math.pow(r[0]-a[0],2)+Math.pow(r[1]-a[1],2)+Math.pow(r[2]-a[2],2)}s.rgb.keyword=function(r){var a=E[r];if(a)return a;var n=1/0,e;for(var t in m)if(m.hasOwnProperty(t)){var i=m[t],o=I(r,i);o<n&&(n=o,e=t)}return e};s.keyword.rgb=function(r){return m[r]};s.rgb.xyz=function(r){var a=r[0]/255,n=r[1]/255,e=r[2]/255;a=a>.04045?Math.pow((a+.055)/1.055,2.4):a/12.92,n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92,e=e>.04045?Math.pow((e+.055)/1.055,2.4):e/12.92;var t=a*.4124+n*.3576+e*.1805,i=a*.2126+n*.7152+e*.0722,o=a*.0193+n*.1192+e*.9505;return[t*100,i*100,o*100]};s.rgb.lab=function(r){var a=s.rgb.xyz(r),n=a[0],e=a[1],t=a[2],i,o,l;return n/=95.047,e/=100,t/=108.883,n=n>.008856?Math.pow(n,1/3):7.787*n+16/116,e=e>.008856?Math.pow(e,1/3):7.787*e+16/116,t=t>.008856?Math.pow(t,1/3):7.787*t+16/116,i=116*e-16,o=500*(n-e),l=200*(e-t),[i,o,l]};s.hsl.rgb=function(r){var a=r[0]/360,n=r[1]/100,e=r[2]/100,t,i,o,l,h;if(n===0)return h=e*255,[h,h,h];e<.5?i=e*(1+n):i=e+n-e*n,t=2*e-i,l=[0,0,0];for(var v=0;v<3;v++)o=a+1/3*-(v-1),o<0&&o++,o>1&&o--,6*o<1?h=t+(i-t)*6*o:2*o<1?h=i:3*o<2?h=t+(i-t)*(2/3-o)*6:h=t,l[v]=h*255;return l};s.hsl.hsv=function(r){var a=r[0],n=r[1]/100,e=r[2]/100,t=n,i=Math.max(e,.01),o,l;return e*=2,n*=e<=1?e:2-e,t*=i<=1?i:2-i,l=(e+n)/2,o=e===0?2*t/(i+t):2*n/(e+n),[a,o*100,l*100]};s.hsv.rgb=function(r){var a=r[0]/60,n=r[1]/100,e=r[2]/100,t=Math.floor(a)%6,i=a-Math.floor(a),o=255*e*(1-n),l=255*e*(1-n*i),h=255*e*(1-n*(1-i));switch(e*=255,t){case 0:return[e,h,o];case 1:return[l,e,o];case 2:return[o,e,h];case 3:return[o,l,e];case 4:return[h,o,e];case 5:return[e,o,l]}};s.hsv.hsl=function(r){var a=r[0],n=r[1]/100,e=r[2]/100,t=Math.max(e,.01),i,o,l;return l=(2-n)*e,i=(2-n)*t,o=n*t,o/=i<=1?i:2-i,o=o||0,l/=2,[a,o*100,l*100]};s.hwb.rgb=function(r){var a=r[0]/360,n=r[1]/100,e=r[2]/100,t=n+e,i,o,l,h;t>1&&(n/=t,e/=t),i=Math.floor(6*a),o=1-e,l=6*a-i,i&1&&(l=1-l),h=n+l*(o-n);var v,g,y;switch(i){default:case 6:case 0:v=o,g=h,y=n;break;case 1:v=h,g=o,y=n;break;case 2:v=n,g=o,y=h;break;case 3:v=n,g=h,y=o;break;case 4:v=h,g=n,y=o;break;case 5:v=o,g=n,y=h;break}return[v*255,g*255,y*255]};s.cmyk.rgb=function(r){var a=r[0]/100,n=r[1]/100,e=r[2]/100,t=r[3]/100,i,o,l;return i=1-Math.min(1,a*(1-t)+t),o=1-Math.min(1,n*(1-t)+t),l=1-Math.min(1,e*(1-t)+t),[i*255,o*255,l*255]};s.xyz.rgb=function(r){var a=r[0]/100,n=r[1]/100,e=r[2]/100,t,i,o;return t=a*3.2406+n*-1.5372+e*-.4986,i=a*-.9689+n*1.8758+e*.0415,o=a*.0557+n*-.204+e*1.057,t=t>.0031308?1.055*Math.pow(t,1/2.4)-.055:t*12.92,i=i>.0031308?1.055*Math.pow(i,1/2.4)-.055:i*12.92,o=o>.0031308?1.055*Math.pow(o,1/2.4)-.055:o*12.92,t=Math.min(Math.max(0,t),1),i=Math.min(Math.max(0,i),1),o=Math.min(Math.max(0,o),1),[t*255,i*255,o*255]};s.xyz.lab=function(r){var a=r[0],n=r[1],e=r[2],t,i,o;return a/=95.047,n/=100,e/=108.883,a=a>.008856?Math.pow(a,1/3):7.787*a+16/116,n=n>.008856?Math.pow(n,1/3):7.787*n+16/116,e=e>.008856?Math.pow(e,1/3):7.787*e+16/116,t=116*n-16,i=500*(a-n),o=200*(n-e),[t,i,o]};s.lab.xyz=function(r){var a=r[0],n=r[1],e=r[2],t,i,o;i=(a+16)/116,t=n/500+i,o=i-e/200;var l=Math.pow(i,3),h=Math.pow(t,3),v=Math.pow(o,3);return i=l>.008856?l:(i-16/116)/7.787,t=h>.008856?h:(t-16/116)/7.787,o=v>.008856?v:(o-16/116)/7.787,t*=95.047,i*=100,o*=108.883,[t,i,o]};s.lab.lch=function(r){var a=r[0],n=r[1],e=r[2],t,i,o;return t=Math.atan2(e,n),i=t*360/2/Math.PI,i<0&&(i+=360),o=Math.sqrt(n*n+e*e),[a,o,i]};s.lch.lab=function(r){var a=r[0],n=r[1],e=r[2],t,i,o;return o=e/360*2*Math.PI,t=n*Math.cos(o),i=n*Math.sin(o),[a,t,i]};s.rgb.ansi16=function(r){var a=r[0],n=r[1],e=r[2],t=1 in arguments?arguments[1]:s.rgb.hsv(r)[2];if(t=Math.round(t/50),t===0)return 30;var i=30+(Math.round(e/255)<<2|Math.round(n/255)<<1|Math.round(a/255));return t===2&&(i+=60),i};s.hsv.ansi16=function(r){return s.rgb.ansi16(s.hsv.rgb(r),r[2])};s.rgb.ansi256=function(r){var a=r[0],n=r[1],e=r[2];if(a===n&&n===e)return a<8?16:a>248?231:Math.round((a-8)/247*24)+232;var t=16+36*Math.round(a/255*5)+6*Math.round(n/255*5)+Math.round(e/255*5);return t};s.ansi16.rgb=function(r){var a=r%10;if(a===0||a===7)return r>50&&(a+=3.5),a=a/10.5*255,[a,a,a];var n=(~~(r>50)+1)*.5,e=(a&1)*n*255,t=(a>>1&1)*n*255,i=(a>>2&1)*n*255;return[e,t,i]};s.ansi256.rgb=function(r){if(r>=232){var a=(r-232)*10+8;return[a,a,a]}r-=16;var n,e=Math.floor(r/36)/5*255,t=Math.floor((n=r%36)/6)/5*255,i=n%6/5*255;return[e,t,i]};s.rgb.hex=function(r){var a=((Math.round(r[0])&255)<<16)+((Math.round(r[1])&255)<<8)+(Math.round(r[2])&255),n=a.toString(16).toUpperCase();return"000000".substring(n.length)+n};s.hex.rgb=function(r){var a=r.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!a)return[0,0,0];var n=a[0];a[0].length===3&&(n=n.split("").map(function(l){return l+l}).join(""));var e=parseInt(n,16),t=e>>16&255,i=e>>8&255,o=e&255;return[t,i,o]};s.rgb.hcg=function(r){var a=r[0]/255,n=r[1]/255,e=r[2]/255,t=Math.max(Math.max(a,n),e),i=Math.min(Math.min(a,n),e),o=t-i,l,h;return o<1?l=i/(1-o):l=0,o<=0?h=0:t===a?h=(n-e)/o%6:t===n?h=2+(e-a)/o:h=4+(a-n)/o+4,h/=6,h%=1,[h*360,o*100,l*100]};s.hsl.hcg=function(r){var a=r[1]/100,n=r[2]/100,e=1,t=0;return n<.5?e=2*a*n:e=2*a*(1-n),e<1&&(t=(n-.5*e)/(1-e)),[r[0],e*100,t*100]};s.hsv.hcg=function(r){var a=r[1]/100,n=r[2]/100,e=a*n,t=0;return e<1&&(t=(n-e)/(1-e)),[r[0],e*100,t*100]};s.hcg.rgb=function(r){var a=r[0]/360,n=r[1]/100,e=r[2]/100;if(n===0)return[e*255,e*255,e*255];var t=[0,0,0],i=a%1*6,o=i%1,l=1-o,h=0;switch(Math.floor(i)){case 0:t[0]=1,t[1]=o,t[2]=0;break;case 1:t[0]=l,t[1]=1,t[2]=0;break;case 2:t[0]=0,t[1]=1,t[2]=o;break;case 3:t[0]=0,t[1]=l,t[2]=1;break;case 4:t[0]=o,t[1]=0,t[2]=1;break;default:t[0]=1,t[1]=0,t[2]=l}return h=(1-n)*e,[(n*t[0]+h)*255,(n*t[1]+h)*255,(n*t[2]+h)*255]};s.hcg.hsv=function(r){var a=r[1]/100,n=r[2]/100,e=a+n*(1-a),t=0;return e>0&&(t=a/e),[r[0],t*100,e*100]};s.hcg.hsl=function(r){var a=r[1]/100,n=r[2]/100,e=n*(1-a)+.5*a,t=0;return e>0&&e<.5?t=a/(2*e):e>=.5&&e<1&&(t=a/(2*(1-e))),[r[0],t*100,e*100]};s.hcg.hwb=function(r){var a=r[1]/100,n=r[2]/100,e=a+n*(1-a);return[r[0],(e-a)*100,(1-e)*100]};s.hwb.hcg=function(r){var a=r[1]/100,n=r[2]/100,e=1-n,t=e-a,i=0;return t<1&&(i=(e-t)/(1-t)),[r[0],t*100,i*100]};s.apple.rgb=function(r){return[r[0]/65535*255,r[1]/65535*255,r[2]/65535*255]};s.rgb.apple=function(r){return[r[0]/255*65535,r[1]/255*65535,r[2]/255*65535]};s.gray.rgb=function(r){return[r[0]/100*255,r[0]/100*255,r[0]/100*255]};s.gray.hsl=s.gray.hsv=function(r){return[0,0,r[0]]};s.gray.hwb=function(r){return[0,100,r[0]]};s.gray.cmyk=function(r){return[0,0,0,r[0]]};s.gray.lab=function(r){return[r[0],0,0]};s.gray.hex=function(r){var a=Math.round(r[0]/100*255)&255,n=(a<<16)+(a<<8)+a,e=n.toString(16).toUpperCase();return"000000".substring(e.length)+e};s.rgb.gray=function(r){var a=(r[0]+r[1]+r[2])/3;return[a/255*100]};var S=q.exports,k=S;function K(){for(var r={},a=Object.keys(k),n=a.length,e=0;e<n;e++)r[a[e]]={distance:-1,parent:null};return r}function R(r){var a=K(),n=[r];for(a[r].distance=0;n.length;)for(var e=n.pop(),t=Object.keys(k[e]),i=t.length,o=0;o<i;o++){var l=t[o],h=a[l];h.distance===-1&&(h.distance=a[e].distance+1,h.parent=e,n.unshift(l))}return a}function J(r,a){return function(n){return a(r(n))}}function T(r,a){for(var n=[a[r].parent,r],e=k[a[r].parent][r],t=a[r].parent;a[t].parent;)n.unshift(a[t].parent),e=J(k[a[t].parent][t],e),t=a[t].parent;return e.conversion=n,e}var B=function(r){for(var a=R(r),n={},e=Object.keys(a),t=e.length,i=0;i<t;i++){var o=e[i],l=a[o];l.parent!==null&&(n[o]=T(o,a))}return n},F=S,G=B,w={},L=Object.keys(F);function _(r){var a=function(n){return n==null?n:(arguments.length>1&&(n=Array.prototype.slice.call(arguments)),r(n))};return"conversion"in r&&(a.conversion=r.conversion),a}function H(r){var a=function(n){if(n==null)return n;arguments.length>1&&(n=Array.prototype.slice.call(arguments));var e=r(n);if(typeof e=="object")for(var t=e.length,i=0;i<t;i++)e[i]=Math.round(e[i]);return e};return"conversion"in r&&(a.conversion=r.conversion),a}L.forEach(function(r){w[r]={},Object.defineProperty(w[r],"channels",{value:F[r].channels}),Object.defineProperty(w[r],"labels",{value:F[r].labels});var a=G(r),n=Object.keys(a);n.forEach(function(e){var t=a[e];w[r][e]=H(t),w[r][e].raw=_(t)})});var Q=w,d=N,p=Q,A=[].slice,P=["keyword","gray","hex"],O={};Object.keys(p).forEach(function(r){O[A.call(p[r].labels).sort().join("")]=r});var M={};function f(r,a){if(!(this instanceof f))return new f(r,a);if(a&&a in P&&(a=null),a&&!(a in p))throw new Error("Unknown model: "+a);var n,e;if(r==null)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(r instanceof f)this.model=r.model,this.color=r.color.slice(),this.valpha=r.valpha;else if(typeof r=="string"){var t=d.get(r);if(t===null)throw new Error("Unable to parse color from string: "+r);this.model=t.model,e=p[this.model].channels,this.color=t.value.slice(0,e),this.valpha=typeof t.value[e]=="number"?t.value[e]:1}else if(r.length){this.model=a||"rgb",e=p[this.model].channels;var i=A.call(r,0,e);this.color=z(i,e),this.valpha=typeof r[e]=="number"?r[e]:1}else if(typeof r=="number")r&=16777215,this.model="rgb",this.color=[r>>16&255,r>>8&255,r&255],this.valpha=1;else{this.valpha=1;var o=Object.keys(r);"alpha"in r&&(o.splice(o.indexOf("alpha"),1),this.valpha=typeof r.alpha=="number"?r.alpha:0);var l=o.sort().join("");if(!(l in O))throw new Error("Unable to parse color from object: "+JSON.stringify(r));this.model=O[l];var h=p[this.model].labels,v=[];for(n=0;n<h.length;n++)v.push(r[h[n]]);this.color=z(v)}if(M[this.model])for(e=p[this.model].channels,n=0;n<e;n++){var g=M[this.model][n];g&&(this.color[n]=g(this.color[n]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}f.prototype={toString:function(){return this.string()},toJSON:function(){return this[this.model]()},string:function(r){var a=this.model in d.to?this:this.rgb();a=a.round(typeof r=="number"?r:1);var n=a.valpha===1?a.color:a.color.concat(this.valpha);return d.to[a.model](n)},percentString:function(r){var a=this.rgb().round(typeof r=="number"?r:1),n=a.valpha===1?a.color:a.color.concat(this.valpha);return d.to.rgb.percent(n)},array:function(){return this.valpha===1?this.color.slice():this.color.concat(this.valpha)},object:function(){for(var r={},a=p[this.model].channels,n=p[this.model].labels,e=0;e<a;e++)r[n[e]]=this.color[e];return this.valpha!==1&&(r.alpha=this.valpha),r},unitArray:function(){var r=this.rgb().color;return r[0]/=255,r[1]/=255,r[2]/=255,this.valpha!==1&&r.push(this.valpha),r},unitObject:function(){var r=this.rgb().object();return r.r/=255,r.g/=255,r.b/=255,this.valpha!==1&&(r.alpha=this.valpha),r},round:function(r){return r=Math.max(r||0,0),new f(this.color.map(W(r)).concat(this.valpha),this.model)},alpha:function(r){return arguments.length?new f(this.color.concat(Math.max(0,Math.min(1,r))),this.model):this.valpha},red:u("rgb",0,c(255)),green:u("rgb",1,c(255)),blue:u("rgb",2,c(255)),hue:u(["hsl","hsv","hsl","hwb","hcg"],0,function(r){return(r%360+360)%360}),saturationl:u("hsl",1,c(100)),lightness:u("hsl",2,c(100)),saturationv:u("hsv",1,c(100)),value:u("hsv",2,c(100)),chroma:u("hcg",1,c(100)),gray:u("hcg",2,c(100)),white:u("hwb",1,c(100)),wblack:u("hwb",2,c(100)),cyan:u("cmyk",0,c(100)),magenta:u("cmyk",1,c(100)),yellow:u("cmyk",2,c(100)),black:u("cmyk",3,c(100)),x:u("xyz",0,c(100)),y:u("xyz",1,c(100)),z:u("xyz",2,c(100)),l:u("lab",0,c(100)),a:u("lab",1),b:u("lab",2),keyword:function(r){return arguments.length?new f(r):p[this.model].keyword(this.color)},hex:function(r){return arguments.length?new f(r):d.to.hex(this.rgb().round().color)},rgbNumber:function(){var r=this.rgb().color;return(r[0]&255)<<16|(r[1]&255)<<8|r[2]&255},luminosity:function(){for(var r=this.rgb().color,a=[],n=0;n<r.length;n++){var e=r[n]/255;a[n]=e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)}return .2126*a[0]+.7152*a[1]+.0722*a[2]},contrast:function(r){var a=this.luminosity(),n=r.luminosity();return a>n?(a+.05)/(n+.05):(n+.05)/(a+.05)},level:function(r){var a=this.contrast(r);return a>=7.1?"AAA":a>=4.5?"AA":""},isDark:function(){var r=this.rgb().color,a=(r[0]*299+r[1]*587+r[2]*114)/1e3;return a<128},isLight:function(){return!this.isDark()},negate:function(){for(var r=this.rgb(),a=0;a<3;a++)r.color[a]=255-r.color[a];return r},lighten:function(r){var a=this.hsl();return a.color[2]+=a.color[2]*r,a},darken:function(r){var a=this.hsl();return a.color[2]-=a.color[2]*r,a},saturate:function(r){var a=this.hsl();return a.color[1]+=a.color[1]*r,a},desaturate:function(r){var a=this.hsl();return a.color[1]-=a.color[1]*r,a},whiten:function(r){var a=this.hwb();return a.color[1]+=a.color[1]*r,a},blacken:function(r){var a=this.hwb();return a.color[2]+=a.color[2]*r,a},grayscale:function(){var r=this.rgb().color,a=r[0]*.3+r[1]*.59+r[2]*.11;return f.rgb(a,a,a)},fade:function(r){return this.alpha(this.valpha-this.valpha*r)},opaquer:function(r){return this.alpha(this.valpha+this.valpha*r)},rotate:function(r){var a=this.hsl(),n=a.color[0];return n=(n+r)%360,n=n<0?360+n:n,a.color[0]=n,a},mix:function(r,a){if(!r||!r.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof r);var n=r.rgb(),e=this.rgb(),t=a===void 0?.5:a,i=2*t-1,o=n.alpha()-e.alpha(),l=((i*o===-1?i:(i+o)/(1+i*o))+1)/2,h=1-l;return f.rgb(l*n.red()+h*e.red(),l*n.green()+h*e.green(),l*n.blue()+h*e.blue(),n.alpha()*t+e.alpha()*(1-t))}};Object.keys(p).forEach(function(r){if(P.indexOf(r)===-1){var a=p[r].channels;f.prototype[r]=function(){if(this.model===r)return new f(this);if(arguments.length)return new f(arguments,r);var n=typeof arguments[a]=="number"?a:this.valpha;return new f(X(p[this.model][r].raw(this.color)).concat(n),r)},f[r]=function(n){return typeof n=="number"&&(n=z(A.call(arguments),a)),new f(n,r)}}});function V(r,a){return Number(r.toFixed(a))}function W(r){return function(a){return V(a,r)}}function u(r,a,n){return r=Array.isArray(r)?r:[r],r.forEach(function(e){(M[e]||(M[e]=[]))[a]=n}),r=r[0],function(e){var t;return arguments.length?(n&&(e=n(e)),t=this[r](),t.color[a]=e,t):(t=this[r]().color[a],n&&(t=n(t)),t)}}function c(r){return function(a){return Math.max(0,Math.min(r,a))}}function X(r){return Array.isArray(r)?r:[r]}function z(r,a){for(var n=0;n<a;n++)typeof r[n]!="number"&&(r[n]=0);return r}var Z=f;export{Z as c};
