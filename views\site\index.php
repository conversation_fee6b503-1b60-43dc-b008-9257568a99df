<?php
/**
 * 首页视图
 */
use yii\helpers\Html;
use yii\helpers\Url;

$this->title = $title;
?>

<div class="site-index">
    <div class="jumbotron text-center bg-transparent">
        <h1 class="display-4"><?= Html::encode($title) ?></h1>
        <p class="lead"><?= Html::encode($message) ?></p>
        <hr class="my-4">
        <p>这是一个基于 Yii 2.0 框架的现代化快速开发平台</p>
        <div class="btn-group" role="group">
            <?= Html::a('功能演示', ['/demo'], ['class' => 'btn btn-primary btn-lg']) ?>
            <?= Html::a('用户管理', ['/user'], ['class' => 'btn btn-success btn-lg']) ?>
            <?= Html::a('代码生成', ['/gii'], ['class' => 'btn btn-info btn-lg', 'target' => '_blank']) ?>
            <?= Html::a('调试工具', ['/debug'], ['class' => 'btn btn-warning btn-lg', 'target' => '_blank']) ?>
        </div>
    </div>

    <div class="body-content">
        <div class="row">
            <div class="col-lg-4">
                <h3>🚀 框架特性</h3>
                <ul class="list-group list-group-flush">
                    <?php foreach ($features as $feature): ?>
                        <li class="list-group-item border-0 px-0">
                            <i class="fas fa-check text-success me-2"></i>
                            <?= Html::encode($feature) ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
            <div class="col-lg-4">
                <h3>🔗 快速链接</h3>
                <div class="list-group">
                    <?= Html::a('<i class="fas fa-play me-2"></i>功能演示', ['/demo'], ['class' => 'list-group-item list-group-item-action']) ?>
                    <?= Html::a('<i class="fas fa-users me-2"></i>用户管理', ['/user'], ['class' => 'list-group-item list-group-item-action']) ?>
                    <?= Html::a('<i class="fas fa-cog me-2"></i>系统设置', ['/admin'], ['class' => 'list-group-item list-group-item-action']) ?>
                    <?= Html::a('<i class="fas fa-tools me-2"></i>开发工具', ['/tool'], ['class' => 'list-group-item list-group-item-action']) ?>
                    <?= Html::a('<i class="fas fa-code me-2"></i>代码生成', ['/gii'], ['class' => 'list-group-item list-group-item-action', 'target' => '_blank']) ?>
                    <?= Html::a('<i class="fas fa-bug me-2"></i>调试工具', ['/debug'], ['class' => 'list-group-item list-group-item-action', 'target' => '_blank']) ?>
                </div>
            </div>
            
            <div class="col-lg-4">
                <h3>📖 开发资源</h3>
                <div class="list-group">
                    <a href="https://www.yiiframework.com/doc/guide/2.0/zh-cn" class="list-group-item list-group-item-action" target="_blank">
                        <i class="fas fa-book me-2"></i>Yii 2.0 指南
                    </a>
                    <a href="https://www.yiiframework.com/doc/api/2.0" class="list-group-item list-group-item-action" target="_blank">
                        <i class="fas fa-file-code me-2"></i>API 文档
                    </a>
                    <a href="https://forum.yiiframework.com" class="list-group-item list-group-item-action" target="_blank">
                        <i class="fas fa-comments me-2"></i>社区论坛
                    </a>
                    <?= Html::a('<i class="fas fa-info-circle me-2"></i>系统信息', ['/site/info'], ['class' => 'list-group-item list-group-item-action']) ?>
                    <?= Html::a('<i class="fas fa-envelope me-2"></i>联系我们', ['/site/contact'], ['class' => 'list-group-item list-group-item-action']) ?>
                    <?= Html::a('<i class="fas fa-question-circle me-2"></i>关于我们', ['/site/about'], ['class' => 'list-group-item list-group-item-action']) ?>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">🎯 快速开始</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>开发环境</h6>
                                <ul>
                                    <li>PHP <?= PHP_VERSION ?></li>
                                    <li>Yii <?= Yii::getVersion() ?></li>
                                    <li>环境: <?= YII_ENV ?></li>
                                    <li>调试: <?= YII_DEBUG ? '开启' : '关闭' ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>控制台命令</h6>
                                <ul>
                                    <li><code>php yii help</code> - 查看所有命令</li>
                                    <li><code>php yii migrate</code> - 数据库迁移</li>
                                    <li><code>php yii cache/flush-all</code> - 清除缓存</li>
                                    <li><code>php yii gii/model</code> - 生成模型</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
