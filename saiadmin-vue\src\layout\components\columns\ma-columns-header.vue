<template>
  <a-layout-header class="layout-header flex flex-col operation-area">
    <div class="flex justify-between" style="height: 50px">
      <a-avatar class="mt-1 ml-2 inline lg:hidden" style="width: 45px" :size="40">
        <img src="../../../assets/logo.png" class="bg-white" />
      </a-avatar>
      <ma-breadcrumb />
      <ma-operation />
    </div>
    <ma-tags class="hidden lg:flex" />
  </a-layout-header>
</template>

<script setup>
import MaBreadcrumb from '../ma-breadcrumb.vue'
import MaOperation from '../ma-operation.vue'
import MaTags from '../ma-tags.vue'
</script>
