import{g as t}from"./index-DkGLNqVb.js";const a={getPageList(e={}){return t({url:"/core/database/index",method:"get",params:e})},getDataSource(e={}){return t({url:"/core/database/dataSource",method:"get",params:e})},getDetailed(e={}){return t({url:"/core/database/detailed",method:"get",params:e})},getRecycle(e={}){return t({url:"/core/database/recycle",method:"get",params:e})},delete(e){return t({url:"/core/database/delete",method:"delete",data:e})},recovery(e){return t({url:"/core/database/recovery",method:"post",data:e})},optimize(e={}){return t({url:"/core/database/optimize",method:"post",data:e})},fragment(e={}){return t({url:"/core/database/fragment",method:"post",data:e})}};export{a};
