import{a as v}from"./role-CkU346eF.js";import S from"./menuPermission-0cOEYOYw.js";import D from"./edit-BYRBvBoh.js";import{M as _}from"./@arco-design-uttiljWv.js";import{r as m,a as w,o as E,h as s,ba as N,n as k,k as p,t,l as a,j as x,p as y,M as U,y as j}from"./@vue-9ZIPiVZG.js";import"./index-DkGLNqVb.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./menu-CBqt8CnT.js";const L={class:"ma-content-block lg:flex justify-between"},$={key:0},Oe={__name:"index",setup(z){const c=m(),l=m(),h=m(),n=m({name:"",code:"",status:""}),V=e=>{h.value.open(e)},b=w({api:v.getPageList,rowSelection:{showCheckedAll:!0},isExpand:!0,operationColumnWidth:220,add:{show:!0,auth:["/core/role/save"],func:async()=>{var e;(e=l.value)==null||e.open()}},edit:{show:!0,auth:["/core/role/update"],func:async e=>{var o,i;if(e.id===1)return _.error("超级管理员角色不可编辑"),!1;(o=l.value)==null||o.open("edit"),(i=l.value)==null||i.setFormData(e)}},delete:{show:!0,auth:["/core/role/destroy"],func:async e=>{var i;if(e.ids.includes(1))return _.error("超级管理员角色不可删除"),!1;(await v.destroy(e)).code===200&&(_.success("删除成功！"),(i=c.value)==null||i.refresh())}}}),C=w([{title:"角色名称",dataIndex:"name",width:220},{title:"角色标识",dataIndex:"code",width:180},{title:"排序",dataIndex:"sort",width:150},{title:"状态",dataIndex:"status",type:"dict",dict:"data_status",width:100}]),M=async()=>{},u=async()=>{var e;(e=c.value)==null||e.refresh()};return E(()=>{M(),u()}),(e,o)=>{const i=s("a-input"),d=s("a-form-item"),f=s("a-col"),R=s("sa-select"),g=s("icon-menu"),B=s("a-link"),F=s("a-space"),I=s("sa-table"),P=N("auth");return p(),k("div",L,[t(I,{ref_key:"crudRef",ref:c,options:b,columns:C,searchForm:n.value},{tableSearch:a(()=>[t(f,{sm:8,xs:24},{default:a(()=>[t(d,{field:"name",label:"角色名称"},{default:a(()=>[t(i,{modelValue:n.value.name,"onUpdate:modelValue":o[0]||(o[0]=r=>n.value.name=r),placeholder:"请输入角色名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),t(f,{sm:8,xs:24},{default:a(()=>[t(d,{field:"code",label:"角色标识"},{default:a(()=>[t(i,{modelValue:n.value.code,"onUpdate:modelValue":o[1]||(o[1]=r=>n.value.code=r),placeholder:"请输入角色标识","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),t(f,{sm:8,xs:24},{default:a(()=>[t(d,{field:"status",label:"状态"},{default:a(()=>[t(R,{modelValue:n.value.status,"onUpdate:modelValue":o[2]||(o[2]=r=>n.value.status=r),dict:"data_status",placeholder:"请选择状态","alow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),operationCell:a(({record:r})=>[r.disabled?(p(),k("div",$)):y("",!0)]),operationBeforeExtend:a(({record:r})=>[r.id>1&&!r.disabled?(p(),x(F,{key:0,size:"mini"},{default:a(()=>[U((p(),x(B,{onClick:A=>V(r)},{default:a(()=>[t(g),o[3]||(o[3]=j(" 菜单权限 "))]),_:2},1032,["onClick"])),[[P,["/core/role/menuPermission"]]])]),_:2},1024)):y("",!0)]),_:1},8,["options","columns","searchForm"]),t(D,{ref_key:"editRef",ref:l,onSuccess:u},null,512),t(S,{ref_key:"mpRef",ref:h,onSuccess:u},null,512)])}}};export{Oe as default};
