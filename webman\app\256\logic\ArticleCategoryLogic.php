<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: SaiAdmin Team
// +----------------------------------------------------------------------
namespace app\article\logic;

use plugin\saiadmin\basic\BaseLogic;
use plugin\saiadmin\exception\ApiException;
use plugin\saiadmin\utils\Helper;
use app\article\model\ArticleCategory;

/**
 * 文章分类表逻辑层
 */
class ArticleCategoryLogic extends BaseLogic
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new ArticleCategory();
    }

    /**
     * 搜索条件处理
     * @param array $where
     * @return mixed
     */
    public function search($where = array())
    {
        $query = $this->model;

        // 分类名称搜索
        if (!empty($where['category_name'])) {
            $query = $query->searchCategoryNameAttr($query, $where['category_name']);
        }

        // 父级ID搜索
        if (isset($where['parent_id']) && $where['parent_id'] !== '') {
            $query = $query->where('parent_id', $where['parent_id']);
        }

        // 状态搜索
        if (isset($where['status']) && $where['status'] !== '') {
            $query = $query->where('status', $where['status']);
        }

        return $query;
    }

    /**
     * 获取列表数据
     * @param mixed $query
     * @return array
     */
    public function getList($query)
    {
        $list = $query->order('sort desc, id desc')->select();
        return $list ? $list->toArray() : array();
    }

    /**
     * 获取单条数据
     * @param int $id
     * @return array
     */
    public function read($id)
    {
        $data = $this->model->find($id);
        return $data ? $data->toArray() : array();
    }

    /**
     * 保存数据
     * @param array $data
     * @return bool
     */
    public function save($data)
    {
        // 设置默认值
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        if (!isset($data['status'])) {
            $data['status'] = 1;
        }

        if (!isset($data['sort'])) {
            $data['sort'] = 0;
        }

        return $this->model->save($data);
    }

    /**
     * 更新数据
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        return $this->model->where('id', $id)->update($data);
    }

    /**
     * 删除数据
     * @param mixed $ids
     * @return bool
     */
    public function delete($ids)
    {
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }

        // 检查是否有子分类
        foreach ($ids as $id) {
            $hasChildren = $this->model->where('parent_id', $id)->count();
            if ($hasChildren > 0) {
                throw new ApiException('分类下存在子分类，无法删除');
            }
        }

        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * 修改状态
     * @param int $id
     * @param int $status
     * @return bool
     */
    public function changeStatus($id, $status)
    {
        return $this->model->where('id', $id)->update(array(
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ));
    }

    /**
     * 获取分类树形结构
     * @return array
     */
    public function getTree()
    {
        $list = $this->model->where('status', 1)->order('sort desc, id asc')->select();
        if (!$list) {
            return array();
        }

        return $this->buildTree($list->toArray());
    }

    /**
     * 构建树形结构
     * @param array $data
     * @param int $parentId
     * @return array
     */
    private function buildTree($data, $parentId = 0)
    {
        $tree = array();
        foreach ($data as $item) {
            if ($item['parent_id'] == $parentId) {
                $children = $this->buildTree($data, $item['id']);
                if ($children) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }

    /**
     * 数字运算操作
     * @param int $id
     * @param string $field
     * @param mixed $value
     * @return bool
     */
    public function numberOperation($id, $field, $value)
    {
        $allowFields = array('sort'); // 允许操作的字段

        if (!in_array($field, $allowFields)) {
            throw new ApiException('不允许操作该字段');
        }

        return $this->model->where('id', $id)->update(array(
            $field => $value,
            'updated_at' => date('Y-m-d H:i:s')
        ));
    }

    /**
     * 批量操作
     * @param array $ids
     * @param string $operation
     * @return bool
     */
    public function batchOperation($ids, $operation)
    {
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }

        switch ($operation) {
            case 'delete':
                return $this->delete($ids);
            case 'enable':
                return $this->model->whereIn('id', $ids)->update(array(
                    'status' => 1,
                    'updated_at' => date('Y-m-d H:i:s')
                ));
            case 'disable':
                return $this->model->whereIn('id', $ids)->update(array(
                    'status' => 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ));
            default:
                throw new ApiException('不支持的操作类型');
        }
    }
}
