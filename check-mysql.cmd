@echo off
chcp 65001 >nul
echo ========================================
echo    SaiAdmin MySQL 服务检查
echo ========================================
echo.

echo 检查MySQL服务状态...
echo.

REM 检查常见的MySQL服务名
set "services=mysql MySQL80 MySQL57 wampmysqld64"
set "found=0"

for %%s in (%services%) do (
    sc query "%%s" >nul 2>&1
    if !errorlevel! == 0 (
        echo ✅ 找到服务: %%s
        sc query "%%s" | find "RUNNING" >nul
        if !errorlevel! == 0 (
            echo    状态: 正在运行
        ) else (
            echo    状态: 已停止，尝试启动...
            net start "%%s" >nul 2>&1
            if !errorlevel! == 0 (
                echo    ✅ 服务启动成功
            ) else (
                echo    ❌ 服务启动失败
            )
        )
        set "found=1"
    )
)

if "%found%"=="0" (
    echo ❌ 未找到MySQL服务
    echo.
    echo 可能的解决方案:
    echo 1. 安装MySQL服务器
    echo 2. 使用XAMPP/WAMP等集成环境
    echo 3. 切换到SQLite数据库
)

echo.
echo 检查MySQL客户端...
mysql --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MySQL客户端可用
    echo.
    echo 测试数据库连接...
    mysql -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "SELECT 1;" >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ 数据库连接成功
        echo.
        echo 检查数据库是否存在...
        mysql -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "USE saiadmin;" >nul 2>&1
        if %errorlevel% == 0 (
            echo ✅ 数据库 'saiadmin' 存在
        ) else (
            echo ⚠️ 数据库 'saiadmin' 不存在
            echo 创建数据库...
            mysql -h127.0.0.1 -P3306 -uroot -p5GeNi1v7P7Xcur5W -e "CREATE DATABASE IF NOT EXISTS saiadmin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" >nul 2>&1
            if %errorlevel% == 0 (
                echo ✅ 数据库创建成功
            ) else (
                echo ❌ 数据库创建失败
            )
        )
    ) else (
        echo ❌ 数据库连接失败
        echo.
        echo 可能的原因:
        echo - 密码错误
        echo - 用户权限不足  
        echo - MySQL服务未启动
        echo - 端口被占用
    )
) else (
    echo ❌ MySQL客户端不可用
    echo 请确保MySQL已正确安装并添加到PATH环境变量
)

echo.
echo 当前数据库配置:
echo - 主机: 127.0.0.1:3306
echo - 数据库: saiadmin
echo - 用户: root
echo - 密码: 5GeNi1v7P7Xcur5W
echo.
echo ========================================
echo 检查完成！
echo ========================================
pause
