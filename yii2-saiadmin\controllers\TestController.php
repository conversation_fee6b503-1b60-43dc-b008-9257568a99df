<?php
/**
 * 架构测试控制器
 */
namespace app\controllers;

use Yii;
use app\components\base\BaseController;

class TestController extends BaseController
{
    /**
     * 测试基本功能
     */
    public function actionIndex()
    {
        return $this->success([
            "message" => "Yii 2.0 架构测试成功",
            "timestamp" => time(),
            "version" => Yii::getVersion(),
            "environment" => YII_ENV
        ]);
    }
    
    /**
     * 测试数据库连接
     */
    public function actionDatabase()
    {
        try {
            $db = Yii::$app->db;
            $command = $db->createCommand("SELECT 1 as test");
            $result = $command->queryOne();
            
            return $this->success([
                "database" => "连接成功",
                "driver" => $db->driverName,
                "result" => $result
            ]);
        } catch (Exception $e) {
            return $this->fail("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 测试缓存功能
     */
    public function actionCache()
    {
        try {
            $cache = Yii::$app->cache;
            $key = "test_cache_" . time();
            $value = "测试缓存数据";
            
            // 设置缓存
            $cache->set($key, $value, 60);
            
            // 获取缓存
            $cached = $cache->get($key);
            
            return $this->success([
                "cache" => "功能正常",
                "set_value" => $value,
                "get_value" => $cached,
                "match" => $value === $cached
            ]);
        } catch (Exception $e) {
            return $this->fail("缓存测试失败: " . $e->getMessage());
        }
    }
    
    /**
     * 测试日志功能
     */
    public function actionLog()
    {
        try {
            Yii::info("架构测试日志", "test");
            Yii::warning("架构测试警告", "test");
            
            return $this->success([
                "log" => "日志记录成功",
                "info_logged" => true,
                "warning_logged" => true
            ]);
        } catch (Exception $e) {
            return $this->fail("日志测试失败: " . $e->getMessage());
        }
    }
}