<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\tesxt\controller;

use plugin\saiadmin\basic\BaseController;
use app\tesxt\logic\DdwxArticleLogic;
use app\tesxt\validate\DdwxArticleValidate;
use support\Request;
use support\Response;

/**
 * test控制器
 */
class DdwxArticleController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new DdwxArticleLogic();
        $this->validate = new DdwxArticleValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['name', ''],
            ['subname', ''],
            ['showname', ''],
            ['showsubname', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

}
