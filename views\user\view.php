<?php
/**
 * 用户详情视图
 */
use yii\helpers\Html;
use yii\widgets\DetailView;

$this->title = $model->username;
$this->params['breadcrumbs'][] = ['label' => '用户管理', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="user-view">
    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('编辑', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('删除', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => '确定要删除这个用户吗？',
                'method' => 'post',
            ],
        ]) ?>
        <?= Html::a('返回列表', ['index'], ['class' => 'btn btn-default']) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'username',
            'email:email',
            'nickname',
            [
                'attribute' => 'status',
                'value' => $model->getStatusText(),
            ],
            [
                'attribute' => 'avatar',
                'format' => 'raw',
                'value' => function ($model) {
                    return Html::img($model->getAvatarUrl(), ['width' => 64, 'height' => 64, 'class' => 'img-thumbnail']);
                }
            ],
            'last_login_at:datetime',
            'created_at:datetime',
            'updated_at:datetime',
        ],
    ]) ?>
</div>
