import S from"./edit-B2-MKvH2.js";import U from"./view-JzXCzoqh.js";import{a as f}from"./post-CmCXCNyH.js";import{M as w}from"./@arco-design-uttiljWv.js";import{r as n,a as v,o as C,h as m,n as F,k as M,t as o,l as s}from"./@vue-9ZIPiVZG.js";import"./index-DkGLNqVb.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const B={class:"ma-content-block lg:flex justify-between"},Nt={__name:"index",setup(D){const p=n(),l=n(),h=n(),i=n({name:"",status:"",create_time:[]}),x=async(e,t)=>{const a=await f.changeStatus({id:t,status:e});a.code===200&&(w.success(a.message),p.value.refresh())},y=v({api:f.getPageList,rowSelection:{showCheckedAll:!0},view:{show:!0,auth:["/core/post/read"],func:async e=>{var t;(t=h.value)==null||t.open(e)}},add:{show:!0,auth:["/core/post/save"],func:async()=>{var e;(e=l.value)==null||e.open()}},edit:{show:!0,auth:["/core/post/update"],func:async e=>{var t,a;(t=l.value)==null||t.open("edit"),(a=l.value)==null||a.setFormData(e)}},delete:{show:!0,auth:["/core/post/destroy"],func:async e=>{var a;(await f.destroy(e)).code===200&&(w.success("删除成功！"),(a=p.value)==null||a.refresh())}},import:{show:!0,url:"/core/post/import",templateUrl:"/core/post/downloadTemplate",auth:["/core/post/import"]},export:{show:!0,url:"/core/post/export",auth:["/core/post/export"]}}),V=v([{title:"ID",dataIndex:"id",width:80},{title:"岗位名称",dataIndex:"name",width:120},{title:"岗位标识",dataIndex:"code",width:180},{title:"排序",dataIndex:"sort",width:180},{title:"状态",dataIndex:"status",type:"dict",dict:"data_status",width:120},{title:"备注",dataIndex:"remark",width:180},{title:"创建时间",dataIndex:"create_time",width:180}]),g=async()=>{},c=async()=>{var e;(e=p.value)==null||e.refresh()};return C(async()=>{g(),c()}),(e,t)=>{const a=m("a-input"),u=m("a-form-item"),d=m("a-col"),k=m("sa-select"),b=m("a-range-picker"),I=m("sa-switch"),R=m("sa-table");return M(),F("div",B,[o(R,{ref_key:"crudRef",ref:p,options:y,columns:V,searchForm:i.value},{tableSearch:s(()=>[o(d,{sm:8,xs:24},{default:s(()=>[o(u,{field:"name",label:"岗位名称"},{default:s(()=>[o(a,{modelValue:i.value.name,"onUpdate:modelValue":t[0]||(t[0]=r=>i.value.name=r),placeholder:"请输入岗位名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1}),o(d,{sm:8,xs:24},{default:s(()=>[o(u,{field:"status",label:"状态"},{default:s(()=>[o(k,{modelValue:i.value.status,"onUpdate:modelValue":t[1]||(t[1]=r=>i.value.status=r),dict:"data_status",placeholder:"请选择状态"},null,8,["modelValue"])]),_:1})]),_:1}),o(d,{sm:8,xs:24},{default:s(()=>[o(u,{field:"create_time",label:"时间范围"},{default:s(()=>[o(b,{modelValue:i.value.create_time,"onUpdate:modelValue":t[2]||(t[2]=r=>i.value.create_time=r),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),status:s(({record:r})=>[o(I,{modelValue:r.status,"onUpdate:modelValue":_=>r.status=_,onChange:_=>x(_,r.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1},8,["options","columns","searchForm"]),o(S,{ref_key:"editRef",ref:l,onSuccess:c},null,512),o(U,{ref_key:"viewRef",ref:h,onSuccess:c},null,512)])}}};export{Nt as default};
