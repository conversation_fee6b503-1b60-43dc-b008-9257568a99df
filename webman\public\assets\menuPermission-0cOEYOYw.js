import{a as x}from"./role-CkU346eF.js";import{m as K}from"./menu-CBqt8CnT.js";import{_ as j}from"./index-DkGLNqVb.js";import{M as z}from"./@arco-design-uttiljWv.js";import{r,h as n,j as D,k as I,l as a,t as o,m as V,y as _}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";import"./vue-QIJ1KGct.js";import"./dayjs-DUkVwsK-.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";const O={class:"w-full"},S={class:"tree-container"},q={__name:"menuPermission",emits:["success"],setup(F,{expose:h,emit:C}){const u=r(!1),v=r(!0),g=r([]),c=r([]),f=r(!1),p=r(),m=r({name:void 0,code:void 0}),N=C,M=async t=>{u.value=!0,m.value={id:t.id,name:t.name,code:t.code},b(!1),y(!1),k(!1),await T(t.id)},b=t=>{p.value.saTree.expandAll(t)},y=t=>{p.value.saTree.checkAll(t)},k=t=>{f.value=t},B=t=>{const e=p.value.saTree,i=e.getExpandedNodes().map(s=>s.id);e.expandNode(t,!i.includes(t[0]))},T=async t=>{v.value=!0;const e=await K.accessMenu({tree:!0});g.value=e.data;const i=await x.getMenuByRole(t);c.value=i.data.menus.map(s=>s.id),c.value.length>0&&k(!0),v.value=!1},U=async t=>{const i=p.value.saTree.getCheckedNodes().map(d=>d.id),s=await x.updateMenuPermission(m.value.id,{menu_ids:i});s.code===200&&z.success(s.message),N("success"),t(!0)},w=()=>u.value=!1;return h({open:M}),(t,e)=>{const i=n("a-input"),s=n("a-form-item"),d=n("a-checkbox"),P=n("a-space"),A=n("sa-tree-slider"),L=n("a-spin"),R=n("a-form"),E=n("a-modal");return I(),D(E,{visible:u.value,"onUpdate:visible":e[4]||(e[4]=l=>u.value=l),onCancel:w,onBeforeOk:U},{title:a(()=>e[5]||(e[5]=[_("菜单权限")])),default:a(()=>[o(R,{model:m.value},{default:a(()=>[o(s,{label:"角色名称",field:"name"},{default:a(()=>[o(i,{disabled:"",modelValue:m.value.name,"onUpdate:modelValue":e[0]||(e[0]=l=>m.value.name=l)},null,8,["modelValue"])]),_:1}),o(s,{label:"角色标识",field:"code"},{default:a(()=>[o(i,{disabled:"",modelValue:m.value.code,"onUpdate:modelValue":e[1]||(e[1]=l=>m.value.code=l)},null,8,["modelValue"])]),_:1}),o(s,{label:"菜单列表",field:"menu_ids"},{default:a(()=>[o(L,{loading:v.value,tip:"菜单加载中...",class:"w-full"},{default:a(()=>[V("div",O,[o(P,{class:"mt-1.5",size:"large"},{default:a(()=>[o(d,{onChange:b},{default:a(()=>e[6]||(e[6]=[_("展开/折叠")])),_:1}),o(d,{onChange:y},{default:a(()=>e[7]||(e[7]=[_("全选/全不选")])),_:1}),o(d,{modelValue:f.value,"onUpdate:modelValue":e[2]||(e[2]=l=>f.value=l),onChange:k},{default:a(()=>e[8]||(e[8]=[_("关闭父子级联动")])),_:1},8,["modelValue"])]),_:1}),V("div",S,[o(A,{ref_key:"tree",ref:p,data:g.value,checkable:"",fieldNames:{title:"label",key:"id"},searchPlaceholder:"过滤菜单","checked-keys":c.value,"onUpdate:checkedKeys":e[3]||(e[3]=l=>c.value=l),"check-strictly":f.value,"virtual-list-props":{height:300},onClick:B},null,8,["data","checked-keys","check-strictly"])])])]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible"])}}},Xe=j(q,[["__scopeId","data-v-c1ac5cca"]]);export{Xe as default};
