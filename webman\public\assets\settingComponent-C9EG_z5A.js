import{_ as S}from"./index-ybrmzYq5.js";import{r as b,h as u,j as f,k as n,l,t,n as _,p as r,y as m,m as g,z as F}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const L={key:0},j={key:1},q={key:2},z={key:3},A={__name:"settingComponent",emits:["confrim"],setup(D,{expose:k,emit:U}){const T=U,y=b(!1),d=b({}),o=b({}),C=a=>{d.value=a,a.view_type=="uploadImage"||a.view_type=="uploadFile"?o.value=a.options?a.options:{multiple:!1}:a.view_type=="codeEditor"||a.view_type=="editor"||a.view_type=="wangEditor"?o.value=a.options?a.options:{height:400}:a.view_type=="date"?o.value=a.options?a.options:{mode:"date",showTime:!1}:a.view_type=="cityLinkage"?o.value=a.options?a.options:{type:"cascader",mode:"code"}:o.value=a.options?a.options:{},y.value=!0},E=a=>{T("confrim",d.value.column_name,o.value),a(!0)};return k({open:C}),(a,e)=>{const x=u("a-input-number"),s=u("a-form-item"),v=u("a-radio"),V=u("a-radio-group"),B=u("a-alert"),p=u("a-option"),w=u("a-select"),N=u("a-form"),I=u("a-modal");return n(),f(I,{visible:y.value,"onUpdate:visible":e[7]||(e[7]=i=>y.value=i),"on-before-ok":E,width:"600px",draggable:"",top:"50px","align-center":!1},{title:l(()=>{var i;return[m("设置组件 - "+F((i=d.value)==null?void 0:i.column_comment),1)]}),default:l(()=>[t(N,{model:o.value},{default:l(()=>[["codeEditor","editor","wangEditor"].includes(d.value.view_type)?(n(),_("div",L,[t(s,{label:"编辑器高度",field:"height","label-col-flex":"auto","label-col-style":{width:"120px"}},{default:l(()=>[t(x,{modelValue:o.value.height,"onUpdate:modelValue":e[0]||(e[0]=i=>o.value.height=i),max:1e3,min:100},null,8,["modelValue"])]),_:1})])):r("",!0),["uploadImage","uploadFile"].includes(d.value.view_type)?(n(),_("div",j,[t(s,{label:"是否可多选",field:"multiple","label-col-flex":"auto","label-col-style":{width:"100px"}},{default:l(()=>[t(V,{modelValue:o.value.multiple,"onUpdate:modelValue":e[1]||(e[1]=i=>o.value.multiple=i)},{default:l(()=>[t(v,{value:!0},{default:l(()=>e[8]||(e[8]=[m("是")])),_:1}),t(v,{value:!1},{default:l(()=>e[9]||(e[9]=[m("否")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o.value.multiple?(n(),f(s,{key:0,label:"数量限制",field:"limit","label-col-flex":"auto","label-col-style":{width:"100px"},extra:"多选模式下生效,限制上传数量"},{default:l(()=>[t(x,{modelValue:o.value.limit,"onUpdate:modelValue":e[2]||(e[2]=i=>o.value.limit=i),max:10,min:1},null,8,["modelValue"])]),_:1})):r("",!0)])):r("",!0),d.value.view_type=="cityLinkage"?(n(),_("div",q,[t(B,{title:"提示"},{default:l(()=>e[10]||(e[10]=[g("p",null,"级联选择器返回的数据类型为 String",-1),g("p",null,"下拉框联动返回的数据类型为 Array",-1)])),_:1}),t(s,{class:"mt-3",label:"组件类型",field:"type","label-col-flex":"auto","label-col-style":{width:"100px"}},{default:l(()=>[t(w,{modelValue:o.value.type,"onUpdate:modelValue":e[3]||(e[3]=i=>o.value.type=i),placeholder:"默认为下拉框联动","allow-clear":""},{default:l(()=>[t(p,{value:"select"},{default:l(()=>e[11]||(e[11]=[m("下拉框联动")])),_:1}),t(p,{value:"cascader"},{default:l(()=>e[12]||(e[12]=[m("级联选择器")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(s,{class:"mt-3",label:"返回数据",field:"mode","label-col-flex":"auto","label-col-style":{width:"100px"}},{default:l(()=>[t(w,{modelValue:o.value.mode,"onUpdate:modelValue":e[4]||(e[4]=i=>o.value.mode=i),placeholder:"默认为省市名称","allow-clear":""},{default:l(()=>[t(p,{value:"name"},{default:l(()=>e[13]||(e[13]=[m("省市名称")])),_:1}),t(p,{value:"code"},{default:l(()=>e[14]||(e[14]=[m("省市编码")])),_:1})]),_:1},8,["modelValue"])]),_:1})])):r("",!0),["date"].includes(d.value.view_type)?(n(),_("div",z,[d.value.view_type=="date"?(n(),f(s,{key:0,class:"mt-3",label:"选择器类型",field:"formType","label-col-flex":"auto","label-col-style":{width:"120px"}},{default:l(()=>[t(w,{modelValue:o.value.mode,"onUpdate:modelValue":e[5]||(e[5]=i=>o.value.mode=i),"allow-clear":""},{default:l(()=>[t(p,{value:"date"},{default:l(()=>e[15]||(e[15]=[m("日期选择器")])),_:1}),t(p,{value:"week"},{default:l(()=>e[16]||(e[16]=[m("周选择器")])),_:1}),t(p,{value:"month"},{default:l(()=>e[17]||(e[17]=[m("月选择器")])),_:1}),t(p,{value:"quarter"},{default:l(()=>e[18]||(e[18]=[m("季度选择器")])),_:1}),t(p,{value:"year"},{default:l(()=>e[19]||(e[19]=[m("年选择器")])),_:1})]),_:1},8,["modelValue"])]),_:1})):r("",!0),o.value.mode=="date"?(n(),f(s,{key:1,class:"mt-3",label:"是否显示时间",field:"showTime","label-col-flex":"auto","label-col-style":{width:"120px"}},{default:l(()=>[t(V,{modelValue:o.value.showTime,"onUpdate:modelValue":e[6]||(e[6]=i=>o.value.showTime=i)},{default:l(()=>[t(v,{value:!0},{default:l(()=>e[20]||(e[20]=[m("是")])),_:1}),t(v,{value:!1},{default:l(()=>e[21]||(e[21]=[m("否")])),_:1})]),_:1},8,["modelValue"])]),_:1})):r("",!0)])):r("",!0)]),_:1},8,["model"])]),_:1},8,["visible"])}}},Ke=S(A,[["__scopeId","data-v-edc33898"]]);export{Ke as default};
