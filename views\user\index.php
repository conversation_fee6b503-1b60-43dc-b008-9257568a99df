<?php
/**
 * 用户列表视图
 */
use yii\helpers\Html;
use yii\grid\GridView;
use yii\widgets\Pjax;

$this->title = '用户管理';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="user-index">
    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('创建用户', ['create'], ['class' => 'btn btn-success']) ?>
        <?= Html::a('批量删除', '#', [
            'class' => 'btn btn-danger',
            'id' => 'batch-delete-btn',
            'style' => 'display:none'
        ]) ?>
    </p>

    <?php Pjax::begin(); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => [
            [
                'class' => 'yii\grid\CheckboxColumn',
                'checkboxOptions' => function ($model, $key, $index, $column) {
                    return ['value' => $model->id];
                }
            ],
            'id',
            'username',
            'email:email',
            'nickname',
            [
                'attribute' => 'status',
                'value' => function ($model) {
                    return $model->getStatusText();
                },
                'filter' => [
                    10 => '正常',
                    9 => '未激活',
                    0 => '已删除'
                ]
            ],
            [
                'attribute' => 'created_at',
                'format' => 'datetime'
            ],
            [
                'attribute' => 'last_login_at',
                'format' => 'datetime'
            ],
            [
                'class' => 'yii\grid\ActionColumn',
                'template' => '{view} {update} {delete}',
                'buttons' => [
                    'view' => function ($url, $model, $key) {
                        return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', $url, [
                            'title' => '查看',
                            'class' => 'btn btn-sm btn-info'
                        ]);
                    },
                    'update' => function ($url, $model, $key) {
                        return Html::a('<span class="glyphicon glyphicon-pencil"></span>', $url, [
                            'title' => '编辑',
                            'class' => 'btn btn-sm btn-primary'
                        ]);
                    },
                    'delete' => function ($url, $model, $key) {
                        return Html::a('<span class="glyphicon glyphicon-trash"></span>', $url, [
                            'title' => '删除',
                            'class' => 'btn btn-sm btn-danger',
                            'data' => [
                                'confirm' => '确定要删除这个用户吗？',
                                'method' => 'post',
                            ],
                        ]);
                    },
                ]
            ],
        ],
    ]); ?>

    <?php Pjax::end(); ?>
</div>

<?php
$this->registerJs("
// 批量删除功能
$(document).on('change', 'input[name=\"selection[]\"]', function() {
    var checked = $('input[name=\"selection[]\"]:checked');
    if (checked.length > 0) {
        $('#batch-delete-btn').show();
    } else {
        $('#batch-delete-btn').hide();
    }
});

$('#batch-delete-btn').click(function(e) {
    e.preventDefault();
    var ids = [];
    $('input[name=\"selection[]\"]:checked').each(function() {
        ids.push($(this).val());
    });
    
    if (ids.length === 0) {
        alert('请选择要删除的用户');
        return;
    }
    
    if (confirm('确定要删除选中的 ' + ids.length + ' 个用户吗？')) {
        $.post('" . \yii\helpers\Url::to(['batch-delete']) . "', {
            ids: ids
        }, function(data) {
            if (data.code === 200) {
                alert(data.message);
                $.pjax.reload({container: '#w0'});
            } else {
                alert(data.message);
            }
        }, 'json');
    }
});
");
?>
