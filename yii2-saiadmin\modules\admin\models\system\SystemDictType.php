<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\modules\admin\models\system;

use yii\db\ActiveRecord;

/**
 * 字典类型模型
 */
class SystemDictType extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    protected $table = 'sa_system_dict_type';

    public function dicts()
    {
        return $this->hasMany(SystemDictData::class, 'type_id', 'id');
    }

}