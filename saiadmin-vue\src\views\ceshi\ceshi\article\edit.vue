<template>
  <component
    is="a-modal"
    :width="tool.getDevice() === 'mobile' ? '100%' : '600px'"
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :ok-loading="loading"
    @cancel="close"
    @before-ok="submit">
    <!-- 表单信息 start -->
    <a-form ref="formRef" :model="formData" :rules="rules" :auto-label-width="true">
      <a-form-item label="" field="name">
        <a-input v-model="formData.name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="subname">
        <a-input v-model="formData.subname" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="pic">
        <a-input v-model="formData.pic" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="author">
        <a-input v-model="formData.author" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="content">
        <ma-wangEditor v-model="formData.content" :height="400" />
      </a-form-item>
      <a-form-item label="" field="readcount">
        <a-input v-model="formData.readcount" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="sort">
        <a-input v-model="formData.sort" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="createtime">
        <a-input v-model="formData.createtime" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="sendtime">
        <a-input v-model="formData.sendtime" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="reason">
        <a-input v-model="formData.reason" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="showname">
        <a-input v-model="formData.showname" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="showsubname">
        <a-input v-model="formData.showsubname" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="showreadcount">
        <a-input v-model="formData.showreadcount" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="showsendtime">
        <a-input v-model="formData.showsendtime" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="showauthor">
        <a-input v-model="formData.showauthor" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="是否可评论" field="canpl">
        <a-input v-model="formData.canpl" placeholder="请输入是否可评论" />
      </a-form-item>
      <a-form-item label="评论是否可回复" field="canplrp">
        <a-input v-model="formData.canplrp" placeholder="请输入评论是否可回复" />
      </a-form-item>
      <a-form-item label="" field="pinglun_check">
        <a-input v-model="formData.pinglun_check" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="zan">
        <a-input v-model="formData.zan" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="" field="pcid">
        <a-input v-model="formData.pcid" placeholder="请输入" />
      </a-form-item>
    </a-form>
    <!-- 表单信息 end -->
  </component>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import tool from '@/utils/tool'
import { Message, Modal } from '@arco-design/web-vue'
import api from '../../api/ceshi/article'

const emit = defineEmits(['success'])
// 引用定义
const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const mode = ref('')

let title = computed(() => {
  return '23' + (mode.value == 'add' ? '-新增' : '-编辑')
})

// 表单初始值
const initialFormData = {
  id: null,
  name: '',
  subname: '',
  pic: '',
  author: '',
  content: '',
  readcount: null,
  sort: null,
  createtime: null,
  sendtime: '',
  reason: '',
  showname: 1,
  showsubname: 1,
  showreadcount: 1,
  showsendtime: 1,
  showauthor: 1,
  canpl: 1,
  canplrp: 1,
  pinglun_check: null,
  zan: null,
  pcid: null,
}

// 表单信息
const formData = reactive({ ...initialFormData })

// 验证规则
const rules = {
  name: [{ required: true, message: '必需填写' }],
  subname: [{ required: true, message: '必需填写' }],
  showname: [{ required: true, message: '必需填写' }],
  showsubname: [{ required: true, message: '必需填写' }],
}

// 打开弹框
const open = async (type = 'add') => {
  mode.value = type
  // 重置表单数据
  Object.assign(formData, initialFormData)
  formRef.value.clearValidate()
  visible.value = true
  await initPage()
}

// 初始化页面数据
const initPage = async () => {}

// 设置数据
const setFormData = async (data) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key]
    }
  }
}

// 数据保存
const submit = async (done) => {
  const validate = await formRef.value?.validate()
  if (!validate) {
    loading.value = true
    let data = { ...formData }
    let result = {}
    if (mode.value === 'add') {
      // 添加数据
      data.id = undefined
      result = await api.save(data)
    } else {
      // 修改数据
      result = await api.update(data.id, data)
    }
    if (result.code === 200) {
      Message.success('操作成功')
      emit('success')
      done(true)
    }
    // 防止连续点击提交
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
  done(false)
}

// 关闭弹窗
const close = () => (visible.value = false)

defineExpose({ open, setFormData })
</script>
