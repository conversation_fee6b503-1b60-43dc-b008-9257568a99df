#!/bin/bash
# Yii 2.0 架构自动化测试脚本

echo "🧪 开始 Yii 2.0 架构测试..."

# 进入项目目录
cd yii2-saiadmin

# 检查Composer依赖
if [ ! -d "vendor" ]; then
    echo "📦 安装Composer依赖..."
    composer install --no-dev --optimize-autoloader
fi

# 检查控制台命令
echo "🔧 测试控制台命令..."
php yii help

# 测试Web应用
echo "🌐 测试Web应用..."
if command -v curl &> /dev/null; then
    echo "测试基本功能..."
    curl -s "http://localhost/yii2-saiadmin/web/test/index" | head -5
    
    echo "测试数据库连接..."
    curl -s "http://localhost/yii2-saiadmin/web/test/database" | head -5
    
    echo "测试缓存功能..."
    curl -s "http://localhost/yii2-saiadmin/web/test/cache" | head -5
    
    echo "测试日志功能..."
    curl -s "http://localhost/yii2-saiadmin/web/test/log" | head -5
else
    echo "⚠️ curl 命令不可用，跳过Web测试"
fi

echo "✅ 架构测试完成！"