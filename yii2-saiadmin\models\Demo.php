<?php
/**
 * 演示模型
 */
namespace app\models;

use app\components\base\BaseModel;

/**
 * Demo 模型
 */
class Demo extends BaseModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return "{{%demo}}";
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [["title"], "required"],
            [["content"], "string"],
            [["status"], "integer"],
            [["title"], "string", "max" => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            "id" => "ID",
            "title" => "标题",
            "content" => "内容",
            "status" => "状态",
            "created_at" => "创建时间",
            "updated_at" => "更新时间",
        ];
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusText()
    {
        $statusMap = [
            0 => "禁用",
            1 => "启用"
        ];
        
        return isset($statusMap[$this->status]) ? $statusMap[$this->status] : "未知";
    }
}