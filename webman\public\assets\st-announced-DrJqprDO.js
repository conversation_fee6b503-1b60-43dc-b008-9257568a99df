import{c as k}from"./index-DkGLNqVb.js";import{r as n,a as w,h as a,n as N,k as L,m as s,t as e,y as r,l as o,z as c}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const T={class:"w-full lg:w-9/12 ma-content-block rounded-sm p-3 mt-3"},B={class:"flex justify-between"},C=["innerHTML"],jt={__name:"st-announced",setup(V){const _=n([]),f=w([{title:"标题",dataIndex:"title",slotName:"title"},{title:"发布时间",dataIndex:"create_time",width:180,align:"right"}]),p=n({}),l=n(!1),y=async m=>{p.value=m,l.value=!0};return(async()=>{const m=await k.getNoticeList({limit:5,orderBy:"id",orderType:"desc"});_.value=m.data.data})(),(m,i)=>{const d=a("a-link"),v=a("a-table"),g=a("a-typography-title"),x=a("a-space"),u=a("a-typography-paragraph"),h=a("a-typography"),b=a("a-modal");return L(),N("div",T,[s("div",B,[i[2]||(i[2]=r(" 系统公告 ")),e(d,null,{default:o(()=>i[1]||(i[1]=[r("更多")])),_:1})]),e(v,{data:_.value,columns:f,class:"mt-2",pagination:!1},{title:o(({record:t})=>[e(d,{onClick:D=>y(t)},{default:o(()=>[r(c(t.title),1)]),_:2},1032,["onClick"])]),_:1},8,["data","columns"]),e(b,{visible:l.value,"onUpdate:visible":i[0]||(i[0]=t=>l.value=t),fullscreen:"",footer:!1},{title:o(()=>i[3]||(i[3]=[r("公告详情")])),default:o(()=>[e(h,{style:{marginTop:"-30px"}},{default:o(()=>[e(g,{class:"text-center"},{default:o(()=>{var t;return[r(c((t=p.value)==null?void 0:t.title),1)]}),_:1}),e(u,{class:"text-right",style:{"font-size":"13px",color:"var(--color-text-3)"}},{default:o(()=>[e(x,{size:"large"},{default:o(()=>{var t;return[s("span",null,"创建时间："+c((t=p.value)==null?void 0:t.create_time),1)]}),_:1})]),_:1}),e(u,null,{default:o(()=>{var t;return[s("div",{innerHTML:(t=p.value)==null?void 0:t.content},null,8,C)]}),_:1})]),_:1})]),_:1},8,["visible"])])}}};export{jt as default};
