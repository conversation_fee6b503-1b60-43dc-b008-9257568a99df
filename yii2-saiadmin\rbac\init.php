<?php
/**
 * RBAC权限初始化
 */
use yii\rbac\DbManager;

$authManager = new DbManager();
$authManager->db = Yii::$app->db;

// 创建权限
$createPost = $authManager->createPermission("createPost");
$createPost->description = "创建文章";
$authManager->add($createPost);

$updatePost = $authManager->createPermission("updatePost");
$updatePost->description = "更新文章";
$authManager->add($updatePost);

$deletePost = $authManager->createPermission("deletePost");
$deletePost->description = "删除文章";
$authManager->add($deletePost);

// 创建角色
$author = $authManager->createRole("author");
$author->description = "作者";
$authManager->add($author);
$authManager->addChild($author, $createPost);
$authManager->addChild($author, $updatePost);

$admin = $authManager->createRole("admin");
$admin->description = "管理员";
$authManager->add($admin);
$authManager->addChild($admin, $author);
$authManager->addChild($admin, $deletePost);

// 分配角色给用户
$authManager->assign($author, 2);
$authManager->assign($admin, 1);