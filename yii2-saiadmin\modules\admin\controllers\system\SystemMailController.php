<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\modules\admin\controllers\system;

use app\components\base\BaseController;
use plugin\saiadmin\app\logic\system\SystemMailLogic;
use plugin\saiadmin\app\validate\system\SystemMailValidate;
use support\Request;
use support\Response;

/**
 * 邮件记录控制器
 */
class SystemMailController extends BaseController
{
    /**
     * 构造
     */
    public function __construct()
    {
        $this->logic = new SystemMailLogic();
        $this->validate = new SystemMailValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['gateway', ''],
            ['from', ''],
            ['code', ''],
            ['email', ''],
            ['status', ''],
            ['create_time', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

}