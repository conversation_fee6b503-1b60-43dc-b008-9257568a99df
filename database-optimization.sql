-- <PERSON><PERSON><PERSON><PERSON> 数据库性能优化脚本
-- 执行前请备份数据库

-- 1. 用户表索引优化
ALTER TABLE `sa_system_user` 
ADD INDEX `idx_username` (`username`),
ADD INDEX `idx_phone` (`phone`),
ADD INDEX `idx_email` (`email`),
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_dept_id` (`dept_id`),
ADD INDEX `idx_create_time` (`create_time`),
ADD INDEX `idx_status_dept` (`status`, `dept_id`);

-- 2. 角色表索引优化
ALTER TABLE `sa_system_role` 
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_code` (`code`),
ADD INDEX `idx_level` (`level`);

-- 3. 菜单表索引优化
ALTER TABLE `sa_system_menu` 
ADD INDEX `idx_parent_id` (`parent_id`),
ADD INDEX `idx_type` (`type`),
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_sort` (`sort`);

-- 4. 部门表索引优化
ALTER TABLE `sa_system_dept` 
ADD INDEX `idx_parent_id` (`parent_id`),
ADD INDEX `idx_level` (`level`),
ADD INDEX `idx_status` (`status`);

-- 5. 日志表索引优化（重要：日志表数据量大）
ALTER TABLE `sa_system_oper_log` 
ADD INDEX `idx_username` (`username`),
ADD INDEX `idx_method` (`method`),
ADD INDEX `idx_create_time` (`create_time`),
ADD INDEX `idx_ip` (`ip`);

ALTER TABLE `sa_system_login_log` 
ADD INDEX `idx_username` (`username`),
ADD INDEX `idx_ip` (`ip`),
ADD INDEX `idx_login_time` (`login_time`),
ADD INDEX `idx_status` (`status`);

-- 6. 字典表索引优化
ALTER TABLE `sa_system_dict_type` 
ADD INDEX `idx_code` (`code`),
ADD INDEX `idx_status` (`status`);

ALTER TABLE `sa_system_dict_data` 
ADD INDEX `idx_type_id` (`type_id`),
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_sort` (`sort`);

-- 7. 附件表索引优化
ALTER TABLE `sa_system_attachment` 
ADD INDEX `idx_storage_mode` (`storage_mode`),
ADD INDEX `idx_mime_type` (`mime_type`),
ADD INDEX `idx_create_time` (`create_time`);

-- 8. 定时任务表索引优化
ALTER TABLE `sa_tool_crontab` 
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_type` (`type`);

-- 9. 创建复合索引提升查询性能
ALTER TABLE `sa_system_user` 
ADD INDEX `idx_dept_status_time` (`dept_id`, `status`, `create_time`);

ALTER TABLE `sa_system_oper_log` 
ADD INDEX `idx_user_time` (`username`, `create_time`);

-- 10. 优化表结构（如果需要）
-- 注意：以下操作会锁表，请在维护时间执行

-- 优化用户表字符集和存储引擎
-- ALTER TABLE `sa_system_user` ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 11. 分析表统计信息
ANALYZE TABLE `sa_system_user`;
ANALYZE TABLE `sa_system_role`;
ANALYZE TABLE `sa_system_menu`;
ANALYZE TABLE `sa_system_dept`;
ANALYZE TABLE `sa_system_oper_log`;
ANALYZE TABLE `sa_system_login_log`;

-- 12. 查看索引使用情况的查询语句
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     COLUMN_NAME,
--     CARDINALITY
-- FROM 
--     information_schema.STATISTICS 
-- WHERE 
--     TABLE_SCHEMA = 'saiadmin' 
--     AND TABLE_NAME LIKE 'sa_%'
-- ORDER BY 
--     TABLE_NAME, INDEX_NAME;

-- 执行完成后的验证查询
-- SHOW INDEX FROM `sa_system_user`;
-- EXPLAIN SELECT * FROM `sa_system_user` WHERE `status` = 1 AND `dept_id` = 1;
