<?php return array(
    'root' => array(
        'name' => 'workerman/webman',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '28bb6fbb22099abf9347376d308a5ffc2d22396f',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'brick/math' => array(
            'pretty_version' => '0.13.1',
            'version' => '0.13.1.0',
            'reference' => 'fc7ed316430118cc7836bf45faff18d5dfc8de04',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.1',
            'version' => '6.11.1.0',
            'reference' => 'd1e91ecf8c598d073d0995afa8cd5c75c6e19e66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'godruoyi/php-snowflake' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => 'b82b313f353e4cf3198b9e6286cd1b5e023e8626',
            'type' => 'library',
            'install_path' => __DIR__ . '/../godruoyi/php-snowflake',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v12.21.0',
            'version' => '12.21.0.0',
            'reference' => 'a048b4fbbef4742ff2eee843971bb8278239c610',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/conditionable' => array(
            'pretty_version' => 'v12.20.0',
            'version' => '12.20.0.0',
            'reference' => 'ec677967c1f2faf90b8428919124d2184a4c9b49',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/conditionable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v12.20.0',
            'version' => '12.20.0.0',
            'reference' => 'e0fe87237da006e7d26e1167b6241786d68923e8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v12.21.0',
            'version' => '12.21.0.0',
            'reference' => 'e862e5648ee34004fa56046b746f490dfa86c613',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/redis' => array(
            'pretty_version' => 'v12.21.0',
            'version' => '12.21.0.0',
            'reference' => 'f9bd649332a58298b63e8d254e414833ccfad15f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/redis',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/support' => array(
            'pretty_version' => 'v12.20.0',
            'version' => '12.20.0.0',
            'reference' => 'd538550b5136b1ecb8be9aa73a9044a9e4971546',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.10.0',
            'version' => '********',
            'reference' => '5cf826f2991858b54d5c3809bee745560a1042a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nelexa/zip' => array(
            'pretty_version' => '4.0.2',
            'version' => '*******',
            'reference' => '88a1b6549be813278ff2dd3b6b2ac188827634a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nelexa/zip',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.10.1',
            'version' => '********',
            'reference' => '1fd1935b2d90aef2f093c5e35f7ae1257c448d00',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '*******',
            'reference' => '181d480e08d9476e61381e04a71b34dc0432e812',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'openspout/openspout' => array(
            'pretty_version' => 'v4.28.5',
            'version' => '********',
            'reference' => 'ab05a09fe6fce57c90338f83280648a9786ce36b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openspout/openspout',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.10.0',
            'version' => '6.10.0.0',
            'reference' => 'bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '*******',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '344572933ad0181accbf4ba763e85a0306a8c5e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.9.0',
            'version' => '4.9.0.0',
            'reference' => '4e0e23cc785f0724a0e838279a9eb03f28b092a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.9.0',
            ),
        ),
        'saithink/saiadmin' => array(
            'pretty_version' => '5.0.6',
            'version' => '*******',
            'reference' => '5325483e2c7db9289d96b8b83b61b0ef3d94907c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../saithink/saiadmin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'saithink/saipackage' => array(
            'pretty_version' => '1.0.0',
            'version' => '*******',
            'reference' => '5d61afafaaa1d7a286e6bc63c4531c7e4d85aa15',
            'type' => 'library',
            'install_path' => __DIR__ . '/../saithink/saipackage',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '*******',
            'reference' => 'a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => '5d68a57d66910405e5c0b63d6f0af941e66fc868',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'b81435fbd6648ea425d1ee96a2d8e68f4ceacd24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '*******',
            'reference' => '241d5ac4910d256660238a7ecf250deba4c73063',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => 'df210c7a2573f1913b2d17cc95f90f53a73d8f7d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'c9a1168891b5aaadfd6332ef44393330b3498c4c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tinywan/jwt' => array(
            'pretty_version' => 'v1.11.3',
            'version' => '1.11.3.0',
            'reference' => '1b067c998d970c252b8ad113a460922f8108b9ac',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tinywan/jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tinywan/storage' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'reference' => '1a78d4b585c34b2c0ea2e3be0e67952aff0e780c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tinywan/storage',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-container' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'b2df244be1e7399ad4c8be1ccc40ed57868f730a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-helper' => array(
            'pretty_version' => 'v3.1.11',
            'version' => '3.1.11.0',
            'reference' => '1d6ada9b9f3130046bf6922fe1bd159c8d88a33c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-helper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-orm' => array(
            'pretty_version' => 'v3.0.34',
            'version' => '3.0.34.0',
            'reference' => '715e55da149fe32a12d68ef10e5b00e70bd3dbec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-orm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-validate' => array(
            'pretty_version' => 'v3.0.7',
            'version' => '3.0.7.0',
            'reference' => '85063f6d4ef8ed122f17a36179dc3e0949b30988',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-validate',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.21.1',
            'version' => '3.21.1.0',
            'reference' => '285123877d4dd97dd7c11842ac5fb7e86e60d81d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.2',
            'version' => '5.6.2.0',
            'reference' => '24ac4c74f91ee2c193fa1aaa5c249cb0822809af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webman/cache' => array(
            'pretty_version' => 'v2.1.2',
            'version' => '2.1.2.0',
            'reference' => '66a5461ea51d23364403b54ee218b736d26bb03f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webman/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webman/captcha' => array(
            'pretty_version' => 'v1.0.5',
            'version' => '1.0.5.0',
            'reference' => '0b2645b813466e4e70bff311511364080bad2ec5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webman/captcha',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webman/event' => array(
            'pretty_version' => 'v1.0.5',
            'version' => '1.0.5.0',
            'reference' => 'b1c3f6b70fd290e48288703d59bead0e28f9fb84',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webman/event',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webman/push' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'reference' => 'b9e51d39a6ae232eab6b3f5c48a918857976add8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webman/push',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webman/redis' => array(
            'pretty_version' => 'v2.1.3',
            'version' => '2.1.3.0',
            'reference' => '559eb1692d39c6fef5cf526223fff728be6c0fb9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webman/redis',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webman/think-orm' => array(
            'pretty_version' => 'v2.1.7',
            'version' => '2.1.7.0',
            'reference' => '9380f0fa22b7d28926c5f7b5b8f35b068f27e846',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webman/think-orm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/coroutine' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => 'df8fc428967d512a74a8a7d80355c1d40228c9fa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/coroutine',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/crontab' => array(
            'pretty_version' => 'v1.0.7',
            'version' => '1.0.7.0',
            'reference' => '74f51ca8204e8eb628e57bc0e640561d570da2cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/crontab',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/webman' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '28bb6fbb22099abf9347376d308a5ffc2d22396f',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/webman-framework' => array(
            'pretty_version' => 'v2.1.2',
            'version' => '2.1.2.0',
            'reference' => 'f803bd867f07bb0929faef060b59a19a44186bfc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/webman-framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'workerman/workerman' => array(
            'pretty_version' => 'v5.1.3',
            'version' => '*******',
            'reference' => '371f3a5decb28f1bd3464ae26d47ea1a4cf0a3c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/workerman',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/ip2region' => array(
            'pretty_version' => 'v2.0.6',
            'version' => '*******',
            'reference' => '66895178be204521e9f5ae9df0ea502893ee53b2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../zoujingli/ip2region',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
