<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace plugin\555\app\controller;

use plugin\saiadmin\basic\BaseController;
use plugin\555\app\logic\ArticleBannerLogic;
use plugin\555\app\validate\ArticleBannerValidate;
use support\Request;
use support\Response;

/**
 * 文章轮播图控制器
 */
class ArticleBannerController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new ArticleBannerLogic();
        $this->validate = new ArticleBannerValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['banner_type', ''],
            ['title', ''],
        ]);
        $data = $this->logic->tree($where);
        return $this->success($data);
    }

}
