import{t as v}from"./index-DkGLNqVb.js";import{a as b}from"./database-CTMAb1z4.js";import{r as e,a as _,h as m,j as h,k as x,l as p,t as w,y,a1 as I}from"./@vue-9ZIPiVZG.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./crypto-js-B6um_4t4.js";import"./lodash-fWIJiXPB.js";import"./qs-DkPR50Nj.js";import"./side-channel-DLYplXY8.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-Cq5rP8eY.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-B70DURAT.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const Rt={__name:"struct",setup(g,{expose:l}){const i=e(!1),r=e(),n=e([{label:"主键",value:"PRI"},{label:"唯一键",value:"UNI"},{label:"普通索引",value:"MUL"},{label:"",value:""}]),a=e({table:""}),s=_({api:b.getDetailed,pageSimple:!0,showSearch:!1,operationColumn:!1}),u=async()=>{var t;(t=r.value)==null||t.refresh()};return l({open:async t=>{a.value.table=t,i.value=!0,await u()}}),(t,o)=>{const c=m("sa-table"),d=m("a-modal");return x(),h(d,{visible:i.value,"onUpdate:visible":o[0]||(o[0]=f=>i.value=f),width:I(v).getDevice()==="mobile"?"100%":"900px",footer:!1},{title:p(()=>o[1]||(o[1]=[y("表结构信息")])),default:p(()=>[w(c,{ref_key:"tableRef",ref:r,options:s,searchForm:a.value,columns:[{title:"字段名称",dataIndex:"column_name",width:"180"},{title:"字段类型",dataIndex:"column_type",width:"100"},{title:"索引",dataIndex:"column_key",type:"dict",render:"tag",options:n.value,width:"100"},{title:"默认值",dataIndex:"default_value",width:"100"},{title:"字段注释",dataIndex:"column_comment",width:"180"}]},null,8,["options","searchForm","columns"])]),_:1},8,["visible","width"])}}};export{Rt as default};
