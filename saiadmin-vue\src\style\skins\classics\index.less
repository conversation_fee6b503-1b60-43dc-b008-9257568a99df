[mine-skin="classics"] {

    .logo {
      border-bottom: 0;
    }
  
    .arco-layout-sider,
    .arco-layout-sider-light,
    .arco-menu-light
    {
      background: #1c1e23;
    }
  
    .logo span,
    .arco-menu-light .arco-menu-pop-header,
    .layout-banner-header .banner-menus li {
      color: rgb(var(--primary-5));
      fill: rgb(var(--primary-5));
    }
  
    .arco-menu-light.arco-menu-horizontal .arco-menu-pop-header.arco-menu-selected:hover
    {
      background-color: #2e3033;
    }
  
    .arco-menu-light .arco-menu-item {
      background-color: rgba(0, 0, 0, 0);
      color: #fff;
      fill: #fff;
    }
  
    .arco-menu-light .arco-menu-item:hover,
    .arco-menu-light .arco-menu-inline-header:hover,
    .arco-menu-light .arco-menu-item.arco-menu-selected {
      // background-color: #ffffff14;
      background-color: rgb(var(--primary-6));
    }
  
  
    .ma-menu .icon,
    .arco-menu-selected .icon {
      fill: #fff;
    }
  
    .arco-menu-light .arco-menu-inline-header,
    .arco-menu-light .arco-menu-pop-header,
    .arco-menu-light .arco-menu-collapse-button,
    .arco-menu-light .arco-menu-collapse-button:hover {
      background-color: rgba(255, 255, 255, 0);
    }
  
    .arco-menu-light .arco-menu-inline-header.arco-menu-selected:hover,
    .arco-menu-light .arco-menu-pop-header:hover,
    .arco-menu-light .arco-menu-pop-header.arco-menu-selected {
      background-color: rgba(0, 0, 0, 0.35);
    }
  
    .arco-menu-light .arco-menu-pop-header .arco-icon,
    .arco-menu-light .arco-menu-pop-header .iconify-icon,
    .arco-menu-light .arco-menu-pop-header.arco-menu-selected .arco-menu-icon,
    .arco-menu-light .arco-menu-item .arco-icon,
    .arco-menu-light .arco-menu-item .arco-menu-icon,
    .arco-menu-light .arco-menu-inline-header .iconify-icon,
    .arco-menu-light .arco-menu-inline-header .arco-icon,
    .arco-menu-light .arco-menu-inline-header {
      color: #fff;
    }
  
    .layout-columns-left-panel {
  
      .menu-title {
        font-weight: bold;
        color: #fff;
        background-color: #232324;
        border-bottom: none;
      }
    }
  
    .layout-columns-left-panel .sider {
      background-color: #17171a;
    }


    
    .parent-menu.active {
        background-color: #ffffff14;
    }
    
    .parent-menu:hover {
      background-color: #ffffff14;
    }

  }
