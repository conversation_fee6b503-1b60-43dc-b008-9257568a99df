export default {
  // 特殊页
  'openForm': 'CRUD',

  // 首页菜单
  'home': 'Home',
  'dashboard': 'Dashboard',
  'userCenter': 'User Center',
  'message': 'Message Center',
  'setting:config': 'System Setting',
  'demo': 'Component Demo',

  // 权限
  'permission': 'Permission',
  'system:user': 'User Manage',
  'system:role': 'Role Manage',
  'system:dept': 'Department Manage',
  'system:menu': 'Menu Manage',
  'system:post': 'Post Manage',

  'dataCenter': 'Data Center',
  'system:dict': 'Dictionary',
  'system:attachment': 'Attached',
  'system:dataMaintain': 'Table Maintenance',
  'system:notice': 'Notice',
  'apps': 'App Center',
  'system:appGroup': 'App Group',
  'system:app': 'App Manage',
  'apis': 'Api Center',
  'system:apiGroup': 'Api Group',
  'system:api': 'Api Manage',

  // 监控
  'monitor': 'Monitor',
  'system:monitor:server': 'Server Monitor',
  'system:onlineUser': 'Online User',
  'system:cache': 'Cache Monitor',
  'system:monitor:rely': 'Reliance Monitor',
  'logs': 'Logs Monitor',
  'system:queueLog': 'Queue Logs',
  'system:loginLog': 'Login Logs',
  'system:operLog': 'Operation Logs',
  'system:apiLog': 'Apis Logs',

  // 工具
  'devTools': 'Tools',
  'setting:module': 'Module Manage',
  'setting:code': 'Code Generator',
  'setting:code:update': 'Edit the build information',
  'setting:crontab': 'Crontab',
  'setting:table': 'Table Designer',
  'systemInterface': 'System Apis',
  }