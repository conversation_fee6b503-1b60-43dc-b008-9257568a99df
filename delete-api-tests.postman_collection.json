{"info": {"name": "SaiAdmin 代码生成器删除功能测试", "description": "测试代码生成器的删除相关API", "version": "1.0.0"}, "item": [{"name": "单个删除", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\"ids\":\"1\"}"}, "url": {"raw": "{{base_url}}/admin/tool/generateTables/delete", "host": ["{{base_url}}"], "path": ["admin", "tool", "generateTables", "delete"]}}}, {"name": "批量删除", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\"ids\":[1,2,3]}"}, "url": {"raw": "{{base_url}}/admin/tool/generateTables/batchDelete", "host": ["{{base_url}}"], "path": ["admin", "tool", "generateTables", "batchDelete"]}}}, {"name": "清空所有", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "[]"}, "url": {"raw": "{{base_url}}/admin/tool/generateTables/clear", "host": ["{{base_url}}"], "path": ["admin", "tool", "generateTables", "clear"]}}}, {"name": "删除生成文件", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\"id\":\"1\"}"}, "url": {"raw": "{{base_url}}/admin/tool/generateTables/deleteGeneratedFiles", "host": ["{{base_url}}"], "path": ["admin", "tool", "generateTables", "deleteGeneratedFiles"]}}}]}