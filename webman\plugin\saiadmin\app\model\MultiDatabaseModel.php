<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 多数据源模型基类
// +----------------------------------------------------------------------
// | Author: AI Assistant (多数据源版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\model;

use think\Model;
use plugin\saiadmin\app\database\MultiDatabaseManager;

/**
 * 多数据源模型基类
 */
abstract class MultiDatabaseModel extends Model
{
    /**
     * 数据库连接名称
     * 子类可以重写此属性来指定使用的数据库
     */
    protected $connection = 'mysql';

    /**
     * 是否启用读写分离
     */
    protected $readWriteSeparation = false;

    /**
     * 强制使用写库的操作
     */
    protected $forceWriteOperations = ['save', 'update', 'delete', 'insert'];

    /**
     * 设置数据库连接
     * @param string $connection 连接名称
     * @return $this
     */
    public function setConnection($connection)
    {
        $this->connection = $connection;
        return $this;
    }

    /**
     * 获取数据库连接
     * @param bool $forceWrite 强制使用写库
     * @return \think\db\Query
     */
    public function getConnection($forceWrite = false)
    {
        if ($this->readWriteSeparation && !$forceWrite && env('DB_RW_SEPARATE', false)) {
            return MultiDatabaseManager::read();
        }

        return MultiDatabaseManager::connection($this->connection);
    }

    /**
     * 查询数据（优先使用读库）
     * @param array $where 查询条件
     * @return \think\Collection
     */
    public function selectFromRead($where = array())
    {
        $query = $this->getConnection(false);

        if (!empty($where)) {
            $query = $query->where($where);
        }

        return $query->select();
    }

    /**
     * 写入数据（强制使用写库）
     * @param array $data 数据
     * @return int|string
     */
    public function insertToWrite($data)
    {
        return $this->getConnection(true)->insert($data);
    }

    /**
     * 更新数据（强制使用写库）
     * @param array $data 数据
     * @param array $where 条件
     * @return int
     */
    public function updateToWrite($data, $where)
    {
        return $this->getConnection(true)->where($where)->update($data);
    }

    /**
     * 删除数据（强制使用写库）
     * @param array $where 条件
     * @return int
     */
    public function deleteFromWrite($where)
    {
        return $this->getConnection(true)->where($where)->delete();
    }
}

/**
 * 日志模型基类
 */
abstract class LogModel extends MultiDatabaseModel
{
    protected $connection = 'mysql_log';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = false;
}

/**
 * 缓存模型基类
 */
abstract class CacheModel extends MultiDatabaseModel
{
    protected $connection = 'mysql_cache';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
}

/**
 * 操作日志模型
 */
class OperationLog extends LogModel
{
    protected $table = 'log_operation';
    protected $pk = 'id';

    /**
     * 记录操作日志
     * @param array $data 日志数据
     * @return bool
     */
    public static function record($data)
    {
        try {
            $log = new self();
            $log->save([
                'user_id' => $data['user_id'] ?? 0,
                'username' => $data['username'] ?? '',
                'method' => $data['method'] ?? '',
                'router' => $data['router'] ?? '',
                'service_name' => $data['service_name'] ?? '',
                'ip' => $data['ip'] ?? '',
                'ip_location' => $data['ip_location'] ?? '',
                'request_data' => json_encode($data['request_data'] ?? []),
                'response_code' => $data['response_code'] ?? '',
                'response_data' => json_encode($data['response_data'] ?? []),
            ]);
            return true;
        } catch (\Exception $e) {
            error_log('操作日志记录失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取用户操作日志
     * @param int $userId 用户ID
     * @param int $limit 限制数量
     * @return array
     */
    public static function getUserLogs($userId, $limit = 50)
    {
        $model = new self();
        return $model->where('user_id', $userId)
            ->order('created_at', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
}

/**
 * 登录日志模型
 */
class LoginLog extends LogModel
{
    protected $table = 'log_login';
    protected $pk = 'id';

    /**
     * 记录登录日志
     * @param array $data 日志数据
     * @return bool
     */
    public static function record(array $data): bool
    {
        try {
            $log = new self();
            $log->save([
                'username' => $data['username'] ?? '',
                'ip' => $data['ip'] ?? '',
                'ip_location' => $data['ip_location'] ?? '',
                'os' => $data['os'] ?? '',
                'browser' => $data['browser'] ?? '',
                'status' => $data['status'] ?? 1,
                'message' => $data['message'] ?? '',
            ]);
            return true;
        } catch (\Exception $e) {
            error_log('登录日志记录失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取登录统计
     * @param string $date 日期
     * @return array
     */
    public static function getLoginStats(string $date = null): array
    {
        $date = $date ?: date('Y-m-d');
        $model = new self();

        $total = $model->whereTime('login_time', $date)->count();
        $success = $model->whereTime('login_time', $date)->where('status', 1)->count();
        $failed = $model->whereTime('login_time', $date)->where('status', 2)->count();

        return [
            'date' => $date,
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0
        ];
    }
}

/**
 * 性能日志模型
 */
class PerformanceLog extends LogModel
{
    protected $table = 'log_performance';
    protected $pk = 'id';

    /**
     * 记录性能日志
     * @param array $data 日志数据
     * @return bool
     */
    public static function record(array $data): bool
    {
        try {
            $log = new self();
            $log->save([
                'request_id' => $data['request_id'] ?? '',
                'uri' => $data['uri'] ?? '',
                'method' => $data['method'] ?? '',
                'response_time' => $data['response_time'] ?? 0,
                'memory_usage' => $data['memory_usage'] ?? 0,
                'query_count' => $data['query_count'] ?? 0,
            ]);
            return true;
        } catch (\Exception $e) {
            error_log('性能日志记录失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取性能统计
     * @param string $date 日期
     * @return array
     */
    public static function getPerformanceStats(string $date = null): array
    {
        $date = $date ?: date('Y-m-d');
        $model = new self();

        $stats = $model->whereTime('created_at', $date)
            ->field([
                'COUNT(*) as total_requests',
                'AVG(response_time) as avg_response_time',
                'MAX(response_time) as max_response_time',
                'AVG(memory_usage) as avg_memory_usage',
                'MAX(memory_usage) as max_memory_usage',
                'AVG(query_count) as avg_query_count'
            ])
            ->find();

        return $stats ? $stats->toArray() : [];
    }
}

/**
 * 缓存数据模型
 */
class CacheData extends CacheModel
{
    protected $table = 'cache_data';
    protected $pk = 'cache_key';

    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 过期时间（秒）
     * @return bool
     */
    public static function setCache(string $key, $value, int $ttl = 3600): bool
    {
        try {
            $cache = new self();
            $expireTime = date('Y-m-d H:i:s', time() + $ttl);

            $cache->save([
                'cache_key' => $key,
                'cache_value' => serialize($value),
                'expire_time' => $expireTime,
            ], true); // 使用replace模式

            return true;
        } catch (\Exception $e) {
            error_log('缓存设置失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取缓存
     * @param string $key 缓存键
     * @return mixed|null
     */
    public static function getCache(string $key)
    {
        try {
            $cache = new self();
            $data = $cache->where('cache_key', $key)
                ->where('expire_time', '>', date('Y-m-d H:i:s'))
                ->find();

            if ($data) {
                return unserialize($data->cache_value);
            }

            return null;
        } catch (\Exception $e) {
            error_log('缓存获取失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public static function deleteCache(string $key): bool
    {
        try {
            $cache = new self();
            return $cache->where('cache_key', $key)->delete() > 0;
        } catch (\Exception $e) {
            error_log('缓存删除失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 清理过期缓存
     * @return int 清理数量
     */
    public static function cleanExpired(): int
    {
        try {
            $cache = new self();
            return $cache->where('expire_time', '<', date('Y-m-d H:i:s'))->delete();
        } catch (\Exception $e) {
            error_log('过期缓存清理失败: ' . $e->getMessage());
            return 0;
        }
    }
}
