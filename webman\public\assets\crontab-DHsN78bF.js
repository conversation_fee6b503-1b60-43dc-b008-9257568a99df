import{h as e}from"./index-ybrmzYq5.js";const a={getPageList(t={}){return e({url:"/tool/crontab/index",method:"get",params:t})},getLogPageList(t={}){return e({url:"/tool/crontab/logPageList",method:"get",params:t})},deleteLog(t){return e({url:"/tool/crontab/deleteCrontabLog",method:"delete",data:t})},run(t={}){return e({url:"/tool/crontab/run",method:"post",data:t})},read(t){return e({url:"/tool/crontab/read?id="+t,method:"get"})},save(t={}){return e({url:"/tool/crontab/save",method:"post",data:t})},destroy(t){return e({url:"/tool/crontab/destroy",method:"delete",data:t})},update(t,o={}){return e({url:"/tool/crontab/update?id="+t,method:"put",data:o})},changeStatus(t={}){return e({url:"/tool/crontab/changeStatus",method:"post",data:t})}};export{a};
