import {
  registerLanguage
} from "./chunk-TIAC434R.js";
import "./chunk-OX74CKBW.js";
import "./chunk-WBIRFOMM.js";
import "./chunk-LK32TJAX.js";

// node_modules/monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution.js
registerLanguage({
  id: "javascript",
  extensions: [".js", ".es6", ".jsx", ".mjs", ".cjs"],
  firstLine: "^#!.*\\bnode",
  filenames: ["jakefile"],
  aliases: ["JavaScript", "javascript", "js"],
  mimetypes: ["text/javascript"],
  loader: () => {
    if (false) {
      return new Promise((resolve, reject) => {
        __require(["vs/basic-languages/javascript/javascript"], resolve, reject);
      });
    } else {
      return import("./javascript-77UZDS7Y.js");
    }
  }
});
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=monaco-editor_esm_vs_basic-languages_javascript_javascript__contribution.js.map
