import{c as M}from"./color-name-BQ5IbGbl.js";import{s as F}from"./simple-swizzle-BUB9Iq-C.js";var g={exports:{}},d=M,i=F,m=Object.hasOwnProperty,w=Object.create(null);for(var b in d)m.call(d,b)&&(w[d[b]]=b);var l=g.exports={to:{},get:{}};l.get=function(r){var n=r.substring(0,3).toLowerCase(),t,o;switch(n){case"hsl":t=l.get.hsl(r),o="hsl";break;case"hwb":t=l.get.hwb(r),o="hwb";break;default:t=l.get.rgb(r),o="rgb";break}return t?{model:o,value:t}:null};l.get.rgb=function(r){if(!r)return null;var n=/^#([a-f0-9]{3,4})$/i,t=/^#([a-f0-9]{6})([a-f0-9]{2})?$/i,o=/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,v=/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,f=/^(\w+)$/,s=[0,0,0,1],a,e,h;if(a=r.match(t)){for(h=a[2],a=a[1],e=0;e<3;e++){var p=e*2;s[e]=parseInt(a.slice(p,p+2),16)}h&&(s[3]=parseInt(h,16)/255)}else if(a=r.match(n)){for(a=a[1],h=a[3],e=0;e<3;e++)s[e]=parseInt(a[e]+a[e],16);h&&(s[3]=parseInt(h+h,16)/255)}else if(a=r.match(o)){for(e=0;e<3;e++)s[e]=parseInt(a[e+1],0);a[4]&&(a[5]?s[3]=parseFloat(a[4])*.01:s[3]=parseFloat(a[4]))}else if(a=r.match(v)){for(e=0;e<3;e++)s[e]=Math.round(parseFloat(a[e+1])*2.55);a[4]&&(a[5]?s[3]=parseFloat(a[4])*.01:s[3]=parseFloat(a[4]))}else return(a=r.match(f))?a[1]==="transparent"?[0,0,0,0]:m.call(d,a[1])?(s=d[a[1]],s[3]=1,s):null:null;for(e=0;e<3;e++)s[e]=u(s[e],0,255);return s[3]=u(s[3],0,1),s};l.get.hsl=function(r){if(!r)return null;var n=/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,t=r.match(n);if(t){var o=parseFloat(t[4]),v=(parseFloat(t[1])%360+360)%360,f=u(parseFloat(t[2]),0,100),s=u(parseFloat(t[3]),0,100),a=u(isNaN(o)?1:o,0,1);return[v,f,s,a]}return null};l.get.hwb=function(r){if(!r)return null;var n=/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,t=r.match(n);if(t){var o=parseFloat(t[4]),v=(parseFloat(t[1])%360+360)%360,f=u(parseFloat(t[2]),0,100),s=u(parseFloat(t[3]),0,100),a=u(isNaN(o)?1:o,0,1);return[v,f,s,a]}return null};l.to.hex=function(){var r=i(arguments);return"#"+c(r[0])+c(r[1])+c(r[2])+(r[3]<1?c(Math.round(r[3]*255)):"")};l.to.rgb=function(){var r=i(arguments);return r.length<4||r[3]===1?"rgb("+Math.round(r[0])+", "+Math.round(r[1])+", "+Math.round(r[2])+")":"rgba("+Math.round(r[0])+", "+Math.round(r[1])+", "+Math.round(r[2])+", "+r[3]+")"};l.to.rgb.percent=function(){var r=i(arguments),n=Math.round(r[0]/255*100),t=Math.round(r[1]/255*100),o=Math.round(r[2]/255*100);return r.length<4||r[3]===1?"rgb("+n+"%, "+t+"%, "+o+"%)":"rgba("+n+"%, "+t+"%, "+o+"%, "+r[3]+")"};l.to.hsl=function(){var r=i(arguments);return r.length<4||r[3]===1?"hsl("+r[0]+", "+r[1]+"%, "+r[2]+"%)":"hsla("+r[0]+", "+r[1]+"%, "+r[2]+"%, "+r[3]+")"};l.to.hwb=function(){var r=i(arguments),n="";return r.length>=4&&r[3]!==1&&(n=", "+r[3]),"hwb("+r[0]+", "+r[1]+"%, "+r[2]+"%"+n+")"};l.to.keyword=function(r){return w[r.slice(0,3)]};function u(r,n,t){return Math.min(Math.max(n,r),t)}function c(r){var n=Math.round(r).toString(16).toUpperCase();return n.length<2?"0"+n:n}var $=g.exports;export{$ as c};
