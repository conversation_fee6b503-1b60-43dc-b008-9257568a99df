{"name": "@wangeditor/upload-image-module", "version": "1.0.2", "description": "wangEditor upload-image module", "author": "wangfupeng1988 <<EMAIL>>", "contributors": [], "homepage": "https://github.com/wangeditor-team/wangEditor#readme", "license": "MIT", "types": "dist/upload-image-module/src/index.d.ts", "main": "dist/index.js", "module": "dist/index.esm.js", "browser": {"./dist/index.js": "./dist/index.js", "./dist/index.esm.js": "./dist/index.esm.js"}, "directories": {"lib": "dist", "test": "__tests__"}, "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.com/"}, "repository": {"type": "git", "url": "git+https://github.com/wangeditor-team/wangEditor.git"}, "scripts": {"test": "jest", "test-c": "jest --coverage", "dev": "cross-env NODE_ENV=development rollup -c rollup.config.js", "dev-watch": "cross-env NODE_ENV=development rollup -c rollup.config.js -w", "build": "cross-env NODE_ENV=production rollup -c rollup.config.js", "dev-size-stats": "cross-env NODE_ENV=development:size_stats rollup -c rollup.config.js", "size-stats": "cross-env NODE_ENV=production:size_stats rollup -c rollup.config.js"}, "bugs": {"url": "https://github.com/wangeditor-team/wangEditor/issues"}, "peerDependencies": {"@uppy/core": "^2.0.3", "@uppy/xhr-upload": "^2.0.3", "@wangeditor/basic-modules": "1.x", "@wangeditor/core": "1.x", "dom7": "^3.0.0", "lodash.foreach": "^4.5.0", "slate": "^0.72.0", "snabbdom": "^3.1.0"}, "gitHead": "5de4d52cda133b945020e457f1de7b6787e842c1"}