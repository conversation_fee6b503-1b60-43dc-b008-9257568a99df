<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ] - 优化缓存类
// +----------------------------------------------------------------------
// | Author: AI Assistant (缓存优化版本)
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\cache;

/**
 * 优化的缓存管理类
 */
class OptimizedCache
{
    /**
     * 缓存前缀
     */
    private static $prefix = 'saiadmin:';
    
    /**
     * 默认过期时间（秒）
     */
    private static $defaultTtl = 3600;
    
    /**
     * 缓存标签管理
     */
    private static $tags = [];
    
    /**
     * 设置缓存（支持标签）
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 过期时间
     * @param array $tags 缓存标签
     * @return bool
     */
    public static function set(string $key, $value, int $ttl = null, array $tags = []): bool
    {
        $ttl = $ttl ?? self::$defaultTtl;
        $fullKey = self::$prefix . $key;
        
        // 序列化数据
        $data = [
            'value' => $value,
            'expire_time' => time() + $ttl,
            'tags' => $tags,
            'created_at' => time()
        ];
        
        // 存储到缓存
        $result = cache()->set($fullKey, serialize($data), $ttl);
        
        // 管理标签
        if (!empty($tags)) {
            self::manageTags($key, $tags);
        }
        
        return $result;
    }
    
    /**
     * 获取缓存
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $fullKey = self::$prefix . $key;
        $data = cache()->get($fullKey);
        
        if ($data === null) {
            return $default;
        }
        
        $data = unserialize($data);
        
        // 检查是否过期
        if (isset($data['expire_time']) && time() > $data['expire_time']) {
            self::delete($key);
            return $default;
        }
        
        return $data['value'] ?? $default;
    }
    
    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public static function delete(string $key): bool
    {
        $fullKey = self::$prefix . $key;
        
        // 清理标签关联
        self::removeFromTags($key);
        
        return cache()->delete($fullKey);
    }
    
    /**
     * 批量删除缓存
     * @param array $keys 缓存键数组
     * @return bool
     */
    public static function deleteMultiple(array $keys): bool
    {
        $success = true;
        foreach ($keys as $key) {
            if (!self::delete($key)) {
                $success = false;
            }
        }
        return $success;
    }
    
    /**
     * 根据标签删除缓存
     * @param string $tag 标签名
     * @return bool
     */
    public static function deleteByTag(string $tag): bool
    {
        $tagKey = self::$prefix . 'tags:' . $tag;
        $keys = cache()->get($tagKey);
        
        if (!$keys) {
            return true;
        }
        
        $keys = unserialize($keys);
        $success = true;
        
        foreach ($keys as $key) {
            if (!self::delete($key)) {
                $success = false;
            }
        }
        
        // 删除标签记录
        cache()->delete($tagKey);
        
        return $success;
    }
    
    /**
     * 记住缓存（如果不存在则设置）
     * @param string $key 缓存键
     * @param callable $callback 回调函数
     * @param int $ttl 过期时间
     * @param array $tags 缓存标签
     * @return mixed
     */
    public static function remember(string $key, callable $callback, int $ttl = null, array $tags = [])
    {
        $value = self::get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        self::set($key, $value, $ttl, $tags);
        
        return $value;
    }
    
    /**
     * 增加缓存值（原子操作）
     * @param string $key 缓存键
     * @param int $value 增加的值
     * @return int|false
     */
    public static function increment(string $key, int $value = 1)
    {
        $fullKey = self::$prefix . $key;
        $current = self::get($key, 0);
        $new = $current + $value;
        
        if (self::set($key, $new)) {
            return $new;
        }
        
        return false;
    }
    
    /**
     * 减少缓存值（原子操作）
     * @param string $key 缓存键
     * @param int $value 减少的值
     * @return int|false
     */
    public static function decrement(string $key, int $value = 1)
    {
        return self::increment($key, -$value);
    }
    
    /**
     * 清空所有缓存
     * @return bool
     */
    public static function flush(): bool
    {
        // 获取所有以前缀开头的键
        $pattern = self::$prefix . '*';
        
        // 这里需要根据具体的缓存驱动实现
        // 如果是Redis，可以使用SCAN命令
        // 如果是文件缓存，需要遍历缓存目录
        
        return cache()->clear();
    }
    
    /**
     * 获取缓存统计信息
     * @return array
     */
    public static function getStats(): array
    {
        return [
            'prefix' => self::$prefix,
            'default_ttl' => self::$defaultTtl,
            'tags_count' => count(self::$tags),
            'memory_usage' => memory_get_usage(true),
            'timestamp' => time()
        ];
    }
    
    /**
     * 管理缓存标签
     * @param string $key 缓存键
     * @param array $tags 标签数组
     */
    private static function manageTags(string $key, array $tags): void
    {
        foreach ($tags as $tag) {
            $tagKey = self::$prefix . 'tags:' . $tag;
            $keys = cache()->get($tagKey);
            
            if ($keys) {
                $keys = unserialize($keys);
            } else {
                $keys = [];
            }
            
            if (!in_array($key, $keys)) {
                $keys[] = $key;
                cache()->set($tagKey, serialize($keys), self::$defaultTtl * 24); // 标签缓存更长时间
            }
        }
    }
    
    /**
     * 从标签中移除键
     * @param string $key 缓存键
     */
    private static function removeFromTags(string $key): void
    {
        // 获取该键的标签信息
        $fullKey = self::$prefix . $key;
        $data = cache()->get($fullKey);
        
        if ($data) {
            $data = unserialize($data);
            $tags = $data['tags'] ?? [];
            
            foreach ($tags as $tag) {
                $tagKey = self::$prefix . 'tags:' . $tag;
                $keys = cache()->get($tagKey);
                
                if ($keys) {
                    $keys = unserialize($keys);
                    $keys = array_filter($keys, fn($k) => $k !== $key);
                    
                    if (empty($keys)) {
                        cache()->delete($tagKey);
                    } else {
                        cache()->set($tagKey, serialize($keys), self::$defaultTtl * 24);
                    }
                }
            }
        }
    }
    
    /**
     * 预热缓存
     * @param array $warmupData 预热数据
     */
    public static function warmup(array $warmupData): void
    {
        foreach ($warmupData as $key => $config) {
            if (isset($config['callback'])) {
                $value = $config['callback']();
                $ttl = $config['ttl'] ?? self::$defaultTtl;
                $tags = $config['tags'] ?? [];
                
                self::set($key, $value, $ttl, $tags);
            }
        }
    }
}
