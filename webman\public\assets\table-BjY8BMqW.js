import{l as z}from"./lodash-fWIJiXPB.js";import{a as k}from"./table-BHRaYvrI.js";import{_ as E}from"./index-ybrmzYq5.js";import{r as g,a as y,h as s,j as V,k as h,l,m as a,t as i,y as C,z as p,C as M}from"./@vue-9ZIPiVZG.js";import"./@arco-design-uttiljWv.js";import"./resize-observer-polyfill-B1PUzC5B.js";import"./compute-scroll-into-view-1gs_hJI2.js";import"./b-tween-BtQQsX34.js";import"./dayjs-DUkVwsK-.js";import"./number-precision-BW_FzNZC.js";import"./scroll-into-view-if-needed-SxpMpKWN.js";import"./b-validate-DHOn5MGm.js";import"./vue-QIJ1KGct.js";import"./color-JIMhKyf3.js";import"./color-string-Ckj7g19G.js";import"./color-name-BQ5IbGbl.js";import"./simple-swizzle-BUB9Iq-C.js";import"./is-arrayish-BII_q15j.js";import"./@wangeditor-Bg8kJaak.js";import"./pinia-CtMvrpix.js";import"./axios-BOAqGR8s.js";import"./crypto-js-B6um_4t4.js";import"./qs-CGfOb-kZ.js";import"./side-channel-0xN0c_x9.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-Pz2pmunN.js";import"./side-channel-list-asz5kCf8.js";import"./side-channel-map-DBz1yoQn.js";import"./get-intrinsic-CCph2EoF.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-cKMTtFGz.js";import"./dunder-proto-CiSsr-aM.js";import"./call-bind-apply-helpers-DFdvRtIg.js";import"./function-bind-CHqF18-c.js";import"./hasown-DwiY0sux.js";import"./call-bound-BMZ_xw6V.js";import"./side-channel-weakmap-CMrfu08b.js";import"./vue-router-DXldG2q0.js";import"./nprogress-DxiOKil-.js";import"./monaco-editor-nMXQdunA.js";import"./file2md5-B4-SI92N.js";import"./spark-md5-D8tidE2e.js";import"./lodash.noop-BeiKyXVG.js";import"./vue-color-kit-w75Wyu4C.js";import"./vue-clipboard3-DpvFlCWw.js";import"./clipboard-ehac6u_s.js";import"./vue-echarts-B-rvonkO.js";import"./resize-detector-G6vbKCU7.js";import"./echarts-Cz-L25MO.js";import"./tslib-BDyQ-Jie.js";import"./zrender-xbpiMqDc.js";import"./@iconify-BfRLEUc9.js";import"./vue-i18n-PeqK6azz.js";import"./@intlify-CJ2pDqUV.js";const O={class:"ma-content-block lg:flex justify-between"},q={class:"lg:w-8/12 w-full"},A={class:"lg:w-4/12 pt-4 pl-2 pr-2"},G={__name:"table",emits:["choose"],setup(H,{expose:I,emit:U}){const D=U,c=g(!1),v=g(),u=g([]),t=y({field_label:"",field_value:"",url:""}),R=()=>{t.field_label="",t.field_value="",t.url="",u.value=[]},S=()=>{D("choose",t),c.value=!1},x=g({table_name:""}),T=y({pk:"id",api:k.getPageList,height:"650px",rowSelection:{type:"radio"},operationColumn:!1}),B=y([{title:"表名称",dataIndex:"table_name",width:160,align:"left"},{title:"表描述",dataIndex:"table_comment",width:120,align:"left"},{title:"应用类型",dataIndex:"template",width:100},{title:"应用名称",dataIndex:"namespace",width:100},{title:"类名称",dataIndex:"class_name",width:150},{title:"类型",dataIndex:"tpl_category",width:110},{title:"数据源",dataIndex:"source",width:100}]),F=async r=>{var o;if(t.field_label="",t.field_value="",t.url="",r.length>0){const _=await k.getTableColumns({table_id:r[0]});u.value=_.data;const d=(o=v.value)==null?void 0:o.getTableData(),w=z.findIndex(d,f=>f.id===r[0]),n=d[w];let m="";n.template=="plugin"?m="/app/"+n.namespace+(n.package_name!=""?"/"+n.package_name:"")+"/"+n.class_name:m="/"+n.namespace+(n.package_name!=""?"/"+n.package_name:"")+"/"+n.class_name,m=m+"/index",n.tpl_category!="tree"&&(m=m+"?saiType=all"),t.url=m}else u.value=[]},N=async()=>{c.value=!0,await M(),j()},j=()=>{var r;(r=v.value)==null||r.refresh()};return I({open:N}),(r,o)=>{const _=s("a-input"),d=s("a-form-item"),w=s("a-col"),n=s("a-tag"),m=s("sa-table"),f=s("a-select"),b=s("a-space"),L=s("a-card"),P=s("a-modal");return h(),V(P,{fullscreen:"",visible:c.value,"onUpdate:visible":o[4]||(o[4]=e=>c.value=e),title:"选择数据模型",onOk:S,"unmount-on-close":""},{default:l(()=>[a("div",O,[a("div",q,[i(m,{ref_key:"crudRef",ref:v,options:T,columns:B,searchForm:x.value,onSelectionChange:F,onResetSearch:R},{tableSearch:l(()=>[i(w,{span:8},{default:l(()=>[i(d,{field:"table_name",label:"表名称"},{default:l(()=>[i(_,{modelValue:x.value.table_name,"onUpdate:modelValue":o[0]||(o[0]=e=>x.value.table_name=e),placeholder:"请输入数据表名称","allow-clear":""},null,8,["modelValue"])]),_:1})]),_:1})]),tpl_category:l(({record:e})=>[e.tpl_category=="single"?(h(),V(n,{key:0,color:"green"},{default:l(()=>o[5]||(o[5]=[C("单表CRUD")])),_:1})):(h(),V(n,{key:1,color:"red"},{default:l(()=>o[6]||(o[6]=[C("树表CRUD")])),_:1}))]),_:1},8,["options","columns","searchForm"])]),a("div",A,[i(L,{title:"配置区域"},{default:l(()=>[i(b,{direction:"vertical",fill:""},{default:l(()=>[i(b,null,{default:l(()=>[o[7]||(o[7]=a("span",{class:"config-label"},"字段Label：",-1)),i(f,{style:{width:"320px"},modelValue:t.field_label,"onUpdate:modelValue":o[1]||(o[1]=e=>t.field_label=e),"field-names":{label:"column_comment",value:"column_name"},options:u.value||[]},{label:l(({data:e})=>[a("span",null,p(e==null?void 0:e.column_comment)+" - "+p(e==null?void 0:e.column_name),1)]),option:l(({data:e})=>[a("span",null,p(e==null?void 0:e.column_comment)+" - "+p(e==null?void 0:e.column_name),1)]),_:1},8,["modelValue","options"])]),_:1}),i(b,null,{default:l(()=>[o[8]||(o[8]=a("span",{class:"config-label"},"字段Value：",-1)),i(f,{style:{width:"320px"},modelValue:t.field_value,"onUpdate:modelValue":o[2]||(o[2]=e=>t.field_value=e),"field-names":{label:"column_comment",value:"column_name"},options:u.value||[]},{label:l(({data:e})=>[a("span",null,p(e==null?void 0:e.column_comment)+" - "+p(e==null?void 0:e.column_name),1)]),option:l(({data:e})=>[a("span",null,p(e==null?void 0:e.column_comment)+" - "+p(e==null?void 0:e.column_name),1)]),_:1},8,["modelValue","options"])]),_:1}),i(b,null,{default:l(()=>[o[9]||(o[9]=a("span",{class:"config-label"},"请求地址：",-1)),i(_,{modelValue:t.url,"onUpdate:modelValue":o[3]||(o[3]=e=>t.url=e),style:{width:"320px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])])]),_:1},8,["visible"])}}},Xe=E(G,[["__scopeId","data-v-a53dfa69"]]);export{Xe as default};
