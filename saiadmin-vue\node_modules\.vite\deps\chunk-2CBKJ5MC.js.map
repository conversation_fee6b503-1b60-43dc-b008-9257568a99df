{"version": 3, "sources": ["../../echarts/lib/component/title/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport ComponentModel from '../../model/Component.js';\nimport ComponentView from '../../view/Component.js';\nimport { windowOpen } from '../../util/format.js';\nvar TitleModel = /** @class */function (_super) {\n  __extends(TitleModel, _super);\n  function TitleModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TitleModel.type;\n    _this.layoutMode = {\n      type: 'box',\n      ignoreSize: true\n    };\n    return _this;\n  }\n  TitleModel.type = 'title';\n  TitleModel.defaultOption = {\n    // zlevel: 0,\n    z: 6,\n    show: true,\n    text: '',\n    target: 'blank',\n    subtext: '',\n    subtarget: 'blank',\n    left: 0,\n    top: 0,\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    borderWidth: 0,\n    padding: 5,\n    itemGap: 10,\n    textStyle: {\n      fontSize: 18,\n      fontWeight: 'bold',\n      color: '#464646'\n    },\n    subtextStyle: {\n      fontSize: 12,\n      color: '#6E7079'\n    }\n  };\n  return TitleModel;\n}(ComponentModel);\n// View\nvar TitleView = /** @class */function (_super) {\n  __extends(TitleView, _super);\n  function TitleView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TitleView.type;\n    return _this;\n  }\n  TitleView.prototype.render = function (titleModel, ecModel, api) {\n    this.group.removeAll();\n    if (!titleModel.get('show')) {\n      return;\n    }\n    var group = this.group;\n    var textStyleModel = titleModel.getModel('textStyle');\n    var subtextStyleModel = titleModel.getModel('subtextStyle');\n    var textAlign = titleModel.get('textAlign');\n    var textVerticalAlign = zrUtil.retrieve2(titleModel.get('textBaseline'), titleModel.get('textVerticalAlign'));\n    var textEl = new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: titleModel.get('text'),\n        fill: textStyleModel.getTextColor()\n      }, {\n        disableBox: true\n      }),\n      z2: 10\n    });\n    var textRect = textEl.getBoundingRect();\n    var subText = titleModel.get('subtext');\n    var subTextEl = new graphic.Text({\n      style: createTextStyle(subtextStyleModel, {\n        text: subText,\n        fill: subtextStyleModel.getTextColor(),\n        y: textRect.height + titleModel.get('itemGap'),\n        verticalAlign: 'top'\n      }, {\n        disableBox: true\n      }),\n      z2: 10\n    });\n    var link = titleModel.get('link');\n    var sublink = titleModel.get('sublink');\n    var triggerEvent = titleModel.get('triggerEvent', true);\n    textEl.silent = !link && !triggerEvent;\n    subTextEl.silent = !sublink && !triggerEvent;\n    if (link) {\n      textEl.on('click', function () {\n        windowOpen(link, '_' + titleModel.get('target'));\n      });\n    }\n    if (sublink) {\n      subTextEl.on('click', function () {\n        windowOpen(sublink, '_' + titleModel.get('subtarget'));\n      });\n    }\n    getECData(textEl).eventData = getECData(subTextEl).eventData = triggerEvent ? {\n      componentType: 'title',\n      componentIndex: titleModel.componentIndex\n    } : null;\n    group.add(textEl);\n    subText && group.add(subTextEl);\n    // If no subText, but add subTextEl, there will be an empty line.\n    var groupRect = group.getBoundingRect();\n    var layoutOption = titleModel.getBoxLayoutParams();\n    layoutOption.width = groupRect.width;\n    layoutOption.height = groupRect.height;\n    var layoutRect = getLayoutRect(layoutOption, {\n      width: api.getWidth(),\n      height: api.getHeight()\n    }, titleModel.get('padding'));\n    // Adjust text align based on position\n    if (!textAlign) {\n      // Align left if title is on the left. center and right is same\n      textAlign = titleModel.get('left') || titleModel.get('right');\n      // @ts-ignore\n      if (textAlign === 'middle') {\n        textAlign = 'center';\n      }\n      // Adjust layout by text align\n      if (textAlign === 'right') {\n        layoutRect.x += layoutRect.width;\n      } else if (textAlign === 'center') {\n        layoutRect.x += layoutRect.width / 2;\n      }\n    }\n    if (!textVerticalAlign) {\n      textVerticalAlign = titleModel.get('top') || titleModel.get('bottom');\n      // @ts-ignore\n      if (textVerticalAlign === 'center') {\n        textVerticalAlign = 'middle';\n      }\n      if (textVerticalAlign === 'bottom') {\n        layoutRect.y += layoutRect.height;\n      } else if (textVerticalAlign === 'middle') {\n        layoutRect.y += layoutRect.height / 2;\n      }\n      textVerticalAlign = textVerticalAlign || 'top';\n    }\n    group.x = layoutRect.x;\n    group.y = layoutRect.y;\n    group.markRedraw();\n    var alignStyle = {\n      align: textAlign,\n      verticalAlign: textVerticalAlign\n    };\n    textEl.setStyle(alignStyle);\n    subTextEl.setStyle(alignStyle);\n    // Render background\n    // Get groupRect again because textAlign has been changed\n    groupRect = group.getBoundingRect();\n    var padding = layoutRect.margin;\n    var style = titleModel.getItemStyle(['color', 'opacity']);\n    style.fill = titleModel.get('backgroundColor');\n    var rect = new graphic.Rect({\n      shape: {\n        x: groupRect.x - padding[3],\n        y: groupRect.y - padding[0],\n        width: groupRect.width + padding[1] + padding[3],\n        height: groupRect.height + padding[0] + padding[2],\n        r: titleModel.get('borderRadius')\n      },\n      style: style,\n      subPixelOptimize: true,\n      silent: true\n    });\n    group.add(rect);\n  };\n  TitleView.type = 'title';\n  return TitleView;\n}(ComponentView);\nexport function install(registers) {\n  registers.registerComponentModel(TitleModel);\n  registers.registerComponentView(TitleView);\n}"], "mappings": ";;;;;;;;;;;;;;;;AAoDA,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC9C,cAAUA,aAAY,MAAM;AAC5B,aAASA,cAAa;AACpB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,YAAW;AACxB,YAAM,aAAa;AAAA,QACjB,MAAM;AAAA,QACN,YAAY;AAAA,MACd;AACA,aAAO;AAAA,IACT;AACA,IAAAA,YAAW,OAAO;AAClB,IAAAA,YAAW,gBAAgB;AAAA;AAAA,MAEzB,GAAG;AAAA,MACH,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA,MACL,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,iBAAc;AAAA;AAEhB,IAAI;AAAA;AAAA,EAAyB,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,aAAY;AACnB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAOA,WAAU;AACvB,aAAO;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,SAAS,SAAU,YAAY,SAAS,KAAK;AAC/D,WAAK,MAAM,UAAU;AACrB,UAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AAC3B;AAAA,MACF;AACA,UAAI,QAAQ,KAAK;AACjB,UAAI,iBAAiB,WAAW,SAAS,WAAW;AACpD,UAAI,oBAAoB,WAAW,SAAS,cAAc;AAC1D,UAAI,YAAY,WAAW,IAAI,WAAW;AAC1C,UAAI,oBAA2B,UAAU,WAAW,IAAI,cAAc,GAAG,WAAW,IAAI,mBAAmB,CAAC;AAC5G,UAAI,SAAS,IAAY,aAAK;AAAA,QAC5B,OAAO,gBAAgB,gBAAgB;AAAA,UACrC,MAAM,WAAW,IAAI,MAAM;AAAA,UAC3B,MAAM,eAAe,aAAa;AAAA,QACpC,GAAG;AAAA,UACD,YAAY;AAAA,QACd,CAAC;AAAA,QACD,IAAI;AAAA,MACN,CAAC;AACD,UAAI,WAAW,OAAO,gBAAgB;AACtC,UAAI,UAAU,WAAW,IAAI,SAAS;AACtC,UAAI,YAAY,IAAY,aAAK;AAAA,QAC/B,OAAO,gBAAgB,mBAAmB;AAAA,UACxC,MAAM;AAAA,UACN,MAAM,kBAAkB,aAAa;AAAA,UACrC,GAAG,SAAS,SAAS,WAAW,IAAI,SAAS;AAAA,UAC7C,eAAe;AAAA,QACjB,GAAG;AAAA,UACD,YAAY;AAAA,QACd,CAAC;AAAA,QACD,IAAI;AAAA,MACN,CAAC;AACD,UAAI,OAAO,WAAW,IAAI,MAAM;AAChC,UAAI,UAAU,WAAW,IAAI,SAAS;AACtC,UAAI,eAAe,WAAW,IAAI,gBAAgB,IAAI;AACtD,aAAO,SAAS,CAAC,QAAQ,CAAC;AAC1B,gBAAU,SAAS,CAAC,WAAW,CAAC;AAChC,UAAI,MAAM;AACR,eAAO,GAAG,SAAS,WAAY;AAC7B,qBAAW,MAAM,MAAM,WAAW,IAAI,QAAQ,CAAC;AAAA,QACjD,CAAC;AAAA,MACH;AACA,UAAI,SAAS;AACX,kBAAU,GAAG,SAAS,WAAY;AAChC,qBAAW,SAAS,MAAM,WAAW,IAAI,WAAW,CAAC;AAAA,QACvD,CAAC;AAAA,MACH;AACA,gBAAU,MAAM,EAAE,YAAY,UAAU,SAAS,EAAE,YAAY,eAAe;AAAA,QAC5E,eAAe;AAAA,QACf,gBAAgB,WAAW;AAAA,MAC7B,IAAI;AACJ,YAAM,IAAI,MAAM;AAChB,iBAAW,MAAM,IAAI,SAAS;AAE9B,UAAI,YAAY,MAAM,gBAAgB;AACtC,UAAI,eAAe,WAAW,mBAAmB;AACjD,mBAAa,QAAQ,UAAU;AAC/B,mBAAa,SAAS,UAAU;AAChC,UAAI,aAAa,cAAc,cAAc;AAAA,QAC3C,OAAO,IAAI,SAAS;AAAA,QACpB,QAAQ,IAAI,UAAU;AAAA,MACxB,GAAG,WAAW,IAAI,SAAS,CAAC;AAE5B,UAAI,CAAC,WAAW;AAEd,oBAAY,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI,OAAO;AAE5D,YAAI,cAAc,UAAU;AAC1B,sBAAY;AAAA,QACd;AAEA,YAAI,cAAc,SAAS;AACzB,qBAAW,KAAK,WAAW;AAAA,QAC7B,WAAW,cAAc,UAAU;AACjC,qBAAW,KAAK,WAAW,QAAQ;AAAA,QACrC;AAAA,MACF;AACA,UAAI,CAAC,mBAAmB;AACtB,4BAAoB,WAAW,IAAI,KAAK,KAAK,WAAW,IAAI,QAAQ;AAEpE,YAAI,sBAAsB,UAAU;AAClC,8BAAoB;AAAA,QACtB;AACA,YAAI,sBAAsB,UAAU;AAClC,qBAAW,KAAK,WAAW;AAAA,QAC7B,WAAW,sBAAsB,UAAU;AACzC,qBAAW,KAAK,WAAW,SAAS;AAAA,QACtC;AACA,4BAAoB,qBAAqB;AAAA,MAC3C;AACA,YAAM,IAAI,WAAW;AACrB,YAAM,IAAI,WAAW;AACrB,YAAM,WAAW;AACjB,UAAI,aAAa;AAAA,QACf,OAAO;AAAA,QACP,eAAe;AAAA,MACjB;AACA,aAAO,SAAS,UAAU;AAC1B,gBAAU,SAAS,UAAU;AAG7B,kBAAY,MAAM,gBAAgB;AAClC,UAAI,UAAU,WAAW;AACzB,UAAI,QAAQ,WAAW,aAAa,CAAC,SAAS,SAAS,CAAC;AACxD,YAAM,OAAO,WAAW,IAAI,iBAAiB;AAC7C,UAAI,OAAO,IAAY,aAAK;AAAA,QAC1B,OAAO;AAAA,UACL,GAAG,UAAU,IAAI,QAAQ,CAAC;AAAA,UAC1B,GAAG,UAAU,IAAI,QAAQ,CAAC;AAAA,UAC1B,OAAO,UAAU,QAAQ,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,UAC/C,QAAQ,UAAU,SAAS,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,UACjD,GAAG,WAAW,IAAI,cAAc;AAAA,QAClC;AAAA,QACA;AAAA,QACA,kBAAkB;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,IAAI,IAAI;AAAA,IAChB;AACA,IAAAA,WAAU,OAAO;AACjB,WAAOA;AAAA,EACT,EAAEC,kBAAa;AAAA;AACR,SAAS,QAAQ,WAAW;AACjC,YAAU,uBAAuB,UAAU;AAC3C,YAAU,sBAAsB,SAAS;AAC3C;", "names": ["TitleModel", "TitleView", "Component_default"]}