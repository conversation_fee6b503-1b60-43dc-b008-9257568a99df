import{a as ie}from"./@arco-design-uttiljWv.js";import{s as le}from"./side-channel-0xN0c_x9.js";var oe=String.prototype.replace,fe=/%20/g,K={RFC1738:"RFC1738",RFC3986:"RFC3986"},z={default:K.RFC3986,formatters:{RFC1738:function(a){return oe.call(a,fe,"+")},RFC3986:function(a){return String(a)}},RFC1738:K.RFC1738,RFC3986:K.RFC3986},ue=z,Q=Object.prototype.hasOwnProperty,O=Array.isArray,b=function(){for(var a=[],e=0;e<256;++e)a.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return a}(),ce=function(e){for(;e.length>1;){var r=e.pop(),t=r.obj[r.prop];if(O(t)){for(var i=[],l=0;l<t.length;++l)typeof t[l]<"u"&&i.push(t[l]);r.obj[r.prop]=i}}},X=function(e,r){for(var t=r&&r.plainObjects?{__proto__:null}:{},i=0;i<e.length;++i)typeof e[i]<"u"&&(t[i]=e[i]);return t},de=function a(e,r,t){if(!r)return e;if(typeof r!="object"&&typeof r!="function"){if(O(e))e.push(r);else if(e&&typeof e=="object")(t&&(t.plainObjects||t.allowPrototypes)||!Q.call(Object.prototype,r))&&(e[r]=!0);else return[e,r];return e}if(!e||typeof e!="object")return[e].concat(r);var i=e;return O(e)&&!O(r)&&(i=X(e,t)),O(e)&&O(r)?(r.forEach(function(l,n){if(Q.call(e,n)){var d=e[n];d&&typeof d=="object"&&l&&typeof l=="object"?e[n]=a(d,l,t):e.push(l)}else e[n]=l}),e):Object.keys(r).reduce(function(l,n){var d=r[n];return Q.call(l,n)?l[n]=a(l[n],d,t):l[n]=d,l},i)},ye=function(e,r){return Object.keys(r).reduce(function(t,i){return t[i]=r[i],t},e)},se=function(a,e,r){var t=a.replace(/\+/g," ");if(r==="iso-8859-1")return t.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(t)}catch{return t}},$=1024,me=function(e,r,t,i,l){if(e.length===0)return e;var n=e;if(typeof e=="symbol"?n=Symbol.prototype.toString.call(e):typeof e!="string"&&(n=String(e)),t==="iso-8859-1")return escape(n).replace(/%u[0-9a-f]{4}/gi,function(v){return"%26%23"+parseInt(v.slice(2),16)+"%3B"});for(var d="",o=0;o<n.length;o+=$){for(var c=n.length>=$?n.slice(o,o+$):n,u=[],s=0;s<c.length;++s){var f=c.charCodeAt(s);if(f===45||f===46||f===95||f===126||f>=48&&f<=57||f>=65&&f<=90||f>=97&&f<=122||l===ue.RFC1738&&(f===40||f===41)){u[u.length]=c.charAt(s);continue}if(f<128){u[u.length]=b[f];continue}if(f<2048){u[u.length]=b[192|f>>6]+b[128|f&63];continue}if(f<55296||f>=57344){u[u.length]=b[224|f>>12]+b[128|f>>6&63]+b[128|f&63];continue}s+=1,f=65536+((f&1023)<<10|c.charCodeAt(s)&1023),u[u.length]=b[240|f>>18]+b[128|f>>12&63]+b[128|f>>6&63]+b[128|f&63]}d+=u.join("")}return d},pe=function(e){for(var r=[{obj:{o:e},prop:"o"}],t=[],i=0;i<r.length;++i)for(var l=r[i],n=l.obj[l.prop],d=Object.keys(n),o=0;o<d.length;++o){var c=d[o],u=n[c];typeof u=="object"&&u!==null&&t.indexOf(u)===-1&&(r.push({obj:n,prop:c}),t.push(u))}return ce(r),e},he=function(e){return Object.prototype.toString.call(e)==="[object RegExp]"},ve=function(e){return!e||typeof e!="object"?!1:!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},be=function(e,r){return[].concat(e,r)},we=function(e,r){if(O(e)){for(var t=[],i=0;i<e.length;i+=1)t.push(r(e[i]));return t}return r(e)},Y={arrayToObject:X,assign:ye,combine:be,compact:pe,decode:se,encode:me,isBuffer:ve,isRegExp:he,maybeMap:we,merge:de},Z=le,L=Y,A=z,ge=Object.prototype.hasOwnProperty,ee={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,r){return e+"["+r+"]"},repeat:function(e){return e}},w=Array.isArray,Ee=Array.prototype.push,re=function(a,e){Ee.apply(a,w(e)?e:[e])},xe=Date.prototype.toISOString,W=A.default,h={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:L.encode,encodeValuesOnly:!1,filter:void 0,format:W,formatter:A.formatters[W],indices:!1,serializeDate:function(e){return xe.call(e)},skipNulls:!1,strictNullHandling:!1},Oe=function(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"||typeof e=="symbol"||typeof e=="bigint"},H={},De=function a(e,r,t,i,l,n,d,o,c,u,s,f,v,p,g,E,F,I){for(var y=e,j=I,N=0,M=!1;(j=j.get(H))!==void 0&&!M;){var V=j.get(e);if(N+=1,typeof V<"u"){if(V===N)throw new RangeError("Cyclic object value");M=!0}typeof j.get(H)>"u"&&(N=0)}if(typeof u=="function"?y=u(r,y):y instanceof Date?y=v(y):t==="comma"&&w(y)&&(y=L.maybeMap(y,function(R){return R instanceof Date?v(R):R})),y===null){if(n)return c&&!E?c(r,h.encoder,F,"key",p):r;y=""}if(Oe(y)||L.isBuffer(y)){if(c){var ae=E?r:c(r,h.encoder,F,"key",p);return[g(ae)+"="+g(c(y,h.encoder,F,"value",p))]}return[g(r)+"="+g(String(y))]}var P=[];if(typeof y>"u")return P;var S;if(t==="comma"&&w(y))E&&c&&(y=L.maybeMap(y,c)),S=[{value:y.length>0?y.join(",")||null:void 0}];else if(w(u))S=u;else{var U=Object.keys(y);S=s?U.sort(s):U}var k=o?String(r).replace(/\./g,"%2E"):String(r),T=i&&w(y)&&y.length===1?k+"[]":k;if(l&&w(y)&&y.length===0)return T+"[]";for(var _=0;_<S.length;++_){var x=S[_],q=typeof x=="object"&&x&&typeof x.value<"u"?x.value:y[x];if(!(d&&q===null)){var C=f&&o?String(x).replace(/\./g,"%2E"):String(x),ne=w(y)?typeof t=="function"?t(T,C):T:T+(f?"."+C:"["+C+"]");I.set(e,N);var G=Z();G.set(H,I),re(P,a(q,ne,t,i,l,n,d,o,t==="comma"&&E&&w(y)?null:c,u,s,f,v,p,g,E,F,G))}}return P},Se=function(e){if(!e)return h;if(typeof e.allowEmptyArrays<"u"&&typeof e.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof e.encodeDotInKeys<"u"&&typeof e.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(e.encoder!==null&&typeof e.encoder<"u"&&typeof e.encoder!="function")throw new TypeError("Encoder has to be a function.");var r=e.charset||h.charset;if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=A.default;if(typeof e.format<"u"){if(!ge.call(A.formatters,e.format))throw new TypeError("Unknown format option provided.");t=e.format}var i=A.formatters[t],l=h.filter;(typeof e.filter=="function"||w(e.filter))&&(l=e.filter);var n;if(e.arrayFormat in ee?n=e.arrayFormat:"indices"in e?n=e.indices?"indices":"repeat":n=h.arrayFormat,"commaRoundTrip"in e&&typeof e.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var d=typeof e.allowDots>"u"?e.encodeDotInKeys===!0?!0:h.allowDots:!!e.allowDots;return{addQueryPrefix:typeof e.addQueryPrefix=="boolean"?e.addQueryPrefix:h.addQueryPrefix,allowDots:d,allowEmptyArrays:typeof e.allowEmptyArrays=="boolean"?!!e.allowEmptyArrays:h.allowEmptyArrays,arrayFormat:n,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:h.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:typeof e.delimiter>"u"?h.delimiter:e.delimiter,encode:typeof e.encode=="boolean"?e.encode:h.encode,encodeDotInKeys:typeof e.encodeDotInKeys=="boolean"?e.encodeDotInKeys:h.encodeDotInKeys,encoder:typeof e.encoder=="function"?e.encoder:h.encoder,encodeValuesOnly:typeof e.encodeValuesOnly=="boolean"?e.encodeValuesOnly:h.encodeValuesOnly,filter:l,format:t,formatter:i,serializeDate:typeof e.serializeDate=="function"?e.serializeDate:h.serializeDate,skipNulls:typeof e.skipNulls=="boolean"?e.skipNulls:h.skipNulls,sort:typeof e.sort=="function"?e.sort:null,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:h.strictNullHandling}},Ae=function(a,e){var r=a,t=Se(e),i,l;typeof t.filter=="function"?(l=t.filter,r=l("",r)):w(t.filter)&&(l=t.filter,i=l);var n=[];if(typeof r!="object"||r===null)return"";var d=ee[t.arrayFormat],o=d==="comma"&&t.commaRoundTrip;i||(i=Object.keys(r)),t.sort&&i.sort(t.sort);for(var c=Z(),u=0;u<i.length;++u){var s=i[u],f=r[s];t.skipNulls&&f===null||re(n,De(f,s,d,o,t.allowEmptyArrays,t.strictNullHandling,t.skipNulls,t.encodeDotInKeys,t.encode?t.encoder:null,t.filter,t.sort,t.allowDots,t.serializeDate,t.format,t.formatter,t.encodeValuesOnly,t.charset,c))}var v=n.join(t.delimiter),p=t.addQueryPrefix===!0?"?":"";return t.charsetSentinel&&(t.charset==="iso-8859-1"?p+="utf8=%26%2310003%3B&":p+="utf8=%E2%9C%93&"),v.length>0?p+v:""},D=Y,B=Object.prototype.hasOwnProperty,J=Array.isArray,m={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:D.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},Fe=function(a){return a.replace(/&#(\d+);/g,function(e,r){return String.fromCharCode(parseInt(r,10))})},te=function(a,e,r){if(a&&typeof a=="string"&&e.comma&&a.indexOf(",")>-1)return a.split(",");if(e.throwOnLimitExceeded&&r>=e.arrayLimit)throw new RangeError("Array limit exceeded. Only "+e.arrayLimit+" element"+(e.arrayLimit===1?"":"s")+" allowed in an array.");return a},je="utf8=%26%2310003%3B",Ne="utf8=%E2%9C%93",Te=function(e,r){var t={__proto__:null},i=r.ignoreQueryPrefix?e.replace(/^\?/,""):e;i=i.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var l=r.parameterLimit===1/0?void 0:r.parameterLimit,n=i.split(r.delimiter,r.throwOnLimitExceeded?l+1:l);if(r.throwOnLimitExceeded&&n.length>l)throw new RangeError("Parameter limit exceeded. Only "+l+" parameter"+(l===1?"":"s")+" allowed.");var d=-1,o,c=r.charset;if(r.charsetSentinel)for(o=0;o<n.length;++o)n[o].indexOf("utf8=")===0&&(n[o]===Ne?c="utf-8":n[o]===je&&(c="iso-8859-1"),d=o,o=n.length);for(o=0;o<n.length;++o)if(o!==d){var u=n[o],s=u.indexOf("]="),f=s===-1?u.indexOf("="):s+1,v,p;f===-1?(v=r.decoder(u,m.decoder,c,"key"),p=r.strictNullHandling?null:""):(v=r.decoder(u.slice(0,f),m.decoder,c,"key"),p=D.maybeMap(te(u.slice(f+1),r,J(t[v])?t[v].length:0),function(E){return r.decoder(E,m.decoder,c,"value")})),p&&r.interpretNumericEntities&&c==="iso-8859-1"&&(p=Fe(String(p))),u.indexOf("[]=")>-1&&(p=J(p)?[p]:p);var g=B.call(t,v);g&&r.duplicates==="combine"?t[v]=D.combine(t[v],p):(!g||r.duplicates==="last")&&(t[v]=p)}return t},Le=function(a,e,r,t){var i=0;if(a.length>0&&a[a.length-1]==="[]"){var l=a.slice(0,-1).join("");i=Array.isArray(e)&&e[l]?e[l].length:0}for(var n=t?e:te(e,r,i),d=a.length-1;d>=0;--d){var o,c=a[d];if(c==="[]"&&r.parseArrays)o=r.allowEmptyArrays&&(n===""||r.strictNullHandling&&n===null)?[]:D.combine([],n);else{o=r.plainObjects?{__proto__:null}:{};var u=c.charAt(0)==="["&&c.charAt(c.length-1)==="]"?c.slice(1,-1):c,s=r.decodeDotInKeys?u.replace(/%2E/g,"."):u,f=parseInt(s,10);!r.parseArrays&&s===""?o={0:n}:!isNaN(f)&&c!==s&&String(f)===s&&f>=0&&r.parseArrays&&f<=r.arrayLimit?(o=[],o[f]=n):s!=="__proto__"&&(o[s]=n)}n=o}return n},Ie=function(e,r,t,i){if(e){var l=t.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,n=/(\[[^[\]]*])/,d=/(\[[^[\]]*])/g,o=t.depth>0&&n.exec(l),c=o?l.slice(0,o.index):l,u=[];if(c){if(!t.plainObjects&&B.call(Object.prototype,c)&&!t.allowPrototypes)return;u.push(c)}for(var s=0;t.depth>0&&(o=d.exec(l))!==null&&s<t.depth;){if(s+=1,!t.plainObjects&&B.call(Object.prototype,o[1].slice(1,-1))&&!t.allowPrototypes)return;u.push(o[1])}if(o){if(t.strictDepth===!0)throw new RangeError("Input depth exceeded depth option of "+t.depth+" and strictDepth is true");u.push("["+l.slice(o.index)+"]")}return Le(u,r,t,i)}},Pe=function(e){if(!e)return m;if(typeof e.allowEmptyArrays<"u"&&typeof e.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof e.decodeDotInKeys<"u"&&typeof e.decodeDotInKeys!="boolean")throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(e.decoder!==null&&typeof e.decoder<"u"&&typeof e.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(typeof e.throwOnLimitExceeded<"u"&&typeof e.throwOnLimitExceeded!="boolean")throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var r=typeof e.charset>"u"?m.charset:e.charset,t=typeof e.duplicates>"u"?m.duplicates:e.duplicates;if(t!=="combine"&&t!=="first"&&t!=="last")throw new TypeError("The duplicates option must be either combine, first, or last");var i=typeof e.allowDots>"u"?e.decodeDotInKeys===!0?!0:m.allowDots:!!e.allowDots;return{allowDots:i,allowEmptyArrays:typeof e.allowEmptyArrays=="boolean"?!!e.allowEmptyArrays:m.allowEmptyArrays,allowPrototypes:typeof e.allowPrototypes=="boolean"?e.allowPrototypes:m.allowPrototypes,allowSparse:typeof e.allowSparse=="boolean"?e.allowSparse:m.allowSparse,arrayLimit:typeof e.arrayLimit=="number"?e.arrayLimit:m.arrayLimit,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:m.charsetSentinel,comma:typeof e.comma=="boolean"?e.comma:m.comma,decodeDotInKeys:typeof e.decodeDotInKeys=="boolean"?e.decodeDotInKeys:m.decodeDotInKeys,decoder:typeof e.decoder=="function"?e.decoder:m.decoder,delimiter:typeof e.delimiter=="string"||D.isRegExp(e.delimiter)?e.delimiter:m.delimiter,depth:typeof e.depth=="number"||e.depth===!1?+e.depth:m.depth,duplicates:t,ignoreQueryPrefix:e.ignoreQueryPrefix===!0,interpretNumericEntities:typeof e.interpretNumericEntities=="boolean"?e.interpretNumericEntities:m.interpretNumericEntities,parameterLimit:typeof e.parameterLimit=="number"?e.parameterLimit:m.parameterLimit,parseArrays:e.parseArrays!==!1,plainObjects:typeof e.plainObjects=="boolean"?e.plainObjects:m.plainObjects,strictDepth:typeof e.strictDepth=="boolean"?!!e.strictDepth:m.strictDepth,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:m.strictNullHandling,throwOnLimitExceeded:typeof e.throwOnLimitExceeded=="boolean"?e.throwOnLimitExceeded:!1}},_e=function(a,e){var r=Pe(e);if(a===""||a===null||typeof a>"u")return r.plainObjects?{__proto__:null}:{};for(var t=typeof a=="string"?Te(a,r):a,i=r.plainObjects?{__proto__:null}:{},l=Object.keys(t),n=0;n<l.length;++n){var d=l[n],o=Ie(d,t[d],r,typeof a=="string");i=D.merge(i,o,r)}return r.allowSparse===!0?i:D.compact(i)},Ce=Ae,Re=_e,Ke=z,Qe={formats:Ke,parse:Re,stringify:Ce};const Be=ie(Qe);export{Be as q};
